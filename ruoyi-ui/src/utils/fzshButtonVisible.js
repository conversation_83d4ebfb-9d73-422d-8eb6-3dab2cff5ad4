//通过fzsh传入的useYbqd和按钮名称，判断是否显示页面的按钮

// 建立字典 ybqd为9拥有 查看详情按钮

const ybqd = {
  5: ['查看详情', '对比'],
  6: ['查看详情', '对比'],
  3: ['通过', '打回', '查看详情']
}

// 方法判断，通过传入的useYbqd，获取按钮列表，判断传入的按钮名称是否存在于列表中
export function fzshButtonVisible(useYbqd, buttonName) {

  // 如果useYbqd不存在于字典中，则返回true
  if(!ybqd[useYbqd]) {
    return true;
  }

  // 如果useYbqd存在于字典中，则判断按钮名称是否存在于列表中
  if (ybqd[useYbqd].includes(buttonName)) {
    return true;
  }

  return false;
}


