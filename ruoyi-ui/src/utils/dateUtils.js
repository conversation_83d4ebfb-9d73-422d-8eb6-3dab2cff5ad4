export function dateToString(date) {
  if(typeof date === 'string') {
    return date;
  }
  var fullYear = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()
  var hours = date.getHours()
  var minutes = date.getMinutes()
  var seconds = date.getSeconds()

  return fullYear + '-' +
    pad<PERSON>ero(month) + '-' +
    pad<PERSON>ero(day) +' ' +
    pad<PERSON>ero(hours) + ':' +
    pad<PERSON>ero(minutes) + ':' +
    pad<PERSON>ero(seconds)
}
function padZero(num) {
  return num < 10 ? '0' + num : num
}

export function getMonthFirstDay(){
  let now = new Date();
  var year = now.getFullYear()
  var month = now.getMonth() + 1
  var day = now.getDate()
  return new Date(year, month - 1, 1, 0, 0, 0)
}

export function getYearFirstDay(){
  let now = new Date();
  var year = now.getFullYear()
  var month = now.getMonth() + 1
  var day = now.getDate()
  return new Date(year -1, month , 1, 0, 0, 0)
}

export function getMonthFirstDayStr(){
  return dateToString(getMonthFirstDay())
}

export function getTodayLastSecond(){
  let now = new Date();
  var year = now.getFullYear()
  var month = now.getMonth() + 1
  var day = now.getDate()
  return new Date(year, month - 1, day, 23, 59, 59)
}

export function getYesterdayFirstSecond(){
  let now = new Date();
  now.setHours(0, 0, 0, 0);
  now.setDate(now.getDate() - 1);
  return now;
}


export function getMonthAgoDateStr() {
  let now = new Date();
  let date = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
  date.setHours(0, 0, 0, 0);
  return dateToString(date);
}

export function getTodayLastSecondStr(){
  return dateToString(getTodayLastSecond())
}

export function getYesterdayFirstSecondStr(){
  return dateToString(getYesterdayFirstSecond())
}


/**
 * 获取指定天数之前的日期
 * @param number 天数
 * @returns {Date}
 */
export function getDayAgoForNum(number) {
  var curDate = new Date()
  return new Date(curDate.getTime() - number * 24 * 60 * 60 * 1000)
}

export function formatZeroSeconds(date) {
  date.setSeconds(0);
  date.setMinutes(0);
  date.setHours(0);
  return date;
}

export function formatLastSeconds(date){
  date.setSeconds(59);
  date.setHours(23);
  date.setMinutes(59);
  return date;
}

export function getDayAgoForNumStr(number) {
  var diffDayForCur = getDayAgoForNum(number)
  return dateToString(diffDayForCur)
}

export function getCurDayStr(){
  return dateToString(new Date())
}
