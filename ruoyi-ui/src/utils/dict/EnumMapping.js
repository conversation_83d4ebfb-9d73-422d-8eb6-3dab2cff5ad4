// 定义一个映射对象，将数字映射到中文
const statusMapping = {
  1: "有",
  2: "临床未确定",
  3: "情况不明",
  4: "无"
};

// 创建一个转换函数
export function getRybqDisplay(value) {
  if (statusMapping[value]) {
    return statusMapping[value];
  }
  return value;
}


const anesthesiaMap = {
  1: "全身麻醉",
  34: "区域阻滞麻醉",
  11: "吸入麻醉",
  35: "局部浸润麻醉",
  12: "静脉麻醉",
  36: "表面麻醉",
  13: "基础麻醉",
  4: "复合麻醉",
  2: "椎管内麻醉",
  41: "静吸复合全麻",
  21: "蛛网膜下腔阻滞麻醉",
  42: "针药复合麻醉",
  22: "硬脊膜外腔阻滞麻醉",
  43: "神经丛与硬膜外阻滞复合麻醉",
  3: "局部麻醉",
  44: "全麻复合全身降温",
  31: "神经丛阻滞麻醉",
  45: "全麻复合控制性降压",
  32: "神经节阻滞麻醉",
  9: "其他麻醉方法",
  33: "神经阻滞麻醉"
};

export function getAnesthesiaMethod(type) {

  return anesthesiaMap[type] || type;
}

// 测试
// console.log(getAnesthesiaMethod(1));  // 输出 "全身麻醉"
// console.log(getAnesthesiaMethod(9));  // 输出 "其他麻醉方法"
// console.log(getAnesthesiaMethod(99)); // 输出 99


// 测试
// console.log(getStatusDisplay(1)); // 输出: "有"
// console.log(getStatusDisplay(5)); // 输出: 5（不在映射中，直接返回数字）
// console.log(getStatusDisplay("未知")); // 输出: "未知"（非数字，直接返回）
