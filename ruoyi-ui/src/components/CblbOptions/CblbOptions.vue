<template>
  <el-form-item label="参保类别">
    <el-select v-model="selectedCblb" multiple clearable>
      <el-option v-for="item in options" :key="item.key" :value="item.value" :label="item.label"/>
    </el-select>
  </el-form-item>
</template>

<script>
export default {
  name: 'CblbOptions',
  data() {
    return {
      options: [{
        label: '职工',
        key: '职工',
        value: '职工'
      },
        {
          label: '居民',
          key: '居民',
          value: '居民'
        },
        {
          label: '自费',
          key: '自费',
          value: '自费'
        },
        {
          label: '生育',
          key: '生育',
          value: '生育'
        }]
    }
  },
  props:{
    value: {
      type: [Array],
      default: []
    }
  },
  computed: {
    selectedCblb: {
      get(){
        return this.value
      },
      set(val){
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
