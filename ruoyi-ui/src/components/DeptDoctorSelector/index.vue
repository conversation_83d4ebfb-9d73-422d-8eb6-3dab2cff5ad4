<template>
  <div style="display: inline">
    <el-form-item label="科室">
      <el-select
        v-model="selectedDept"
        @change="handleDeptChange"
        filterable
        placeholder="请选择科室"
      >
        <el-option
          v-for="(item, index) in deptOptions"
          :key="index"
          :value="item.hDeptName"
          :label="item.hDeptName"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="医生">
      <el-select
        v-model="selectedDoctor"
        filterable
        clearable
        placeholder="请选择医生"
        style="width: 200px"
        :disabled="!selectedDept"
      >
        <el-option
          v-for="(doctor, index) in doctorOptions"
          :key="index"
          :value="doctor.nickName"
          :label="doctor.nickName"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import { selectHDeptNameList } from "@/api/system/hdept";
import {selectDoctorByHDeptName} from "@/api/system/user";

export default {
  name: 'DeptDoctorSelector',
  props: {
    value: {
      type: Object,
      default: () => ({
        dept: '',
        doctor: ''
      })
    }
  },
  data() {
    return {
      deptOptions: [],
      doctorOptions: [],
      loading: false
    };
  },
  computed: {
    selectedDept: {
      get() {
        return this.value.dept;
      },
      set(val) {
        this.$emit('input', {
          ...this.value,
          dept: val
        });
      }
    },
    selectedDoctor: {
      get() {
        return this.value.doctor;
      },
      set(val) {
        this.$emit('input', {
          ...this.value,
          doctor: val
        });
      }
    }
  },
  async created() {
    await this.loadDeptList();
  },
  methods: {
    async loadDeptList() {
      try {
        this.loading = true;
        const { code, rows } = await selectHDeptNameList();
        if (code === 200) {
          this.deptOptions = [{hDeptName:'所有'}];
          this.deptOptions = this.deptOptions.concat(rows);
          this.value.dept = '所有';
          this.handleDeptChange('所有');
        }
      } finally {
        this.loading = false;
      }
    },

    async handleDeptChange(deptName) {
      if (!deptName) {
        this.doctorOptions = [];
        this.selectedDoctor = '';
        return;
      }

      try {
        this.loading = true;
        const { code, rows } = await selectDoctorByHDeptName(deptName);
        if (code === 200) {
          this.doctorOptions = rows;
          this.selectedDoctor = '';
        }
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
