<template>
  <el-tabs type="card" v-model="activeName">

    <el-tab-pane label="诊断记录" name="zdjl"  >
      <el-table :border="true" :data="zdxxList" height="450" v-loading="loading">
        <el-table-column show-overflow-tooltip label="住院号" align="center" prop="zyh" />
        <el-table-column show-overflow-tooltip label="诊断名称" align="center" prop="zdname"/>
        <el-table-column show-overflow-tooltip label="诊断编号" align="center" prop="zdcode" />
        <el-table-column show-overflow-tooltip label="诊断类型" align="center" prop="zdtype" />
        <el-table-column show-overflow-tooltip label="诊断排序" align="center" prop="zdsort" />
        <el-table-column show-overflow-tooltip label="记录来源" align="center" prop="jlly" />
        <el-table-column show-overflow-tooltip label="入院病情" align="center" prop="rybq" />
        <el-table-column show-overflow-tooltip label="出院情况" align="center" prop="cyqk" />
        <el-table-column show-overflow-tooltip label="费用" align="center" prop="fy" />
      </el-table>
    </el-tab-pane>
    <el-tab-pane label="病历记录" name="bljl">
      <el-table :border="true" :data="bljlList" height="500" >
        <el-table-column show-overflow-tooltip label="住院号" align="center" prop="zyh" />
        <el-table-column show-overflow-tooltip label="病历名称" align="left" prop="blname"/>
        <el-table-column show-overflow-tooltip label="内容信息" align="left" prop="blnr"   width="950px" class="el-tooltip__popper"/>
        <el-table-column show-overflow-tooltip label="创建时间" align="center" prop="createdate" />
      </el-table>
    </el-tab-pane>
    <el-tab-pane label="费用信息" name="fyxx">
      <el-form style="display: flex" :model="fyxx">
        <el-form-item label="显示类别" prop="query" style="width: 350px;">
          <el-select v-model="fyxx.query" clearable>
            <el-option value="明细费用">明细费用</el-option>
            <el-option value="分项汇总">分项汇总</el-option>
          </el-select>
        </el-form-item>
         <el-form-item style="width: 210px;margin-right: 10px">
        <el-input
            v-model="fyxx.xmmc"
            placeholder="费用名称"
            clearable
          />
           </el-form-item>
        <el-form-item style="width: 210px; margin-right: 10px">
          <el-input
            v-model="fyxx.gyxmmc"
            placeholder="共用费用名称"
            clearable/>
        </el-form-item>
        <el-form-item style="margin-right: 10px">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item style="width: 250px">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="fyxxHz">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table :border="true" :data="fyxxList">
        <el-table-column show-overflow-tooltip label="科目名称" align="center" prop="fykmname" />
      
        <el-table-column show-overflow-tooltip label="项目编号" align="center" prop="xmbm"  show-overflow-tooltip width="200px"/>
        <el-table-column show-overflow-tooltip label="项目名称" align="left" prop="xmmc"  show-overflow-tooltip  width="450px"/>
        <el-table-column show-overflow-tooltip label="数量" align="center" prop="sl" />
        <el-table-column show-overflow-tooltip label="单价" align="center" prop="price" />
        <el-table-column show-overflow-tooltip label="金额" align="center" prop="je" />
        <el-table-column show-overflow-tooltip label="单位" align="center" prop="dw" />
        <el-table-column show-overflow-tooltip label="规格" align="center" prop="guige" />
        <el-table-column show-overflow-tooltip label="费用时间" align="center" :formatter="formatDateTime" prop="fydate"  width="160"/>
          <el-table-column show-overflow-tooltip label="医生名称" align="center" prop="ysname" />
      </el-table>


      <pagination
        v-show="fyxxTotal>0"
        :total=fyxxTotal
        :page.sync="fyxxParams.pageNum"
        :limit.sync="fyxxParams.pageSize"
        @pagination="fyxxHz"
      />

    </el-tab-pane>
    <el-tab-pane label="病人基本信息" name="brjbxx">
      <el-table :border="true" :data="brxxList" height="450">
        <!--            <el-table-column label="医保就诊号" align="center" prop="jzh" />-->
        <el-table-column show-overflow-tooltip label="住院号" align="center" prop="zyh"/>
        <el-table-column show-overflow-tooltip label="姓名" align="center" prop="name" />
        <el-table-column show-overflow-tooltip label="床号" align="center" prop="bed" />
        <el-table-column show-overflow-tooltip label="年龄" align="center" prop="age" />
        <el-table-column show-overflow-tooltip label="性别" align="center" prop="sex" />
        <el-table-column show-overflow-tooltip label="电话" align="center" prop="tel" />
        <el-table-column show-overflow-tooltip label="医保号" align="center" prop="ybh" />
        <el-table-column show-overflow-tooltip label="入院时间" align="center" prop="rydate" />
        <el-table-column show-overflow-tooltip label="出院时间" align="center" prop="cydate" />
        <el-table-column show-overflow-tooltip label="医生名称" align="center" prop="doctorname" />
        <el-table-column show-overflow-tooltip label="科室名称" align="center" prop="deptname" />
      </el-table>
    </el-tab-pane>

    <el-tab-pane label="检查信息" name="jcxx">
      <el-table :border="true" :data="jcxxList"  height="600">
        <el-table-column show-overflow-tooltip label="项目名称" align="center" prop="xmmc"/>
        <el-table-column show-overflow-tooltip label="检查所见" align="center" prop="jcsj" />
        <el-table-column show-overflow-tooltip label="结论" align="center" prop="jcjl" />
        <el-table-column show-overflow-tooltip label="医生" align="center" prop="doctor" />
      </el-table>
    </el-tab-pane>


    <el-tab-pane label="检验信息" name="jyxx">
      <el-table :border="true" :data="jyxxList"  height="600">
        <el-table-column show-overflow-tooltip label="项目名称" align="center" prop="xmmc"  />
        <el-table-column show-overflow-tooltip label="合格范围" align="center" prop="jyfw" />
        <el-table-column show-overflow-tooltip label="结果" align="center" prop="jyjg" />
        <el-table-column show-overflow-tooltip label="单位" align="center" prop="jydw" />
        <el-table-column show-overflow-tooltip label="标志" align="center" prop="jgbz" />
        <el-table-column show-overflow-tooltip label="医生" align="center" prop="doctor" />
      </el-table>
    </el-tab-pane>

  </el-tabs>
</template>

<script>
import {getbljl, getBrxx, getFyxx, getFyxxByKs, getZdjl} from "@/api/gksz/jklog";
import {listJyxx} from "@/api/mlcx/jyxx";
import {listJcxx} from "@/api/mlcx/jcxx";
import { dateToString } from '@/utils/dateUtils'


export default {
  props:['jzh'],
  name: "tabs",
  data() {
    return {
      fyxxTotal: 0,
      loading:true,
      fyxx: {
        query:null,
        xmmc:"",
        gyxmmc: ''
      },
      dateRange: null,
      //审核信息
      jklogList:[],
      //诊断信息
      zdxxList:[],
      //费用信息
      fyxxList:[],
      //病人信息
      brxxList:[],
      //病例信息
      bljlList:[],
      jcxxList:[],
      jyxxList:[],
      activeName:'zdjl',
      fyxxParams: {
        pageNum: 1,
        pageSize: 10,
        jzh: null,
        xmmc:'',
      },
    }
  },
  watch:{
    jzh(newVal){
      this.getData()
    }
  },
  created() {
    this.getData()
  },
  methods:{
    getData() {
      this.fyxxTotal = 0
      this.activeName = 'zdjl'
      this.loading = true
      this.getZdjlList()
      this.getBrxxList()
      this.getFyxxList()
      this.getBljlList()
      this.getJyxxList()
      this.getJcxxList()
    },
    formatDateTime(row, column, cellValue) {
        if (cellValue) {
              // 假设 cellValue 是时间戳或 ISO 时间字符串
              const date = new Date(cellValue);
              const year = date.getFullYear();
              const month = (date.getMonth() + 1).toString().padStart(2, '0');
              const day = date.getDate().toString().padStart(2, '0');
              const hours = date.getHours().toString().padStart(2, '0');
              const minutes = date.getMinutes().toString().padStart(2, '0');
              const seconds = date.getSeconds().toString().padStart(2, '0');
              return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }
            return '';
      },
    //获取病例信息
    getBljlList() {
      getbljl(this.jzh).then(response => {
        this.bljlList = response.rows;
      });
    },
    //获取诊断信息
    getZdjlList() {
      getZdjl(this.jzh).then(response => {
        this.zdxxList = response.rows;
        this.loading = false
      }).catch(error => {
        this.loading = false
      })
    },
    //获取费用信息
    getFyxxList() {
      this.fyxxParams.jzh = this.jzh
      this.fyxxParams.xmmc = this.fyxx.xmmc
      getFyxx(this.fyxxParams).then(response => {
        this.fyxxList = response.rows;
        this.fyxxTotal = response.total;
      });
    },
    //获取病人信息
    getBrxxList() {
      getBrxx(this.jzh).then(response => {
        this.brxxList = response.rows;
      });
    },
    getJcxxList() {
      listJcxx({jzh:this.jzh}).then(response => {
        this.jcxxList = response.rows;
      });
    },
    getJyxxList() {
      listJyxx({jzh:this.jzh}).then(response => {
        this.jyxxList = response.rows;
      });
    },
    fyxxHz() {
      console.log(this.dateRange)
      this.fyxxParams.startDate = this.dateRange != null ? dateToString(this.dateRange[0]) : null;
      this.fyxxParams.endDate = this.dateRange != null ? dateToString(this.dateRange[1]) : null;
      this.fyxxParams.gyxmmc = this.fyxx.gyxmmc
      console.log(this.fyxxParams)
      if (this.fyxx.query == "分项汇总") {
        this.fyxxParams.jzh = this.jzh
        this.fyxxParams.xmmc = this.fyxx.xmmc
        getFyxxByKs(this.fyxxParams).then(response => {
          this.fyxxList = response.rows;
          this.fyxxTotal = response.total;
        });
      } else  {
        this.getFyxxList()
      }
    },
  }
}
</script>

<style lang="scss">
.el-tooltip__popper {
  max-width: 80%;
}
</style>
