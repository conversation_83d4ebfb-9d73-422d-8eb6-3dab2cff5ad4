<template>
  <el-form-item label="科室">
    <el-select v-model="deptSelect" placeholder="选择科室">
      <el-option v-for="item in deptList" :key="item" :value="item" :label="item" />
    </el-select>
  </el-form-item>
</template>

<script>
import { getDeptList } from '../../api/tjfx/blykfx'

export default {
  name: 'DeptConditions',
  data(){
    return {
      deptList: ['所有'],
    }
  },
  props: {
    value: {
      type: [String],
      default: ''
    }
  },
  async created() {
    await this.getDeptList()
  },
  methods: {
    async getDeptList(){
      const deptRes = await getDeptList()
      if(deptRes.is_ys) {
        this.deptList = deptRes.list
      } else {
        this.deptList.push(...deptRes.list)
      }
      this.deptSelect = this.deptList[0]
    }
  },
  computed: {
    deptSelect: {
      get(){
        return this.value
      },
      set(val){
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
