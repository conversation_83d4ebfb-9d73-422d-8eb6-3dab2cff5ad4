<template>
  <el-select clearable  ref="colorSelect" placeholder="请选择" v-model="myColor" style="width: 100px" @change="handleChange">
    <el-option
      v-for="item in colorList"
      :key="item"
      label=" "
      :value="item"
      v-html="'<div style=background-color:'+ item+';width:' +'100%'+';height:'+'90%'+'></div>'">
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: "ColorSelect",
  model: {
    prop: 'color',
    event: 'updateColor'
  },
  props: {
    color: {
      type: Object | String,
      default: null
    },
    item: {
      type: Object,
      default: null
    },
    isUpdate: {
      type: String,
      default: "true"
    },
    column: {
      type: String,
      default: "showcolor"
    }
  },
  data() {
    return {
      myColor: null,
      colorList: ["#FF8C00", "#FF0000", "#B22222", "#FFFFFF"],
    }
  },
  methods: {
    setSelectColor(color) {
      this.myColor = this.color;
      this.$nextTick(() => {
        let dom = this.$refs.colorSelect;
        if (dom) {
          dom = dom.$el.children[0];
          let inputDom = dom.querySelectorAll(".el-input__inner");
          let icon = dom.querySelectorAll(".el-input__icon");
          inputDom[0].style["background-color"] = color;
          icon[0].style["color"] = "black";
        }
      })
    },
    handleChange(val) {
      this.setSelectColor(val);
      this.item[this.column] = val
      if (this.isUpdate == 'true') {
        this.$emit('updateColor', this.item, val);
      }
    }
  },
  created() {
    if (this.color && this.color.length > 0) {
      this.setSelectColor(this.color)
    }
  },
  watch: {
    'color': function (val) {
      this.setSelectColor(val);
    }
  }
}
</script>

<style scoped>

</style>

