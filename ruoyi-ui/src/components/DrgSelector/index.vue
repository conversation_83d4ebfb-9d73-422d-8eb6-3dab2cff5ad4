<template>
  <el-form-item label="DRG分组">
    <el-select
      v-model="selectedDrg"
      filterable
      clearable
      placeholder="请选择DRG分组"
      :loading="loading"
    >
      <el-option
        v-for="item in drgOptions"
        :key="item.drgbh"
        :value="item.drgbh"
        :label="`${item.drgbh}[${item.drgmc}]`"
      />
    </el-select>
  </el-form-item>
</template>

<script>
import { listDrgdict2 } from "@/api/drg/drgdict";

export default {
  name: 'DrgSelector',
  props: {
    value: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      drgOptions: [],
      loading: false
    };
  },
  computed: {
    selectedDrg: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
  },
  async created() {
    await this.loadDrgList();
  },
  methods: {
    async loadDrgList() {
      try {
        this.loading = true;
        const { code, rows } = await listDrgdict2();
        if (code === 200) {
          this.drgOptions = rows;
        }
      } catch (error) {
        console.error('加载DRG列表失败:', error);
      } finally {
        this.loading = false;
      }
    },
  }
};
</script>
