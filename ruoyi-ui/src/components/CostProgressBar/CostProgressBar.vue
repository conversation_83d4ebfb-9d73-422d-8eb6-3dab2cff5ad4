<template>
  <div class="cost-progress-container">
    <!-- 进度条标题 -->
    <div class="progress-header" v-if="title">
      <h3>{{ title }}</h3>
    </div>

    <div class="progress-wrapper">
      <el-tooltip
        :content="tooltipContent"
        placement="top"
        effect="dark"
      >
        <el-progress
          :percentage="totalPercentage"
          :show-text="false"
          :stroke-width="strokeWidth"
          :color="progressColor"
        ></el-progress>
      </el-tooltip>

      <!-- 极低费用标识 -->
      <div
        class="marker low-marker"
        :style="{ left: lowPercentage + '%' }"
        v-if="showLowCost"
      >
        <div class="marker-line low-line"></div>
        <div class="marker-label low-label">
          {{ lowLabel }}: {{ formatCurrency(lowCost) }}
        </div>
      </div>

      <!-- 极高费用标识 -->
      <div
        class="marker high-marker"
        :style="{ left: highPercentage + '%' }"
        v-if="showHighCost"
      >
        <div class="marker-line high-line"></div>
        <div class="marker-label high-label">
          {{ highLabel }}: {{ formatCurrency(highCost) }}
        </div>
      </div>

      <!-- 总费用标识 -->
<!--      <div-->
<!--        class="marker total-marker"-->
<!--        :style="{ left: totalPercentage + '%' }"-->
<!--      >-->
<!--        <div class="marker-dot"></div>-->
<!--        <div class="marker-label total-label">-->
<!--          {{ totalLabel }}: {{ formatCurrency(totalCost) }}-->
<!--        </div>-->
<!--      </div>-->
    </div>
  </div>
</template>

<script>
export default {
  name: 'CostProgressBar',
  props: {
    // 费用数据
    totalCost: {
      type: Number,
      required: true
    },
    lowCost: {
      type: Number,
      default: 0
    },
    highCost: {
      type: Number,
      default: 0
    },

    // 显示控制
    title: {
      type: String,
      default: ''
    },
    showLowCost: {
      type: Boolean,
      default: true
    },
    showHighCost: {
      type: Boolean,
      default: true
    },
    showLegend: {
      type: Boolean,
      default: true
    },

    // 样式配置
    strokeWidth: {
      type: Number,
      default: 12
    },
    progressColor: {
      type: String,
      default: '#409eff'
    },

    // 标签文字
    totalLabel: {
      type: String,
      default: '总费用'
    },
    lowLabel: {
      type: String,
      default: '极低费用'
    },
    highLabel: {
      type: String,
      default: '极高费用'
    }
  },
  computed: {
    maxValue() {
      return Math.max(this.totalCost, this.highCost, this.lowCost) * 1.1;
    },
    lowPercentage() {
      return Math.min((this.lowCost / this.maxValue) * 100, 100);
    },
    highPercentage() {
      return Math.min((this.highCost / this.maxValue) * 100, 100);
    },
    totalPercentage() {
      return Math.min((this.totalCost / this.maxValue) * 100, 100);
    },
    tooltipContent() {
      return `${this.totalLabel}: ${this.formatCurrency(this.totalCost)}
${this.lowLabel}: ${this.formatCurrency(this.lowCost)}
${this.highLabel}: ${this.formatCurrency(this.highCost)}`;
    }
  },
  methods: {
    formatCurrency(amount) {
      return `¥${amount.toLocaleString()}`;
    }
  }
}
</script>

<style scoped>
.cost-progress-container {
  width: 100%;
  margin-bottom: 10px;
}

.progress-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.progress-wrapper {
  position: relative;
  margin-bottom: 25px;
}

.marker {
  position: absolute;
  top: -3px;
  transform: translateX(-50%);
  z-index: 1;
  font-weight: bold;
}

.marker-line {
  width: 2px;
  height: 16px;
  border-radius: 1px;
  margin: 0 auto;
}

.low-line {
  background-color: #e6a23c;
}

.high-line {
  background-color: #f56c6c;
}

.marker-dot {
  width: 6px;
  height: 6px;
  background-color: #409eff;
  border-radius: 50%;
  border: 1px solid #fff;
  box-shadow: 0 0 0 1px #409eff;
  margin: 0 auto;
}

.marker-label {
  position: absolute;
  top: 18px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  font-size: 11px;
  font-weight: 400;
  line-height: 1.2;
}

.low-label {
  color: #e6a23c;
}

.high-label {
  color: #f56c6c;
}

.total-label {
  top: -20px;
  color: #409eff;
  font-weight: 500;
  background: #fff;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #409eff;
  font-size: 11px;
}

/* 图例样式 */
.legend {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 5px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #606266;
}

.legend-icon {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.total-icon {
  background-color: #409eff;
  border-radius: 50%;
}

.low-icon {
  background-color: #e6a23c;
  height: 3px;
}

.high-icon {
  background-color: #f56c6c;
  height: 3px;
}
</style>
