<script>
import {
  dateToString,
  getMonthFirstDay,
  getMonthFirstDayStr,
  getTodayLastSecond,
  getTodayLastSecondStr
} from '@/utils/dateUtils'

export default {
  data(){
    return {
      dialogVisible: false,
      startDate: getMonthFirstDay(),
      endDate: getTodayLastSecond(),
      params:{
        startDate: getMonthFirstDayStr(),
        endDate: getTodayLastSecondStr()
      }
    }
  },
  methods: {
    openDialog(){
      this.dialogVisible = true;
    },
    handleCalcel(){
      this.dialogVisible = false;
    },
    handleGenerate(){
      console.log(this.params)
      this.params = {
        startDate : dateToString(this.startDate),
        endDate : dateToString(this.endDate)
      }
      this.download(`/word/report/DRGWord/export?startDate=${this.params.startDate}&endDate=${this.params.endDate}`, {  }, `DRG工作简报_${new Date().getTime()}.docx`)
    }
  }
}
</script>

<template>
  <div>
    <svg-icon icon-class="download" @click="openDialog"/>

    <el-dialog title="时间范围选择" :visible.sync="dialogVisible"  append-to-body>
      <div style="width: 100%">
        <el-form size="small" :inline="true" >
          <el-row>
            <el-col :span="12">
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="startDate"
                  type="datetime"
                  placeholder="选择开始时间"
                  default-time="00:00:00"/>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="结束时间">
                <el-date-picker
                  v-model="endDate"
                  type="datetime"
                  placeholder="选择结束时间"
                  default-time="23:59:59"/>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleGenerate" >生成</el-button>
        <el-button type="info" @click="handleCalcel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
