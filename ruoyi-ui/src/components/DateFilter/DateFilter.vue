<template>
  <div>

    <el-form-item label="时间类型"
                  size="small"
                  label-width="68px">
      <el-select v-model="filter.datetype">
        <el-option v-for="item in datetypeOptions" :key="item.key" :value="item.value" :label="item.label"/>
      </el-select>
    </el-form-item>
    <el-form-item label="开始时间"
                  size="small"
    label-width="68px">
      <el-date-picker
        v-model="filter.startDate"
        type="datetime"
        placeholder="选择开始时间"
        default-time="00:00:00"
        value-format="yyyy-MM-dd HH:mm:ss">
      </el-date-picker>
    </el-form-item>

    <el-form-item label="结束时间"
                  size="small"
                  label-width="68px">
      <el-date-picker
        v-model="filter.endDate"
        type="datetime"
        placeholder="选择结束时间"
        default-time="23:59:59"
        value-format="yyyy-MM-dd HH:mm:ss">
      </el-date-picker>
    </el-form-item>
  </div>
</template>

<script>
import { getMonthFirstDayStr, getTodayLastSecondStr } from '../../utils/dateUtils'

export default {
  name: 'dateFilter',
  data(){
    return {
      datetypeOptions: [
        {
          value: 'jsdate',
          key: '结算时间',
          label: '结算时间'
        },
        {
          value: 'cydate',
          key: '出院时间',
          label: '出院时间'
        }
      ]
    }
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        startDate: getMonthFirstDayStr(),
        endDate: getTodayLastSecondStr(),
        datetype: 'jsdate'
      })
    }
  },
  computed: {
    filter: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
