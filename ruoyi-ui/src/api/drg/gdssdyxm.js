import request from '@/utils/request'

// 查询手术项目对应明细列表
export function listGdssdyxm(query) {
  return request({
    url: '/drg/gdssdyxm/list',
    method: 'get',
    params: query
  })
}

// 查询手术项目对应明细详细
export function getGdssdyxm(id) {
  return request({
    url: '/drg/gdssdyxm/' + id,
    method: 'get'
  })
}

// 新增手术项目对应明细
export function addGdssdyxm(data) {
  return request({
    url: '/drg/gdssdyxm',
    method: 'post',
    data: data
  })
}

// 修改手术项目对应明细
export function updateGdssdyxm(data) {
  return request({
    url: '/drg/gdssdyxm',
    method: 'put',
    data: data
  })
}

// 删除手术项目对应明细
export function delGdssdyxm(id) {
  return request({
    url: '/drg/gdssdyxm/' + id,
    method: 'delete'
  })
}
