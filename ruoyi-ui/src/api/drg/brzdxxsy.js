import request from '@/utils/request'

// 查询首页临床诊断列表
export function listZdxxSy(query) {
  return request({
    url: '/drg/brzdxxsy/list',
    method: 'get',
    params: query
  })
}

// 查询首页临床诊断详细
export function getZdxxSy(id) {
  return request({
    url: '/drg/brzdxxsy/' + id,
    method: 'get'
  })
}

// 新增首页临床诊断
export function addZdxxSy(data) {
  return request({
    url: '/drg/brzdxxsy',
    method: 'post',
    data: data
  })
}

// 修改首页临床诊断
export function updateZdxxSy(data) {
  return request({
    url: '/drg/brzdxxsy',
    method: 'put',
    data: data
  })
}

// 删除首页临床诊断
export function delZdxxSy(id) {
  return request({
    url: '/drg/brzdxxsy/' + id,
    method: 'delete'
  })
}

export function listSyZdxx(query) {
  return request({
    url: '/drg/brzdxxsy/zdxxsy',
    method: 'get',
    params: query
  })
}
