import request from '@/utils/request'

export function getIcdList(queryVo, pageNum, pageSize) {
  return request({
    method: 'post',
    url: '/system/icd10ybdy/icdMaintainList',
    data: queryVo,
    params: {pageNum, pageSize}
  })
}

export function compareIcdHis(compareIcdHisVo) {
  return request({
    url: '/system/icd10ybdy/compareIcdHis',
    method: 'get',
    params: compareIcdHisVo
  })
}


export function updateIcdInfo(vo) {
  return request({
    url: '/system/icd10ybdy/icdUpdate',
    data: vo,
    method: 'post'
  })
}

export function icdAdd(vo) {
  return request({
    url: `/system/icd10ybdy/icdAdd`,
    data: vo,
    method: 'post'
  })
}
