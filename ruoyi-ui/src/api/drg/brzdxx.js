import request from '@/utils/request'

// 查询诊断信息列表
export function listBrzdxx(query) {
  return request({
    url: '/drg/brzdxx/list',
    method: 'get',
    params: query
  })
}


// 查询诊断信息列表
export function listZdxx(query) {
  return request({
    url: '/drg/brzdxx/zdxx',
    method: 'get',
    params: query
  })
}
// 查询手术信息列表
export function listSsxx(query) {
  return request({
    url: '/drg/brzdxx/ssxx',
    method: 'get',
    params: query
  })
}
// 查询首页诊断信息列表
export function listZdxxSy(query) {
  return request({
    url: '/drg/brzdxx/zdxxsy',
    method: 'get',
    params: query
  })
}
// 查询医保诊断信息列表
export function listZdxxYb(query) {
  return request({
    url: '/drg/brzdxx/zdxxyb',
    method: 'get',
    params: query
  })
}

// 查询病种类型
export function listBzType(query) {
  return request({
    url: '/drg/brzdxx/bztype',
    method: 'get',
    params: query
  })
}
// 查询诊断信息详细
export function getBrzdxx(id) {
  return request({
    url: '/drg/brzdxx/' + id,
    method: 'get'
  })
}

// 新增诊断信息
export function addBrzdxx(data) {
  return request({
    url: '/drg/brzdxx',
    method: 'post',
    data: data
  })
}

// 修改诊断信息
export function updateBrzdxx(data) {
  return request({
    url: '/drg/brzdxx',
    method: 'put',
    data: data
  })
}

// 删除诊断信息
export function delBrzdxx(id) {
  return request({
    url: '/drg/brzdxx/' + id,
    method: 'delete'
  })
}

// 删除诊断信息
export function delBrzdxxById(query) {
  return request({
    url: '/drg/brzdxx/remove',
    method: 'get',
    params: query
  })
}


// 查询临床诊断信息列表
export function icd10(query) {
  return request({
    url: '/drg/brzdxx/Icd10',
    method: 'get',
    params: query
  })
}
export function icd10yb(query) {
  return request({
    url: '/drg/brzdxx/Icd10Yb',
    method: 'get',
    params: query
  })
}

// 查询医保诊断信息列表
export function selectYbzdByLczd(query) {
  return request({
    url: '/drg/brzdxx/selectYbzdByLczd',
    method: 'get',
    params: query
  })
}
// 查询诊断信息列表
export function icd09(query) {
  return request({
    url: '/drg/brzdxx/Icd09',
    method: 'get',
    params: query
  })
}
export function icd09yb(query) {
  return request({
    url: '/drg/brzdxx/Icd09Yb',
    method: 'get',
    params: query
  })
}

export function selectZdFyxx(query) {
  return request({
    url: '/drg/brzdxx/selectZdFyxx',
    method: 'get',
    params: query
  })
}

export function selectZyzd(query) {
  return request({
    url: '/drg/brzdxx/selectZyzd',
    method: 'get',
    params: query
  })
}
export function saveZd(zdxx,ssxx,brid,zyid,yhbs) {
  return request({
    url: '/drg/yfz/savedata',
    method: 'post',
    params: {zdxx,ssxx,brid,zyid,yhbs}
  })
}

export function yjbm(zdxx) {
  return request({
    url: '/drg/yfz/yjbm',
    method: 'post',
    params: {zdxx}
  })
}


export function zzdtj(brid,zyid,yhbs) {
  return request({
    url: '/drg/znbmtj/znbm',
    method: 'post',
    params: {brid,zyid,yhbs}
  })
}


export function znbmAll() {
  return request({
    url: '/drg/znbmtj/znbmAll',
    method: 'post'
  })
}



export function selectBrzdfy(brxx) {
  return request({
    url: '/drg/brzdxx/selectBrzdfy',
    method: 'get',
    params: brxx
  })
}

export function listZdxxByYb(condition) {
  return request({
    url: '/drg/brzdxx/listZdxxByYb?condition=' + condition,
    method: 'get',
  })
}

export function listZdxxByLc(condition) {
  return request({
    url: '/drg/brzdxx/listZdxxByLc?condition=' + condition,
    method: 'get',
  })
}

export function selectBrzdxxByOnePatient(brxx){
  return request({
    url: '/drg/brzdxx/getBrzdxxByOnePatient',
    method: 'get',
    params: brxx
  })
}
