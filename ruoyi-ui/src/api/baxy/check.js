import request from '@/utils/request'

// 查询病案校验列表
export function listCheckbz(query) {
  return request({
    url: '/baxy/checkbz/list',
    method: 'get',
    params: query
  })
}

export function listCheckbzPf(query) {
  return request({
    url: '/baxy/checkpf/list',
    method: 'get',
    params: query
  })
}

export function listCheckbz2(query) {
  return request({
    url: '/baxy/checkbz/list2',
    method: 'get',
    params: query
  })
}

export function getBBasyCheckBzErrortype() {
  return request({
    url: '/baxy/checkbz/getBBasyCheckBzErrortype',
    method: 'get',
  })
}

// 查询病案校验详细
export function getCheckbz(id) {
  return request({
    url: '/baxy/checkbz/' + id,
    method: 'get'
  })
}

// 新增病案校验
export function addCheckbz(data) {
  return request({
    url: '/baxy/checkbz',
    method: 'post',
    data: data
  })
}

// 修改病案校验
export function updateCheckbz(data) {
  return request({
    url: '/baxy/checkbz',
    method: 'put',
    data: data
  })
}

// 删除病案校验
export function delCheckbz(id) {
  return request({
    url: '/baxy/checkbz/' + id,
    method: 'delete'
  })
}
