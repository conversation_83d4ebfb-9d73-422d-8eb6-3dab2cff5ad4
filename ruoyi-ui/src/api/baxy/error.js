import request from '@/utils/request'

// 查询质控信息列表
export function listError(query) {
  return request({
    url: '/baxy/error/list',
    method: 'get',
    params: query
  })
}

export function listErrorCjq(query) {
  return request({
    url: '/baxy/error/listCjq',
    method: 'get',
    params: query
  })
}


export function deductScoreDetails(query) {
  return request({
    url: '/baxy/error/deductScoreDetails',
    method: 'get',
    params: query
  })
}

// 查询质控信息详细
export function getError(id) {
  return request({
    url: '/baxy/error/' + id,
    method: 'get'
  })
}

// 新增质控信息
export function addError(data) {
  return request({
    url: '/baxy/error',
    method: 'post',
    data: data
  })
}

export function addErrorGetId(data) {
  return request({
    url: '/baxy/error/addError',
    method: 'post',
    data: data
  })
}


// 修改质控信息
export function updateError(data) {
  return request({
    url: '/baxy/error',
    method: 'put',
    data: data
  })
}

// 删除质控信息
export function delError(id) {
  return request({
    url: '/baxy/error/' + id,
    method: 'delete'
  })
}
