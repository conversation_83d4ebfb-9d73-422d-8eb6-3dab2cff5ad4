import request from '@/utils/request'

// 查询诊断判断列表
export function listZdpd(query) {
  return request({
    url: '/system/zdpd/list',
    method: 'get',
    params: query
  })
}

// 查询诊断判断详细
export function getZdpd(id) {
  return request({
    url: '/system/zdpd/' + id,
    method: 'get'
  })
}

// 新增诊断判断
export function addZdpd(data) {
  return request({
    url: '/system/zdpd',
    method: 'post',
    data: data
  })
}

// 修改诊断判断
export function updateZdpd(data) {
  return request({
    url: '/system/zdpd',
    method: 'put',
    data: data
  })
}

// 删除诊断判断
export function delZdpd(id) {
  return request({
    url: '/system/zdpd/' + id,
    method: 'delete'
  })
}
