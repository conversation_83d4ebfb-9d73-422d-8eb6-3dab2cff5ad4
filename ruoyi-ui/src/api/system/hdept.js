import request from '@/utils/request'

// 查询hdept列表
export function listHdept(query) {
  return request({
    url: '/system/hdept/list',
    method: 'get',
    params: query
  })
}

export function getUserDeptList() {
  return request({
    url: '/system/hdept/getUserDeptList',
    method: 'get'
  })
}

export function getUserDeptIdList() {
  return request({
    url: '/system/hdept/getUserDeptIdList',
    method: 'get'
  })
}

export function getUserDeptNameList() {
  return request({
    url: '/system/hdept/getUserDeptNameList',
    method: 'get'
  })
}



export function selectHDeptNameList() {
  return request({
    url: '/system/hdept/selectHDeptNameList',
    method: 'get'
  })
}

export function selectAllHDeptList() {
  return request({
    url: '/system/hdept/listAll',
    method: 'get'
  })
}

export function selectHdeptListByUser(userId) {
  return request({
    url: `/system/hDept/getUserDepts/${userId}`
  })
}


// 查询hdept详细
export function getHdept(hDeptId) {
  return request({
    url: '/system/hdept/' + hDeptId,
    method: 'get'
  })
}

// 新增hdept
export function addHdept(data) {
  return request({
    url: '/system/hdept',
    method: 'post',
    data: data
  })
}

// 修改hdept
export function updateHdept(data) {
  return request({
    url: '/system/hdept',
    method: 'put',
    data: data
  })
}

// 删除hdept
export function delHdept(hDeptId) {
  return request({
    url: '/system/hdept/' + hDeptId,
    method: 'delete'
  })
}

