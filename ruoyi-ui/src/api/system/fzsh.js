import request from '@/utils/request'

// 查询DRG维护审核列表
export function listFzsh(query) {
  return request({
    url: '/system/fzsh/list',
    method: 'get',
    params: query
  })
}

// 查询DRG维护审核详细
export function getFzsh(id) {
  return request({
    url: '/system/fzsh/' + id,
    method: 'get'
  })
}

// 新增DRG维护审核
export function addFzsh(data) {
  return request({
    url: '/system/fzsh',
    method: 'post',
    data: data
  })
}

// 修改DRG维护审核
export function updateFzsh(data) {
  return request({
    url: '/system/fzsh',
    method: 'put',
    data: data
  })
}

// 删除DRG维护审核
export function delFzsh(id) {
  return request({
    url: '/system/fzsh/' + id,
    method: 'delete'
  })
}

export function submitFzsh(data) {
  return request({
    url: '/system/fzsh/handleSubmit',
    method: 'post',
    data: data
  })
}

export function handleAudits(data) {
  return request({
    url: '/system/fzsh/handleAudits',
    method: 'post',
    data: data
  })
}

export function getScqk(){
  return request({
    url: '/system/fzsh/syscqk',
    method: 'get'
  })
}

export function selectLastSubmitFzsh(brid, zyid) {
  return request({
    url: `/system/record/getLastSubmitByPatient`,
    method: 'get',
    params: {
      brid, zyid
    }
  })
}

// 保存诊断手术信息
export function saveZdSs(data) {
  return request({
    url: '/system/fzsh/saveZdSs',
    method: 'post',
    data
  })
}
