import request from '@/utils/request'

// 查询定额信息列表
export function listDexx(query) {
  return request({
    url: '/system/dexx/list',
    method: 'get',
    params: query
  })
}

// 查询定额信息详细
export function getDexx(id) {
  return request({
    url: '/system/dexx/' + id,
    method: 'get'
  })
}

// 新增定额信息
export function addDexx(data) {
  return request({
    url: '/system/dexx',
    method: 'post',
    data: data
  })
}

// 修改定额信息
export function updateDexx(data) {
  return request({
    url: '/system/dexx',
    method: 'put',
    data: data
  })
}

// 删除定额信息
export function delDexx(id) {
  return request({
    url: '/system/dexx/' + id,
    method: 'delete'
  })
}
