import request from '@/utils/request'

// 查询医保智能分析列表
export function listZnfx(query) {
  return request({
    url: '/system/znfx/list',
    method: 'get',
    params: query
  })
}

// 查询医保智能分析详细
export function getZnfx(id) {
  return request({
    url: '/system/znfx/' + id,
    method: 'get'
  })
}

// 新增医保智能分析
export function addZnfx(data) {
  return request({
    url: '/system/znfx',
    method: 'post',
    data: data
  })
}

// 修改医保智能分析
export function updateZnfx(data) {
  return request({
    url: '/system/znfx',
    method: 'put',
    data: data
  })
}

// 删除医保智能分析
export function delZnfx(id) {
  return request({
    url: '/system/znfx/' + id,
    method: 'delete'
  })
}
