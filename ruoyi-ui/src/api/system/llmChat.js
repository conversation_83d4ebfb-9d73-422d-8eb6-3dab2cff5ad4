import request from '@/utils/request'

// 查询AI对话提示字典列表
export function listLlmChat(query) {
  return request({
    url: '/system/llmChat/list',
    method: 'get',
    params: query
  })
}

export function changeLlmDictStatus(id, status) {
  return request({
    url: '/system/llmChat/changeStatus',
    method: 'post',
    data: {id, status}
  })
}

// 查询AI对话提示字典详细
export function getLlmChat(id) {
  return request({
    url: '/system/llmChat/' + id,
    method: 'get'
  })
}

// 新增AI对话提示字典
export function addLlmChat(data) {
  return request({
    url: '/system/llmChat',
    method: 'post',
    data: data
  })
}

// 修改AI对话提示字典
export function updateLlmChat(data) {
  return request({
    url: '/system/llmChat',
    method: 'put',
    data: data
  })
}

// 删除AI对话提示字典
export function delLlmChat(id) {
  return request({
    url: '/system/llmChat/' + id,
    method: 'delete'
  })
}
