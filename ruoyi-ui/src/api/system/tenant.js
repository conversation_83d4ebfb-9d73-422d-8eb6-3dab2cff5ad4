import request from '@/utils/request'

// 查询租户管理列表
export function listTenant(query) {
  return request({
    url: '/system/tenant/list',
    method: 'get',
    params: query
  })
}

// 查询租户管理详细
export function getTenant(id) {
  return request({
    url: '/system/tenant/' + id,
    method: 'get'
  })
}

// 新增租户管理
export function addTenant(data) {
  return request({
    url: '/system/tenant',
    method: 'post',
    data: data
  })
}

// 修改租户管理
export function updateTenant(data) {
  return request({
    url: '/system/tenant',
    method: 'put',
    data: data
  })
}

// 删除租户管理
export function delTenant(id) {
  return request({
    url: '/system/tenant/' + id,
    method: 'delete'
  })
}

// 查询已分配用户列表
export function allocatedUserList(query) {
  return request({
    url: '/system/tenant/authUser/allocatedList',
    method: 'get',
    params: query
  })
}

// 取消用户授权角色
export function authUserCancel(data) {
  return request({
    url: '/system/tenant/authUser/cancel',
    method: 'put',
    data: data
  })
}

// 批量取消用户授权角色
export function authUserCancelAll(data) {
  return request({
    url: '/system/tenant/authUser/cancelAll',
    method: 'put',
    params: data
  })
}


// 查询租户未分配用户列表
export function unallocatedUserList(query) {
  return request({
    url: '/system/tenant/authUser/unallocatedList',
    method: 'get',
    params: query
  })
}

// 分配用户选择
export function authUserSelectAll(data) {
  return request({
    url: '/system/tenant/authUser/selectAll',
    method: 'put',
    params: data
  })
}
