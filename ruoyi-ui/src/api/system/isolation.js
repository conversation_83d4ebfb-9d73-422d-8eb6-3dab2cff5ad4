import request from '@/utils/request'

// 查询数据隔离管理列表
export function listIsolation(query) {
  return request({
    url: '/system/isolation/list',
    method: 'get',
    params: query
  })
}

export function listIsolationTable(query) {
  return request({
    url: '/system/isolation/listTable',
    method: 'get',
    params: query
  })
}

export function listIsolationTableProcess(query) {
  return request({
    url: '/system/isolation/listProcess',
    method: 'get',
    params: query
  })
}

// 查询数据隔离管理详细
export function getIsolation(id) {
  return request({
    url: '/system/isolation/' + id,
    method: 'get'
  })
}

// 新增数据隔离管理
export function addIsolation(data) {
  return request({
    url: '/system/isolation',
    method: 'post',
    data: data
  })
}

// 修改数据隔离管理
export function updateIsolation(data) {
  return request({
    url: '/system/isolation',
    method: 'put',
    data: data
  })
}

// 删除数据隔离管理
export function delIsolation(id) {
  return request({
    url: '/system/isolation/' + id,
    method: 'delete'
  })
}

export function testSQL(originSql) {
  return request({
    url: '/system/isolation/testSQL',
    method: 'post',
    params: {originSql}
  })
}

export function reflush() {
  return request({
    url: '/system/isolation/reflush',
    method: 'post',
  })
}
