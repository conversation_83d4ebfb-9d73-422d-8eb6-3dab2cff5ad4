import request from '@/utils/request'

// 查询诊断手术对应表列表
export function listIcddyss(query) {
  return request({
    url: '/system/icddyss/list',
    method: 'get',
    params: query
  })
}
export function listIcddyss2(query) {
  return request({
    url: '/system/icddyss/list2',
    method: 'get',
    params: query
  })
}
// 查询诊断手术对应表详细
export function getIcddyss(bzbm) {
  return request({
    url: '/system/icddyss/' + bzbm,
    method: 'get'
  })
}

// 新增诊断手术对应表
export function addIcddyss(data) {
  return request({
    url: '/system/icddyss',
    method: 'post',
    data: data
  })
}

// 修改诊断手术对应表
export function updateIcddyss(data) {
  return request({
    url: '/system/icddyss',
    method: 'put',
    data: data
  })
}

// 删除诊断手术对应表
export function delIcddyss(query) {
  return request({
    url: '/system/icddyss/remove',
    method: 'get',
    params: query
  })
}
