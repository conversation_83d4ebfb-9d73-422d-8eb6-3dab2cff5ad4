import request from '@/utils/request'

// 查询参数列表
export function listOption(query) {
  return request({
    url: '/drg/option/list',
    method: 'get',
    params: query
  })
}

// 查询参数详细
export function getOption(cCode) {
  return request({
    url: '/drg/option/' + cCode,
    method: 'get'
  })
}

// 新增参数
export function addOption(data) {
  return request({
    url: '/drg/option',
    method: 'post',
    data: data
  })
}

// 修改参数
export function updateOption(data) {
  return request({
    url: '/drg/option',
    method: 'put',
    data: data
  })
}

// 删除参数
export function delOption(cCode) {
  return request({
    url: '/drg/option/' + cCode,
    method: 'delete'
  })
}


export function getYyMame() {
  return request({
    url: '/drg/option/yyname',
    method: 'get'
  })
}
