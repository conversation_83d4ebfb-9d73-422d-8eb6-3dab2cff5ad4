import request from '@/utils/request'

// 查询检验信息列表
export function listJyxx(query) {
  return request({
    url: '/mlcx/jyxx/list',
    method: 'get',
    params: query
  })
}

export function jyxxAll(query){
  return request({
    url: '/mlcx/jyxx/all',
    method: 'get',
    params: query
  })
}

// 查询检验信息详细
export function getJyxx(id) {
  return request({
    url: '/mlcx/jyxx/' + id,
    method: 'get'
  })
}

// 新增检验信息
export function addJyxx(data) {
  return request({
    url: '/mlcx/jyxx',
    method: 'post',
    data: data
  })
}

// 修改检验信息
export function updateJyxx(data) {
  return request({
    url: '/mlcx/jyxx',
    method: 'put',
    data: data
  })
}

// 删除检验信息
export function delJyxx(id) {
  return request({
    url: '/mlcx/jyxx/' + id,
    method: 'delete'
  })
}
