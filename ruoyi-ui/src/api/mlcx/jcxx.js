import request from '@/utils/request'

// 查询检查信息列表
export function listJcxx(query) {
  return request({
    url: '/mlcx/jcxx/list',
    method: 'get',
    params: query
  })
}

// 查询检查信息列表
export function allJcxx(query) {
  return request({
    url: '/mlcx/jcxx/all',
    method: 'get',
    params: query
  })
}


// 查询检查信息详细
export function getJcxx(id) {
  return request({
    url: '/mlcx/jcxx/' + id,
    method: 'get'
  })
}

// 新增检查信息
export function addJcxx(data) {
  return request({
    url: '/mlcx/jcxx',
    method: 'post',
    data: data
  })
}

// 修改检查信息
export function updateJcxx(data) {
  return request({
    url: '/mlcx/jcxx',
    method: 'put',
    data: data
  })
}

// 删除检查信息
export function delJcxx(id) {
  return request({
    url: '/mlcx/jcxx/' + id,
    method: 'delete'
  })
}
