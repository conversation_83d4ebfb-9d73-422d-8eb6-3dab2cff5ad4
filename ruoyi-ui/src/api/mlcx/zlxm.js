import request from '@/utils/request'

// 查询诊疗目录列表
export function listZlxm(query) {
  return request({
    url: '/mlcx/zlxm/list',
    method: 'get',
    params: query
  })
}

// 查询诊疗目录详细
export function getZlxm(xmlsh) {
  return request({
    url: '/mlcx/zlxm/' + xmlsh,
    method: 'get'
  })
}

// 新增诊疗目录
export function addZlxm(data) {
  return request({
    url: '/mlcx/zlxm',
    method: 'post',
    data: data
  })
}

// 修改诊疗目录
export function updateZlxm(data) {
  return request({
    url: '/mlcx/zlxm',
    method: 'put',
    data: data
  })
}

// 删除诊疗目录
export function delZlxm(xmlsh) {
  return request({
    url: '/mlcx/zlxm/' + xmlsh,
    method: 'delete'
  })
}
