import request from '@/utils/request'

// 查询诊断排序列表
export function listZdpx(query) {
  return request({
    url: '/drg/zdpx/list',
    method: 'get',
    params: query
  })
}

// 查询诊断排序详细
export function getZdpx(id) {
  return request({
    url: '/drg/zdpx/' + id,
    method: 'get'
  })
}

// 新增诊断排序
export function addZdpx(data) {
  return request({
    url: '/drg/zdpx',
    method: 'post',
    data: data
  })
}

// 修改诊断排序
export function updateZdpx(data) {
  return request({
    url: '/drg/zdpx',
    method: 'put',
    data: data
  })
}

// 删除诊断排序
export function delZdpx(id) {
  return request({
    url: '/drg/zdpx/' + id,
    method: 'delete'
  })
}
