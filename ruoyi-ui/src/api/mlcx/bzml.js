import request from '@/utils/request'

// 查询病种目录列表
export function listBzml(query) {
  return request({
    url: '/mlcx/bzml/list',
    method: 'get',
    params: query
  })
}

// 查询特病病种目录列表
export function listBzmltb(query) {
  return request({
    url: '/mlcx/bzml/listtb',
    method: 'get',
    params: query
  })
}

// 查询病种目录详细
export function getBzml(bzbm) {
  return request({
    url: '/mlcx/bzml/' + bzbm,
    method: 'get'
  })
}

// 新增病种目录
export function addBzml(data) {
  return request({
    url: '/mlcx/bzml',
    method: 'post',
    data: data
  })
}

// 修改病种目录
export function updateBzml(data) {
  return request({
    url: '/mlcx/bzml',
    method: 'put',
    data: data
  })
}

// 删除病种目录
export function delBzml(bzbm) {
  return request({
    url: '/mlcx/bzml/' + bzbm,
    method: 'delete'
  })
}


export function getjbbmml(type,zdxx) {
  return request({
    url: '/ruoyi/bzml/getjbbmml',
    method: 'post',
    params: {type,zdxx}
  })
}


export function getjbbmmlList(query) {
  return request({
    url: '/ruoyi/bzml/getjbbmmlList',
    method: 'get',
    params: query
  })
}

