import request from '@/utils/request'

// 查询手麻信息列表
export function listSmxx(query) {
  return request({
    url: '/mlcx/smxx/list',
    method: 'get',
    params: query
  })
}

// 查询手麻信息详细
export function getSmxx(id) {
  return request({
    url: '/mlcx/smxx/' + id,
    method: 'get'
  })
}

// 新增手麻信息
export function addSmxx(data) {
  return request({
    url: '/mlcx/smxx',
    method: 'post',
    data: data
  })
}

// 修改手麻信息
export function updateSmxx(data) {
  return request({
    url: '/mlcx/smxx',
    method: 'put',
    data: data
  })
}

// 删除手麻信息
export function delSmxx(id) {
  return request({
    url: '/mlcx/smxx/' + id,
    method: 'delete'
  })
}
