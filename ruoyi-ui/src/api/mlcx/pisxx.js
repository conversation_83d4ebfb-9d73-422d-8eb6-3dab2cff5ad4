import request from '@/utils/request'

// 查询病理信息列表
export function listPisxx(query) {
  return request({
    url: '/mlcx/pisxx/list',
    method: 'get',
    params: query
  })
}

// 查询病理信息详细
export function getPisxx(id) {
  return request({
    url: '/mlcx/pisxx/' + id,
    method: 'get'
  })
}

// 新增病理信息
export function addPisxx(data) {
  return request({
    url: '/mlcx/pisxx',
    method: 'post',
    data: data
  })
}

// 修改病理信息
export function updatePisxx(data) {
  return request({
    url: '/mlcx/pisxx',
    method: 'put',
    data: data
  })
}

// 删除病理信息
export function delPisxx(id) {
  return request({
    url: '/mlcx/pisxx/' + id,
    method: 'delete'
  })
}
