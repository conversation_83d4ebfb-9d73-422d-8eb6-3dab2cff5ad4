import request from '@/utils/request'

// 查询患者目录列表
export function listBrxx(query) {
  return request({
    url: '/mlcx/brxx/list',
    method: 'get',
    params: query
  })
}
export function updateBrxxShzt(query) {
  return request({
    url: '/mlcx/brxx/updateBrxxShzt',
    method: 'get',
    params: query
  })
}
// 查询患者目录详细
export function getBrxx(brtype) {
  return request({
    url: '/mlcx/brxx/' + brtype,
    method: 'get'
  })
}

// 新增患者目录
export function addBrxx(data) {
  return request({
    url: '/mlcx/brxx',
    method: 'post',
    data: data
  })
}

// 修改患者目录
export function updateBrxx(data) {
  return request({
    url: '/mlcx/brxx',
    method: 'put',
    data: data
  })
}

// 删除患者目录
export function delBrxx(brtype) {
  return request({
    url: '/mlcx/brxx/' + brtype,
    method: 'delete'
  })
}

// 通过病人姓名和入院时间获取病人信息
export function getBrxxByKkxx(data) {
  return request({
    url: '/mlcx/brxx/getBrxxByKkxx',
    method: 'post',
    data : data
  })
}
