import request from '@/utils/request'

// 查询药品目录列表
export function listYpml(query) {
  return request({
    url: '/mlcx/ypml/list',
    method: 'get',
    params: query
  })
}

// 查询药品目录详细
export function getYpml(yplsh) {
  return request({
    url: '/mlcx/ypml/' + yplsh,
    method: 'get'
  })
}

// 新增药品目录
export function addYpml(data) {
  return request({
    url: '/mlcx/ypml',
    method: 'post',
    data: data
  })
}

// 修改药品目录
export function updateYpml(data) {
  return request({
    url: '/mlcx/ypml',
    method: 'put',
    data: data
  })
}

// 删除药品目录
export function delYpml(yplsh) {
  return request({
    url: '/mlcx/ypml/' + yplsh,
    method: 'delete'
  })
}
export function getka09(query) {
  return request({
    url: '/mlcx/ypml/getka09',
    method: 'get',
    params: query
  })
}
export function findUsetymList(query) {
  return request({
    url: '/mlcx/ypml/findUsetymList',
    method: 'get',
    params: query
  })
}
export function findUseListzlxm(query) {
  return request({
    url: '/mlcx/ypml/findUseListzlxm',
    method: 'get',
    params: query
  })
}
