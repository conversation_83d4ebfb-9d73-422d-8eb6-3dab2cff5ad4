import request from '@/utils/request'

// 查询限制信息列表
export function listXzxm(query) {
  return request({
    url: '/gksz/xzxm/list',
    method: 'get',
    params: query
  })
}

// 查询限制信息详细
export function getXzxm(id) {
  return request({
    url: '/gksz/xzxm/' + id,
    method: 'get'
  })
}

// 新增限制信息
export function addXzxm(data) {
  return request({
    url: '/gksz/xzxm',
    method: 'post',
    data: data
  })
}

// 修改限制信息
export function updateXzxm(data) {
  return request({
    url: '/gksz/xzxm',
    method: 'put',
    data: data
  })
}

// 删除限制信息
export function delXzxm(id) {
  return request({
    url: '/gksz/xzxm/' + id,
    method: 'delete'
  })
}
