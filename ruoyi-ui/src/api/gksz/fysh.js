import request from '@/utils/request'

// 查询费用审核列表
export function listFysh(query) {
  return request({
    url: '/gksz/fysh/list',
    method: 'get',
    params: query
  })
}

export function listks() {
  return request({
    url: '/gksz/fysh/listks',
    method: 'get'
  })
}

// 查询费用审核详细
export function getFysh(brtype) {
  return request({
    url: '/gksz/fysh/' + brtype,
    method: 'get'
  })
}

// 新增费用审核
export function addFysh(data) {
  return request({
    url: '/gksz/fysh',
    method: 'post',
    data: data
  })
}

// 同步费用
export function copyfyblzd(data) {
  return request({
    url: '/gksz/cysh/transfyblzd?jzh='+data.jzh,
    method: 'get',
  })
}

// 重转费用
export function refreshCost(brbs) {
  return request({
    url: `/gksz/cysh/refreshCost/${brbs}`,
    method: 'get'
  })
}

// 修改费用审核
export function updateFysh(data) {
  return request({
    url: '/gksz/fysh',
    method: 'put',
    data: data
  })
}

// 删除费用审核
export function delFysh(brtype) {
  return request({
    url: '/gksz/fysh/' + brtype,
    method: 'delete'
  })
}

export function fysh(strKey, strData, in3) {
  return request({
    url: '/gksz/cysh/checkRule',
    method: 'get',
    params: {strKey, strData, in3}
  })
}

export function batchFysh() {
  return request({
    url: '/gksz/cysh/batchFysh',
    method: 'get',
  })
}

export function batchFyshByWgjlDays() {
  return request({
    url: '/gksz/cysh/batchFyshByWgjlDays',
    method: 'get',
  })
}



export function checkPatThreeDaysAgo() {
  return request({
    url: '/gksz/cysh/checkPatThreeDaysAgo',
    method: 'get'
  })
}

export function checkHistoryPatBatch(data){
  return request({
    url: '/gksz/task/checkPatBath',
    method: 'post',
    data: data
  })
}
