import request from '@/utils/request'

// 查询费用项目违规配置列表
export function listWgpz(query) {
  return request({
    url: '/gksz/wgpz/list',
    method: 'get',
    params: query
  })
}

// 查询费用项目违规配置详细
export function getWgpz(id) {
  return request({
    url: '/gksz/wgpz/' + id,
    method: 'get'
  })
}

// 新增费用项目违规配置
export function addWgpz(data) {
  return request({
    url: '/gksz/wgpz',
    method: 'post',
    data: data
  })
}

// 修改费用项目违规配置
export function updateWgpz(data) {
  return request({
    url: '/gksz/wgpz',
    method: 'put',
    data: data
  })
}

// 删除费用项目违规配置
export function delWgpz(id) {
  return request({
    url: '/gksz/wgpz/' + id,
    method: 'delete'
  })
}
