import request from '@/utils/request'

// 查询特病用药情况列表
export function listTbyyl(query) {
  return request({
    url: '/gksz/tbyyl/list',
    method: 'get',
    params: query
  })
}

// 查询特病用药情况详细
export function getTbyyl(id) {
  return request({
    url: '/gksz/tbyyl/' + id,
    method: 'get'
  })
}

// 新增特病用药情况
export function addTbyyl(data) {
  return request({
    url: '/gksz/tbyyl',
    method: 'post',
    data: data
  })
}

// 修改特病用药情况
export function updateTbyyl(data) {
  return request({
    url: '/gksz/tbyyl',
    method: 'put',
    data: data
  })
}

// 删除特病用药情况
export function delTbyyl(id) {
  return request({
    url: '/gksz/tbyyl/' + id,
    method: 'delete'
  })
}
