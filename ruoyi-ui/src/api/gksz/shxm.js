import request from '@/utils/request'

// 查询审核项目列表
export function listShxm(query) {
  return request({
    url: '/gksz/shxm/list',
    method: 'get',
    params: query
  })
}

// 查询审核项目详细
export function getShxm(id) {
  return request({
    url: '/gksz/shxm/' + id,
    method: 'get'
  })
}

// 新增审核项目
export function addShxm(data) {
  return request({
    url: '/gksz/shxm',
    method: 'post',
    data: data
  })
}

// 修改审核项目
export function updateShxm(data) {
  return request({
    url: '/gksz/shxm',
    method: 'put',
    data: data
  })
}

// 删除审核项目
export function delShxm(id) {
  return request({
    url: '/gksz/shxm/' + id,
    method: 'delete'
  })
}
