import request from '@/utils/request'

// 查询违规检查结果列表
export function listResult(query) {
  return request({
    url: '/gksz/wgjlcheck/result/list',
    method: 'get',
    params: query
  })
}

// 查询违规检查结果详细
export function getResult(id) {
  return request({
    url: '/gksz/wgjlcheck/result/' + id,
    method: 'get'
  })
}

// 新增违规检查结果
export function addResult(data) {
  return request({
    url: '/gksz/wgjlcheck/result',
    method: 'post',
    data: data
  })
}

// 修改违规检查结果
export function updateResult(data) {
  return request({
    url: '/gksz/wgjlcheck/result',
    method: 'put',
    data: data
  })
}

// 删除违规检查结果
export function delResult(id) {
  return request({
    url: '/gksz/wgjlcheck/result/' + id,
    method: 'delete'
  })
}
