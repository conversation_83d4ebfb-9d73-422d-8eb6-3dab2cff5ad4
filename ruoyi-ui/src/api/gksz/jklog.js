import request from '@/utils/request'

// 查询医保管控记录信息列表
export function listJklog(query) {
  return request({
    url: '/gksz/jklog/list',
    method: 'get',
    params: query
  })
}

// 查询医保管控记录信息详细
export function getJklog(id) {
  return request({
    url: '/gksz/jklog/' + id,
    method: 'get'
  })
}

// 新增医保管控记录信息
export function addJklog(data) {
  return request({
    url: '/gksz/jklog',
    method: 'post',
    data: data
  })
}

// 修改医保管控记录信息
export function updateJklog(data) {
  return request({
    url: '/gksz/jklog',
    method: 'put',
    data: data
  })
}

// 删除医保管控记录信息
export function delJklog(id) {
  return request({
    url: '/gksz/jklog/' + id,
    method: 'delete'
  })
}

// 获取个人诊断记录
export function getZdjl(jzh) {
  return request({
    url: '/gksz/jklog/zdjl?jzh=' + jzh,
    method: 'post',
  })
}

// 获取个人诊断记录
export function getbljl(jzh) {
  return request({
    url: '/gksz/jklog/bljl?jzh=' + jzh,
    method: 'post',
  })
}

// 获取个人诊断记录
export function getFyxx(query) {
  return request({
    url: '/gksz/jklog/fyxx',
    method: 'get',
    params: query
  })
}
// 获取个人诊断记录
export function getFyxxByKs(query) {
  return request({
    url: '/gksz/jklog/fyxxByks',
    method: 'get',
    params: query
  })
}

// 获取个人诊断记录
export function getBrxx(jzh) {
  return request({
    url: '/gksz/jklog/brxx?jzh=' + jzh,
    method: 'post',
  })
}


// 查询医保管控记录信息列表
export function listJklog2(query) {
  return request({
    url: '/gksz/jklog/list2',
    method: 'get',
    params: query
  })
}
