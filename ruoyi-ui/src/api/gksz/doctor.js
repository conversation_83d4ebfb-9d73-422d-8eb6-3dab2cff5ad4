import request from '@/utils/request'

// 查询医生指标列表
export function listDoctor(query) {
  return request({
    url: '/gksz/doctor/list',
    method: 'get',
    params: query
  })
}

// 查询医生指标详细
export function getDoctor(month) {
  return request({
    url: '/gksz/doctor/' + month,
    method: 'get'
  })
}

// 新增医生指标
export function addDoctor(data) {
  return request({
    url: '/gksz/doctor',
    method: 'post',
    data: data
  })
}

// 修改医生指标
export function updateDoctor(data) {
  return request({
    url: '/gksz/doctor',
    method: 'put',
    data: data
  })
}

// 删除医生指标
export function delDoctor(month) {
  return request({
    url: '/gksz/doctor/' + month,
    method: 'delete'
  })
}
