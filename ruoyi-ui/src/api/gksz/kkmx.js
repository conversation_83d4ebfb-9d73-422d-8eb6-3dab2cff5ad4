import request from '@/utils/request'

// 查询扣款明细列表
export function listKkmx(query) {
  return request({
    url: '/gksz/kkmx/list',
    method: 'get',
    params: query
  })
}


export function deptList() {
  return request({
    url: '/gksz/kkmx/deptList',
    method: 'get'
  })
}

export function doctorList() {
  return request({
    url: '/gksz/kkmx/doctorList',
    method: 'get'
  })
}

export function doctorBydept(deptName) {
  return request({
    url: '/gksz/kkmx/doctorBydept?deptName=' + deptName,
    method: 'get',
  })
}


export function appeal(data) {
  return request({
    url: '/gksz/kkmx/appeal',
    method: 'post',
    data: data
  })
}

export function sendKkxx(data) {
  return request({
    url: '/gksz/kkmx/send',
    method: 'post',
    data: data
  })
}

// 查询扣款明细详细
export function getKkmx(id) {
  return request({
    url: '/gksz/kkmx/' + id,
    method: 'get'
  })
}

// 新增扣款明细
export function addKkmx(data) {
  return request({
    url: '/gksz/kkmx',
    method: 'post',
    data: data
  })
}

// 修改扣款明细
export function updateKkmx(data) {
  return request({
    url: '/gksz/kkmx',
    method: 'put',
    data: data
  })
}

// 删除扣款明细
export function delKkmx(id) {
  return request({
    url: '/gksz/kkmx/' + id,
    method: 'delete'
  })
}

// 删除扣款明细
export function importTemplate() {
  return request({
    url: '/gksz/kkmx/importTemplate',
    method: 'post'
  })
}

