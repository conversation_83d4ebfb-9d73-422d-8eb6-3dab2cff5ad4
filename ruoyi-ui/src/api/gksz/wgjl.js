import request from '@/utils/request'


export function cysh(query) {
  return request({
    url: '/gksz/cysh/cysh1',
    method: 'get',
    params: query
  })
}


export function gdjclist(query) {
  return request({
    url: '/gksz/wgjl/gdjclist',
    method: 'get',
    params: query
  })
}

// 查询违规查询列表
export function listWgjl(query) {
  return request({
    url: '/gksz/wgjl/list',
    method: 'get',
    params: query
  })
}

export function selectCheckWgjl(query) {
  return request({
    url: '/gksz/wgjl/selectCheckWgjl',
    method: 'get',
    params: query
  })
}


// 查询违规查询列表
export function listWgjlYs(query) {
  return request({
    url: '/gksz/wgjl/listys',
    method: 'get',
    params: query
  })
}

// 查询违规查询列表
export function listWgjlKs(query) {
  return request({
    url: '/gksz/wgjl/listks',
    method: 'get',
    params: query
  })
}

export function listWgjl2(query) {
  return request({
    url: '/gksz/wgjl/list2',
    method: 'get',
    params: query
  })
}

// 查询违规查询详细
export function getWgjl(id) {
  return request({
    url: '/gksz/wgjl/' + id,
    method: 'get'
  })
}

// 新增违规查询
export function addWgjl(data) {
  return request({
    url: '/gksz/wgjl',
    method: 'post',
    data: data
  })
}


// 新增违规查询
export function sendWgklCheck(data) {
  return request({
    url: '/gksz/wgjl/sendWgklCheck',
    method: 'post',
    data: data
  })
}

// 修改违规查询
export function updateWgjl(data) {
  return request({
    url: '/gksz/wgjl',
    method: 'put',
    data: data
  })
}

// 删除违规查询
export function delWgjl(id) {
  return request({
    url: '/gksz/wgjl/' + id,
    method: 'delete'
  })
}
