import request from '@/utils/request'

// 查询药品管控设置列表
export function listDz(query) {
  return request({
    url: '/gksz/dz/list',
    method: 'get',
    params: query
  })
}

export function YbgkDzAll(query) {
  return request({
    url: '/gksz/dz/selectYbgkDzAll',
    method: 'get',
    params: query
  })
}

export function getProList(value) {
  return request({
    url: '/gksz/dz/getProList?value=' + value,
    method: 'get'
  })
}

// 查询药品管控设置详细
export function getDz(id) {
  return request({
    url: '/gksz/dz/' + id,
    method: 'get'
  })
}

// 新增药品管控设置
export function addDz(data) {
  return request({
    url: '/gksz/dz',
    method: 'post',
    data: data
  })
}

// 修改药品管控设置
export function updateDz(data) {
  return request({
    url: '/gksz/dz',
    method: 'put',
    data: data
  })
}

// 删除药品管控设置
export function delDz(id) {
  return request({
    url: '/gksz/dz/' + id,
    method: 'delete'
  })
}
