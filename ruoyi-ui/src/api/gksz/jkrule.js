import request from '@/utils/request'

// 查询监控规则列表
export function listJkrule(query) {
  return request({
    url: '/gksz/jkrule/list',
    method: 'get',
    params: query
  })
}

export function listRules(query) {
  return request({
    url: '/gksz/jkrule/listrules',
    method: 'get',
    params: query
  })
}

// 查询监控规则详细
export function getJkrule(id) {
  return request({
    url: '/gksz/jkrule/' + id,
    method: 'get'
  })
}

// 新增监控规则
export function addJkrule(data) {
  return request({
    url: '/gksz/jkrule',
    method: 'post',
    data: data
  })
}

// 修改监控规则
export function updateJkrule(data) {
  return request({
    url: '/gksz/jkrule',
    method: 'put',
    data: data
  })
}

// 删除监控规则
export function delJkrule(id) {
  return request({
    url: '/gksz/jkrule/' + id,
    method: 'delete'
  })
}
