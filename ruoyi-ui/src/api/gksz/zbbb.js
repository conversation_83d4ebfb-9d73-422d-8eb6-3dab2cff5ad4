import request from '@/utils/request'

// 查询指标报表列表
export function listZbbb(query) {
  return request({
    url: '/gksz/zbbb/list',
    method: 'get',
    params: query
  })
}

// 查询指标报表详细
export function getZbbb(month) {
  return request({
    url: '/gksz/zbbb/' + month,
    method: 'get'
  })
}

// 新增指标报表
export function addZbbb(data) {
  return request({
    url: '/gksz/zbbb',
    method: 'post',
    data: data
  })
}

// 修改指标报表
export function updateZbbb(data) {
  return request({
    url: '/gksz/zbbb',
    method: 'put',
    data: data
  })
}

// 删除指标报表
export function delZbbb(month) {
  return request({
    url: '/gksz/zbbb/' + month,
    method: 'delete'
  })
}
