import request from '@/utils/request'

export function searchPath(name) {
  return request({
    url: '/clinicalPath/pathways/searchPath?name=' + name,
    method: 'get'
  })
}


export function getConById(id) {
  return request({
    url: '/clinicalPath/pathways/getConById?id=' + id,
    method: 'get'
  })
}


export function resetPathData() {
  return request({
    url: '/clinicalPath/pathways/resetPathData',
    method: 'post',
  })
}



export function getPathWay(data) {
  return request({
    url: '/clinicalPath/pathways/getPathWay',
    method: 'post',
    data: data
  })
}

// 查询临床路径数据管理列表
export function listWay(query) {
  return request({
    url: '/clinicalPath/pathways/list',
    method: 'get',
    params: query
  })
}

// 查询临床路径数据管理详细
export function getWayPdf(id) {
  return request({
    url: '/clinicalPath/pathways/pdf/' + id,
    method: 'get',
    responseType: "blob"
  })
}

export function getWay(id) {
  return request({
    url: '/clinicalPath/pathways/' + id,
    method: 'get'
  })
}

// 新增临床路径数据管理
export function addWay(data) {
  return request({
    url: '/clinicalPath/pathways',
    method: 'post',
    data: data
  })
}

// 修改临床路径数据管理
export function updateWay(data) {
  return request({
    url: '/clinicalPath/pathways',
    method: 'put',
    data: data
  })
}

// 删除临床路径数据管理
export function delWay(id) {
  return request({
    url: '/clinicalPath/pathways/' + id,
    method: 'delete'
  })
}
