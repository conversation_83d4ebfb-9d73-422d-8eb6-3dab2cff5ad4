import request from '@/utils/request'

// 查询临床路径费用项目列表
export function listLcljFyxm(query) {
  return request({
    url: '/clinicalPath/lcljFyxm/list',
    method: 'get',
    params: query
  })
}


// 根据临床路径查询临床路径费用项目列表
export function listLcljFyxmByLclj(query) {
  return request({
    url: '/clinicalPath/lcljFyxm/listLcljFyxmByLclj',
    method: 'get',
    params: query
  })
}

// 根据临床路径查询临床路径费用项目列表
export function listLcljFyxmByzd(query) {
  return request({
    url: '/clinicalPath/lcljFyxm/selectLcljFyxmByzd',
    method: 'get',
    params: query
  })
}


// 修改临床路径费用项目审核状态
export function updateLcljFyxmStatus(data) {
  return request({
    url: '/clinicalPath/lcljFyxm',
    method: 'put',
    data: data
  })
}

// 查询单临床路径费用项目列表
export function listfyxmByPathId(pathId) {
  return request({
    url: '/clinicalPath/lcljFyxm/listfyxmByPathId',
    method: 'get',
    params: {pathId}
  })
}

export function saveLcljAndFyxms(lcljStr) {
  return request({
    url: '/clinicalPath/lcljFyxm/saveLcljAndFyxms',
    method: 'post',
    data: lcljStr
  })
}

// 查询临床路径列表
export function listLclj(query) {
  return request({
    url: '/clinicalPath/lcljFyxm/listLclj',
    method: 'get',
    params: query
  })
}

// 查询临床路径列表
export function listLcljSh(query) {
  return request({
    url: '/clinicalPath/lcljFyxm/listLcljSh',
    method: 'get',
    params: query
  })
}


// 查询是否已经提交当前方案
export function selectLcljItem(query) {
  return request({
    url: '/clinicalPath/lcljFyxm/selectLcljItem',
    method: 'get',
    params: query
  })
}


export function selectLcljFyxmShById(query) {
  return request({
    url: '/clinicalPath/lcljFyxm/selectLcljFyxmShById',
    method: 'get',
    params: query
  })
}


// 删除临床路径
export function delLcljById(pathId) {
  return request({
    url: '/clinicalPath/lcljFyxm/delLcljById',
    method: 'get',
    params: {pathId}
  })
}

