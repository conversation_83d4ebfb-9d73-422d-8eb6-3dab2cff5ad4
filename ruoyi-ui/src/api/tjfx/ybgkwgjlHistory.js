import request from '@/utils/request'

export function ybExcludeWgjl(query) {
  return request({
    method: 'get',
    url: '/system/wgjlHistory/ybExcludeWgjl',
    params: query
  })
}

export function listYbgkWgjlHistory(query) {
  return request({
    method: 'get',
    url: '/system/wgjlHistory/list',
    params: query
  })
}

export function removeYbgkWgjlHistory(query) {
  return request({
    method: 'DELETE',
    url: `/system/wgjlHistory/${query}`,
  })
}
