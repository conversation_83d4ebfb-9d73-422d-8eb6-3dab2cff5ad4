import request from '@/utils/request'

export function listYsztsj(query) {
  return request({
    url: '/tjfx/yssy/ysztsj',
    method: 'get',
    params: query
  })
}

export function listYsczjybl(query) {
  return request({
    url: '/tjfx/yssy/ysczjybl',
    method: 'get',
    params: query
  })
}

export function listYsczjybz(query) {
  return request({
    url: '/tjfx/yssy/ysczjybz',
    method: 'get',
    params: query
  })
}



export function listYsbllx(query) {
  return request({
    url: '/tjfx/yssy/ysbllxfx',
    method: 'get',
    params: query
  })
}

export function listYsfyjg(query) {
  return request({
    url: '/tjfx/yssy/ysfyjg',
    method: 'get',
    params: query
  })
}

