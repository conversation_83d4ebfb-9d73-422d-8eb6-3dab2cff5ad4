import request from '@/utils/request'

export function getYsph(query) {
  return request({
    url: '/tjfx/tjfx/zkfx/ysph',
    method: 'get',
    params: query
  })
}

export function getKsph(query) {
  return request({
    url: '/tjfx/tjfx/zkfx/ksph',
    method: 'get',
    params: query
  })
}

export function getLxzb(query) {
  return request({
    url: '/tjfx/tjfx/zkfx/lxzb',
    method: 'get',
    params: query
  })
}

export function getXmcy(query) {
  return request({
    url: '/tjfx/tjfx/zkfx/xm/wordcloud',
    method: 'get',
    params: query
  })
}

export function getMrwg(query) {
  return request({
    url: '/tjfx/tjfx/zkfx/mrwg',
    method: 'get',
    params: query
  })
}

export function getXzxmSl(query) {
  return request({
    url: '/tjfx/tjfx/zkfx/xzxmsl',
    method: 'get',
    params: query
  })
}
