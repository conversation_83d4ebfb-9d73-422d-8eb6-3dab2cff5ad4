import request from '@/utils/request'

// export function getDeptnameList(){
//   return request({
//     url: '/jsxx/hisjsxx/getDeptnameList',
//     method: 'post'
//   })
// }

export function getDoctorList(deptname){
  return request({
    url: `/jsxx/hisjsxx/getDoctorListByDept/${deptname}`,
    method: 'post'
  })
}

export function getZbfx(data, page, limit) {
  return request({
    url: `/tjfx/tjfx/zbfx?pageNum=${page}&pageSize=${limit}`,
    method: 'post',
    data: data
  })
}

export function getDeptnameList(){
  return request({
    url: '/tjfx/tjfx/getDeptList',
    method: 'post'
  })
}
