import request from '@/utils/request'

// 查询病例分组明细列表
export function listBlfzmx(query) {
  return request({
    url: '/tjfx/blfzmx/list',
    method: 'get',
    params: query
  })
}

// 查询病例分组明细详细
export function getBlfzmx(scdate) {
  return request({
    url: '/tjfx/blfzmx/' + scdate,
    method: 'get'
  })
}

// 新增病例分组明细
export function addBlfzmx(data) {
  return request({
    url: '/tjfx/blfzmx',
    method: 'post',
    data: data
  })
}

// 修改病例分组明细
export function updateBlfzmx(data) {
  return request({
    url: '/tjfx/blfzmx',
    method: 'put',
    data: data
  })
}

// 删除病例分组明细
export function delBlfzmx(scdate) {
  return request({
    url: '/tjfx/blfzmx/' + scdate,
    method: 'delete'
  })
}
