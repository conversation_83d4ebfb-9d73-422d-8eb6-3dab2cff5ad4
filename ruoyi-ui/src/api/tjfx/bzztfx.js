import request from '@/utils/request'

// 查询病组整体情况分析列表
export function listBzztfx(query) {
  return request({
    url: '/tjfx/bzsy/bzztqk',
    method: 'get',
    params: query
  })
}

export function listBzczjy(query) {
  return request({
    url: '/tjfx/bzsy/bzczjy',
    method: 'get',
    params: query
  })
}

export function listBzfyjg(query) {
  return request({
    url: '/tjfx/bzsy/bzfyjg',
    method: 'get',
    params: query
  })
}

export function listBzblByDoctor(query) {
  return request({
    url: '/tjfx/bzsy/bzbllistByDoctor',
    method: 'get',
    params: query
  })
}

export function listBzblByDept(query) {
  return request({
    url: '/tjfx/bzsy/bzbllistByDept',
    method: 'get',
    params: query
  })
}

export function listBzblfx(query) {
  return request({
    url: '/tjfx/bzsy/bzbllxfxlist',
    method: 'get',
    params: query
  })
}


export function listBzczjybl(query) {
  return request({
    url: '/tjfx/bzsy/bzczjybllist',
    method: 'get',
    params: query
  })
}

export function listBzczblfyjg(query) {
  return request({
    url: '/tjfx/bzsy/bzczblfyjg',
    method: 'get',
    params: query
  })
}

export function listBzjyblfyjg(query) {
  return request({
    url: '/tjfx/bzsy/bzjyblfyjg',
    method: 'get',
    params: query
  })
}

export function listBzKsmx(query) {
  return request({
    url: '/tjfx/bzsy/bzKsmx',
    method: 'get',
    params: query
  })
}

export function listBzKsmxDept(query) {
  return request({
    url: '/tjfx/bzsy/bzKsmxdept',
    method: 'get',
    params: query
  })
}


export function listBzKsxmpm(query) {
  return request({
    url: '/tjfx/bzsy/bzKsxmpm',
    method: 'get',
    params: query
  })
}
