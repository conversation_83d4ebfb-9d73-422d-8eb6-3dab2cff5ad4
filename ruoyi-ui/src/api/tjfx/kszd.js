import request from '@/utils/request'

export function getKszdList(params){
  return request({
    url: `/system/deptConsolidation/list`,
    method: 'get',
    params: params
  })
}

export function addKszd(obj){
  return request({
    url: `/system/deptConsolidation/add`,
    method: 'post',
    data: obj
  })
}

export function editKszd(obj){
  return request({
    url: `/system/deptConsolidation/edit`,
    method: 'post',
    data: obj
  })
}

export function changeKszdStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/system/deptConsolidation/edit',
    method: 'post',
    data: data
  })
}
