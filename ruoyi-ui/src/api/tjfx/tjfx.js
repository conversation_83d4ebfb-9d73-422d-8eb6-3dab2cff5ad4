import request from '@/utils/request'

// 查询病例权重-盈亏图列表
export function getBlqzYkxx(query) {
  return request({
    url: '/tjfx/tjfx/getBlqzYkxx',
    method: 'get',
    params: query
  })
}

export function getDeptYxzb(query) {
  return request({
    url: '/tjfx/tjfx/getDeptYxzb',
    method: 'get',
    params: query
  })
}



export function getKsqzYkxx(query) {
  return request({
    url: '/tjfx/tjfx/getKsqzYkxx',
    method: 'post',
    params: query
  })
}

export function getAvgCMI() {
  return request({
    url: '/tjfx/tjfx/getAvgCMI',
    method: 'get',
  })
}

export function getDeptListFromSy(){
  return request({
    url: '/tjfx/tjfx/getDeptListFromSy',
    method: 'get'
  })
}

export function getDoctorListByDept(dept) {
  return request({
    url: `/tjfx/tjfx/getDoctorListByDept`,
    method: 'get',
    params: {
      deptName: dept
    }
  })
}

export function getDrgFztjData(query, pageNum, pageSize) {
  return request({
    url: `/tjfx/tjfx/report/drgfztj`,
    method: 'get',
    params : {
      ...query,
      pageNum,
      pageSize
    }
  })
}

export function getDiagDiffDetails(query) {
  return request({
    url: `/tjfx/tjfx/diagDiffDetails`,
    method: 'get',
    params: query
  })
}

export function getBaZbfx(query) {
  return request({
    url: `/tjfx/tjfx/report/bazbfx`,
    method: 'get',
    params: query
  })
}
