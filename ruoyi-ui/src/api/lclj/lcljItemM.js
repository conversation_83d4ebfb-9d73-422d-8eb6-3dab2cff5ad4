import request from '@/utils/request'

// 查询路径项目管理列表
export function listLcljItemM(query) {
  return request({
    url: '/system/lcljItemM/list',
    method: 'get',
    params: query
  })
}

// 查询路径项目管理详细
export function getLcljItemM(cId) {
  return request({
    url: '/system/lcljItemM/' + cId,
    method: 'get'
  })
}

// 新增路径项目管理
export function addLcljItemM(data) {
  return request({
    url: '/system/lcljItemM',
    method: 'post',
    data: data
  })
}

// 修改路径项目管理
export function updateLcljItemM(data) {
  return request({
    url: '/system/lcljItemM',
    method: 'put',
    data: data
  })
}

// 删除路径项目管理
export function delLcljItemM(cId) {
  return request({
    url: '/system/lcljItemM/' + cId,
    method: 'delete'
  })
}
