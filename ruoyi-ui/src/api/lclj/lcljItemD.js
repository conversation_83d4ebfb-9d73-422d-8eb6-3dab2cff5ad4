import request from '@/utils/request'

// 查询药品使用记录列表
export function listHlcljItemD(query) {
  return request({
    url: '/system/hlcljItemD/list',
    method: 'get',
    params: query
  })
}

// 查询药品使用记录详细
export function getHlcljItemD(cBillId) {
  return request({
    url: '/system/hlcljItemD/' + cBillId,
    method: 'get'
  })
}

// 新增药品使用记录
export function addHlcljItemD(data) {
  return request({
    url: '/system/hlcljItemD',
    method: 'post',
    data: data
  })
}

// 修改药品使用记录
export function updateHlcljItemD(data) {
  return request({
    url: '/system/hlcljItemD',
    method: 'put',
    data: data
  })
}

// 删除药品使用记录
export function delHlcljItemD(cBillId) {
  return request({
    url: '/system/hlcljItemD/' + cBillId,
    method: 'delete'
  })
}
