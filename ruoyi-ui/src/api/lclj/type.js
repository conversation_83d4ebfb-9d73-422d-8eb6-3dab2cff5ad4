import request from '@/utils/request'

// 查询临床路径管理列表
export function listType(query) {
  return request({
    url: '/lclj/type/list',
    method: 'get',
    params: query
  })
}

// 查询临床路径管理详细
export function getType(cId) {
  return request({
    url: '/lclj/type/' + cId,
    method: 'get'
  })
}

// 新增临床路径管理
export function addType(data) {
  return request({
    url: '/lclj/type',
    method: 'post',
    data: data
  })
}

// 修改临床路径管理
export function updateType(data) {
  return request({
    url: '/lclj/type',
    method: 'put',
    data: data
  })
}

// 删除临床路径管理
export function delType(cId) {
  return request({
    url: '/lclj/type/' + cId,
    method: 'delete'
  })
}
