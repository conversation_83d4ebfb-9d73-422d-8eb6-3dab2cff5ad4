import request from '@/utils/request'

// 查询时间花费管理列表
export function listDate(query) {
  return request({
    url: '/lclj/date/list',
    method: 'get',
    params: query
  })
}

// 查询时间花费管理详细
export function getDate(cId) {
  return request({
    url: '/lclj/date/' + cId,
    method: 'get'
  })
}

// 新增时间花费管理
export function addDate(data) {
  return request({
    url: '/lclj/date',
    method: 'post',
    data: data
  })
}

// 修改时间花费管理
export function updateDate(data) {
  return request({
    url: '/lclj/date',
    method: 'put',
    data: data
  })
}

// 删除时间花费管理
export function delDate(cId) {
  return request({
    url: '/lclj/date/' + cId,
    method: 'delete'
  })
}
