import request from '@/utils/request'
import Cookies from 'js-cookie'

export function chatTest(prompt) {
  // console.log(prompt)
  return request({
    url: `/llm/api/test`,
    method: 'post',
    params: {prompt : prompt}
  })
}

export function chat(vo) {
  return request({
    url: `/llm/api/chat/complete`,
    method: 'post',
    data: vo
  })
}

export function getJyxxForChat(jzh) {
  return request({
    url: `/mlcx/jyxx/chat/jyxx/${jzh}`,
    method: 'get'
  })
}

export function getJcxxForChat(jzh) {
  return request({
    url: `/mlcx/jcxx/jcxxForChat/${jzh}`,
    method: 'get'
  })
}

export function chatV1(chatMessage) {
  return request({
    url: `/llm/api/chat/v1`,
    method: 'post',
    params: {chatMessage}
  })
}

export function getFyxxTen(brid, zyid) {
  return request({
    url: `/gksz/fyxx/getFyxx/ten/${brid}/${zyid}`,
    method: 'get',
  })
}
