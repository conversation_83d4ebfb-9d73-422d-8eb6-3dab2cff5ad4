import request from '@/utils/request'

// 查询费用查询列表
export function listFyxx(query) {
  return request({
    url: '/gksz/fyxx/list',
    method: 'get',
    params: query
  })
}
export function getExcessProjects(query) {
  return request({
    url: '/gksz/fyxx/getExcessProjects',
    method: 'get',
    params: query
  })
}

// 查询费用查询详细
export function getFyxx(id) {
  return request({
    url: '/gksz/fyxx/' + id,
    method: 'get'
  })
}

// 新增费用查询
export function addFyxx(data) {
  return request({
    url: '/gksz/fyxx',
    method: 'post',
    data: data
  })
}

// 修改费用查询
export function updateFyxx(data) {
  return request({
    url: '/gksz/fyxx',
    method: 'put',
    data: data
  })
}

// 删除费用查询
export function delFyxx(id) {
  return request({
    url: '/gksz/fyxx/' + id,
    method: 'delete'
  })
}

export function selectFyxxByBr(query) {
  return request({
    url: '/gksz/fyxx/selectFyxxByBr',
    method: 'get',
    params: query
  })
}

export function selectkmfyandbg(query) {
  return request({
    url: '/gksz/fyxx/selectkmfyandbg',
    method: 'get',
    params: query
  })
}

export function selectzdlcljfy(query) {
  return request({
    url: '/gksz/fyxx/selectzdlcljfy',
    method: 'get',
    params: query
  })
}


export function delProFeeItem(data) {
  return request({
    url: '/gksz/fyxx/delProFeeItem',
    method: 'delete',
    data: data
  })
}
