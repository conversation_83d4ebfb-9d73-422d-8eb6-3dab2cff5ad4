import request from '@/utils/request'

// 查询DRG特例单议列表
export function listTldy(query) {
  return request({
    url: '/jsxx/tldy/list',
    method: 'get',
    params: query
  })
}

// 查询DRG特例单议详细
export function getTldy(id) {
  return request({
    url: '/jsxx/tldy/' + id,
    method: 'get'
  })
}

// 新增DRG特例单议
export function addTldy(data) {
  return request({
    url: '/jsxx/tldy',
    method: 'post',
    data: data
  })
}

// 修改DRG特例单议
export function updateTldy(data) {
  return request({
    url: '/jsxx/tldy',
    method: 'put',
    data: data
  })
}

// 删除DRG特例单议
export function delTldy(id) {
  return request({
    url: '/jsxx/tldy/' + id,
    method: 'delete'
  })
}
