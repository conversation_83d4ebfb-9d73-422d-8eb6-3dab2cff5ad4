<template>
  <div id="app">
    <router-view v-if="isRouterAlive"/>
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";
import { getOption } from '@/api/system/option'
import watermark from "watermark-dom";
import {dateToString} from "@/utils/dateUtils";

export default {
  name: "App",
  provide() {
    return {
      reload: this.reload
    }
  },
  mounted() {
    this.loadWatermark();
    this.$store.watch(
      (state) => [state.user.org, state.user.name],
      () => {
        if (this.$store.state.user.org || this.$store.state.user.name) {
          this.reloadWatermark();
        }
      }
    );
  },
  data() {
    return {
      isRouterAlive: true,
      watermarkLoaded: false
    }
  },
  methods: {
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function (){
        this.isRouterAlive = true
      })
    },
    loadWatermark() {
      getOption("watermark_content").then(res => {
        if(res.data && res.data.cValue === '1') {
          const newOptions = this.getWatermarkOptions();
          watermark.load(newOptions);
          this.watermarkLoaded = true;
        }
      });
    },
    reloadWatermark() {
      if (this.watermarkLoaded) {
        this.loadWatermark();
      }
    },
    getWatermarkOptions() {
      return {
        watermark_rows: 2,
        watermark_cols: 2,
        watermark_x_space: 320,
        watermark_y_space: 320,
        watermark_font: '20px',
        watermark_width: 450,
        watermark_height: 120,
        watermark_txt: (this.$store.state.user.org ? this.$store.state.user.org : '') + " " + (this.$store.state.user.name ? this.$store.state.user.name : '') + " " + dateToString(new Date())
      };
    }

  },
  components: { ThemePicker },
    metaInfo() {
      let sysName = process.env.VUE_APP_TITLE;
      getOption("login_sys_name").then(res => {
        if(res.data && res.data.cValue){
          sysName = res.data.cValue;
        }
        console.log(sysName)
      })
        return {
            // title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
            title: sysName,
            titleTemplate: title => {
                return title ? `${title} - ${sysName}` : sysName
            }
        }
    }
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
</style>
