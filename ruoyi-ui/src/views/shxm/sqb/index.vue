<template>
  <div class="app-container">
    <h1>项目使用申请表</h1>
    <el-form :model="form" label-width="120px">
      <!-- 基本信息行 -->
      <el-row :gutter="12">
        <el-col :span="6">
          <el-form-item label="姓名">
            <el-input v-model="form.brname" placeholder="请输入患者姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="住院号">
            <el-input v-model="form.zyh" placeholder="请输入住院号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="12">
        <el-col :span="6">
          <el-form-item label="科室">
<!--            <el-input v-model="form.ksname" placeholder="ksname"></el-input>-->
            <el-select v-model="form.ksname">
              <el-option v-for="item in deptList" :value="item" :key="item" :label="item"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="性别">
            <!--            <el-input v-model="form.sex" placeholder="sex"></el-input>-->
            <el-select v-model="form.sex">
              <el-option value="男" key="男" label="男"/>
              <el-option value="女" key="女" label="女"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="width: 70%">
        <el-col :span="8">
          <el-form-item label="医保号">
            <el-input v-model="form.ybh" placeholder="请输入医保号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 项目信息 -->
      <el-row :gutter="20" style="width: 70%">
        <el-col :span="8">
          <el-form-item label="申请项目">
            <el-input v-model="form.xmmc" placeholder="项目名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="项目数量">
            <el-input  v-model="form.xmsl" placeholder="项目数量"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
      </el-row>

      <!-- 申请理由 -->
      <el-form-item label="申请理由">
        <el-input
          style="width: 45%"
          type="textarea"
          v-model="form.sqly"
          placeholder="请输入申请理由"
          rows="4"
        ></el-input>
      </el-form-item>

      <!-- 临床诊断 -->
      <el-form-item label="临床诊断">
        <el-input
          style="width: 45%"
          type="textarea"
          v-model="form.zdqk"
          placeholder="请输入临床诊断"
          rows="4"
        ></el-input>
      </el-form-item>

      <!-- 医保办意见 -->
<!--      <el-form-item label="医保办意见">-->
<!--        <el-input-->
<!--          style="width: 45%"-->
<!--          type="textarea"-->
<!--          v-model="form.ybb"-->
<!--          placeholder="请输入医保办意见"-->
<!--          rows="4"-->
<!--        ></el-input>-->
<!--      </el-form-item>-->

      <!-- 提交按钮 -->
      <el-row>
        <el-col :span="24" class="form-buttons">
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getDeptListFromSy } from '@/api/tjfx/tjfx'
import { addShxm } from '@/api/gksz/shxm'
import { getCurDayStr } from '@/utils/dateUtils'

export default {
  name: 'Sqb',
  data() {
    return {
      deptList: [],
      form: {
        brname: '', // 病人姓名
        sex: '', // 性别
        ksname: '', // 科室
        zyh: '', // 住院号
        xmmc: '', // 项目名称
        xmsl: '', // 项目编号
        sqly: '', // 申请理由
        zdqk: '' // 临床诊断
      },
    };
  },
  created() {
    this.initDept()
  },
  methods: {
    initDept(){
      console.log("okk")
      getDeptListFromSy().then(res => {
        this.deptList = res.list
      })
    },
    submitForm() {
      this.form.createDate = getCurDayStr().substring(0, 10);
      this.form.updateDate = getCurDayStr().substring(0, 10);
      this.form.sqrq = getCurDayStr().substring(0, 10);
      console.log(this.form);
      addShxm(this.form).then(res => {
        if(res.code) {
          this.$message.success("表单已提交！");
        }
      })
    },
    resetForm() {
      this.$refs.form.resetFields();
    },
  },
};
</script>

<style scoped>
.project-approval-form {
  max-width: 900px;
  margin: 0 auto;
}
.form-buttons {
  text-align: left;
  margin-top: 20px;
}
</style>
