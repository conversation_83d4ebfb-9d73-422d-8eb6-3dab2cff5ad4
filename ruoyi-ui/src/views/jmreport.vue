<template>
  <i-frame :src="openUrl"/>
</template>

<script>
import { getToken } from '@/utils/auth'

import iFrame from "@/components/iFrame/index";
import { getOption } from '@/api/system/option'

export default {
  components: { iFrame },
  data() {
    return {
      openUrl: undefined
    }
  },
  mounted() {
    if(!this.openUrl) {
      getOption('server_ip').then(res => {
        // this.openUrl = 'http:' + res.data.cvalue + ':8096/jmreport/view/' + '' + '?token=' + getToken()
        this.openUrl = `http://${res.data.cValue}:8096/jmreport/view/${this.$route.query.reportId}?token=${getToken()}`
        console.log(this.openUrl)
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
