<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="科室名称" prop="hDeptName">
        <el-input
          v-model="queryParams.hDeptName"
          placeholder="请输入科室名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="hisid" prop="hisid">-->
<!--        <el-input-->
<!--          v-model="queryParams.hisid"-->
<!--          placeholder="请输入hisid"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="${comment}" prop="code">-->
<!--        <el-input-->
<!--          v-model="queryParams.code"-->
<!--          placeholder="请输入${comment}"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="简拼" prop="nccd">-->
<!--        <el-input-->
<!--          v-model="queryParams.nccd"-->
<!--          placeholder="请输入简拼"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="机构id" prop="jgid">-->
<!--        <el-input-->
<!--          v-model="queryParams.jgid"-->
<!--          placeholder="请输入机构id"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="科室主任" prop="deptLeader">
        <el-input
          v-model="queryParams.deptLeader"
          placeholder="请输入科室主任"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:hdept:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:hdept:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:hdept:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:hdept:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="hdeptList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="科室id" align="center" prop="hDeptId" />-->
      <el-table-column label="科室名称" align="center" prop="hDeptName">
        <template slot-scope="scope">
<!--          <dict-tag :options="dict.type.${column.dictType}" :value="scope.row.hDeptName"/>-->
          <span>{{scope.row.hDeptName}}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="hisid" align="center" prop="hisid">-->
<!--        <template slot-scope="scope">-->
<!--&lt;!&ndash;          <dict-tag :options="dict.type.${column.dictType}" :value="scope.row.hisid"/>&ndash;&gt;-->
<!--          <span>{{scope.row.hisid}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="${comment}" align="center" prop="code">-->
<!--        <template slot-scope="scope">-->
<!--&lt;!&ndash;          <dict-tag :options="dict.type.${column.dictType}" :value="scope.row.code"/>&ndash;&gt;-->
<!--          <span>{{scope.row.code}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="简拼" align="center" prop="nccd">
        <template slot-scope="scope">
<!--          <dict-tag :options="dict.type.${column.dictType}" :value="scope.row.nccd"/>-->
          <span>{{scope.row.nccd}}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="机构id" align="center" prop="jgid">-->
<!--        <template slot-scope="scope">-->
<!--&lt;!&ndash;          <dict-tag :options="dict.type.${column.dictType}" :value="scope.row.jgid"/>&ndash;&gt;-->
<!--          <span>{{scope.row.jgid}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="科室主任" align="center" prop="deptLeader">
        <template slot-scope="scope">
<!--          <dict-tag :options="dict.type.${column.dictType}" :value="scope.row.deptLeader"/>-->
          <span>{{scope.row.deptLeader}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:hdept:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:hdept:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改hdept对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="科室名称" prop="hDeptName">
          <el-input v-model="form.hDeptName" placeholder="请输入科室名称" />
        </el-form-item>
<!--        <el-form-item label="hisid" prop="hisid">-->
<!--          <el-input v-model="form.hisid" placeholder="请输入hisid" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="${comment}" prop="code">-->
<!--          <el-input v-model="form.code" placeholder="请输入${comment}" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="简拼" prop="nccd">-->
<!--          <el-input v-model="form.nccd" placeholder="请输入简拼" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="机构id" prop="jgid">-->
<!--          <el-input v-model="form.jgid" placeholder="请输入机构id" />-->
<!--        </el-form-item>-->
        <el-form-item label="科室主任" prop="deptLeader">
          <el-input v-model="form.deptLeader" placeholder="请输入科室主任" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHdept, getHdept, delHdept, addHdept, updateHdept } from "@/api/system/hdept";

export default {
  name: "Hdept",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // hdept表格数据
      hdeptList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hDeptName: null,
        hisid: null,
        code: null,
        nccd: null,
        jgid: null,
        deptLeader: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        hDeptName: [
          { required: true, message: "科室名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询hdept列表 */
    getList() {
      this.loading = true;
      listHdept(this.queryParams).then(response => {
        this.hdeptList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        hDeptId: null,
        hDeptName: null,
        hisid: null,
        code: null,
        nccd: null,
        jgid: null,
        deptLeader: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.hDeptId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加hdept";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const hDeptId = row.hDeptId || this.ids
      getHdept(hDeptId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改hdept";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.hDeptId != null) {
            updateHdept(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHdept(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const hDeptIds = row.hDeptId || this.ids;
      this.$modal.confirm('是否确认删除hdept编号为"' + hDeptIds + '"的数据项？').then(function() {
        return delHdept(hDeptIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/hdept/export', {
        ...this.queryParams
      }, `hdept_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
