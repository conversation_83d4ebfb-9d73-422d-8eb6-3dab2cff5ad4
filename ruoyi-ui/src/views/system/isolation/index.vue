<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane name="isolation" label="数据隔离设置">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="字段" prop="param">
            <el-input
              v-model="queryParams.param"
              placeholder="请输入字段"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="类型" prop="param">
            <el-select
              v-model="queryParams.type"
              placeholder="请选择类型"
              clearable
              @keyup.enter.native="handleQuery"
            >
              <el-option v-for="(item,index) in typeOptions" :value="item.value" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="primary"  icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:isolation:add']">新增</el-button>
            <el-button type="primary" size="mini" @click="reflush">刷新</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="isolationList">
          <el-table-column label="名称" align="center" prop="name" />
          <el-table-column label="字段" align="center" prop="param" />
          <el-table-column label="类型" align="center" prop="type" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:isolation:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:isolation:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改数据隔离管理对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入名称" />
            </el-form-item>
            <el-form-item label="字段" prop="param">
              <el-input v-model="form.param" placeholder="请输入字段" />
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型">
                <el-option v-for="(item,index) in typeOptions" :value="item.value" :label="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-tab-pane>
      <el-tab-pane name="test" label="测试SQL">
        测试sql：（开启多租户时使用，否则会报错）<el-input type="textarea" v-model="originSql" style="margin: 20px 0;"></el-input>
        结果sql：<el-input type="textarea" v-model="resultSql" disabled style="margin: 20px 0;"></el-input>
        <el-button type="primary" size="mini" @click="testSQL">获取测试结果</el-button>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { listIsolation, getIsolation, delIsolation, addIsolation, updateIsolation, testSQL, reflush } from "@/api/system/isolation";

export default {
  name: "Isolation",
  data() {
    return {
      activeName: "isolation",
      originSql: null,
      resultSql: null,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据隔离管理表格数据
      isolationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        param: null,
        type: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "表名不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "类型不能为空", trigger: "change" }
        ]
      },
      typeOptions: [
        {
          value: 1,
          name: "数据表"
        },
        {
          value: 2,
          name: "存储过程"
        }
      ]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    testSQL() {
      if (this.originSql == null || this.originSql == "") {
        this.$modal.msgWarning("请输入SQL语句")
        return
      }

      console.log(this.originSql)
      if (this.originSql.indexOf("<>") > -1) {
        this.originSql = this.originSql.replaceAll("<>","&lt;&gt;")
      }
      console.log(this.originSql)

      testSQL(this.originSql).then(res => {
        this.resultSql = res
      })
    },
    reflush() {
      reflush().then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("刷新成功！")
        } else {
          this.$modal.msgError("刷新失败！")
        }
      })
    },
    /** 查询数据隔离管理列表 */
    getList() {
      this.loading = true;
      listIsolation(this.queryParams).then(response => {
        this.isolationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        param: null,
        type: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: null,
        param: null,
        type: null
      }
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据隔离管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getIsolation(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改数据隔离管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateIsolation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIsolation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除数据隔离管理编号为"' + ids + '"的数据项？').then(function() {
        return delIsolation(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  }
};
</script>
