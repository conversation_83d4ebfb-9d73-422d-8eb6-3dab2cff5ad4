<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="住院号" prop="zyh">
        <el-input
          v-model="queryParams.zyh"
          placeholder="请输入住院号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="病人姓名" prop="brname">
        <el-input
          v-model="queryParams.brname"
          placeholder="请输入病人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="xmmc">
        <el-input
          v-model="queryParams.xmmc"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编码" prop="xmbm">
        <el-input
          v-model="queryParams.xmbm"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目类型" prop="category">
        <el-select v-model="queryParams.category" placeholder="请选择项目类型" clearable>
          <el-option
            v-for="dict in categoryOptions"
            :key="dict"
            :label="dict"
            :value="dict">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="级别" prop="severity">
<!--        <el-input-->
<!--          v-model="queryParams.severity"-->
<!--          placeholder="请输入级别"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
        <el-select v-model="queryParams.severity" placeholder="请选择级别" clearable>
          <el-option
          v-for="dict in levelOptions"
          :key="dict.key"
          :label="dict.label"
          :value="dict.value">

          </el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="提示类型" prop="type">-->
<!--        <el-select v-model="queryParams.type" placeholder="请选择提示类型" clearable>-->
<!--          <el-option-->
<!--            v-for="dict in dict.type.${dictType}"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['system:znfx:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['system:znfx:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:znfx:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:znfx:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="znfxList" @selection-change="handleSelectionChange" :border="true">
      <el-table-column label="住院号" align="center" prop="zyh" width="100px" />
      <el-table-column label="病人姓名" align="center" prop="brname" />
      <el-table-column label="项目名称" align="center" prop="xmmc" width="180px" />
      <el-table-column label="项目国家编码" align="center" prop="xmbm" width="250px" />
      <el-table-column label="项目类型" align="center" prop="category" />
      <el-table-column label="金额" align="center" prop="cost" />
      <el-table-column label="原因" align="center" prop="reason" width="280px" show-overflow-tooltip />
      <el-table-column label="提示类型" align="center" prop="type" width="100px" />
      <el-table-column label="医学证据" align="center" prop="evidence" width="160px" show-overflow-tooltip />
      <el-table-column label="级别" align="center" prop="severity" />
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width" width="120px">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="detail(scope.row)"
          >详情
          </el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:znfx:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120px" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:znfx:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:znfx:remove']"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改医保智能分析对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="住院号" prop="zyh">
          <el-input v-model="form.zyh" placeholder="请输入住院号" />
        </el-form-item>
        <el-form-item label="病人姓名" prop="brname">
          <el-input v-model="form.brname" placeholder="请输入病人姓名" />
        </el-form-item>
        <el-form-item label="项目名称" prop="xmmc">
          <el-input v-model="form.xmmc" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目国家编码" prop="xmbm">
          <el-input v-model="form.xmbm" placeholder="请输入项目国家编码" />
        </el-form-item>
        <el-form-item label="项目类型" prop="category">
          <el-input v-model="form.category" placeholder="请输入项目类型" />
        </el-form-item>
        <el-form-item label="金额" prop="cost">
          <el-input v-model="form.cost" placeholder="请输入金额" />
        </el-form-item>
        <el-form-item label="原因" prop="reason">
          <el-input v-model="form.reason" placeholder="请输入原因" />
        </el-form-item>
        <el-form-item label="医学证据" prop="evidence">
          <el-input v-model="form.evidence" placeholder="请输入医学证据" />
        </el-form-item>
        <el-form-item label="级别" prop="severity">
          <el-input v-model="form.severity" placeholder="请输入级别" />
        </el-form-item>
<!--        <el-form-item label="提示类型" prop="type">-->
<!--          <el-select v-model="form.type" placeholder="请选择提示类型">-->
<!--            <el-option-->
<!--              v-for="dict in dict.type.${dictType}"-->
<!--              :key="dict.value"-->
<!--              :label="dict.label"-->
<!--              :value="dict.value"-->
<!--            ></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-drawer
      :title="title"
      :visible.sync="drawerOpen"
      direction="rtl"
      :wrapper-closable="false"
      size="80%">
      <tabs :jzh="jzh"></tabs>
    </el-drawer>
  </div>
</template>

<script>
import { listZnfx, getZnfx, delZnfx, addZnfx, updateZnfx } from "@/api/system/znfx";
import tabs from "@/components/DetailsTabs/tabs";
import VirtualScroll, {VirtualColumn} from "el-table-virtual-scroll";
export default {
  name: "Znfx",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      jzh:"",
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 医保智能分析表格数据
      znfxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      drawerOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        brbs: null,
        brid: null,
        zyid: null,
        zyh: null,
        brname: null,
        xmmc: null,
        xmbm: null,
        category: null,
        cost: null,
        reason: null,
        evidence: null,
        severity: null,
        type: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      levelOptions: [
        {
          value: '低',
          key: '低',
          label: '低'
        },
        {
          value: '中',
          key: '中',
          label: '中'
        },
        {
          value: '高',
          key: '高',
          label: '高'
        }
      ],
      categoryOptions: [
        '治疗',
        '检查',
        '药物',
        '材料',
        '检验'
      ]
    };
  },
  created() {
    this.getList();
  },
  components: {
    VirtualColumn,
    VirtualScroll,
    tabs
  },
  methods: {
    /** 查询医保智能分析列表 */
    getList() {
      this.loading = true;
      listZnfx(this.queryParams).then(response => {
        this.znfxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        brbs: null,
        jzh:null,
        brid: null,
        zyid: null,
        zyh: null,
        brname: null,
        xmmc: null,
        xmbm: null,
        category: null,
        cost: null,
        reason: null,
        evidence: null,
        severity: null,
        type: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    detail(row) {
      this.jzh = ""
      this.jzh = row.jzh
      this.drawerOpen = true
      this.title =  row.zyh + '[' +  row.brname + ']患者详情'
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加医保智能分析";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getZnfx(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改医保智能分析";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateZnfx(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZnfx(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除医保智能分析编号为"' + ids + '"的数据项？').then(function() {
        return delZnfx(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/znfx/export', {
        ...this.queryParams
      }, `znfx_${new Date().getTime()}.xlsx`)
    },
  }
};
</script>
