<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="住院号">
        <el-input v-model="queryParams.zyh" placeholder="请输入住院号" clearable />
      </el-form-item>
      <el-form-item label="姓名">
        <el-input v-model="queryParams.xm" placeholder="请输入姓名" clearable />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="未审核" value="0" />
          <el-option label="已通过" value="1" />
          <el-option label="已打回" value="2" />
          <el-option label="已结账" value="3" />
        </el-select>
      </el-form-item>
      <br>
      <el-form-item label="开始时间">
        <el-date-picker
          v-model="startDate"
          type="datetime"
          placeholder="选择开始时间"
          default-time="00:00:00">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="结束时间">
        <el-date-picker
          v-model="endDate"
          type="datetime"
          placeholder="选择结束时间"
          default-time="23:59:59">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="时间类型">
        <el-select v-model="queryParams.dateType" placeholder="请选择类型" clearable>
          <el-option label="出院时间" :value="0" />
          <el-option label="提交时间" :value="1" />
          <el-option label="审核时间" :value="2" />
          <el-option label="结算时间" :value="3" />
          <el-option label="操作时间" :value="4" />
        </el-select>
      </el-form-item>

      <br>
      <el-form-item label="排序规则">
        <el-cascader
          v-model="orderRule"
          :options="orderOptions"
          @change="handleOrderChange"/>
      </el-form-item>
      <el-button type="primary" @click="getList">搜索</el-button>
    </el-form>

<!--    todo 增加一个抽屉，显示病人首页列表，用于管理和观察首页是否经过校验-->

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['system:fzsh:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['system:fzsh:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:fzsh:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:fzsh:export']"
        >导出</el-button>
        <el-button
          type="primary"
          plain
          icon="el-icon-edit"
          size="mini"
          @click="drawerVisible = true">
        查看病案室审查情况
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fzshList" @selection-change="handleSelectionChange" style="width: 100%"
              height="650px"
              :border="true">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="ID" align="center" prop="id" />-->
<!--      <el-table-column label="病人ID" align="center" prop="brid">-->

<!--      </el-table-column>-->
<!--      <el-table-column label="主页ID" align="center" prop="zyid">-->

<!--      </el-table-column>-->
<!--      <el-table-column label="就诊号" width="100" align="center" prop="jzh">-->
<!--      </el-table-column>-->
      <el-table-column label="住院号" width="120" align="center" prop="zyh">
        <template slot-scope="scope">
          <span :style="{ backgroundColor: scope.row.difference === 1 ? '#ffebee' : 'transparent', padding: '2px 8px', borderRadius: '4px' }">
            {{ scope.row.zyh }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="病人姓名" align="center" prop="xm">
      </el-table-column>
      <el-table-column label="DRG编号" width="80" align="center" prop="drgbh">
      </el-table-column>
      <el-table-column label="DRG名称" width="180" align="center" prop="drgmc">
      </el-table-column>
      <el-table-column label="主要诊断" width="180" align="center" prop="zyzd">
      </el-table-column>
      <el-table-column label="疾病代码" width="100" align="center" prop="jbdm">
      </el-table-column>
      <el-table-column label="主要手术" width="180" align="center" prop="zyss">
      </el-table-column>
      <el-table-column label="手术编码" width="100" align="center" prop="ssbm">
      </el-table-column>
      <el-table-column label="总费用" align="center" prop="zfy">
      </el-table-column>
      <el-table-column label="标杆费用" align="center" prop="zfbz">
      </el-table-column>
      <el-table-column label="标杆完成比例" width="100" align="center">
        <template slot-scope="scope">
          <span>{{ (scope.row.zfy / scope.row.zfbz * 100).toFixed(2) }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormatter">
        <template slot-scope="scope">
          <span :style="{ color: getStatusColor(scope.row.status) }">{{ statusFormatter(scope.row, scope.column, scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出院时间" width="100" align="center" prop="cydate">
      </el-table-column>
      <el-table-column label="结算时间" width="100" align="center" prop="hisJsdate"/>
      <el-table-column label="提交人" align="center" prop="submitBy">
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="checkBy">
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="checkTime" width="180">
      </el-table-column>
      <el-table-column label="审核意见及备注" align="center" prop="remark">
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="210" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handlePass(scope.row)"
            v-if="fzshButtonVisible(useYbqd, '通过')"
          >通过</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleCompare(scope.row)"
            v-if="fzshButtonVisible(useYbqd, '对比')"
          >对比</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleBack(scope.row)"
            v-if="fzshButtonVisible(useYbqd, '打回')"
          >打回</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleSetl(scope.row)"
            v-if="fzshButtonVisible(useYbqd, '结账')"
          >结账</el-button>
         <el-button
           size="mini"
           type="text"
           icon="el-icon-edit"
           @click="showDetails(scope.row)"
           v-if="fzshButtonVisible(useYbqd, '详情')"
         >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="showSettle(scope.row)"
            v-if="fzshButtonVisible(useYbqd, '查看详情')"
          >查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-drawer
      title="首页审查情况"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%">
      <div class="drawer-container">
        <el-table :data="drawerData">
<!--          <el-table-column property="jzh" label="就诊号" align="center" width="150"></el-table-column>-->
          <el-table-column property="bah" label="住院号" align="center" width="150"></el-table-column>
          <el-table-column property="xm" label="姓名" align="center" width="150"></el-table-column>
          <!--        <el-table-column property="address" label="主要诊断"></el-table-column>-->
          <!--        <el-table-column property="date" label="诊断编码" width="150"></el-table-column>-->
          <!--        <el-table-column property="date" label="主要手术" width="150"></el-table-column>-->
          <!--        <el-table-column property="date" label="手术编码" width="150"></el-table-column>-->
          <el-table-column property="status" label="是否审查" align="center" width="150">
            <template slot-scope="scope">
              <span :style="{ color: getStatusColor(scope.row.status) }">{{ statusFormatter(scope.row, scope.column, scope.row.status) }}</span>
            </template>
          </el-table-column>
          <el-table-column property="rydate" label="入院时间" align="center" width="150"></el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <el-drawer
      title="手术诊断对比"
      :visible.sync="compareDrawerVisible"
      direction="rtl"
      size="60%">
      <div class="drawer-container">
        <div class="compare-section">
          <div class="section-title">诊断对比</div>
          <div>
            <el-button type="success" @click="saveContent('5')">保存医生诊断手术</el-button>
          </div>
          <div class="compare-container">
            <div class="compare-list">
              <div class="list-header">医生诊断</div>
              <div class="list-content">
                <div v-for="(item, index) in compareData.doctorDiagnosis"
                     :key="'doctor-'+index"
                     class="list-item"
                     :class="{ 'highlight': getHighlightStatus(item, 'doctorDiagnosis') }">
                  <div class="item-content">
                    <div class="item-name">{{ item.zdmc }}</div>
                    <div class="item-code">{{ item.jbbm }}</div>
                    <div class="item-remark" v-if="item.remark">
                      <el-tooltip :content="item.remark" placement="top">
                        <span class="remark-text">{{ item.remark.length > 10 ? item.remark.substring(0, 10) + '...' : item.remark }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="item-actions">
                    <el-button type="text" @click="editRemark('doctorDiagnosis', index)">
                      <i class="el-icon-edit"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('doctorDiagnosis', index, 'up')" :disabled="index === 0">
                      <i class="el-icon-top"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('doctorDiagnosis', index, 'down')" :disabled="index === compareData.doctorDiagnosis.length - 1">
                      <i class="el-icon-bottom"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('doctorDiagnosis', index, 'top')" :disabled="index === 0">
                      <i class="el-icon-upload2"></i>
                    </el-button>
                    <el-button type="text" @click="deleteItem('doctorDiagnosis', index)">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </div>
                </div>
                <div class="add-item">
<!--                  <el-input v-model="newDoctorDiagnosis.zdmc" placeholder="诊断名称" size="small" style="width: 120px"/>-->
<!--                  <el-input v-model="newDoctorDiagnosis.jbbm" placeholder="诊断编码" size="small" style="width: 120px"/>-->
<!--                  <el-button type="primary" size="small" @click="addItem('doctorDiagnosis')">添加</el-button>-->
                  <el-autocomplete
                  v-model="diagQueryStr.doctorDiagnosis"
                  :fetch-suggestions="diagSearchAsync"
                  placeholder="请输入新诊断或手术"
                  @select="handleSelect($event, 'doctorDiagnosis')"
                ></el-autocomplete>
                </div>
              </div>
            </div>
            <div class="compare-list">
              <div class="list-header">病案诊断</div>
              <div class="list-content">
                <div v-for="(item, index) in compareData.medicalDiagnosis"
                     :key="'medical-'+index"
                     class="list-item"
                     :class="{ 'highlight': getHighlightStatus(item, 'medicalDiagnosis') }">
                  <div class="item-content">
                    <div class="item-name">{{ item.zdmc }}</div>
                    <div class="item-code">{{ item.jbbm }}</div>
                    <div class="item-remark" v-if="item.remark">
                      <el-tooltip :content="item.remark" placement="top">
                        <span class="remark-text">{{ item.remark.length > 10 ? item.remark.substring(0, 10) + '...' : item.remark }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="item-actions">
                    <el-button type="text" @click="editRemark('medicalDiagnosis', index)">
                      <i class="el-icon-edit"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('medicalDiagnosis', index, 'up')" :disabled="index === 0">
                      <i class="el-icon-top"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('medicalDiagnosis', index, 'down')" :disabled="index === compareData.medicalDiagnosis.length - 1">
                      <i class="el-icon-bottom"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('medicalDiagnosis', index, 'top')" :disabled="index === 0">
                      <i class="el-icon-upload2"></i>
                    </el-button>
                    <el-button type="text" @click="deleteItem('medicalDiagnosis', index)">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </div>
                </div>
                <div class="add-item">
<!--                  <el-input v-model="newMedicalDiagnosis.zdmc" placeholder="诊断名称" size="small" style="width: 120px"/>-->
<!--                  <el-input v-model="newMedicalDiagnosis.jbbm" placeholder="诊断编码" size="small" style="width: 120px"/>-->
<!--                  <el-button type="primary" size="small" @click="addItem('medicalDiagnosis')">添加</el-button>-->
                  <el-autocomplete
                    v-model="diagQueryStr.medicalDiagnosis"
                    :fetch-suggestions="diagSearchAsync"
                    placeholder="请输入新诊断或手术"
                    @select="handleSelect($event, 'medicalDiagnosis')"
                  ></el-autocomplete>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="compare-section">
          <div class="section-title">手术对比</div>
          <div class="compare-container">
            <div class="compare-list">
              <div class="list-header">医生手术</div>
              <div class="list-content">
                <div v-for="(item, index) in compareData.doctorOperation"
                     :key="'doctor-op-'+index"
                     class="list-item"
                     :class="{ 'highlight': getHighlightStatus(item, 'doctorOperation') }">
                  <div class="item-content">
                    <div class="item-name">{{ item.ssmc }}</div>
                    <div class="item-code">{{ item.ssbm }}</div>
                    <div class="item-remark" v-if="item.remark">
                      <el-tooltip :content="item.remark" placement="top">
                        <span class="remark-text">{{ item.remark.length > 10 ? item.remark.substring(0, 10) + '...' : item.remark }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="item-actions">
                    <el-button type="text" @click="editRemark('doctorOperation', index)">
                      <i class="el-icon-edit"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('doctorOperation', index, 'up')" :disabled="index === 0">
                      <i class="el-icon-top"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('doctorOperation', index, 'down')" :disabled="index === compareData.doctorOperation.length - 1">
                      <i class="el-icon-bottom"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('doctorOperation', index, 'top')" :disabled="index === 0">
                      <i class="el-icon-upload2"></i>
                    </el-button>
                    <el-button type="text" @click="deleteItem('doctorOperation', index)">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </div>
                </div>
                <div class="add-item">
<!--                  <el-input v-model="newDoctorOperation.ssmc" placeholder="手术名称" size="small" style="width: 120px"/>-->
<!--                  <el-input v-model="newDoctorOperation.ssbm" placeholder="手术编码" size="small" style="width: 120px"/>-->
<!--                  <el-button type="primary" size="small" @click="addItem('doctorOperation')">添加</el-button>-->
                  <el-autocomplete
                    v-model="diagQueryStr.doctorOperation"
                    :fetch-suggestions="oprnSearchAsync"
                    placeholder="请输入新诊断或手术"
                    @select="handleSelect($event, 'doctorOperation')"
                  ></el-autocomplete>
                </div>
              </div>
            </div>
            <div class="compare-list">
              <div class="list-header">病案手术</div>
              <div class="list-content">
                <div v-for="(item, index) in compareData.medicalOperation"
                     :key="'medical-op-'+index"
                     class="list-item"
                     :class="{ 'highlight': getHighlightStatus(item, 'medicalOperation') }">
                  <div class="item-content">
                    <div class="item-name">{{ item.ssmc }}</div>
                    <div class="item-code">{{ item.ssbm }}</div>
                    <div class="item-remark" v-if="item.remark">
                      <el-tooltip :content="item.remark" placement="top">
                        <span class="remark-text">{{ item.remark.length > 10 ? item.remark.substring(0, 10) + '...' : item.remark }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="item-actions">
                    <el-button type="text" @click="editRemark('medicalOperation', index)">
                      <i class="el-icon-edit"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('medicalOperation', index, 'up')" :disabled="index === 0">
                      <i class="el-icon-top"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('medicalOperation', index, 'down')" :disabled="index === compareData.medicalOperation.length - 1">
                      <i class="el-icon-bottom"></i>
                    </el-button>
                    <el-button type="text" @click="moveItem('medicalOperation', index, 'top')" :disabled="index === 0">
                      <i class="el-icon-upload2"></i>
                    </el-button>
                    <el-button type="text" @click="deleteItem('medicalOperation', index)">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </div>
                </div>
                <div class="add-item">
<!--                  <el-input v-model="newMedicalOperation.ssmc" placeholder="手术名称" size="small" style="width: 120px"/>-->
<!--                  <el-input v-model="newMedicalOperation.ssbm" placeholder="手术编码" size="small" style="width: 120px"/>-->
<!--                  <el-button type="primary" size="small" @click="addItem('medicalOperation')">添加</el-button>-->
                  <el-autocomplete
                    v-model="diagQueryStr.medicalOperation"
                    :fetch-suggestions="oprnSearchAsync"
                    placeholder="请输入新诊断或手术"
                    @select="handleSelect($event, 'medicalOperation')"
                  ></el-autocomplete>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <el-dialog :title="'诊断手术详情'" :visible.sync="qdOpen" width="85%">
      <el-tabs v-model="dialogActiveName" type="card">
        <el-tab-pane label="医保诊断详情" name="first">
          <div>
            <el-table :data="detailInfoPat.zdxx">
              <el-table-column label="诊断次序" align="center" prop="zdcx" show-overflow-tooltip/>
              <el-table-column label="病种名称" align="center" prop="zdmc" show-overflow-tooltip/>
              <el-table-column label="疾病代码" align="center" prop="jbbm" show-overflow-tooltip/>
              <el-table-column label="入院病情" align="center" prop="rybq" show-overflow-tooltip>
                <template v-slot="scope">
                  {{getRybqDisplay(scope.row.rybq)}}
                </template>
              </el-table-column>
              <el-table-column label="记录人" align="center" prop="jlr" show-overflow-tooltip>
                <template v-slot="scope">
                  {{ scope.row.jlr ? scope.row.jlr : '病案科' }}
                </template>
              </el-table-column>
            </el-table>

            <div style="margin-top: 30px">
              <el-table :data="detailInfoPat.ssxx">
                <el-table-column label="手术次序" align="center" prop="sscx" show-overflow-tooltip/>
                <el-table-column label="手术名称" align="center" prop="ssmc" show-overflow-tooltip/>
                <el-table-column label="手术编码" align="center" prop="ssbm" show-overflow-tooltip/>
                <el-table-column label="术者" align="center" prop="sz" show-overflow-tooltip/>
                <el-table-column label="手术开始时间" align="center" prop="sskssj" show-overflow-tooltip/>
                <el-table-column label="手术结束时间" align="center" prop="ssjssj" show-overflow-tooltip/>
                <el-table-column label="麻醉医师" align="center" prop="mzys" show-overflow-tooltip/>
                <el-table-column label="麻醉开始时间" align="center" prop="mzkssj" show-overflow-tooltip/>
                <el-table-column label="麻醉结束时间" align="center" prop="mzjssj" show-overflow-tooltip/>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="保存诊断详情" name="second">
          <div>
            <el-table :data="detailInfoPat.submitDise">
              <el-table-column label="诊断次序" align="center" prop="order_number" show-overflow-tooltip/>
              <el-table-column label="诊断名称" align="center" prop="diag_name" show-overflow-tooltip/>
              <el-table-column label="诊断编码" align="center" prop="diag_code" show-overflow-tooltip/>
              <el-table-column label="入院病情" align="center" prop="adm_cond_type" show-overflow-tooltip>
                <template v-slot="scope">
                  {{getRybqDisplay(scope.row.adm_cond_type)}}
                </template>
              </el-table-column>
              <el-table-column label="记录人" align="center" prop="create_user" show-overflow-tooltip/>
            </el-table>

            <div style="margin-top: 30px">
              <el-table :data="detailInfoPat.submitOprn">
                <el-table-column label="手术次序" align="center" prop="order_number" show-overflow-tooltip/>
                <el-table-column label="手术名称" align="center" prop="oprn_oprt_name" show-overflow-tooltip/>
                <el-table-column label="手术编码" align="center" prop="oprn_oprt_code" show-overflow-tooltip/>
                <el-table-column label="术者" align="center" prop="oper_dr_name" show-overflow-tooltip/>
                <el-table-column label="手术开始时间" align="center" prop="oprn_oprt_begntime" show-overflow-tooltip/>
                <el-table-column label="手术结束时间" align="center" prop="oprn_oprt_endtime" show-overflow-tooltip/>
                <el-table-column label="麻醉医师" align="center" prop="anst_dr_name" show-overflow-tooltip/>
                <el-table-column label="麻醉开始时间" align="center" prop="anst_begntime" show-overflow-tooltip/>
                <el-table-column label="麻醉结束时间" align="center" prop="anst_endtime" show-overflow-tooltip/>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div style="margin-top: 30px">
        <el-button size="small" type="primary" @click="handlePass({jzh: detailInfoPat.brid + '_' + detailInfoPat.zyid})">通过</el-button>
        <el-button size="small" type="danger" @click="handleBack({jzh: detailInfoPat.brid + '_' + detailInfoPat.zyid})">打回</el-button>
        <el-button size="small" type="warning" @click="dialogToJsqd">结算清单</el-button>
        <el-button size="small" type="success" @click="dialogToYfz">预分组</el-button>
      </div>
    </el-dialog>

    <el-dialog title="编辑备注" :visible.sync="editingRemark.listName !== ''" width="30%">
      <el-input
        type="textarea"
        v-model="editingRemark.content"
        :rows="4"
        placeholder="请输入备注信息">
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelEditRemark">取 消</el-button>
        <el-button type="primary" @click="saveRemark">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  listFzsh,
  getFzsh,
  delFzsh,
  addFzsh,
  updateFzsh,
  handleAudits,
  getScqk,
  selectLastSubmitFzsh,
  saveZdSs
} from '@/api/system/fzsh'
import { parseTime } from '../../../utils/ruoyi'
import { getdk, getFzPageIp } from '@/api/drg/syjl'
import {
  dateToString,
  formatLastSeconds,
  formatZeroSeconds,
  getDayAgoForNum,
  getTodayLastSecond
} from '../../../utils/dateUtils'
import { listZdxxByLc, listZdxxYb } from '@/api/drg/brzdxx'
import { listSsxxByLc, listSsxxYb } from '@/api/drg/ssjl'
import {getRybqDisplay} from '@/utils/dict/EnumMapping'
import { getOption } from '@/api/system/option'
import { checkPermi } from '@/utils/permission'
import { fzshButtonVisible } from '../../../utils/fzshButtonVisible'
import { selectSetlZdList } from '@/api/system/settleZdxx'
import { selectSetlSsList } from '@/api/system/settleSsjl'



export default {
  name: "Fzsh",
  data() {
    return {
      brbs: '', //抽屉内的brbs
      brid: '',
      zyid: '',
      diagQueryStr: {
        doctorDiagnosis: '',
        medicalDiagnosis: ''
      },
      dialogActiveName: 'first',
      drawerVisible: false,
      drawerData: [],
      address : '',
      port : '',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // DRG维护审核表格数据
      fzshList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        brid: null,
        zyid: null,
        jzh: null,
        zyh: null,
        xm: null,
        drgbh: null,
        drgmc: null,
        zyzd: null,
        jbdm: null,
        zyss: null,
        ssbm: null,
        zfy: null,
        zfbz: null,
        status: '0',
        submitBy: null,
        checkBy: null,
        checkTime: null,
        startDate: '',
        endDate: '',
        dateType: 1
      },
      orderRule: ['zyh', 'asc'],
      startDate: new Date(),
      endDate: new Date(),
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      statusOptions: [
        {
          label: '未审核',
          value: 0,
          row: {
            listClass: 'default'
          }
        },
        {
          label: '审核通过',
          value: 1,
          row: {
            listClass: 'default'
          }
        },
        {
          label: '已打回',
          value: 2,
          row: {
            listClass: 'default'
          }
        },
        {
          label: '已结账',
          value: 3,
          row: {
            listClass: 'default'
          }
        }
      ],
      qdOpen: false,
      detailInfoPat: {
        brid: '',
        zyid: '',
        zdxx: [],
        ssxx: [],
        submitDise: [],
        submitOprn: []
      },
      useYbqd: 0,
      orderOptions: [
        {
          value: 'zyh',
          label: '病案号',
          children: [
            {
              value: 'asc',
              label: '升序'
            },
            {
              value: 'desc',
              label: '降序'
            }
          ]
        },
        {
          value: 'check_time',
          label: '审核时间',
          children: [
            {
              value: 'asc',
              label: '升序'
            },
            {
              value: 'desc',
              label: '降序'
            }
          ]
        },
        {
          value: 'cydate',
          label: '出院时间',
          children: [
            {
              value: 'asc',
              label: '升序'
            },
            {
              value: 'desc',
              label: '降序'
            }
          ]
        },
        {
          value: 'his_jsdate',
          label: '结算时间',
          children: [
            {
              value: 'asc',
              label: '升序'
            },
            {
              value: 'desc',
              label: '降序'
            }
          ]
        },
      ],
      compareDrawerVisible: false,
      compareData: {
        doctorDiagnosis: [],
        medicalDiagnosis: [],
        doctorOperation: [],
        medicalOperation: []
      },
      newDoctorDiagnosis: { name: '', code: '', remark: '' },
      newMedicalDiagnosis: { name: '', code: '', remark: '' },
      newDoctorOperation: { name: '', code: '', remark: '' },
      newMedicalOperation: { name: '', code: '', remark: '' },
      editingRemark: {
        listName: '',
        index: -1,
        content: ''
      }
    };
  },
  created() {
    var curDate = new Date()
    curDate.setDate(curDate.getDate() - 7)
    this.startDate = formatZeroSeconds(getDayAgoForNum(7))
    this.endDate = formatLastSeconds(this.endDate)
    console.log(getTodayLastSecond());

    this.role = this.$store.state.user.roles[0]
    this.initIPAndPort()
    this.getList();
    this.initDrawerData()
  },
  methods: {
    handleSelect(item, listName) {
      this.compareData[listName].push({
        ...item,
        brid: this.brid,
        zyid: this.zyid,
        brbs: this.brbs,
        jlly: listName.includes('doctor') ? '5' : '6'
      })
      this.diagQueryStr[listName] = ''
      console.log(this.diagQueryStr)
    },
    oprnSearchAsync(queryString, cb) {
      listSsxxByLc(queryString).then(res => {
        const ssList = res.rows.map(item => ({
          value: item.ssstr,
          ssmc: item.ssmc,
          ssbm: item.ssbm
        }))
        cb(ssList);
      })
    },
    diagSearchAsync(queryString, cb){
      listZdxxByLc(queryString).then(res => {
        const zdList = res.rows.map(item => ({
          value: item.zdstr,
          zdmc: item.zdmc,
          jbbm: item.jbbm
        }));
        cb(zdList);
      })
    },
    saveContent(val) {
      let diagListName = val === '5' ? 'doctorDiagnosis' : 'medicalDiagnosis'
      let oprnListName = val === '5' ? 'doctorOperation' : 'medicalOperation'

      const diagList = this.compareData[diagListName]
      const oprnList = this.compareData[oprnListName]

      for (let i = 0; i < diagList.length; i++) {
        diagList[i].zdcx = i + 1;
      }
      for (let i = 0; i < oprnList.length; i++) {
        oprnList[i].sscx = i + 1;
      }

      console.log('hello')
      saveZdSs({
        brbs: this.brbs,
        jlly: val,
        zdList: diagList,
        ssList: oprnList ? oprnList : []
      }).then(res => {
        if (res.code === 200) {
          this.$modal.msgSuccess('保存成功')
        } else {
          this.$modal.msgError('保存失败')
        }
      }).catch(err => {
        this.$modal.msgError('保存失败：' + err.message)
      })
    },
    fzshButtonVisible,
    checkPermi,
    handleOrderChange(val){
      this.queryParams.orderKey = val[0]
      this.queryParams.orderFun = val[1]
      // console.log(val)
    },
    formatDate(val){
      return dateToString(val);
    },
    dialogToJsqd(){
      getdk().then(response => {
        var dkh = response.rows[0]
        getFzPageIp().then(response => {
          let brbs = this.detailInfoPat.brid + '_' + this.detailInfoPat.zyid
          window.open("http://" + response + ":" + dkh + "/views/jsxx/index?brid=" + this.detailInfoPat.brid + "&zyid=" + this.detailInfoPat.zyid + "&brbs=" + brbs);
        });
      })
    },
    dialogToYfz(){
      getdk().then(response => {
        var dkh = response.rows[0]
        getFzPageIp().then(response => {
          let brbs = this.detailInfoPat.brid + '_' + this.detailInfoPat.zyid
          window.open("http://" + response + ":" + dkh + "/views/drg/yfz/index?brid=" + this.detailInfoPat.brid + "&brbs=" + brbs + "&qdflag=1" + "&id=1");
        });
      });
    },
    getRybqDisplay(val){
      return getRybqDisplay(val)
    },
    initYbZdSs(){
      this.detailInfoPat.zdxx = [];
      this.detailInfoPat.ssxx = [];
      this.detailInfoPat.submitOprn = [];
      this.detailInfoPat.submitDise = [];
      //获取医保诊断
      listZdxxYb({
        brid: this.detailInfoPat.brid,
        zyid: this.detailInfoPat.zyid
      }).then(res => {
        console.log(res)
        this.detailInfoPat.zdxx = res.rows
      })
      listSsxxYb({
        brid: this.detailInfoPat.brid,
        zyid: this.detailInfoPat.zyid
      }).then(res => {
        console.log(res)
        this.detailInfoPat.ssxx = res.rows
      })

      //获取提交的诊断
      selectLastSubmitFzsh(this.detailInfoPat.brid, this.detailInfoPat.zyid).then(res => {
        console.log(res)
        let detail = JSON.parse(res.record.infoJson)
        console.log(detail, 'detail')
        this.detailInfoPat.submitDise = detail.data.diseinfo;
        this.detailInfoPat.submitOprn = detail.data.oprninfo;
        console.log(this.detailInfoPat, 'finalInfo')
      })

      console.log(this.detailInfoPat)
    },
    showSettle(value){
      this.detailInfoPat.brid = value.brid;
      this.detailInfoPat.zyid = value.zyid;
      this.qdOpen = true;
      this.initYbZdSs()
    },
    parseTime,
    /** 查询DRG维护审核列表 */
    getList() {
      // 格式化时间参数
      if(this.startDate && this.endDate) {
        this.queryParams.startDate = dateToString(this.startDate)
        this.queryParams.endDate = dateToString(this.endDate)
      }
      this.loading = true;
      listFzsh(this.queryParams).then(response => {
        this.fzshList = response.rows;
        this.total = response.total;
        this.loading = false;

        const testObj = this.fzshList[0]
        console.log(testObj.zfy / testObj.zfbz * 100)
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        brid: null,
        zyid: null,
        jzh: null,
        zyh: null,
        xm: null,
        drgbh: null,
        drgmc: null,
        zyzd: null,
        jbdm: null,
        zyss: null,
        ssbm: null,
        zfy: null,
        zfbz: null,
        status: null,
        submitBy: null,
        checkBy: null,
        checkTime: null,
        remark: null,
        createBy: null,
        updateBy: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加DRG维护审核";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFzsh(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改DRG维护审核";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFzsh(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFzsh(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除DRG维护审核编号为"' + ids + '"的数据项？').then(function() {
        return delFzsh(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/fzsh/export', {
        ...this.queryParams
      }, `fzsh_${new Date().getTime()}.xlsx`)
    },
    getStatusColor(status) {
      const colorMap = {
        '-1': 'red',
        '1': 'green',
        '2': 'blue',
        '0': 'orange',
        '3': 'purple'
      };
      return colorMap[status] || 'black';
    },
    statusFormatter(row, column, cellValue){const statusMap = {
      '-1': '未提交',
      '0': '未审核',
      '1': '已通过',
      '2': '已打回',
      '3': '已结账'
    };
      return statusMap[cellValue] || 'Unknown';
    },
    handlePass(row){
      handleAudits({
        id: row.id,
        status: '1',
        jzh: row.jzh
      }).then(res => {
        if(res.code === 200) {
          this.$modal.msgSuccess('操作成功')
          this.qdOpen = false
          this.getList()
        }
      })
    },
    handleSetl(row){
      handleAudits({
        id: row.id,
        status: '3',
        jzh: row.jzh
      }).then(res => {
        if(res.code === 200) {
          this.$modal.msgSuccess('操作成功')
          this.qdOpen = false
          this.getList()
        }
      })
    },
    handleBack(row){
      handleAudits({
        id: row.id,
        status: '2',
        jzh: row.jzh
      }).then(res => {
        if(res.code === 200) {
          this.$modal.msgSuccess('操作成功')
          this.qdOpen = false
          this.getList()
        }
      })
    },
    async initIPAndPort(){
      const useYbqdRes = await getOption("use_ybqd")
      this.useYbqd = useYbqdRes.data.cValue

      getdk().then(response => {
        if (response == undefined || response == null || response == "") {
          this.$modal.msgError("前端端口配置错误");
          return
        }
        this.dkh = response.rows[0]
        getFzPageIp().then(response => {
          if (response == undefined || response == null || response == "") {
            this.$modal.msgError("分组页面IP地址配置错误");
            return
          }
          this.address = response
        });
      });
    },
    showDetails(row){
      let urlStr = "http://" + this.address + ":" + this.dkh + "/views/drg/yfz/index?id=2&bah=" + row.zyh + "&brbs=" + row.jzh;
      if(this.useYbqd == '3') {
        urlStr += '&qdflag=1'
      }
      if(this.$store.state.user.name === 'admin') {
        urlStr = urlStr + "&audit=1"
      }
      window.open(urlStr);
    },
    async initDrawerData(){
      const res = await getScqk()
      this.drawerData = res.rows
    },
    async handleCompare(row) {
      this.compareDrawerVisible = true;

      this.brbs = row.jzh
      this.brid = row.brid
      this.zyid = row.zyid

      const doctorZd = await selectSetlZdList({ brbs : row.jzh, jlly: '5' })
      const doctorSs = await selectSetlSsList({ brbs : row.jzh, jlly: '5' })
      const medicalZd = await selectSetlZdList({ brbs : row.jzh, jlly: '6' })
      const medicalSs = await selectSetlSsList({ brbs : row.jzh, jlly: '6' })

      this.compareData = {
        doctorDiagnosis: doctorZd.rows,
        medicalDiagnosis: medicalZd.rows,
        doctorOperation: doctorSs.rows,
        medicalOperation: medicalSs.rows
      }

    },
    // 检查项目是否在对方列表中
    checkItemExists(item, targetList) {
      if(item.jbbm !== undefined) {
        return targetList.some(target =>
          target.zdmc === item.zdmc && target.jbbm === item.jbbm
        );
      } else {
        console.log('sss')
        return targetList.some(target =>
          target.ssmc === item.ssmc && target.ssbm === item.ssbm
        )
      }
    },
    // 获取高亮状态
    getHighlightStatus(item, listName) {
      if (listName.includes('doctor')) {
        const targetList = listName.includes('Diagnosis')
          ? this.compareData.medicalDiagnosis
          : this.compareData.medicalOperation;
        return !this.checkItemExists(item, targetList);
      } else {
        const targetList = listName.includes('Diagnosis')
          ? this.compareData.doctorDiagnosis
          : this.compareData.doctorOperation;
        return !this.checkItemExists(item, targetList);
      }
    },
    moveItem(listName, index, direction) {
      const list = this.compareData[listName];
      if (direction === 'up' && index > 0) {
        const tmp = list.splice(index - 1, 1, list[index])[0];
        list[index] = tmp;
      } else if (direction === 'down' && index < list.length - 1) {
        const tmp = list.splice(index + 1, 1, list[index])[0];
        list[index] = tmp;
      } else if (direction === 'top') {
        const item = list.splice(index, 1)[0];
        list.unshift(item);
      }
    },
    deleteItem(listName, index) {
      this.compareData[listName].splice(index, 1);
    },
    addItem(listName) {
      const newItem = this[`new${listName.charAt(0).toUpperCase() + listName.slice(1)}`];
      if (newItem.name && newItem.code) {
        this.compareData[listName].push({
          name: newItem.name,
          code: newItem.code,
          remark: newItem.remark || ''
        });
        newItem.name = '';
        newItem.code = '';
        newItem.remark = '';
      }
    },
    editRemark(listName, index) {
      this.editingRemark = {
        listName,
        index,
        content: this.compareData[listName][index].remark || ''
      };
    },
    saveRemark() {
      if (this.editingRemark.listName && this.editingRemark.index >= 0) {
        this.compareData[this.editingRemark.listName][this.editingRemark.index].remark = this.editingRemark.content;
        this.editingRemark = {
          listName: '',
          index: -1,
          content: ''
        };
      }
    },
    cancelEditRemark() {
      this.editingRemark = {
        listName: '',
        index: -1,
        content: ''
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.page{

}
.drawer-container{
  padding: 10px;
}
.compare-container {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  gap: 20px;
}

.compare-list {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.list-header {
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-weight: bold;
  text-align: center;
}

.list-content {
  padding: 10px;
  min-height: 200px;
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;

  &.highlight {
    background-color: #ffebee;
    border-color: #ffcdd2;
  }
}

.item-content {
  flex: 1;
}

.item-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.item-code {
  color: #666;
  font-size: 12px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.add-item {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding: 8px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  align-items: center;

  .el-autocomplete {
    flex: 1;
    width: 100%;
  }
}

.compare-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-left: 10px;
  border-left: 4px solid #409EFF;
}

.item-remark {
  margin-top: 4px;
  font-size: 12px;
  color: #666;

  .remark-text {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: #f5f7fa;
    padding: 2px 6px;
    border-radius: 4px;
  }
}
</style>
