<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="判断名称" prop="pdmc">
        <el-input
          v-model="queryParams.pdmc"
          placeholder="请输入判断名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="判断编码" prop="pdbh">
        <el-input
          v-model="queryParams.pdbh"
          placeholder="请输入判断编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="判断类别" prop="pdlb">
        <el-input
          v-model="queryParams.pdlb"
          placeholder="请输入判断类别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入院病情" prop="rybq">
        <el-input
          v-model="queryParams.rybq"
          placeholder="请输入入院病情"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验名称" prop="jymc">
        <el-input
          v-model="queryParams.jymc"
          placeholder="请输入检验名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="校验结果" prop="jyjg">
        <el-input
          v-model="queryParams.jyjg"
          placeholder="请输入校验结果"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="不属于离院方式范围" prop="lyfs">
        <el-input
          v-model="queryParams.lyfs"
          placeholder="请输入不属于离院方式范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="离院方式范围" prop="notlyfs">
        <el-input
          v-model="queryParams.notlyfs"
          placeholder="请输入离院方式范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主诊断判断标准(1全字符，0前几位字符)" prop="zzdbz">
        <el-input
          v-model="queryParams.zzdbz"
          placeholder="请输入主诊断判断标准(1全字符，0前几位字符)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入院病情判断标准(1主诊断，0其他诊断)" prop="rybqbz">
        <el-input
          v-model="queryParams.rybqbz"
          placeholder="请输入入院病情判断标准(1主诊断，0其他诊断)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验结果判断标准(0之间，1大于，2小于)" prop="jyjgbz">
        <el-input
          v-model="queryParams.jyjgbz"
          placeholder="请输入检验结果判断标准(0之间，1大于，2小于)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="其他诊断判断标准(1全字符，0前几位字符)" prop="qtzdbz">
        <el-input
          v-model="queryParams.qtzdbz"
          placeholder="请输入其他诊断判断标准(1全字符，0前几位字符)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="多条件判断标准(1都，0或)" prop="dtjbz">
        <el-input
          v-model="queryParams.dtjbz"
          placeholder="请输入多条件判断标准(1都，0或)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否更换(1是，0否)" prop="isreplace">
        <el-input
          v-model="queryParams.isreplace"
          placeholder="请输入是否更换(1是，0否)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否双向判断(1是，0否)" prop="istwoway">
        <el-input
          v-model="queryParams.istwoway"
          placeholder="请输入是否双向判断(1是，0否)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="没有符合条件的其他诊断是否提示(1是，0否)" prop="istip">
        <el-input
          v-model="queryParams.istip"
          placeholder="请输入没有符合条件的其他诊断是否提示(1是，0否)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:zdpd:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:zdpd:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:zdpd:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:zdpd:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="zdpdList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="判断名称" align="center" prop="pdmc" />
      <el-table-column label="判断编码" align="center" prop="pdbh" />
      <el-table-column label="判断类别" align="center" prop="pdlb" />
      <el-table-column label="主要诊断范围" align="center" prop="zzd" />
      <el-table-column label="其他诊断范围" align="center" prop="qtzd" />
      <el-table-column label="不属于其他诊断的范围" align="center" prop="notqtzd" />
      <el-table-column label="入院病情" align="center" prop="rybq" />
      <el-table-column label="检验名称" align="center" prop="jymc" />
      <el-table-column label="校验结果" align="center" prop="jyjg" />
      <el-table-column label="不属于离院方式范围" align="center" prop="lyfs" />
      <el-table-column label="离院方式范围" align="center" prop="notlyfs" />
      <el-table-column label="主诊断判断标准(1全字符，0前几位字符)" align="center" prop="zzdbz" />
      <el-table-column label="入院病情判断标准(1主诊断，0其他诊断)" align="center" prop="rybqbz" />
      <el-table-column label="检验结果判断标准(0之间，1大于，2小于)" align="center" prop="jyjgbz" />
      <el-table-column label="其他诊断判断标准(1全字符，0前几位字符)" align="center" prop="qtzdbz" />
      <el-table-column label="多条件判断标准(1都，0或)" align="center" prop="dtjbz" />
      <el-table-column label="是否更换(1是，0否)" align="center" prop="isreplace" />
      <el-table-column label="是否双向判断(1是，0否)" align="center" prop="istwoway" />
      <el-table-column label="没有符合条件的其他诊断是否提示(1是，0否)" align="center" prop="istip" />
      <el-table-column label="提示" align="center" prop="tip" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:zdpd:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:zdpd:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改诊断判断对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="判断名称" prop="pdmc">
          <el-input v-model="form.pdmc" placeholder="请输入判断名称" />
        </el-form-item>
        <el-form-item label="判断编码" prop="pdbh">
          <el-input v-model="form.pdbh" placeholder="请输入判断编码" />
        </el-form-item>
        <el-form-item label="判断类别" prop="pdlb">
          <el-input v-model="form.pdlb" placeholder="请输入判断类别" />
        </el-form-item>
        <el-form-item label="主要诊断范围" prop="zzd">
          <el-input v-model="form.zzd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="其他诊断范围" prop="qtzd">
          <el-input v-model="form.qtzd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="不属于其他诊断的范围" prop="notqtzd">
          <el-input v-model="form.notqtzd" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="入院病情" prop="rybq">
          <el-input v-model="form.rybq" placeholder="请输入入院病情" />
        </el-form-item>
        <el-form-item label="检验名称" prop="jymc">
          <el-input v-model="form.jymc" placeholder="请输入检验名称" />
        </el-form-item>
        <el-form-item label="校验结果" prop="jyjg">
          <el-input v-model="form.jyjg" placeholder="请输入校验结果" />
        </el-form-item>
        <el-form-item label="不属于离院方式范围" prop="lyfs">
          <el-input v-model="form.lyfs" placeholder="请输入不属于离院方式范围" />
        </el-form-item>
        <el-form-item label="离院方式范围" prop="notlyfs">
          <el-input v-model="form.notlyfs" placeholder="请输入离院方式范围" />
        </el-form-item>
        <el-form-item label="主诊断判断标准(1全字符，0前几位字符)" prop="zzdbz">
          <el-input v-model="form.zzdbz" placeholder="请输入主诊断判断标准(1全字符，0前几位字符)" />
        </el-form-item>
        <el-form-item label="入院病情判断标准(1主诊断，0其他诊断)" prop="rybqbz">
          <el-input v-model="form.rybqbz" placeholder="请输入入院病情判断标准(1主诊断，0其他诊断)" />
        </el-form-item>
        <el-form-item label="检验结果判断标准(0之间，1大于，2小于)" prop="jyjgbz">
          <el-input v-model="form.jyjgbz" placeholder="请输入检验结果判断标准(0之间，1大于，2小于)" />
        </el-form-item>
        <el-form-item label="其他诊断判断标准(1全字符，0前几位字符)" prop="qtzdbz">
          <el-input v-model="form.qtzdbz" placeholder="请输入其他诊断判断标准(1全字符，0前几位字符)" />
        </el-form-item>
        <el-form-item label="多条件判断标准(1都，0或)" prop="dtjbz">
          <el-input v-model="form.dtjbz" placeholder="请输入多条件判断标准(1都，0或)" />
        </el-form-item>
        <el-form-item label="是否更换(1是，0否)" prop="isreplace">
          <el-input v-model="form.isreplace" placeholder="请输入是否更换(1是，0否)" />
        </el-form-item>
        <el-form-item label="是否双向判断(1是，0否)" prop="istwoway">
          <el-input v-model="form.istwoway" placeholder="请输入是否双向判断(1是，0否)" />
        </el-form-item>
        <el-form-item label="没有符合条件的其他诊断是否提示(1是，0否)" prop="istip">
          <el-input v-model="form.istip" placeholder="请输入没有符合条件的其他诊断是否提示(1是，0否)" />
        </el-form-item>
        <el-form-item label="提示" prop="tip">
          <el-input v-model="form.tip" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listZdpd, getZdpd, delZdpd, addZdpd, updateZdpd } from "@/api/system/zdpd";

export default {
  name: "Zdpd",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 诊断判断表格数据
      zdpdList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        pdmc: null,
        pdbh: null,
        pdlb: null,
        zzd: null,
        qtzd: null,
        notqtzd: null,
        rybq: null,
        jymc: null,
        jyjg: null,
        lyfs: null,
        notlyfs: null,
        zzdbz: null,
        rybqbz: null,
        jyjgbz: null,
        qtzdbz: null,
        dtjbz: null,
        isreplace: null,
        istwoway: null,
        istip: null,
        tip: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询诊断判断列表 */
    getList() {
      this.loading = true;
      listZdpd(this.queryParams).then(response => {
        this.zdpdList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        pdmc: null,
        pdbh: null,
        pdlb: null,
        zzd: null,
        qtzd: null,
        notqtzd: null,
        rybq: null,
        jymc: null,
        jyjg: null,
        lyfs: null,
        notlyfs: null,
        zzdbz: null,
        rybqbz: null,
        jyjgbz: null,
        qtzdbz: null,
        dtjbz: null,
        isreplace: null,
        istwoway: null,
        istip: null,
        tip: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加诊断判断";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getZdpd(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改诊断判断";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateZdpd(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZdpd(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除诊断判断编号为"' + ids + '"的数据项？').then(function() {
        return delZdpd(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/zdpd/export', {
        ...this.queryParams
      }, `zdpd_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
