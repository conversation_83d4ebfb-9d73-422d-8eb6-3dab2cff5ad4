<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="科室" prop="officeId">
<!--        <el-input-->
<!--          v-model="queryParams.officeId"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
        <el-select v-model="queryParams.officeId">
          <el-option v-for="item in deptList" :value="item" :key="item" :label="item"/>
        </el-select>
      </el-form-item>
      <el-form-item label="参保类别" prop="xzlb">
<!--        <el-input-->
<!--          v-model="queryParams.xzlb"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
        <el-select v-model="queryParams.xzlb" clearable>
          <el-option v-for="item in cblb" :value="item" :key="item" :label="item"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:dexx:add']"
        >新增</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['system:dexx:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:dexx:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['system:dexx:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dexxList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="指标类别" align="center" prop="type">
        <template slot-scope="scope">
          {{scope.row.type}}
        </template>
      </el-table-column>
      <el-table-column label="科室" align="center" prop="officeId">
        <template slot-scope="scope">
          {{scope.row.officeId}}
        </template>
      </el-table-column>
      <el-table-column label="参保类别" align="center" prop="xzlb">
        <template slot-scope="scope">
          {{scope.row.xzlb}}
        </template>
      </el-table-column>
      <el-table-column label="定额" align="center" prop="de">
        <template slot-scope="scope">
          <span>{{scope.row.de}}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="备注信息" align="center" prop="remarks">-->
<!--        <template slot-scope="scope">-->
<!--          <dict-tag :value="scope.row.remarks"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:dexx:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:dexx:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改定额信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="指标类别" prop="type">
          <el-input v-model="form.type" placeholder="请输入指标名称" />
        </el-form-item>
        <el-form-item label="科室" prop="officeId">
          <el-input v-model="form.officeId" placeholder="请输入科室" />
        </el-form-item>
        <el-form-item label="参保类别" prop="xzlb">
          <el-input v-model="form.xzlb" placeholder="请输入参保类别" />
        </el-form-item>
        <el-form-item label="定额" prop="de">
          <el-input v-model="form.de" placeholder="请输入定额" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入${comment}" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDexx, getDexx, delDexx, addDexx, updateDexx } from "@/api/system/dexx";
import { parseTime } from '../../../utils/ruoyi'
import { getDeptListFromSy } from '@/api/tjfx/tjfx'
import { getCurDayStr } from '@/utils/dateUtils'

export default {
  name: "Dexx",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 定额信息表格数据
      dexxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        officeId: '全院',
        xzlb: '职工',
        de: null,
        debh: null,
        debegin: null,
        deend: null,
        jkCode: null,
        createDate: null,
        updateDate: null,
        remarks: null,
      },
      //科室
      deptList: [
        '全院','内一科','内二科'
      ],
      //参保类别
      cblb: [
        '职工','居民'
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        xzlb: [
          { required: true, message: "参保类别不能为空", trigger: "blur" }
        ],
        de: [
          { required: true, message: "定额不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.initDept()
    this.getList();
  },
  methods: {
    initDept(){
      getDeptListFromSy().then(res => {
        this.deptList = ['全院']
        this.deptList.push(...res.list)
      })
    },
    parseTime,
    /** 查询定额信息列表 */
    getList() {
      this.loading = true;
      listDexx(this.queryParams).then(response => {
        this.dexxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        type: null,
        officeId: null,
        xzlb: null,
        de: null,
        debh: null,
        debegin: null,
        deend: null,
        jkCode: null,
        createBy: null,
        createDate: null,
        updateBy: null,
        updateDate: null,
        remarks: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加定额信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDexx(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改定额信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.form.updateDate = getCurDayStr()
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDexx(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDexx(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除定额信息编号为"' + ids + '"的数据项？').then(function() {
        return delDexx(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/dexx/export', {
        ...this.queryParams
      }, `dexx_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
