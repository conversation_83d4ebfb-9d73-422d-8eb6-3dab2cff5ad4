<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="病案号" prop="bah">
        <el-input
          v-model="queryParams.bah"
          placeholder="请输入病案号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="显示差异" prop="sfqf">
        <el-select v-model="queryParams.sfqf">
          <el-option value="1" label="本地分组器差异">本地分组器差异</el-option>
          <el-option value="0" label="开源分组器差异">开源分组器差异</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:tldy:export']">导出</el-button>
        <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
        <el-button type="success"  size="mini" @click="blfzmxPlfz">本地分组器分组测试</el-button>
        <el-button type="success"  size="mini" @click="chsDrgPlfz">开源分组器分组测试</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="blfzmxList" >
      <el-table-column show-overflow-tooltip label="DRG编码" align="center" prop="drgbh" />
      <el-table-column show-overflow-tooltip label="DRG名称" align="center" prop="drgmc" />
      <el-table-column show-overflow-tooltip label="测试DRG编码" align="center" prop="csdrgbh" />
      <el-table-column show-overflow-tooltip label="测试DRG名称" align="center" prop="csdrgmc" />
      <el-table-column show-overflow-tooltip label="开源DRG编码" align="center" prop="kydrgbh" />
      <el-table-column show-overflow-tooltip label="开源DRG名称" align="center" prop="kydrgmc" />
      <el-table-column show-overflow-tooltip label="病案号" align="center" prop="bah" />
      <el-table-column show-overflow-tooltip label="医疗机构" align="center" prop="yljg" />
      <el-table-column show-overflow-tooltip label="结算流水号" align="center" prop="jslsh" />
      <el-table-column show-overflow-tooltip label="姓名" align="center" prop="xm" />
      <el-table-column show-overflow-tooltip label="住院次数" align="center" prop="zycs" />
      <el-table-column show-overflow-tooltip label="性别" align="center" prop="xb" />
      <el-table-column show-overflow-tooltip label="年龄" align="center" prop="nl" />
      <el-table-column show-overflow-tooltip label="不足一周岁年龄" align="center" prop="bzyzsnl" />
      <el-table-column show-overflow-tooltip label="新生儿出生体重" align="center" prop="xsecstz" />
      <el-table-column show-overflow-tooltip label="新生儿入院体重" align="center" prop="xserytz" />
      <el-table-column show-overflow-tooltip label="住院天数" align="center" prop="sjzyts" />
      <el-table-column show-overflow-tooltip label="离院方式" align="center" prop="lyfs" />
      <el-table-column show-overflow-tooltip label="总费用" align="center" prop="zfy" />
      <el-table-column show-overflow-tooltip label="主要诊断编码" align="center" prop="jbdm" />
      <el-table-column show-overflow-tooltip label="其他诊断编码1" align="center" prop="jbdm1" />
      <el-table-column show-overflow-tooltip label="其他诊断编码2" align="center" prop="jbdm2" />
      <el-table-column show-overflow-tooltip label="其他诊断编码3" align="center" prop="jbdm3" />
      <el-table-column show-overflow-tooltip label="其他诊断编码4" align="center" prop="jbdm4" />
      <el-table-column show-overflow-tooltip label="其他诊断编码5" align="center" prop="jbdm5" />
      <el-table-column show-overflow-tooltip label="其他诊断编码6" align="center" prop="jbdm6" />
      <el-table-column show-overflow-tooltip label="其他诊断编码7" align="center" prop="jbdm7" />
      <el-table-column show-overflow-tooltip label="其他诊断编码8" align="center" prop="jbdm8" />
      <el-table-column show-overflow-tooltip label="其他诊断编码9" align="center" prop="jbdm9" />
      <el-table-column show-overflow-tooltip label="其他诊断编码10" align="center" prop="jbdm10" />
      <el-table-column show-overflow-tooltip label="其他诊断编码11" align="center" prop="jbdm11" />
      <el-table-column show-overflow-tooltip label="其他诊断编码12" align="center" prop="jbdm12" />
      <el-table-column show-overflow-tooltip label="其他诊断编码13" align="center" prop="jbdm13" />
      <el-table-column show-overflow-tooltip label="其他诊断编码14" align="center" prop="jbdm14" />
      <el-table-column show-overflow-tooltip label="其他诊断编码15" align="center" prop="jbdm15" />
      <el-table-column show-overflow-tooltip label="手术操作编码1" align="center" prop="ssjczbm1" />
      <el-table-column show-overflow-tooltip label="手术操作编码2" align="center" prop="ssjczbm2" />
      <el-table-column show-overflow-tooltip label="手术操作编码3" align="center" prop="ssjczbm3" />
      <el-table-column show-overflow-tooltip label="手术操作编码4" align="center" prop="ssjczbm4" />
      <el-table-column show-overflow-tooltip label="手术操作编码5" align="center" prop="ssjczbm5" />
      <el-table-column show-overflow-tooltip label="手术操作编码6" align="center" prop="ssjczbm6" />
      <el-table-column show-overflow-tooltip label="手术操作编码7" align="center" prop="ssjczbm7" />
      <el-table-column show-overflow-tooltip label="手术操作编码8" align="center" prop="ssjczbm8" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listBlfzmx, getBlfzmx, delBlfzmx, addBlfzmx, updateBlfzmx } from "@/api/tjfx/blfzmx";
import {getToken} from "@/utils/auth";
import {importTemplate} from "@/api/gksz/kkmx";
import {blfzmxPlfz} from "@/api/drg/syjl";
import {Message, MessageBox} from "element-ui";

export default {
  name: "Blfzmx",
  data() {
    return {
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/tjfx/blfzmx/importData"
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 病例分组明细表格数据
      blfzmxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 出院科室名称时间范围
      daterangeScdate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        scdate: null,
        yljg: null,
        jslsh: null,
        xm: null,
        bah: null,
        zycs: null,
        xb: null,
        nl: null,
        bzyzsnl: null,
        xsecstz: null,
        xserytz: null,
        csrq: null,
        rytj: null,
        rysj: null,
        rykb: null,
        cysj: null,
        cykb: null,
        sjzyts: null,
        lyfs: null,
        zfy: null,
        drgbh: null,
        drgmc: null,
        drgqz: null,
        jbdm: null,
        zyzd: null,
        jbdm1: null,
        qtzd1: null,
        sfzl1: null,
        jbdm2: null,
        qtzd2: null,
        sfzl2: null,
        jbdm3: null,
        qtzd3: null,
        sfzl3: null,
        jbdm4: null,
        qtzd4: null,
        sfzl4: null,
        jbdm5: null,
        qtzd5: null,
        sfzl5: null,
        jbdm6: null,
        qtzd6: null,
        sfzl6: null,
        jbdm7: null,
        qtzd7: null,
        sfzl7: null,
        jbdm8: null,
        qtzd8: null,
        sfzl8: null,
        jbdm9: null,
        qtzd9: null,
        sfzl9: null,
        jbdm10: null,
        qtzd10: null,
        sfzl10: null,
        jbdm11: null,
        qtzd11: null,
        sfzl11: null,
        jbdm12: null,
        qtzd12: null,
        sfzl12: null,
        jbdm13: null,
        qtzd13: null,
        sfzl13: null,
        jbdm14: null,
        qtzd14: null,
        sfzl14: null,
        jbdm15: null,
        qtzd15: null,
        sfzl15: null,
        jsdate: null,
        ssjczbm1: null,
        ssjczmc1: null,
        ssjczbm2: null,
        ssjczmc2: null,
        ssjczbm3: null,
        ssjczmc3: null,
        ssjczbm4: null,
        ssjczmc4: null,
        ssjczbm5: null,
        ssjczmc5: null,
        ssjczbm6: null,
        ssjczmc6: null,
        ssjczbm7: null,
        ssjczmc7: null,
        ssjczbm8: null,
        ssjczmc8: null,
        drgrzlb: null,
        jstype: null,
        jsfs: null,
        zyzfy: null,
        tczf: null,
        drgzfbz: null,
        drgybf: null,
        cyksbm: null,
        cyksmc: null,
        sfqf:null,
        csdrgbh:null,
        csdrgmc:null,
        kydrgbh:null,
        kydrgmc:null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async blfzmxPlfz() {
      MessageBox.alert("是否进行本地分组器批量分组","系统提示",{
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
      }).then(res=>{
        if (res == "confirm") {
          blfzmxPlfz()
        }
      }).catch(err=>{
        console.log(err)
      })
    },
    async chsDrgPlfz() {
      MessageBox.alert("是否进行开源分组器批量分组","系统提示",{
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
      }).then(res=>{
        if (res == "confirm") {
          chsDrgPlfz()
        }
      }).catch(err=>{
        console.log(err)
      })
    },
    handleImport() {
      this.upload.title = "病例分组明细导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    /** 查询病例分组明细列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeScdate && '' != this.daterangeScdate) {
        this.queryParams.params["beginScdate"] = this.daterangeScdate[0];
        this.queryParams.params["endScdate"] = this.daterangeScdate[1];
      }
      listBlfzmx(this.queryParams).then(response => {
        this.blfzmxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        scdate: null,
        yljg: null,
        jslsh: null,
        xm: null,
        bah: null,
        zycs: null,
        xb: null,
        nl: null,
        bzyzsnl: null,
        xsecstz: null,
        xserytz: null,
        csrq: null,
        rytj: null,
        rysj: null,
        rykb: null,
        cysj: null,
        cykb: null,
        sjzyts: null,
        lyfs: null,
        zfy: null,
        drgbh: null,
        drgmc: null,
        drgqz: null,
        jbdm: null,
        zyzd: null,
        jbdm1: null,
        qtzd1: null,
        sfzl1: null,
        jbdm2: null,
        qtzd2: null,
        sfzl2: null,
        jbdm3: null,
        qtzd3: null,
        sfzl3: null,
        jbdm4: null,
        qtzd4: null,
        sfzl4: null,
        jbdm5: null,
        qtzd5: null,
        sfzl5: null,
        jbdm6: null,
        qtzd6: null,
        sfzl6: null,
        jbdm7: null,
        qtzd7: null,
        sfzl7: null,
        jbdm8: null,
        qtzd8: null,
        sfzl8: null,
        jbdm9: null,
        qtzd9: null,
        sfzl9: null,
        jbdm10: null,
        qtzd10: null,
        sfzl10: null,
        jbdm11: null,
        qtzd11: null,
        sfzl11: null,
        jbdm12: null,
        qtzd12: null,
        sfzl12: null,
        jbdm13: null,
        qtzd13: null,
        sfzl13: null,
        jbdm14: null,
        qtzd14: null,
        sfzl14: null,
        jbdm15: null,
        qtzd15: null,
        sfzl15: null,
        jsdate: null,
        ssjczbm1: null,
        ssjczmc1: null,
        ssjczbm2: null,
        ssjczmc2: null,
        ssjczbm3: null,
        ssjczmc3: null,
        ssjczbm4: null,
        ssjczmc4: null,
        ssjczbm5: null,
        ssjczmc5: null,
        ssjczbm6: null,
        ssjczmc6: null,
        ssjczbm7: null,
        ssjczmc7: null,
        ssjczbm8: null,
        ssjczmc8: null,
        drgrzlb: null,
        jstype: null,
        jsfs: null,
        zyzfy: null,
        tczf: null,
        drgzfbz: null,
        drgybf: null,
        cyksbm: null,
        cyksmc: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeScdate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.scdate)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加病例分组明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const scdate = row.scdate || this.ids
      getBlfzmx(scdate).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改病例分组明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.scdate != null) {
            updateBlfzmx(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBlfzmx(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const scdates = row.scdate || this.ids;
      this.$modal.confirm('是否确认删除病例分组明细编号为"' + scdates + '"的数据项？').then(function() {
        return delBlfzmx(scdates);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tjfx/blfzmx/export', {
        ...this.queryParams
      }, `blfzmx_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
