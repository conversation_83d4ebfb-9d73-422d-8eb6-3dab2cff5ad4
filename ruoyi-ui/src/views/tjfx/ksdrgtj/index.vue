<template>
  <div class="app-container">
    <div>
      <el-form v-model="params" :inline="true" label-width="68px" size="small">
        <el-form-item>
          <date-filter v-model="dateParams"/>
          <dept-conditions v-model="params.ksname"/>
          <cblb-options v-model="params.cblb"/>
          <el-button @click="handleSearch" type="primary">搜索</el-button>
          <el-button @click="handleExport" type="success">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="dataList" :border="true" v-loading="loading">
<!--        <el-table-column prop="startDate" label="开始时间" width="100" align="center"/>-->
<!--        <el-table-column prop="endDate" label="结束时间" width="100" align="center"/>-->
<!--        <el-table-column prop="datetype" label="时间类型" width="100" align="center"/>-->
        <el-table-column prop="cykb" label="出院科别" width="100" align="center"/>
        <el-table-column prop="drgzs" label="DRG组数" width="100" align="center"/>
        <el-table-column prop="zbas" label="总病案数" width="100" align="center">
          <template v-slot="scope">
            <el-button type="text" @click="openReport(scope.row)">
              {{ scope.row.zbas }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="zgbas" label="职工病案数" width="100" align="center"/>
        <el-table-column prop="jmbas" label="居民病案数" width="100" align="center"/>
        <el-table-column prop="rzbas" label="入组病案数" width="100" align="center"/>
        <el-table-column prop="rzl" label="入组率" width="100" align="center"/>
        <el-table-column prop="zqz" label="总权重" width="100" align="center"/>
        <el-table-column prop="cmi" label="CMI" width="100" align="center"/>
        <el-table-column prop="zfy" label="总费用" width="100" align="center"/>
        <el-table-column prop="pjzyr" label="平均住院日" width="100" align="center"/>
        <el-table-column prop="zyk" label="总盈亏" width="100" align="center"/>
        <el-table-column prop="ljyk" label="例均盈亏" width="100" align="center"/>
        <el-table-column prop="fyxhzs" label="费用消耗指数" width="100" align="center"/>
        <el-table-column prop="sjxhzs" label="时间消耗指数" width="100" align="center"/>
      </el-table>
    </div>
    <div>
      <pagination
        :limit.sync="pageSize"
        :page.sync="pageNum"
        :total="total"
        @pagination="getList"
        ></pagination>
    </div>
    <el-dialog :visible.sync="showReport" width="80%">
      <iframe :src="reportUrl" width="100%" height="600px"></iframe>
    </el-dialog>
  </div>
</template>

<script>
import DateFilter from '../../../components/DateFilter/DateFilter.vue'
import DeptConditions from '../../../components/DeptConditions/index.vue'
import CblbOptions from '../../../components/CblbOptions/CblbOptions.vue'
import { getKsdrgtj } from '../../../api/tjfx/ksdrgtj'
import { getFzPageIp } from '../../../api/drg/syjl'
import { getMonthFirstDayStr, getTodayLastSecondStr } from '../../../utils/dateUtils'

export default {
  name: 'Ksdrgtj',
  components: { CblbOptions, DeptConditions, DateFilter },
  data(){
    return {
      dataList: [],
      loading: false,
      params: {
        ksname: '',
        cblb: []
      },
      dateParams: {
        datetype: 'jsdate',
        startDate: getMonthFirstDayStr(),
        endDate: getTodayLastSecondStr()
      },
      pageSize: 10,
      pageNum: 1,
      total: 0,
      showReport: false,
      reportUrl: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList(){
      this.loading = true
      const res = await getKsdrgtj({
        ...this.params,
        ...this.dateParams
      }, {pageSize : this.pageSize, pageNum : this.pageNum})

      this.dataList = res.rows
      this.total = res.total
      this.loading = false
    },
    async openReport(row) {
      let ip = await getFzPageIp()
      this.reportUrl =
        `http://${ip}:8096/jmreport/view/839089181511389184?dept=${row.cykb}&startDate=${this.dateParams.startDate}&endDate=${this.dateParams.endDate}&datetype=${this.dateParams.datetype}`
      this.showReport = true
    },
    handleExport(){
      this.download(`/tjfx/tjfx/export/ksdrgtj`, {
        ...this.params,
        ...this.dateParams
      }, `科室DRG统计_${new Date().getTime()}.xlsx`)
    },
    handleSearch(){
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">

</style>
