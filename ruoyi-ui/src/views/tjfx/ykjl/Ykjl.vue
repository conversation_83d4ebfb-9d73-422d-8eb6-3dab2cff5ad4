<template>
  <div class="app-container">
    <div style="height: 300px">
      <el-table :data="dataList" :border="true" v-loading="loading">
        <el-table-column prop="bah" label="病案号" align="center" width="100"/>
        <el-table-column prop="zfje" label="自付金额" align="center" width="100"/>
        <el-table-column prop="ybylfwf" label="一般医疗服务费" align="center" width="100"/>
        <el-table-column prop="ybylczf" label="一般治疗操作费" align="center" width="100"/>
        <el-table-column prop="hlfzyf" label="护理费住院费" align="center" width="100"/>
        <el-table-column prop="qtfy" label="其他费用" align="center" width="100"/>
        <el-table-column prop="blzdf" label="病理诊断费" align="center" width="100"/>
        <el-table-column prop="syszdf" label="实验室诊断费" align="center" width="100"/>
        <el-table-column prop="yxxzdf" label="影像学诊断费" align="center" width="100"/>
        <el-table-column prop="lczdxmf" label="临床诊断项目费" align="center" width="100"/>
        <el-table-column prop="fsszlxmf" label="非手术治疗项目费" align="center" width="100"/>
        <el-table-column prop="lcwlzlf" label="临床物理治疗费" align="center" width="100"/>
        <el-table-column prop="sszlf" label="手术治疗费" align="center" width="100"/>
        <el-table-column prop="mzf" label="麻醉费" align="center" width="100"/>
        <el-table-column prop="ssf" label="手术费" align="center" width="100"/>
        <el-table-column prop="kff" label="康复费" align="center" width="100"/>
        <el-table-column prop="zyzlf" label="中医治疗费" align="center" width="100"/>
        <el-table-column prop="xyf" label="西药费" align="center" width="100"/>
        <el-table-column prop="kjywf" label="抗菌药物费" align="center" width="100"/>
        <el-table-column prop="zcyf" label="中成药费" align="center" width="100"/>
        <el-table-column prop="zcyf1" label="中草药费" align="center" width="100"/>
        <el-table-column prop="xf" label="血费" align="center" width="100"/>
        <el-table-column prop="bdblzpf" label="白蛋白类制品费" align="center" width="100"/>
        <el-table-column prop="qdblzpf" label="球蛋白类制品费" align="center" width="100"/>
        <el-table-column prop="nxyzlzpf" label="凝血因子类制品费" align="center" width="100"/>
        <el-table-column prop="xbyzlzpf" label="细胞因子类制品费" align="center" width="100"/>
        <el-table-column prop="jcyycxyyclf" label="检查用一次性医用材料费" align="center" width="100"/>
        <el-table-column prop="zcyycxyyclf" label="治疗用一次性医用材料费" align="center" width="100"/>
        <el-table-column prop="ssyycxyyclf" label="手术用一次性医用材料费" align="center" width="100"/>
        <el-table-column prop="qtf" label="其他费" align="center" width="100"/>
        <el-table-column prop="tczf" label="统筹支付" align="center" width="100"/>
        <el-table-column prop="drgzf" label="DRG支付" align="center" width="100"/>
        <el-table-column prop="ylfkfs" label="医疗付款方式" align="center" width="100"/>
        <el-table-column prop="zfy" label="总费用" align="center" width="100"/>
        <el-table-column prop="zyk" label="总盈亏" align="center" width="100"/>
      </el-table>
    </div>
    <div>

    </div>
  </div>
</template>

<script>
import { getYkjl } from '../../../api/tjfx/ykjl'

export default {
  name: 'Ykjl',
  data(){
    return{
      dataList: [],
      loading: false
    }
  },
  created() {
  },
  methods:{
    async getData(bah){
      this.loading = true
      const res = await getYkjl({ bah: bah })
      this.dataList = res.rows
      this.loading = false
    }
  },
  props:{
    rowData: {
      type: String,
      default: ''
    }
  },
  watch: {
    rowData: {
      handler(newVal) {
        this.getData(newVal)
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style scoped lang="scss">

</style>
