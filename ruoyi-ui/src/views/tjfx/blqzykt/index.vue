<template>
  <div>
    <el-card>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="日期类型">
          <el-select v-model="queryParams.datetype" placeholder="日期类型" clearable>
            <el-option v-for="item in datetypeList" :value="item.value" :label="item.name">{{item.name}}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="daterangeDate"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="科室">
          <el-select v-model="queryParams.cykb" placeholder="科室" clearable>
            <el-option v-for="item in deptList" :value="item.cykb">{{item.cykb}}</el-option>
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="参保类别">
          <el-select v-model="queryParams.cblb" placeholder="参保类别" clearable>
            <el-option v-for="item in cblbList" :value="item">{{item}}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="handleChangeData">
            切换为科室数据图
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card  id="pdfDom" style="margin: 2%;">
      <div id="main" style="width: 1300px; height: 450px;margin-left: -40px;"></div>
<!--      <div id="ksChart" style="width: 1300px; height: 450px; margin-left: -40px"></div>-->
    </el-card>


    <el-card  style="margin: 2%;" >
      <span>病组类型：</span>
      <el-radio-group v-model="bzlx" @change="setBzlx">
        <el-radio-button label="全部" >
          <span style="font-weight: bolder;">全部</span>
        </el-radio-button>
        <el-radio-button label="战略病组" >
          <el-tooltip content="权重高，盈亏为正" placement="top">
            <span style="color:navy;font-weight: bolder;" >战略病组</span>
          </el-tooltip>
        </el-radio-button>
        <el-radio-button label="关注病组" >
          <el-tooltip content="权重高，盈亏为负" placement="top">
          <span style="color:#ffba00;font-weight: bolder;">关注病组</span>
          </el-tooltip>
        </el-radio-button>
        <el-radio-button label="优势病组" >
          <el-tooltip content="权重低，盈亏为正" placement="top">
          <span style="color:#13ce66;font-weight: bolder;">优势病组</span>
          </el-tooltip>
        </el-radio-button>
        <el-radio-button label="劣势病组" >
          <el-tooltip content="权重低，盈亏为负" placement="top">
          <span style="color:red;font-weight: bolder;">劣势病组</span>
          </el-tooltip>
        </el-radio-button>
      </el-radio-group>
      <el-button style="margin-left: 10px" icon="el-icon-download" type="warning" size="small" @click="exportInfo">
        导出
      </el-button>
    </el-card>

    <el-table style="margin-left: 2%;" v-loading="loading" :data="ykxxListCopy">
      <el-table-column label="DRG分组" align="center" prop="drgbh"  width="170px" >
        <template slot-scope="scope">
          <span class="bzlx" v-if="scope.row.zfqz>avgCMI && scope.row.zyk>0" style="color:navy">{{scope.row.drgbh}}</span>
          <span class="bzlx" v-if="scope.row.zfqz>avgCMI && scope.row.zyk<=0" style="color:#ffba00;">{{scope.row.drgbh}}</span>
          <span class="bzlx" v-if="scope.row.zfqz<=avgCMI && scope.row.zyk>0" style="color:#13ce66;">{{scope.row.drgbh}}</span>
          <span class="bzlx" v-if="scope.row.zfqz<=avgCMI && scope.row.zyk<=0" style="color:red;">{{scope.row.drgbh}}</span>
        </template>
      </el-table-column>
      <el-table-column label="DRG名称" align="center" prop="drgmc" width="500px" />
      <el-table-column label="平均住院天数" align="center" prop="zyts" width="150px" />
      <el-table-column label="支付权重" align="center" prop="zfqz" width="150px" />
      <el-table-column label="总盈亏" align="center" prop="zyk" width="150px" />
      <el-table-column label="例均费用" align="center" prop="ljfy" width="150px" />
      <el-table-column label="人数" align="center" prop="rs" width="150px"/>
    </el-table>

  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getBlqzYkxx, getAvgCMI, getKsqzYkxx } from '@/api/tjfx/tjfx'
import {getDeptList} from "@/api/drg/syjl";
import getPdf from "@/utils/htmlToPdf"

export default {
  data() {
    return {
      bzlx:"全部",
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据图显示病组或是科室，false为病组，true为科室
      isDeptData: false,
      // 病例权重-盈亏图表格数据
      ykxxList: [],
      ykxxListCopy: [],
      ksykxxList: [],
      deptList: [],
      cblbList: ["职工","居民","自费","生育"],
      datetypeList: [
        {name:"出院时间", value:"cydate"},
        {name:"结算时间", value:"jsdate"}
      ],
      avgCMI:0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期类别时间范围
      daterangeDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        drgbh: null,
        drgmc: null,
        zyts: null,
        zfqz: null,
        zyk: null,
        rs: null,
        bz: null,
        date: null,
        cykb: null,
        cblb: null,
        datetype: null
      },
      // 表单参数
      form: {},
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
     this.queryParams.datetype="jsdate";
     var now   = new Date();
     var monthn = now.getMonth()+1;
     var yearn  = now.getFullYear();
     var dayn = now.getDate();
     // var dayn2 = now.getDate()-1;
     // var h = now.getHours();
     // var m =now.getMinutes();
     // var s = now.getSeconds();
     this.daterangeDate[0] = yearn+"-"+monthn+"-"+"01";
     this.daterangeDate[1] = yearn+"-"+monthn+"-"+dayn;
      getAvgCMI().then(res=>{
        this.avgCMI = res
      })
      getDeptList().then(res => {
        this.deptList = res.rows
      })
      this.getList();
    },
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDate && '' != this.daterangeDate) {
        this.queryParams.params["beginDate"] = this.daterangeDate[0];
        this.queryParams.params["endDate"] = this.daterangeDate[1];
      }
      getBlqzYkxx(this.queryParams).then(response => {
        this.ykxxList = response.rows;
        this.ykxxListCopy = JSON.parse(JSON.stringify(this.ykxxList))
        this.total = response.total;
        this.loading = false;
        if (this.avgCMI == null || this.avgCMI == "" || this.avgCMI == 0) {
          getAvgCMI().then(res=>{
            this.avgCMI = res
            this.initChart()
          })
        } else {
          this.initChart()
        }
        getKsqzYkxx(this.queryParams.params).then(res => {
          this.ksykxxList = res.rows
        })
      });
    },
    reset() {
      this.form = {
        drgbh: null,
        drgmc: null,
        zyts: null,
        zfqz: null,
        zyk: null,
        rs: null,
        bz: null,
        date: null,
        cykb: null,
        cblb: null,
        datetype: null
      };
      this.resetForm("form");
    },
    handleQuery() {

      if (this.queryParams.datetype == null && this.daterangeDate.length > 0) {
        this.$modal.alertWarning("请选择日期类型")
        return
      }

      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.bzlx = "全部"
      this.daterangeDate = [];
      this.resetForm("queryForm");
      this.queryParams.cykb = null
      this.handleQuery();
    },
    setBzlx() {
      if (this.bzlx == "战略病组") {
        this.ykxxListCopy = this.ykxxList.filter(item => item.zfqz > this.avgCMI && item.zyk > 0);
      } else if (this.bzlx == "优势病组") {
        this.ykxxListCopy = this.ykxxList.filter(item => item.zfqz < this.avgCMI && item.zyk > 0);
      } else if (this.bzlx == "关注病组") {
        this.ykxxListCopy = this.ykxxList.filter(item => item.zfqz > this.avgCMI && item.zyk < 0);
      } else if (this.bzlx == "劣势病组") {
        this.ykxxListCopy = this.ykxxList.filter(item => item.zfqz < this.avgCMI && item.zyk < 0);
      } else {
        this.ykxxListCopy = this.ykxxList
      }
    },
    initChart() {
      const chartDom = document.getElementById('main')
      // console.log(chartDom);
      const myChart = echarts.init(chartDom)
      const option = {
        grid: {
          left: '3%',  // 控制数值显示位置
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          name: "CMI值",
          type: 'value',
          axisLine: {
            show: true  // 隐藏 x 轴线，但保留数据
          }
        },
        yAxis: {
          name: "盈亏",
          type: 'value',
          axisLine: {
            show: false  // 隐藏 x 轴线，但保留数据
          }
        },
        tooltip: {
        },
        dataZoom: [
          {   // 第一个 dataZoom 组件
            startValue: '0',      // 起始位置
            endValue: '3'          // 结束位置
          }
        ],
        series: [
          {
            symbol: 'circle',
            symbolSize: 20,
            data: this.handleYkxx(),
            type: 'scatter',
            markLine: {
              data: [
                { xAxis: this.avgCMI } ,
                { yAxis: 0 },
              ],
              lineStyle: {
                color: '#000',
                type: 'solid'
              }
            },
            itemStyle: {
              opacity: 1,
              symbolSize: 2,
              color: '#ccc',
            },
            label: {
              show: true,
              formatter: function (param) {
                return param.data.name;
              },
              textStyle: {
                color: 'red',
                fontSize: 7,
                fontWeight: 'bold'
              }
            },
          },
        ],
      }
      myChart.setOption(option)
    },
    handleExport() {
      getPdf("病组权重-盈亏图",)
    },
    handleYkxx(){
      if(!this.isDeptData) {
        return this.ykxxList.map(item => ({
          value: [item.zfqz, item.zyk],
          name: item.drgbh
        }))
      }else {
        return this.ksykxxList.map(item => ({
          value: [item.zfqz, item.zyk],
          name: item.cykb
        }))
      }
    },
    handleChangeData(){
      this.isDeptData = !this.isDeptData
      console.log(this.isDeptData)
      this.initChart()
    },
    exportInfo(){
      this.download('/tjfx/tjfx/export/getBlqzYkxx', {
        ...this.queryParams
      }, `病组盈亏明细_${new Date().getTime()}.xlsx`)
    },
  }
}
</script>
