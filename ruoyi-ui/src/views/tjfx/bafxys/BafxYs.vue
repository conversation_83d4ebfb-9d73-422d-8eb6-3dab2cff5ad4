<template>
  <div class="app-container" style="height: 400px">
    <div>
      <el-table :data="dataList" :border="true" v-loading="loading">
        <el-table-column prop="bah" label="病案号" align="center" width="100"/>
<!--        <el-table-column prop="brbs" label="病人标识" align="center" width="100"/>-->
        <el-table-column prop="xm" label="患者姓名" align="center" width="100"/>
        <el-table-column prop="rysj" label="入院时间" align="center" width="100"/>
        <el-table-column prop="cykb" label="出院科别" align="center" width="100"/>
        <el-table-column prop="zyzd" label="主要诊断" align="center" width="100"/>
        <el-table-column prop="cysj" label="出院时间" align="center" width="100"/>
        <el-table-column prop="ssjczmc1" label="手术名称" align="center" width="100"/>
        <el-table-column prop="zyys" label="住院医师" align="center" width="100"/>
        <el-table-column prop="zfy" label="总费用" align="center" width="100"/>
        <el-table-column prop="jsdate" label="结算时间" align="center" width="100"/>
        <el-table-column prop="drgbh" label="drg编号" align="center" width="100"/>
        <el-table-column prop="sjzyts" label="住院天数" align="center" width="100"/>
        <el-table-column prop="zfbz" label="支付标准" align="center" width="100"/>
        <el-table-column prop="bgwcbl" label="标杆完成比例" align="center" width="100"/>
        <el-table-column prop="drgmc" label="drg名称" align="center" width="100"/>
        <el-table-column prop="ybbl" label="药品比例" align="center" width="100"/>
        <el-table-column prop="hcbl" label="耗材比例" align="center" width="100"/>
        <el-table-column prop="ykje" label="盈亏金额" align="center" width="100"/>
        <el-table-column prop="sjxhzs" label="时间消耗指数" align="center" width="100"/>
      </el-table>
    </div>
    <div>

    </div>
  </div>
</template>

<script>
export default {
  // 病案分析-按医生
  name: 'BafxYs',
  data(){
    return {
      loading : false,
      dataList: []
    }
  },
  props: {
    queryData: {
      deep: true,
      immediate: true,
      handler(newVal) {
        if(newVal) {
          this.fetchData = newVal
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>
