<script>
import {
  listYbgkWgjlHistory,
  getYbgkWgjlHistory,
  delYbgkWgjlHistory,
  updateYbgkWgjlHistory,
  removeYbgkWgjlHistory
} from '@/api/tjfx/ybgkwgjlHistory'
import { listDept } from "@/api/system/dept";
import { listUser } from "@/api/system/user";
import { parseTime } from '../../../utils/ruoyi'

export default {
  name: 'WgjlHistory',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 违规历史记录表格数据
      wgjlHistoryList: [],
      // 汇总数据
      summaryData: {
        processed: 0,
        unprocessed: 0,
        total: 0
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 部门树选项
      deptOptions: [],
      // 用户选项
      userOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: null,
        endTime: null,
        statDimension: 'detail', // 默认为明细
        kdksname: null,
        doctorname: null,
        status: null,
        jltype: '3'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
    this.getDeptList();
    this.getUserList();
  },
  methods: {
    parseTime,
    /** 查询违规历史记录列表 */
    getList() {
      this.loading = true;
      // console.log(this.dateRange);
      listYbgkWgjlHistory({
        ...this.queryParams,
        startDate: this.dateRange && this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : null,
        endDate: this.dateRange && this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : null,
      }).then(response => {
        this.wgjlHistoryList = response.rows;
        this.total = response.total;
        this.loading = false;
        // 获取汇总数据
        // this.getSummaryData();
      });
    },
    /** 查询部门下拉树结构 */
    getDeptList() {
      listDept().then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 查询用户列表 */
    getUserList() {
      listUser().then(response => {
        this.userOptions = response.rows;
      });
    },
    /** 获取汇总数据 */
    getSummaryData() {
      const params = { ...this.queryParams };
      params.pageSize = 999999; // 获取所有数据进行统计
      params.pageNum = 1;

      listYbgkWgjlHistory(params).then(response => {
        const data = response.rows;

        // 计算已处理、未处理和总数
        this.summaryData.processed = data.filter(item => item.status === 1).length;
        this.summaryData.unprocessed = data.filter(item => item.status === 0).length;
        this.summaryData.total = data.length;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        status: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 处理违规记录 */
    handleProcess(row) {
      const wgjlHistory = { id: row.id, status: 1 };
      updateYbgkWgjlHistory(wgjlHistory).then(response => {
        this.$modal.msgSuccess("处理成功");
        this.getList();
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/ybgkWgjlHistory/export', {
        ...this.queryParams
      }, `违规历史记录_${new Date().getTime()}.xlsx`);
    },
    remove(row){
      let ids = []
      ids.push(row.id);
      removeYbgkWgjlHistory(...ids).then(response => {
        this.$modal.msgSuccess("操作成功")
        this.getList();
      })
    }
  }
};
</script>

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
<!--      <el-form-item label="统计维度" prop="statDimension">-->
<!--        <el-select v-model="queryParams.statDimension" placeholder="请选择统计维度" clearable>-->
<!--          <el-option value="dept" label="科室"></el-option>-->
<!--          <el-option value="doctor" label="医生"></el-option>-->
<!--          <el-option value="detail" label="明细"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="科室" prop="kdksname">
        <el-select v-model="queryParams.kdksname" placeholder="请选择科室" clearable>
          <el-option
            v-for="dept in deptOptions"
            :key="dept.deptId"
            :label="dept.deptName"
            :value="dept.deptId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排除类型" prop="jltype">
        <el-select v-model="queryParams.jltype" placeholder="请选择排除类型" clearable>
          <el-option key="3" value="3" label="医保排除"/>
          <el-option key="2" value="2" label="临床排除"/>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="医生" prop="doctorname">-->
<!--        <el-select v-model="queryParams.doctorname" placeholder="请选择医生" clearable>-->
<!--          <el-option-->
<!--            v-for="user in userOptions"-->
<!--            :key="user.userId"-->
<!--            :label="user.userName"-->
<!--            :value="user.userId"-->
<!--          ></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="处理状态" prop="status">-->
<!--        <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable>-->
<!--          <el-option value="0" label="未处理"></el-option>-->
<!--          <el-option value="1" label="已处理"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 汇总统计卡片 -->
<!--    <div class="summary-container">-->
<!--      <el-card class="summary-card" shadow="hover">-->
<!--        <div class="summary-title">总违规数</div>-->
<!--        <div class="summary-value">{{ summaryData.total }}</div>-->
<!--      </el-card>-->
<!--      <el-card class="summary-card" shadow="hover">-->
<!--        <div class="summary-title">已处理</div>-->
<!--        <div class="summary-value">{{ summaryData.processed }}</div>-->
<!--      </el-card>-->
<!--      <el-card class="summary-card" shadow="hover">-->
<!--        <div class="summary-title">未处理</div>-->
<!--        <div class="summary-value">{{ summaryData.unprocessed }}</div>-->
<!--      </el-card>-->
<!--    </div>-->

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:ybgkWgjlHistory:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 违规历史记录表格 -->
    <el-table v-loading="loading" :data="wgjlHistoryList" @selection-change="selection => ids = selection.map(item => item.id)">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" v-if="false" />
<!--      <el-table-column label="病人ID" align="center" prop="brid" />-->
<!--      <el-table-column label="住院ID" align="center" prop="zyid" />-->
      <el-table-column label="病人姓名" align="center" prop="brname"/>
      <el-table-column label="病案号" align="center" prop="zyh" />
      <el-table-column label="科室" align="center" prop="kdksname" />
      <el-table-column label="医生" align="center" prop="doctorname" />
      <el-table-column label="违规内容" align="center" prop="jklog" min-width="300px" :show-overflow-tooltip="true" />
      <el-table-column label="违规时间" align="center" prop="createDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="处理状态" align="center" prop="status">-->
<!--        <template slot-scope="scope">-->
<!--          <el-tag type="success" v-if="scope.row.status === 1">已处理</el-tag>-->
<!--          <el-tag type="danger" v-else>未处理</el-tag>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-check"-->
<!--            @click="handleProcess(scope.row)"-->
<!--            v-if="scope.row.status === 0"-->
<!--            v-hasPermi="['system:ybgkWgjlHistory:edit']"-->
<!--          >标记为已处理</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-view"-->
<!--            @click="handleProcess(scope.row)"-->
<!--            v-hasPermi="['system:ybgkWgjlHistory:query']"-->
<!--          >查看详情</el-button>-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="remove(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<style scoped lang="scss">
.summary-container {
  display: flex;
  margin-bottom: 20px;

  .summary-card {
    flex: 1;
    margin-right: 20px;
    text-align: center;

    &:last-child {
      margin-right: 0;
    }

    .summary-title {
      font-size: 16px;
      color: #606266;
      margin-bottom: 10px;
    }

    .summary-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
    }
  }
}

.el-tag {
  &.el-tag--success {
    background-color: #f0f9eb;
  }

  &.el-tag--danger {
    background-color: #fef0f0;
  }
}
</style>
