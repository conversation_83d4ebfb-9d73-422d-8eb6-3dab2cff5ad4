<template>
  <div v-loading="loading">
    <el-collapse style="margin: 15px">
      <el-collapse-item title="查询" name="1">
        <div>
          <el-form
            style="margin-top: 20px; margin-left: 1%; width: 98%;"
            :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

            <el-form-item label="DRG病组">
              <el-select v-model="queryParams.drgbh"
                         @focus="resetDept"
                         filterable
                         :filter-method="drgFilter"
                         @change="getZdSsList"
                         placeholder="DRG病组"
                         style="width: 1000px;"
                         clearable
              >
                <el-option v-for="(item,index) in drgList" :value="item.drgbh" :key="index"
                           :label="item.drgmc + '[' + item.drgbh + ']' ">{{ item.drgmc }}[{{ item.drgbh }}]
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="日期类型">
              <el-select v-model="queryParams.dateType" placeholder="日期类型" clearable>
                <el-option v-for="item in dateTypeList" :value="item.value" :label="item.name">{{
                    item.name
                  }}
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="日期范围">
              <el-date-picker
                v-model="queryParams.adtFrom"
                type="datetime"
                placeholder="请选择开始日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
              -
              <el-date-picker
                v-model="queryParams.adtTo"
                type="datetime"
                placeholder="请选择截至日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="参保类别">
              <el-select multiple v-model="cblb" placeholder="参保类别" clearable>
                <el-option v-for="item in cblbList" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>

            <el-form-item label="科室">
              <el-select @change="getDoctorByDept" v-model="queryParams.deptName" placeholder="科室" clearable>
                <el-option v-for="item in deptList" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>

            <el-form-item label="医生">
              <el-select v-model="queryParams.zyys" placeholder="医生" clearable>
                <el-option v-for="item in doctorList" :label="item" :value="item"/>
              </el-select>
            </el-form-item>

            <el-form-item label-width="130px" label="是否检索入院时间">
              <el-select v-model="queryParams.isCheckRy" placeholder="是否检索入院时间" clearable>
                <el-option v-for="item in isCheckRyOptions" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>

            <el-form-item label="入院时间">
              <el-date-picker
                v-model="queryParams.adtFromRy"
                type="datetime"
                placeholder="请选择开始日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
              -
              <el-date-picker
                v-model="queryParams.adtToRy"
                type="datetime"
                placeholder="请选择截至日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="清算方式">
              <el-select v-model="queryParams.qsfs" placeholder="清算方式" clearable>
                <el-option v-for="item in qsfsOptions" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>

            <el-form-item label="是否盈利">
              <el-select v-model="queryParams.ykflag" placeholder="是否盈利" clearable>
                <el-option v-for="item in ykflag" :label="item.name" :value="item.value"/>
              </el-select>
            </el-form-item>

            <el-form-item label="主要诊断">
              <el-select style="width: 300px" v-model="queryParams.zd" placeholder="主要诊断" clearable>
                <el-option v-for="item in zdList" :label="item.zyzd" :value="item.zyzd"/>
              </el-select>
            </el-form-item>

            <el-form-item label="主要手术">
              <el-select style="width: 300px" v-model="queryParams.ss" placeholder="主要手术" clearable>
                <el-option v-for="item in ssList" :label="item.ssjczmc1" :value="item.ssjczmc1"/>
              </el-select>
            </el-form-item>


            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>

          </el-form>
        </div>
      </el-collapse-item>
    </el-collapse>


    <div style="width: 100%; padding: 0 1%">
      <div style="width: 100%; height: 300px; margin-top: 20px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-card style="width: 100%; height: 100px;">
              <br>
              <el-row :gutter="24">
                <el-col  :span="8">
                  <div>
                    <el-statistic title="医疗总费用（万元）">
                      <template slot="formatter">
                        <div style="color: #1c84c6">{{ groupMainInfo.zfy ? groupMainInfo.zfy : 0.00 }}</div>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>
                    <el-statistic title="总权重（指数）">
                      <template slot="formatter">
                        <div style="color: #4AB7BD">{{ groupMainInfo.zqz ? groupMainInfo.zqz : 0.00 }}</div>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div>
                    <el-statistic title="总盈亏（万元）">
                      <template slot="formatter">
                        <div style="color: #1ab394">{{ groupMainInfo.zyk ? groupMainInfo.zyk : 0.00 }}</div>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
              </el-row>
            </el-card>
            <el-card>
              <div style="width: 100%; height: 200px;">
                <br>
                <br>
                <el-row :gutter="24">
                  <el-col :span="6">
                    <div>
                      <el-statistic title="出院病例数">
                        <template slot="formatter">
                          {{ groupMainInfo.cybls ? groupMainInfo.cybls : 0.00  }}
                        </template>
                      </el-statistic>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div>
                      <el-statistic title="人数占比">
                        <template slot="formatter">
                          {{ groupMainInfo.rcrqb ? groupMainInfo.rcrqb : 0.00  }}
                        </template>
                      </el-statistic>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div>
                      <el-statistic title="死亡率（%）">
                        <template slot="formatter">
                          {{ groupMainInfo.swl ? groupMainInfo.swl : 0.00  }}
                        </template>
                      </el-statistic>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div>
                      <el-statistic title="病案数">
                        <template slot="formatter">
                          <div style="color: #0080ff">{{ groupMainInfo.bas ? groupMainInfo.bas : 0.00 }}</div>
                        </template>
                      </el-statistic>
                    </div>
                  </el-col>
                </el-row>
                <br>
                <br>
                <el-row :gutter="24">
                  <el-col :span="6">
                    <div>
                      <el-statistic title="次均费用（万元）">
                        <template slot="formatter">
                          {{ groupMainInfo.cjfy ? groupMainInfo.cjfy : 0.00  }}
                        </template>
                      </el-statistic>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div>
                      <el-statistic title="时间消耗指数">
                        <template slot="formatter">
                          {{ groupMainInfo.sjxhzs ? groupMainInfo.sjxhzs : 0.00  }}
                        </template>
                      </el-statistic>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div>
                      <el-statistic title="费用消耗指数">
                        <template slot="formatter">
                          {{ groupMainInfo.fyxhzs ? groupMainInfo.fyxhzs : 0.00  }}
                        </template>
                      </el-statistic>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card>
              <div id="fyjg-chart" style="width: 100%; height: 300px;"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <el-card style="width: 100%; height: 400px; margin-top: 50px">
        <el-row :gutter="24">
          <el-col :span="5">
            <div>
              <div id="czjy-chart" style="width: 100%;height: 400px"></div>
            </div>
          </el-col>
          <el-col :span="6">
            <div>
              <div id="jy-chart" style="width: 100%;height: 400px;"></div>
            </div>
          </el-col>
          <el-col :span="6">
            <div>
              <div id="cz-chart" style="width: 100%;height: 400px;"></div>
            </div>
          </el-col>
          <el-col :span="6">
            <div>
              <div id="bg-chart" style="width: 100%;height: 400px;"></div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <el-card style="width: 100%; height: 500px; margin-top: 20px;">
        <el-row :gutter="24">
          <el-col :span="14">
            <div>
              <el-button style="margin: 10px 10px" type="warning" icon="el-icon-download" size="mini" @click="exportKsmx">
                导出
              </el-button>
              <el-tabs v-model="activeNameKsmx">
                <el-tab-pane name="亏损明细" label="亏损明细">
                  <el-table border :data="doctorDataListKsmx" height="350">
                    <el-table-column width="120px" show-overflow-tooltip label="科室" align="left" prop="cykb"/>
                    <el-table-column show-overflow-tooltip label="医生" align="left" prop="zyys"/>
                    <el-table-column show-overflow-tooltip label="DRG编码" align="left" prop="drgbh"/>
                    <el-table-column width="120px" show-overflow-tooltip label="DRG名称" align="left" prop="drgmc"/>
                    <el-table-column width="120px" show-overflow-tooltip label="住院号" align="left" prop="zyh"/>
                    <el-table-column width="120px" show-overflow-tooltip label="类型" align="left" prop="type"/>
                    <el-table-column width="450px" show-overflow-tooltip label="亏损详情" align="left" prop="details"/>
                    <el-table-column width="200px" show-overflow-tooltip label="项目名称" align="left" prop="xmmc"/>
                    <el-table-column width="100px" show-overflow-tooltip label="该病人费用" align="left" prop="brfy"/>
                    <el-table-column width="160px" show-overflow-tooltip label="其他病人平均费用" align="left" prop="pjfy"/>
                  </el-table>
                </el-tab-pane>
                <el-tab-pane name="按科室" label="按科室">
                  <el-table border :data="deptDataListKsmx" height="350">
                    <el-table-column width="120px" show-overflow-tooltip label="科室" align="left" prop="cykb"/>
                    <el-table-column width="200px" show-overflow-tooltip label="项目名称" align="left" prop="xmmc"/>
                    <el-table-column width="120px" show-overflow-tooltip label="类型" align="left" prop="type"/>
                    <el-table-column show-overflow-tooltip label="DRG编码" align="left" prop="drgbh"/>
                    <el-table-column width="120px" show-overflow-tooltip label="DRG名称" align="left" prop="drgmc"/>
                    <el-table-column width="100px" show-overflow-tooltip label="该科室病人平均费用" align="left" prop="brfy"/>
                    <el-table-column width="160px" show-overflow-tooltip label="其他病人平均费用" align="left" prop="pjfy"/>
                  </el-table>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>
          <el-col :span="10">
            <div>
              <div id="ksxm-chart" style="width: 100%;height: 450px"></div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <el-card style="width: 100%; height: 400px; margin-top: 20px; margin-bottom: 20px;">
        <el-radio-group v-model="activeName" @change="changeDeptOrDoctor">
          <el-radio-button label="同病组不同科室">
            <span>同病组不同科室</span>
          </el-radio-button>
          <el-radio-button label="同病组不同医生">
            <span>同病组不同医生</span>
          </el-radio-button>
        </el-radio-group>

        <el-button style="margin-left: 10px" type="warning" icon="el-icon-download" size="mini" @click="handleExport">
          导出
        </el-button>

        <el-table :border="true" :data="deptOrDoctorList" height="350">
          <el-table-column show-overflow-tooltip label="序号" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="病组编码" align="left" prop="drgbh"/>
          <el-table-column width="200px" show-overflow-tooltip label="名称" align="left" prop="drgmc"/>
          <el-table-column width="200px" v-if="activeName=='同病组不同科室'" show-overflow-tooltip label="科室"
                           align="left"
                           prop="cykb"/>
          <el-table-column width="200px" v-if="activeName=='同病组不同医生'" show-overflow-tooltip label="医生"
                           align="left"
                           prop="zyys"/>
          <el-table-column width="120px" show-overflow-tooltip label="病例数" align="left" prop="bls"/>
          <el-table-column width="120px" show-overflow-tooltip label="总费用" align="left" prop="zfy"/>
          <el-table-column width="120px" show-overflow-tooltip label="超支/结余" align="left" prop="zyk"/>
          <el-table-column width="120px" show-overflow-tooltip label="次均费用" align="left" prop="cjfy"/>
          <el-table-column width="120px" show-overflow-tooltip label="平均住院日" align="left" prop="pjzyr"/>
          <el-table-column width="120px" show-overflow-tooltip label="药占比" align="left" prop="yzb"/>
          <el-table-column width="120px" show-overflow-tooltip label="材占比" align="left" prop="czb"/>
          <el-table-column width="120px" show-overflow-tooltip label="平均药品费" align="left" prop="ywf">
            <template slot-scope="{ row }">
              <div :class="row.ywf > row.ypfbg ? 'red-text' : 'black-text'">
                {{ row.ywf }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120px" show-overflow-tooltip label="药品标杆" align="left" prop="ypfbg"/>
          <el-table-column width="120px" show-overflow-tooltip label="平均治疗费" align="left" prop="zlf">
            <template slot-scope="{ row }">
              <div :class="row.zlf > row.zlfbg ? 'red-text' : 'black-text'">
                {{ row.zlf }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120px" show-overflow-tooltip label="治疗标杆" align="left" prop="zlfbg"/>
          <el-table-column width="120px" show-overflow-tooltip label="平均耗材费" align="left" prop="hcf">
            <template slot-scope="{ row }">
              <div :class="row.hcf > row.hcfbg ? 'red-text' : 'black-text'">
                {{ row.hcf }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120px" show-overflow-tooltip label="耗材标杆" align="left" prop="hcfbg"/>
          <el-table-column width="120px" show-overflow-tooltip label="平均检查费" align="left" prop="jcf">
            <template slot-scope="{ row }">
              <div :class="row.jcf > row.jcfbg ? 'red-text' : 'black-text'">
                {{ row.jcf }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120px" show-overflow-tooltip label="检查标杆" align="left" prop="jcfbg"/>
          <el-table-column width="120px" show-overflow-tooltip label="平均检验费" align="left" prop="jyf">
            <template slot-scope="{ row }">
              <div :class="row.jyf > row.jyfbg ? 'red-text' : 'black-text'">
                {{ row.jyf }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120px" show-overflow-tooltip label="检验标杆" align="left" prop="jyfbg"/>
          <el-table-column width="120px" show-overflow-tooltip label="平均其他费" align="left" prop="qtf">
            <template slot-scope="{ row }">
              <div :class="row.qtf > row.qtfbg ? 'red-text' : 'black-text'">
                {{ row.qtf }}
              </div>
            </template>
          </el-table-column>
          <el-table-column width="120px" show-overflow-tooltip label="其他标杆" align="left" prop="qtfbg"/>
        </el-table>
      </el-card>
    </div>


  </div>
</template>

<script>
import * as echarts from 'echarts'
import {drgdictByDrgbh, listDrgdict2} from "@/api/drg/drgdict"
import {
  listBzblByDept,
  listBzblByDoctor,
  listBzczblfyjg,
  listBzczjy,
  listBzfyjg,
  listBzjyblfyjg,
  listBzztfx,
  listBzKsmx,
  listBzKsxmpm, listBzKsmxDept
} from "@/api/tjfx/bzztfx";
import PinYinMatch from "pinyin-match";
import {getDeptDict} from "@/api/system/dept";
import {selectDoctorByHDeptName} from "@/api/system/user";
import {selectSsListByDrg, selectZdListByDrg} from "@/api/drg/syjl";
import {checkRoleItem} from "@/utils/permission";
import {getUserDeptList} from "@/api/system/hdept";


export default {
  name: "index",
  data() {
    return {
      userDept: [],
      userName: null,
      cblb: null,
      flag: null,
      deptName: null,
      loading: false,
      selectDrg: '',
      activeName: '同病组不同科室',
      activeNameKsmx: '亏损明细',
      deptOrDoctorList: [],
      deptDataList: [],
      doctorDataList: [],
      cblbList: [
        {name: "所有", value: "%"},
        {name: "职工", value: "职工"},
        {name: "居民", value: "居民"},
        {name: "自费", value: "自费"},
        {name: "生育", value: "生育"},
      ],
      deptList: [],
      doctorList: [],
      ssList: null,
      zdList: null,
      dateTypeList: [
        {name: "出院时间", value: "cydate"},
        {name: "结算时间", value: "jsdate"}
      ],
      ykflag: [
        {name: "是", value: "ying"},
        {name: "否", value: "kui"},
        {name: "平", value: "ping"}
      ],
      qsfsOptions: [
        {name: "DRG支付", value: "40"},
        {name: "按项目", value: "1"},
        {name: "单病种", value: "2"},
        {name: "按床日", value: "3"},
        {name: "按人头", value: "4"},
        {name: "其他", value: "9"}
      ],
      isCheckRyOptions: [
        {name: "否", value: "notcheck"},
        {name: "是", value: "check"},
      ],
      drgList: [],
      drgListby: [],
      queryParams: {
        drgbh: null,
        drgmc: null,
        cblb: null,
        dateType: null,
        adtFrom: null,
        adtTo: null,
        isCheckRy: null,
        deptName: null,
        deptList: null,
        adtFromRy: null,
        adtToRy: null,
        qsfs: null,
        ykflag: null,
        zyys: null,
        zd: null,
        ss: null,
      },
      groupMainInfo: {},
      bzfyjg: {},
      bzbllxfx: {},
      showSearch: true,
      czjy: {},
      ksmxList: [],
      deptDataListKsmx: [],
      doctorDataListKsmx: [],
    }
  },
  methods: {
    exportKsmx() {
      if (this.queryParams.drgbh) {
        this.download('tjfx/bzsy/exportKsmx', {
          ...this.queryParams
        }, `病组亏损明细_${new Date().getTime()}.xlsx`)
      } else {
        this.$modal.msgWarning("请选择要导出的病组")
      }
    },
    handleQuery() {
      console.log(this.flag)
      if (this.flag == 2) {
        if (!this.queryParams.zyys && this.doctorList) {
          this.queryParams.zyys = this.doctorList[0]
        }

        if (!this.queryParams.deptName && this.deptList.length > 0) {
          if (this.deptList.length == 1) {
            this.queryParams.deptName = this.deptList[0].value
            this.getDoctorByDept(this.queryParams.deptName)
          } else {
            this.queryParams.deptList = this.deptList.map(item => item.value)
          }
        }
      }


      if (this.cblb) {
        this.queryParams.cblb = this.cblb.join(',');
        if (this.queryParams.cblb.indexOf("%") > -1) {
          this.queryParams.cblb = null
        }
      }
      if ((this.queryParams.adtFrom || this.queryParams.adtFrom) && !this.queryParams.dateType) {
        this.queryParams.dateType = 'cydate'
      }
      this.getList();
    },
    async getList() {
      console.log(this.queryParams)
      if (this.queryParams.drgbh == null) {
        this.$modal.alertWarning("请选择病组")
        return
      }

      if (!this.queryParams.adtFrom && !this.queryParams.adtTo) {
        this.queryParams.dateType = null
      }

      this.loading = true
      const res = await listBzztfx(this.queryParams)
      if (res.code == 200) {
        if (res.total > 0) {
          this.groupMainInfo = res.rows[0]
          await this.initChartData()
          this.loading = false
        } else {
          this.$modal.alertWarning("该病组数据为空")
          this.clearMainData()
          this.loading = false
        }
      }
    },
    clearMainData() {
      echarts.init(document.getElementById('fyjg-chart')).clear()
      echarts.init(document.getElementById('czjy-chart')).clear()
      echarts.init(document.getElementById('jy-chart')).clear()
      echarts.init(document.getElementById('cz-chart')).clear()
      echarts.init(document.getElementById('bg-chart')).clear()
      echarts.init(document.getElementById('ksxm-chart')).clear()
      this.ksmxList = []
      this.deptOrDoctorList = []
      this.deptDataList = []
      this.deptDataListKsmx = []
      this.doctorDataList = []
      this.doctorDataListKsmx = []
      this.groupMainInfo = {}
    },
    resetQuery() {
      echarts.init(document.getElementById('fyjg-chart')).clear()
      echarts.init(document.getElementById('czjy-chart')).clear()
      echarts.init(document.getElementById('jy-chart')).clear()
      echarts.init(document.getElementById('cz-chart')).clear()
      echarts.init(document.getElementById('bg-chart')).clear()
      echarts.init(document.getElementById('ksxm-chart')).clear()
      this.ksmxList = []
      this.deptOrDoctorList = []
      this.deptDataList = []
      this.deptDataListKsmx = []
      this.doctorDataList = []
      this.doctorDataListKsmx = []
      this.groupMainInfo = {}
      this.cblb = null
      this.queryParams = {
        drgbh: null,
        drgmc: null,
        cblb: null,
        dateType: null,
        adtFrom: null,
        adtTo: null,
        isCheckRy: null,
        deptName: null,
        adtFromRy: null,
        adtToRy: null,
        qsfs: null,
      }
    },
    initChartData() {
      try {
        this.initCzjyChart();
        this.initCzChart();
        this.initJyChart();
        this.initBgChart();
        this.initFyjgChart();
        this.initDeptOrDoctorData();
        this.initKsmxList()
        this.initKsxmpmChart()
      } catch (e) {
        this.loading = false
      }
    },
    async initKsxmpmChart() {
      const res = await listBzKsxmpm(this.queryParams).catch(err => {
        this.$modal.msgError(err)
        this.loading = false
      })
      if (res.code == 200) {
        const ksxmpmList = res.rows
        const ksxmpmChartDom = document.getElementById('ksxm-chart')
        const ksxmpmChart = echarts.init(ksxmpmChartDom)
        const ksxmpmOption = {
          title: {
            text: '病组亏损项目',
            left: 'center',
            top: 'top',
            textStyle: {
              fontSize: 16,
              color: '#000000',
              fontFamily: '幼圆'
            }
          },
          xAxis: {
            type: 'category',
            data: ksxmpmList.map(item => item.xmmc),
            name: '项目',
            nameLocation: 'start',
            nameGap: 25,
            nameTextStyle: {
              fontSize: 12,
              fontFamily: '幼圆'
            },
            axisLabel: {
              fontSize: 10,
              fontFamily: '幼圆',
              formatter: function (value) {
                return value.match(/.{1,3}/g).join('\n');
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '超出金额',
            nameLocation: 'end',
            nameTextStyle: {
              fontSize: 12,
              fontFamily: '幼圆'
            },
            axisLabel: {
              fontSize: 12,
              fontFamily: 'Consolas'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function (params) {
              const data = params[0];
              return `项目名称: ${data.name}<br>超出金额: ${data.value}`;
            }
          },
          series: [
            {
              data: ksxmpmList.map(item => item.ccje),
              type: 'bar',
              showBackground: true,
              backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
              }
            }
          ]
        };
        ksxmpmChart.setOption(ksxmpmOption)
      }
    },
    async initKsmxList() {
      const res = await listBzKsmx(this.queryParams).catch(err => {
        this.$modal.msgError(err)
        this.loading = false
      })
      if (res.code == 200) {
        this.doctorDataListKsmx = res.rows
      }
      const deptRes = await listBzKsmxDept(this.queryParams).catch(err => {
        this.$modal.msgError(err)
        this.loading = false
      })
      if (deptRes.code == 200) {
        this.deptDataListKsmx = deptRes.rows
      }
    },
    changeDeptOrDoctor() {
      if (this.activeName == '同病组不同科室') {
        this.deptOrDoctorList = this.deptDataList
      } else if (this.activeName == '同病组不同医生') {
        this.deptOrDoctorList = this.doctorDataList
      }
    },
    handleExport() {
      if (this.queryParams.drgbh) {
        this.download('tjfx/bzsy/export', {
          ...this.queryParams
        }, `病组首页科室医生数据_${new Date().getTime()}.xlsx`)
      } else {
        this.$modal.msgWarning("请选择要导出的病组")
      }
    },
    drgFilter(val) {
      this.queryParams.drgbh = val
      if (val) {
        this.drgList = []
        var drgList = this.drgListby.filter((item) => {
          if (PinYinMatch.match(item.drgbh, val) || PinYinMatch.match(item.drgmc, val)) {
            return true
          }
        })
        this.drgList = drgList
      } else {
        this.drgList = this.drgListby
      }
    },
    resetDept() {
      this.drgList = this.drgListby
    },
    async initDeptOrDoctorData() {
      const doctorRes = await listBzblByDoctor(this.queryParams).catch(err => {
        this.$modal.msgError(err)
        this.loading = false
      })
      if (doctorRes.code == 200) {
        this.doctorDataList = doctorRes.rows
        if (this.activeName == '同病组不同医生') {
          this.deptOrDoctorList = this.doctorDataList
        }
      }
      const deptRes = await listBzblByDept(this.queryParams).catch(err => {
        this.$modal.msgError(err)
        this.loading = false
      })
      if (deptRes.code == 200) {
        this.deptDataList = deptRes.rows
        if (this.activeName == '同病组不同科室') {
          this.deptOrDoctorList = this.deptDataList
        }
      }
    },
    async initFyjgChart() {
      const res = await listBzfyjg(this.queryParams).catch(err => {
        this.$modal.msgError(err)
        this.loading = false
      })
      if (res.code == 200) {
        if (res.total > 0 && res.rows[0]) {
          this.bzfyjg = res.rows[0]
        } else {
          this.bzfyjg.zhylfwf = 0
          this.bzfyjg.kff = 0
          this.bzfyjg.zdf = 0
          this.bzfyjg.zlf = 0
          this.bzfyjg.ywf = 0
          this.bzfyjg.xyyxyzpf = 0
          this.bzfyjg.hcf = 0
          this.bzfyjg.zyf = 0
          this.bzfyjg.qtf = 0
        }
        let zfy = this.bzfyjg.zhylfwf + this.bzfyjg.kff + this.bzfyjg.zdf +
          this.bzfyjg.zlf + this.bzfyjg.ywf + this.bzfyjg.xyyxyzpf +
          this.bzfyjg.hcf + this.bzfyjg.zyf + this.bzfyjg.qtf
        const fyjgChartDom = document.getElementById('fyjg-chart')
        const fyjgChart = echarts.init(fyjgChartDom)
        const fyjgOption = {
          title: {
            text: '费用构成（万元）',
            left: 'center',
            top: 'top',
            textStyle: {
              fontSize: 16,
              color: '#000000',
              fontFamily: '幼圆'
            }
          },
          series: [
            {
              type: 'pie',
              radius: ['30%', '70%'],
              center: ['70%', '50%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'outside',
                formatter: function (param) {
                  return param
                },
                fontSize: 10
              },
              labelLine: {
                show: true
              },
              data: [
                {value: this.bzfyjg.zhylfwf, name: '综合医疗服务费'},
                {value: this.bzfyjg.kff, name: '康复费'},
                {value: this.bzfyjg.zdf, name: '诊断费'},
                {value: this.bzfyjg.zlf, name: '治疗费'},
                {value: this.bzfyjg.ywf, name: '药物费'},
                {value: this.bzfyjg.xyyxyzpf, name: '血液和血液制品费'},
                {value: this.bzfyjg.hcf, name: '耗材费'},
                {value: this.bzfyjg.zyf, name: '中医费'},
                {value: this.bzfyjg.qtf, name: '其他费用'},
              ]
            }
          ],
          legend: {
            data: ['综合医疗服务费', '康复费', '诊断费', '治疗费', '药物费', '血液和血液制品费', '耗材费', '中医费', '其他费用'],
            orient: 'vertical',
            left: 10,
            top: 10,
            formatter: function (name) {
              let data = fyjgOption.series[0].data;
              for (let i = 0; i < data.length; i++) {
                if (data[i].name === name) {
                  if (zfy == 0 || zfy == null) {
                    return `${name} : ${data[i].value}万元`;
                  }
                  return `${name} : ${data[i].value}万元 (${((data[i].value / zfy) * 100).toFixed(2)}%)`;
                }
              }
            },
            textStyle: {
              color: '#333',
              fontSize: 10,
              fontFamily: 'consolas'
            }
          }
        }
        fyjgChart.setOption(fyjgOption)
      }
    },
    async initBgChart() {
      if (this.queryParams.drgbh) {
        const res = await drgdictByDrgbh(this.queryParams.drgbh)
        if (res) {
          const bfjg = res
          let zfy = bfjg.ypf + bfjg.jyf + bfjg.jcf + bfjg.zlf + bfjg.qtf + bfjg.hcf
          const bgChartDom = document.getElementById('bg-chart')
          const bgChart = echarts.init(bgChartDom)
          const bgOption = {
            title: {
              text: '病组标杆费用（万元）',
              left: 'center',
              top: 'top',
              textStyle: {
                fontSize: 16,
                color: '#000000',
                fontFamily: '幼圆'
              }
            },
            radar: {
              indicator: [
                {
                  name: '治疗标杆' + '\n' + this.formatNumber(bfjg.zlf) + '\n' + (bfjg.zlf == 0 ? '0.00%)' : ((bfjg.zlf / zfy) * 100).toFixed(2) + '%'),
                  max: zfy
                },
                {
                  name: '药品标杆' + '\n' + this.formatNumber(bfjg.ypf) + '\n' + (bfjg.ypf == 0 ? '0.00%)' : ((bfjg.ypf / zfy) * 100).toFixed(2) + '%'),
                  max: zfy
                },
                {
                  name: '耗材标杆' + '\n' + this.formatNumber(bfjg.hcf) + '\n' + (bfjg.hcf == 0 ? '0.00%)' : ((bfjg.hcf / zfy) * 100).toFixed(2) + '%'),
                  max: zfy
                },
                {
                  name: '检验标杆' + '\n' + this.formatNumber(bfjg.jyf) + '\n' + (bfjg.jyf == 0 ? '0.00%)' : ((bfjg.jyf / zfy) * 100).toFixed(2) + '%'),
                  max: zfy
                },
                {
                  name: '检查标杆' + '\n' + this.formatNumber(bfjg.jcf) + '\n' + (bfjg.jcf == 0 ? '0.00%)' : ((bfjg.jcf / zfy) * 100).toFixed(2) + '%'),
                  max: zfy
                },
                {
                  name: '其他标杆' + '\n' + this.formatNumber(bfjg.qtf) + '\n' + (bfjg.qtf == 0 ? '0.00%)' : ((bfjg.qtf / zfy) * 100).toFixed(2) + '%'),
                  max: zfy
                },
              ],
              center: ['50%', '50%'],
              radius: 80,
              name: {
                textStyle: {
                  color: "#000000",
                  fontFamily: 'Consolas,幼圆'
                }
              }
            },
            series: [
              {
                type: 'radar',
                data: [
                  {
                    value: [bfjg.zlf, bfjg.ypf, bfjg.hcf, bfjg.jyf, bfjg.jcf, bfjg.qtf],
                  },
                ]
              }
            ]
          }
          bgChart.setOption(bgOption)
        }
      }
    },
    getMaxValue(obj) {
      let maxValue = 0;
      for (let [key, value] of Object.entries(obj)) {
        if (value > maxValue) {
          maxValue = value;
        }
      }
      return maxValue;
    },
    formatNumber(value) {
      const newValue = value / 10000;
      return newValue.toFixed(4);
    },
    async initCzChart() {
      const res = await listBzczblfyjg(this.queryParams).catch(err => {
        this.$modal.msgError(err)
        this.loading = false
      })
      if (res.code == 200) {
        const czBlfyjg = res.rows[0]
        const maxValue = this.getMaxValue(czBlfyjg)
        let zfy = czBlfyjg.jcf + czBlfyjg.jyf + czBlfyjg.qtf + czBlfyjg.hcf + czBlfyjg.zlf + czBlfyjg.ywf
        const czChartDom = document.getElementById('cz-chart')
        const czChart = echarts.init(czChartDom)
        const czOption = {
          title: {
            text: '超支病例费用结构（万元）',
            left: 'center',
            top: 'top',
            textStyle: {
              fontSize: 16,
              color: '#000000',
              fontFamily: '幼圆'
            }
          },
          radar: {
            indicator: [
              {
                name: '治疗费' + '\n' + this.formatNumber(czBlfyjg.zlf) + '\n' + (czBlfyjg.zlf == 0 ? '0.00%)' : ((czBlfyjg.zlf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '药品费' + '\n' + this.formatNumber(czBlfyjg.ywf) + '\n' + (czBlfyjg.ywf == 0 ? '0.00%)' : ((czBlfyjg.ywf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '耗材费' + '\n' + this.formatNumber(czBlfyjg.hcf) + '\n'  + (czBlfyjg.hcf == 0 ? '0.00%)' : ((czBlfyjg.hcf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '检验费' + '\n' + this.formatNumber(czBlfyjg.jyf) + '\n' + (czBlfyjg.jyf == 0 ? '0.00%)' : ((czBlfyjg.jyf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '检查费' + '\n' + this.formatNumber(czBlfyjg.jcf) + '\n' + (czBlfyjg.jcf == 0 ? '0.00%)' : ((czBlfyjg.jcf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '其他费' + '\n' + this.formatNumber(czBlfyjg.qtf) + '\n' + (czBlfyjg.qtf == 0 ? '0.00%)' : ((czBlfyjg.qtf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              }
            ],
            center: ['50%', '50%'],
            radius: 80,
            name: {
              textStyle: {
                color: "#000000",
                fontFamily: 'Consolas,幼圆'
              }
            }
          },
          series: [
            {
              type: 'radar',
              data: [
                {
                  value: [czBlfyjg.zlf, czBlfyjg.ywf, czBlfyjg.hcf, czBlfyjg.jyf, czBlfyjg.jcf, czBlfyjg.qtf],
                },
              ]
            }
          ]
        };
        czChart.setOption(czOption)
      }
    },
    async initJyChart() {
      const res = await listBzjyblfyjg(this.queryParams).catch(err => {
        this.$modal.msgError(err)
        this.loading = false
      })
      if (res.code == 200) {
        const jyBlfyjg = res.rows[0]
        const maxValue = this.getMaxValue(jyBlfyjg)
        let zfy = jyBlfyjg.jcf + jyBlfyjg.jyf + jyBlfyjg.qtf + jyBlfyjg.hcf + jyBlfyjg.zlf + jyBlfyjg.ywf
        const jyChartDom = document.getElementById('jy-chart')
        const jyChart = echarts.init(jyChartDom)
        const jyOption = {
          title: {
            text: '结余病例费用结构（万元）',
            left: 'center',
            top: 'top',
            textStyle: {
              fontSize: 16,
              color: '#000000',
              fontFamily: '幼圆'
            }
          },
          radar: {
            indicator: [
              {
                name: '治疗费' + '\n' + this.formatNumber(jyBlfyjg.zlf) + '\n' + (jyBlfyjg.zlf == 0 ? '0.00%)' : ((jyBlfyjg.zlf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '药品费' + '\n' + this.formatNumber(jyBlfyjg.ywf) + '\n' + (jyBlfyjg.ywf == 0 ? '0.00%)' : ((jyBlfyjg.ywf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '耗材费' + '\n' + this.formatNumber(jyBlfyjg.hcf) + '\n' + (jyBlfyjg.hcf == 0 ? '0.00%)' : ((jyBlfyjg.hcf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '检验费' + '\n' + this.formatNumber(jyBlfyjg.jyf) + '\n' + (jyBlfyjg.jyf == 0 ? '0.00%)' : ((jyBlfyjg.jyf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '检查费' + '\n' + this.formatNumber(jyBlfyjg.jcf) + '\n' + (jyBlfyjg.jcf == 0 ? '0.00%)' : ((jyBlfyjg.jcf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              },
              {
                name: '其他费' + '\n' + this.formatNumber(jyBlfyjg.qtf) + '\n' + (jyBlfyjg.qtf == 0 ? '0.00%)' : ((jyBlfyjg.qtf / zfy) * 100).toFixed(2) + '%'),
                max: maxValue
              }
            ],
            center: ['50%', '50%'],
            radius: 80,
            name: {
              textStyle: {
                color: "#000000",
                fontFamily: 'Consolas,幼圆'
              }
            }
          },
          series: [
            {
              type: 'radar',
              data: [
                {
                  value: [jyBlfyjg.zlf, jyBlfyjg.ywf, jyBlfyjg.hcf, jyBlfyjg.jyf, jyBlfyjg.jcf, jyBlfyjg.qtf],
                },
              ]
            }
          ]
        }
        jyChart.setOption(jyOption)
      }
    },
    async initCzjyChart() {
      listBzczjy(this.queryParams).then(res => {
        if (res.total > 0 && res.rows[0] != null) {
          this.czjy.czbls = res.rows[0].czbls
          this.czjy.jybls = res.rows[0].jybls
        } else {
          this.czjy.czbls = 0
          this.czjy.jybls = 0
        }

        let bls = this.czjy.czbls + this.czjy.jybls
        const czjyChartDom = document.getElementById('czjy-chart')
        const czjyChart = echarts.init(czjyChartDom)
        const czjyOption = {
          title: {
            text: '超支结余病例统计',
            left: 'center',
            top: 'top',
            textStyle: {
              fontSize: 16,
              color: '#000000',
              fontFamily: '幼圆'
            }
          },
          series: [
            {
              type: 'pie',
              radius: 70,
              center: ['50%', '50%'],
              avoidLabelOverlap: false,
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              data: [
                {value: this.czjy.czbls, name: '超支病例'},
                {value: this.czjy.jybls, name: '结余病例'},
              ]
            }
          ],
          legend: {
            data: ['超支病例', '结余病例'],
            orient: 'vertical',
            left: 'center',
            bottom: '10%',
            formatter: function (name) {
              let data = czjyOption.series[0].data;
              for (let i = 0; i < data.length; i++) {
                if (data[i].name === name) {
                  if (res.total == 0) {
                    return `${name} : ${data[i].value}`;
                  }
                  return `${name} : ${data[i].value} (${((data[i].value / bls) * 100).toFixed(2)}%)`;
                }
              }
            },
            textStyle: {
              color: '#333',
              fontSize: 10,
              fontFamily: 'consolas'
            }
          }
        }
        czjyChart.setOption(czjyOption)
      }).catch(error => {
        this.$modal.msgError(error)
        this.loading = false
      })
    },
    async init() {
      listDrgdict2({}).then(res => {
        this.drgList = res.rows
        this.drgListby = JSON.parse(JSON.stringify(this.drgList))
      })
      if (this.flag == 2) {
        this.checkRole()
      } else {
        getDeptDict(this.$store.state.user.nickName).then(res => {
          this.deptList = res
        })
      }
    },
    async getDoctorByDept(deptName) {
      if (this.flag == 2 && checkRoleItem("ys")) {
        return
      }
      const res = await selectDoctorByHDeptName(deptName)
      if (res.code == 200) {
        this.doctorList = res.rows.map(item => item.nickName)
      }
    },
    async getZdSsList(drgbh) {
      const zdRes = await selectZdListByDrg(drgbh)
      if (zdRes.code == 200) {
        this.zdList = zdRes.rows
      }
      const ssRes = await selectSsListByDrg(drgbh)
      if (ssRes.code == 200) {
        this.ssList = ssRes.rows
      }
    },
    async checkRole() {
      if (checkRoleItem("ys") || checkRoleItem("kzrys")) {
        this.doctorList = [this.$store.state.user.nickName]
        this.queryParams.zyys = this.$store.state.user.nickName
        const res = await getUserDeptList()
        if (res.code == 200) {
          const deptList = res.deptList ? res.deptList : null
          if (deptList && deptList.length > 0) {
            deptList.forEach(item => {
              this.deptList.push({
                name: item.hDeptName,
                value: item.hDeptName
              })
            })
          }
        }
      } else {
        this.flag = 1
        getDeptDict(this.$store.state.user.nickName).then(res => {
          this.deptList = res
        })
      }
    }
  },
  created() {
    console.log(this.$route.query)
    if (this.$route.query.hasOwnProperty("drgbh") && this.$route.query.drgbh) {
      this.flag = 1
      this.queryParams.drgbh = this.$route.query.drgbh.trim()
      this.getZdSsList(this.queryParams.drgbh)
      this.queryParams.adtFrom = this.$route.query.adtFrom
      this.queryParams.adtTo = this.$route.query.adtTo
      this.queryParams.dateType = this.$route.query.dateType ? this.$route.query.dateType.trim() : 'cydate'
      this.queryParams.deptName = this.$route.query.deptName
      this.getDoctorByDept(this.queryParams.deptName)
      this.queryParams.adtFromRy = this.$route.query.adtFromRy
      this.queryParams.adtToRy = this.$route.query.adtToRy
      this.queryParams.isCheckRy = this.$route.query.isCheckRy == '是' ? 'check' : 'notcheck'
      this.queryParams.qsfs = this.$route.query.qsfs == '%' ? null : this.$route.query.qsfs.trim()
      if (this.$route.query.cblb && this.$route.query.cblb.trim()) {
        if (this.$route.query.cblb.indexOf("%") == -1) {
          this.queryParams.cblb = this.$route.query.cblb.trim()
          this.cblb = this.queryParams.cblb.split(",")
        } else {
          this.queryParams.cblb = '%'
          this.cblb = ['%']
        }
      }
      if (this.$route.query.zd) {
        this.queryParams.zd = this.$route.query.zd.trim()
      }
      if (this.$route.query.ss) {
        this.queryParams.ss = this.$route.query.ss.trim()
      }
      if (this.$route.query.zyys) {
        this.queryParams.zyys = this.$route.query.zyys.trim()
      }
      if (this.$route.query.ykflag) {
        const ykflag = this.$route.query.ykflag
        if (ykflag == "是") {
          this.queryParams.ykflag = "ying"
        }
        if (ykflag == "否") {
          this.queryParams.ykflag = "kui"
        }
        if (ykflag == "平") {
          this.queryParams.ykflag = "ping"
        }
      }
      this.getList()
    } else {
      this.flag = 2
    }
    this.init();
  }
}
</script>

<style lang="scss">
.table {
  width: 100%;

  tr {
    height: 30px;
  }
}


.red-text {
  color: red;
}

.black-text {

}
</style>
