<template>
  <div v-loading="loading">
    <div>
      <el-form
        style="margin-top: 20px; margin-left: 1%; width: 98%;"
        :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="科室">
          <el-select v-model="queryParams.cykb"
                     @focus="resetDept"
                     @change="getDoctorByDept(queryParams.cykb)"
                     filterable
                     :filter-method="deptFilter"
                     placeholder="科室"
                     clearable
          >
            <el-option v-for="(item,index) in deptList" :value="item.cykb" :key="index" :label="item.cykb">{{item.cykb}}</el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="医生">
          <el-select v-model="queryParams.zyys"
                     @focus="resetDoctor"
                     @change="resetDoctor"
                     filterable
                     :filter-method="doctorFilter"
                     placeholder="医生"
                     clearable
          >
            <el-option v-for="(item,index) in doctorList" :value="item.zyys" :key="index" :label="item.zyys">{{item.zyys}}</el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="科室">-->
<!--          <el-autocomplete-->
<!--            :popper-append-to-body="false"-->
<!--            class="inline-input"-->
<!--            v-model="queryParams.cykb"-->
<!--            :fetch-suggestions="querySearchDept"-->
<!--            placeholder="科室"-->
<!--            @select="handleSelectDept"-->
<!--            clearable-->
<!--          ></el-autocomplete>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="医生">-->
<!--          <el-autocomplete-->
<!--            :popper-append-to-body="false"-->
<!--            class="inline-input"-->
<!--            v-model="queryParams.zyys"-->
<!--            :fetch-suggestions="querySearchDoctor"-->
<!--            placeholder="医生"-->
<!--            @select="handleSelectDoctor"-->
<!--            clearable-->
<!--          ></el-autocomplete>-->
<!--        </el-form-item>-->
        <el-form-item label="日期类型">
          <el-select v-model="queryParams.dateType" placeholder="日期类型" clearable>
            <el-option v-for="item in dateTypeList" :value="item.value" :label="item.name">{{item.name}}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="daterangeDate"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="参保类别">
          <el-select v-model="queryParams.cblb" placeholder="参保类别" clearable>
            <el-option v-for="item in cblbList" :value="item">{{item}}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="clearYsData">重置</el-button>
          <!--          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>-->
        </el-form-item>
      </el-form>
    </div>

    <div class="ztsj" style="width: 100%;display: flex;justify-content: space-around;height: 100%;">
      <el-card style="width: 98%; margin-right: 2%;height: 100%;">
        <div style="width: 50%;display: block;height: 100%; padding-bottom: 10px;">
          <div style="width: 100%; margin-top: 5px;height: 25%;">
            <el-card style="width: 100%;padding-left:90px;">
              <table style="font-size: 12px;text-align: center">
                <tr style="font-weight: bold;font-size: 18px;">
                  <td style="color: #1c84c6">{{ysztfz.zfy}}</td>
                  <td style="color: #1f2d3d">{{ysztfz.zqz}}</td>
                  <td style="color: #1ab394">{{ysztfz.zyk}}</td>
                </tr>
                <tr>
                  <td style="padding: 0 20px;">医疗总费用（万元）</td>
                  <td style="padding: 0 20px;">总权重（指数）</td>
                  <td style="padding: 0 20px;">总盈亏（万元）</td>
                </tr>
              </table>
            </el-card>
          </div>

          <el-card style="width: 100%; padding-bottom: 5px;height: 80%;">
            <div>
              <table class="table" style="text-align: center;">
                <tr style="font-weight: bold">
                  <td>{{ysztfz.drgzs}}</td>
                  <td>{{ysztfz.cmi}}</td>
                  <td>{{ysztfz.cjfy}}</td>
                  <td>{{ysztfz.sjxhzs}}</td>
                </tr>
                <tr style="font-size: 13px;">
                  <td>覆盖总组数</td>
                  <td>CMI</td>
                  <td>次均费用（万元）</td>
                  <td>时间消耗指数</td>
                </tr>
                <tr style="font-weight: bold">
                  <td>{{ysztfz.yzb}}</td>
                  <td>{{ysztfz.rzl}}</td>
                  <td>{{ysztfz.cybls}}</td>
                  <td>{{ysztfz.rcrqb}}</td>
                </tr>
                <tr style="font-size: 13px;">
                  <td>药占（%）</td>
                  <td>入组率（%）</td>
                  <td>出院病例数（例次）</td>
                  <td>人数占比</td>
                </tr>
                <tr style="font-weight: bold">
                  <td>{{ysztfz.fyxhzs}}</td>
                  <td>{{ysztfz.pjzyr}}</td>
                  <td>{{ysztfz.swl}}</td>
                  <td>{{ysztfz.bas}}</td>
                </tr>
                <tr style="font-size: 13px;">
                  <td>费用消耗指数</td>
                  <td>平均住院日</td>
                  <td>死亡率（%）</td>
                  <td>病案数</td>
                </tr>
              </table>
            </div>
          </el-card>
        </div>
        <div style="width: 50%; margin: 0 1%;">
          <div style="display: block; margin-left: 30px;margin-top: 20px;">
            <div>费用构成</div>
            <div id="chart-fyjg" style="width: 100%; height: 250px;"></div>
          </div>
        </div>
      </el-card>
    </div>

    <div class="chart" style="width: 100%;">
      <div class="chart-first" style="display: flex; width: 100%;">
        <el-card style="width: 50%; margin: 0 1%;">
          <div>病例构成及超支结余</div>
          <div style="display: flex;">
            <div id="chart-blgc" style="width: 35%; height: 250px;margin-top: 20px;">

            </div>
            <div class="table-blgc" style="width: 70%;">
              <el-radio-group v-model="activeName" @change="setBlgc">
                <el-radio-button label="结余病例" >
                  <span>结余病例</span>
                </el-radio-button>
                <el-radio-button label="超支病例" >
                  <span>超支病例</span>
                </el-radio-button>
              </el-radio-group>

              <el-table :data="blgcList" height="220">
                <el-table-column show-overflow-tooltip label="序号" align="center">
                  <template slot-scope="scope">
                    {{scope.$index + 1}}
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="病人姓名" align="center" prop="xm"/>
                <el-table-column show-overflow-tooltip label="费用消耗指数" align="center" prop="fyxhzs" />
                <el-table-column show-overflow-tooltip label="CMI" align="center" prop="cmi" />
              </el-table>
            </div>
          </div>
        </el-card>
        <el-card style="width: 50%; margin: 0 1%;">
          <div>病组构成及超支结余</div>
          <div style="display: flex;">
            <div id="chart-bzgc" style="width: 35%; height: 250px;margin-top: 20px;">

            </div>
            <div class="table-bzgc" style="width: 70%;">

              <el-radio-group v-model="activeName1" @change="setBzgc">
                <el-radio-button label="结余病组" >
                  <span>结余病组</span>
                </el-radio-button>
                <el-radio-button label="超支病组" >
                  <span>超支病组</span>
                </el-radio-button>
              </el-radio-group>

              <el-table :data="bzgcList" height="220">
                <el-table-column show-overflow-tooltip label="序号" align="center">
                  <template slot-scope="scope">
                    {{scope.$index + 1}}
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="病组姓名" align="center" prop="drgmc"/>
                <el-table-column show-overflow-tooltip label="费用消耗指数" align="center" prop="fyxhzs" />
                <el-table-column show-overflow-tooltip label="CMI" align="center" prop="cmi" />
              </el-table>
            </div>
          </div>
        </el-card>
      </div>


      <div class="chart-second" style="display: flex; width: 100%;margin-top: 10px;">
        <el-card style="width: 100%; margin: 0 1%;">
          <div>病例类型组成</div>
          <div style="display: flex;">
            <div id="chart-bllx" style="width: 30%; height: 250px;margin-top: 20px;margin-left: 100px;">

            </div>
            <div class="table-bllx" style="width: 75%;">
              <el-radio-group v-model="activeName2" @change="setBllx">
                <el-radio-button label="低倍率病例" >
                  <span>低倍率病例</span>
                </el-radio-button>
                <el-radio-button label="高倍率病例" >
                  <span>高倍率病例</span>
                </el-radio-button>
                <el-radio-button label="正常倍率病例" >
                  <span>正常倍率病例</span>
                </el-radio-button>
              </el-radio-group>

              <el-table :data="bllxList" height="220" style="width: 100%;">
                <el-table-column show-overflow-tooltip label="序号" align="center">
                  <template slot-scope="scope">
                    {{scope.$index + 1}}
                  </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="病人姓名" align="center" prop="xm"/>
                <el-table-column show-overflow-tooltip label="费用消耗指数" align="center" prop="fyxhzs" />
                <el-table-column show-overflow-tooltip label="CMI" align="center" prop="cmi" />
                <el-table-column show-overflow-tooltip label="支付标准" align="center" prop="zfbz" />
                <el-table-column show-overflow-tooltip label="总费用" align="center" prop="zfy" />
              </el-table>
            </div>
          </div>
        </el-card>

      </div>
    </div>
  </div>
</template>

<script>
import {getDeptList, getDoctorByDept} from "@/api/drg/syjl";
import {listYsztsj, listYsczjybl, listYsczjybz, listYsbllx, listYsfyjg} from "@/api/tjfx/yssy";
import * as echarts from "echarts";
import PinYinMatch from "pinyin-match";
import { getDeptListFromSy } from '@/api/tjfx/tjfx'

export default {
  name: "index",
  data() {
    return {
      loading: false,
      activeName: "结余病例",
      activeName1: "结余病组",
      activeName2: "低倍率病例",
      showSearch: true,
      cblbList: ["职工","居民","自费","生育"],
      dateTypeList: [
        {name:"出院时间", value:"cydate"},
        {name:"结算时间", value:"jsdate"}
      ],
      doctorList:[],
      doctorListby:[],
      deptList:[],
      deptListby:[],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        date: null,
        cblb: null,
        dateType: null,
        zyys: null,
        adtFrom: null,
        adtTo: null
      },
      ysztfz: {
        zyys: 0.00,
        zfy: 0.00,
        cybls: 0.00,
        cjfy: 0.00,
        zqz: 0.00,
        rcrqb: 0.00,
        fyxhzs: 0.00,
        sjxhzs: 0.00,
        swl: 0.00,
        drgzs: 0.00,
        bas: 0.00,
        rzl: 0.00,
        cmi: 0.00,
        pjzyr: 0.00,
        yzb: 0.00,
        czb: 0.00,
        zfl: 0.00,
        zyk: 0.00
      },
      daterangeDate: [],
      ysJyBl:[],
      ysCzBl:[],
      ysJyBz:[],
      ysCzBz:[],
      bzgcList:[],
      blgcList:[],
      dblblList:[],
      gblblList:[],
      bllxList:[],
      zcblblList:[],
      ysfyjg: {
        zhylfwf: 0.00,
        kff: 0.00,
        zdf: 0.00,
        zlf: 0.00,
        ywf: 0.00,
        xyyxyzpf: 0.00,
        hcf: 0.00,
        zyf: 0.00,
        qtf: 0.00,
      },
    }
  },
  methods:{
    getDeptListByUser(){
      getDeptListFromSy().then(res => {
        res.list.forEach(item => {
          this.deptList.push({
            cykb: item
          })
        })
        this.deptListby = this.deptList
      })
    },
    resetDept() {
      this.deptList = this.deptListby
    },
    resetDoctor() {
      this.doctorList = this.doctorListby
      if (this.doctorList.length == 0 && this.queryParams.cykb == null) {
        this.$modal.msgWarning("请先选择科室")
        this.queryParams.zyys = null
      }
    },
    deptFilter(val) {
      this.queryParams.cykb = val
      if (val) {
        this.deptList = []
        var deptList = this.deptListby.filter((item) => {
          if (PinYinMatch.match(item.cykb, val)) {
            return true
          }
        })
        this.deptList = deptList
      } else {
        this.deptList = this.deptListby
      }
    },
    doctorFilter(val) {
      this.queryParams.zyys = val
      if (val) {
        this.doctorList = []
        var doctorList = this.doctorListby.filter((item) => {
          if (PinYinMatch.match(item.zyys, val)) {
            return true
          }
        })
        this.doctorList = doctorList
      } else {
        this.doctorList = this.doctorListby
      }
    },
    getDoctorByDept(cykb) {
      this.queryParams.zyys = null
      this.deptList = this.deptListby
      getDoctorByDept({cykb:cykb}).then(response => {
        this.doctorList = response.rows;
        this.doctorListby = JSON.parse(JSON.stringify(this.doctorList))
      });
    },
    setBlgc() {
      if (this.activeName == '结余病例') {
        this.blgcList = this.ysJyBl
      } else if (this.activeName == '超支病例') {
        this.blgcList = this.ysCzBl
      }
    },
    setBzgc() {
      if (this.activeName1 == '结余病组') {
        this.bzgcList = this.ysJyBz
      } else if (this.activeName1 == '超支病组') {
        this.bzgcList = this.ysCzBz
      }
    },
    setBllx() {
      if (this.activeName2 == '正常倍率病例') {
        this.bllxList = this.zcblblList
      } else if (this.activeName2 == '高倍率病例') {
        this.bllxList =  this.gblblList
      } else if (this.activeName2 == '低倍率病例') {
        this.bllxList = this.dblblList
      }
    },
    // querySearchDoctor(queryString, cb) {
    //   if (queryString != null || queryString == "") {
    //     let results = []
    //     for (let i = 0; i < this.doctorList.length; i++) {
    //       if (this.doctorList[i].zyys.indexOf(queryString) > -1) {
    //         results.push(this.doctorList[i])
    //       }
    //     }
    //     cb(results);
    //   } else {
    //     cb(this.doctorList);
    //   }
    // },
    // querySearchDept(queryString, cb) {
    //   if (queryString != null || queryString == "") {
    //     let results = []
    //     for (let i = 0; i < this.deptList.length; i++) {
    //       if (this.deptList[i].cykb.indexOf(queryString) > -1) {
    //         results.push(this.deptList[i])
    //       }
    //     }
    //     cb(results);
    //   } else {
    //     cb(this.deptList);
    //   }
    // },
    // handleSelectDept(item) {
    //   this.queryParams.cykb = item.cykb
    //   getDoctorByDept({cykb:item.cykb}).then(res => {
    //     this.doctorList = res.rows
    //       for (let i = 0; i < this.doctorList.length; i++) {
    //         this.doctorList[i]['value'] = this.doctorList[i].zyys
    //       }
    //   })
    // },
    // handleSelectDoctor(item) {
    //   this.queryParams.zyys = item.zyys
    // },
    reset() {
      this.form = {
        date: null,
        cblb: null,
        dateType: null
      };
      this.resetForm("form");
    },
    handleQuery() {

      console.log(this.daterangeDate);
      if (this.queryParams.dateType == null && this.daterangeDate != null && this.daterangeDate.length > 0) {
        this.$modal.alertWarning("请选择日期类型");
        return
      } else if (this.queryParams.dateType != null && (this.daterangeDate == null || this.daterangeDate.length == 0)) {
        this.queryParams.dateType = null;
      }

      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      // if (this.queryParams.zyys == null) {
      //   this.$modal.alertWarning("请选择医生")
      //   return
      // }

      if (this.queryParams.cblb == null) {
        this.queryParams.cblb = "";
      }

      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDate && '' != this.daterangeDate) {
        this.queryParams.adtFrom = this.daterangeDate[0];
        this.queryParams.adtTo = this.daterangeDate[1];
      }
      listYsztsj(this.queryParams).then(res => {
        if (res.total > 0) {
          this.ysztfz = res.rows[0]
          this.initYsData()
        } else {
          this.$modal.alertWarning("该医生数据为空")
          this.clearYsData()
          this.loading = false
        }
      });
    },
    clearYsData() {
      echarts.init(document.getElementById('chart-blgc')).clear()
      echarts.init(document.getElementById('chart-bzgc')).clear()
      echarts.init(document.getElementById('chart-bllx')).clear()
      echarts.init(document.getElementById('chart-fyjg')).clear()
      this.ysJyBl = []
      this.ysCzBl = []
      this.ysJyBz = []
      this.ysCzBz = []
      this.bzgcList = []
      this.blgcList = []
      this.dblblList = []
      this.gblblList = []
      this.bllxList = []
      this.zcblblList = []
      this.ysztfz = {
        zyys: 0.00,
        zfy: 0.00,
        cybls: 0.00,
        cjfy: 0.00,
        zqz: 0.00,
        rcrqb: 0.00,
        fyxhzs: 0.00,
        sjxhzs: 0.00,
        swl: 0.00,
        drgzs: 0.00,
        bas: 0.00,
        rzl: 0.00,
        cmi: 0.00,
        pjzyr: 0.00,
        yzb: 0.00,
        czb: 0.00,
        zfl: 0.00,
        zyk: 0.00
      }
    },
    initYsData() {
      this.initBlgcChart();
      this.initBzgcChart();
      this.initBllxChart();
      this.initFygcChart();
    },
    initBlgcChart() {
      listYsczjybl(this.queryParams).then(res => {
        if (res.total > 0 && res.rows[0] != null) {
          this.ysJyBl = res.rows.filter(function (obj) {
            return obj.flag == 0;
          });
          this.ysCzBl = res.rows.filter(function (obj) {
            return obj.flag == 1;
          });

          if (this.activeName == '结余病例') {
            this.blgcList = this.ysJyBl
          } else if (this.activeName == '超支病例') {
            this.blgcList =  this.ysCzBl
          }
        }
        const blgcChartDom = document.getElementById('chart-blgc')
        const blgcChart = echarts.init(blgcChartDom)
        const blgcOption = {
          series: [
            {
              type: 'pie',
              radius: ['20%', '40%'],
              avoidLabelOverlap: false,
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              data: [
                { value: this.ysCzBl.length, name: '超支病例' },
                { value: this.ysJyBl.length, name: '结余病例' },
              ]
            }
          ],
          legend: {
            data: ['超支病例', '结余病例'],
            orient: 'vertical',
            formatter: function (name) {
              let data = blgcOption.series[0].data;
              for (let i = 0; i < data.length; i++) {
                if (data[i].name === name) {
                  return `${name} : ${data[i].value} (${((data[i].value / res.total) * 100).toFixed(2)}%)`;
                }
              }
            },
            textStyle: {
              color: '#333',
              fontSize: 10,
              fontFamily: 'consolas'
            }
          }
        }
        blgcChart.setOption(blgcOption)
      }).catch(error => {
        this.$modal.msgError(error)
        this.loading = false
      })
    },
    initBzgcChart() {
      listYsczjybz(this.queryParams).then(res => {
        if (res.total > 0) {
          this.ysJyBz = res.rows.filter(function (obj) {
            return obj.flag == 0;
          });
          this.ysCzBz = res.rows.filter(function (obj) {
            return obj.flag == 1;
          });

          if (this.activeName1 == '结余病组') {
            this.bzgcList = this.ysJyBz
          } else if (this.activeName1 == '超支病组') {
            this.bzgcList =  this.ysCzBz
          }
        }
        const bzgcChartDom = document.getElementById('chart-bzgc')
        const bzgcChart = echarts.init(bzgcChartDom)
        const bzgcOption = {
          series: [
            {
              type: 'pie',
              radius: ['20%', '40%'],
              avoidLabelOverlap: false,
              data: [
                { value: this.ysCzBz.length, name: '超支病组' },
                { value: this.ysJyBz.length, name: '结余病组' },
              ],
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
            }
          ],
          legend: {
            data: ['超支病组', '结余病组'],
            orient: 'vertical',
            formatter: function (name) {
              let data = bzgcOption.series[0].data;
              for (let i = 0; i < data.length; i++) {
                if (data[i].name === name) {
                  return `${name} : ${data[i].value} (${((data[i].value / res.total) * 100).toFixed(2)}%)`;
                }
              }
            },
            textStyle: {
              color: '#333',
              fontSize: 10,
              fontFamily: 'consolas'
            }
          }
        }
        bzgcChart.setOption(bzgcOption)
      }).catch(error => {
        this.$modal.msgError(error)
        this.loading = false
      })
    },
    initBllxChart() {
      listYsbllx(this.queryParams).then(res => {
        if (res.total > 0) {
          this.gblblList = res.rows.filter(function (obj) {
            return obj.flag == 1;
          });
          this.dblblList = res.rows.filter(function (obj) {
            return obj.flag == 2;
          });
          this.zcblblList = res.rows.filter(function (obj) {
            return obj.flag == 0;
          });

          if (this.activeName2 == '正常倍率病例') {
            this.bllxList = this.zcblblList
          } else if (this.activeName2 == '高倍率病例') {
            this.bllxList =  this.gblblList
          } else if (this.activeName2 == '低倍率病例') {
            this.bllxList = this.dblblList
          }


        }
        const bllxChartDom = document.getElementById('chart-bllx')
        const bllxChart = echarts.init(bllxChartDom)
        const bllxOption = {
          series: [
            {
              type: 'pie',
              radius: ['20%', '40%'],
              avoidLabelOverlap: false,
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              data: [
                { value: this.gblblList.length, name: '高倍率病例' },
                { value: this.zcblblList.length, name: '正常倍率病例' },
                { value: this.dblblList.length, name: '低倍率病例' }
              ]
            }
          ],
          legend: {
            data: ['高倍率病例', '正常倍率病例','低倍率病例'],
            orient: 'vertical',
            formatter: function (name) {
              let data = bllxOption.series[0].data;
              for (let i = 0; i < data.length; i++) {
                if (data[i].name === name) {
                  return `${name} : ${data[i].value} (${((data[i].value / res.total) * 100).toFixed(2)}%)`;
                }
              }
            },
            textStyle: {
              color: '#333',
              fontSize: 10,
              fontFamily: 'consolas'
            }
          }
        }
        bllxChart.setOption(bllxOption)
      }).catch(error => {
        this.$modal.msgError(error)
        this.loading = false
      })
    },
    initFygcChart() {
      listYsfyjg(this.queryParams).then(res => {
        if (res.total > 0 && res.rows[0] != null) {
          this.ysfyjg = res.rows[0]
        } else {
          this.ysfyjg.zhylfwf = 0
          this.ysfyjg.kff = 0
          this.ysfyjg.zdf = 0
          this.ysfyjg.zlf = 0
          this.ysfyjg.ywf = 0
          this.ysfyjg.xyyxyzpf = 0
          this.ysfyjg.hcf = 0
          this.ysfyjg.zyf = 0
          this.ysfyjg.qtf = 0
        }
        let zfy = this.ysfyjg.zhylfwf + this.ysfyjg.kff + this.ysfyjg.zdf +
          this.ysfyjg.zlf + this.ysfyjg.ywf + this.ysfyjg.xyyxyzpf +
          this.ysfyjg.hcf + this.ysfyjg.zyf + this.ysfyjg.qtf
        const fyjgChartDom = document.getElementById('chart-fyjg')
        const fyjgChart = echarts.init(fyjgChartDom)
        const fyjgOption = {
          series: [
            {
              type: 'pie',
              radius: ['30%', '70%'],
              center: ['70%', '50%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'outside',
                formatter: '{b} \n {c}万元 \n 占比{d}%',
                fontSize: 10
              },
              labelLine: {
                show: false
              },
              data: [
                { value: this.ysfyjg.zhylfwf, name: '综合医疗服务费' },
                { value: this.ysfyjg.kff, name: '康复费' },
                { value: this.ysfyjg.zdf, name: '诊断费' },
                { value: this.ysfyjg.zlf, name: '治疗费' },
                { value: this.ysfyjg.ywf, name: '药物费' },
                { value: this.ysfyjg.xyyxyzpf, name: '血液和血液制品费' },
                { value: this.ysfyjg.hcf, name: '耗材费' },
                { value: this.ysfyjg.zyf, name: '中医费' },
                { value: this.ysfyjg.qtf, name: '其他费用' },
              ]
            }
          ],
          legend: {
            data: ['综合医疗服务费', '康复费','诊断费','治疗费','药物费','血液和血液制品费', '耗材费','中医费','其他费用'],
            orient: 'vertical',
            left: 10,
            top: 10,
            formatter: function (name) {
              let data = fyjgOption.series[0].data;
              for (let i = 0; i < data.length; i++) {
                if (data[i].name === name) {
                  if (zfy == 0 || zfy == null) {
                    return `${name} : ${data[i].value}万元`;
                  }
                  return `${name} : ${data[i].value}万元 (${((data[i].value / zfy) * 100).toFixed(2)}%)`;
                }
              }
            },
            textStyle: {
              color: '#333',
              fontSize: 10,
              fontFamily: 'consolas'
            }
          }
        }
        fyjgChart.setOption(fyjgOption)
        this.loading = false
      }).catch(error => {
        this.$modal.msgError(error)
        this.loading = false
      })
    },
    // resetQuery() {
    //   this.daterangeDate = [];
    //   this.queryParams = {
    //     pageNum: 1,
    //     pageSize: 10,
    //     date: null,
    //     cblb: null,
    //     dateType: null,
    //     zyys: null,
    //     adtFrom: null,
    //     adtTo: null
    //   }
    //   this.activeName = "jybl"
    //   this.activeName1 = "jybz"
    //   this.activeName2 = "dblbl"
    //   this.doctorList=[],
    //     this.dblblList=[],
    //     this.gblblList=[],
    //     this.zcblblList=[],
    //     this.ysztfz= {
    //     zyys: null,
    //       zfy: null,
    //       cybls: null,
    //       cjfy: null,
    //       zqz: null,
    //       rcrqb: null,
    //       fyxhzs: null,
    //       sjxhzs: null,
    //       swl: null,
    //       drgzs: null,
    //       bas: null,
    //       rzl: null,
    //       cmi: null,
    //       pjzyr: null,
    //       yzb: null,
    //       czb: null,
    //       zfl: null
    //   }
    //   this.daterangeDate=[]
    //   this.ysJyBl=[]
    //   this.ysCzBl=[]
    //   this.ysJyBz=[]
    //   this.ysCzBz=[]
    //   this.ysfyjg= {
    //     zhylfwf: null,
    //     kff: null,
    //     zdf: null,
    //     zlf: null,
    //     ywf: null,
    //     xyyxyzpf: null,
    //     hcf: null,
    //     zyf: null,
    //     qtf: null,
    //   }
    // },
    // handleExport() {
    //   getPdf("医生首页-" + this.queryParams.zyys,)
    // },
  },
  mounted() {
    // getDoctorList({}).then(res=>{
    //   this.doctorList = res.rows
    //   for (let i = 0; i < this.doctorList.length; i++) {
    //     this.doctorList[i]['value'] = this.doctorList[i].zyys
    //   }
    // })

    // getDeptList({}).then(res=>{
    //   this.deptList = res.rows
    //   this.deptListby = JSON.parse(JSON.stringify(this.deptList))
    //   // for (let i = 0; i < this.deptList.length; i++) {
    //   //   this.deptList[i]['value'] = this.deptList[i].cykb
    //   // }
    // })
    this.getDeptListByUser()
  }
}
</script>

<style lang="scss">
.ztsj-card div:nth-child(2) {
  font-weight: bolder;
  margin-left: 20px;
  font-size: 15px;
}

.ztsj {
  width: 98%;
  margin: 1%;
  div {
    width: 100%;
    display: flex;
    .ztsj-card {
      display: block;
      //height: 95%;
      width: 19%;
      margin-left: 5px;
      font-size: 11px;
      div {
        display: flex;
      }
    }
  }
}
* {
  -webkit-user-select:none;/*webkit浏览器*/
  -moz-user-select:none;/*火狐*/
  -ms-user-select:none;/*IE10*/
  user-select:none;
}
.table {
  width: 100%;
  tr {
    height: 30px;
  }
}
</style>
