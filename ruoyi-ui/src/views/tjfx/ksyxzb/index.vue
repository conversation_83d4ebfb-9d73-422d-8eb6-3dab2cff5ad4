<template>
  <div class="app-container">
    <div>
      <el-form v-model="params" :inline="true" label-width="68px" size="small">
        <el-form-item>
          <date-filter v-model="dateParams"/>
        </el-form-item>
        <el-form-item>
          <cblb-options v-model="params.cblb"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-tabs v-loading="loading" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="科室支付权重统计" name="zfqz">
          <div v-if="activeName === 'zfqz'" class="chart" id="chart-zfqz"></div>
        </el-tab-pane>
        <el-tab-pane label="科室人均床日统计" name="pjdays">
          <div v-if="activeName === 'pjdays'" class="chart" id="chart-pjdays"></div>
        </el-tab-pane>
        <el-tab-pane label="科室费用消耗指数统计" name="fyxhzs">
          <div v-if="activeName === 'fyxhzs'" class="chart" id="chart-fyxhzs"></div>
        </el-tab-pane>
        <el-tab-pane label="科室时间消耗指数统计" name="sjxhzs">
          <div v-if="activeName === 'sjxhzs'" class="chart" id="chart-sjxhzs"></div>
        </el-tab-pane>
        <el-tab-pane label="科室盈亏（万元）统计" name="zyk">
          <div v-if="activeName === 'zyk'" class="chart" id="chart-zyk"></div>
        </el-tab-pane>
        <el-tab-pane label="科室例均支付权重统计" name="ljzfqz">
          <div v-if="activeName === 'ljzfqz'" class="chart" id="chart-ljzfqz"></div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import DateFilter from '@/components/DateFilter/DateFilter.vue';
import CblbOptions from '@/components/CblbOptions/CblbOptions.vue';
import { getDeptYxzb } from '@/api/tjfx/tjfx';
import { getMonthFirstDayStr, getTodayLastSecondStr } from '@/utils/dateUtils';
import * as echarts from 'echarts';

export default {
  name: 'Ksyxzb',
  components: { CblbOptions, DateFilter },
  data() {
    return {
      titles: {
        zfqz: '支付权重',
        pjdays: '人均床日',
        fyxhzs: '费用消耗指数',
        sjxhzs: '时间消耗指数',
        zyk: '总盈亏',
        ljzfqz: '例均支付权重'
      },
      activeName: 'zfqz',
      dataList: [],
      loading: true,
      params: {
        cblb: [],
      },
      dateParams: {
        datetype: 'jsdate',
        startDate: getMonthFirstDayStr(),
        endDate: getTodayLastSecondStr()
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleClick(tab) {
      const tabName = tab.paneName;
      this.activeName = tabName;
      this.$nextTick(() => {
        this.initChart(tabName);
      });
    },
    async initChart(tabName) {
      const chartId = `chart-${tabName}`;
      const chartDom = document.getElementById(chartId);
      if (!chartDom) return;

      this.dataList.sort((a, b) => b[tabName] - a[tabName]);
      const myChart = echarts.init(chartDom);
      const option = {
        dataZoom: [
          {
            type: 'slider',
            start: 0,
            end: 100,
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          name: '科室',
          nameTextStyle: {
            fontFamily: '幼圆',
            fontWeight: 'bold',
          },
          data: this.dataList.map(item => item.cykb),
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            interval: 0,
            formatter: function (value) {
              const maxLengthPerLine = 1;
              let result = '';
              for (let i = 0; i < value.length; i += maxLengthPerLine) {
                result += value.substring(i, i + maxLengthPerLine) + '\n';
              }
              return result.trim();
            },
            textStyle: {
              fontSize: 12,
              fontFamily: '幼圆',
              fontWeight: 'bold',
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: '#eee',
              width: 1
            }
          }
        },
        yAxis: {
          type: 'value',
          name: this.titles[tabName],
          nameTextStyle: {
            fontFamily: '幼圆',
            fontWeight: 'bold',
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
              fontFamily: 'Consolas'
            }
          }
        },
        series: [
          {
            name: this.titles[tabName],
            barWidth: '20px',
            data: this.dataList.map(item => item[tabName]),
            type: 'bar',
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: 'top',
                  textStyle: {
                    fontSize: 12,
                    fontFamily: 'Consolas'
                  }
                }
              }
            }
          }
        ]
      };

      myChart.setOption(option);
    },
    async getList() {

      if (!this.dateParams.startDate && !this.dateParams.endDate) {
        this.dateParams.datetype = null
      }

      this.loading = true;
      try {
        const res = await getDeptYxzb({
          ...this.params,
          ...this.dateParams
        });
        this.dataList = res.rows;
        console.log(this.dataList )
        if (this.activeName) {
          this.$nextTick(() => {
            this.initChart(this.activeName);
          });
        }
      } catch (error) {
        console.error('获取数据失败', error);
      } finally {
        this.loading = false;
      }
    },
    handleSearch() {
      this.getList();
    }
  }
};
</script>

<style>
.chart {
  width: 100%;
  height: 600px;
}
</style>
