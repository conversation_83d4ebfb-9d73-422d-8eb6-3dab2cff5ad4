<template>
  <div class="app-container">
    <div>
      <el-form :model="params" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item>
          <date-filter v-model="dateParams"/>
          <dept-conditions v-model="params.ksname"/>
          <cblb-options v-model="params.cblb"/>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="dataList" v-loading="loading" :border="true">
        <el-table-column prop="cykb" label="出院科别" align="center" width="180" show-overflow-tooltip/>
        <el-table-column prop="ys" label="医生" align="center" width="100"/>
        <el-table-column prop="drgzs" label="DRG组数" align="center" width="100"/>
        <el-table-column prop="zbas" label="总病案数" align="center" width="100">
          <template v-slot="scope">
            <el-button type="text" @click="openReport(scope.row)">{{ scope.row.zbas }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="rzbas" label="入组病案数" align="center" width="100"/>
        <el-table-column prop="rzl" label="入组率" align="center" width="100"/>
        <el-table-column prop="zqz" label="总权重" align="center" width="100"/>
        <el-table-column prop="cmi" label="cmi" align="center" width="100"/>
        <el-table-column prop="zfy" label="总费用" align="center" width="100"/>
        <el-table-column prop="pjzyr" label="平均住院日" align="center" width="100"/>
        <el-table-column prop="zyk" label="总盈亏" align="center" width="100"/>
        <el-table-column prop="ljyk" label="例均盈亏" align="center" width="100"/>
        <el-table-column prop="fyxhzs" label="费用消耗指数" align="center" width="100"/>
      </el-table>
    </div>
    <div>
      <pagination :page.sync="pageNum" :limit.sync="pageSize" :total="total" @pagination="handleSearch"/>
    </div>
    <div>
      <el-dialog :visible.sync="showReport" width="80%">
        <iframe :src="reportUrl" width="100%" height="600px"></iframe>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import DateFilter from '../../../components/DateFilter/DateFilter.vue'
import { getMonthFirstDayStr, getTodayLastSecondStr } from '../../../utils/dateUtils'
import DeptConditions from '../../../components/DeptConditions/index.vue'
import CblbOptions from '../../../components/CblbOptions/CblbOptions.vue'
import { ysdrgtj } from '../../../api/tjfx/ysdrgtj'
import { load } from 'runjs/lib/script'
import { getdk, getFzPageIp } from '../../../api/drg/syjl'

export default {
  name: 'Ysdrgtj',
  components: { CblbOptions, DeptConditions, DateFilter },
  data(){
    return {
      dateParams: {
        datetype: 'jsdate',
        startDate: getMonthFirstDayStr(),
        endDate: getTodayLastSecondStr()
      },
      params: {
        cblb: [],
        ksname: ''
      },
      dataList: [],
      loading: true,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      showReport: false,
      reportUrl: ''
    }
  },
  methods: {
    load,
    async handleSearch(){
      this.loading = true
      const res = await ysdrgtj({
        ...this.params,
        ...this.dateParams
      }, {
        pageSize: this.pageSize,
        pageNum: this.pageNum})

      this.dataList = res.rows
      this.total = res.total
      this.loading = false
    },
    handleExport(){

    },
    async openReport(val){
      let ip = await getFzPageIp()
      this.reportUrl = `http://${ip}:8096/jmreport/view/839032496843395072?zyys=${val.ys}&ks=${val.cykb}&startDate=${this.dateParams.startDate}&endDate=${this.dateParams.endDate}&datetype=${this.dateParams.datetype}`
      this.showReport = true
    }
  },
  created() {
    this.handleSearch()
  }
}
</script>

<style>

</style>
