<template>
  <div class="app-container">
    <div>
      <el-form :model="params" ref="queryForm" size="small" :inline="true" label-width="68px">
        <dept-conditions v-model="params.dept"/>
        <cblb-options v-model="params.cblb"/>
        <el-form-item label="数据维度">
          <el-select v-model="params.depttype">
            <el-option v-for="item in datetypeOptions" :key="item.key" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="显示诊断">
          <el-select v-model="params.showzd">
            <el-option key="是" value="1" label="是"></el-option>
            <el-option key="否" value="0" label="否"></el-option>
          </el-select>
        </el-form-item>
        <br>
        <el-form-item>
          <date-filter v-model="dateParams"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="dataList"
                style="width: 100%"
                :border="true"
                v-loading="loading">
        <el-table-column prop="kszmc" label="科室组名称" align="center" width="100" show-overflow-tooltip/>
        <el-table-column prop="ys" label="医生" align="center" width="100"/>
        <el-table-column prop="zd" label="诊断" align="center" v-if="params.showzd === '1'" width="180" show-overflow-tooltip/>
        <el-table-column prop="sscz" label="手术操作" align="center" v-if="params.showzd === '1'" width="180" show-overflow-tooltip/>
        <el-table-column prop="drgbm" label="DRG编码" align="center" width="100"/>
        <el-table-column prop="drgmc" label="DRG名称" align="center" width="180" show-overflow-tooltip/>
        <el-table-column prop="rzbas" label="入组病案数" align="center" width="100"/>
        <el-table-column prop="cmi" label="CMI" align="center" width="80"/>
        <el-table-column prop="zqz" label="总权重" align="center" width="100"/>
        <el-table-column prop="sjxhzs" label="时间消耗指数" align="center" width="100"/>
        <el-table-column prop="fyxhzs" label="费用消耗指数" align="center" width="100"/>
        <el-table-column prop="pjzyts" label="平均住院天数" align="center" width="100"/>
        <el-table-column prop="zfy" label="总费用" align="center" width="100"/>
        <el-table-column prop="ljyk" label="例均盈亏" align="center" width="100"/>
        <el-table-column prop="zfbz" label="支付标准" align="center" width="100"/>
        <el-table-column prop="swrs" label="死亡人数" align="center" width="100"/>
        <el-table-column prop="dfxswrs" label="低风险死亡人数" align="center" width="100"/>
        <el-table-column prop="ypfy" label="药品费用" align="center" width="100"/>
        <el-table-column prop="zhfwfy" label="综合服务类费用" align="center" width="100"/>
        <el-table-column prop="zdlfy" label="诊断类费用" align="center" width="100"/>
        <el-table-column prop="zllfy" label="治疗类费用" align="center" width="100"/>
        <el-table-column prop="kfl" label="康复类" align="center" width="100"/>
        <el-table-column prop="zyl" label="中医类" align="center" width="100"/>
        <el-table-column prop="xf" label="血费" align="center" width="100"/>
        <el-table-column prop="cllfy" label="材料类费用" align="center" width="100"/>
        <el-table-column prop="qtfy" label="其他费用" align="center" width="100"/>
        <el-table-column prop="fyhj" label="费用合计" align="center" width="100"/>
        <el-table-column prop="jcfyzdlfybf" label="检查费用诊断类费用部分" align="center" width="100"/>
        <el-table-column prop="jyfyzdlfybf" label="检验费用诊断类费用部分" align="center" width="100"/>
        <el-table-column prop="yzb" label="药占比" align="center" width="100"/>
        <el-table-column prop="czb" label="耗占比" align="center" width="100"/>
        <el-table-column prop="yxsr" label="有效收入" align="center" width="100"/>
        <el-table-column prop="ycyk" label="预测盈亏" align="center" width="100"/>
        <el-table-column prop="zcblrs" label="正常倍率人数" align="center" width="100"/>
        <el-table-column prop="gblrs" label="高倍率人数" align="center" width="100"/>
        <el-table-column prop="wrzrs" label="未入组人数" align="center" width="100"/>
        <el-table-column prop="dblrs" label="低倍率人数" align="center" width="100"/>
        <el-table-column prop="zytsxy2t" label="住院天数小于2天" align="center" width="100"/>
        <el-table-column prop="zytsdy60t" label="住院天数大于60天" align="center" width="100"/>
        <el-table-column prop="pjfy" label="平均费用" align="center" width="100"/>
      </el-table>
    </div>
    <div>
      <pagination
        :page="pageNum"
        :limit.sync="pageSize"
        :total.sync="total"
        @pagination="handleSearch"
      ></pagination>
    </div>
  </div>
</template>

<script>
import DeptConditions from '../../../components/DeptConditions/index.vue'
import CblbOptions from '../../../components/CblbOptions/CblbOptions.vue'
import DateFilter from '../../../components/DateFilter/DateFilter.vue'
import { getCurDayStr, getMonthFirstDayStr, getTodayLastSecondStr } from '../../../utils/dateUtils'
import { getKsdrgfx } from '../../../api/tjfx/ksdrgfx'

export default {
  name: 'Ksdrgfx',
  components: { DateFilter, CblbOptions, DeptConditions },
  data(){
    return{
      dataList: [],
      dateParams: {
        datetype: 'jsdate',
        startDate: getMonthFirstDayStr(),
        endDate: getTodayLastSecondStr()
      },
      params: {
        dept: '',
        cblb: [],
        showzd: '1',
        depttype: 'dept'
      },
      loading: true,
      datetypeOptions: [
        {
          label: '医生',
          value: 'doctor',
          key: '医生'
        },
        {
          label: '科室',
          value: 'dept',
          key: '科室'
        }
      ],
      pageNum: 1,
      pageSize: 10,
      total: 0
    }
  },
  methods: {
    async handleSearch(){
      console.log({
        ...this.params,
        ...this.dateParams
      }, '参数')
      this.loading = true
      const res = await getKsdrgfx({
        ...this.params,
        ...this.dateParams
      }, {pageSize : 10, pageNum : 1})
      this.dataList = res.rows
      this.total = res.total
      this.loading = false
    },
    handleExport(){
      this.download('/tjfx/tjfx/ksdrgfx/export', {
        ...this.params,
        ...this.dateParams
      }, `科室DRG分析_${getCurDayStr()}.xlsx`)
    }
  },
  created() {
    this.handleSearch()
  }
}
</script>

<style scoped lang="scss">

</style>
