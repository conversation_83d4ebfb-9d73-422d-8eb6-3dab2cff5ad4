<script>
import { addKszd, changeKszdStatus, editKszd, getKszdList } from '@/api/tjfx/kszd'
import { changeUserStatus } from '@/api/system/user'

export default {
  name: 'Kszd',
  data(){
    return {
      deptConsolidationList: [],
      params: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      newConsolidationObj: {},
      openEditDialog: false,
      kszdObj: {
        status: 1
      },
      statusDict: [
        {
          value : 1,
          label: '启用'
        },
        {
          value: 0,
          label: '停用'
        }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList(){
      const res = await getKszdList(this.params);
      this.deptConsolidationList = res.rows
      this.total = res.total
    },
    openAddDialog(){
      this.kszdObj = { status : 1 }
      this.openEditDialog = true
    },
    async submitForm(){
      if(this.kszdObj.id) {
        const res = await editKszd(this.kszdObj)
        if(res.code == '200') {
          this.$modal.msgSuccess("成功");
        }
      } else {
        const res = await addKszd(this.kszdObj)
        if(res.code == '200') {
          this.$modal.msgSuccess("成功");
        }
      }
      this.getList()
      this.openEditDialog = false
    },
    cancel(){
      this.openEditDialog = false
    },
    openEdit(val){
      this.kszdObj = { ...val }
      this.openEditDialog = true
    },
    handleStatusChange(row){
      let text = row.status == 0 ? "停用" : "启用";
      this.$modal.confirm('确认要"' + text + '该映射吗？').then(function() {
        return changeKszdStatus(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === 0 ? 1 : 0;
      });
    }
  }
}
</script>

<template>
  <div class="app-container">
    <div class="container">
      <div>
        <el-form :model="params" ref="queryForm" class="searchForm" :inline="true">
          <el-form-item label="原名称">
            <el-input
              v-model="params.sourceDeptName"
              placeholder="请输入字段名称"
              clearable
              @keyup.enter.native="getList"/>
          </el-form-item>
          <el-form-item label="映射名称">
            <el-input
              v-model="params.targetDeptName"
              placeholder="请输入字段名称"
              clearable
              @keyup.enter.native="getList"/>
          </el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
        </el-form>
      </div>
      <div>
        <div style="margin-bottom: 5px">
          <el-button type="primary" size="mini" @click="openAddDialog">新增</el-button>
        </div>
        <div>
          <el-table style="width: 100%" height="600" :border="true" :data="deptConsolidationList">
            <el-table-column  label="ID" align="center" prop="id" show-overflow-tooltip/>
            <el-table-column  label="原名称" align="center" prop="sourceDeptName" show-overflow-tooltip/>
            <el-table-column  label="映射名称" align="center" prop="targetDeptName" show-overflow-tooltip/>
            <el-table-column label="状态" align="center" key="status">
              <template v-slot="scope">
                <el-switch
                  v-model="scope.row.status"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleStatusChange(scope.row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column  label="操作" align="center" prop="id" show-overflow-tooltip>
              <template v-slot="scope">
                <div>
                  <el-button type="text" @click="openEdit(scope.row)">修改</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            :total="total"
            :page.sync="params.pageNum"
            :limit.sync="params.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>

    <div>

    </div>

    <el-dialog title="信息设置" :visible.sync="openEditDialog" width="800px" append-to-body>
      <div>
        <el-form :model="kszdObj" ref="objForm" class="searchForm" :inline="true">
          <el-row>
            <el-col :span="12">
              <el-form-item label="原名称">
                <el-input
                  v-model="kszdObj.sourceDeptName"
                  placeholder="请输入字段名称"
                  clearable/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="映射名称">
                <el-input
                  v-model="kszdObj.targetDeptName"
                  placeholder="请输入字段名称"
                  clearable/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="状态">
                <el-radio-group v-model="kszdObj.status">
                  <el-radio
                    v-for="dict in statusDict"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dict.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>
