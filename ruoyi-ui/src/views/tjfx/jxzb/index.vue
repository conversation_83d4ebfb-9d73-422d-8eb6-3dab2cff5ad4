<template>
  <div class="container-page">
    <div>
      <el-form :inline="true">
        <el-form-item label="参保类别">
          <el-select v-model="queryParam.cblb" placeholder="选择参保类别">
            <el-option
              v-for="item in cblbOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="数据类型">
          <el-select v-model="queryParam.dataType" placeholder="选择数据类型">
            <el-option
              :key="1"
              :label="'病组'"
              :value="1">
            </el-option>
            <el-option :key="1" label="科室" :value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="科室">
          <el-select v-model="queryParam.deptname" placeholder="选择科室" @change="deptChange">
            <el-option
              v-for="item in deptOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="医生" v-if="doctorOptionsVisible">
          <el-select v-model="queryParam.doctor" placeholder="选择医生">
            <el-option
              v-for="item in doctorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <br>

        <el-form-item label="时间类型">
          <el-select v-model="queryParam.datetype" placeholder="时间类型">
            <el-option
              v-for="item in datetypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="开始时间">
            <el-date-picker
              v-model="startDate"
              type="datetime"
              placeholder="选择开始时间"
              default-time="00:00:00">
            </el-date-picker>
        </el-form-item>

        <el-form-item label="结束时间">
          <el-date-picker
            v-model="endDate"
            type="datetime"
            placeholder="选择结束时间"
            default-time="23:59:59">
          </el-date-picker>
        </el-form-item>
        <el-button type="primary" @click="submit">搜索</el-button>
        <el-button type="success" @click="handleExport">导出</el-button>
      </el-form>
    </div>
    <div class="container-sub">
      <el-row :gutter="24">
        <el-col :span="3">
          <div>
            <el-statistic :value="hzData.drgzs" :title="'入组病例数'" :precision="0"></el-statistic>
          </div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-statistic :value="hzData.zfy" title="总费用" :precision="0"></el-statistic>
          </div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-statistic :value="hzData.yk" title="总盈亏" :precision="0"></el-statistic>
          </div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-statistic :value="hzData.cjfy" title="次均费用" :precision="0"></el-statistic>
          </div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-statistic :value="hzData.brpjdays" title="平均住院天数" :precision="0"></el-statistic>
          </div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-statistic :value="hzData.shortterm" title="住院小于两天" :precision="0"></el-statistic>
          </div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-statistic :value="hzData.longterm" title="住院大于60天" :precision="0"></el-statistic>
          </div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-statistic :value="total" title="总病组数" :precision="0"></el-statistic>
          </div>
        </el-col>
      </el-row>
    </div>
    <div>
      <el-table
       v-loading="loading"
      :data="dataList"
      style="width: 100%"
      :border="true"
      >
        <el-table-column
          fixed
          prop="drgmc"
          label="DRG名称"
          align="center"
          width="150"
          v-if="dataShowType === 1">
        </el-table-column>
        <el-table-column
          prop="drgbh"
          label="DRG编号"
          align="center"
          width="150"
          v-if="dataShowType === 1">
        </el-table-column>
        <el-table-column
          fixed
          prop="cykb"
          label="科室"
          align="center"
          width="150"
          v-if="dataShowType === 2">
        </el-table-column>
        <el-table-column
          prop="drgzs"
          label="入组病例数"
          align="center"
          width="90">
        </el-table-column>
        <el-table-column
          prop="zqz"
          label="总权重"
          align="center"
          width="80">
        </el-table-column>
        <el-table-column
          prop="zfqz"
          label="病组权重"
          align="center"
          width="80">
        </el-table-column>
        <el-table-column
          prop="mdczs"
          label="MDC总数"
          align="center"
          width="80">
        </el-table-column>
        <el-table-column
          prop="jbdmsl"
          label="疾病数量"
          align="center"
          width="80">
        </el-table-column>
        <el-table-column
          prop="fyxhzs"
          label="费用消耗指数"
          align="center"
          width="80">
        </el-table-column>
        <el-table-column
          prop="sjxhzs"
          label="时间消耗指数"
          align="center"
          width="80">
        </el-table-column>
        <el-table-column
          prop="zfy"
          label="总费用"
          align="center"
          width="160">
        </el-table-column>
        <el-table-column
          prop="yk"
          label="盈亏金额"
          align="center"
          width="160">
        </el-table-column>
        <el-table-column
          prop="cjfy"
          label="次均费用"
          align="center"
          width="160">
        </el-table-column>
        <el-table-column
          prop="brpjdays"
          label="平均住院天数"
          align="center"
          width="80">
        </el-table-column>
        <el-table-column
          prop="zfbzb"
          label="支付标准比"
          align="center"
          width="160">
        </el-table-column>

        <el-table-column
          prop="jcfl"
          label="基础费率"
          align="center"
          width="160"
          v-if="dataShowType === 1">
        </el-table-column>
        <el-table-column
          prop="zfbz"
          label="支付标准"
          align="center"
          width="160">
        </el-table-column>
        <el-table-column
          prop="cmi"
          label="CMI"
          align="center"
          width="90">
        </el-table-column>
        <el-table-column
          prop="shortterm"
          label="住院小于两天"
          align="center"
          width="90">
        </el-table-column>
        <el-table-column
          prop="longterm"
          label="住院大于60天"
          align="center"
          width="90">
        </el-table-column>
        <el-table-column
          prop="fyjd"
          label="费用极低数量"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="fyjg"
          label="费用极高数量"
          align="center"
          width="100">
        </el-table-column>
      </el-table>
    </div>
    <div>
      <el-pagination
        background
        layout="prev, pager, next"
        :current-page="pageNum"
        :page-size="pageSize"
        :total="total"
        @current-change="pageChange"
        @size-change="sizeChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getDeptnameList, getDoctorList, getZbfx } from '@/api/tjfx/jxzb'
import { Loading } from 'element-ui'

export default {
  name: 'Jxzb',
  data(){
    return {
      loading: true,
      startDate: new Date(),
      endDate: new Date(),
      doctorOptionsVisible : false,
      //科室
      deptOptions : [],
      // 医生
      doctorOptions : [],
      // 时间类型
      datetypeOptions: [
        {
          value: 'jsdate',
          label: '结算时间'
        },
        {
          value: 'cydate',
          label: '出院时间'
        }
      ],
      // 参保类别
      cblbOptions: [
        {
          value: '所有',
          label: '所有'
        },
        {
          value: '职工',
          label: '职工'
        },
        {
          value: '居民',
          label: '居民'
        },
        {
          value: '自费',
          label: '自费'
        },
        {
          value: '生育',
          label: '生育'
        }
      ],
      queryParam: {
        deptname : '全院',
        doctor: null,
        datetype : 'jsdate',
        startDate: '',
        endDate: '',
        cblb: '所有',
        dataType: 1
      },
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      hzData: {},
      // 数据维度类型，1病组  2科室
      dataShowType: 1
    }
  },
  methods: {
    // 选择科室之后的操作
    deptChange(val){
      console.log(this.queryParam)
      console.log(val)

      // 通过当前选择的科室获取医生，并更新doctorOptions
      this.queryParam.doctor = null
      if(val === '全院'){
        this.doctorOptionsVisible = false
        this.queryParam.doctor = null
      } else {
        getDoctorList(val).then(res => {
          this.doctorOptions = [];
          this.doctorOptions.push({
            value: '所有',
            label: '所有'
          })
          res.list.forEach(item => {
            this.doctorOptions.push({
              value: item,
              label: item
            })
          })
        })
        this.doctorOptionsVisible = true
      }
    },
    async getHzData(){
      const params = { ...this.queryParam }
      params.is_hz = 1
      // console.log(params, '汇总参数')
      getZbfx(params, this.pageNum, this.pageSize).then(res => {
        this.hzData = res.rows[0]
      })
    },
    submit(){
      // 格式化时间参数
      if(this.startDate && this.endDate) {
        this.queryParam.startDate = this.dateToString(this.startDate)
        this.queryParam.endDate = this.dateToString(this.endDate)
      }

      this.loading = true
      this.getHzData();
      // console.log(this.queryParam)
      getZbfx(this.queryParam, this.pageNum, this.pageSize).then(res => {
        this.dataList = res.rows
        this.total = res.total
        this.dataShowType = this.queryParam.dataType
        this.loading = false
      })
    },
    async initData(){
      let now = new Date();
      var year = now.getFullYear()
      var month = now.getMonth() + 1
      var day = now.getDate()

      // this.endDate = year + '-' + this.padZero(month) + '-' + this.padZero(day) + ' 23:59:59'
      // this.startDate = year + '-' + this.padZero(month) + '-01 00:00:00'

      this.endDate = new Date(year, month - 1, day, 23, 59, 59)
      this.startDate = new Date(year, month - 1, 1, 0, 0, 0)

      const deptnameListRes = await getDeptnameList()
      this.deptOptions = [];
      if(!deptnameListRes.is_ys) {
        this.deptOptions.push({
          value: '全院',
          label: '全院'
        });
      }
      const dataMap = []
      deptnameListRes.list.forEach(item => {
        this.deptOptions.push({
          value: item,
          label: item
        })
      })
      this.queryParam.deptname = this.deptOptions[0].value
      this.deptChange(this.deptOptions[0].value)
      this.submit()
    },
    padZero(num) {
      return num < 10 ? '0' + num : num
    },
    dateToString(date) {
      var fullYear = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hours = date.getHours()
      var minutes = date.getMinutes()
      var seconds = date.getSeconds()

      return fullYear + '-' +
        this.padZero(month) + '-' +
        this.padZero(day) +' ' +
        this.padZero(hours) + ':' +
        this.padZero(minutes) + ':' +
        this.padZero(seconds)
    },
    pageChange(val){
      this.pageNum = val
      this.submit()
    },
    sizeChange(val){
      this.pageSize = val
      this.submit()
    },
    handleExport(){
      this.download("/tjfx/tjfx/zbfx/export", {
        ...this.queryParam
      }, `指标分析数据_${new Date().getTime()}.xlsx`)
    }
  },
  created() {
    this.initData()
  }
}
</script>

<style scoped lang="scss">
.container-page {
  padding: 20px;
  .container-sub {
    margin-bottom: 10px;
  }
}
</style>
