<template>
  <div>
    <div class="app-container">

      <div v-loading="loading">
        <div>
          <el-form style="margin-left: 1%; width: 98%;" :inline="true">

            <el-form-item label="参保类别">
              <el-select v-model="queryParams.cblb" placeholder="选择参保类别">
                <el-option
                  v-for="item in cblbOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="科室">
              <el-select v-model="queryParams.cykb"
                         @focus="resetDept"
                         filterable
                         :filter-method="deptFilter"
                         placeholder="科室"
                         clearable
              >
                <el-option
                  v-for="(item,index) in deptList"
                  :value="item.cykb"
                  :key="index"
                  :label="item.cykb">
                </el-option>
              </el-select>
            </el-form-item>


            <el-form-item label="记录来源">
              <el-select v-model="queryParams.jlly" placeholder="记录来源">
                <el-option
                  v-for="item in jllyOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="时间类型">
              <el-select v-model="queryParams.datetype" placeholder="时间类型" clearable>
                <el-option
                  v-for="item in datetypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>

            <br>

            <el-form-item label="开始时间">
              <el-date-picker
                v-model="startDateTiem"
                type="datetime"
                placeholder="选择开始时间"
                format="yyyy-MM-dd HH:mm:ss"
                default-time="00:00:00">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="结束时间">
              <el-date-picker
                v-model="endDateTiem"
                type="datetime"
                placeholder="选择结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                default-time="23:59:59">
              </el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="clearQueryparams">重置</el-button>
            </el-form-item>
          </el-form>
        </div>


        <div>

          <div class="main-content">
            <div class="item" v-for="(item,index) in dataList">
              <el-card shadow="hover" @click.native="handleCardClick(item)">
                <div>
                  <div class="title">
                    <div>{{item.title}}</div>
                    <div class="value">
                      {{item.value}}%
                    </div>
                  </div>
                  <div class="num">
                    <div>{{item.name}}</div>
                    <div>{{item.num}}</div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>

          <!-- 抽屉组件 -->
          <el-drawer
            title="主要诊断选择正确率明细"
            :visible.sync="drawerVisible"
            size="70%"
            direction="rtl"
          >
            <div class="drawer-table-wrapper">
              <el-table :data="drawerTableData" style="width: 100%" :border="true" v-loading="diagDiffLoading">
                <el-table-column prop="bah" label="住院号" width="120" />
                <el-table-column prop="xm" label="病人姓名" width="110" />
                <el-table-column prop="cykb" label="科室" min-width="160" />
                <el-table-column prop="zyys" label="医生" width="110" />
                <el-table-column label="医生主诊断编码" min-width="140">
                  <template slot-scope="scope">
                    <span v-if="scope.row.drDiagCode" :class="{'diff-highlight-dr': scope.row.drDiagCode && scope.row.drDiagCode !== scope.row.mrDiagCode}">{{scope.row.drDiagCode}}</span>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
                <el-table-column label="医生主诊断" min-width="180">
                  <template slot-scope="scope">
                    <span v-if="scope.row.drDiagName" :class="{'diff-highlight-dr': scope.row.drDiagName && scope.row.drDiagName !== scope.row.mrDiagName}">{{scope.row.drDiagName}}</span>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
                <el-table-column label="病案主诊断编码" min-width="140">
                  <template slot-scope="scope">
                    <span v-if="scope.row.mrDiagCode" :class="{'diff-highlight-mr': scope.row.mrDiagCode && scope.row.drDiagCode !== scope.row.mrDiagCode}">{{scope.row.mrDiagCode}}</span>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
                <el-table-column label="病案室主诊断" min-width="180">
                  <template slot-scope="scope">
                    <span v-if="scope.row.mrDiagName" :class="{'diff-highlight-mr': scope.row.mrDiagName && scope.row.drDiagName !== scope.row.mrDiagName}">{{scope.row.mrDiagName}}</span>
                    <span v-else>--</span>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                :limit.sync="diagDiffDetailsPageParam.pageSize"
                :page.sync="diagDiffDetailsPageParam.pageNum"
                :total="diagDiffDetailsTotal"
                @pagination="getDiagDiffDetails"
              />
            </div>
          </el-drawer>

        </div>

      </div>

    </div>
  </div>
</template>

<script>
import PinYinMatch from "pinyin-match";
import { getDeptListByBas, getDeptListByBasAndTime } from '@/api/drg/syjl'
import { getBaZbfx, getDiagDiffDetails } from '@/api/tjfx/tjfx'
import {dateToString, getMonthFirstDay, getTodayLastSecond} from "@/utils/dateUtils";

export default {
  name: "index",
  data() {
    return {
      loading: false,
      diagDiffLoading: false,
      diagDiffDetailsPageParam: {
        pageNum: 1,
        pageSize: 10
      },
      diagDiffDetailsTotal: 0,
      queryParams: {
        date: null,
        cblb: null,
        datetype: 'cydate',
        jlly: "4",
        adtFrom: null,
        adtTo: null
      },
      startDateTiem: getMonthFirstDay(),
      endDateTiem: getTodayLastSecond(),
      showSearch: true,
      deptList:[],
      deptListby:[],
      datetypeOptions: [
        {
          value: 'jsdate',
          label: '结算时间'
        },
        {
          value: 'cydate',
          label: '出院时间'
        }
      ],
      cblbOptions: [
        {
          value: '%',
          label: '所有'
        },
        {
          value: '职工',
          label: '职工'
        },
        {
          value: '居民',
          label: '居民'
        },
        {
          value: '自费',
          label: '自费'
        },
        {
          value: '生育',
          label: '生育'
        }
      ],
      jllyOptions: [
        {
          label:"医生",
          value:"3"
        },
        {
          label:"病案室",
          value:"4"
        }
      ],
      dataList: [{
        title:'首页填报完整率',
        name:'首页填充完整例数',
        value: 0,
        num: 0,
      },{
        title:'首页项目填报完整率',
        name:'首页项目填报的必填项目之和',
        value: 0,
        num: 0,
      },{
        title:'主要诊断选择正确率',
        name:'主要诊断选择正确例数',
        value: 0,
        num: 0,
      },{
        title:'主要手术及操作选择正确率',
        name:'主要手术及操作选择正确例数',
        value: 0,
        num: 0,
      },{
        title:'其他诊断填写完整正确率',
        name:'其他诊断填写完整正确例数',
        value: 0,
        num: 0,
      },{
        title:'手术及操作编码正确率',
        name:'手术及操作编码正确例数',
        value: 0,
        num: 0,
      },{
        title:'主要诊断编码与名称符合率',
        name:'主要诊断编码与名称符合例数',
        value: 0,
        num: 0,
      },{
        title:'其他诊断编码与名称符合率',
        name:'其他诊断编码与名称符合例数',
        value: 0,
        num: 0,
      },{
        title:'首页数据质量优秀率',
        name:'首页数据质量优秀数',
        value: 0,
        num: 0,
      },{
        title:'主要诊断修正率',
        name:'主要诊断修正例数',
        value: 0,
        num: 0,
      },{
        title:'主要手术修正率',
        name:'主要手术修正例数',
        value: 0,
        num: 0,
      },{
        title:'入组修正率',
        name:'入组修正率',
        value: 0,
        num: 0,
      }],
      drawerVisible: false,
      drawerTableData: [],
    }
  },
  methods: {
    getDiagDiffDetails(){
      this.diagDiffLoading = true
      this.buildQueryParams()
      let params = { ...this.queryParams, ...this.diagDiffDetailsPageParam }
      getDiagDiffDetails(params).then(res => {
        this.drawerTableData = res.rows
        this.diagDiffDetailsTotal = res.total
        this.diagDiffLoading = false
      })
    },
    clearQueryparams() {
      this.queryParams = {
        date: null,
        cblb: null,
        datetype: "cydate",
        jlly: "4",
        adtFrom: null,
        adtTo: null
      }
      this.startDateTiem = getMonthFirstDay()
      this.endDateTiem = getTodayLastSecond()
      this.handleQuery()
    },
    resetDept() {
      this.deptList = this.deptListby
    },
    deptFilter(val) {
      this.queryParams.cykb = val
      if (val) {
        this.deptList = []
        var deptList = this.deptListby.filter((item) => {
          if (PinYinMatch.match(item.cykb, val)) {
            return true
          }
        })
        this.deptList = deptList
      } else {
        this.deptList = this.deptListby
      }
    },
    buildQueryParams() {
      if (this.startDateTiem != null && this.startDateTiem != '') {
        this.queryParams.adtFrom = dateToString(this.startDateTiem)
      } else {
        this.queryParams.adtFrom = null;
      }
      if (this.endDateTiem != null && this.endDateTiem != '') {
        this.queryParams.adtTo = dateToString(this.endDateTiem)
      } else {
        this.queryParams.adtTo = null;
      }

      if (this.queryParams.adtTo == null && this.queryParams.adtFrom == null && this.queryParams.datetype != null) {
        this.queryParams.datetype = null;
      }

      console.log(this.queryParams)
      if ((this.queryParams.adtTo != null || this.queryParams.adtFrom != null) && (this.queryParams.datetype == null || this.queryParams.datetype == "")) {
        this.$modal.msgWarning("请选择日期类型")
      }
    },
    handleQuery() {
      this.buildQueryParams()
      this.getList();
    },
    clearData() {
      this.dataList = [{
        title:'首页填报完整率',
        name:'首页填充完整例数',
        value: 0,
        num: 0,
      },{
        title:'首页项目填报完整率',
        name:'首页项目填报的必填项目之和',
        value: 0,
        num: 0,
      },{
        title:'主要诊断选择正确率',
        name:'主要诊断选择正确例数',
        value: 0,
        num: 0,
      },{
        title:'主要手术及操作选择正确率',
        name:'主要手术及操作选择正确例数',
        value: 0,
        num: 0,
      },{
        title:'其他诊断填写完整正确率',
        name:'其他诊断填写完整正确例数',
        value: 0,
        num: 0,
      },{
        title:'手术及操作编码正确率',
        name:'手术及操作编码正确例数',
        value: 0,
        num: 0,
      },{
        title:'主要诊断编码与名称符合率',
        name:'主要诊断编码与名称符合例数',
        value: 0,
        num: 0,
      },{
        title:'其他诊断编码与名称符合率',
        name:'其他诊断编码与名称符合例数',
        value: 0,
        num: 0,
      },{
        title:'首页数据质量优秀率',
        name:'首页数据质量优秀数',
        value: 0,
        num: 0,
      },{
        title:'主要诊断修正率',
        name:'主要诊断修正例数',
        value: 0,
        num: 0,
      },{
        title:'主要手术修正率',
        name:'主要手术修正例数',
        value: 0,
        num: 0,
      },{
        title:'入组修正率',
        name:'入组修正例数',
        value: 0,
        num: 0,
      }]
    },
    getList() {
      if (this.queryParams.cblb == null) {
        this.queryParams.cblb = "";
      }

      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDate && '' != this.daterangeDate) {
        this.queryParams.adtFrom = this.daterangeDate[0];
        this.queryParams.adtTo = this.daterangeDate[1];
      }


      this.loading = true;
      getBaZbfx(this.queryParams).then(res => {

        console.log(res)

        if(res.total <= 0) {
          this.loading = false
          this.$modal.msgWarning("不存在病案数据")
          this.clearData()
          return
        }

        this.dataList = res.rows
        this.loading = false
      }).catch(error => {

        this.loading = false
        this.clearData()

        console.log(error)
      })
    },
    getDeptList(){
      getDeptListByBasAndTime({
        cydateStart: this.startDateTiem,
        cydateEnd: this.endDateTiem
      }).then(res=>{
        this.deptList = res.rows
        this.deptListby = JSON.parse(JSON.stringify(this.deptList))
      })
    },
    handleCardClick(item) {
      console.log(item)
      if (item.title === '主要诊断选择正确率') {
        this.drawerVisible = true;
        this.getDiagDiffDetails()
      }
    },
    expandRow(row) {
      // 展开操作逻辑，后续可补充
      this.$message.info('展开: ' + row.zyh);
    },
  },
  watch: {
    startDateTiem(val, oldVal) {
      if (val !== oldVal) {
        this.getDeptList();
      }
    },
    endDateTiem(val, oldVal) {
      if (val !== oldVal) {
        this.getDeptList();
      }
    }
  },
  created() {
    this.handleQuery()
    this.getDeptList()
  }
}
</script>

<style scoped lang="scss">
.main-content {
  width: 100%;
  display: flex;
  justify-content: left;
  flex-wrap: wrap;
  .item {
    width: 32%;
    margin-top: 5px;
    margin-left: 1%;
  }
}
.value {
  background-color: #f8f8f9;
  width: 40%;
  height: 45%;
  margin-top: 10px;
  font-size: 35px;
  text-align: right;
  color: #93bbe9;
}
.num {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}
.title {
  display: flex;
  justify-content: space-between;
}

// 抽屉表格美化
.drawer-table-wrapper {
  padding: 24px 24px 0 24px;
  box-sizing: border-box;
}

.diff-highlight-dr {
  background: #ffeded !important;
  color: #ff4d4f;
  border-radius: 3px;
  padding: 2px 4px;
  font-weight: bold;
}
.diff-highlight-mr {
  background: #e6f7ff !important;
  color: #1890ff;
  border-radius: 3px;
  padding: 2px 4px;
  font-weight: bold;
}
</style>
