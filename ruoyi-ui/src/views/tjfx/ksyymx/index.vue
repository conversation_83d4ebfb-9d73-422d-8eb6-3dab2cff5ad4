<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <dept-doctor-selector v-model="selectedData"/>
      <drg-selector v-model="queryParams.drgbh"/>
      <el-form-item label="结算日期">
        <el-date-picker
          v-model="queryParams.adtFrom"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.adtTo"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>


    <el-table border v-loading="loading" :data="ksyyList">
      <el-table-column show-overflow-tooltip width="150" label="科室" align="left" prop="cykb"/>
      <el-table-column show-overflow-tooltip label="医生" align="left" prop="zyys"/>
      <el-table-column show-overflow-tooltip label="DRG编号" align="left" prop="drgbh"/>
      <el-table-column show-overflow-tooltip width="250" label="DRG名称" align="left" prop="drgmc"/>
      <el-table-column show-overflow-tooltip width="120" label="住院号" align="left" prop="zyh"/>
      <el-table-column show-overflow-tooltip width="120" label="类型" align="left" prop="type"/>
      <el-table-column show-overflow-tooltip width="350" label="亏损详情" align="left" prop="details"/>
      <el-table-column show-overflow-tooltip width="150" label="项目名称" align="left" prop="xmmc"/>
      <el-table-column show-overflow-tooltip label="病人费用" align="left" prop="brfy"/>
      <el-table-column show-overflow-tooltip width="130" label="其他病人平均费用" align="left" prop="pjfy"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import DeptDoctorSelector from '@/components/DeptDoctorSelector';
import DrgSelector from '../../../components/DrgSelector';
import {listKsyymx} from "@/api/drg/ksyy";
import {getTodayLastSecondStr, getYesterdayFirstSecondStr} from "@/utils/dateUtils";

export default {
  name: "Ksyymx",
  components: {DeptDoctorSelector, DrgSelector},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 病案亏损原因表格数据
      ksyyList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        cykb: '所有',
        zyys: null,
        adtFrom: getYesterdayFirstSecondStr(),
        adtTo: getTodayLastSecondStr(),
        drgbh: null
      },
      selectedData: {
        dept: null,
        doctor: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listKsyymx(this.queryParams).then(response => {
        this.ksyyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.cykb = this.selectedData.dept
      this.queryParams.zyys = this.selectedData.doctor
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        cykb: '所有',
        zyys: null,
        adtFrom: getYesterdayFirstSecondStr(),
        adtTo: getTodayLastSecondStr(),
        drgbh: null
      };
      this.selectedData.dept = '所有'
      this.selectedData.doctor = null
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('drg/ksyy/export', {
        ...this.queryParams
      }, `亏损明细_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
