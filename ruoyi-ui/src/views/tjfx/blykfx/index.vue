<template>

  <div class="app-container">
    <div>
      <el-form :model="params" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="startDate"
            type="datetime"
            placeholder="选择开始时间"
            default-time="00:00:00">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="结束时间">
          <el-date-picker
            v-model="endDate"
            type="datetime"
            placeholder="选择结束时间"
            default-time="23:59:59">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="时间类型">
          <el-select v-model="params.datetype">
            <el-option v-for="item in datetypeOptions" :key="item.key" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>

        <el-form-item label="倍率状态">
          <el-select v-model="params.type" clearable>
            <el-option v-for="item in typeOptions" :key="item.key" :value="item.value" :label="item.label"/>
          </el-select>
        </el-form-item>
        <br>
        <dept-conditions v-model="params.ksname" clearable></dept-conditions>

        <cblb-options v-model="params.cblb"></cblb-options>

        <el-form-item>
          <el-button @click="search" type="primary">搜索</el-button>
          <el-button @click="handleExport" type="success">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div>
      <el-table
        v-loading="loading"
        :data="dataList"
        style="width: 100%"
        :border="true">
        <el-table-column
          prop="bah"
          label="病案号"
          align="center"
          fixed="left"
          width="100">
          <template v-slot="scope">
            <el-button type="text" @click="showYkjl(scope.row)">
              {{ scope.row.bah }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="blzt"
          label="倍率状态"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="xm"
          label="姓名"
          align="center"
          width="100">
          <template v-slot="scope">
            <el-button type="text" @click="openYfz(scope.row)">{{ scope.row.xm }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="xb"
          label="性别"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="ylfkfs"
          label="医疗付款方式"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="nl"
          label="年龄"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="cykb"
          label="出院科别"
          show-overflow-tooltip
          align="center"
          width="120">
        </el-table-column>
        <el-table-column
          prop="zyys"
          label="住院医师"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="cydate"
          label="出院时间"
          show-overflow-tooltip
          align="center"
          width="110">
        </el-table-column>
        <el-table-column
          prop="rydate"
          label="入院时间"
          show-overflow-tooltip
          align="center"
          width="110">
        </el-table-column>
        <el-table-column
          prop="hisJsdate"
          label="结算时间"
          show-overflow-tooltip
          align="center"
          width="110">
        </el-table-column>
        <el-table-column
          prop="drgmc"
          label="DRG名称"
          show-overflow-tooltip
          align="center"
          width="150">
        </el-table-column>
        <el-table-column
          prop="drgbh"
          label="DRG编号"
          show-overflow-tooltip
          align="center"
          width="150">
        </el-table-column>
        <el-table-column
          prop="zfbz"
          label="支付标准"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="zfy"
          label="总费用"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="bl"
          label="倍率"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="sjzyts"
          label="实际住院天数"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="ykje"
          label="盈亏金额"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="jbdm"
          label="主要诊断"
          align="center"
          show-overflow-tooltip
          width="150">
        </el-table-column>
        <el-table-column
          prop="ssjczbm1"
          label="主要手术"
          align="center"
          show-overflow-tooltip
          width="150">
        </el-table-column>
        <el-table-column
          prop="zfje"
          label="自付金额"
          align="center"
          show-overflow-tooltip
          width="80">
        </el-table-column>
        <el-table-column
          prop="ypf"
          label="药品费"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="ypf_bg"
          label="药品标杆"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="jyf"
          label="检验费"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="ypf"
          label="药品费"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="jyf_bg"
          label="检验费标杆"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="jcf"
          label="检查费"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="jcf_bg"
          label="检查费标杆"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="ssf"
          label="手术费"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="ssf_bg"
          label="手术费标杆"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="zlf"
          label="诊疗费"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="zlf_bg"
          label="诊疗费标杆"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="hcf"
          label="耗材费"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="hcf_bg"
          label="耗材费标杆"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="tczf"
          label="统筹支付"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="drgzf"
          label="DRG支付"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="brbs"
          label="病人标识"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="pjdays"
          label="标杆床日"
          align="center"
          width="100">
        </el-table-column>
        <el-table-column
          prop="otherDiags"
          label="其他诊断"
          align="center"
          show-overflow-tooltip
          width="100">
        </el-table-column>
        <el-table-column
          prop="otherOpers"
          label="其他手术与操作"
          align="center"
          show-overflow-tooltip
          width="100">
        </el-table-column>
      </el-table>
    </div>

    <div>
      <el-dialog :visible.sync="ykjlVisible"
                 width="80%"
                 title="盈亏记录详情">
        <ykjl :row-data="bahForYkjl"/>
      </el-dialog>
    </div>

    <div>
      <el-pagination
        background
        :page-sizes="[10, 20, 50, 100]"
        layout="sizes,prev, pager, next"
        :current-page="pageNum"
        :page-size="pageSize"
        :total="total"
        @current-change="pageChange"
        @size-change="sizeChange"
      >
      </el-pagination>
    </div>
  </div>


</template>

<script>
import { blykfxData, getDeptList } from '../../../api/tjfx/blykfx'
import {
  dateToString,
  getMonthFirstDay,
  getMonthFirstDayStr, getTodayLastSecond,
  getTodayLastSecondStr
} from '../../../utils/dateUtils'
import DeptConditions from '../../../components/DeptConditions/index.vue'
import CblbOptions from '../../../components/CblbOptions/CblbOptions.vue'
import Ykjl from '../ykjl/Ykjl.vue'
import { getdk, getFzPageIp } from '../../../api/drg/syjl'

export default {
  name: 'Blykfx',
  components: { Ykjl, CblbOptions, DeptConditions },
  data(){
    return {
      loading: true,
      dataList: [],
      ykjlVisible : false,
      bahForYkjl: '',
      params: {
        datetype: 'jsdate',
        startDate: getMonthFirstDayStr(),
        endDate: getTodayLastSecondStr(),
        cblb: [],
        type: '',
        ksname: '所有',
        bah: ''
      },
      cblbSelect: [],
      datetypeOptions: [
        {
          label: '结算时间',
          key: 'jsdate',
          value: 'jsdate'
        },
        {
          label: '出院时间',
          key: 'cydate',
          value: 'cydate'
        }
      ],
      typeOptions: [
        {
          label: '01费用极高',
          key: '01费用极高',
          value: '01费用极高'
        },
        {
          label: '02费用极低',
          key: '02费用极低',
          value: '02费用极低'
        },
        {
          label: '03主诊不存在',
          key: '03主诊不存在',
          value: '03主诊不存在'
        },
        {
          label: '04歧义组',
          key: '04歧义组',
          value: '04歧义组'
        },
        {
          label: '05正常倍率',
          key: '05正常倍率',
          value: '05正常倍率'
        },
      ],
      deptnameList: ['所有'],
      startDate: getMonthFirstDay(),
      endDate: getTodayLastSecond(),
      pageNum: 1,
      pageSize: 10,
      total: 0,
    }
  },
  created() {
    // this.getDeptList()
    this.getList()
  },
  methods: {
    async getList(){
      this.params.startDate = dateToString(this.startDate)
      this.params.endDate = dateToString(this.endDate)
      this.loading = true
      const res = await blykfxData({...this.params}, {pageNum : this.pageNum, pageSize : this.pageSize})
      this.dataList = res.rows
      this.total = res.total
      this.loading = false
    },
    async search() {
      this.loading = true
      console.log(this.cblbSelect)
      await this.getList()
    },
    async getDeptList(){
      const deptRes = await getDeptList()
      this.deptnameList.push(...deptRes.list)
    },
    pageChange(val){
      this.pageNum = val
      this.getList()
    },
    sizeChange(val){
      this.pageSize = val
      this.getList()
    },
    handleExport(){
      this.params.startDate = dateToString(this.startDate)
      this.params.endDate = dateToString(this.endDate)
      this.download("/tjfx/tjfx/export/blykfx", {
        ...this.params
      }, `指标分析数据_${new Date().getTime()}.xlsx`)
    },
    showYkjl(val) {
      this.bahForYkjl = val.bah
      this.ykjlVisible = true
    },
    async openYfz(row) {
      const dk = await getdk()
      let ip = await getFzPageIp()
      // console.log(`http://${ip}:${dk.rows[0]}/views/drg/yfz?id=1&bah=${row.bah}`)
      window.open(`http://${ip}:${dk.rows[0]}/views/drg/yfz/index?id=1&bah=${row.bah}`)
    }
  }
}
</script>

<style scoped lang="scss">

</style>
