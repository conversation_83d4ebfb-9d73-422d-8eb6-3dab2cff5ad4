<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane name="drgfzmx" label="DRG分组明细">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="DRG编码" prop="drgbh">
            <el-input
              v-model="queryParams.drgbh"
              placeholder="请输入DRG编码"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="病案号" prop="bah">
            <el-input
              v-model="queryParams.bah"
              placeholder="请输入病案号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
            <el-button type="danger"  size="mini" @click="updateFz">更新分组</el-button>
          </el-form-item>
        </el-form>
        <el-table :border="true" v-loading="loading" :data="drgfzmxList">
          <el-table-column label="医疗机构编码" align="center" prop="yljgbm"/>
          <el-table-column label="医疗机构名称" align="left" prop="yljgmc"/>
          <el-table-column label="MDC编码" align="center" prop="mdcbh"/>
          <el-table-column label="MDC名称" align="left" prop="mdcmc"/>
          <el-table-column label="ADRG编码" align="center" prop="adrgbh"/>
          <el-table-column label="ADRG名称" align="left" prop="adrgmc"/>
          <el-table-column label="DRG编码" align="center" prop="drgbh"/>
          <el-table-column label="DRG名称" align="left" prop="drgmc"/>
          <el-table-column label="组别" align="center" prop="zb"/>
          <el-table-column label="病案号" align="center" prop="bah"/>
          <el-table-column label="结算ID" align="center" prop="jsid"/>
          <el-table-column label="姓名" align="center" prop="xm"/>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
        <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
          <el-upload
            ref="upload"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url + '?updateSupport=' + upload.updateSupport"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
              <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
            </div>
            <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
          </el-upload>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
          </div>
        </el-dialog>
      </el-tab-pane>
      <el-tab-pane name="drgydxxmx" label="DRG月度信息明细">
        <el-form :model="queryParamsZxbxmx" ref="queryFormZxbxmx" size="small" :inline="true" v-show="showSearchZxbxmx" label-width="68px">
          <el-form-item label="DRG编码" prop="drgbh">
            <el-input
              v-model="queryParamsZxbxmx.drgbh"
              placeholder="请输入DRG编码"
              clearable
              @keyup.enter.native="handleQueryZxbxmx"
            />
          </el-form-item>

          <el-form-item label="病案号" prop="bah">
            <el-input
              v-model="queryParamsZxbxmx.bah"
              placeholder="请输入病案号"
              clearable
              @keyup.enter.native="handleQueryZxbxmx"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryZxbxmx">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQueryZxbxmx">重置</el-button>
            <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImportZxbxmx">导入</el-button>
            <el-button type="danger"  size="mini" @click="updateBx">更新报销</el-button>
          </el-form-item>
        </el-form>
        <el-table :border="true" v-loading="loadingZxbxmx" :data="zxbxmxList">
          <el-table-column label="定点医疗机构编码" align="center" prop="jgid" />
          <el-table-column width="250" label="定点医疗机构名称" align="center" prop="yymc" />
          <el-table-column label="结算id" align="center" prop="setlId" />
          <el-table-column label="病案号" align="center" prop="bah" />
          <el-table-column label="人员编码" align="center" prop="psnNo" />
          <el-table-column label="姓名" align="center" prop="xm" />
          <el-table-column label="费率" align="center" prop="jcfl" />
          <el-table-column label="权重" align="center" prop="zfqz" />
          <el-table-column label="分组编码" align="center" prop="drgbh" />
          <el-table-column width="200" label="分组名称" align="center" prop="drgmc" />
          <el-table-column label="结算日期" align="center" prop="jsdate" width="180"/>
          <el-table-column label="住院医疗总费用" align="center" prop="zfy" />
          <el-table-column label="医疗机构系数" align="center" prop="yljgxs" />
          <el-table-column label="证件号码" align="center" prop="sfz" />
          <el-table-column label="年龄" align="center" prop="age" />
          <el-table-column label="性别" align="center" prop="xb" />
          <el-table-column label="医疗类别" align="center" prop="medType" />
          <el-table-column label="入院时间" align="center" prop="rydate" width="180"/>
          <el-table-column label="出院时间" align="center" prop="cydate" width="180"/>
          <el-table-column label="科室名称" align="center" prop="deptname" />
          <el-table-column label="实际支付起付线" align="center" prop="qbx" />
          <el-table-column label="超限价自费费用" align="center" prop="cxzf" />
          <el-table-column label="先行自付金额" align="center" prop="xxzf" />
          <el-table-column label="账户共济支付金额" align="center" prop="gjzf" />
          <el-table-column label="个人账户支出" align="center" prop="zhzf" />
          <el-table-column label="主要诊断代码" align="center" prop="jbdm" />
          <el-table-column label="主要诊断名称" align="center" prop="zdmc" />
          <el-table-column label="主要手术代码" align="center" prop="ssjczbm1" />
          <el-table-column label="主要手术名称" align="center" prop="ssjczmc1" />
          <el-table-column label="退费结算标志" align="center" prop="tfflag" />
          <el-table-column label="实际住院天数" align="center" prop="sjzyts" />
          <el-table-column label="分组类别" align="center" prop="fztype" />
          <el-table-column label="结算结果类型" align="center" prop="jsjglx" />
          <el-table-column label="DRG支付标准" align="center" prop="zfbz" />
          <el-table-column label="结付率" align="center" prop="jfl" />
          <el-table-column label="盈亏额" align="center" prop="yk" />
          <el-table-column label="职工统筹支付总金额" align="center" prop="zgtc" />
          <el-table-column label="DRG职工统筹支付总金额" align="center" prop="zgtcdrg" />
          <el-table-column label="居民统筹支付总金额" align="center" prop="jmtc" />
          <el-table-column label="DRG居民统筹支付总金额" align="center" prop="jmtcdrg" />
          <el-table-column label="公务员医疗补助资金支出" align="center" prop="gwybz" />
          <el-table-column label="DRG公务员医疗补助资金支出" align="center" prop="gwybzdrg" />
          <el-table-column label="大额支付总金额" align="center" prop="dezf" />
          <el-table-column label="DRG大额支付总金额" align="center" prop="dezfdrg" />
          <el-table-column label="大病支付总金额" align="center" prop="dbzf" />
          <el-table-column label="DRG大病支付总金额" align="center" prop="dbzfdrg" />
          <el-table-column label="一至六级残疾军人医疗补助基金" align="center" prop="scbz" />
          <el-table-column label="DRG一至六级残疾军人医疗补助基金" align="center" prop="scbzdrg" />
          <el-table-column label="保健基金" align="center" prop="bjj" />
          <el-table-column label="DRG保健基金" align="center" prop="bjjdrg" />
          <el-table-column label="保健预付基金" align="center" prop="bjyf" />
          <el-table-column label="DRG保健预付基金" align="center" prop="bjyfdrg" />
          <el-table-column label="企业补充医疗保险基金" align="center" prop="qybc" />
          <el-table-column label="DRG企业补充医疗保险基金" align="center" prop="qybcdrg" />
          <el-table-column label="居民意外伤害基金" align="center" prop="jmywsh" />
          <el-table-column label="DRG居民意外伤害基金" align="center" prop="jmywshdrg" />
          <el-table-column label="医疗救助基金支出" align="center" prop="yljz" />
          <el-table-column label="DRG医疗救助基金支出" align="center" prop="yljzdrg" />
          <el-table-column label="特惠保补偿金" align="center" prop="thb" />
          <el-table-column label="DRG特惠保补偿金" align="center" prop="thbdrg" />
          <el-table-column label="政府兜底基金" align="center" prop="zfdd" />
          <el-table-column label="DRG政府兜底基金" align="center" prop="zfdddrg" />
          <el-table-column label="职工大病保险基金" align="center" prop="zgdbbx" />
          <el-table-column label="DRG职工大病保险基金" align="center" prop="zgdbbxdrg" />
          <el-table-column label="其他基金" align="center" prop="qt" />
          <el-table-column label="DRG其他基金" align="center" prop="qtdrg" />
          <el-table-column label="医疗救助再救助基金" align="center" prop="yljzzjz" />
          <el-table-column label="DRG医疗救助再救助基金" align="center" prop="yljzzjzdrg" />
        </el-table>
        <pagination
          v-show="totalZxbxmx>0"
          :total="totalZxbxmx"
          :page.sync="queryParamsZxbxmx.pageNum"
          :limit.sync="queryParamsZxbxmx.pageSize"
          @pagination="getListZxbxmx"
        />
        <el-dialog :title="uploadZxbxmx.title" :visible.sync="uploadZxbxmx.open" width="400px">
          <el-upload
            ref="uploadZxbxmx"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="uploadZxbxmx.headers"
            :action="uploadZxbxmx.url + '?updateSupport=' + uploadZxbxmx.updateSupport"
            :disabled="uploadZxbxmx.isUploading"
            :on-progress="handleFileUploadProgressZxbxmx"
            :on-success="handleFileSuccessZxbxmx"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              <el-checkbox v-model="uploadZxbxmx.updateSupport" />是否更新已经存在的数据
              <el-link type="info" style="font-size:12px" @click="importTemplateZxbxmx">下载模板</el-link>
            </div>
            <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
          </el-upload>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileFormZxbxmx">确 定</el-button>
            <el-button @click="uploadZxbxmx.open = false">取 消</el-button>
          </div>
        </el-dialog>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {listDrgfzmx} from "@/api/tjfx/drgfzmx";
import {getToken} from "@/utils/auth";
import {updateBx, updateFz} from "@/api/drg/yfz"
import {importTemplate} from "@/api/gksz/kkmx";
import {listZxbxmx} from "@/api/tjfx/zxbxmx";
import DrgWordReport from '@/components/DrgWordReport/index.vue'

export default {
  name: "Drgfzmx",
  components: {
    DrgWordReport
  },
  data() {
    return {
      activeName: "drgfzmx",
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/tjfx/drgfzmx/importData"
      },
      uploadZxbxmx: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/tjfx/zxbxmx/importData"
      },
      // 遮罩层
      loading: true,
      loadingZxbxmx: true,
      // 显示搜索条件
      showSearch: true,
      showSearchZxbxmx: true,
      // 总条数
      total: 0,
      totalZxbxmx: 0,
      // DRG分组明细表格数据
      drgfzmxList: [],
      zxbxmxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        drgbh: null,
        bah: null,
      },
      queryParamsZxbxmx: {
        pageNum: 1,
        pageSize: 10,
        drgbh: null,
        bah: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList()
    try {
      this.getListZxbxmx()
    } catch (e) {
      this.$modal.msgWarning("无法获取DRG月度信息明细！")
    }
  },
  methods: {
    async updateFz() {
      let flag = true
      await updateFz().catch(err => {
        this.$modal.msgError("更新分组失败")
        flag = false
        return
      })
      if (flag) {
        this.$modal.msgSuccess("更新分组成功")
      }
    },
    async updateBx() {
      let flag = true
      await updateBx().catch(err => {
        this.$modal.msgError("更新报销失败")
        flag = false
        return
      })
      if (flag) {
        this.$modal.msgSuccess("更新报销成功")
      }
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 查询DRG分组明细列表 */
    getList() {
      this.loading = true;
      listDrgfzmx(this.queryParams).then(response => {
        this.drgfzmxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleImport() {
      this.upload.title = "DRG分组明细导入";
      this.upload.open = true;
    },
    // 表单重置
    reset() {
      this.form = {
        yljgbm: null,
        yljgmc: null,
        mdcbh: null,
        mdcmc: null,
        adrgbh: null,
        adrgmc: null,
        drgbh: null,
        drgmc: null,
        zb: null,
        bah: null,
        jsid: null,
        xm: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    getListZxbxmx() {
      this.loadingZxbxmx = true;
      listZxbxmx(this.queryParamsZxbxmx).then(response => {
        this.zxbxmxList = response.rows;
        this.totalZxbxmx = response.total;
        this.loadingZxbxmx = false;
      });
    },
    /** 下载模板操作 */
    importTemplateZxbxmx() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgressZxbxmx(event, file, fileList) {
      this.uploadZxbxmx.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccessZxbxmx(response, file, fileList) {
      this.uploadZxbxmx.open = false;
      this.uploadZxbxmx.isUploading = false;
      this.$refs.uploadZxbxmx.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getListZxbxmx();
    },
    // 提交上传文件
    submitFileFormZxbxmx() {
      this.$refs.uploadZxbxmx.submit();
    },
    handleImportZxbxmx() {
      this.uploadZxbxmx.title = "DRG分组明细导入";
      this.uploadZxbxmx.open = true;
    },

    /** 搜索按钮操作 */
    handleQueryZxbxmx() {
      this.queryParamsZxbxmx.pageNum = 1;
      this.getListZxbxmx();
    },
    /** 重置按钮操作 */
    resetQueryZxbxmx() {
      this.resetForm("queryFormZxbxmx");
      this.handleQueryZxbxmx();
    },

  }
};
</script>
