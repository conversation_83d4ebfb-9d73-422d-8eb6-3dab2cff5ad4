<template>
  <el-container style="height: 100vh; background: #ffffff; margin: auto">
    <el-header style="height: 60px; color: #000000; text-align: center; font-size: 24px; line-height: 60px; font-weight: bold">
      管控情况
    </el-header>
    <el-main style="height: 100%">
      <el-row :gutter="24" style="height: 47%">
        <el-col :span="7" class="chart-col">
          <div class="chart-container">
            <div id="wordcloud" class="chart"></div>
          </div>
        </el-col>
        <el-col :span="10" class="chart-col">
          <div class="chart-container">
            <div id="ksphChart" class="chart"></div>
          </div>
        </el-col>
        <el-col :span="7" class="chart-col">
          <div class="chart-container">
            <div id="ysphChart" class="chart"></div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="24" style="margin-top: 20px; height: 47%">
        <el-col :span="7" class="chart-col">
          <div class="chart-container">
            <div id="lxzbChart" class="chart"></div>
          </div>
        </el-col>
        <el-col :span="10" class="chart-col">
          <div class="chart-container">
            <div id="mrwgChart" class="chart"></div>
          </div>
        </el-col>
        <el-col :span="7" class="chart-col">
          <div class="chart-container">
            <div id="xzxmSlChart" class="chart"></div>
          </div>
        </el-col>
      </el-row>
    </el-main>
  </el-container>
</template>

<script>
import * as echarts from 'echarts';
import { getKsph, getLxzb, getMrwg, getXmcy, getXzxmSl, getYsph } from '@/api/tjfx/zkfx'
import screenfull from 'screenfull'
import {getDayAgoForNumStr, getTodayLastSecondStr} from "@/utils/dateUtils";

export default {
  data(){
    return{
      queryParam: {
        startDate: getDayAgoForNumStr(30),
        endDate: getTodayLastSecondStr(),
      },
      // 基本路径，默认为空根目录，如果你的项目发布后的地址为目录形式，
      // 即abc.com/tinymce，baseUrl需要配置成tinymce，不然发布后资源会找不到
      baseUrl: window.location.origin ? window.location.origin : '',
      ysphData: [],
      ksphData: [],
      lxzbData: [],
      xmcyData: [],
      mrwgData: [],
      xzxmSlData: [],
    }
  },
  mounted() {
    //监听键盘Esc按键事件
    let self = this;
    this.$nextTick(function() {
      document.addEventListener('keydown', function(e) {
        // console.log("有键盘按下", e)
        //此处填写你的业务逻辑即可
        if (e.code === 'F11') {
          // console.log('按下了F11')
          // self.$store.commit('SET_FLAG', true)
          self.fullScreen()
        } else if (e.code === 'Escape') {
          self.cancleFullScreen()
        }
      }, true);
    });
    this.initData();
    // this.loadOldEChartsAndWordCloud();
  },
  methods: {
    loadWordCloud() {
      const script = document.createElement('script');
      script.src = `${this.baseUrl}/echarts3/echarts-wordcloud.js`;
      script.onload = this.initWordCloud;
      document.body.appendChild(script);
    },
    loadOldEChartsAndWordCloud() {
      // console.log('开始加载')
      const scriptOldECharts = document.createElement('script');
      // console.log('创建script')
      scriptOldECharts.src = `${this.baseUrl}/echarts3/echart3.js`;
      // console.log('指定文件', `${this.baseUrl}/echarts3/echart3.js`)
      console.log(window)
      console.log(window.echarts3)
      scriptOldECharts.onload = () => {
        this.loadWordCloud();
      };
      document.body.appendChild(scriptOldECharts);

    },
    initWordCloud() {
      // console.log(this.xmcyData, '词云数据')
      const oldEcharts = window.echarts3;
      const chart = oldEcharts.init(document.getElementById('wordcloud'));
      chart.setOption( {
        title: {
          text: '提醒项目词云'
        },
        series: [{
          type: 'wordCloud',
          gridSize: 8,
          sizeRange: [12, 26],
          rotationRange: [-90, 90],
          shape: 'circle',
          width: '100%',
          height: '100%',
          drawOutOfBound: false,
          textStyle: {
            normal: {
              color: function() {
                return 'rgb(' + [
                  Math.round(Math.random() * 180),
                  Math.round(Math.random() * 180),
                  Math.round(Math.random() * 180)
                ].join(',') + ')';
              }
            },
            emphasis: {
              shadowBlur: 10,
              shadowColor: '#333'
            }
          },
          data: this.xmcyData
          // data: [
          //   { name: '血液加温治疗(术中加温)', value: 8000 },
          //   { name: '乳果糖口服溶液', value: 6181 },
          //   { name: '吸入用布地奈德混悬液', value: 4386 },
          //   { name: '琥珀酰明胶注射液', value: 4055 },
          //   { name: '注射用炎琥宁', value: 2467 },
          //   { name: '氯基已酸氯化钠注射液', value: 2244 },
          //   { name: '雷贝拉唑钠肠溶片', value: 1898 },
          //   { name: '硫酸氢氯吡格雷片', value: 1484 },
          //   { name: '沙库巴曲缬沙坦钠片', value: 1112 },
          //   { name: '依诺肝素钠注射液', value: 965 }
          // ]
        }]
      });
    },
    initCharts() {
      // 初始化第一个图表
      let ysphChart = echarts.init(document.getElementById('ysphChart'));
      ysphChart.setOption({
        title: {
          text: '医生提醒排行'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}'
        },
        series: [
          {
            name: '医生',
            type: 'funnel',
            left: '10%',
            top: 60,
            bottom: 60,
            width: '80%',
            min: 0,
            max: 200,
            minSize: '0%',
            maxSize: '100%',
            sort: 'descending',
            gap: 2,
            label: {
              show: true,
              position: 'inside'
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid'
              }
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            emphasis: {
              label: {
                fontSize: 20
              }
            },
            data: this.ysphData
          }
        ]
      });

      // 初始化第二个图表
      let ksphChart = echarts.init(document.getElementById('ksphChart'));
      ksphChart.setOption({
        title: {
          text: '科室提醒数据'
        },
        xAxis: {
          type: 'category',
          data: this.ksphData.map(item => {return item.name}),
          axisLabel: {
            interval: 0, // 强制显示所有标签
            rotate: 45, // 旋转标签，避免重叠
            textStyle: {
              color: '#606060' // 标签颜色
            }
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: this.ksphData,
            type: 'bar'
          }
        ]
      });

      // 初始化第三个图表
      let mrwgChart = echarts.init(document.getElementById('mrwgChart'));
      mrwgChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          formatter: '{b} <br/>提醒数量 : {c}'
        },
        title: {
          text: '每日提醒数量'
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            interval: 7,
            rotate: 30
          },
          max: this.mrwgData[this.mrwgData.length - 1].name,
          min: 'auto',
          data: this.mrwgData.map(item => {
            return item.name
          })
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: this.mrwgData.map(item => {
              return item.value
            }),
            type: 'line',
            smooth: true
          }
        ],
        dataZoom: {
          type: 'inside'
        }
      });

      // 初始化第四个图表
      // let chart4 = echarts.init(document.getElementById('chart4'));
      // chart4.setOption({
      //   // ECharts 配置
      // });

      // 初始化第五个图表
      let xzxmSlChart = echarts.init(document.getElementById('xzxmSlChart'));
      xzxmSlChart.setOption({
        title: {
          text: '限制设置数量'
        },
        xAxis: {
          type: 'category',
          data: this.xzxmSlData.map(item => {
            return item.name
          })
        },
        yAxis: {
          type: 'value',
          max: null
        },
        series: [
          {
            data: this.xzxmSlData,
            type: 'bar',
            label: { // 在柱子上显示数值
              show: true,
              position: 'top', // 可以选择在柱子的顶部显示数值
              formatter: '{c}' // {c} 表示数据值，即数量
            }
          }
        ]
      });

      // 初始化第五个图表
      let lxzbChart = echarts.init(document.getElementById('lxzbChart'));
      lxzbChart.setOption({
        title: {
          text: '各类型占比',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        // legend: {
        //   orient: 'vertical',
        //   left: 'right',
        //   textStyle: {
        //     color: '#000000'
        //   }
        // },
        series: [
          {
            type: 'pie',
            radius: '65%',
            data: this.lxzbData,
            center: ['50%', '60%'],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              normal: {
                textStyle: {
                  color: '#000000'
                }
              }
            }
          }
        ]
      });
    },
    async initData(){
      await this.getYsphData()
      await this.getKsphData()
      await this.getLxzbData()
      await this.getXmcyData()
      await this.getMrwgData()
      await this.getXzxmSlData()
      this.initCharts()
    },
    async getXmcyData(){
      const res = await getXmcy(this.queryParam)
      this.xmcyData = res.list
      this.loadOldEChartsAndWordCloud()
    },
    async getYsphData(){
      const res = await getYsph(this.queryParam)
      this.ysphData = res.list.map(item => {
        return {
          value: item.nums,
          name: item.doctorname
        }
      })
    },
    async getXzxmSlData(){
      const res = await getXzxmSl(this.queryParam)
      if(res.code !== 200 || res.list.length === 0 ){
        return
      }
      this.xzxmSlData = res.list
    },
    async getKsphData(){
      const res = await getKsph(this.queryParam)
      this.ksphData = res.list.map(item => {
        return {
          value: item.nums,
          name: item.kdksname
        }
      })
      this.ksphData = this.ksphData.filter(item => !/^\d+$/.test(item.name));
    },
    async getLxzbData(){
      const res = await getLxzb(this.queryParam)
      if(res.code !== 200 || res.list.length === 0 ){
        return
      }
      this.lxzbData = res.list.map(item => {
        return {
          name : this.handleJkTypeName(item.name),
          value: item.value
        }
      })
    },
    async getMrwgData(){
      const res = await getMrwg(this.queryParam)
      if(res.code !== 200 || res.list.length === 0 ){
        return
      }
      this.mrwgData = res.list
    },
    handleJkTypeName(name){
      switch (name) {
        case 'xzbz': return '限制病种';
        case 'xzlcbx': return '限制临床表现';
        case 'syzdts': return '使用最大天数';
        case 'rqzdyl': return '特定日期最大用量';
        case 'blgyxm': return '不能共用项目';
        case 'xxmsyhjs': return '使用最限项目使用后加收大天数';
        case 'dcylxz': return '单次用量限制';
        case 'zyzdyl': return '一次住院最多使用次数';
        case 'xyydj': return '限医院级别';
        case 'jjz': return '禁忌症';
        case 'yzetsy': return '限制儿童使用';
        case 'xlx': return '限男性';
        case 'xnx': return '限女性';
        case 'xzyszc': return '限医生职级[1-医师2-主治3-副主任4-主任医师]';
        case 'yzlrsy': return '限制老人使用';
        case 'ybbz': return '医保备注';
        default: return '其他'
      }
    },
    fullScreen() {
      this.$store.dispatch('app/toggleSideBarHide', true);
      this.$store.dispatch('settings/changeSetting', {
        key: 'showNavBar',
        value: false
      });
      this.$store.dispatch('settings/changeSetting', {
        key: 'tagsView',
        value: false
      });
      this.initData()
    },
    cancleFullScreen(){
      this.$store.dispatch('app/toggleSideBarHide', false);
      this.$store.dispatch('settings/changeSetting', {
        key: 'showNavBar',
        value: true
      });
      this.$store.dispatch('settings/changeSetting', {
        key: 'tagsView',
        value: true
      });
      screenfull.exit()
      this.initData()

    }
  },
};
</script>

<style scoped lang="scss">

.chart-col {
  height: 100%;

  .chart-container {
    background: #ffffff;
    border-radius: 10px;
    padding: 20px;
    height: 100%;
    border: 1px solid #DDDDDD; /* 边框颜色 */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */

    .chart {
      width: 100%;
      height: 100%;
    }

  }

}


</style>
