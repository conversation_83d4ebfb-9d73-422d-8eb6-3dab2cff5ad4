<template>
  <div class="app-container">
    <div>
      <el-form :inline="true">

        <el-form-item prop="参保类别">
          <el-select v-model="queryParam.cblb" placeholder="选择参保类别">
            <el-option
              v-for="item in cblbOptions"
              :key="item.value"
              :prop="item.prop"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="科室">
          <el-select v-model="queryParam.ksname" placeholder="选择科室">
            <el-option
              v-for="item in deptOptions"
              :key="item.value"
              :prop="item.prop"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="时间类型">
          <el-select v-model="queryParam.dateType" placeholder="时间类型">
            <el-option
              v-for="item in datetypeOptions"
              :key="item.value"
              :prop="item.prop"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <br>

        <el-form-item prop="开始时间">
          <el-date-picker
            v-model="startDate"
            type="datetime"
            placeholder="选择开始时间"
            default-time="00:00:00">
          </el-date-picker>
        </el-form-item>

        <el-form-item prop="结束时间">
          <el-date-picker
            v-model="endDate"
            type="datetime"
            placeholder="选择结束时间"
            default-time="23:59:59">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submit">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table :data="list">
        <el-table-column prop="drgmc" width="120px" align="center" label="DRG名称"></el-table-column>
        <el-table-column width="100px" prop="drgbh" label="DRG编码" align="center"></el-table-column>
        <el-table-column prop="zfqz" label="支付权重" width="100" align="center"></el-table-column>
        <el-table-column prop="cykb" label="出院科别" width="100" align="center"></el-table-column>
        <el-table-column prop="zqz" label="总权重" width="100" align="center"></el-table-column>
        <el-table-column prop="pjzyr" label="平均住院日" width="100" align="center"></el-table-column>
        <el-table-column prop="pjfy" label="平均费用" width="100" align="center"></el-table-column>
        <el-table-column prop="fyxhzs" label="费用消耗指数" width="100" align="center"></el-table-column>
        <el-table-column prop="zfy" label="总费用" width="100" align="center"></el-table-column>
        <el-table-column prop="zyk" label="总盈亏" width="100" align="center"></el-table-column>
        <el-table-column prop="ljyk" label="例均盈亏" width="100" align="center"></el-table-column>
        <el-table-column prop="swl" label="死亡率" width="100" align="center"></el-table-column>
        <el-table-column prop="drgfyjdjjgz" label="DRG费用极低及极高组" width="100" align="center"></el-table-column>
        <el-table-column prop="zytsxy2hdy60t" label="住院天数小于2或大于60天" width="100" align="center"></el-table-column>
        <el-table-column prop="sl" label="sl" width="100" align="center"></el-table-column>
        <el-table-column prop="zfbz" label="zfbz" width="100" align="center"></el-table-column>
      </el-table>
      <div>
        <el-pagination
          background
          layout="prev, pager, next"
          :current-page="pageNum"
          :page-size="pageSize"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>


<script>
import { getDrgFztjData } from '@/api/tjfx/tjfx'
import { date } from '@/assets/js/mammoth.browser.min'
import { getDeptnameList } from '@/api/tjfx/jxzb'
import { dateToString, getMonthFirstDay, getTodayLastSecond } from '@/utils/dateUtils'

export default {
  /*
  DRG分组统计
   */
  name: 'Fztj',
  data() {
    return {
      list: [],
      queryParam: {
        startDate: '',
        endDate: '',
        dateType: 'jsdate',
        cblb: '%',
        ksname: '所有'
      },
      pageSize: 10,
      pageNum: 1,
      startDate: getMonthFirstDay(),
      endDate: getTodayLastSecond(),
      //科室
      deptOptions : [],
      // 时间类型
      datetypeOptions: [
        {
          value: 'jsdate',
          prop: '结算时间'
        },
        {
          value: 'cydate',
          prop: '出院时间'
        }
      ],
      // 参保类别
      cblbOptions: [
        {
          value: '%',
          prop: '所有'
        },
        {
          value: '职工',
          prop: '职工'
        },
        {
          value: '居民',
          prop: '居民'
        },
        {
          value: '自费',
          prop: '自费'
        },
        {
          value: '生育',
          prop: '生育'
        }
      ],
      total: 0
    }
  },
  created() {
    this.initData()
  },
  methods: {
    async initData(){
      const deptnameListRes = await getDeptnameList()
      this.deptOptions = [];
      if(!deptnameListRes.is_ys) {
        this.deptOptions.push({
          value: '全院',
          prop: '全院'
        });
      }
      deptnameListRes.list.forEach(item => {
        this.deptOptions.push({
          value: item,
          prop: item
        })
      })

      await this.submit()
    },
    async getList(){
      const res = await getDrgFztjData(this.queryParam, this.pageNum, this.pageSize)

      if(res.code !== 200) {
        return;
      }
      this.list = res.rows
      this.total = res.total
    },
    async submit(){
      this.queryParam.startDate = dateToString(this.startDate)
      this.queryParam.endDate = dateToString(this.endDate)
      await this.getList()
    },
    pageChange(val){
      this.pageNum = val
      this.submit()
    },
    sizeChange(val){
      this.pageSize = val
      this.submit()
    }
  }
}
</script>

<style scoped lang="scss">

</style>
