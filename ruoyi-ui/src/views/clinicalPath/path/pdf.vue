<template>
  <div class="app-container">
    <embed v-if="pathPdfUrl" :src="pathPdfUrl" type="application/pdf" width="100%" height="900px" />
  </div>
</template>

<script>

import {getWayPdf} from "@/api/clinicalPath/pathways";

export default {
  name: "Waypdf",
  data() {
    return {
      pathPdfUrl: null,
    };
  },
  created() {
    if (this.$route.query.id) {
      const pathId = this.$route.query.id
      this.getPathCon(pathId)
    } else {
      this.$modal.msgError("未携带参数！")
    }
  },
  methods: {
    async getPathCon(pathId) {
      try {
        const res = await getWayPdf(pathId)
        this.pathPdfUrl = URL.createObjectURL(res);
      } catch (e) {
        this.$modal.msgError("未找到该路径的PDF文件！")
      }
    },
  }
};
</script>
