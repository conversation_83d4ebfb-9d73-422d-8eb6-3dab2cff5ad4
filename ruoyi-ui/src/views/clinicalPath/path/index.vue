<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="路径名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入路径名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="主要诊断" prop="icd10Main">
        <el-input
          v-model="queryParams.icd10Main"
          placeholder="请输入主要诊断"
          clearable
        />
      </el-form-item>
      <el-form-item label="手术" prop="icd9">
        <el-input
          v-model="queryParams.icd9"
          placeholder="请输入手术"
          clearable
        />
      </el-form-item>
      <el-form-item label="其他诊断" prop="icd10Other">
        <el-input
          v-model="queryParams.icd10Other"
          placeholder="请输入手术"
          clearable
        />
      </el-form-item>
      <el-form-item label="路径内容" prop="icd10Other">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入路径内容"
          clearable
        />
      </el-form-item>
      <el-form-item label-width="100" label="路径状态" prop="needIcd9">
        <el-select clearable v-model="queryParams.status">
          <el-option label="正常" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item  label-width="100" label="路径版本" prop="version">
        <el-select clearable v-model="queryParams.version">
          <el-option v-for="item in version" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="danger" icon="el-icon-refresh" size="mini" @click="resetPathData">刷新路径缓存</el-button>
      </el-form-item>
    </el-form>


    <el-table border v-loading="loading" :data="wayList">
      <el-table-column width="300" show-overflow-tooltip label="路径名称" align="left" prop="name" />
      <el-table-column width="150" show-overflow-tooltip label="路径版本" align="left" prop="version" />
      <el-table-column width="150" show-overflow-tooltip label="状态" align="left" prop="status">
        <template slot-scope="{ row }">
          <el-tag v-if="row.status == '1'" type="success">正常</el-tag>
          <el-tag v-else type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column width="150" show-overflow-tooltip label="主要诊断" align="left" prop="icd10Main" />
      <el-table-column width="150" show-overflow-tooltip label="手术编码" align="left" prop="icd9" />
      <el-table-column width="150" show-overflow-tooltip label="次要诊断" align="left" prop="icd10Other" />
      <el-table-column show-overflow-tooltip label="最小年龄" align="left" prop="minAge" />
      <el-table-column show-overflow-tooltip label="最大年龄" align="left" prop="maxAge" />
      <el-table-column width="150" show-overflow-tooltip label="第二诊断" align="left" prop="icd10Second" />
      <el-table-column width="150" show-overflow-tooltip label="排除诊断" align="left" prop="icd10Pc" />
      <el-table-column width="150" show-overflow-tooltip label="排除手术" align="left" prop="icd9Pc" />
      <el-table-column width="150" show-overflow-tooltip label="病理诊断" align="left" prop="blzd" />
      <el-table-column width="180" show-overflow-tooltip label="是否需要手术" align="left" prop="needIcd9" >
        <template slot-scope="{ row }">
          {{ row.needIcd9 == '1' ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" fixed class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-has-role="['admin']"
            v-hasPermi="['clinicalPath:way:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            @click="getPathCon(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-drawer
      :title="title"
      :visible.sync="open"
      direction="rtl"
      :wrapper-closable="false"
      size="97%"
      append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <div style="display: flex; justify-content: space-around">
          <el-card style="width: 50%;margin: 0 20px;padding: 5px">
            <el-scrollbar style="height: 750px;">
              <div v-html="form.content"></div>
            </el-scrollbar>
          </el-card>
          <div style="width: 50%">
            <el-form-item label="路径名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入路径名称" />
            </el-form-item>
            <el-form-item label="路径版本" prop="version">
              <el-select v-model="form.version" placeholder="请输入路径版本" >
                <el-option v-for="item in version" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item label="主要诊断" prop="icd10Main">
              <el-input v-model="form.icd10Main" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="第二诊断" prop="icd10Second">
              <el-input v-model="form.icd10Second" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="次要诊断" prop="icd10Other">
              <el-input v-model="form.icd10Other" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="手术编码" prop="icd9">
              <el-input v-model="form.icd9" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="排除诊断" prop="icd10Pc">
              <el-input v-model="form.icd10Pc" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="排除手术" prop="icd9Pc">
              <el-input v-model="form.icd9Pc" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="病理诊断" prop="blzd">
              <el-input v-model="form.blzd"  placeholder="请输入内容" />
            </el-form-item>
            <div style="display:flex;">
              <el-form-item label="最小年龄" prop="minAge">
                <el-input v-model="form.minAge" type="number" placeholder="请输入内容" />
              </el-form-item>
              <el-form-item label="最大年龄" prop="maxAge">
                <el-input v-model="form.maxAge" type="number" placeholder="请输入内容" />
              </el-form-item>
            </div>
            <el-form-item label="需要手术" prop="needIcd9">
              <el-select v-model="form.needIcd9">
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="使用状态" prop="status">
              <el-select v-model="form.status">
                <el-option label="正常" value="1" />
                <el-option label="停用" value="0" />
              </el-select>
            </el-form-item>
            <el-button style="margin: 5px; float: right" type="primary" @click="submitForm">确 定</el-button>
          </div>
        </div>
      </el-form>
    </el-drawer>

    <el-drawer
      :title="pathConTitle"
      :visible.sync="pathConOpen"
      direction="rtl"
      :wrapper-closable="false"
      size="90%"
      append-to-body>
      <embed v-if="pathPdfUrl" :src="pathPdfUrl" type="application/pdf" width="100%" height="800px" />
    </el-drawer>

  </div>
</template>

<script>
import {listWay, addWay, updateWay, getWayPdf, resetPathData, getConById} from "@/api/clinicalPath/pathways";
import {selectHDeptNameList} from "@/api/system/hdept";

export default {
  name: "Way",
  data() {
    return {
      pathConOpen: false,
      pathConTitle: null,
      version: [
        '2009年版',
        '2010年版',
        '2011年版',
        '2011年县级医院版',
        '2012年版',
        '2012年县级医院版',
        '2013年版',
        '2013年县级医院版',
        '2016年版',
        '2016年县级医院版',
        '2017年版',
        '2017年县级医院版',
        '2019年版',
        '2023年版'
      ],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 临床路径数据管理表格数据
      wayList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        version: null,
        icd10Main: null,
        icd10Second: null,
        icd10Other: null,
        icd10Pc: null,
        icd9: null,
        icd9Pc: null,
        content: null,
        minAge: null,
        maxAge: null,
        blzd: null,
        status: null,
        needIcd9: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "路径名称不能为空", trigger: "blur" }
        ],
        version: [
          { required: true, message: "路径版本不能为空", trigger: "blur" }
        ],
      },
      path: {},
      pathPdfUrl: null
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async resetPathData() {
      const res = await resetPathData()
      if (res.code == 200) {
        this.$modal.msgSuccess("刷新成功")
      }
      console.log(res)
    },
    getPathCon(row) {
      const routeUrl = this.$router.resolve({
        path: "/views/pathway/pdf",
        query: {id: row.id}
      })
      window.open(routeUrl.href, '_blank')
    },
    /** 查询临床路径数据管理列表 */
    getList() {
      this.loading = true;
      listWay(this.queryParams).then(response => {
        this.wayList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        version: null,
        icd10Main: null,
        icd10Second: null,
        icd10Other: null,
        icd10Pc: null,
        icd9: null,
        icd9Pc: null,
        content: null,
        minAge: null,
        maxAge: null,
        blzd: null,
        needIcd9: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      this.loading = true
      this.form = JSON.parse(JSON.stringify(row));
      this.form.content = await getConById(row.id);
      this.open = true;
      this.title = "修改临床路径数据管理";
      this.loading = false
    },
    /** 提交按钮 */
    submitForm() {
      const res = JSON.parse(JSON.stringify(this.form))
      res.content = null
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (res.id != null) {
            updateWay(res).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWay(res).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
  }
};
</script>
