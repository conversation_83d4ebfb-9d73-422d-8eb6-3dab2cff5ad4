<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">

      <el-form-item label="住院号" prop="zyh">
        <el-input
          style="width: 100px"
          v-model="queryParams.zyh"
          placeholder="住院号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="病人姓名" prop="brname">
        <el-input
          style="width: 100px"
          v-model="queryParams.brname"
          placeholder="病人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用项目" prop="fymName">
        <el-input
          v-model="queryParams.fymName"
          placeholder="费用项目"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="科室" prop="kdksname">
        <el-select v-model="queryParams.kdksname"
                   @focus="resetDept"
                   @change="getDoctorByDept(queryParams.kdksname)"
                   filterable
                   :filter-method="deptFilter"
                   placeholder="科室"
                   clearable
                   style="width: 150px">
          <el-option v-for="(item,index) in deptList" :value="item.kdksname" :key="index" :label="item.kdksname">
            {{ item.kdksname }}
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="医生" prop="doctorname">
        <el-select v-model="queryParams.doctorname"
                   @focus="resetDoctor"
                   @change="resetDoctor"
                   filterable
                   :filter-method="doctorFilter"
                   placeholder="医生"
                   clearable
                   style="width: 100px">
          <el-option v-for="(item,index) in doctorList" :value="item.doctorname" :key="index" :label="item.doctorname">
            {{ item.doctorname }}
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="监控类别" prop="jktypes">
        <el-select multiple v-model="queryParams.jktypes"
                   placeholder="监控类别" clearable>
          <el-option v-for="item in jkruleList" :value="item.code" :label="item.name"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-select v-model="queryParams.zyzt" placeholder="在院状态" clearable style="width: 100px">
          <el-option v-for="item in zyztList" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="违规时间" prop="createDate">
        <el-date-picker
          v-model="queryParams.createDateStart"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.createDateEnd"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>

      </el-form-item>

      <el-form-item label="发送时间" prop="sendDate">
        <el-date-picker
          v-model="queryParams.sendDateStart"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.sendDateEnd"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="严重性" prop="xzlb">
        <ColorSelector placeholder="请选择严重性" isUpdate="false" :item="queryParams" column="xzlb"
                       :color="queryParams.xzlb"/>
      </el-form-item>

      <el-form-item label="物价限制" prop="wjxzflag">
        <el-select v-model="queryParams.wjxzflag" placeholder="物价限制" clearable style="width: 100px">
          <el-option v-for="item in wjxzflagOptions" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="检查状态" prop="checkStatus">
        <el-select v-model="queryParams.checkStatus" placeholder="检查状态" clearable style="width: 100px">
          <el-option v-for="item in checkStatusOptions" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table highlight-current-row :border="true" v-loading="loading" :data="wgjlList">
      <el-table-column fixed="left" label="检查状态" align="center" class-name="small-padding fixed-width" width="120px">
        <template slot-scope="scope">
          {{ scope.row.checkStatus == 1 ? '已检查' : '未检查' }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width" width="150px">
        <template slot-scope="scope">
          <div v-if="scope.row.checkStatus == 1">
          </div>
          <el-button
            v-else
            size="mini"
            type="text"
            @click="setResult(scope.row)"
          >填写检查结果
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="detail(scope.row)"
          >详情
          </el-button>
        </template>
      </el-table-column>
      <el-table-column sortable label="住院号" align="center" prop="zyh" show-overflow-tooltip width="130px"/>
      <el-table-column sortable label="病人姓名" align="center" prop="brname" show-overflow-tooltip width="100px"/>
      <el-table-column sortable prop="jklog" label="违规内容" align="left" show-overflow-tooltip width="650px">
        <template slot-scope="scope">
          <div>
            <span :style="{color: scope.row.xzlb == '#FFFFFF' ? '#000000' : scope.row.xzlb}">
              {{ scope.row.jklog }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column sortable label="就诊科室" align="center" width="100px" prop="kdksname" show-overflow-tooltip/>
      <el-table-column sortable label="医生" align="center" prop="doctorname" show-overflow-tooltip/>
      <el-table-column sortable label="违规数量" align="center" width="100px" prop="sl"/>
      <el-table-column sortable label="创建时间" align="center" prop="createDate" width="120px">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column sortable label="床号" align="center" prop="bed" width="80px"/>
      <el-table-column sortable label="违规金额" align="center" width="100px" prop="je"/>
      <el-table-column label="疑点名称" prop="ybbz" show-overflow-tooltip width="650px"/>
      <el-table-column label="处理信息" align="center" prop="tshOper" width="120px"/>
      <el-table-column show-overflow-tooltip sortable label="备注" align="left" prop="checkRemark" width="650px"/>
      <el-table-column label="违规类型" align="center" show-overflow-tooltip width="120px" prop="wgtype"/>
      <el-table-column label="违规原因" align="center" show-overflow-tooltip width="300px" prop="wgyy"/>
      <el-table-column label="检查结果" show-overflow-tooltip width="650px" prop="jcjg" />
      <el-table-column sortable label="扣分" show-overflow-tooltip align="center" prop="score"/>
      <el-table-column label="发送日期" align="left" prop="sendDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.sendDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-drawer
      title="填写检查结果"
      :visible.sync="drawerVisible"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeDrawer"
      size="45%">

      <el-form ref="resultForm" :rules="resultFormRules" :label-positio="labelPositio" class="resultForm" :model="resultForm">
        <el-form-item label="住院号" prop="zyh">
          <el-input disabled v-model="resultForm.zyh" placeholder="住院号"/>
        </el-form-item>
        <el-form-item label="科室" prop="dept">
          <el-input disabled v-model="resultForm.dept" placeholder="科室"/>
        </el-form-item>
        <el-form-item label="医生" prop="doctor">
          <el-input disabled v-model="resultForm.doctor" placeholder="医生" />
        </el-form-item>
        <el-form-item label="违规类型" prop="wgtype">
          <el-select style="width: 100%" v-model="resultForm.wgtype" placeholder="违规类型">
            <el-option
              v-for="dict in wgTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="违规原因" prop="wgyy">
          <el-select style="width: 100%" v-model="resultForm.wgyy" placeholder="违规原因">
            <el-option
              v-for="dict in wgResonOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检查结果" prop="jcjg">
          <el-input type="textarea" :maxlength="300" :show-word-limit="true" v-model="resultForm.jcjg" placeholder="检查结果"/>
        </el-form-item>
        <el-form-item v-if="checkRole(['ybry'])" label="扣分" prop="score">
          <el-input type="number" v-model="resultForm.score" placeholder="扣分" />
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="saveResult">保存</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>

    <el-drawer
      :title="title"
      :visible.sync="open"
      direction="rtl"
      :wrapper-closable="false"
      size="80%">
      <tabs :jzh="jzh"></tabs>
    </el-drawer>


  </div>
</template>

<script>
import {selectCheckWgjl, listWgjlKs, listWgjlYs} from "@/api/gksz/wgjl";
import {listRules} from "@/api/gksz/jkrule";
import PinYinMatch from 'pinyin-match';
import {getTodayLastSecondStr, getYesterdayFirstSecondStr} from "@/utils/dateUtils";
import {addResult} from "@/api/gksz/checkWgjlResult";
import tabs from "@/components/DetailsTabs/tabs.vue";
import {getUserDeptNameList } from "@/api/system/hdept";
import {checkRole} from "@/utils/permission";


export default {
  name: "Checkwgjl",
  components: {tabs},
  data() {
    return {
      jzh: null,
      checkDoctor: null,
      checkDeptList: null,
      labelPositio: 'top',
      drawerVisible: false,
      wjxzflagOptions: [
        {
          value: "1",
          label: "是"
        },
        {
          value: "0",
          label: "否"
        }
      ],
      checkStatusOptions: [
        {
          value: "1",
          label: "已检查"
        },
        {
          value: "0",
          label: "未检查"
        }
      ],
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 违规查询表格数据
      wgjlList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      wgTypeOptions: [],
      wgResonOptions: [],
      doctorList: [],
      doctorListby: [],
      resultForm: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jzh: null,
        jklog: null,
        jktypes: null,
        brname: null,
        kdksname: null,
        doctorname: null,
        fymName: null,
        sl: null,
        je: null,
        clFlag: null,
        clHfxx: null,
        createDate: null,
        zyh: null,
        ybbz: null,
        createDateStart: getYesterdayFirstSecondStr(),
        createDateEnd: getTodayLastSecondStr(),
        tshOper: null,
        fyxm: null,
        xzlb: null,
        wjxzflag: null,
        checkDeptList: null,
        checkDoctor: null,
        checkStatus: null,
        sendDateStart: null,
        sendDateEnd: null
      },
      // 表单参数
      form: {},
      // 表单校验
      resultFormRules: {
        wgtype: [
          { required: true, message: '请输入违规类型', trigger: 'blur' },
        ],
        wgyy: [
          { required: true, message: '请输入违规原因', trigger: 'blur' },
        ],
        jcjg: [
          { required: true, message: '请输入检查结果', trigger: 'blur' },
        ],
        score: [
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error('扣分不能小于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      },
      deptList: [],
      deptListby: [],  //备用
      jkruleList: [],
      zyztList: [
        {
          value: '1',
          name: '在院',
          label: '在院'
        },
        {
          value: '0',
          name: '出院',
          label: '出院'
        }
      ]
    };
  },
  created() {
    this.init();
  },
  methods: {
    checkRole,
    cancel() {
      this.open = false;
    },
    detail(row) {
      this.jzh = row.jzh
      this.open = true
      this.title =  row.zyh + '[' +  row.brname + ']患者详情'
    },
    saveResult() {
      this.$refs['resultForm'].validate((valid) => {
        if (valid) {
          console.log(this.resultForm)
          addResult(this.resultForm).then(res => {
            if (res.code == 200) {
              this.getList()
              this.$modal.msgSuccess("保存成功")
              this.drawerVisible = false
              return
            }
          })
        } else {
          return false;
        }
      });
    },
    resetForm() {
      this.resultForm.wgtype = null
      this.resultForm.wgyy = null
      this.resultForm.jcjg = null
      this.resultForm.score = null
    },
    closeDrawer() {
      this.resultForm = {}
    },
    setResult(row) {
      this.resultForm = {
        dept: row.kdksname,
        doctor: row.doctorname,
        zyh: row.zyh,
        wgjlId: row.id,
        wgtype: null,
        wgyy: null,
        jcjg: null,
        score: null,
        jgid: row.jgid,
        sendDate: row.sendDate
      }
      this.drawerVisible = true
    },
    resetDept() {
      this.deptList = this.deptListby
    },
    resetDoctor() {
      this.doctorList = this.doctorListby
    },
    deptFilter(val) {
      this.queryParams.kdksname = val
      if (val) {
        this.deptList = []
        var deptList = this.deptListby.filter((item) => {
          if (PinYinMatch.match(item.kdksname, val)) {
            return true
          }
        })
        this.deptList = deptList
      } else {
        this.deptList = this.deptListby
      }
    },
    doctorFilter(val) {
      this.queryParams.doctorname = val
      if (val) {
        this.doctorList = []
        var doctorList = this.doctorListby.filter((item) => {
          if (PinYinMatch.match(item.doctorname, val)) {
            return true
          }
        })
        this.doctorList = doctorList
      } else {
        this.doctorList = this.doctorListby
      }
    },
    getDoctorByDept(deptName) {
      this.doctorList = []
      this.doctorListby = []
      this.queryParams.doctorname = null
      this.deptList = this.deptListby
      if (!deptName) {
        return
      }
      listWgjlYs({kdksname: deptName, brtype: this.wgType}).then(response => {
        this.doctorList = response.rows;
        this.doctorListby = JSON.parse(JSON.stringify(this.doctorList))
      });
    },
    async init() {
      const res = await getUserDeptNameList()
      if (res.code == 200) {
        const deptNameList = res.deptNameList ? res.deptNameList : null
        if (deptNameList && deptNameList.length > 0) {
          this.checkDeptList = deptNameList
          console.log(this.checkDeptList)
        } else {
          this.$modal.msgWarning("当前医生不属于任何科室")
          this.loading = false
          return
        }
      } else {
        this.$modal.msgWarning("当前医生不属于任何科室")
        this.loading = false
        return
      }
      this.checkDoctor = this.$store.state.user.nickName ? this.$store.state.user.nickName : null
      this.queryParams.checkDeptList = this.checkDeptList
      this.queryParams.checkDoctor = this.checkDoctor
      this.getList();
      this.getDeptList()
      this.getDoctorList()
      this.getRulesList()
      this.getDictList()
    },
    getDictList() {
      this.getDicts("sys_wg_type").then(response => {
        this.wgTypeOptions = response.data;
      });
      this.getDicts("sys_wg_reson").then(response => {
        this.wgResonOptions = response.data;
      });
    },
    getDeptList() {
      listWgjlKs({brtype: this.wgType}).then(response => {
        this.deptList = response.rows;
        this.deptListby = JSON.parse(JSON.stringify(this.deptList))
      });
    },
    getDoctorList() {
      listWgjlYs({brtype: this.wgType}).then(response => {
        this.doctorList = response.rows;
        this.doctorListby = JSON.parse(JSON.stringify(this.doctorList))
      });
    },
    getRulesList() {
      listRules(this.queryParams).then(response => {
        this.jkruleList = response.rows;
      });
    },
    getList() {
      this.loading = true;
      if (!this.queryParams.createDateStart || !this.queryParams.createDateEnd) {
        this.queryParams.createDateStart = getYesterdayFirstSecondStr()
        this.queryParams.createDateEnd = getTodayLastSecondStr()
      }
      selectCheckWgjl(this.queryParams).then(response => {
        this.wgjlList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.checkDeptList == null || this.checkDeptList.length == 0) {
        this.$modal.msgWarning("当前医生不属于任何科室")
        return
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams ={
        pageNum: 1,
          pageSize: 10,
          jzh: null,
          jklog: null,
          jktypes: null,
          brname: null,
          kdksname: null,
          doctorname: null,
          fymName: null,
          sl: null,
          je: null,
          clFlag: null,
          clHfxx: null,
          createDate: null,
          zyh: null,
          ybbz: null,
          createDateStart: getYesterdayFirstSecondStr(),
          createDateEnd: getTodayLastSecondStr(),
          tshOper: null,
          fyxm: null,
          xzlb: null,
          wjxzflag: null,
          checkDeptList: this.checkDeptList,
          checkDoctor: this.checkDoctor
      }
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.checkDeptList == null || this.checkDeptList.length == 0) {
        this.$modal.msgWarning("当前医生不属于任何科室")
        return
      }
      this.download('gksz/wgjl/checkWgjlExport', {
        ...this.queryParams
      }, `违规记录_${new Date().getTime()}.xlsx`)
    },
  }
};
</script>

<style>
.resultForm {
  margin: 25px;
}
</style>
