<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="项目编码" prop="xmbm">
        <el-input
          v-model="queryParams.xmbm"
          placeholder="项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医院名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="医院名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item  label="医保名称" prop="ybname">
        <el-input
          v-model="queryParams.ybname"
          placeholder="医保名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类别" prop="xmlb">
       <el-select clearable v-model="queryParams.xmlb">
         <el-option value="yp" label="药品" />
         <el-option value="zl" label="诊疗" />
         <el-option value="hc" label="耗材" />
         <el-option value="cy" label="中草药" />
       </el-select>
      </el-form-item>
      <el-form-item label="不同点" prop="btflag">
        <el-select clearable v-model="queryParams.btflag">
          <el-option value="cd" label="产地不同" />
          <el-option value="gg" label="规格不同" />
          <el-option value="mc" label="名称不同" />
          <el-option value="jg" label="价格不同" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>


    <el-table :border="true" v-loading="loading" :data="dzList">
      <el-table-column width="150px"show-overflow-tooltip label="项目编码" align="center" prop="xmbm" />
      <el-table-column width="200px" show-overflow-tooltip label="医院名称" align="center" prop="name" />
      <el-table-column width="200px" show-overflow-tooltip label="医保名称" align="center" prop="ybname" />
      <el-table-column show-overflow-tooltip label="医院单价" align="center" prop="price" />
      <el-table-column show-overflow-tooltip label="医保单价" align="center" prop="ybprice" />
      <el-table-column show-overflow-tooltip label="医院单位" align="center" prop="dw" />
      <el-table-column show-overflow-tooltip label="医保单位" align="center" prop="ybdw" />
      <el-table-column width="150px" show-overflow-tooltip label="医院规格" align="center" prop="gg" />
      <el-table-column width="150px" show-overflow-tooltip label="医保规格" align="center" prop="ybgg" />
      <el-table-column show-overflow-tooltip label="医院剂型" align="center" prop="jx" />
      <el-table-column show-overflow-tooltip label="医保剂型" align="center" prop="ybjx" />
      <el-table-column show-overflow-tooltip label="费用等级" align="center" prop="fydj" />
      <el-table-column width="150px" show-overflow-tooltip label="医院产地" align="center" prop="cd" />
      <el-table-column width="150px" show-overflow-tooltip label="医保产地" align="center" prop="ybcd" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { YbgkDzAll } from "@/api/gksz/dz";

export default {
  name: "Ybdz",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 药品管控设置表格数据
      dzList: [],
      // 弹出层标题
      title: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        xmbm: null,
        fymid: null,
        jx: null,
        cd: null,
        gg: null,
        bz: null,
        name: null,
        dw: null,
        price: null,
        type: null,
        flag: null,
        ybjx: null,
        ybcd: null,
        ybgg: null,
        ybbz: null,
        ybname: null,
        ybdw: null,
        ybprice: null,
        fydj: null,
        opdate: null,
        nccd: null,
        createDate: null,
        updateDate: null,
        remarks: null,
        dxlbcode: null,
        dxlbname: null,
        xxlbcode: null,
        xxlbname: null,
        yycode: null,
        ybcode: null,
        lbcode: null,
        lbname: null,
        nameFlag: null,
        ggFlag: null,
        cdFlag: null,
        gjxmdm: null,
        ybxz: null,
        orgcode: null,
        xzmc: null,
        xmlb: null,
        btflag: null,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getYbxx() {

    },
    /** 查询药品管控设置列表 */
    getList() {
      this.loading = true;
      YbgkDzAll(this.queryParams).then(response => {
        this.dzList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('gksz/dz/ybdz/export', {
        ...this.queryParams
      }, `医保对照结果_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
