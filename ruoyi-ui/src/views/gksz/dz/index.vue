<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="queryParams.ybname"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助记码" prop="nccd">
        <el-input
          v-model="queryParams.nccd"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['gksz:dz:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table
      border
      :data="dzList"
      @selection-change="handleSelectionChange"
      highlight-current-row
      @row-click="getDataList">
      <el-table-column label="项目编号" align="left" prop="xmbm" width="250px"/>
      <el-table-column label="项目名称" align="left" prop="ybname"  width="300px"/>
      <el-table-column label="剂型" align="left" prop="ybjx" />
      <el-table-column label="规格" align="left" prop="ybgg"/>
      <el-table-column label="单位" align="left" prop="ybdw" />
      <el-table-column label="单价" align="left" prop="ybprice" />
      <el-table-column label="助记码" align="left" prop="nccd" width="150px"/>
      <el-table-column label="类别" align="left" prop="type" />
      <el-table-column label="备注" align="left" prop="ybxz" show-overflow-tooltip width="300px"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes=pageSizes
      @pagination="getList"
    />
    <br>

    <div v-if="xz">
      <el-button type="success" icon="el-icon-plus" size="mini" @click="addXzData">新增限制信息</el-button>
      <el-table
        border
        :data="xzxmList"
        @selection-change="handleSelectionChange"
        highlight-current-row>
        <el-table-column label="限制方式" align="center" prop="useflag" width="150px" />
        <el-table-column label="限制名称" align="center" prop="xzname" width="250px" />
        <el-table-column label="限制值" align="center" prop="xzvalue"/>
        <el-table-column label="严重性" align="center" prop="showcolor" width="180px">
          <template slot-scope="scope">
            <ColorSelector :item="scope.row" @updateColor="updateColor" :color="scope.row.showcolor"/>
          </template>
        </el-table-column>
        <el-table-column label="医保备注" align="center" prop="ybxz" />
        <el-table-column label="操作" align="center" width="280px">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['gksz:xzxm:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['gksz:xzxm:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="xzxmTotal>0"
        :total="xzxmTotal"
        :page.sync="xzxmqueryParams.pageNum"
        :limit.sync="xzxmqueryParams.pageSize"
        @pagination="getXzxmList"
      />
    </div>

    <!-- 限制信息 -->
    <el-dialog title="限制信息" :visible.sync="xzopen" width="1200px" append-to-body @close="resetData">
      <el-form :model="addParams" size="small" :inline="true" label-width="68px" style="margin-top: 10px">
        <el-form-item label="限制名称" >
          <el-select :value="selectXzmc">
            <el-option v-for="(item,index) in jkruleList" :value="item.name" :key="index" @click.native="setXzData(item)"/>
          </el-select>
        </el-form-item>
        <el-form-item label="限制值" prop="name" style="width: 500px;">
          <el-input
            v-if="notbzandks"
            v-model="addParams.newValue"
            placeholder="请输入限制值"
            style="width: 400px;"
          />
          <el-input
            v-if="bz"
            v-model="selectBzXzvalue"
            placeholder="请选择限制病种"
            style="width: 400px;"
          />
          <el-input
            v-if="ks"
            v-model="selectKsXzvalue"
            placeholder="请选择限制科室"
            style="width: 400px;"
          />
        </el-form-item>
        <el-form-item label="严重性" >
          <ColorSelector placeholder="请选择严重性" isUpdate="false" :item="addParams" :color="addParams.showcolor"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="addNewValue">添加</el-button>
        </el-form-item>
      </el-form>

      <!--病种信息-->
      <div v-if="bzopen">
        <el-form :model="bzmlqueryParams" ref="bzqueryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="病种名称" prop="bzmc">
            <el-input
              v-model="bzmlqueryParams.bzmc"
              placeholder="请输入名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="queryBzData">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetBzData">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="bzmlList" @selection-change="handleSelectionChange" highlight-current-row @row-click="setBzData">
          <el-table-column label="编码" align="center" prop="bzbm" />
          <el-table-column label="名称" align="center" prop="bzmc" />
          <el-table-column label="助记码" align="center" prop="zjm"/>
        </el-table>
        <pagination
          v-show="bzmlTotal>0"
          :total="bzmlTotal"
          :page.sync="bzmlqueryParams.pageNum"
          :limit.sync="bzmlqueryParams.pageSize"
          @pagination="getBzData"
        />
      </div>

      <!-- 科室信息 -->
      <div v-if="ksopen">
        <el-form :model="dictqueryParams" ref="dictqueryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="科室名称" prop="dictLabel">
            <el-input
              v-model="dictqueryParams.dictLabel"
              placeholder="请输入科室名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="queryKsData">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetKsData">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="dataList" @selection-change="handleSelectionChange"
                  highlight-current-row @row-click="setKsData">
          <el-table-column label="编码" align="center" prop="dictValue" />
          <el-table-column label="名称" align="center" prop="dictLabel" />
        </el-table>
        <pagination
          v-show="dataTotal>0"
          :total="dataTotal"
          :page.sync="dictqueryParams.pageNum"
          :limit.sync="dictqueryParams.pageSize"
          @pagination="getKsData"
        />
      </div>
    </el-dialog>

    <!-- 修改限制信息对话框 -->
    <el-dialog :title="title" :visible.sync="xzUpdateopen" width="500px" append-to-body>
      <el-form ref="form" :model="xzxmForm" :rules="rules" label-width="80px">
        <el-form-item label="限制名称" prop="xzname">
          <el-input v-model="xzxmForm.xzname" placeholder="请输入限制名称"/>
        </el-form-item>
        <el-form-item label="限制值" prop="xzvalue">
          <el-input v-model="xzxmForm.xzvalue" placeholder="请输入限制值"/>
        </el-form-item>
        <el-form-item label="管控方式" prop="useflag">
          <el-select v-model="xzxmForm.useflag">
            <el-option value="提醒">提醒</el-option>
            <el-option value="询问">询问</el-option>
            <el-option value="审核">审核</el-option>
            <el-option value="禁止">禁止</el-option>
            <el-option value="不启用">不启用</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="医保限制" prop="ybxz">
          <el-input 
            type="textarea" 
            v-model="xzxmForm.ybxz" 
            placeholder="请输入医保备注"
            :rows="3"
            resize="none">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listDz, getDz, delDz, addDz, updateDz } from "@/api/gksz/dz";
import { listXzxm, getXzxm, delXzxm, addXzxm, updateXzxm } from "@/api/gksz/xzxm";
import { listJkrule, getJkrule, delJkrule, addJkrule, updateJkrule } from "@/api/gksz/jkrule";
import { listBzml, getBzml, delBzml, addBzml, updateBzml } from "@/api/mlcx/bzml";
import { listData, getData, delData, addData, updateData } from "@/api/mlcx/data";

export default {
  name: "Dz",
  data() {
    return {
      pageSizes: [5,100],
      //限制值是否为必填
      mustxzxx:0,
      //控制显示
      xzUpdateopen:false,
      xz:false,
      ks:false,
      bz:false,
      notbzandks:true,
      //当前处理行索引
      dzrow:'',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      xzxmTotal: 0,
      jkruleTotal: 0,
      bzmlTotal: 0,
      dataTotal: 0,
      // 药品管控设置表格数据
      dzList: [],
      // 限制信息表格数据
      xzxmList: [],
      jkruleList: [],
      bzmlList: [],
      dataList: [],
      selectXzmc:null,
      selectKsXzvalue:null,
      selectBzXzvalue:null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      xzopen:false,
      bzopen:false,
      ksopen:false,
      // 新增限制值
      addParams: {
        newValue: null,
        showcolor: null
      },
      selectParams: {
        cate:null
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        xmbm: null,
        fymid: null,
        jx: null,
        cd: null,
        gg: null,
        bz: null,
        name: null,
        dw: null,
        price: null,
        type: null,
        flag: null,
        ybjx: null,
        ybcd: null,
        ybgg: null,
        ybbz: null,
        ybname: null,
        ybdw: null,
        ybprice: null,
        fydj: null,
        opdate: null,
        nccd: null,
        createDate: null,
        updateDate: null,
        remarks: null,
        dxlbcode: null,
        dxlbname: null,
        xxlbcode: null,
        xxlbname: null,
        yycode: null,
        ybcode: null,
        lbcode: null,
        lbname: null,
        nameFlag: null,
        ggFlag: null,
        cdFlag: null,
        gjxmdm: null,
        ybxz: null,
        orgcode: null
      },
      xzxmqueryParams: {
        pageNum: 1,
        pageSize: 10,
        xmtype: null,
        orgcode: null,
        xmbm: null,
        xmmc: null,
        xzcode: null,
        xzname: null,
        xzvalue: null,
        ybxz: null,
        createDate: null,
        updateDate: null,
        remarks: null,
        useflag: null
      },
      jkrulequeryParams: {
        pageNum: 1,
        pageSize: 100,
        gjcode: null,
        code: null,
        name: null,
        xzvalue: null,
        useflag: null,
        mzzy: null,
        mxxzflag: null,
        mustxzxx: null,
        mustchar: null,
        sort: null,
      },
      bzmlqueryParams: {
        pageNum: 1,
        pageSize: 5,
        bzmc: null,
        zjm: null,
        bzfl: null,
        tjm: null,
        jmbzfl: null,
        sybzfl: null
      },
      dictqueryParams: {
        pageNum: 1,
        pageSize: 5,
        dictCode: null,
        dictSort: null,
        dictLabel: null,
        dictValue: null,
        dictType: null,
        cssClass: null,
        listClass: null,
        isDefault: null,
        status: null,
      },
      // 表单参数
      form: {},
      xzxmForm: {},
      // 表单校验
      rules: {
        xmbm: [
          { required: true, message: "医保流水号不能为空", trigger: "blur" }
        ],
        fymid: [
          { required: true, message: "医院项目id不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "类别不能为空", trigger: "change" }
        ],
      },
      // 限制名称
      xzbm:null,
      useflag:null,
      // 当前操作行
      currentRow: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    updateColor(row, val) {
      const data = {
        id: row.id,
        showcolor: row.showcolor
      }
      updateXzxm(data).catch(err => {
        this.$modal.msgError(err)
      })
    },
    setXzData(item) {
      this.mustxzxx = item.mustxzxx
      this.selectXzmc = item.name
      this.xzbm = item.code
      this.useflag = item.useflag
      if (item.code == 'xzbz') {
        this.notbzandks = false
        this.bz = true
        this.ks = false
        this.ksopen = false
        this.getBzData()
        this.showBzData()
      }
      else if (item.code == 'xzks') {
        this.notbzandks = false
        this.bz = false
        this.ks = true
        this.bzopen = false
        this.getKsData()
        this.showKsData()
      }
      else {
        this.notbzandks = true
        this.bz = false
        this.ks = false
        this.bzopen = false
        this.ksopen = false
      }
    },
    setBzData(row) {
      this.selectBzXzvalue = row.bzmc
    },
    setKsData(row) {
      this.selectKsXzvalue = row.dictLabel
    },
    /** 查询药品管控设置列表 */
    getList() {
      listDz(this.queryParams).then(response => {
        this.dzList = response.rows;
        this.total = response.total;
      });
    },
    // 取消按钮
    cancel() {
      this.xzUpdateopen = false;
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        xmbm: null,
        fymid: null,
        jx: null,
        cd: null,
        gg: null,
        bz: null,
        name: null,
        dw: null,
        price: null,
        type: null,
        flag: null,
        ybjx: null,
        ybcd: null,
        ybgg: null,
        ybbz: null,
        ybname: null,
        ybdw: null,
        ybprice: null,
        fydj: null,
        opdate: null,
        nccd: null,
        createBy: null,
        createDate: null,
        updateBy: null,
        updateDate: null,
        remarks: null,
        delFlag: null,
        dxlbcode: null,
        dxlbname: null,
        xxlbcode: null,
        xxlbname: null,
        yycode: null,
        ybcode: null,
        lbcode: null,
        lbname: null,
        nameFlag: null,
        ggFlag: null,
        cdFlag: null,
        gjxmdm: null,
        ybxz: null,
        orgcode: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.ybname = null
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加药品管控设置";
    },
    /** 提交按钮 */
    submitForm() {
      if (this.xzxmForm.useflag == '提醒')
        this.xzxmForm.useflag = 1
      if (this.xzxmForm.useflag == '询问')
        this.xzxmForm.useflag = 2
      if (this.xzxmForm.useflag == '审核')
        this.xzxmForm.useflag = 3
      if (this.xzxmForm.useflag == '禁止')
        this.xzxmForm.useflag = 4
      if (this.xzxmForm.useflag == '不启用')
        this.xzxmForm.useflag = 5
      this.xzxmForm["createBy"] = this.$store.state.user.name
      updateXzxm(this.xzxmForm).then(response => {
        this.$modal.msgSuccess("修改成功");
        this.xzUpdateopen = false;
        this.getXzxmList(this.currentRow);
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除此条限制信').then(function() {
        return delXzxm(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
        this.getXzxmList(this.currentRow)
      }).catch(() => {});

    },
    /** 修改限制信息 */
    handleUpdate(row) {
      const id = row.id || this.ids
      getXzxm(id).then(response => {
        this.xzxmForm = response.data;
        if (this.xzxmForm.useflag == 1)
          this.xzxmForm.useflag = '提醒'
        if (this.xzxmForm.useflag == 2)
          this.xzxmForm.useflag = '询问'
        if (this.xzxmForm.useflag == 3)
          this.xzxmForm.useflag = '审核'
        if (this.xzxmForm.useflag == 4)
          this.xzxmForm.useflag = '禁止'
        if (this.xzxmForm.useflag == 5)
          this.xzxmForm.useflag = '不启用'
        this.xzUpdateopen = true;
        this.title = "修改限制信息";
      });

    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('gksz/dz/export', {
        ...this.queryParams
      }, `dz_${new Date().getTime()}.xlsx`)
    },
    getDataList(row) {
      this.xz = true
      this.getXzxmList(row)
      this.getJkruleList()
    },
    getXzxmList(row) {
      this.currentRow = row
      this.xzxmqueryParams.xmbm = row.xmbm
      listXzxm(this.xzxmqueryParams).then(response => {
        this.xzxmList = response.rows;
        this.xzxmTotal = response.total;
        for (let i = 0; i < this.xzxmList.length; i++) {
          if (this.xzxmList[i].useflag == 1)
            this.xzxmList[i].useflag = '提醒'
          if (this.xzxmList[i].useflag == 2)
            this.xzxmList[i].useflag = '询问'
          if (this.xzxmList[i].useflag == 3)
            this.xzxmList[i].useflag = '审核'
          if (this.xzxmList[i].useflag == 4)
            this.xzxmList[i].useflag = '禁止'
          if (this.xzxmList[i].useflag == 5)
            this.xzxmList[i].useflag = '不启用'
        }
      })
    },
    getJkruleList() {
      this.selectParams.cate = null
      this.jkrulequeryParams.name = null
      listJkrule(this.jkrulequeryParams).then(response => {
        this.jkruleList = response.rows;
        this.jkruleTotal = response.total;
      })
    },
    addNewValue() {
      var xzvalue = ''
      if (this.selectXzmc == '限病种') {
        if (this.selectBzXzvalue == null) {
          this.$modal.msgError("请选择限制病种");
          return
        }
        else {
          xzvalue = this.selectBzXzvalue
        }
      } else if (this.selectXzmc == '限科室使用') {
        if (this.selectKsXzvalue == null) {
          this.$modal.msgError("请选择限制科室");
          return
        }
        else {
          xzvalue = this.selectKsXzvalue
        }
      } else {
        if (this.mustxzxx == 1) {
          if (this.addParams.newValue == null) {
            this.$modal.msgError("请输入限定值");
            return
          } else {
            xzvalue = this.addParams.newValue
          }
        }
      }
      let time = new Date()
      var xzxmForm = {
        xmtype:this.currentRow.type,
        orgcode:'Y',
        xmbm:this.currentRow.xmbm,
        xmmc:this.currentRow.ybname,
        xzcode:this.xzbm,
        xzname:this.selectXzmc,
        xzvalue:xzvalue,
        ybxz: null,
        createBy: this.$store.state.user.name,
        createDate: this.timestampToTime(time.toLocaleString('en-US',{hour12: false}).split(" ")),
        updateBy: null,
        updateDate: this.timestampToTime(time.toLocaleString('en-US',{hour12: false}).split(" ")),
        remarks: null,
        useflag:this.useflag,
        showcolor:this.addParams.showcolor,
      }
      console.log(xzxmForm)
      addXzxm(xzxmForm).then(response => {
        this.$modal.msgSuccess("新增成功");
        this.open = false;
        this.addParams.newValue = null
        this.selectBzXzvalue = null
        this.selectKsXzvalue = null
        this.selectXzmc = null
        this.getXzxmList(this.currentRow);
      });
      this.bzopen = false
      this.ksopen = false
      this.xzopen = false
    },
    getBzData() {
      listBzml(this.bzmlqueryParams).then(response => {
        this.bzmlList = response.rows;
        this.bzmlTotal = response.total;
      });
    },
    timestampToTime(times) {
      let time = times[1]
      let mdy = times[0]
      mdy = mdy.split('/')
      let month = parseInt(mdy[0]);
      let day = parseInt(mdy[1]);
      let year = parseInt(mdy[2])
      return year + '-' + month + '-' + day + ' ' + time
    },
    getKsData() {
      this.dictqueryParams.dictType = "dept"
      listData(this.dictqueryParams).then(response => {
        this.dataList = response.rows;
        this.dataTotal = response.total;
      });
    },
    showBzData() {
      this.bzopen = true
    },
    showKsData() {
      this.ksopen = true
    },
    queryBzData() {
      listBzml(this.bzmlqueryParams).then(response => {
        this.bzmlList = response.rows;
        this.bzmlTotal = response.total;
      });
    },
    resetBzData() {
      this.bzmlqueryParams.bzmc = null
      listBzml(this.bzmlqueryParams).then(response => {
        this.bzmlList = response.rows;
        this.bzmlTotal = response.total;
      });
    },
    queryKsData() {
      listData(this.dictqueryParams).then(response => {
        this.dataList = response.rows;
        this.dataTotal = response.total;
      });
    },
    resetKsData() {
      this.dictqueryParams.dictLabel = null
      listData(this.dictqueryParams).then(response => {
        this.dataList = response.rows;
        this.dataTotal = response.total;
      });
    },
    resetData() {
      this.ksopen = false
      this.bzopen = false
      this.selectXzmc = null
      this.addParams.newValue = null
    },
    addXzData() {
      this.xzopen = true
    }
  }
};
</script>
