<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目编码" prop="xmbm">
        <el-input
          v-model="queryParams.xmbm"
          placeholder="请输入项目编码"
          clearable
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="xmmc">
        <el-input
          v-model="queryParams.xmmc"
          placeholder="请输入项目名称"
          clearable
        />
      </el-form-item>
<!--      <el-form-item label="检查比例" prop="percentage">-->
<!--        <el-input-->
<!--          v-model="queryParams.percentage"-->
<!--          placeholder="请输入检查比例"-->
<!--          clearable-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label-width="100px" label="是否打包收费" prop="isBind">-->
<!--        <el-select clearable placeholder="请选择是否打包收费" v-model="queryParams.isBind">-->
<!--          <el-option value="0" name="否" label="否"></el-option>-->
<!--          <el-option value="1" name="是" label="是"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label-width="100px" label="绑定项目编码" prop="bindXmbm">-->
<!--        <el-input-->
<!--          v-model="queryParams.bindXmbm"-->
<!--          placeholder="请输入绑定项目编码"-->
<!--          clearable-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label-width="100px" label="绑定项目名称" prop="bindXmmc">-->
<!--        <el-input-->
<!--          v-model="queryParams.bindXmmc"-->
<!--          placeholder="请输入绑定项目名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['gksz:wgpz:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['gksz:wgpz:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['gksz:wgpz:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['gksz:wgpz:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="wgpzList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column width="450" show-overflow-tooltip label="项目编码" align="left" prop="xmbm"/>
      <el-table-column width="450" show-overflow-tooltip label="项目名称" align="left" prop="xmmc"/>
      <!--      <el-table-column label="检查类型" align="left" prop="type" />-->
      <el-table-column label="检查比例" align="left" prop="percentage"/>
<!--      <el-table-column label="是否打包收费" align="left" prop="isBind">-->
<!--        <template slot-scope="scope">-->
<!--          {{ scope.row.isBind == '1' ? '是' : '否' }}-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column width="250" show-overflow-tooltip label="绑定项目编码" align="left" prop="bindXmbm"/>-->
<!--      <el-table-column width="250" show-overflow-tooltip label="绑定项目名称" align="left" prop="bindXmmc"/>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['gksz:wgpz:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['gksz:wgpz:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


    <el-drawer
      :title="title"
      :visible.sync="open"
      direction="rtl"
      :wrapper-closable="false"
      size="70%">

      <el-form style="margin: 20px" label-position="top" ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="项目编码" prop="xmbm">
          <el-select
            style="width: 100%;"
            v-model="form.xmbm"
            filterable
            remote
            reserve-keyword
            placeholder="请输入项目编码"
            :remote-method="getProData"
            @change="handleSelectChange"
            :loading="searchLoading">
            <el-option
              v-for="(item, index) in proList"
              :key="index"
              :label="item.xmbm + '[' + item.ybname + ']'"
              :value="item.xmbm">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="xmmc">
          <el-input disabled v-model="form.xmmc" placeholder="请输入项目名称"/>
        </el-form-item>
        <el-form-item label="检查比例" prop="percentage">
          <el-input type="number" v-model="form.percentage" placeholder="请输入检查比例"/>
        </el-form-item>
<!--        <el-form-item label="是否打包收费" prop="isBind">-->
<!--          <el-select placeholder="请选择是否打包收费" v-model="form.isBind">-->
<!--            <el-option value="1" label="是"/>-->
<!--            <el-option value="0" label="否"/>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="绑定项目编码" prop="bindXmbm">-->
<!--          <el-select-->
<!--            style="width: 100%;"-->
<!--            v-model="form.bindXmbm"-->
<!--            filterable-->
<!--            remote-->
<!--            reserve-keyword-->
<!--            placeholder="请输入绑定项目编码"-->
<!--            :remote-method="getProData"-->
<!--            @change="handleSelectChangeBind"-->
<!--            :loading="searchLoading">-->
<!--            <el-option-->
<!--              v-for="(item, index) in proList"-->
<!--              :key="index"-->
<!--              :label="item.xmbm + '[' + item.ybname + ']'"-->
<!--              :value="item.xmbm">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="绑定项目名称" prop="bindXmmc">-->
<!--          <el-input disabled v-model="form.bindXmmc" placeholder="请输入绑定项目名称"/>-->
<!--        </el-form-item>-->
        <el-form-item>
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </el-form-item>
      </el-form>

    </el-drawer>


  </div>
</template>

<script>
import {addWgpz, delWgpz, getWgpz, listWgpz, updateWgpz} from "@/api/gksz/wgpz";
import tabs from "@/components/DetailsTabs/tabs.vue";
import {getProList} from "@/api/gksz/dz";

export default {
  name: "Wgpz",
  components: {tabs},
  data() {
    return {
      // 遮罩层
      loading: true,
      searchLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 费用项目违规配置表格数据
      wgpzList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        xmbm: null,
        xmmc: null,
        type: null,
        percentage: null,
        isBind: null,
        bindXmbm: null,
        bindXmmc: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        xmbm: [
          {required: true, message: "项目编码不能为空", trigger: "blur"}
        ],
        xmmc: [
          {required: true, message: "项目名称不能为空", trigger: "blur"}
        ],
        percentage: [
          {required: true, message: "检查比例不能为空", trigger: "blur"},
          {validator: this.checkNumber, trigger: 'blur'}
        ],
        // isBind: [
        //   {required: true, message: "是否打包收费不能为空", trigger: "blur"},
        //   {validator: this.checkIsBind, trigger: 'blur'}
        // ],
        // bindXmbm: [
        //   {validator: this.checkBind, trigger: 'blur'}
        // ],
        // bindXmmc: [
        //   {validator: this.checkBind, trigger: 'blur'}
        // ],
      },
      proList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    checkBind(rule, value, callback) {
      if (!value  && this.form.isBind == '1') {
        return callback(new Error('打包项目不能为空'));
      } else {
        callback();
      }
    },
    checkIsBind(rule, value, callback) {
      if (value && value == 0 && this.form.bindXmbm != null) {
        return callback(new Error('存在打包信息'));
      } else {
        callback();
      }
    },
    handleSelectChange(value) {
      const selectedItem = this.proList.find(item => item.xmbm === value);
      if (selectedItem) {
        this.form.xmmc = selectedItem.ybname;
      }
    },
    handleSelectChangeBind(value) {
      const selectedItem = this.proList.find(item => item.xmbm === value);
      if (selectedItem) {
        this.form.bindXmmc = selectedItem.ybname;
      }
    },
    getProData(value) {
      getProList(value).then(res => {
        this.proList = res.rows
      }).catch(err => {
        this.$modal.msgError("获取失败" + err)
      })
      this.proList = [];
    },
    checkNumber(rule, value, callback) {
      if (value && (value <= 0 || value >= 1)) {
        return callback(new Error('比例应该在0-1之间！'));
      } else {
        callback();
      }
    },
    /** 查询费用项目违规配置列表 */
    getList() {
      this.loading = true;
      listWgpz(this.queryParams).then(response => {
        this.wgpzList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        xmbm: null,
        xmmc: null,
        type: null,
        percentage: null,
        isBind: null,
        bindXmbm: null,
        bindXmmc: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加费用项目违规配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWgpz(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改费用项目违规配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.form.type = '过度诊疗'
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWgpz(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWgpz(this.form).then(response => {
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$modal.msgError("当前项目已存在");
                this.open = false;
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除费用项目违规配置编号为"' + ids + '"的数据项？').then(function () {
        return delWgpz(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('gksz/wgpz/export', {
        ...this.queryParams
      }, `wgpz_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
