<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="住院号" prop="zyh">
        <el-input
          style="width: 100px"
          v-model="queryParams.zyh"
          placeholder="住院号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="病人姓名" prop="brname">
        <el-input
          style="width: 100px"
          v-model="queryParams.brname"
          placeholder="病人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用项目" prop="fymName">
        <el-input
          v-model="queryParams.fymName"
          placeholder="费用项目"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="科室" prop="kdksname">
        <el-select v-model="queryParams.kdksname"
                   @focus="resetDept"
                   @change="getDoctorByDept(queryParams.kdksname)"
                   filterable
                   :filter-method="deptFilter"
                   placeholder="科室"
                   clearable
                   style="width: 150px">
          <el-option v-for="(item,index) in deptList" :value="item.kdksname" :key="index" :label="item.kdksname"/>
        </el-select>
      </el-form-item>

      <el-form-item label="医生" prop="doctorname">
        <el-select v-model="queryParams.doctorname"
                   @focus="resetDoctor"
                   @change="resetDoctor"
                   filterable
                   :filter-method="doctorFilter"
                   placeholder="医生"
                   clearable
                   style="width: 100px">
          <el-option v-for="(item,index) in doctorList" :value="item.doctorname" :key="index" :label="item.doctorname"/>
        </el-select>
      </el-form-item>

      <el-form-item label="监控类别" prop="jktypes">
        <el-select multiple v-model="queryParams.jktypes" placeholder="监控类别" clearable>
          <el-option v-for="item in jkruleList" :value="item.code" :label="item.name"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-select v-model="queryParams.zyzt" placeholder="在院状态" clearable style="width: 100px">
          <el-option v-for="item in zyztList" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间" prop="createDate">
        <el-date-picker
          v-model="queryParams.createDateStart"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.createDateEnd"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>


      <el-form-item label="严重性" prop="xzlb">
        <ColorSelector placeholder="请选择严重性" isUpdate="false" :item="queryParams" column="xzlb"
                       :color="queryParams.xzlb"/>
      </el-form-item>


      <el-form-item label="物价限制" prop="wjxzflag">
        <el-select v-model="queryParams.wjxzflag" placeholder="物价限制" clearable style="width: 100px">
          <el-option v-for="item in wjxzflagOptions" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
        <el-button type="success" size="mini" @click="batchFyshByWgjlDays" v-if="pageId == 1">批量审核</el-button>
        <el-button type="danger" size="mini" @click="toSend">发送</el-button>
      </el-form-item>
    </el-form>

    <virtual-scroll
      ref="virtualScroll"
      :data="wgjlList"
      :item-size="70"
      @selection-change="handleSelectionChange"
      key-prop="id"
      @change="onChange">
      <el-table height="550" stripe :border="true" row-key="id" v-loading="loading" :data="virtualList" highlight-current-row>
        <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width" width="120px">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleDelete(scope.row)"
              v-hasPermi="['gksz:wgjl:remove']"
            >删除
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleExclude(scope.row)"
            >排除
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="detail(scope.row)"
            >详情
            </el-button>
          </template>
        </el-table-column>
        <virtual-column class-name="small-padding fixed-width" type="selection" width="40" align="center"/>
        <el-table-column sortable label="住院号" align="center" prop="zyh" show-overflow-tooltip width="130px"/>
        <el-table-column sortable label="病人姓名" align="center" prop="brname" show-overflow-tooltip width="100px"/>
        <el-table-column sortable label="违规内容" align="left" prop="jklog" show-overflow-tooltip width="650px">
          <template slot-scope="scope">
            <div>
            <span :style="{color: scope.row.xzlb == '#FFFFFF' ? '#000000' : scope.row.xzlb}">
              {{ scope.row.jklog }}
            </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          :filters="deptNameFilterList"
          :filter-method="deptFilterMethod"
          width="100px"
          show-overflow-tooltip
          label="就诊科室"
          align="center"
          prop="kdksname"/>

        <el-table-column
          :filters="doctorNameFilterList"
          :filter-method="doctorFilterMethod"
          show-overflow-tooltip
          label="医生"
          align="center"
          prop="doctorname"/>

        <el-table-column sortable label="违规数量" align="center" width="100px" prop="sl"/>
        <el-table-column sortable label="创建时间" align="center" prop="createDate" width="120px">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column sortable label="床号" align="center" prop="bed" width="80px"/>
        <el-table-column sortable label="违规金额" align="center" width="100px" prop="je"/>
        <el-table-column sortable label="疑点名称" prop="ybbz" show-overflow-tooltip width="650px"/>
        <el-table-column sortable label="参保类别" align="center" prop="insutype" width="80px"/>
       <!-- <el-table-column sortable label="身份证" align="center" prop="sfz" show-overflow-tooltipwidth="80px" /> -->
        <el-table-column sortable label="医保报销" align="center" prop="tc" width="80px"/>
        <el-table-column sortable label="费用明细" align="center" prop="fymx" show-overflow-tooltip width="150px"/>
         <el-table-column sortable label="项目名称" align="center" prop="fymName" show-overflow-tooltip width="150px"/>
        <el-table-column sortable label="处理信息" align="center" prop="tshOper" width="120px"/>
      </el-table>
    </virtual-scroll>

    <br>

    <pagination
      :page-sizes="pageSizes"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :visible.sync="loading1"
               :close-on-press-escape="false"
               :close-on-click-modal="false"
               :show-close="false"
               width="20%"
               center>
      <div>请等待...</div>
    </el-dialog>

    <el-drawer
      :title="title"
      :visible.sync="open"
      direction="rtl"
      :wrapper-closable="false"
      size="80%">
      <tabs :jzh="jzh"></tabs>
    </el-drawer>


    <el-drawer
      title=""
      :visible.sync="sendDrawerVisible"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeDrawer"
      v-loading="sendLoading"
      size="80%">

      <div style="margin: 15px;">
        <el-form :model="sendParams" :inline="true">

          <el-form-item label="科室" prop="deptName">
            <el-select v-model="sendParams.checkDept"
                       @change="sendFormGetDoctorByDept(sendParams.checkDept)"
                       filterable
                       placeholder="科室"
                       clearable
                       style="width: 150px">
              <el-option v-for="(item,index) in sendDeptList" :value="item.hDeptName" :key="index"
                         :label="item.hDeptName"/>
            </el-select>
          </el-form-item>

          <el-form-item label="医生" prop="doctorName">
            <el-select v-model="sendParams.checkDoctor"
                       filterable
                       placeholder="医生"
                       clearable
                       style="width: 100px">
              <el-option v-for="(item,index) in sendDoctorList" :value="item.nickName" :key="index"
                         :label="item.nickName"/>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="danger" style="margin-left: 20px" @click="send">发送</el-button>
          </el-form-item>
        </el-form>
      </div>

      <virtual-scroll
        style="margin: 15px;"
        ref="virtualScroll1"
        :data="sendParams.wgjlList"
        :item-size="70"
        key-prop="id"
        @change="onChange2">
        <el-table height="600" stripe :border="true" row-key="id" :data="virtualList2">
          <el-table-column sortable label="住院号" align="center" prop="zyh" show-overflow-tooltip width="130px"/>
          <el-table-column sortable label="病人姓名" align="center" prop="brname" show-overflow-tooltip width="100px"/>
          <el-table-column sortable prop="jklog" label="违规内容" align="left" show-overflow-tooltip width="650px">
            <template slot-scope="scope">
              <div>
            <span :style="{color: scope.row.xzlb == '#FFFFFF' ? '#000000' : scope.row.xzlb}">
              {{ scope.row.jklog }}
            </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column sortable label="就诊科室" align="center" width="100px" prop="kdksname"
                           show-overflow-tooltip/>
          <el-table-column sortable label="医生" align="center" prop="doctorname" show-overflow-tooltip/>
          <el-table-column sortable label="违规数量" align="center" width="100px" prop="sl"/>
          <el-table-column sortable label="创建时间" align="center" prop="createDate" width="120px">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable label="床号" align="center" prop="bed" width="80px"/>
          <el-table-column sortable label="违规金额" align="center" width="100px" prop="je"/>
          <el-table-column sortable label="疑点名称" prop="ybbz" show-overflow-tooltip width="650px"/>
          <el-table-column sortable label="处理信息" align="center" prop="tshOper" width="120px"/>
          <el-table-column sortable label="备注" align="center" prop="checkRemark" width="400px">
            <template slot-scope="scope">
              <el-input type="textarea" :maxlength="100" :show-word-limit="true" v-model="scope.row.checkRemark" placeholder="备注"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </virtual-scroll>
    </el-drawer>
  </div>
</template>

<script>
import {delWgjl, listWgjl, listWgjl2, listWgjlKs, listWgjlYs, sendWgklCheck} from "@/api/gksz/wgjl";
import {listRules} from "@/api/gksz/jkrule";
import tabs from "@/components/DetailsTabs/tabs"
import PinYinMatch from 'pinyin-match';
import {MessageBox} from "element-ui";
import {batchFyshByWgjlDays} from "@/api/gksz/fysh";
import VirtualScroll, {VirtualColumn} from "el-table-virtual-scroll";
import {selectDoctorByHDeptName} from "@/api/system/user";
import {getTodayLastSecondStr, getYesterdayFirstSecondStr} from "@/utils/dateUtils";
import {selectHDeptNameList} from "@/api/system/hdept";
import { ybExcludeWgjl } from '@/api/tjfx/ybgkwgjlHistory'


export default {
  name: "Wgjl",
  data() {
    return {
      pageSizes: [10,50,100,2000,5000,10000],
      sendParams: {
        checkDept: null,
        checkDoctor: null,
        wgjlList: [],
      },
      wjxzflagOptions: [
        {
          value: "1",
          label: "是"
        },
        {
          value: "0",
          label: "否"
        }
      ],
      deptNamePym: "",
      sendDrawerVisible: false,
      shopen: false,
      jzh: null,
      // 选中数组
      selectItems: [],
      //审核信息
      jklogList: [],
      cwxx: null,
      pageId: null,
      // 遮罩层
      loading: true,
      loading1: false,
      sendLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      jklogTotal: 0,
      // 违规查询表格数据
      wgjlList: [],
      doctorNameFilterList: [],
      deptNameFilterList: [],
      virtualList: [],
      virtualList2: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      doctorList: [],
      doctorListby: [],
      queryParams: {
        pageNum: 0,
        pageSize: 10,
        jzh: null,
        jklog: null,
        jktypes: null,
        brname: null,
        kdksname: null,
        doctorname: null,
        fymName: null,
        sl: null,
        je: null,
        clFlag: null,
        clHfxx: null,
        createDate: null,
        zyh: null,
        ybbz: null,
        createDateStart: getYesterdayFirstSecondStr(),
        createDateEnd: getTodayLastSecondStr(),
        tshOper: null,
        fyxm: null,
        xzlb: null,
        wjxzflag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        createDate: [
          {required: true, message: "创建时间不能为空", trigger: "blur"}
        ],
      },
      deptList: [],
      deptListby: [],  //备用
      jkruleList: [],
      zyztList: [
        {
          value: '1',
          name: '在院',
          label: '在院'
        },
        {
          value: '0',
          name: '出院',
          label: '出院'
        }
      ],
      sendDeptList: [],
      sendDoctorList: [],
    };
  },
  created() {
    this.pageId = this.$route.query.id
    this.init();
  },
  components: {
    VirtualColumn,
    VirtualScroll,
    tabs
  },
  methods: {
    deptFilterMethod(value, row) {
      return row.kdksname == value
    },
    doctorFilterMethod(value, row) {
      return row.doctorname == value
    },
    async getSendDeptInfo() {
      const res = await selectHDeptNameList()
      if (res.code == 200) {
        this.sendDeptList = res.rows
      }
    },
    async sendFormGetDoctorByDept(deptId) {
      this.sendParams.checkDoctor = null
      this.sendDoctorList = []
      if (!deptId) {
        return
      }
      const res = await selectDoctorByHDeptName(deptId)
      if (res.code == 200) {
        this.sendDoctorList = res.rows
      }
    },
    async send() {
      if (!this.sendParams.checkDoctor || !this.sendParams.checkDept) {
        this.$modal.msgWarning("请选择科室与医生！")
        return
      }
      this.sendLoading = true
      const data = {
        checkDept: this.sendParams.checkDept,
        checkDoctor: this.sendParams.checkDoctor,
        wgjlList: this.sendParams.wgjlList.map(item => ({ id: item.id, checkRemark: item.checkRemark }))
      }
      const res = await sendWgklCheck(data).catch(err => {
        this.sendLoading = false
        if (err.toString().indexOf("Storage") > -1) {
          this.$modal.msgError("选择数据过多，超出浏览器限制！")
        } else {
          this.$modal.msgError("发送失败！")
        }
      })
      if (res.code == 200) {
        this.sendDrawerVisible = false
        this.sendLoading = false
        this.$modal.msgSuccess("发送成功！")
      }
    },
    closeDrawer() {
      this.sendParams = {
        checkDept: null,
        checkDoctor: null,
        wgjlList: [],
      }
    },
    handleSelectionChange(selection) {
      this.selectItems = selection.map(item => item)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    onChange(renderData) {
      this.virtualList = renderData
    },
    onChange2(renderData) {
      this.virtualList2 = renderData
    },
    toSend() {
      if (!this.selectItems || this.selectItems.length == 0) {
        this.$modal.msgWarning("请选择违规记录！")
        return
      }
      this.sendParams.wgjlList = this.selectItems
      this.sendDrawerVisible = true
    },
    async batchFyshByWgjlDays() {
      MessageBox.alert("是否进行批量审核，将审核在规定天数内存在违规记录的病人,将花费较长时间", "系统提示", {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
      }).then(res => {
        if (res == "confirm") {
          batchFyshByWgjlDays()
        }
      }).catch(err => {
      })
    },
    resetDept() {
      this.deptList = this.deptListby
    },
    resetDoctor() {
      this.doctorList = this.doctorListby
    },
    deptFilter(val) {
      this.queryParams.kdksname = val
      if (val) {
        this.deptList = []
        var deptList = this.deptListby.filter((item) => {
          if (PinYinMatch.match(item.kdksname, val)) {
            return true
          }
        })
        this.deptList = deptList
      } else {
        this.deptList = this.deptListby
      }
    },
    doctorFilter(val) {
      this.queryParams.doctorname = val
      if (val) {
        this.doctorList = []
        var doctorList = this.doctorListby.filter((item) => {
          if (PinYinMatch.match(item.doctorname, val)) {
            return true
          }
        })
        this.doctorList = doctorList
      } else {
        this.doctorList = this.doctorListby
      }
    },
    getDoctorByDept(deptName) {
      this.doctorList = []
      this.doctorListby = []
      this.queryParams.doctorname = null
      this.deptList = this.deptListby
      if (!deptName) {
        return
      }
      listWgjlYs({kdksname: deptName, brtype: this.wgType}).then(response => {
        this.doctorList = response.rows;
        this.doctorListby = JSON.parse(JSON.stringify(this.doctorList))
      });
    },
    async init() {
      this.wgType = this.pageId == 1 ? 2 : 1
      this.getList();
      this.getDeptList()
      this.getDoctorList()
      this.getRulesList()
      this.getSendDeptInfo()
    },
    getDeptList() {
      listWgjlKs({brtype: this.wgType}).then(response => {
        this.deptList = response.rows;
        this.deptListby = JSON.parse(JSON.stringify(this.deptList))
      });
    },
    getDoctorList() {
      listWgjlYs({brtype: this.wgType}).then(response => {
        this.doctorList = response.rows;
        this.doctorListby = JSON.parse(JSON.stringify(this.doctorList))
      });
    },
    getRulesList() {
      listRules(this.queryParams).then(response => {
        this.jkruleList = response.rows;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id;
      this.$modal.confirm('是否确认删除当前违规记录？').then(function () {
        return delWgjl(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    handleExclude(row) {
      ybExcludeWgjl({id: row.id}).then(response => {
        this.$modal.msgSuccess("操作成功，请重新审核该病人");
        this.getList();
      }).catch(e => {
        this.$modal.msgError(e.message);
      })
    },
    detail(row) {
      this.jzh = ""
      this.jzh = row.jzh
      this.open = true
      this.title =  row.zyh + '[' +  row.brname + ']患者详情'
    },
    /** 查询违规查询列表 */
    getList() {
      this.loading = true;
      if (!this.queryParams.createDateStart || !this.queryParams.createDateEnd) {
        this.queryParams.createDateStart = getYesterdayFirstSecondStr()
        this.queryParams.createDateEnd = getTodayLastSecondStr()
      }
      if (this.pageId == 1) {
        listWgjl(this.queryParams).then(response => {
          this.wgjlList = response.rows;
          this.total = response.total;
          this.loading = false;
          this.getDeptNameFilterList()
          this.getDoctorNameFilterList()
        });
      } else if (this.pageId == 2) {
        listWgjl2(this.queryParams).then(response => {
          this.wgjlList = response.rows;
          this.total = response.total;
          this.loading = false;
          this.getDeptNameFilterList()
          this.getDoctorNameFilterList()
        });
      }
    },
    getDeptNameFilterList() {
      const deptNameList = [...new Set(this.wgjlList.map(item => item.kdksname))]
      this.deptNameFilterList = deptNameList.map(kdksname => ({ value: kdksname, text: kdksname }))
    },
    getDoctorNameFilterList() {
      const doctorNameList = [...new Set(this.wgjlList.map(item => item.doctorname))]
      this.doctorNameFilterList = doctorNameList.map(doctorname => ({ value: doctorname, text: doctorname }))
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        jzh: null,
        jklog: null,
        jktypes: null,
        brname: null,
        kdksname: null,
        doctorname: null,
        fymName: null,
        sl: null,
        je: null,
        clFlag: null,
        clHfxx: null,
        createDate: null,
        zyh: null,
        ybbz: null,
        createDateStart: getYesterdayFirstSecondStr(),
        createDateEnd: getTodayLastSecondStr(),
        tshOper: null,
        fyxm: null,
        xzlb: null,
        wjxzflag: null,
      },
        this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.pageId == 1) {
        this.download('gksz/wgjl/export', {
          ...this.queryParams
        }, `wgjl_${new Date().getTime()}.xlsx`)
      } else if (this.pageId == 2) {
        this.download('gksz/wgjl/export2', {
          ...this.queryParams
        }, `wgjl_${new Date().getTime()}.xlsx`)
      }
    },
    cancel() {
      this.open = false;
    },
  }
};
</script>

<style>
</style>
