<template>
  <div v-loading="loading" class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="住院号" prop="zyh">
        <el-input
          v-model="queryParams.zyh"
          placeholder="请输入住院号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="科室" prop="deptname">
        <el-select v-model="queryParams.deptname"
                   placeholder="科室" clearable style="width: 150px">
          <el-option v-for="item in deptList" :value="item.deptname">{{ item.deptname }}</el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="日期类型">
        <el-select v-model="queryParams.dateType" placeholder="日期类型" clearable>
          <el-option v-for="item in datetypeList" :value="item.value" :label="item.name">{{ item.name }}</el-option>
        </el-select>
      </el-form-item>


      <el-form-item label="日期">
        <el-date-picker
          v-model="queryParams.adtFrom"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.adtTo"
          type="datetime"
          placeholder="请选择截至日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          default-time="['23:59:59']"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>


      <el-form-item label="在院状态" prop="zyzt">
        <el-select
          clearable
          v-model="queryParams.zyzt"
          placeholder="请输入在院状态"
        >
          <el-option value="1" label="在院">在院</el-option>
          <el-option value="0" label="出院">出院</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
        <el-button type="success" size="mini" @click="batchFysh">批量审核</el-button>
        <el-button type="danger" size="mini" @click="brxxSync">同步</el-button>
        <el-button type="info" size="mini" @click="openHistoryCheck"  v-has-role="['admin']">审核历史病人</el-button>
      </el-form-item>
    </el-form>

    <div id="content">

    </div>

    <el-table :row-class-name="getRowClassName" :data="fyshList">
      <el-table-column label="住院号" align="center" prop="zyh"/>
      <el-table-column label="姓名" align="center" prop="name"/>
      <el-table-column label="年龄" align="center" prop="age"/>
      <el-table-column label="入院日期" align="center" prop="rydate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.rydate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出院日期" align="center" prop="cydate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.cydate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="医生姓名" align="center" prop="doctorname"/>
      <el-table-column label="科室名称" align="center" prop="deptname"/>
      <el-table-column width="230px" label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="transfyblzd(scope.row)"
            v-hasPermi="['gksz:fysh:edit']"
          >同步
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="getJklogList(scope.row,scope.$index)"
            v-hasPermi="['gksz:fysh:edit']"
          >审核
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="czfy(scope.row)"
            v-hasPermi="['gksz:fysh:edit']"
          >重转费用
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-upload2"
            @click="detail(scope.row)"
            v-hasPermi="['gksz:fysh:remove']"
          >详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="pageSizes"
      @pagination="getList"
    />

    <br>

    <el-table :data="jklogList" v-if="shopen" height="450px">
      <el-table-column label="住院号" align="left" prop="zyh" min-width="10%"/>
      <el-table-column label="超限内容" align="left" prop="jklog" min-width="40%">
        <template slot-scope="scope">
          <div>
            <span :style="{color: scope.row.xzlb == '#FFFFFF' ? '#000000' : scope.row.xzlb}">
              {{ scope.row.jklog }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="疑点名称" align="left" prop="ybbz" min-width="40%"/>
      <el-table-column label="数量" align="center" prop="sl" min-width="5%"/>
      <el-table-column label="金额" align="center" prop="je" min-width="5%"/>
      <el-table-column width="200px" label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-remove"
            @click="delWgjlItem(scope.row,scope.$index)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>


    <el-dialog :title="title" :visible.sync="open" width="1500px" append-to-body>

      <tabs :jzh="jzh"></tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="loading1"
               :close-on-press-escape="false"
               :close-on-click-modal="false"
               :show-close="false"
               width="20%"
               center
    >
      <div>请等待...</div>
    </el-dialog>

    <el-dialog
      title="历史病人审核"
      :visible.sync="historyCheckDialogVisible"
      width="850px"
      append-to-body
    >
      <el-form :model="historyCheckForm" label-width="100px">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="historyCheckForm.startDate"
            type="datetime"
            placeholder="开始日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 220px"
          />
          <span style="margin: 0 5px">至</span>
          <el-date-picker
            v-model="historyCheckForm.endDate"
            type="datetime"
            placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 220px"
          />
        </el-form-item>
        <el-form-item label="审核选项">
          <el-checkbox v-model="historyCheckForm.autoCompleteExpense" style="margin-right: 20px">自动补全费用</el-checkbox>
          <el-checkbox v-model="historyCheckForm.checkMz" style="margin-right: 20px">审核门诊</el-checkbox>
          <el-checkbox v-model="historyCheckForm.checkZy">审核住院</el-checkbox>
        </el-form-item>
        <el-form-item label="审核口令">
         <el-input
           v-model="historyCheckForm.pwd"
           placeholder="因为该功能只允许管理员执行请输入审核口令,不清楚请联系软件公司"
           clearable
           @keyup.enter.native="handleQuery"
         />

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="historyCheckDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="checkHistoryPatBatch">确 定</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import {
  listFysh,
  copyfyblzd,
  listks,
  batchFysh,
  refreshCost,
  checkPatThreeDaysAgo,
  checkHistoryPatBatch
} from '@/api/gksz/fysh'
import {cysh, delWgjl} from "@/api/gksz/wgjl";
import tabs from "@/components/DetailsTabs/tabs"
import {MessageBox} from "element-ui";
import {brxxSync} from "@/api/system/sync";

export default {
  name: "Fysh",
  data() {
    return {
      historyCheckDialogVisible: false,
      historyCheckForm: {
        startDate: null,
        endDate: null,
        autoCompleteExpense: false,
        checkMz: true,
        checkZy: true
      },
      pageSizes: [5],
      jzh: null,
      cwxx: null,
      zyzt: '在院',
      daterangeDate: [],
      shopen: false,
      //审核信息
      jklogList: [],
      // 遮罩层
      loading: true,
      loading1: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      jklogTotal: 0,
      // 费用审核表格数据
      fyshList: [],
      deptList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      datetypeList: [
        {name: "出院时间", value: "cydate"},
        {name: "入院时间", value: "rydate"}
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        brtype: null,
        jzh: null,
        brid: null,
        zyid: null,
        zyh: null,
        name: null,
        bed: null,
        age: null,
        sex: null,
        tel: null,
        ybh: null,
        jgid: null,
        rydate: null,
        zyzt: null,
        deptid: null,
        doctorname: null,
        bzname: null,
        doctorid: null,
        bzcode: null,
        deptname: null,
        cydate: null,
        dateType: null,
        adtFrom: null,
        adtTo: null,
      },
      jklogqueryParams: {
        pageNum: 1,
        pageSize: 10000,
        jktype: null,
        jklog: null,
        sl: null,
        je: null,
        jzh: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.init()
    this.getList();
  },
  components: {
    tabs
  },
  methods: {
    // 打开批量审核dialog
    openHistoryCheck(){
      this.historyCheckDialogVisible = true;

    },
    // 批量审核历史病人
    checkHistoryPatBatch(){
      if (!this.historyCheckForm.startDate || !this.historyCheckForm.endDate) {
        this.$message.warning('请选择完整的时间范围');
        return;
      }

     // 将日期字符串转换为 Date 对象
       const startDate = new Date(this.historyCheckForm.startDate);
       const endDate = new Date(this.historyCheckForm.endDate);

       // 计算时间差（以毫秒为单位）
       const timeDifference = endDate.getTime() - startDate.getTime();

      //  // 将时间差转换为天数
      //  const dayDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));
      // if (dayDifference>366){
      //   this.$message.warning('审核时间不能超过365天');
      //   return;
      // }

      if(this.historyCheckForm.pwd!='zkysh'){
        this.$message.warning('请录入正确的审核口令');
        return;
      }

      this.loading = true;
      this.historyCheckDialogVisible = false;

      // 调用接口，传入时间范围和是否自动补全费用参数
      checkHistoryPatBatch({
        startDate: this.historyCheckForm.startDate,
        endDate: this.historyCheckForm.endDate,
        autoCompleteExpense: this.historyCheckForm.autoCompleteExpense,
        checkMz: this.historyCheckForm.checkMz,
        checkZy: this.historyCheckForm.checkZy
      }).then(res => {
        if (res.code === 200) {
          this.$modal.msgSuccess("该操作将花较长时间，请不要重复审核");
        } else {
          this.$modal.msgError(res.msg || "审核操作失败");
        }
      }).catch(err => {
        this.$modal.msgError("审核操作异常");
      }).finally(() => {
        this.loading = false;
      });
    },
    async delWgjlItem(row,index) {
      try {
        const confirmResult = await MessageBox.confirm('是否确定删除此条违规记录？');
        if (confirmResult == 'confirm') {
          const res = await delWgjl(row.id);
          if (res.code == 200) {
            this.$modal.msgSuccess("删除成功");
            this.jklogList.splice(index, 1);
          } else {
            this.$modal.msgError(res.message || "删除失败");
          }
        }
      } catch (err) {
        this.$modal.msgError("已取消删除");
      }
    },
    getRowClassName({ row, rowIndex }) {
      return row.isSelected ? 'selected-row' : '';
    },
    init() {
      listks().then(res => {
        this.deptList = res.rows
      })
      this.queryParams.zyzt = '1'
    },
    //重新获取该病人费用
    czfy(row){
      this.loading = true
      refreshCost(row.jzh).then(res => {
        if(res.code === 200) {
          this.$modal.msgSuccess("操作成功")
        } else {
          this.$modal.msgError("操作失败")
        }
        this.loading = false
      }).catch(err => {
        this.loading = false
      }).finally({
      })
    },
    //获取审核信息
    getJklogList(row,index) {
      this.shopen = false
      this.jklogList = []
      this.cwxx = null
      this.jklogqueryParams.jzh = row.jzh
      this.loading1 = true
      cysh(this.jklogqueryParams).then(response => {

        this.jklogList = response.rows[0]
       if (!Array.isArray(this.jklogList)) {
          return;
       }
        this.jklogTotal = response.rows[0].length
        this.shopen = true
        this.loading1 = false

        for (let i = 0; i < this.fyshList.length; i++) {
          if (this.fyshList[i].isSelected) {
            this.fyshList[i].isSelected = false
          }
        }
        this.$set(this.fyshList[index], 'isSelected', true);

      }).catch(err => {
        this.$modal.msgError(err);
        this.loading1 = false
      })
    },
    /** 查询费用审核列表 */
    getList() {
      this.shopen = false
      this.jklogList = []
      this.loading = true;
      listFysh(this.queryParams).then(response => {
        this.fyshList = response.rows;
        this.total = response.total;
        this.loading = false;

        this.fyshList = this.fyshList.map(item => ({ ...item, isSelected: false }));

      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        brtype: null,
        jzh: null,
        brid: null,
        zyid: null,
        zyh: null,
        name: null,
        bed: null,
        age: null,
        sex: null,
        tel: null,
        ybh: null,
        jgid: null,
        rydate: null,
        zyzt: null,
        deptid: null,
        doctorname: null,
        bzname: null,
        doctorid: null,
        bzcode: null,
        deptname: null,
        cydate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      console.log(this.queryParams.zyzt)
      if (this.queryParams.dateType == null && (this.queryParams.adtFrom != null || this.queryParams.adtTo != null)) {
        this.queryParams.dateType = "cydate"
      }

      if (this.queryParams.dateType != null && this.queryParams.adtFrom == null && this.queryParams.adtTo == null) {
        this.queryParams.dateType = null
      }



      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 5,
        brtype: null,
        jzh: null,
        brid: null,
        zyid: null,
        zyh: null,
        name: null,
        bed: null,
        age: null,
        sex: null,
        tel: null,
        ybh: null,
        jgid: null,
        rydate: null,
        zyzt: "1",
        deptid: null,
        doctorname: null,
        bzname: null,
        doctorid: null,
        bzcode: null,
        deptname: null,
        cydate: null,
        dateType: null,
        adtFrom: null,
        adtTo: null,
      }
      this.jklogList = []
      this.shopen = false
      this.resetForm("queryForm");
      this.handleQuery();
    },
//费用同步
    transfyblzd(row) {
      console.log(row);
      this.loading = true
      copyfyblzd(row).then(response => {
        if (response == "ok") {
          this.loading = false
          this.$modal.msgSuccess("同步成功!!");
        } else {
          this.loading = false
          this.$modal.msgWarning("后台无响应!!");
        }
        // console.log(response)
        // if (response.total == 1) {
        //    this.$modal.msgWarning("后台!!");
        // } else {
        //  this.$modal.msgWarning("后台无响应!!");
        // }
      })
        .catch(error => {
          this.loading = false
          // this.$modal.msgWarning(error);
        })
    },

    /** 详情按钮操作 */
    detail(row) {
      this.jzh = ""
      this.jzh = row.jzh
      this.open = true
      this.title = '详情'
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('gksz/fysh/export', {
        ...this.queryParams
      }, `fysh_${new Date().getTime()}.xlsx`)
    },
    async batchFysh() {
      MessageBox.alert("是否进行批量审核，将审核所有在院病人以及出院七天内的病人,将花费较长时间", "系统提示", {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
      }).then(res => {
        if (res == "confirm") {
          batchFysh()
        }
      }).catch(err => {
        console.log(err)
      })


    },
    async brxxSync() {
      MessageBox.alert("是否进行同步在院病人,将花费较长时间", "系统提示", {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
      }).then(res => {
        if (res == "confirm") {
          brxxSync().then(res => {
            console.log(res)
            if (res.code == 200) {
              this.$modal.msgSuccess("同步成功")
            } else {
              this.$modal.msgSuccess(res.msg)
            }
          }).catch(err => {
            this.$modal.msgSuccess(err)
          })
        }
      }).catch(err => {
        console.log(err)
      })

    }
  }
};
</script>


<style>
.el-table .selected-row {
  background: oldlace;
}
</style>
