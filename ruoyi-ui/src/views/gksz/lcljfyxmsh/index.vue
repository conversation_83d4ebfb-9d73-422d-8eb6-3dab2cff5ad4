<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" class="searchForm" :inline="true">
      <el-form-item label="DRG编码" prop="drgbh">
        <el-input
          v-model="queryParams.drgbh"
          placeholder="DRG编码"
          clearable
        />
      </el-form-item>
      <el-form-item label="主要诊断编码" prop="jbbm">
        <el-input
          v-model="queryParams.bzbm"
          placeholder="主要诊断编码"
          clearable
        />
      </el-form-item>
      <el-form-item label="主要手术编码" prop="ssbm">
        <el-input
          v-model="queryParams.ssbm"
          placeholder="主要手术编码"
          clearable
        />
      </el-form-item>
      <el-form-item label="不含手术编号" prop="pcssbm">
        <el-input
          v-model="queryParams.pcssbm"
          placeholder="不含手术编号"
          clearable
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择审核状态">
          <el-option label="待审核" value="0"/>
          <el-option label="已通过" value="1"/>
          <el-option label="未通过" value="2"/>
        </el-select>
      </el-form-item>

      <el-form-item label="提交时间">
        <el-date-picker
          v-model="startDateTime"
          type="datetime"
          placeholder="选择开始时间"
          default-time="00:00:00">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="">
        <el-date-picker
          v-model="endDateTime"
          type="datetime"
          placeholder="选择结束时间"
          default-time="23:59:59">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :border="true" class="lcljTable" v-loading="loading" :data="lcljList">
      <el-table-column sortable width="150px" show-overflow-tooltip label="DRG编码" align="left" prop="drgbh"/>
      <el-table-column sortable width="250px" show-overflow-tooltip label="DRG名称" align="left" prop="drgmc"/>
      <el-table-column sortable width="150px" show-overflow-tooltip label="主要诊断编码" align="left" prop="bzbm"/>
      <el-table-column sortable width="250px" show-overflow-tooltip label="主要诊断名称" align="left" prop="bzmc"/>
      <el-table-column sortable width="150px" show-overflow-tooltip label="主要手术编码" align="left" prop="ssbm"/>
      <el-table-column sortable width="150px" show-overflow-tooltip label="不含手术编号" align="left" prop="pcssbm"/>
      <el-table-column width="150px" show-overflow-tooltip label="审核状态"  align="left" prop="status">
        <template slot-scope="scope">
          {{ scope.row.status == '0' ? '待审核' : scope.row.status == '1' ? '已通过' : scope.row.status == '2' ? '未通过' : scope.row.status }}
        </template>
      </el-table-column>
      <el-table-column sortable width="200px" show-overflow-tooltip label="提交时间" align="left" prop="submitDate"/>
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status == 0"
            size="mini"
            type="text"
            @click="showDetails(scope.row)"
          >审核
          </el-button>
          <el-button
            v-else
            size="mini"
            type="text"
            @click="showDetails(scope.row)"
          >重新审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawerVisible"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeDrawer"
      size="90%">
      <div class="drawer-container">
        <div style="margin: 0px 20px;">
          <el-input v-model="remark" placeholder="备注" style="margin-bottom: 5px;" type="textarea"></el-input>
          <el-button type="danger" @click="handleOperate(currentPath,1)">审核通过</el-button>
          <el-button type="primary" @click="handleOperate(currentPath,2)">审核不通过</el-button>
        </div>

        <div>
          <el-table class="fyxmTable" :data="feeData" :border="true">
            <el-table-column label="总费用" align="left" prop="zfy">
              <template slot-scope="scope">
                {{ getZfy() }}
              </template>
            </el-table-column>
            <el-table-column label="药品占比" align="left" prop="ypzb">
              <template slot-scope="scope">
                {{ getFyzb("ypf") }}
              </template>
            </el-table-column>
            <el-table-column label="耗材占比" align="left" prop="hczb">
              <template slot-scope="scope">
                {{ getFyzb("hcf") }}
              </template>
            </el-table-column>
            <el-table-column label="检验占比" align="left" prop="jyzb">
              <template slot-scope="scope">
                {{ getFyzb("jyf") }}
              </template>
            </el-table-column>
            <el-table-column label="检查占比" align="left" prop="jczb">
              <template slot-scope="scope">
                {{ getFyzb("jcf") }}
              </template>
            </el-table-column>
            <el-table-column label="标杆费用" align="left" prop="bgfy"/>
          </el-table>
        </div>

        <virtual-scroll
          :data="fyxmList"
          :item-size="70"
          key-prop="id"
          @change="onChange">
          <el-table
            :data="virtualList"
            height="600"
            stripe
            row-key="id"
            highlight-selection-row
            :border="true">
          <el-table-column sortable width="120px" show-overflow-tooltip label="使用病人数" align="center" prop="sybrs" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="总的病人数" align="center" prop="zbrs" />
          <el-table-column sortable width="140px" show-overflow-tooltip label="使用该项目占比" align="center" prop="sygxmzb" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="日平均用量" align="center" prop="rpjyl" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均用量" align="center" prop="pjyl" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均费用" align="center" prop="pjfy" />
          <el-table-column width="250px"  show-overflow-tooltip label="项目名称" align="left" prop="xmmc" />
          <el-table-column sortable width="120px"  show-overflow-tooltip label="费用类别" align="center" prop="fykmname" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="使用总天数" align="center" prop="syzts" />
          <el-table-column sortable width="80px" show-overflow-tooltip label="属性" align="left" prop="xmtype"/>
          <el-table-column sortable width="140px" show-overflow-tooltip label="首次使用天数" align="center" prop="scsyts" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="费用占比" align="center" prop="fyzb" />
          <el-table-column sortable width="180px" show-overflow-tooltip label="费用占所有项目的占比" align="center" prop="fyzsyxmdzb" />
          <el-table-column width="250px"  show-overflow-tooltip label="项目编码" align="left" prop="xmbm" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均住院费" align="center" prop="pjzyf" />
          <el-table-column sortable width="90px" show-overflow-tooltip label="全部病人总用量" align="center" prop="zl" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均药品费" align="center" prop="ypf" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均检验费" align="center" prop="jyf" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均检查费" align="center" prop="jcf" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均手术费" align="center" prop="ssf" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均治疗费" align="center" prop="zlf" />
          <el-table-column sortable width="120px" show-overflow-tooltip label="平均耗材费" align="center" prop="hcf" />
        </el-table>
        </virtual-scroll>
      </div>
    </el-drawer>

  </div>
</template>

<script>
import {listLcljSh, updateLcljFyxmStatus, listfyxmByPathId} from "@/api/clinicalPath/lcljfyxm";
import {dateToString, getTodayLastSecond,getYearFirstDay} from "@/utils/dateUtils";
import VirtualScroll from 'el-table-virtual-scroll'
import {VirtualColumn} from 'el-table-virtual-scroll'

export default {
  name: "Lcljfyxmsh",
  components: {
    VirtualScroll,
    VirtualColumn
  },
  data() {
    return {
      startDateTime: getYearFirstDay(),
      endDateTime: getTodayLastSecond(),
      drawerVisible: false,
      drawerTitle: null,
      // 遮罩层
      loading: true,
      showSearch: true,
      // 总条数
      total: 0,
      // 临床路径费用项目表格数据
      lcljList: [],
      fyxmList: [],
      virtualList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        pcssbm: null,
        bzbm: null,
        ssbm: null,
        drgbh: null,
        status: null,
        submitStatus: "1",
        startSubmitDate: null,
        endSubmitDate: null
      },
      feeData: [{
        zfy: 0,
        ypzb: 0,
        hczb: 0,
        jyzb: 0,
        jczb: 0,
        bgfy: 0
      }],
      currentPath: null,
      remark: null,
      fyTypeMap: {
        jcf: ["检查费", "检查费[病理]", "影像学诊断费", "检查费[麻醉]"],
        ypf: ["中成费", "中成药费", "西药费", "中草费", "中草药费"],
        jyf: ["检验费", "实验室诊断费", "病理诊断费"],
        hcf: ["检查用一次性医用材料费", "手术用一次性医用材料费", "治疗用一次性医用材料费", "卫生材料"]
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    onChange(renderData) {
      this.virtualList = renderData
    },
    getFyzb(type) {
      if (this.fyxmList.length > 0) {
        const allfy = this.getZfy();
        if (allfy == 0) return 0;
        const totalPjfy = this.fyxmList.reduce((accumulator, currentObj) => {
          if (this.fyTypeMap[type].includes(currentObj.fykmname)) {
            return accumulator + (parseFloat(currentObj.pjfy) || 0);
          }
          return accumulator;
        }, 0);
        return (totalPjfy / allfy * 100).toFixed(2) + '%';
      } else {
        return 0;
      }
    },
    getZfy() {
      if (this.fyxmList.length > 0) {
        return this.fyxmList.reduce((acc, item) => acc + parseFloat(item.pjfy), 0).toFixed(4)
      } else {
        return 0;
      }
    },
    closeDrawer() {
      this.currentPath = null
      this.drawerTitle =  null
      this.remark =  null
    },
    handleOperate(row,operate) {
      this.$modal.confirm('请确认审核结果：' + (operate == 1 ? "通过" : "不通过")).then(res => {
        if (res == "confirm") {
          row.remark = this.remark
          row.status = operate;
          updateLcljFyxmStatus(row).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess("审核成功：" + (operate == 1 ? "通过" : "不通过"))
              this.drawerVisible = false
              this.drawerTitle = null
              this.getList()
            }
          })
        }
      })
    },
    showDetails(row) {
      this.currentPath = row
      listfyxmByPathId(row.id).then(res=> {
        this.fyxmList = res.rows
        console.log(this.fyxmList)
        this.drawerTitle = "DRG编号：" + row.drgbh + "|诊断编码：" + row.bzbm + (row.ssbm == null ? "" : "|手术编码：" + row.ssbm) + (row.pcssbm == null ? "" : "|不含手术编码：" + row.pcssbm)
        this.feeData[0].bgfy = row.zfbz
        this.drawerVisible = true;
      }).catch(err => {
        this.$modal.msgError("获取失败!")
        this.drawerVisible = false;
      })
    },
    /** 查询临床路径费用项目列表 */
    getList() {
      this.loading = true;
      console.log( this.queryParams)
      listLcljSh(this.queryParams).then(response => {
        this.lcljList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.startDateTime != null && this.startDateTime != '') {
        this.queryParams.startSubmitDate = dateToString(this.startDateTime)
      } else {
        this.queryParams.startSubmitDate = null;
      }
      if (this.endDateTime != null && this.endDateTime != '') {
        this.queryParams.endSubmitDate = dateToString(this.endDateTime)
      } else {
        this.queryParams.endSubmitDate = null;
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  }
};
</script>


<style>
.lcljTable, .searchForm {
  margin-left: 1%;
  width: 98%;
}
.searchForm {
  margin-top: 20px;
}
</style>
