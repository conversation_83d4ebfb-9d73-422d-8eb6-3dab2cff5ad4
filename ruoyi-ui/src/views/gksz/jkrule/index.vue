<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="规则编码" prop="gjcode">
        <el-input
          v-model="queryParams.gjcode"
          placeholder="请输入规则编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规则名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入规则名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="严重性" prop="showcolor">
        <ColorSelector placeholder="请选择严重性" isUpdate="false" :item="queryParams" :color="queryParams.showcolor"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
                   v-hasPermi="['gksz:jkrule:export']">导出
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="jkruleList">
      <el-table-column label="编号" align="center" prop="id"/>
      <el-table-column label="规则编码" align="center" prop="gjcode"/>
      <el-table-column label="规则名称" align="center" prop="name"/>
      <el-table-column label="限制值" align="center" prop="xzvalue"/>
      <el-table-column label="限制方式" align="center" prop="useflag"/>
      <el-table-column label="门诊住院" align="center" prop="mzzy"/>
      <el-table-column label="显示序号" align="center" prop="sort"/>
      <el-table-column label="备注" align="center" prop="remark"/>
      <el-table-column label="严重性" align="center" prop="showcolor">
        <template slot-scope="scope">
          <ColorSelector :item="scope.row" @updateColor="updateColor" :color="scope.row.showcolor"/>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {listJkrule, updateJkrule} from "@/api/gksz/jkrule";

export default {
  name: "Jkrule",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 监控规则表格数据
      jkruleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        gjcode: null,
        code: null,
        name: null,
        xzvalue: null,
        useflag: null,
        mzzy: null,
        mxxzflag: null,
        mustxzxx: null,
        mustchar: null,
        sort: null,
        showcolor: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    updateColor(row, val) {
      row.showcolor = val
      const data = {
        id: row.id,
        showcolor: row.showcolor
      }
      updateJkrule(data).catch(err => {
        this.$modal.msgError(err)
      })
    },
    /** 查询监控规则列表 */
    getList() {
      this.loading = true;
      listJkrule(this.queryParams).then(response => {
        this.jkruleList = response.rows;
        for (let i = 0; i < this.jkruleList.length; i++) {
          if (this.jkruleList[i].useflag == 1)
            this.jkruleList[i].useflag = '提醒'
          else if (this.jkruleList[i].useflag == 2)
            this.jkruleList[i].useflag = '询问'
          else if (this.jkruleList[i].useflag == 3)
            this.jkruleList[i].useflag = '审核'
          else if (this.jkruleList[i].useflag == 4)
            this.jkruleList[i].useflag = '禁止'
          else if (this.jkruleList[i].useflag == 5)
            this.jkruleList[i].useflag = '不启用'
        }
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        gjcode: null,
        code: null,
        name: null,
        xzvalue: null,
        useflag: null,
        mzzy: null,
        mxxzflag: null,
        mustxzxx: null,
        mustchar: null,
        sort: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/jkrule/export', {
        ...this.queryParams
      }, `jkrule_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
