<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="brname">
        <el-input
          v-model="queryParams.brname"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="住院号" prop="zyh">
        <el-input
          v-model="queryParams.zyh"
          placeholder="请输入住院号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="科室名称" prop="ksname">
        <el-select clearable v-model="queryParams.ksname"
                   placeholder="请选择科室" >
          <el-option v-for="item in dataList" :value="item.dictLabel">{{item.dictLabel}}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="医生姓名" prop="doctorname">
        <el-input
          v-model="queryParams.doctorname"
          placeholder="请输入医生姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用日期" prop="sqDate">
        <el-date-picker
          v-model="daterangeRydate.startDate"
          type="datetime"
          placeholder="请选择开始日期"
          @change="dataSearch"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="daterangeRydate.endDate"
          type="datetime"
          placeholder="请选择截至日期"
          @change="dataSearch"
          value-format="yyyy-MM-dd HH:mm:ss"
          default-time="['23:59:59']"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['gksz:shxm:export']"
        >导出</el-button>
        <el-button icon="el-icon-edit" type="success" size="mini" @click="showDialog">诊断内容展开</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="shxmList">
        <el-table-column label="住院号" align="center" prop="zyh"/>
      <el-table-column label="姓名" align="center" prop="brname"/>

     <!-- <el-table-column label="床号" align="center" prop="bed"/> -->
      <el-table-column label="科室名称" align="center" prop="ksname" show-overflow-tooltip/>
<!--      <el-table-column label="医生姓名" align="center" prop="doctorname"/>-->
      <el-table-column prop="zdqk" label="诊断情况" align="center" width="200px" show-overflow-tooltip>
        <template slot-scope="scope">
          {{!wanzheng?(scope.row.zdqk.length > 20 ? scope.row.zdqk.slice(0, 20) + "..." : scope.row.zdqk):scope.row.zdqk}}
        </template>
      </el-table-column>
      <el-table-column label="申请理由" align="center" prop="sqly" show-overflow-tooltip/>
      <el-table-column label="申请项目" align="center" prop="xmmc" show-overflow-tooltip/>
      <el-table-column label="数量" align="center" prop="xmsl" width="50px"/>
      <el-table-column label="金额" align="center" prop="xmje"/>
      <el-table-column label="状态" align="center" prop="shzt"/>
      <el-table-column label="申请日期" align="center" prop="sqrq" width="120px">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.sqrq, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row,1)"
            v-hasPermi="['gksz:shxm:edit']"
          >批准</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row,2)"
            v-hasPermi="['gksz:shxm:edit']"
          >拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改审核项目对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="诊断情况" prop="zdqk">
          <el-input v-model="form.zdqk" placeholder="请输入诊断情况" />
        </el-form-item>
        <el-form-item label="申请理由" prop="sqly">
          <el-input v-model="form.sqly" placeholder="请输入申请理由" />
        </el-form-item>
        <el-form-item label="申请项目" prop="xmmc">
          <el-input v-model="form.xmmc" placeholder="请输入申请项目" />
        </el-form-item>
        <el-form-item label="医保科意见" prop="ybkyj">
          <el-input v-model="form.ybkyj" placeholder="请输入医保科意见" />
        </el-form-item>
        <el-form-item label="医保科审核状态" prop="shzt">
          <el-input v-model="form.shzt" placeholder="请输入医保科审核状态" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listShxm, getShxm, delShxm, addShxm, updateShxm } from "@/api/gksz/shxm";
import {listData} from "@/api/mlcx/data";
import { parseTime } from '../../../utils/ruoyi'

export default {
  name: "Shxm",
  data() {
    return {
      daterangeRydate: {
        startDate:null,
        endDate:null
      },
      wanzheng:false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 审核项目表格数据
      shxmList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 收费id时间范围
      daterangeSqrq: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        brname: null,
        zyh: null,
        ksname: null,
        doctorname: null,
        sqrq: null,
      },
      // 表单参数
      form: {},
      dataList:[],
      // 表单校验
      rules: {
        type: [
          { required: true, message: "类别不能为空", trigger: "change" }
        ],
        jzh: [
          { required: true, message: "就诊号不能为空", trigger: "blur" }
        ],
        brname: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        sex: [
          { required: true, message: "性别不能为空", trigger: "change" }
        ],
        age: [
          { required: true, message: "年龄不能为空", trigger: "blur" }
        ],
        ybh: [
          { required: true, message: "社会保障号不能为空", trigger: "blur" }
        ],
        zdqk: [
          { required: true, message: "诊断情况不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,
    dataSearch() {
      this.getListByDataTime();
    },
    async getListByDataTime(data) {

    },
    showDialog() {
      if (this.wanzheng) this.wanzheng = false
      else this.wanzheng = true
    },
    /** 查询审核项目列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.params["beginSqrq"] = this.daterangeRydate.startDate;
      this.queryParams.params["endSqrq"] = this.daterangeRydate.endDate;
      listShxm(this.queryParams).then(response => {
        this.shxmList = response.rows;
        for (let i = 0; i < this.shxmList.length; i++) {
          if (this.shxmList[i].shzt == 6)
            this.shxmList[i].shzt = '审核通过'
          if (this.shxmList[i].shzt == 7)
            this.shxmList[i].shzt = '不通过'
          if (this.shxmList[i].shzt == '')
            this.shxmList[i].shzt = '待审核'
        }
        this.total = response.total;
        this.loading = false;
      });
      listData({
        pageNum: 1,
        pageSize: 10000,
        dictCode: null,
        dictSort: null,
        dictLabel: null,
        dictValue: null,
        dictType: "dept",
        cssClass: null,
        listClass: null,
        isDefault: null,
        status: null,
      }).then(response => {
        this.dataList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        type: null,
        jzh: null,
        brname: null,
        sex: null,
        age: null,
        ybh: null,
        zyh: null,
        bed: null,
        tel: null,
        ksname: null,
        doctorname: null,
        zdqk: null,
        sqly: null,
        xmmc: null,
        xmsl: null,
        xmje: null,
        fymid: null,
        sqrq: null,
        kzrname: null,
        kzrtel: null,
        kzrshdate: null,
        clsyksmc: null,
        clsykskzr: null,
        clsykskzrtel: null,
        ybkyj: null,
        shzt: null,
        createBy: null,
        createDate: null,
        updateBy: null,
        updateDate: null,
        remarks: null,
        delFlag: null,
        sfId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeRydate = {
          startDate:null,
          endDate:null
      },
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加审核项目";
    },
    /** 修改按钮操作 */
    handleUpdate(row,flag) {
      this.reset();
      const id = row.id || this.ids
      getShxm(id).then(response => {
        this.form = response.data;
        if (flag == 1)
          this.form.shzt = '审核通过'
        if (flag == 2)
          this.form.shzt = '不通过'
        this.open = true;
        this.title = "修改审核项目";
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (this.form.shzt == '审核通过')
        this.form.shzt = 6
      else if (this.form.shzt == '不通过')
        this.form.shzt = 7
      else
        this.form.shzt = ''
      var flag = this.form.shzt
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateShxm(this.form).then(response => {
              if (flag == 6)
              this.$modal.msgSuccess("已批准");
              else if (flag == 7)
                this.$modal.msgSuccess("已拒绝");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除审核项目编号为"' + ids + '"的数据项？').then(function() {
        return delShxm(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('gksz/shxm/export', {
        ...this.queryParams
      }, `shxm_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
