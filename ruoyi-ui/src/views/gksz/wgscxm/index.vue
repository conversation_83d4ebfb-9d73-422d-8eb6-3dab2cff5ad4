<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
 <el-form-item label="批次" prop="zcpc">
        <el-input
          v-model="queryParams.zcpc"
          placeholder="输入1查1,输2查2,不输所有"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <dept-doctor-selector v-model="selectedData"/>
      <el-form-item label="违规日期">
        <el-date-picker
          v-model="queryParams.adtFrom"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.adtTo"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>

      </el-form-item>



      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出查询</el-button>
        <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImport">导入项目</el-button>
         <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleExportXm">导出项目</el-button>
      </el-form-item>
    </el-form>

    <el-table border v-loading="loading" :data="wgscxmList">
      <el-table-column show-overflow-tooltip width="60" label="类别" align="left" prop="brtype"/>
      <el-table-column show-overflow-tooltip width="150" label="科室" align="left" prop="kdksname"/>
      <el-table-column show-overflow-tooltip width="120" label="医生" align="left" prop="doctorname"/>
      <el-table-column show-overflow-tooltip width="120" label="住院号" align="left" prop="zyh"/>
      <el-table-column show-overflow-tooltip width="100" label="患者姓名" align="left" prop="brname"/>
      <el-table-column show-overflow-tooltip width="100" label="身份证" align="left" prop="sfz"/>
      <el-table-column show-overflow-tooltip width="100" label="参保类别" align="left" prop="cblb"/>

      <el-table-column show-overflow-tooltip width="220" label="项目编码" align="left" prop="fym_id"/>
      <el-table-column show-overflow-tooltip width="220" label="项目名称" align="left" prop="fym_name"/>
       <el-table-column show-overflow-tooltip width="100" label="入院时间" align="left" prop="rydate"/>
        <el-table-column show-overflow-tooltip width="100" label="出院时间" align="left" prop="cydate"/>
         <el-table-column show-overflow-tooltip width="100" label="问题类型" align="left" prop="jktype"/>
      <el-table-column show-overflow-tooltip width="100" label="违规数量" align="left" prop="sl"/>
      <el-table-column show-overflow-tooltip width="100" label="违规金额" align="left" prop="je"/>
      <el-table-column show-overflow-tooltip width="100" label="单价" align="left" prop="price"/>
       <el-table-column show-overflow-tooltip width="100" label="费用等级" align="left" prop="fydj"/>
        <el-table-column show-overflow-tooltip width="100" label="时间" align="left" prop="createDate"/>
      <el-table-column show-overflow-tooltip width="600" label="违规内容" align="left" prop="jklog"/>
      <el-table-column show-overflow-tooltip width="600" label="诊断信息" align="left" prop="zdxx"/>
      <el-table-column show-overflow-tooltip width="600" label="医保备注" align="left" prop="ybbz"/>
      <el-table-column show-overflow-tooltip width="100" label="批次" align="left" prop="zcpc"/>
      <el-table-column show-overflow-tooltip width="100" label="医保报销" align="left" prop="ybbx"/>
      <el-table-column show-overflow-tooltip width="100" label="统筹" align="left" prop="tc"/>
      <el-table-column show-overflow-tooltip width="100" label="大额" align="left" prop="de"/>
      <el-table-column show-overflow-tooltip width="100" label="医疗救助" align="left" prop="yljz"/>
       <el-table-column show-overflow-tooltip width="300" label="费用明细" align="left" prop="fymx"/>
    </el-table>


    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {listWgscxm} from "@/api/gksz/wgscxm";
import DeptDoctorSelector from "@/components/DeptDoctorSelector/index.vue";
import {getTodayLastSecondStr, getYesterdayFirstSecondStr} from "@/utils/dateUtils";
import {getToken} from "@/utils/auth";

export default {
  name: "Wgscxm",
  components: {DeptDoctorSelector},
  data() {
    return {
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/gksz/wgscxm/importData"
      },
      selectedData: {
        dept: null,
        doctor: null
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 违规筛查项目表格数据
      wgscxmList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        adtFrom: getYesterdayFirstSecondStr(),
        adtTo: getTodayLastSecondStr(),
        kdksname: null,
        doctorname: null
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 导入按钮操作 */
    handleImport() {
       if (confirm('重新导入将会删除之前的项目，是否确认导入？')) {
              this.upload.title = "违规筛查项目导入";
              this.upload.open = true;
            }

    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
      this.getSendData();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 查询违规筛查项目列表 */
    getList() {
      this.loading = true;
      listWgscxm(this.queryParams).then(response => {
        this.wgscxmList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.kdksname = this.selectedData.dept
      this.queryParams.doctorname = this.selectedData.doctor
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        adtFrom: getYesterdayFirstSecondStr(),
        adtTo: getTodayLastSecondStr(),
        kdksname: '所有',
        doctorname: null
      }
      this.selectedData.dept = '所有'
      this.selectedData.doctor = null
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleExport() {

      this.download('gksz/wgscxm/export', {
        ...this.queryParams
      }, `自查项目违规列表_${new Date().getTime()}.xlsx`)
    },
    handleExportXm() {
      this.download('gksz/wgscxm/exportxm', {
        ...this.queryParams
      }, `自查项目列表本院名称_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
