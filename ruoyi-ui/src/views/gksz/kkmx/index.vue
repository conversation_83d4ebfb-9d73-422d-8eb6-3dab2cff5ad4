<template>
  <div class="app-container">
    <el-form :inline="true" label-width="68px" size="small">
      <el-form-item label="导入时间" prop="createDate">
        <el-date-picker
          v-model="queryParams.startDate"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.endDate"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="科室" prop="deptname">
        <el-select v-model="queryParams.deptname"
                   @focus="resetDept"
                   @change="getDoctorByDept(queryParams.deptname)"
                   filterable
                   :filter-method="deptFilter"
                   placeholder="科室"
                   clearable>
          <el-option v-for="(item,index) in deptList" :value="item" :key="index" :label="item"/>
        </el-select>
      </el-form-item>

      <el-form-item label="医生" prop="kddoctor">
        <el-select v-model="queryParams.kddoctor"
                   @focus="resetDoctor"
                   @change="resetDoctor"
                   filterable
                   :filter-method="doctorFilter"
                   placeholder="医生"
                   clearable>
          <el-option v-for="(item,index) in doctorList" :value="item" :key="index" :label="item"/>
        </el-select>
      </el-form-item>
      <el-form-item label="是否过期">
        <el-select clearable placeholder="是否过期" v-model="queryParams.dateLimit">
          <el-option value="wgq" label="未过期"/>
          <el-option value="ygq" label="已过期"/>
        </el-select>
      </el-form-item>
      <el-form-item label="申诉状态">
        <el-select clearable placeholder="申诉状态" v-model="queryParams.appealStatus">
          <el-option value="0" label="未申诉"/>
          <el-option value="1" label="已申诉"/>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
        <el-button type="danger" size="mini" @click="handleSned">发送</el-button>
      </el-form-item>
    </el-form>

    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane name="notSend" label="未发送">

        <virtual-scroll
          ref="virtualScroll"
          :data="kkmxList"
          :item-size="70"
          @selection-change="handleSelectionChange"
          key-prop="id"
          @change="onChange">
          <el-table height="500" stripe :border="true" row-key="id" v-loading="loading" :data="virtualList" highlight-current-row>
            <virtual-column class-name="small-padding fixed-width" type="selection" width="40" align="center"/>
            <el-table-column width="150px" show-overflow-tooltip label="就诊流水号" align="left" prop="jzhlsh"/>
            <el-table-column
              :filters="deptListAll"
              :filter-method="deptFilterMethod"
              width="100px"
              show-overflow-tooltip
              label="科室名称"
              align="left"
              prop="deptname"/>
            <el-table-column
              :filters="doctorListAll"
              :filter-method="doctorFilterMethod"
              width="100px"
              show-overflow-tooltip
              label="开单医师"
              align="left"
              prop="kddoctor"/>
            <el-table-column width="100px" show-overflow-tooltip label="参保人" align="left" prop="brname"/>
            <el-table-column width="100px" show-overflow-tooltip label="证件号码" align="left" prop="sfz"/>
            <el-table-column width="100px" show-overflow-tooltip label="性别" align="left" prop="sex"/>
            <el-table-column width="100px" show-overflow-tooltip label="就诊金额" align="left" prop="jzje"/>
            <el-table-column width="300px" show-overflow-tooltip label="违规项目" align="left" prop="wgxm"/>
            <el-table-column width="500px" show-overflow-tooltip label="违规内容" align="left" prop="wgnr"/>
            <el-table-column width="300px" show-overflow-tooltip label="疑点名称" align="left" prop="ldname"/>
            <el-table-column width="100px" show-overflow-tooltip label="医疗类别" align="left" prop="yllx"/>
            <el-table-column width="100px" show-overflow-tooltip label="入院日期" align="left" prop="rydate"/>
            <el-table-column width="100px" show-overflow-tooltip label="出院日期" align="left" prop="cydate"/>
            <el-table-column width="100px" show-overflow-tooltip label="结算日期" align="left" prop="jsdate"/>
            <el-table-column width="100px" show-overflow-tooltip label="出院诊断" align="left" prop="cyzd"/>
            <el-table-column width="100px" show-overflow-tooltip label="单据节点" align="left" prop="djjd"/>
            <el-table-column width="100px" show-overflow-tooltip label="处理状态" align="left" prop="clzt"/>
            <el-table-column width="100px" show-overflow-tooltip label="是否扣款" align="left" prop="sfkk"/>
            <el-table-column width="100px" show-overflow-tooltip label="下发日期" align="left" prop="xfdate"/>
            <el-table-column width="100px" show-overflow-tooltip label="疑点状态" align="left" prop="ldzt"/>
            <el-table-column width="100px" show-overflow-tooltip label="违规金额" align="left" prop="wgje"/>
            <el-table-column width="100px" show-overflow-tooltip label="申诉金额" align="left" prop="ssje"/>
            <el-table-column width="100px" show-overflow-tooltip label="申诉理由" align="left" prop="ssly"/>
            <el-table-column width="150px" show-overflow-tooltip label="费用明细流水号AB" align="left" prop="fymxlsh"/>
            <el-table-column width="100px" show-overflow-tooltip label="处方日期" align="left" prop="cfdate"/>
            <el-table-column width="100px" show-overflow-tooltip label="违规数量" align="left" prop="wgsl"/>
            <el-table-column width="150px" show-overflow-tooltip label="医院目录编码" align="left" prop="yyxmbh"/>
            <el-table-column width="150px" show-overflow-tooltip label="医院目录名称" align="left" prop="yyxmmc"/>
            <el-table-column width="180px" show-overflow-tooltip label="导入时间" align="left" prop="createDate"/>
            <el-table-column width="100px" show-overflow-tooltip label="申诉人" align="left" prop="appealer"/>
            <el-table-column width="100px" show-overflow-tooltip label="申诉人科室" align="left" prop="appealerDept"/>
            <el-table-column width="180px" show-overflow-tooltip label="申诉日期" align="left" prop="appealDate"/>
            <el-table-column width="100px" show-overflow-tooltip label="医保区划" align="left" prop="ybqh"/>
            <el-table-column width="150px" show-overflow-tooltip label="医药机构" align="left" prop="yyjg"/>
            <el-table-column width="100px" show-overflow-tooltip label="机构类型" align="left" prop="jglx"/>
            <el-table-column width="100px" show-overflow-tooltip label="机构等级" align="left" prop="jgdj"/>
          </el-table>

        </virtual-scroll>


        <pagination
          style="margin-bottom: 20px"
          :page-sizes="pageSizes"
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

      </el-tab-pane>


      <el-tab-pane name="send" label="已发送">

        <!--        <el-button style="float: right;margin-bottom: 5px" type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>-->

        <el-table border v-loading="loading2" :data="sendDataList" highlight-current-row>
          <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width"
                           width="120px">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="getAppealCon(scope.row)"
              >申诉内容
              </el-button>
            </template>
          </el-table-column>
          <el-table-column width="150px" show-overflow-tooltip label="申诉状态" align="left" prop="appealStatus"/>
          <el-table-column width="150px" show-overflow-tooltip label="就诊流水号" align="left" prop="jzhlsh"/>
          <el-table-column
            :filters="deptListAll"
            :filter-method="deptFilterMethod"
            width="100px"
            show-overflow-tooltip
            label="科室名称"
            align="left"
            prop="deptname"/>
          <el-table-column
            :filters="doctorListAll"
            :filter-method="doctorFilterMethod"
            width="100px"
            show-overflow-tooltip
            label="开单医师"
            align="left"
            prop="kddoctor"/>
          <el-table-column width="100px" show-overflow-tooltip label="参保人" align="left" prop="brname"/>
          <el-table-column width="100px" show-overflow-tooltip label="证件号码" align="left" prop="sfz"/>
          <el-table-column width="100px" show-overflow-tooltip label="性别" align="left" prop="sex"/>
          <el-table-column width="100px" show-overflow-tooltip label="就诊金额" align="left" prop="jzje"/>
          <el-table-column width="300px" show-overflow-tooltip label="违规项目" align="left" prop="wgxm"/>
          <el-table-column width="500px" show-overflow-tooltip label="违规内容" align="left" prop="wgnr"/>
          <el-table-column width="300px" show-overflow-tooltip label="疑点名称" align="left" prop="ldname"/>
          <el-table-column width="100px" show-overflow-tooltip label="医疗类别" align="left" prop="yllx"/>
          <el-table-column width="100px" show-overflow-tooltip label="入院日期" align="left" prop="rydate"/>
          <el-table-column width="100px" show-overflow-tooltip label="出院日期" align="left" prop="cydate"/>
          <el-table-column width="100px" show-overflow-tooltip label="结算日期" align="left" prop="jsdate"/>
          <el-table-column width="100px" show-overflow-tooltip label="出院诊断" align="left" prop="cyzd"/>
          <el-table-column width="100px" show-overflow-tooltip label="单据节点" align="left" prop="djjd"/>
          <el-table-column width="100px" show-overflow-tooltip label="处理状态" align="left" prop="clzt"/>
          <el-table-column width="100px" show-overflow-tooltip label="是否扣款" align="left" prop="sfkk"/>
          <el-table-column width="100px" show-overflow-tooltip label="下发日期" align="left" prop="xfdate"/>
          <el-table-column width="100px" show-overflow-tooltip label="疑点状态" align="left" prop="ldzt"/>
          <el-table-column width="100px" show-overflow-tooltip label="违规金额" align="left" prop="wgje"/>
          <el-table-column width="100px" show-overflow-tooltip label="申诉金额" align="left" prop="ssje"/>
          <el-table-column width="100px" show-overflow-tooltip label="申诉理由" align="left" prop="ssly"/>
          <el-table-column width="150px" show-overflow-tooltip label="费用明细流水号AB" align="left" prop="fymxlsh"/>
          <el-table-column width="100px" show-overflow-tooltip label="处方日期" align="left" prop="cfdate"/>
          <el-table-column width="100px" show-overflow-tooltip label="违规数量" align="left" prop="wgsl"/>
          <el-table-column width="150px" show-overflow-tooltip label="医院目录编码" align="left" prop="yyxmbh"/>
          <el-table-column width="150px" show-overflow-tooltip label="医院目录名称" align="left" prop="yyxmmc"/>
          <el-table-column width="180px" show-overflow-tooltip label="导入时间" align="left" prop="createDate"/>
          <el-table-column width="100px" show-overflow-tooltip label="申诉人" align="left" prop="appealer"/>
          <el-table-column width="100px" show-overflow-tooltip label="申诉人科室" align="left" prop="appealerDept"/>
          <el-table-column width="180px" show-overflow-tooltip label="申诉日期" align="left" prop="appealDate"/>
          <el-table-column width="100px" show-overflow-tooltip label="医保区划" align="left" prop="ybqh"/>
          <el-table-column width="150px" show-overflow-tooltip label="医药机构" align="left" prop="yyjg"/>
          <el-table-column width="100px" show-overflow-tooltip label="机构类型" align="left" prop="jglx"/>
          <el-table-column width="100px" show-overflow-tooltip label="机构等级" align="left" prop="jgdj"/>
        </el-table>

        <pagination
          style="margin-bottom: 20px"
          v-show="sendTotal>0"
          :total="sendTotal"
          :page.sync="sendQueryParams.pageNum"
          :limit.sync="sendQueryParams.pageSize"
          @pagination="getSendData"
        />

      </el-tab-pane>
    </el-tabs>


    <el-drawer
      :title="appealConTitle"
      :visible.sync="appealConOpen"
      direction="rtl"
      :wrapper-closable="false"
      size="80%">
      <el-card style="margin: 10px">
        <div v-html="appealCon"></div>
      </el-card>
    </el-drawer>


    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport"/>
          是否更新已经存在的用户数据
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>


    <el-drawer
      title=""
      :visible.sync="sendDrawerVisible"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeDrawer"
      v-loading="sendLoading"
      size="80%">

      <div style="margin: 15px;">
        <el-form :model="sendParams" :inline="true">

          <el-form-item label="科室" prop="deptName">
            <el-select v-model="sendParams.appealerDept"
                       @change="sendFormGetDoctorByDept(sendParams.appealerDept)"
                       filterable
                       placeholder="科室"
                       clearable
                       style="width: 150px">
              <el-option v-for="(item,index) in sendDeptList" :value="item.hDeptName" :key="index"
                         :label="item.hDeptName"/>
            </el-select>
          </el-form-item>

          <el-form-item label="医生" prop="doctorName">
            <el-select v-model="sendParams.appealer"
                       filterable
                       placeholder="医生"
                       clearable
                       style="width: 100px">
              <el-option v-for="(item,index) in sendDoctorList" :value="item.nickName" :key="index"
                         :label="item.nickName"/>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="danger" style="margin-left: 20px" @click="send">发送</el-button>
          </el-form-item>
        </el-form>
      </div>

      <virtual-scroll
        style="margin: 15px;"
        ref="virtualScroll1"
        :data="sendParams.dataList"
        :item-size="70"
        key-prop="id"
        @change="onChange2">
        <el-table height="500" stripe :border="true" row-key="id" border v-loading="loading" :data="virtualList2" highlight-current-row>
          <el-table-column width="150px" show-overflow-tooltip label="就诊流水号" align="left" prop="jzhlsh"/>
          <el-table-column width="100px" show-overflow-tooltip label="科室名称" align="left" prop="deptname"/>
          <el-table-column width="100px" show-overflow-tooltip label="开单医师" align="left" prop="kddoctor"/>
          <el-table-column width="100px" show-overflow-tooltip label="参保人" align="left" prop="brname"/>
          <el-table-column width="100px" show-overflow-tooltip label="证件号码" align="left" prop="sfz"/>
          <el-table-column width="100px" show-overflow-tooltip label="性别" align="left" prop="sex"/>
          <el-table-column width="100px" show-overflow-tooltip label="就诊金额" align="left" prop="jzje"/>
          <el-table-column width="300px" show-overflow-tooltip label="违规项目" align="left" prop="wgxm"/>
          <el-table-column width="500px" show-overflow-tooltip label="违规内容" align="left" prop="wgnr"/>
          <el-table-column width="300px" show-overflow-tooltip label="疑点名称" align="left" prop="ldname"/>
          <el-table-column width="100px" show-overflow-tooltip label="医疗类别" align="left" prop="yllx"/>
          <el-table-column width="100px" show-overflow-tooltip label="入院日期" align="left" prop="rydate"/>
          <el-table-column width="100px" show-overflow-tooltip label="出院日期" align="left" prop="cydate"/>
          <el-table-column width="100px" show-overflow-tooltip label="结算日期" align="left" prop="jsdate"/>
          <el-table-column width="100px" show-overflow-tooltip label="出院诊断" align="left" prop="cyzd"/>
          <el-table-column width="100px" show-overflow-tooltip label="单据节点" align="left" prop="djjd"/>
          <el-table-column width="100px" show-overflow-tooltip label="处理状态" align="left" prop="clzt"/>
          <el-table-column width="100px" show-overflow-tooltip label="是否扣款" align="left" prop="sfkk"/>
          <el-table-column width="100px" show-overflow-tooltip label="下发日期" align="left" prop="xfdate"/>
          <el-table-column width="100px" show-overflow-tooltip label="疑点状态" align="left" prop="ldzt"/>
          <el-table-column width="100px" show-overflow-tooltip label="违规金额" align="left" prop="wgje"/>
          <el-table-column width="100px" show-overflow-tooltip label="申诉金额" align="left" prop="ssje"/>
          <el-table-column width="100px" show-overflow-tooltip label="申诉理由" align="left" prop="ssly"/>
          <el-table-column width="150px" show-overflow-tooltip label="费用明细流水号AB" align="left" prop="fymxlsh"/>
          <el-table-column width="100px" show-overflow-tooltip label="处方日期" align="left" prop="cfdate"/>
          <el-table-column width="100px" show-overflow-tooltip label="违规数量" align="left" prop="wgsl"/>
          <el-table-column width="150px" show-overflow-tooltip label="医院目录编码" align="left" prop="yyxmbh"/>
          <el-table-column width="150px" show-overflow-tooltip label="医院目录名称" align="left" prop="yyxmmc"/>
          <el-table-column width="180px" show-overflow-tooltip label="导入时间" align="left" prop="createDate"/>
          <el-table-column width="100px" show-overflow-tooltip label="申诉人" align="left" prop="appealer"/>
          <el-table-column width="180px" show-overflow-tooltip label="申诉日期" align="left" prop="appealDate"/>
          <el-table-column width="100px" show-overflow-tooltip label="医保区划" align="left" prop="ybqh"/>
          <el-table-column width="150px" show-overflow-tooltip label="医药机构" align="left" prop="yyjg"/>
          <el-table-column width="100px" show-overflow-tooltip label="机构类型" align="left" prop="jglx"/>
          <el-table-column width="100px" show-overflow-tooltip label="机构等级" align="left" prop="jgdj"/>
        </el-table>
      </virtual-scroll>
    </el-drawer>


  </div>
</template>

<script>
import {deptList, doctorBydept, doctorList, getKkmx, importTemplate, listKkmx, sendKkxx} from "@/api/gksz/kkmx";
import {getToken} from "@/utils/auth";
import DateFilter from "@/components/DateFilter/DateFilter.vue";
import {getTodayLastSecondStr, getYesterdayFirstSecondStr} from "@/utils/dateUtils";
import tabs from "@/components/DetailsTabs/tabs.vue";
import VirtualScroll, {VirtualColumn} from "el-table-virtual-scroll";
import {selectDoctorByHDeptName} from "@/api/system/user";
import {selectHDeptNameList} from "@/api/system/hdept";
import PinYinMatch from "pinyin-match";

export default {
  name: "Kkmx",
  components: {VirtualColumn, VirtualScroll, tabs, DateFilter},
  data() {
    return {
      activeName: "notSend",
      sendDataList: null,
      sendDrawerVisible: false,
      sendLoading: false,
      pageSizes: [10, 50, 100, 2000, 5000, 10000],
      appealConTitle: null,
      appealCon: null,
      appealConOpen: false,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/gksz/kkmx/importData"
      },
      // 遮罩层
      loading: true,
      loading2: false,
      // 显示搜索条件
      showSearch: true,
      selectItems: [],
      single: true,
      multiple: true,
      ids: [],
      // 总条数
      total: 0,
      sendTotal: 0,
      // 扣款明细表格数据
      kkmxList: [],
      virtualList: [],
      virtualList2: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        dateLimit: null,
        kddoctor: null,
        deptname: null,
        appealStatus: null,
        sendStatus: null,
        startDate: getYesterdayFirstSecondStr(),
        endDate: getTodayLastSecondStr(),
      },
      sendQueryParams: {
        pageSize: 10,
        pageNum: 1,
      },
      // 表单参数
      form: {},
      sendParams: {
        appealerDept: null,
        appealer: null,
        dataList: null,
      },
      sendDeptList: [],
      sendDoctorList: [],
      deptList: [],
      deptListby: [],
      doctorList: [],
      doctorListby: [],
      doctorListAll: [],
      deptListAll: [],
    };
  },
  created() {
    this.init()
  },
  methods: {
    handleClick(tab) {
      if (tab.name === 'send' && this.sendDataList === null) {
        this.getSendData()
      }
    },
    async getSendData() {
      this.loading2 = true
      const data = this.queryParams
      data.sendStatus = "1"
      data.pageNum = this.sendQueryParams.pageNum
      data.pageSize = this.sendQueryParams.pageSize
      const res = await listKkmx(data)
      this.sendDataList = res.rows;
      this.sendTotal = res.total;
      this.loading2 = false;
    },
    deptFilterMethod(value, row) {
      return row.deptname == value
    },
    doctorFilterMethod(value, row) {
      return row.kddoctor == value
    },
    resetDept() {
      this.deptList = this.deptListby
    },
    resetDoctor() {
      this.doctorList = this.doctorListby
    },
    deptFilter(val) {
      this.queryParams.deptname = val
      if (val) {
        this.deptList = []
        var deptList = this.deptListby.filter((item) => {
          if (PinYinMatch.match(item, val)) {
            return true
          }
        })
        this.deptList = deptList
      } else {
        this.deptList = this.deptListby
      }
    },
    doctorFilter(val) {
      this.queryParams.kddoctor = val
      if (val) {
        this.doctorList = []
        var doctorList = this.doctorListby.filter((item) => {
          if (PinYinMatch.match(item, val)) {
            return true
          }
        })
        this.doctorList = doctorList
      } else {
        this.doctorList = this.doctorListby
      }
    },
    async getDeptNameList() {
      const res = await deptList()
      this.deptList = res.rows;
      this.deptListby = JSON.parse(JSON.stringify(this.deptList))
      this.deptListAll = this.deptListby.map(item => ({value: item, text: item}))
    },
    async getDoctorNameList() {
      const res = await doctorList()
      this.doctorListAll = res.rows;
      this.doctorListAll = this.doctorListAll.map(item => ({value: item, text: item}))
    },
    getDoctorByDept(deptName) {
      this.doctorList = []
      this.doctorListby = []
      this.queryParams.kddoctor = null
      this.deptList = this.deptListby
      if (!deptName) {
        return
      }
      doctorBydept(deptName).then(res => {
        this.doctorList = res.rows;
        this.doctorListby = JSON.parse(JSON.stringify(this.doctorList))
      });
    },
    async send() {
      if (!this.sendParams.appealer || !this.sendParams.appealerDept) {
        this.$modal.msgWarning("请选择科室和医生！")
        return
      }
      const data = {
        appealerDept: this.sendParams.appealerDept,
        appealer: this.sendParams.appealer,
        sendList: this.sendParams.dataList.map(item => item.id),
      }
      const res = await sendKkxx(data).catch(err => {
        this.sendDrawerVisible = false
      })
      if (res.code == 200) {
        this.$modal.msgSuccess("发送成功！")
        this.getList()
        this.getSendData()
        this.sendDrawerVisible = false
      }
    },
    async getSendDeptInfo() {
      const res = await selectHDeptNameList()
      if (res.code == 200) {
        this.sendDeptList = res.rows
      }
    },
    async sendFormGetDoctorByDept(deptId) {
      this.sendParams.appealer = null
      this.sendDoctorList = []
      if (!deptId) {
        return
      }
      const res = await selectDoctorByHDeptName(deptId)
      if (res.code == 200) {
        this.sendDoctorList = res.rows
      }
    },
    closeDrawer() {
      this.sendParams = {
        appealerDept: null,
        appealer: null,
        dataList: [],
      }
    },
    handleSelectionChange(selection) {
      this.selectItems = selection.map(item => item)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    onChange(renderData) {
      this.virtualList = renderData
    },
    onChange2(renderData) {
      this.virtualList2 = renderData
    },
    handleSned() {
      if (!this.selectItems || this.selectItems.length == 0) {
        this.$modal.msgWarning("请选择扣款记录！")
        return
      }
      this.sendParams.dataList = this.selectItems
      this.sendDrawerVisible = true
    },
    async getAppealCon(row) {
      this.appealConTitle = "病人[" + row.brname + "]扣款申诉内容"
      this.appealConOpen = true
      const res = await getKkmx(row.id)
      if (res.code == 200) {
        this.appealCon = res.data.appealContent
      } else {
        this.appealCon = row.appealContent
      }
    },
    init() {
      this.getDeptNameList()
      this.getDoctorNameList()
      this.getSendDeptInfo()
      this.getList();
      this.$alert("扣款的明细数据需要从两定系统中导出，再在该系统中导入");
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "扣款明细导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", {dangerouslyUseHTMLString: true});
      this.getList();
      this.getSendData();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 查询扣款明细列表 */
    async getList() {
      const data = this.queryParams
      data.sendStatus = "0"
      const res = await listKkmx(data)
      this.kkmxList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.activeName == "notSend") {
        this.getList();
      } else {
        this.getSendData()
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.queryParams.dateLimit = null
      this.queryParams.appealStatus = null
      this.queryParams.kddoctor = null
      this.queryParams.deptname = null
      this.queryParams.sendStatus = null
      this.queryParams.startDate = getYesterdayFirstSecondStr()
      this.queryParams.endDate = getTodayLastSecondStr()
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      const data = this.queryParams
      data.sendStatus = "1"
      this.download('gksz/kkmx/export', {
        ...data
      }, `扣款明细_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

