<template>
  <div class="app-container">
    <el-form :inline="true" label-width="68px" size="small">
      <el-form-item label="导入时间" prop="createDate">
        <el-date-picker
          v-model="queryParams.startDate"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.endDate"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="发送时间" prop="createDate">
        <el-date-picker
          v-model="queryParams.startDateSend"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.endDateSend"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="申诉状态">
        <el-select clearable placeholder="申诉状态" v-model="queryParams.appealStatus">
          <el-option value="0" label="未申诉"/>
          <el-option value="1" label="已申诉"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table border v-loading="loading" :data="kkmxList" highlight-current-row>
      <el-table-column width="100px" show-overflow-tooltip label="申诉状态" align="left" prop="appealStatus"/>
      <el-table-column width="150px" show-overflow-tooltip label="就诊流水号" align="left" prop="jzhlsh"/>
      <el-table-column width="100px" show-overflow-tooltip label="科室名称" align="left" prop="deptname"/>
      <el-table-column width="100px" show-overflow-tooltip label="开单医师" align="left" prop="kddoctor"/>
      <el-table-column width="100px" show-overflow-tooltip label="参保人" align="left" prop="brname"/>
      <el-table-column width="100px" show-overflow-tooltip label="证件号码" align="left" prop="sfz"/>
      <el-table-column width="100px" show-overflow-tooltip label="性别" align="left" prop="sex"/>
      <el-table-column width="100px" show-overflow-tooltip label="就诊金额" align="left" prop="jzje"/>
      <el-table-column width="300px" show-overflow-tooltip label="违规项目" align="left" prop="wgxm"/>
      <el-table-column width="500px" show-overflow-tooltip label="违规内容" align="left" prop="wgnr"/>
      <el-table-column width="300px" show-overflow-tooltip label="疑点名称" align="left" prop="ldname"/>
      <el-table-column width="100px" show-overflow-tooltip label="医疗类别" align="left" prop="yllx"/>
      <el-table-column width="100px" show-overflow-tooltip label="入院日期" align="left" prop="rydate"/>
      <el-table-column width="100px" show-overflow-tooltip label="出院日期" align="left" prop="cydate"/>
      <el-table-column width="100px" show-overflow-tooltip label="结算日期" align="left" prop="jsdate"/>
      <el-table-column width="100px" show-overflow-tooltip label="出院诊断" align="left" prop="cyzd"/>
      <el-table-column width="100px" show-overflow-tooltip label="单据节点" align="left" prop="djjd"/>
      <el-table-column width="100px" show-overflow-tooltip label="处理状态" align="left" prop="clzt"/>
      <el-table-column width="100px" show-overflow-tooltip label="是否扣款" align="left" prop="sfkk"/>
      <el-table-column width="100px" show-overflow-tooltip label="下发日期" align="left" prop="xfdate"/>
      <el-table-column width="100px" show-overflow-tooltip label="疑点状态" align="left" prop="ldzt"/>
      <el-table-column width="100px" show-overflow-tooltip label="违规金额" align="left" prop="wgje"/>
      <el-table-column width="100px" show-overflow-tooltip label="申诉金额" align="left" prop="ssje"/>
      <el-table-column width="100px" show-overflow-tooltip label="申诉理由" align="left" prop="ssly"/>
      <el-table-column width="150px" show-overflow-tooltip label="费用明细流水号AB" align="left" prop="fymxlsh"/>
      <el-table-column width="100px" show-overflow-tooltip label="处方日期" align="left" prop="cfdate"/>
      <el-table-column width="100px" show-overflow-tooltip label="违规数量" align="left" prop="wgsl"/>
      <el-table-column width="150px" show-overflow-tooltip label="医院目录编码" align="left" prop="yyxmbh"/>
      <el-table-column width="150px" show-overflow-tooltip label="医院目录名称" align="left" prop="yyxmmc"/>
      <el-table-column width="180px" show-overflow-tooltip label="导入时间" align="left" prop="createDate"/>
      <el-table-column width="180px" show-overflow-tooltip label="发送时间" align="left" prop="sendDate"/>
      <el-table-column width="100px" show-overflow-tooltip label="申诉人" align="left" prop="appealer"/>
      <el-table-column width="100px" show-overflow-tooltip label="申诉人科室" align="left" prop="appealerDept"/>
      <el-table-column width="180px" show-overflow-tooltip label="申诉日期" align="left" prop="appealDate"/>
      <el-table-column width="100px" show-overflow-tooltip label="医保区划" align="left" prop="ybqh"/>
      <el-table-column width="150px" show-overflow-tooltip label="医药机构" align="left" prop="yyjg"/>
      <el-table-column width="100px" show-overflow-tooltip label="机构类型" align="left" prop="jglx"/>
      <el-table-column width="100px" show-overflow-tooltip label="机构等级" align="left" prop="jgdj"/>
      <el-table-column width="150px" fixed label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleAppeal(scope.row)">
            申诉
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="getAppealCon(scope.row)"
          >申诉内容
          </el-button>
        </template>
      </el-table-column>
    </el-table>


    <el-drawer
      :title="appealConTitle"
      :visible.sync="appealConOpen"
      direction="rtl"
      :wrapper-closable="false"
      size="80%">
      <div>
        <div style="margin: 10px" v-html="appealCon"></div>
      </div>
    </el-drawer>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {deptList, doctorList, getKkmx, listKkmx} from "@/api/gksz/kkmx";
import DateFilter from "@/components/DateFilter/DateFilter.vue";
import {getTodayLastSecondStr, getYesterdayFirstSecondStr} from "@/utils/dateUtils";
import {getUserDeptList} from "@/api/system/hdept";
import tabs from "@/components/DetailsTabs/tabs.vue";

export default {
  name: "CheckKkmx",
  components: {tabs, DateFilter},
  data() {
    return {
      appealConTitle: null,
      appealCon: null,
      appealConOpen: false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 扣款明细表格数据
      kkmxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        deptList: [],
        appealer: null,
        appealStatus: null,
        startDate: getYesterdayFirstSecondStr(),
        endDate: getTodayLastSecondStr(),
        startDateSend: null,
        endDateSend: null,
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.init()
  },
  methods: {
    async getAppealCon(row) {
      this.appealConTitle = "病人[" + row.brname + "]扣款申诉内容"
      this.appealConOpen = true
      const res = await getKkmx(row.id)
      if (res.code == 200) {
        this.appealCon = res.data.appealContent
      } else {
        this.appealCon = row.appealContent
      }
    },
    async init() {
      const res = await getUserDeptList()
      if (res.code == 200) {
        const deptList = res.deptList ? res.deptList : null
        if (deptList && deptList.length > 0) {
          this.queryParams.deptList = deptList.map(item => item.hDeptName)
        } else {
          this.$modal.msgWarning("当前医生不属于任何科室")
          this.loading = false
          return
        }
      } else {
        this.$modal.msgWarning("当前医生不属于任何科室")
        this.loading = false
        return
      }
      this.queryParams.appealer = this.$store.state.user.nickName ? this.$store.state.user.nickName : null
      this.getList();
    },
    handleAppeal(row) {
      // 跳转到申诉页面
      const routeUrl = this.$router.resolve({
        path: "/views/kkxx/appeal",
        query: {id: row.id}
      })
      window.open(routeUrl.href, '_blank')
    },
    /** 查询扣款明细列表 */
    async getList() {
      const res = await listKkmx(this.queryParams)
      this.kkmxList = res.rows;
      this.total = res.total;
      this.loading = false;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.queryParams.appealStatus = null
      this.queryParams.startDateSend = null
      this.queryParams.endDateSend = null
      this.queryParams.startDate = getYesterdayFirstSecondStr()
      this.queryParams.endDate = getTodayLastSecondStr()
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('gksz/kkmx/export', {
        ...this.queryParams
      }, `扣款明细_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
