<template>
  <div>
    <div>
      <el-form :model="queryParams" ref="queryForm" class="searchForm" :inline="true">
        <el-form-item label="DRG编号">
          <el-autocomplete
            clearable
            class="inline-input"
            v-model="queryParams.drgInfo"
            :fetch-suggestions="querySearch"
            placeholder="DRG编号"
            @select="selectDrgBzbm"
            :popper-append-to-body="false"
            popper-class="custom-autocomplete-popper"
          ></el-autocomplete>
        </el-form-item>

        <el-form-item label="主要诊断编码">
          <el-input v-model="queryParams.zdInfo" placeholder="主要诊断编码" clearable/>
        </el-form-item>

        <el-form-item label="主要手术及操作编码">
          <el-input v-model="queryParams.ssbm" placeholder="主要手术及操作编码" clearable/>
        </el-form-item>

        <el-form-item label="不含手术编号">
          <el-input v-model="queryParams.pcssbm" placeholder="不含手术编号" clearable/>
        </el-form-item>

        <el-form-item label="出院时间">
          <el-date-picker
            v-model="startDateTime"
            type="datetime"
            placeholder="选择开始时间"
            default-time="00:00:00">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="-">
          <el-date-picker
            v-model="endDateTime"
            type="datetime"
            placeholder="选择结束时间"
            default-time="23:59:59">
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
          <el-button type="primary" size="mini" @click="save(1)">提交审核</el-button>
          <el-button size="mini" @click="showDetails">查看审核情况</el-button>
          <el-button type="primary" size="mini" @click="save(0)">保存到草稿箱</el-button>
          <el-button size="mini" @click="showDrafts">草稿箱</el-button>
          <el-button size="mini" @click="customItem">自定义项目</el-button>
          <el-button type="warning" size="mini" @click="save(2)">设置资源消耗</el-button>
        </el-form-item>

      </el-form>
    </div>

    <div>
      <el-table class="fyxm-table" :data="selectFeeData" :border="true">
        <el-table-column label="总费用" align="left" prop="zfy">
          <template slot-scope="scope">
            {{ getZfy(selectItems) }}
          </template>
        </el-table-column>
        <el-table-column label="药品占比" align="left" prop="ypzb">
          <template slot-scope="scope">
            {{ getFyzb("ypf", selectItems) }}
          </template>
        </el-table-column>
        <el-table-column label="耗材占比" align="left" prop="hczb">
          <template slot-scope="scope">
            {{ getFyzb("hcf", selectItems) }}
          </template>
        </el-table-column>
        <el-table-column label="检验占比" align="left" prop="jyzb">
          <template slot-scope="scope">
            {{ getFyzb("jyf", selectItems) }}
          </template>
        </el-table-column>
        <el-table-column label="检查占比" align="left" prop="jczb">
          <template slot-scope="scope">
            {{ getFyzb("jcf", selectItems) }}
          </template>
        </el-table-column>
        <el-table-column label="标杆费用" align="left" prop="bgfy"/>
      </el-table>
    </div>

    <virtual-scroll
      ref="virtualScroll"
      :data="lcljFyxmList"
      :item-size="70"
      @selection-change="handleSelectionChange"
      key-prop="id"
      @change="onChange">
      <el-table
        class="fyxm-table"
        v-loading="loading"
        :data="virtualList"
        stripe
        row-key="id"
        highlight-selection-row
        :border="true"
        height="480">
        <virtual-column fixed="left" class-name="small-padding fixed-width" type="selection" width="80" align="center"/>
        <el-table-column sortable width="75px" show-overflow-tooltip label="使用病人数" align="left" prop="sybrs"/>
        <el-table-column sortable width="60px" show-overflow-tooltip label="总病人数" align="left" prop="zbrs"/>
        <el-table-column
            sortable
            width="90px"
            :filters="RatioOptions"
            :filter-method="RatioFilterMethod"
            show-overflow-tooltip
            label="使用该项目占比"
            align="left"
            prop="sygxmzb"/>
        <el-table-column sortable width="120px" label="日平均用量" align="left" prop="rpjyl">
          <template slot-scope="scope">
            <el-input type="number" v-model="scope.row.rpjyl"
                      @change="validateNumber(scope.row,'rpjyl',scope.row.rpjyl)"/>
          </template>
        </el-table-column>
        <el-table-column sortable width="120px" label="平均用量" align="left" prop="pjyl">
          <template slot-scope="scope">
            <el-input type="number" v-model="scope.row.pjyl" @change="validateNumber(scope.row,'pjyl',scope.row.pjyl)"/>
          </template>
        </el-table-column>
        <el-table-column sortable width="140px" label="平均费用" align="left" prop="pjfy">
          <template slot-scope="scope">
            <el-input type="number" v-model="scope.row.pjfy" @change="validateNumber(scope.row,'pjfy',scope.row.pjfy)"/>
          </template>
        </el-table-column>
        <el-table-column sortable width="250px" show-overflow-tooltip label="项目名称" align="left" prop="xmmc"/>
        <el-table-column
          :filters="filterOptions"
          :filter-method="filterMethod"
          width="120px"
          show-overflow-tooltip
          label="费用类别"
          align="left"
          prop="fykmname"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="使用总天数" align="left" prop="syzts"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="首次使用天数" align="left" prop="scsyts"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="属性" align="left" prop="xmtype">
          <template slot-scope="scope">
            <select v-model="scope.row.xmtype">
              <option>通用</option>
              <option>必做</option>
              <option></option>
            </select>
          </template>
        </el-table-column>
        <el-table-column sortable width="90px" show-overflow-tooltip label="全部病人总用量" align="left" prop="zl"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="费用占比" align="left" prop="fyzb"/>
        <el-table-column sortable width="100px" show-overflow-tooltip label="费用占所有项目的占比" align="left"
                         prop="fyzsyxmdzb"/>
        <el-table-column sortable width="250px" show-overflow-tooltip label="项目编码" align="left" prop="xmbm"/>
        <el-table-column sortable width="100px" show-overflow-tooltip label="平均住院费" align="left" prop="pjzyf"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="平均药品费" align="left" prop="ypf"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="平均检验费" align="left" prop="jyf"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="平均检查费" align="left" prop="jcf"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="平均手术费" align="left" prop="ssf"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="平均治疗费" align="left" prop="zlf"/>
        <el-table-column sortable width="80px" show-overflow-tooltip label="平均耗材费" align="left" prop="hcf"/>
        <el-table-column fixed="right" label="操作">
          <template slot-scope="{ row }">
            <el-button size="mini" type="text" @click="delItem(row)">删除</el-button>
          </template>
        </el-table-column>
        <div slot="append">
          <el-divider>到底了~</el-divider>
        </div>
      </el-table>

    </virtual-scroll>

    <el-drawer
      title="草稿箱"
      :visible.sync="draftsDrawerVisible"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeDrawer"
      size="80%">

      <div class="drawer-container">
        <div style="display: flex;margin: 15px;">
          <el-form :model="draftsParams" ref="queryForm" class="drafts-card" :inline="true">
            <el-form-item label="DRG编码" prop="drgbh">
              <el-input
                v-model="draftsParams.drgbh"
                placeholder="DRG编码"
                clearable
              />
            </el-form-item>
            <el-form-item label="主要诊断编码" prop="jbbm">
              <el-input
                v-model="draftsParams.bzbm"
                placeholder="主要诊断编码"
                clearable
              />
            </el-form-item>
            <el-form-item label="主要手术编码" prop="ssbm">
              <el-input
                v-model="draftsParams.ssbm"
                placeholder="主要手术编码"
                clearable
              />
            </el-form-item>
            <el-form-item label="不含手术编号" prop="pcssbm">
              <el-input
                v-model="draftsParams.pcssbm"
                placeholder="不含手术编号"
                clearable
              />
            </el-form-item>

            <el-form-item label="草稿创建时间">
              <el-date-picker
                v-model="draftsParams.startDateTime"
                type="datetime"
                placeholder="选择开始时间"
                default-time="00:00:00">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="">
              <el-date-picker
                v-model="draftsParams.endDateTime"
                type="datetime"
                placeholder="选择结束时间"
                default-time="23:59:59">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="showDrafts">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetDrafts">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :border="true" :data="draftsList" height="600">
          <el-table-column label="DRG编码" align="center" prop="drgbh"/>
          <el-table-column label="主要诊断编码" align="center" prop="bzbm"/>
          <el-table-column label="主要手术编码" align="center" prop="ssbm"/>
          <el-table-column label="不含手术编号" align="center" prop="pcssbm"/>
          <el-table-column label="草稿创建时间" align="center" prop="createDate"/>
          <el-table-column label="操作" align="center" width="220">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="show(scope.$index,scope.row)"
              >查看
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="deleteDrafts(scope.$index,scope.row)"
              >删除
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="edit(scope.$index,scope.row,'submit')"
              >继续编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <el-drawer
      title="审核情况"
      :visible.sync="drawerVisible"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeDrawer"
      size="80%">
      <div class="drawer-container">
        <div style="display: flex;margin: 15px;">
          <el-select v-model="status" placeholder="请选择审核状态">
            <el-option label="全部" value="%"/>
            <el-option label="待审核" value="0"/>
            <el-option label="已通过" value="1"/>
            <el-option label="未通过" value="2"/>
          </el-select>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="showDetails">搜索</el-button>
        </div>
        <el-table :border="true" :data="lcljList" height="600">
          <el-table-column show-overflow-tooltip label="DRG编码" align="center" prop="drgbh"/>
          <el-table-column show-overflow-tooltip label="主要诊断编码" align="center" prop="bzbm"/>
          <el-table-column show-overflow-tooltip label="主要手术编码" align="center" prop="ssbm"/>
          <el-table-column show-overflow-tooltip label="不含手术编号" align="center" prop="pcssbm"/>
          <el-table-column show-overflow-tooltip label="审核状态" align="center" prop="status">
            <template slot-scope="scope">
              <div v-if="scope.row.status == 0">待审核</div>
              <div v-if="scope.row.status == 1">已通过</div>
              <div v-if="scope.row.status == 2" style="color: red;">未通过</div>
            </template>
          </el-table-column>
          <el-table-column width="600px" show-overflow-tooltip label="备注" align="left" prop="remark"/>
          <el-table-column label="操作" align="center" width="220">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.status == 2"
                size="mini"
                type="text"
                @click="edit(scope.$index,scope.row,'resubmit')"
              >重新提交
              </el-button>
              <el-button
                v-if="scope.row.status == 1"
                size="mini"
                type="text"
                @click="edit(scope.$index,scope.row,'resubmit')"
              >编辑
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="show(scope.$index,scope.row)"
              >查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>

    <el-drawer
      :visible.sync="fyxmDrawerVisible"
      direction="ltr"
      :title="fyxmDrawerTitle"
      size="90%">
      <div class="drawer-container">
        <el-table class="fyxm-table" :data="feeData" :border="true">
          <el-table-column label="总费用" align="left" prop="zfy">
            <template slot-scope="scope">
              {{ getZfy(fyxmList) }}
            </template>
          </el-table-column>
          <el-table-column label="药品占比" align="left" prop="ypzb">
            <template slot-scope="scope">
              {{ getFyzb("ypf", fyxmList) }}
            </template>
          </el-table-column>
          <el-table-column label="耗材占比" align="left" prop="hczb">
            <template slot-scope="scope">
              {{ getFyzb("hcf", fyxmList) }}
            </template>
          </el-table-column>
          <el-table-column label="检验占比" align="left" prop="jyzb">
            <template slot-scope="scope">
              {{ getFyzb("jyf", fyxmList) }}
            </template>
          </el-table-column>
          <el-table-column label="检查占比" align="left" prop="jczb">
            <template slot-scope="scope">
              {{ getFyzb("jcf", fyxmList) }}
            </template>
          </el-table-column>
          <el-table-column label="标杆费用" align="left" prop="bgfy"/>
        </el-table>
        <virtual-scroll
          :data="fyxmList"
          :item-size="70"
          key-prop="id"
          @change="onChange2">
          <el-table
            :data="virtualList2"
            height="600"
            stripe
            row-key="id"
            highlight-selection-row
            :border="true">
            <el-table-column sortable width="75px" show-overflow-tooltip label="使用病人数" align="center"
                             prop="sybrs"/>
            <el-table-column sortable width="60px" show-overflow-tooltip label="总病人数" align="center" prop="zbrs"/>
            <el-table-column sortable width="90px" show-overflow-tooltip label="使用该项目占比" align="center"
                             prop="sygxmzb"/>
            <el-table-column sortable width="120px" show-overflow-tooltip label="日平均用量" align="center"
                             prop="rpjyl"/>
            <el-table-column sortable width="120px" show-overflow-tooltip label="平均用量" align="center" prop="pjyl"/>
            <el-table-column sortable width="140px" show-overflow-tooltip label="平均费用" align="center" prop="pjfy"/>
            <el-table-column sortable width="250px" show-overflow-tooltip label="项目名称" align="left" prop="xmmc"/>
            <el-table-column sortable width="120px" show-overflow-tooltip label="费用类别" align="left"
                             prop="fykmname"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="使用总天数" align="center"
                             prop="syzts"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="首次使用天数" align="center"
                             prop="scsyts"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="属性" align="center" prop="xmtype"/>
            <el-table-column sortable width="90px" show-overflow-tooltip label="全部病人总用量" align="center"
                             prop="zl"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="费用占比" align="center" prop="fyzb"/>
            <el-table-column sortable width="100px" show-overflow-tooltip label="费用占所有项目的占比" align="center"
                             prop="fyzsyxmdzb"/>
            <el-table-column sortable width="250px" show-overflow-tooltip label="项目编码" align="left" prop="xmbm"/>
            <el-table-column sortable width="100px" show-overflow-tooltip label="平均住院费" align="center"
                             prop="pjzyf"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="平均药品费" align="center" prop="ypf"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="平均检验费" align="center" prop="jyf"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="平均检查费" align="center" prop="jcf"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="平均手术费" align="center" prop="ssf"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="平均治疗费" align="center" prop="zlf"/>
            <el-table-column sortable width="80px" show-overflow-tooltip label="平均耗材费" align="center" prop="hcf"/>
            <div slot="append">
              <el-divider>到底了~</el-divider>
            </div>
          </el-table>
        </virtual-scroll>
      </div>
    </el-drawer>

    <el-drawer
      :visible.sync="dzxxDrawerVisible"
      direction="ltr"
      :title="dzxxDrawerTitle"
      size="60%">
      <div class="drawer-container">

        <el-form :model="dzxxParams" class="searchForm" :inline="true">
          <el-form-item label="医保名称">
            <el-input v-model="dzxxParams.ybname" placeholder="医保名称" clearable/>
          </el-form-item>
          <el-form-item label="助记码">
            <el-input v-model="dzxxParams.nccd" placeholder="助记码" clearable/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getDzxxList">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetDzxxQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table class="dzxx-table" v-loading="dzxxLoading" :data="dzxxList" :border="true">
          <el-table-column show-overflow-tooltip label="医保编码" align="left" prop="xmbm"/>
          <el-table-column show-overflow-tooltip label="医保名称" align="left" prop="ybname"/>
          <el-table-column width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="selectDzItem(scope.$index,scope.row)">选择此项目</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="dzxxTotal>0"
          :total="dzxxTotal"
          :page.sync="dzxxParams.pageNum"
          :limit.sync="dzxxParams.pageSize"
          @pagination="getDzxxList"
        />

      </div>
    </el-drawer>

    <el-drawer
      :visible.sync="dzItemDrawerVisible"
      direction="rtl"
      @close="closeDzItemDrawer"
      :title="dzItemDrawerTitle"
      size="60%">
      <div class="drawer-container">
        <el-form :inline="true" :label-position="labelPosition" label-width="200px" class="dzItem-form" ref="dzItemForm"
                 :rules="dzItemRules" :model="dzxxItemParams">
          <div>
            <el-form-item label="项目名称" prop="xmmc">
              <el-input disabled v-model="dzxxItemParams.xmmc" placeholder="项目名称" clearable/>
            </el-form-item>
            <el-form-item label="项目编码" prop="xmbm">
              <el-input disabled v-model="dzxxItemParams.xmbm" placeholder="项目编码" clearable/>
            </el-form-item>
            <el-form-item label="日平均用量" prop="rpjyl">
              <el-input type="number" v-model="dzxxItemParams.rpjyl" placeholder="日平均用量" clearable/>
            </el-form-item>
            <el-form-item label="平均用量" prop="pjyl">
              <el-input type="number" v-model="dzxxItemParams.pjyl" placeholder="平均用量" clearable/>
            </el-form-item>
            <el-form-item label="平均费用" prop="pjfy">
              <el-input type="number" v-model="dzxxItemParams.pjfy" placeholder="平均费用" clearable/>
            </el-form-item>
            <el-form-item label="费用类别" prop="fykmname">
              <el-input v-model="dzxxItemParams.fykmname" placeholder="费用类别" clearable/>
            </el-form-item>
            <el-form-item label="使用病人数" prop="sybrs">
              <el-input type="number" v-model="dzxxItemParams.sybrs" placeholder="使用病人数" clearable/>
            </el-form-item>
            <el-form-item label="总病人数" prop="zbrs">
              <el-input type="number" v-model="dzxxItemParams.zbrs" placeholder="总病人数" clearable/>
            </el-form-item>
            <el-form-item label="使用总天数" prop="syzts">
              <el-input type="number" v-model="dzxxItemParams.syzts" placeholder="使用总天数" clearable/>
            </el-form-item>
            <el-form-item label="属性" prop="xmtype">
              <el-select v-model="dzxxItemParams.xmtype">
                <el-option value="" label="空"></el-option>
                <el-option value="通用" label="通用"></el-option>
                <el-option value="必做" label="必做"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="首次使用天数" prop="scsyts">
              <el-input type="number" v-model="dzxxItemParams.scsyts" placeholder="首次使用天数" clearable/>
            </el-form-item>
            <el-form-item type="number" label="费用占所有项目的占比" prop="fyzsyxmdzb">
              <el-input v-model="dzxxItemParams.fyzsyxmdzb" placeholder="费用占所有项目的占比" clearable/>
            </el-form-item>
          </div>
          <div class="dzItem-form-footer">
            <el-form-item>
              <el-button class="dzItem-form-button" type="primary" @click="addDzxxItem">添加</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-drawer>

    <div style="height: 20px;"></div>

  </div>
</template>

<script>
import {
  delLcljById,
  listLclj,
  listLcljFyxm,
  listLcljFyxmByLclj,
  saveLcljAndFyxms,
  selectLcljFyxmShById,
  selectLcljItem
} from "@/api/clinicalPath/lcljfyxm";
import {dateToString, getMonthFirstDay, getYearFirstDay, getTodayLastSecond} from "@/utils/dateUtils";
import {drgdictByDrgbh} from "@/api/drg/drgdict";
import {listDz} from "@/api/gksz/dz";
import VirtualScroll from 'el-table-virtual-scroll'
import {VirtualColumn} from 'el-table-virtual-scroll'
import {getDrgBzbmCount} from '@/api/drg/syjl'

export default {
  name: "Lcljfyxm",
  components: {
    VirtualScroll,
    VirtualColumn
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (value <= 0 && value != null && value != undefined && value != '') {
        return callback(new Error('数据应该大于0'));
      } else {
        callback();
      }
    };
    return {
      //单项对照信息
      labelPosition: 'left',
      dzItemRules: {
        sybrs: [
          {validator: checkNumber, trigger: 'blur'}
        ],
        zbrs: [
          {validator: checkNumber, trigger: 'blur'}
        ],
        xmbm: [
          {required: true, message: "项目编码不能为空", trigger: "blur"},
          {validator: checkNumber, trigger: 'blur'}
        ],
        xmmc: [
          {required: true, message: "项目名称不能为空", trigger: "blur"},
          {validator: checkNumber, trigger: 'blur'}
        ],
        rpjyl: [
          {required: true, message: "日平均用量不能为空", trigger: "blur"},
          {validator: checkNumber, trigger: 'blur'}
        ],
        pjfy: [
          {required: true, message: "平均费用不能为空", trigger: "blur"},
          {validator: checkNumber, trigger: 'blur'}
        ],
        syzts: [
          {validator: checkNumber, trigger: 'blur'}
        ],
        scsyts: [
          {validator: checkNumber, trigger: 'blur'}
        ],
        pjyl: [
          {required: true, message: "平均用量不能为空", trigger: "blur"},
          {validator: checkNumber, trigger: 'blur'}
        ],
        fyzsyxmdzb: [
          {validator: checkNumber, trigger: 'blur'}
        ],
      },
      dzxxItemParams: {
        sybrs: null,
        zbrs: null,
        sygxmzb: null,
        rpjyl: null,
        pjyl: null,
        pjfy: null,
        xmmc: null,
        fykmname: null,
        syzts: null,
        xmtype: null,
        scsyts: null,
        fyzb: null,
        fyzsyxmdzb: null,
        xmbm: null,
        pjzyf: null,
        zl: null,
        ypf: null,
        jyf: null,
        jcf: null,
        ssf: null,
        zlf: null,
        hcf: null
      },
      dzItemDrawerTitle: null,
      dzItemDrawerVisible: false,
      //对照信息相关数据
      dzxxLoading: false,
      dzxxParams: {
        pageNum: 1,
        pageSize: 10,
        ybname: null,
        nccd: null,
        xmbm: null
      },
      dzxxTotal: 0,
      dzxxList: [],
      dzxxDrawerTitle: "项目对照信息",
      dzxxDrawerVisible: false,
      //前端跳转地址
      fyxmDrawerVisible: false,
      fyxmDrawerTitle: null,
      filterOptions: [],
      RatioOptions: [
        { text: '0-0.1', value: 0.1},
        { text: '0.1-0.2', value: 0.2},
        { text: '0.2-0.3', value: 0.3},
        { text: '0.3-0.4', value: 0.4},
        { text: '0.4-0.5', value: 0.5},
        { text: '0.5-0.6', value: 0.6},
        { text: '0.6-0.7', value: 0.7},
        { text: '0.7-0.8', value: 0.8},
        { text: '0.8-0.9', value: 0.9},
        { text: '0.9-1.0', value: 1.0},
      ],
      drawerVisible: false,
      draftsDrawerVisible: false,
      // 遮罩层
      loading: false,
      // 选中数组
      selectItems: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 临床路径费用项目表格数据
      fyxmList: [],
      lcljFyxmList: [],
      virtualList: [],
      virtualList2: [],
      lcljList: [],
      draftsList: [],
      // 弹出层标题
      title: "",
      queryParams: {
        id: null,
        cyStartDate: null,
        cyEndDate: null,
        drgbh: null,
        bzbm: null,
        ssbm: null,
        pcssbm: null,
        drgInfo: null,
        zdInfo: null,
        submitStatus: null,
        useRatio: 0
      },
      startDateTime: getYearFirstDay(),
      endDateTime: getTodayLastSecond(),
      status: "%",
      selectFeeData: [{
        zfy: 0,
        ypzb: 0,
        hczb: 0,
        jyzb: 0,
        jczb: 0,
        bgfy: 0
      }],
      fyTypeMap: {
        jcf: ["检查费", "检查费[病理]", "影像学诊断费", "检查费[麻醉]"],
        ypf: ["中成费", "中成药费", "西药费", "中草费", "中草药费"],
        jyf: ["检验费", "实验室诊断费", "病理诊断费"],
        hcf: ["检查用一次性医用材料费", "手术用一次性医用材料费", "治疗用一次性医用材料费", "卫生材料"]
      },
      feeData: [{
        zfy: 0,
        ypzb: 0,
        hczb: 0,
        jyzb: 0,
        jczb: 0,
        bgfy: 0
      }],
      draftsParams: {
        pageNum: 1,
        pageSize: 1000000000,
        pcssbm: null,
        bzbm: null,
        ssbm: null,
        drgbh: null,
        submitStatus: 0,
        startCreateDate: null,
        endCreateDate: null,
        startDateTime: getMonthFirstDay(),
        endDateTime: getTodayLastSecond(),
      }
    }
  },
  methods: {
    async querySearch(queryString, cb) {
      const drgBzbmList = await getDrgBzbmCount()
      if (drgBzbmList != null && drgBzbmList != undefined && drgBzbmList.hasOwnProperty("rows")) {
        const result = drgBzbmList.rows.map(item => ({
          value: item.cykb + '--' + item.drgbh + (item.drgmc ? '[' + item.drgmc + ']' : '') + '--' + item.jbdm + (item.zyzd ? '[' + item.zyzd + ']': '') +  '--' + item.sl,
          drgInfo: item.drgbh + (item.drgmc ? '[' + item.drgmc + ']' : ''),
          zdInfo: item.jbdm + (item.zyzd ? '[' + item.zyzd + ']': ''),
          drgbh: item.drgbh,
          jbdm: item.jbdm,
          cykb: item.cykb,
          sl: item.sl
        }))
        if (queryString == null || queryString == '') {
          cb(result);
        }
        const filtered = result.filter(item => item.value.includes(queryString) || queryString == null);
        cb(filtered);
      } else {
        cb([])
      }
    },
    selectDrgBzbm(item) {
      this.queryParams = {
        id: null,
        cyStartDate: null,
        cyEndDate: null,
        drgbh: item.drgbh,
        bzbm: item.jbdm,
        ssbm: null,
        pcssbm: null,
        submitStatus: null,
        drgInfo: item.drgInfo,
        zdInfo: item.zdInfo,
      }
      this.startDateTime = getYearFirstDay()
      this.endDateTime = getTodayLastSecond()
    },
    delItem(row) {
      const index = this.lcljFyxmList.findIndex(item => item === row)
      if (index > -1) {
        this.lcljFyxmList.splice(index, 1)
      }
    },
    //编辑
    async edit(index, row, flag) {
      const editPathId = row.id
      this.loading = true

      const data = await selectLcljFyxmShById({id: editPathId}).catch(err => {
        this.$modal.msgError("获取当前方案出错！" + err)
        this.loading = false
        return
      });

      if (data == '' || data == null || data == undefined) {
        this.$modal.msgWarning("当前治疗方案不存在！")
        this.loading = false
        return
      }

      this.draftsDrawerVisible = false
      this.drawerVisible = false
      this.queryParams = {
        id: data.id,
        drgbh: data.drgbh,
        bzbm: data.bzbm,
        ssbm: data.ssbm,
        pcssbm: data.pcssbm,
        submitStatus: data.submitStatus,
        cyStartDate: null,
        cyEndDate: null,
      }
      this.startDateTime = getYearFirstDay()
      this.endDateTime = getTodayLastSecond()
      this.handleQuery(flag)
      console.log(this.queryParams)
    },
    resetFeeData() {
      this.selectFeeData = [{
        zfy: 0,
        ypzb: 0,
        hczb: 0,
        jyzb: 0,
        jczb: 0,
        bgfy: 0
      }]
      this.selectItems = []
    },
    onChange(renderData) {
      this.virtualList = renderData
    },
    onChange2(renderData) {
      this.virtualList2 = renderData
    },
    //添加项目
    closeDzItemDrawer() {
      this.resetDzItemForm()
    },
    resetDzItemForm() {
      this.dzxxItemParams = {
        sybrs: null,
        zbrs: null,
        sygxmzb: null,
        rpjyl: null,
        pjyl: null,
        pjfy: null,
        xmmc: null,
        fykmname: null,
        syzts: null,
        xmtype: null,
        scsyts: null,
        fyzb: null,
        fyzsyxmdzb: null,
        xmbm: null,
        pjzyf: null,
        zl: null,
        ypf: null,
        jyf: null,
        jcf: null,
        ssf: null,
        zlf: null,
        hcf: null
      }
    },
    addDzxxItem() {
      this.$refs["dzItemForm"].validate(valid => {
        if (valid) {
          if (this.lcljFyxmList.length > 0) {
            this.dzxxItemParams.drgbh = this.lcljFyxmList[0].drgbh
            this.dzxxItemParams.bzbm = this.lcljFyxmList[0].bzbm
            this.dzxxItemParams.ssbm = this.lcljFyxmList[0].ssbm
            this.dzxxItemParams.pcssbm = this.lcljFyxmList[0].pcssbm
          } else {
            this.dzxxItemParams.drgbh = this.queryParams.drgbh
            this.dzxxItemParams.bzbm = this.queryParams.bzbm
            this.dzxxItemParams.ssbm = this.queryParams.ssbm
            this.dzxxItemParams.pcssbm = this.queryParams.pcssbm
          }
          this.lcljFyxmList.push(this.dzxxItemParams);
          this.$nextTick(() => {
            this.$refs.virtualScroll.toggleRowSelection(this.lcljFyxmList[this.lcljFyxmList.length - 1], true);
          });
          this.dzItemDrawerVisible = false;
          this.$modal.msgSuccess("添加成功");
          this.resetDzItemForm()
        }
      });
    },
    selectDzItem(index, data) {
      for (let i = 0; i < this.lcljFyxmList.length; i++) {
        if (this.lcljFyxmList[i].xmmc == data.ybname && this.lcljFyxmList[i].xmbm == data.xmbm) {
          this.$modal.msgWarning("已存在当前项目")
          return
        }
      }
      this.dzItemDrawerVisible = true
      this.dzItemDrawerTitle = data.ybname
      this.dzxxItemParams.xmmc = data.ybname
      this.dzxxItemParams.xmbm = data.xmbm
    },
    resetDzxxQuery() {
      this.dzxxParams = {
        pageNum: 1,
        pageSize: 10,
        ybname: null,
        nccd: null
      }
      this.getDzxxList()
    },
    customItem() {
      if (this.queryParams.drgbh == null || this.queryParams.drgbh == '' || this.queryParams.bzbm == null || this.queryParams.bzbm == '') {
        this.$modal.msgWarning("请先输入DRG编号和诊断等信息！")
        return
      }
      if (this.dzxxList == null || this.dzxxList.length == 0) {
        listDz(this.dzxxParams).then(res => {
          this.dzxxList = res.rows
          this.dzxxTotal = res.total
          this.dzxxDrawerVisible = true
        }).catch(err => {
          this.$modal.msgError("获取失败" + err)
          this.dzxxDrawerVisible = false
        })
      } else {
        this.dzxxDrawerVisible = true
      }
    },
    getDzxxList() {
      this.dzxxLoading = true
      listDz(this.dzxxParams).then(res => {
        this.dzxxList = res.rows
        this.dzxxTotal = res.total
        this.dzxxLoading = false
      }).catch(err => {
        this.$modal.msgError("获取失败" + err)
        this.dzxxLoading = false
      })
    },
    resetDrafts() {
      this.draftsParams = {
        pageNum: 1,
        pageSize: 1000000000,
        pcssbm: null,
        bzbm: null,
        ssbm: null,
        drgbh: null,
        submitStatus: 0,
        startCreateDate: null,
        endCreateDate: null,
        startDateTime: getMonthFirstDay(),
        endDateTime: getTodayLastSecond(),
      }
      this.showDrafts()
    },
    deleteDrafts(index, row) {
      delLcljById(row.id).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("删除成功");
          this.draftsList.splice(index, 1)
        }
      })
    },
    async save(submitStatus) {
      //判断是否进行了勾选
      if (this.selectItems.length == 0) {
        this.$modal.msgWarning("请选择要保存的数据！")
        return
      }
      //如果是保存为草稿，则判断是否已经存在该方案的提交记录，若存在提交记录则不能保存为草稿
      const params = {
        drgbh: this.selectItems[0].drgbh,
        bzbm: this.selectItems[0].bzbm,
        ssbm: this.selectItems[0].ssbm,
        pcssbm: this.selectItems[0].pcssbm,
        submitStatus: 1
      }
      const data = await selectLcljItem(params)
      if (data != '' && data != null && data != undefined) {
        if (submitStatus == 0) {
          console.log(this.queryParams)
          if (this.queryParams.id != null && this.queryParams.submitStatus == 1) {
            this.saveProjects(submitStatus)
            return
          }
          this.$modal.alertWarning("已存在相同的DRG编号和诊断提交记录")
          return
        }
        if (submitStatus == 1 || submitStatus == 2) {
          const status = data.status == 1 ? "审核通过" : data.status == 2 ? "审核不通过" : "待审核";
          this.$confirm("已存在相同的DRG编号和诊断提交记录，审核状态：" + status + "，是否覆盖", '提示', {
            confirmButtonText: '覆盖',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.saveProjects(submitStatus)
          }).catch(() => {
          });
          return
        }
      } else {
        this.saveProjects(submitStatus)
      }
    },
    //保存
    saveProjects(submitStatus) {
      this.loading = true;
      const queryParams = {
        drgbh: this.selectItems[0].drgbh,
        bzbm: this.selectItems[0].bzbm,
        ssbm: this.selectItems[0].ssbm,
        pcssbm: this.selectItems[0].pcssbm,
        submitStatus: submitStatus,
        fyxmList: this.selectItems,
        status: 0,
      }
      let dataStr = JSON.stringify(queryParams)
      saveLcljAndFyxms(dataStr).then(res => {
        if (submitStatus = '2') {
          this.$modal.msgSuccess("设置成功！")
        } else {
          this.$modal.msgSuccess("保存成功！")
        }
        this.queryParams.submitStatus = 0
        this.loading = false;
      }).catch(err => {
        this.$modal.msgError("保存失败！")
        this.loading = false;
      })
    },
    //计算四费占比
    getFyzb(type, list) {
      if (list != null && list.length > 0) {
        const allfy = this.getZfy(list);
        if (allfy == 0) return 0;
        const totalPjfy = list.reduce((accumulator, currentObj) => {
          if (this.fyTypeMap[type].includes(currentObj.fykmname)) {
            return accumulator + (parseFloat(currentObj.pjfy) || 0);
          }
          return accumulator;
        }, 0);
        return (totalPjfy / allfy * 100).toFixed(2) + '%';
      } else {
        return 0;
      }
    },
    //计算选中项目总费用
    getZfy(list) {
      if (list != null && list.length > 0) {
        return list.reduce((acc, item) => acc + parseFloat(item.pjfy), 0).toFixed(4)
      } else {
        return 0;
      }
    },
    //查看已提交方案的项目明细
    show(index, row) {
      this.fyxmList = row.fyxmList
      this.fyxmDrawerTitle = "DRG编号：" + row.drgbh + "|诊断编码：" + row.bzbm + (row.ssbm == null ? "" : "|手术编码：" + row.ssbm) + (row.pcssbm == null ? "" : "|不含手术编码：" + row.pcssbm)
      drgdictByDrgbh(row.drgbh).then(res => {
        if (res != undefined && res != null) {
          this.feeData[0].bgfy = res.zfbz
          this.fyxmDrawerVisible = true;
        } else {
          this.fyxmDrawerVisible = true;
        }
      }).catch(err => {
        this.fyxmDrawerVisible = true;
      })
    },
    //删除已提交但还未审核的方案
    del(index, row) {
      delLcljById(row.id).then(res => {
        if (res.code == 200) {
          this.$modal.msgSuccess("删除成功");
          this.lcljList.splice(index, 1)
        }
      })
    },
    //获取费别筛选选项列表
    getFilterOptions() {
      const uniqueFykmnamea = new Set(this.lcljFyxmList.map(item => item.fykmname));
      const newArray = Array.from(uniqueFykmnamea).map(fykmname => ({
        text: fykmname,
        value: fykmname
      }));
      this.filterOptions = newArray
    },
    RatioFilterMethod(value, row) {
      return row.sygxmzb >= value - 0.1 && row.sygxmzb <= value
    },
    //根据费别过滤
    filterMethod(value, row) {
      return row.fykmname == value
    },
    //验证可修改值的正确性
    validateNumber(row, type, value) {
      if (value <= 0) {
        this.$modal.msgWarning("当前数据不能为0或负数")
        row[type] = 0
      }
    },
    //用于查看已提交方案列表时，可根据状态筛选，关闭抽屉自动清空状态
    closeDrawer() {
      this.status = "%"
      this.draftsParams = {
        pageNum: 1,
        pageSize: 1000000000,
        pcssbm: null,
        bzbm: null,
        ssbm: null,
        drgbh: null,
        submitStatus: 0,
        startCreateDate: null,
        endCreateDate: null,
        startDateTime: getMonthFirstDay(),
        endDateTime: getTodayLastSecond(),
      }
    },
    //查看审核情况
    showDetails() {
      const queryParams = {
        pageNum: 1,
        pageSize: 1000000000,
        status: this.status == "%" ? null : this.status,
        submitStatus: 1
      }
      listLclj(queryParams).then(response => {
        this.lcljList = response.rows;
      });
      this.drawerVisible = true
    },
    //查看草稿箱
    showDrafts() {
      if (this.draftsParams.startDateTime != null && this.draftsParams.startDateTime != '') {
        this.draftsParams.startCreateDate = dateToString(this.draftsParams.startDateTime)
      } else {
        this.draftsParams.startDateTime = null
      }
      if (this.draftsParams.endDateTime != null && this.draftsParams.endDateTimev != '') {
        this.draftsParams.endCreateDate = dateToString(this.draftsParams.endDateTime)
      } else {
        this.draftsParams.endCreateDate = null
      }
      listLclj(this.draftsParams).then(response => {
        this.draftsList = response.rows;
      });
      this.draftsDrawerVisible = true
    },
    //根据条件，获取可选费用项目信息
    getList() {
      this.resetFeeData()
      this.loading = true;
      listLcljFyxm(this.queryParams).then(response => {
        this.lcljFyxmList = response.rows;
        this.lcljFyxmList.forEach(row => {
          if (row.selectStatus === 1) {
            this.$nextTick(() => {
              this.$refs.virtualScroll.toggleRowSelection(row, true);
            });
          }
        })
        this.loading = false;
        if (this.lcljFyxmList.length == 0) {
          this.$modal.msgWarning("没有对应费用数据")
        } else {
          this.getBgfy()
          this.getFilterOptions()
        }
      });
    },
    //标杆费用
    getBgfy() {
      this.selectFeeData[0].bgfy = this.lcljFyxmList[0].zfbz
    },
    /** 搜索按钮操作 */
    handleQuery(flag) {
      if (this.queryParams.drgbh == null || this.queryParams.drgbh == '' || this.queryParams.bzbm == null || this.queryParams.bzbm == '') {
        this.$modal.msgWarning("请输入查询信息！")
        return
      }
      if (this.startDateTime != null && this.startDateTime != '') {
        this.queryParams.cyStartDate = dateToString(this.startDateTime)
      } else {
        this.queryParams.cyStartDate = null;
      }
      if (this.endDateTime != null && this.endDateTime != '') {
        this.queryParams.cyEndDate = dateToString(this.endDateTime)
      } else {
        this.queryParams.cyEndDate = null;
      }
      if (flag != "resubmit") {
        this.queryParams.submitStatus = null
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetFeeData()
      this.queryParams = {
        id: null,
        cyStartDate: null,
        cyEndDate: null,
        drgbh: null,
        bzbm: null,
        ssbm: null,
        pcssbm: null,
        submitStatus: null
      }
      this.lcljFyxmList = []
    },
    //导出
    async handleExport() {
      let exportParams = null;
      if (this.lcljFyxmList.length > 0) {
        exportParams = {
          drgbh: this.lcljFyxmList[0].drgbh,
          bzbm: this.lcljFyxmList[0].bzbm,
          ssbm: this.lcljFyxmList[0].ssbm,
          pcssbm: this.lcljFyxmList[0].pcssbm,
        }
      } else if (this.queryParams.drgbh != null && this.queryParams.drgbh != '' &&
        this.queryParams.bzbm != null && this.queryParams.bzbm != '') {
        exportParams = {
          drgbh: this.queryParams.drgbh,
          bzbm: this.queryParams.bzbm,
          ssbm: this.queryParams.ssbm,
          pcssbm: this.queryParams.pcssbm,
        }
      }
      if (exportParams == null) {
        this.$modal.alertError("请先确认治疗方案！")
        return
      }
      const lcljFyxms = await listLcljFyxmByLclj(this.queryParams)
      if (lcljFyxms.total == 0) {
        this.$modal.alertError("请先保存数据！")
        return
      }
      this.download('clinicalPath/lcljFyxm/export', {
        ...exportParams
      }, `治疗方案费用项目数据${new Date().getTime()}.xlsx`)
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectItems = selection.map(item => item)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
  },
  created() {
  },
}
</script>

<style lang="scss">
.fyxm-table, .searchForm {
  margin-left: 1%;
  width: 98%;
}

.searchForm {
  margin-top: 20px;
}

.dzxx-table {
  width: 98%;
  margin: 0 1%;
}

.dzItem-form {
  width: 98%;
  margin: 0 1%;
}

.dzItem-form-footer {
  width: 100%;
  text-align: left;
}

.custom-autocomplete-popper {
  width: 700px !important; /* 设置弹出框宽度，根据需要调整 */
}
</style>
