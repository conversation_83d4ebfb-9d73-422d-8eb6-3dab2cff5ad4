<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="病人姓名" prop="brname">
        <el-input
          v-model="queryParams.brname"
          placeholder="病人姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="住院号" prop="zyh">
        <el-input
          v-model="queryParams.zyh"
          placeholder="住院号"
          clearable
        />
      </el-form-item>
      <el-form-item label="费用项目" prop="fymName">
        <el-input
          v-model="queryParams.fymName"
          placeholder="费用项目"
          clearable
        />
      </el-form-item>
      <dept-doctor-selector v-model="selectedData"/>
      <el-form-item label="违规时间">
        <el-date-picker
          v-model="queryParams.startDate"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.endDate"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table border v-loading="loading" :data="dataList">
       <el-table-column show-overflow-tooltip width="60" label="类别" align="left" prop="brtype"/>
      <el-table-column label="科室" align="left" prop="kdksname" width="150px" show-overflow-tooltip />
      <el-table-column label="医生" align="left" prop="doctorname" width="120px" show-overflow-tooltip/>
      <el-table-column label="住院号" align="left" prop="zyh" width="120px" show-overflow-tooltip/>
      <el-table-column label="病人姓名" align="left" prop="brname" width="120px" show-overflow-tooltip/>
     <el-table-column show-overflow-tooltip width="100" label="身份证" align="left" prop="sfz"/>
      <el-table-column label="参保类别" align="left" prop="cblb" width="100px" show-overflow-tooltip/>
     
      <el-table-column label="项目编码" align="left" prop="fymId" width="220px" show-overflow-tooltip/>
      <el-table-column label="项目名称" align="left" prop="fymName" width="220px" show-overflow-tooltip/>
      <el-table-column show-overflow-tooltip width="100" label="入院时间" align="left" prop="rydate"/>
      <el-table-column show-overflow-tooltip width="100" label="出院时间" align="left" prop="cydate"/>
      <el-table-column show-overflow-tooltip width="100" label="问题类型" align="left" prop="jktype"/>
      <el-table-column label="违规数量" align="left" prop="sl" width="100px" show-overflow-tooltip/>
      <el-table-column label="违规金额" align="left" prop="je" width="100px" show-overflow-tooltip/>
       <el-table-column show-overflow-tooltip width="100" label="单价" align="left" prop="price"/>
        <el-table-column label="费用等级" align="left" prop="fydj" width="100px" show-overflow-tooltip/>
      <el-table-column label="违规内容" align="left" prop="jklog" width="650px" show-overflow-tooltip/>
      <el-table-column show-overflow-tooltip width="600" label="诊断信息" align="left" prop="zdxx"/>
      <el-table-column show-overflow-tooltip width="100" label="医保报销" align="left" prop="ybbx"/>
      <el-table-column show-overflow-tooltip width="100" label="统筹" align="left" prop="tc"/>
      <el-table-column show-overflow-tooltip width="100" label="大额" align="left" prop="de"/>
      <el-table-column show-overflow-tooltip width="100" label="医疗救助" align="left" prop="yljz"/>
<!--      <el-table-column label="医保备注" align="left" prop="ybbz" width="650px" show-overflow-tooltip/>-->
    </el-table>

    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {gdjclist} from "@/api/gksz/wgjl";
import {getTodayLastSecondStr, getYesterdayFirstSecondStr} from "@/utils/dateUtils";
import DrgSelector from "@/components/DrgSelector/index.vue";
import DeptDoctorSelector from "@/components/DeptDoctorSelector/index.vue";


export default {
  name: "WgjlGdjc",
  components: {DeptDoctorSelector, DrgSelector},
  data() {
    return {
      loading: true,
      showSearch: true,
      total: 0,
      dataList: [],
      queryParams: {
        pageNum: 0,
        pageSize: 10,
        brname: null,
        zyh: null,
        kdksname: '所有',
        doctorname: null,
        fymName: null,
        startDate: getYesterdayFirstSecondStr(),
        endDate: getTodayLastSecondStr(),
      },
      selectedData: {
        dept: null,
        doctor: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      gdjclist(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.kdksname = this.selectedData.dept
      this.queryParams.doctorname = this.selectedData.doctor
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 0,
        pageSize: 10,
        brname: null,
        kdksname: '所有',
        doctorname: null,
        fymName: null,
        startDate: getYesterdayFirstSecondStr(),
        endDate: getTodayLastSecondStr(),
      }
      this.selectedData.dept = '所有'
      this.selectedData.doctor = null
      this.handleQuery();
    },
    handleExport() {
      this.download('gksz/wgjl/exportGdjc', {
        ...this.queryParams
      }, `过度检查违规记录_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style>
</style>
