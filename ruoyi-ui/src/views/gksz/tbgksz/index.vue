<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="bzmlList"
              highlight-current-row
              @row-click="getTbList">
      <el-table-column label="病种编码" align="center" prop="bzbm" />
      <el-table-column label="病种名称" align="center" prop="bzmc" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes=pageSizes
      @pagination="getList"
    />

    <br>

    <el-button type="primary" icon="el-icon-plus" size="mini" @click="selectlb" v-hasPermi="['system:example:add']">新增类别</el-button>
    <el-button type="primary" icon="el-icon-plus" size="mini" @click="selectyp" v-hasPermi="['system:example:add']">新增药品</el-button>
    <el-button type="primary" icon="el-icon-plus" size="mini" @click="selectzl" v-hasPermi="['system:example:add']">新增诊疗</el-button>


    <el-table  v-loading="loading" :data="tbyylList">
      <el-table-column label="类别" align="center" prop="type" />
      <el-table-column show-overflow-tooltip width="200px" label="项目名称" align="center" prop="xmmc" />
      <el-table-column label="使用次数" align="center" prop="usecs" />
      <el-table-column label="使用数量" align="center" prop="usesl" />
      <el-table-column label="月允许用量" align="center" prop="monthsl" />
      <el-table-column label="年允许用量" align="center" prop="yearsl" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="tbtotal>0"
      :total="tbtotal"
      :page.sync="tbqueryParams.pageNum"
      :limit.sync="tbqueryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog title="新增类别" :visible.sync="lbopen" width="800px" append-to-body >
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="类别编号" prop="yplsh">
          <el-input
            v-model="queryParams.yplsh"
            placeholder="请输入类别编号"
            clearable
          />
        </el-form-item>
        <el-form-item label="类别名称" prop="tym">
          <el-input
            v-model="queryParams.tym"
            placeholder="请输入类别名称"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="selectlb">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetlb">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table highlight-current-row :data="lbList"  @row-click="setRow">>
        <el-table-column label="类别编号" align="center" prop="yplsh" />
        <el-table-column label="类别名称" align="center" prop="tym" />
      </el-table>
      <pagination
        v-show="lbtotal>0"
        :total="lbtotal"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="selectlb"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="新增药品" :visible.sync="ypopen" width="800px" append-to-body >
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="类别编号" prop="yplsh">
          <el-input
            v-model="queryParams.yplsh"
            placeholder="请输入类别编号"
            clearable
          />
        </el-form-item>
        <el-form-item label="类别名称" prop="tym">
          <el-input
            v-model="queryParams.tym"
            placeholder="请输入类别名称"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="selectyp">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetyp">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table highlight-current-row :data="ypList" @row-click="setRow">>
        <el-table-column label="类别编号" align="center" prop="yplsh" />
        <el-table-column label="类别名称" align="center" prop="tym" />
      </el-table>
      <pagination
        v-show="yptotal>0"
        :total="yptotal"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="selectyp"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="新增诊疗" :visible.sync="zlopen" width="800px" append-to-body>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="类别编号" prop="yplsh">
          <el-input
            v-model="queryParams.yplsh"
            placeholder="请输入类别编号"
            clearable
          />
        </el-form-item>
        <el-form-item label="类别名称" prop="tym">
          <el-input
            v-model="queryParams.tym"
            placeholder="请输入类别名称"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="selectzl">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetzl">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table highlight-current-row :data="zlList" @row-click="setRow">>
        <el-table-column label="类别编号" align="center" prop="yplsh" />
        <el-table-column label="类别名称" align="center" prop="tym" />
      </el-table>
      <pagination
        v-show="zltotal>0"
        :total="zltotal"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="selectzl"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listBzmltb } from "@/api/mlcx/bzml";
import { getka09,findUsetymList,findUseListzlxm } from "@/api/mlcx/ypml";
import { listTbyyl,addTbyyl,delTbyyl } from "@/api/gksz/tbyyl";

export default {
  name: "Bzml",
  data() {
    return {
      pageSizes:[5],
      // 遮罩层
      loading: true,
      tbloading: true,
      // 总条数
      total: 0,
      tbtotal: 0,
      lbtotal: 0,
      yptotal: 0,
      zltotal: 0,
      // 病种目录表格数据
      bzmlList: [],
      tbyylList: [],
      lbList: [],
      ypList: [],
      zlList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        bzmc: null,
        zjm: null,
        bzfl: null,
        tjm: null,
        jmbzfl: null,
        sybzfl: null,
        yplsh:null,
        tym:null
      },
      tbqueryParams: {
        pageNum: 1,
        pageSize: 5,
        id:null,
        bzbh:null,
      },
      currentRow:null,
      lbopen:false,
      ypopen:false,
      zlopen:false,
      addRow:null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询病种目录列表 */
    getList() {
      this.loading = true;
      listBzmltb(this.queryParams).then(response => {
        this.bzmlList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getTbList(row) {
      this.currentRow = row
      this.tbqueryParams.bzbh = row.bzbm
      this.tbloading = true;
      listTbyyl(this.tbqueryParams).then(response => {
        this.tbyylList = response.rows;
        this.tbtotal = response.total;
        this.tbloading = false;
      });
    },
    selectlb() {
      if (this.currentRow == null) {
        this.$modal.msgWarning("请选择病种");
        return
      }
      this.lbopen = true
      getka09(this.queryParams).then(response => {
        this.lbList = response.rows
        this.lbtotal = response.total;
      });
    },
    selectyp() {
      if (this.currentRow == null) {
        this.$modal.msgWarning("请选择病种");
        return
      }
      this.ypopen = true
      console.log(this.queryParams)
      findUsetymList(this.queryParams).then(response => {
        this.ypList = response.rows
        this.yptotal = response.total;
      });
    },
    selectzl(){
      if (this.currentRow == null) {
        this.$modal.msgWarning("请选择病种");
        return
      }
      this.zlopen = true
      findUseListzlxm(this.queryParams).then(response => {
        this.zlList = response.rows
        this.zltotal = response.total;
      });
    },
    cancel() {
      this.lbopen = false
      this.ypopen = false
      this.zlopen = false
      this.addRow = null
    },
    submitForm(){
      console.log(this.currentRow)
      addTbyyl({
        bzbh:this.currentRow.bzbm,
        bzmc:this.currentRow.bzmc,
        type:this.addRow.bz,
        xmmc:this.addRow.tym,
        usecs:0,
        usesl:0,
        monthsl:0,
        yearsl:0
      }).then(response => {
        this.$modal.msgSuccess("新增成功");
        this.open = false;
        this.getTbList(this.currentRow);
      });
      this.lbopen = false
      this.ypopen = false
      this.zlopen = false
      this.addRow = null
    },
    resetlb() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 5,
        bzmc: null,
        zjm: null,
        bzfl: null,
        tjm: null,
        jmbzfl: null,
        sybzfl: null,
        yplsh:null,
        tym:null
      }
      this.selectlb()
    },
    resetyp() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 5,
        bzmc: null,
        zjm: null,
        bzfl: null,
        tjm: null,
        jmbzfl: null,
        sybzfl: null,
        yplsh:null,
        tym:null
      }
      this.selectyp()
    },
    resetzl() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 5,
        bzmc: null,
        zjm: null,
        bzfl: null,
        tjm: null,
        jmbzfl: null,
        sybzfl: null,
        yplsh:null,
        tym:null
      }
      this.selectzl()
    },
    setRow(row) {
      this.addRow = row
    },
    handleDelete(row) {
      console.log(row)
      const ids = row.id
      this.$modal.confirm('是否确认删除该条记录').then(function() {
        return delTbyyl(ids);
      }).then(() => {
        this.getTbList(this.currentRow);
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
