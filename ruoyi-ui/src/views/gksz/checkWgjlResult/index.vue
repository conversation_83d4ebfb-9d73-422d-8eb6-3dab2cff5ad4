<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">


      <el-form-item label="住院号" prop="zyh">
        <el-input
          v-model="queryParams.zyh"
          placeholder="请输入住院号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="科室" prop="dept">
        <el-select v-model="queryParams.dept"
                   @change="getDoctorByDept(queryParams.dept)"
                   filterable
                   placeholder="科室"
                   clearable>
          <el-option v-for="(item,index) in deptList" :value="item.hDeptName" :key="index" :label="item.hDeptName" />
        </el-select>
      </el-form-item>

      <el-form-item label="医生" prop="doctorName">
        <el-select v-model="queryParams.doctor"
                   filterable
                   placeholder="医生"
                   clearable>
          <el-option v-for="(item,index) in doctorList" :value="item.nickName" :key="index" :label="item.nickName" />
        </el-select>
      </el-form-item>

      <el-form-item label="发送时间" prop="sendDate">
        <el-date-picker
          v-model="queryParams.sendDateStart"
          type="datetime"
          placeholder="请选择开始日期"
          default-time="['00:00:00']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="queryParams.sendDateEnd"
          type="datetime"
          placeholder="请选择截至日期"
          default-time="['23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>


    <el-table border v-loading="loading" :data="resultList" @selection-change="handleSelectionChange">
      <el-table-column width="150px" show-overflow-tooltip label="科室" align="left" prop="dept" />
      <el-table-column width="150px" show-overflow-tooltip label="住院号" align="left" prop="zyh" />
      <el-table-column width="150px" show-overflow-tooltip label="医生" align="left" prop="doctor" />
      <el-table-column show-overflow-tooltip width="200px"  label="违规类型" align="left" prop="wgtype" />
      <el-table-column show-overflow-tooltip width="400px" label="违规原因" align="left" prop="wgyy" />
      <el-table-column show-overflow-tooltip width="400px"  label="检查结果" align="left" prop="jcjg" />
      <el-table-column label="扣分" align="left" prop="score" />
      <el-table-column label="发送日期" align="left" prop="sendDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.sendDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listResult } from "@/api/gksz/checkWgjlResult";
import { selectDoctorByHDeptName } from "@/api/system/user";
import { selectHDeptNameList } from "@/api/system/hdept";
import {parseTime} from "../../../utils/ruoyi";

export default {
  name: "checkWgjlResult",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 违规检查结果表格数据
      resultList: [],
      deptList: [],
      doctorList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dept: null,
        zyh: null,
        doctor: null,
        wgtype: null,
        wgyy: null,
        jcjg: null,
        score: null,
        wgjlId: null,
        sendDateStart: null,
        sendDateEnd: null,
      },
    };
  },
  created() {
    this.getList();
    this.getDeptList()
  },
  methods: {
    parseTime,
    async getDeptList() {
      const res = await selectHDeptNameList()
      if (res.code == 200) {
        this.deptList = res.rows
      }
    },
    async getDoctorByDept(deptId) {
      this.queryParams.doctor = null
      this.doctorList = []
      if (!deptId) {
        return
      }
      const res = await selectDoctorByHDeptName(deptId)
      if (res.code == 200) {
        this.doctorList = res.rows
      }
    },
    /** 查询违规检查结果列表 */
    getList() {
      this.loading = true;
      listResult(this.queryParams).then(response => {
        this.resultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          dept: null,
          zyh: null,
          doctor: null,
          wgtype: null,
          wgyy: null,
          jcjg: null,
          score: null,
          wgjlId: null
      },
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('gksz/wgjlcheck/result/export', {
        ...this.queryParams
      }, `违规记录检查结果_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
