<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="药品编码" prop="ypbm">
        <el-input
          v-model="queryParams.ypbm"
          placeholder="请输入药品编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="通用名" prop="tym">
        <el-input
          v-model="queryParams.tym"
          placeholder="请输入通用名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名" prop="spm">
        <el-input
          v-model="queryParams.spm"
          placeholder="请输入商品名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mlcx:ypml:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ypmlList" @selection-change="handleSelectionChange">
      <el-table-column label="药品编码" align="center" prop="ypbm" show-overflow-tooltip width="120px"/>
      <el-table-column label="通用名" align="center" prop="tym" show-overflow-tooltip width="120px"/>
      <el-table-column label="类别代码" align="center" prop="lbdm" />
      <el-table-column label="处方药标志" align="center" prop="cfybz" />
      <el-table-column label="费用等级" align="center" prop="ylfydj" />
      <el-table-column label="批发价" align="center" prop="pfj" />
      <el-table-column label="标准单价" align="center" prop="ylbzdj" />
      <el-table-column label="自付比例" align="center" prop="ylzfbl" />
      <el-table-column label="剂型" align="center" prop="jx" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listYpml, getYpml, delYpml, addYpml, updateYpml } from "@/api/mlcx/ypml";

export default {
  name: "Ypml",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 药品目录表格数据
      ypmlList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ypbm: null,
        tym: null,
        tymzjm: null,
        spm: null,
        spmzjm: null,
        ywm: null,
        lbdm: null,
        cfybz: null,
        ylfydj: null,
        gsfydj: null,
        syfydj: null,
        pfj: null,
        ylbzdj: null,
        gsbzdj: null,
        sybzdj: null,
        ylzfbl: null,
        gszfbl: null,
        syzfbl: null,
        jx: null,
        bzsl: null,
        bzdw: null,
        hl: null,
        hldw: null,
        rl: null,
        rldw: null,
        gmp: null,
        ycmc: null,
        ypxjfs: null,
        bgsj: null,
        tqfydj: null,
        tqzfbl: null,
        tqbzdj: null,
        bz: null,
        cjzfbl: null,
        cjbzdj: null,
        cjfydj: null,
        wsssybz: null,
        xetsybz: null,
        xmzsybz: null,
        jcybz: null,
        zzsjbz: null,
        zzsjsbjg: null,
        bzdj: null,
        zfbl: null,
        fydj: null,
        gsfzqjbj: null,
        gskfxmbj: null,
        gsfpgxxmbl: null,
        jbsj: null,
        tqxmdj: null,
        starttime: null,
        endtime: null,
        cCoId: null,
        cJx: null,
        cPym: null,
        changeMask: null,
        fylb: null,
        bzsm: null,
        ycbm: null,
        flm: null,
        flypxh: null,
        xpmm: null,
        jxm: null,
        ggm: null,
        zlm: null,
        bzm: null,
        tqxmbz: null,
        sypc: null,
        ypyf: null,
        ypgg: null,
        yxbsm: null,
        cBxblXjyy: null,
        cBxblZxzwsy: null,
        cBxblXzwsy: null,
        cGsfydj: null,
        cSyfydj: null,
        cGszfbl: null,
        cSyzfbl: null,
        cHyfydj: null,
        cCwbsm: null,
        cRtbsm: null,
        cMzbsm: null,
        cJcbsm: null,
        cZzbsm: null,
        cZzsbjg: null,
        wjbm: null,
        cGjypdm: null,
        cZcjx: null,
        cZcgg: null,
        cSjgg: null,
        cBzcz: null,
        cZxbzsl: null,
        cZxbzdw: null,
        cZxzjdw: null,
        cPzwh: null,
        cYpbwm: null,
        cYcmc1: null,
        cGxfl: null,
        cYckly: null,
        cYczly: null,
        cYybw: null,
        cPzff: null,
        cXwygj: null,
        cGnyzz: null,
        cYfyyl: null,
        gjypdm: null,
        mcyl: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询药品目录列表 */
    getList() {
      this.loading = true;
      listYpml(this.queryParams).then(response => {
        this.ypmlList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        yplsh: null,
        ypbm: null,
        tym: null,
        tymzjm: null,
        spm: null,
        spmzjm: null,
        ywm: null,
        lbdm: null,
        cfybz: null,
        ylfydj: null,
        gsfydj: null,
        syfydj: null,
        pfj: null,
        ylbzdj: null,
        gsbzdj: null,
        sybzdj: null,
        ylzfbl: null,
        gszfbl: null,
        syzfbl: null,
        jx: null,
        bzsl: null,
        bzdw: null,
        hl: null,
        hldw: null,
        rl: null,
        rldw: null,
        gmp: null,
        ycmc: null,
        ypxjfs: null,
        bgsj: null,
        tqfydj: null,
        tqzfbl: null,
        tqbzdj: null,
        bz: null,
        cjzfbl: null,
        cjbzdj: null,
        cjfydj: null,
        wsssybz: null,
        xetsybz: null,
        xmzsybz: null,
        jcybz: null,
        zzsjbz: null,
        zzsjsbjg: null,
        bzdj: null,
        zfbl: null,
        fydj: null,
        gsfzqjbj: null,
        gskfxmbj: null,
        gsfpgxxmbl: null,
        jbsj: null,
        tqxmdj: null,
        starttime: null,
        endtime: null,
        cCoId: null,
        cJx: null,
        cPym: null,
        changeMask: null,
        fylb: null,
        bzsm: null,
        ycbm: null,
        flm: null,
        flypxh: null,
        xpmm: null,
        jxm: null,
        ggm: null,
        zlm: null,
        bzm: null,
        tqxmbz: null,
        sypc: null,
        ypyf: null,
        ypgg: null,
        yxbsm: null,
        cBxblXjyy: null,
        cBxblZxzwsy: null,
        cBxblXzwsy: null,
        cGsfydj: null,
        cSyfydj: null,
        cGszfbl: null,
        cSyzfbl: null,
        cHyfydj: null,
        cCwbsm: null,
        cRtbsm: null,
        cMzbsm: null,
        cJcbsm: null,
        cZzbsm: null,
        cZzsbjg: null,
        wjbm: null,
        cGjypdm: null,
        cZcjx: null,
        cZcgg: null,
        cSjgg: null,
        cBzcz: null,
        cZxbzsl: null,
        cZxbzdw: null,
        cZxzjdw: null,
        cPzwh: null,
        cYpbwm: null,
        cYcmc1: null,
        cGxfl: null,
        cYckly: null,
        cYczly: null,
        cYybw: null,
        cPzff: null,
        cXwygj: null,
        cGnyzz: null,
        cYfyyl: null,
        gjypdm: null,
        mcyl: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.yplsh)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加药品目录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const yplsh = row.yplsh || this.ids
      getYpml(yplsh).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改药品目录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.yplsh != null) {
            updateYpml(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addYpml(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const yplshs = row.yplsh || this.ids;
      this.$modal.confirm('是否确认删除药品目录编号为"' + yplshs + '"的数据项？').then(function() {
        return delYpml(yplshs);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mlcx/ypml/export', {
        ...this.queryParams
      }, `ypml_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style>
td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .wrap {
            width: 450px;

        }

        .wrap40 {
            width: 40px;

        }

        .fixed-table-toolbar {
            display: none;
        }
</style>
