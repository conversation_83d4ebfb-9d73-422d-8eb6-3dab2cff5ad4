<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="病种名称" prop="bzmc">
        <el-input
          v-model="queryParams.bzmc"
          placeholder="请输入病种名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助记码" prop="zjm">
        <el-input
          v-model="queryParams.zjm"
          placeholder="请输入助记码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mlcx:bzml:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bzmlList" @selection-change="handleSelectionChange">
      <el-table-column label="病种编码" align="center" prop="bzbm" />
      <el-table-column label="病种名称" align="center" prop="bzmc" />
      <el-table-column label="助记码" align="center" prop="zjm" />
      <el-table-column label="病种分类" align="center" prop="bzfl" />
      <el-table-column label="统计码" align="center" prop="tjm" />
      <el-table-column label="居民病种分类" align="center" prop="jmbzfl" />
      <el-table-column label="社会机构编码" align="center" prop="sybzfl" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listBzml, getBzml, delBzml, addBzml, updateBzml } from "@/api/mlcx/bzml";

export default {
  name: "Bzml",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 病种目录表格数据
      bzmlList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bzmc: null,
        zjm: null,
        bzfl: null,
        tjm: null,
        jmbzfl: null,
        sybzfl: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询病种目录列表 */
    getList() {
      this.loading = true;
      listBzml(this.queryParams).then(response => {
        this.bzmlList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        bzbm: null,
        bzmc: null,
        zjm: null,
        bzfl: null,
        tjm: null,
        jmbzfl: null,
        sybzfl: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.bzbm)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加病种目录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const bzbm = row.bzbm || this.ids
      getBzml(bzbm).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改病种目录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.bzbm != null) {
            updateBzml(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBzml(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const bzbms = row.bzbm || this.ids;
      this.$modal.confirm('是否确认删除病种目录编号为"' + bzbms + '"的数据项？').then(function() {
        return delBzml(bzbms);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mlcx/bzml/export', {
        ...this.queryParams
      }, `bzml_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
