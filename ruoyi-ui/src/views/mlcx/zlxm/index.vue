<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目编码" prop="xmbm">
        <el-input
          v-model="queryParams.xmbm"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="xmmc">
        <el-input
          v-model="queryParams.xmmc"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="助记码" prop="zjm">
        <el-input
          v-model="queryParams.zjm"
          placeholder="请输入助记码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:zlxm:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="zlxmList" @selection-change="handleSelectionChange">
      <el-table-column label="项目编码" align="center" prop="xmbm" show-overflow-tooltip width="200px"/>
      <el-table-column label="项目名称" align="center" prop="xmmc" show-overflow-tooltip width="200px"/>
      <el-table-column label="单价" align="center" prop="ylbzj" />

      <el-table-column label="单位" align="center" prop="dw" />
      <el-table-column label="费用等级" align="center" prop="ylfydj" />

      <el-table-column label="医疗自付比例" align="center" prop="ylzfbl" />

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listZlxm, getZlxm, delZlxm, addZlxm, updateZlxm } from "@/api/mlcx/zlxm";

export default {
  name: "Zlxm",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 诊疗目录表格数据
      zlxmList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        lbdm1: null,
        lbdm2: null,
        lbdm3: null,
        lbdm4: null,
        xmbm: null,
        xmmc: null,
        zjm: null,
        tpj: null,
        ylbzj: null,
        gsbzj: null,
        sybzj: null,
        dw: null,
        ylfydj: null,
        gsfydj: null,
        syfydj: null,
        ylzfbl: null,
        gszfbl: null,
        syzfbl: null,
        txbl: null,
        xjfs: null,
        lsf: null,
        bz: null,
        bgsj: null,
        tpxmbz: null,
        tqfydj: null,
        tqzfbl: null,
        tqbzdj: null,
        xmnh: null,
        cwnr: null,
        cjzfbl: null,
        cjbzdj: null,
        cjfydj: null,
        bzj: null,
        zzbl: null,
        fydj: null,
        gsfzqjbj: null,
        gskfxmbj: null,
        gsfpgxxmbj: null,
        gsktfbj: null,
        tsytbj: null,
        nhycxhc: null,
        jjsm: null,
        zgcfjl: null,
        zgcfyl15: null,
        zgcfyl20: null,
        zgcfcb: null,
        zgcfzf: null,
        jmcfjl: null,
        jmcfyl15: null,
        jmcfyl20: null,
        jmcfcb: null,
        jmcfzf: null,
        gscfjl: null,
        gscfcb: null,
        gscfzf: null,
        sycfjl: null,
        sycfyl15: null,
        sycfyl20: null,
        sycfcb: null,
        sycfzf: null,
        tqcfjl: null,
        tqcfyl15: null,
        tqcfyl20: null,
        tqcfcb: null,
        tqcfzf: null,
        zzsj: null,
        jbsj: null,
        zgdebxbz: null,
        jmdebxbz: null,
        ylggxmbj: null,
        gjxmdm: null,
        cGjxmfl: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询诊疗目录列表 */
    getList() {
      this.loading = true;
      listZlxm(this.queryParams).then(response => {
        this.zlxmList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        lbdm1: null,
        lbdm2: null,
        lbdm3: null,
        lbdm4: null,
        xmlsh: null,
        xmbm: null,
        xmmc: null,
        zjm: null,
        tpj: null,
        ylbzj: null,
        gsbzj: null,
        sybzj: null,
        dw: null,
        ylfydj: null,
        gsfydj: null,
        syfydj: null,
        ylzfbl: null,
        gszfbl: null,
        syzfbl: null,
        txbl: null,
        xjfs: null,
        lsf: null,
        bz: null,
        bgsj: null,
        tpxmbz: null,
        tqfydj: null,
        tqzfbl: null,
        tqbzdj: null,
        xmnh: null,
        cwnr: null,
        cjzfbl: null,
        cjbzdj: null,
        cjfydj: null,
        bzj: null,
        zzbl: null,
        fydj: null,
        gsfzqjbj: null,
        gskfxmbj: null,
        gsfpgxxmbj: null,
        gsktfbj: null,
        tsytbj: null,
        nhycxhc: null,
        jjsm: null,
        zgcfjl: null,
        zgcfyl15: null,
        zgcfyl20: null,
        zgcfcb: null,
        zgcfzf: null,
        jmcfjl: null,
        jmcfyl15: null,
        jmcfyl20: null,
        jmcfcb: null,
        jmcfzf: null,
        gscfjl: null,
        gscfcb: null,
        gscfzf: null,
        sycfjl: null,
        sycfyl15: null,
        sycfyl20: null,
        sycfcb: null,
        sycfzf: null,
        tqcfjl: null,
        tqcfyl15: null,
        tqcfyl20: null,
        tqcfcb: null,
        tqcfzf: null,
        zzsj: null,
        jbsj: null,
        zgdebxbz: null,
        jmdebxbz: null,
        ylggxmbj: null,
        gjxmdm: null,
        cGjxmfl: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.xmlsh)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加诊疗目录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const xmlsh = row.xmlsh || this.ids
      getZlxm(xmlsh).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改诊疗目录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.xmlsh != null) {
            updateZlxm(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZlxm(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const xmlshs = row.xmlsh || this.ids;
      this.$modal.confirm('是否确认删除诊疗目录编号为"' + xmlshs + '"的数据项？').then(function() {
        return delZlxm(xmlshs);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/zlxm/export', {
        ...this.queryParams
      }, `zlxm_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
