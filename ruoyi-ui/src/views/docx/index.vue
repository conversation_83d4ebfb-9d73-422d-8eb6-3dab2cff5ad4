<template>
  <div class="app-container home" style="display: flex">
    <div style="flex: 65;padding-right: 20px">
      <TinyEditor @input="getContent" :value="con" :patient-name="kkmxInfo.brname ? kkmxInfo.brname : ''"></TinyEditor>
    </div>
    <div style="flex: 35">
      <div>
        <el-button type="primary"  @click="handleSubmit">提交申诉</el-button>
      </div>
      <el-tabs style="margin-top: 20px" type="border-card">
        <el-tab-pane label="扣款详情">
          <div v-if="kkmxInfo">
            <ul>
              <li><span>病人：{{ kkmxInfo.brname }}</span></li>
              <li><span>违规项目：{{ kkmxInfo.wgxm }}</span></li>
              <li><span v-if="kkmxInfo.cydate">出院日期：{{ kkmxInfo.cydate }}</span></li>
              <li><span v-if="kkmxInfo">出院诊断：{{ kkmxInfo.cyzd }}</span></li>
            </ul>
          </div>
        </el-tab-pane>
        <el-tab-pane label="病程记录">
          <div>
            <span v-for="item in blnrArray">{{ item }}<br><br></span>
          </div>
        </el-tab-pane>
        <el-tab-pane label="诊断信息">
          <div>
            <div v-for="item in brzdxx">
              <span style="width: 30%">{{ item.zdmc }}</span> |
              <span>{{ item.jbbm }}</span>
              <br><br>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-dialog
      title="出现了同名病人，请选择是哪一个病人"
      :visible.sync="patientChooseDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      center>
      <div>
        <el-row :gutter="20">
          <el-col :span="6" v-for="(item, index) in brxxArray">
            <div>
              <el-descriptions title="病人信息" :column="1">
                <el-descriptions-item label="姓名">{{ item.name }}</el-descriptions-item>
                <el-descriptions-item label="入院日期">{{ item.rydate }}</el-descriptions-item>
                <el-descriptions-item label="诊断" v-if="item.bzname">{{ item.bzname }}</el-descriptions-item>
                <el-descriptions-item label="出院日期" v-if="item.cydate">{{ item.cydate }}</el-descriptions-item>
              </el-descriptions>
              <el-button type="primary" @click="choosePatient(index)">选择</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer"></span>
    </el-dialog>
  </div>
</template>
<script>
import TinyEditor from '@/components/TinyEditor/index.vue'
import {blxxList} from '@/api/drg/blxx'
import {getBrxxByKkxx} from '@/api/mlcx/brxx'
import {appeal, getKkmx} from '@/api/gksz/kkmx'
import {selectBrzdxxByOnePatient} from '@/api/drg/brzdxx'

export default {
  name: 'Index',
  data() {
    return {
      con: '',
      blnrArray: [],
      kkmxInfo: {},
      brxx: {},
      brzdxx: [],
      brxxArray: [],
      patientChooseDialogVisible: false
    }
  },
  methods: {
    async handleSubmit() {
      if (this.con) {
        const appealer = this.$store.state.user.nickName
        const appealContent = this.con
        const data = {
          id: this.kkmxInfo.id,
          appealer: appealer,
          appealContent: appealContent
        }
        const res = await appeal(data)
        if (res.code == 200) {
          this.$modal.msgSuccess("提交成功！")
        }
      } else {
        this.$modal.msgWarning("请输入申诉内容！")
      }

    },
    getContent(val) {
      this.con = val
    },
    async initPatient() {
      //获取到病人的基本信息
      const brxxRes = await getBrxxByKkxx({
        name: this.kkmxInfo.brname,
        rydate: this.kkmxInfo.rydate
      })
      this.brxxArray = brxxRes.data
      //如果查询到多个病人，则需要让用户选择
      if (brxxRes.data.length > 1) {
        //此即为同一天出现了同名的病人入院，该特殊情况需要让用户选择是哪个病人
        this.patientChooseDialogVisible = true
      } else {
        this.brxx = brxxRes.data[0]
        await this.initData()
      }
    },
    async initData() {
      //获取病人的诊断信息
      const brzdxxRes = await selectBrzdxxByOnePatient({
        brbs: this.brxx.jzh
      })
      this.brzdxx = brzdxxRes.data

      //获取到病人的病历信息
      blxxList({
        jzh: this.brxx.jzh,
        blname: '病程记录'
      }).then(res => {
        let rec = res.rows
        this.blnrArray = rec.map(item => item.blnr)
      })
    },
    choosePatient(val) {
      this.brxx = this.brxxArray[val]
      this.patientChooseDialogVisible = false
      this.initData()
    },
    async init() {
      if (this.$route.query.id) {
        const kkxxId = this.$route.query.id
        const res = await getKkmx(kkxxId)
        if (res.code == 200) {
          this.kkmxInfo = res.data
          this.con = this.kkmxInfo.appealContent
          this.initPatient()
        } else {
          this.$modal.msgError("获取扣款信息失败！")
        }
      } else {
        this.$modal.msgError("未携带参数！")
      }
    }
  },
  created() {
    this.init()
  },
  components: {
    TinyEditor
  }
}
</script>
