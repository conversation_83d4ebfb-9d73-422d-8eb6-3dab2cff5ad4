<template>
<div class="main"   id="pdfDom">

  <el-dialog title="打回" :visible.sync="showDhForm" width="900px" append-to-body>

    <el-input
      rows="5"
      type="textarea"
      placeholder="请输入打回建议"
      v-model="advice">
    </el-input>
    <div style="margin-top: 20px">
      <el-button plain @click="setAdvice('主要诊断调整')">主要诊断调整</el-button>
      <el-button plain @click="setAdvice('其他诊断调整')">其他诊断调整</el-button>
      <el-button plain @click="setAdvice('主要手术操作调整')">主要手术操作调整</el-button>
      <el-button plain @click="setAdvice('其他手术操作调整')">其他手术操作调整</el-button>
      <el-button plain @click="setAdvice('病理调整')">病理调整</el-button>
      <el-button plain @click="setAdvice('外因调整')">外因调整</el-button>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="daHui">打 回</el-button>
    </div>
  </el-dialog>

  <div style="height: 5px; margin-left: 10px; display: flex; justify-content: space-between; align-items: center;">
    <el-button style="height:45px;margin-top: -5px;width: 100px;" type="danger" plain @click="DhForm">打回</el-button>
    <el-button style="height:45px;margin-top: -5px;width: 100px; margin-right: 20px;" type="primary" plain @click="goBack">返回</el-button>
  </div>

  <div class="top">
    <div>结算ID:{{jsid}}</div>
    <div>病案号:{{bah}}</div>
    <div>本次就诊唯一编号:{{jzh}}</div>
  </div>
  <div class="row2" v-if="!hid">
    <div class="jbxx">
      <div class="left">
        <div style="text-indent: 5em;color: #000;font-weight: bold" v-if="jsxx.psnName!=null">{{jsxx.psnName==null?"":jsxx.psnName}}  {{jsxx.fixmedinsName==null?"":jsxx.fixmedinsName}}</div>
        <div style="text-indent: 5em">性别: {{jsxx.gend==1?'男':jsxx.gend==2?'女':""}}, 年龄: {{jsxx.age==null?"":jsxx.age}}岁</div>
        <div style="text-indent: 5em">国籍:{{syjl.gj==null?"":syjl.gj}} 民族:{{syjl.mz==null?"":syjl.mz}}</div>
        <div style="text-indent: 5em">患者证件类型:{{jsxx.psnCertType==null?"":jsxx.psnCertType}} 患者证件号码:{{jsxx.certno==null?"":jsxx.certno}}</div>
        <div><b>单<span style="color: #f1f1f1">占</span>位：</b>{{jsxx.empName==null?"":jsxx.empName}} 职业:{{syjl.zy==null?"":syjl.zy}}</div>
        <div style="text-indent: 5em">单位地址:{{syjl.gzdwjdz==null?"":syjl.gzdwjdz}} 电话:{{syjl.dwdh==null?"":syjl.dwdh}}</div>
        <div><b>联系人：</b>联系人:{{syjl.lxrxm==null?"":syjl.lxrxm}} 关系:{{syjl.gx==null?"":syjl.gx}} 电话:{{syjl.dh==null?"":syjl.dh}}</div>
        <div><b>地<span style="color: #f1f1f1">位</span>址：</b>联系地址:{{syjl.xzz==null?"":syjl.xzz}}</div>
        <div style="text-indent: 5em">现居地:{{syjl.xzz==null?"":syjl.xzz}}</div>
        <div style="text-indent: 5em">单位所在地:{{syjl.gzdwjdz==null?"":syjl.gzdwjdz}}</div>
      </div>
      <div class="right">
        <div><b>医保信息：</b>医保类型:{{syjl.ylfkfs==null?"":syjl.ylfkfs}} 特殊人员类型:{{jsxx.psnType==null?"":jsxx.psnType}}</div>
        <div><b>新生儿信息：</b>出生体重:{{syjl.xsecstz==null?"":syjl.xsecstz}}g 入院体重:{{syjl.xserytz==null?"":jsxx.xserytz}}g</div>
        <div><b>主诊医师：</b>{{syjl.zyys==null?"":syjl.zyys}}</div>
        <div><b>住院信息：</b>入院时间:{{syjl.rysj==null?"":syjl.rysj}} 实际住院:{{syjl.sjzyts==null?"":syjl.sjzyts}} 住院科室:{{syjl.rykb==null?"":syjl.rykb}}</div>
      </div>
    </div>
    <div style="width: 100%; text-align: center;">
      <el-button style="background-color: #fff;border: #fff;" size="mini" @click="hidContent">收起基本信息</el-button>
    </div>
  </div>
  <div class="show" style="height: 80px; padding-top: 15px;" v-else>
    <div class="showContent">
      <div class="showRow1">{{jsxx.psnName}}  {{jsxx.fixmedinsName}}
        <el-button class="showBtn" size="mini" @click="showContent">展示基本信息</el-button>
      </div>
      <div class="showRow2">{{jsxx.gend==1?'男':'女'}}|  {{jsxx.age}}岁| 医保类型：{{syjl.ylfkfs}}|
        入院科别：{{syjl.rykb}}| 实际住院：{{syjl.sjzyts}}</div>
    </div>
  </div>
  <div class="row3">
    <el-button style="background-color: #fff;color: #000;border: #fff;"
               type="primary"  size="mini" @click="jsxxDownload">结算清单下载</el-button>
    <div class="jsqd"><b>结算清单</b></div>
    <div class="ybxx">
      <div class="tit2" style="margin-left: 40px">
        <div style="display: flex;width: 25%">
          <div class="yuan"></div>
          <div style="margin-left: 2px">医疗机构编码：</div>
        </div>
        <div style="display: flex;width: 25%">
          <div class="yuan"></div>
          <div style="margin-left: 2px">医保结算等级：</div>
        </div>
        <div style="display: flex;width: 25%">
          <div class="yuan"></div>
          <div style="margin-left: 2px">医保编号：</div>
        </div>
        <div style="display: flex;width: 25%">
          <div class="yuan"></div>
          <div style="margin-left: 2px">申报时间：</div>
        </div>
      </div>
      <div class="tit2 content">
        <div style="margin-left: 52px;">{{jsxx.fixmedinsCode}}</div>
        <div style="margin-left: 52px;"></div>
        <div style="margin-left: 52px;"></div>
        <div style="margin-left: 52px;"></div>
      </div>
    </div>
    <div class="tit">
      一、住 院 诊 疗 信 息
    </div>
<!--    住院信息    -->
    <div style="display: flex; justify-content: left;margin-left: 40px; margin-top: 15px">
      <div class="ract"></div>
      <div style="font-weight: bold;margin-left: 2px">住院信息</div>
    </div>
    <div style="display: flex;margin-top: 15px;margin-left: 40px;">
      <div style="display: flex;width: 20%">
        <div class="yuan"></div>
        <div style="font-weight: bold;margin-left: 2px">住院医疗类型：{{jsxx.medType}}</div>
      </div>
      <div style="width: 20%;margin-left: 10px">入院途径：{{syjl.rytj}}</div>
      <div style="width: 20%;margin-left: -5px">治疗类别：</div>
    </div>
    <div style="display: flex;margin-top: 15px;margin-left: 52px">
      <div style="width: 20%;font-weight: bold;">入院时间：{{syjl.rysj}}</div>
      <div style="width: 20%">入院科别：{{syjl.rykb}}</div>
      <div style="width: 20%">转院科别：{{syjl.zkkb}}</div>
    </div>
    <div style="display: flex;margin-top: 15px;margin-left: 52px">
      <div style="width: 20%;font-weight: bold;">出院时间：{{syjl.cysj}}</div>
      <div style="width: 20%">出院科别：{{syjl.cykb}}</div>
      <div style="width: 20%">实际住院：{{syjl.sjzyts}}</div>
    </div>
    <div style="display: flex;margin-top: 15px;margin-left: 52px">
      <div style="width: 35%">门（急）急诊诊断（西医诊断）：{{syjl.mzzd}}</div>
      <div style="width: 35%">疾病代码：{{syjl.jbbm}}</div>
    </div>
    <div style="display: flex;margin-top: 15px;margin-left: 52px">
      <div style="width: 35%">门（急）急诊诊断（中医诊断）：</div>
      <div style="width: 35%">疾病代码：</div>
    </div>


    <!--    诊疗信息    -->


    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div class="ract"></div>
      <div style="font-weight: bold;margin-left: 2px">诊断信息</div>
      <div style="font-size: 12px; margin-left: 15px">诊断代码计数：{{syzdxx.length}}</div>
<!--      <div style="width: 100px;height: 25px;text-align: center;border: #e7e7e7 2px solid; line-height: 25px;">西医诊断</div>-->
<!--      <div style="width: 100px;height: 25px;text-align: center;border: #e7e7e7 2px solid; line-height: 25px;">中医诊断</div>-->
    </div>
    <table class="zdxx">
      <tr  style="width: 100%;background-color: #f8f8f8;height: 30px;">
        <td></td>
        <td>出院诊断名称</td>
        <td>疾病代码</td>
        <td>入院病情</td>
      </tr>
      <tr  v-if="syzdxx.length == 0" style="width: 100%; background-color: #f8f8f8;height: 40px; line-height: 40px">
        <td colspan="4" style="text-align: center;border: #ccc solid 1px">暂时没有数据</td>
      </tr>
      <tr v-else style="width: 100%;height: 30px;" v-for="(item,index) in syzdxx">
        <td style="background-color: #f4f8f7;">{{index==0?'主要诊断':'其他诊断'}}</td>
        <td>{{item.zdmc}}</td>
        <td>{{item.jbbm}}</td>
        <td>{{item.rybq}}</td>
      </tr>
    </table>



    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div class="ract"></div>
      <div style="font-weight: bold;margin-left: 2px">手术及操作</div>
      <div style="font-size: 12px; margin-left: 15px">手术及操作代码计数：{{syssxx.length}}</div>
    </div>
    <table class="ssxx">
      <tr  style="width: 100%;background-color: #f8f8f8;height: 30px;">
        <td>手术及操作名称</td>
        <td>手术及操作代码</td>
        <td>手术及操作日期</td>
        <td>麻醉方式</td>
        <td>术者医师</td>
        <td>麻醉医师</td>
      </tr>
      <tr  v-if="syssxx.length == 0" style="width: 100%; background-color: #f8f8f8;height: 40px; line-height: 40px">
         <td colspan="6" style="text-align: center;border: #ccc solid 1px">暂时没有数据</td>
      </tr>
      <tr v-else style="width: 100%;height: 30px;" v-for="(item,index) in syssxx">
        <td>{{item.ssmc}}</td>
        <td>{{item.ssbm}}</td>
        <td>{{item.ssrq}}</td>
        <td>{{getAnstWay(item.mzfs)}}</td>
        <td>{{item.sz}}</td>
        <td>{{item.mzys}}</td>
      </tr>
    </table>

    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div style="width: 40%">呼吸机使用时间</div>
      <div>颅脑损伤患者昏迷时间：入院前：{{syjl.ryq_t}}天 {{syjl.ryq_xs}}小时 {{syjl.ryq_f}}分钟 入院后：{{syjl.ryh_t}}天 {{syjl.ryh_xs}}小时 {{syjl.ryh_f}}分钟</div>
    </div>


    <table class="ssxx">
      <tr  style="width: 100%;background-color: #f8f8f8;height: 30px;">
        <td>重症监护病房类型</td>
        <td>进重症监护室时间</td>
        <td>出重症监护室时间</td>
        <td>合计（小时）</td>
      </tr>
      <tr style="width: 100%; background-color: #f8f8f8;height: 40px; line-height: 40px">
        <td colspan="4" style="text-align: center;border: #ccc solid 1px">暂时没有数据</td>
      </tr>
<!--      <tr  style="width: 100%;height: 30px;" v-for="(item,index) in syssxx">-->
<!--        <td></td>-->
<!--        <td></td>-->
<!--        <td></td>-->
<!--        <td></td>-->
<!--      </tr>-->
    </table>


    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div style="width: 25%">输血品种：</div>
      <div style="width: 25%">输血量：</div>
      <div style="width: 25%">输血计量单位：</div>
    </div>

    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div style="width: 25%">特级护理天数：</div>
      <div style="width: 25%">Ⅰ级护理天数：</div>
      <div style="width: 25%">Ⅱ级护理天数:</div>
      <div style="width: 25%">Ⅲ级护理天数:</div>
    </div>


    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div class="ract"></div>
      <div style="font-weight: bold;margin-left: 2px">离院信息</div>
    </div>
    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div style="width: 25%">离院方法：{{syjl.lyfs}}</div>
      <div style="width: 25%"><input type="checkbox">出院31天再住院计划</div>
    </div>
    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div style="width: 25%">主诊医师：{{syjl.zyys}}</div>
        <div style="width: 25%">主诊医师代码：</div>
    </div>


    <div class="tit">
      二、医 疗 收 费 信 息
    </div>

    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div class="ract"></div>
      <div style="font-weight: bold;margin-left: 2px">结算清单</div>
    </div>


    <div style="display: flex;line-height: 65px; margin-left: 40px;text-align: center;
    font-size: 19px; font-weight: bold;
    margin-top: 25px;height: 65px; border: solid #ccc 1px;width: 93%">
      <div style="width: 50%;border-right: 1px solid #ccc;">基金支付 <span style="font-family: Consolas">￥{{jsxx.fundPaySumamt}}</span></div>
      <div style="width: 50%;">个人支付 <span style="font-family: Consolas">￥{{jsxx.psnPay}}</span></div>
    </div>

    <div style="display: flex; margin-left: 40px; margin-top: 25px">
      <div style="width: 30%">业务流水线：</div>
      <div style="width: 30%">结算开始时间-结算结算时间：{{jsxx.begndate}}-{{jsxx.setlTime}}</div>
    </div>


    <div class="sfxx">
      <div class="left" style="width: 50%">
        <div style="font-weight: bold">项目金额明细表</div>
        <table class="xmjemxb">
          <tr style="background-color: #f8f8f8;">
            <td>项目名称</td>
            <td>项目金额</td>
            <td>甲类（元）</td>
            <td>乙类（元）</td>
            <td>自费（元）</td>
            <td>其他（元）</td>
          </tr>
          <tr>
            <td>床位费</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>诊查费</td>
            <td>{{syjl.blzdf+syjl.syszdf+syjl.yxxzdf+syjl.lczdxmf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>检查费</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>化验费</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>治疗费</td>
            <td>{{syjl.fsszlxmf+syjl.wlzlf+syjl.sszlf+syjl.maf+syjl.ssf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>手术费</td>
            <td>{{syjl.ssf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>护理费</td>
            <td>{{syjl.hlf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>卫生材料费</td>
            <td>{{syjl.hcyyclf+syjl.yyclf+syjl.ycxyyclf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>西药费</td>
            <td>{{syjl.xyf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>中药饮片费</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>中成药费</td>
            <td>{{syjl.zcyf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>一般诊疗费</td>
            <td>{{syjl.zlczf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>挂号费</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>其他费</td>
            <td>{{syjl.qtf}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>-</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td>金额合计</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </table>
        <div style="display: flex;margin-top: 10px">
          <div style="width: 30%">医保支付方式：</div>
          <div style="width: 30%">医疗机构填写部门：</div>
          <div style="width: 30%">医疗机构填报人：</div>
        </div>
      </div>
      <div class="right" style="width: 50%">
        <div style="font-weight: bold">基金支付</div>
        <table>
          <tr>
            <td style="background-color: #f8f8f8;">基金支付类型</td>
            <td style="background-color: #f8f8f8;">金额（元）</td>
          </tr>
          <tr>
            <td>医保统筹基金支付</td>
            <td>{{jsxx.hifpPay}}</td>
          </tr>
          <tr>
            <td>其他支付（大病保险）</td>
            <td>{{jsxx.hifmiPay}}</td>
          </tr>
          <tr>
            <td>其他支付（医疗救助）</td>
            <td>{{jsxx.mafPay}}</td>
          </tr>
          <tr>
            <td>其他支付（公务员医疗补贴）</td>
            <td>{{jsxx.cvlservPay}}</td>
          </tr>
          <tr>
            <td>其他支付（大额补充）</td>
            <td>{{jsxx.hifobPay}}</td>
          </tr>
          <tr>
            <td>其他支付（企业补充）</td>
            <td>{{jsxx.hifesPay}}</td>
          </tr>
        </table>
        <div style="font-weight: bold;margin-top: 40px">个人支付</div>
        <table>
          <tr>
            <td style="background-color: #f8f8f8;">个人支付类型</td>
            <td style="background-color: #f8f8f8;">金额（元）</td>
          </tr>
          <tr>
            <td>个人自付</td>
            <td>{{jsxx.psnPay}}</td>
          </tr>
          <tr>
            <td>个人自费</td>
            <td>{{jsxx.fulamtOwnpayAmt}}</td>
          </tr>
          <tr>
            <td>个人账户支付</td>
            <td>{{jsxx.acctPay}}</td>
          </tr>
          <tr>
            <td>个人现金支付</td>
            <td>{{jsxx.cashPayamt}}</td>
          </tr>
        </table>
      </div>
    </div>

  </div>
</div>
</template>

<script>
import { listJsxx } from "@/api/jsxx/jsxx";
import { getdk, getFzPageIp, listSyjl } from '@/api/drg/syjl'
import { listBrzdxx, selectBrzdxxByOnePatient } from '@/api/drg/brzdxx'
import {listSsjl} from "@/api/drg/ssjl";
import getPdf from "@/utils/htmlToPdf.js";
import {updateBrxxShzt, listBrxx} from "@/api/mlcx/brxx";
import { getAnesthesiaMethod } from '@/utils/dict/EnumMapping'
export default {
  name: "index",
  data(){
    return {
      showDhForm:false,
      hid:false,
      jsid:null,
      bah:null,
      jzh:null,
      brid:null,
      zyid:null,
      brbs:null,
      advice:"",
      jsxxQueryParams: {
        pageNum: 1,
        pageSize: 10,
        setlId:null,
        mdtrtId: null,
        psnNo: null,
        psnName: null,
        psnCertType: null,
        certno: null,
        gend: null,
        naty: null,
        brdy: null,
        age: null,
        insutype: null,
        psnType: null,
        cvlservFlag: null,
        flxempeFlag: null,
        nwbFlag: null,
        insuOptins: null,
        empName: null,
        payLoc: null,
        fixmedinsCode: null,
        fixmedinsName: null,
        hospLv: null,
        fixmedinsPoolarea: null,
        lmtpricHospLv: null,
        dedcHospLv: null,
        begndate: null,
        enddate: null,
        setlTime: null,
        mdtrtCertType: null,
        medType: null,
        clrType: null,
        clrWay: null,
        clrOptins: null,
        medfeeSumamt: null,
        fulamtOwnpayAmt: null,
        overlmtSelfpay: null,
        preselfpayAmt: null,
        inscpScpAmt: null,
        actPayDedc: null,
        hifpPay: null,
        poolPropSelfpay: null,
        cvlservPay: null,
        hifesPay: null,
        hifmiPay: null,
        hifobPay: null,
        mafPay: null,
        othPay: null,
        fundPaySumamt: null,
        psnPay: null,
        acctPay: null,
        cashPayamt: null,
        balc: null,
        acctMulaidPay: null,
        medinsSetlId: null,
        refdSetlFlag: null,
        year: null,
        diseCodg: null,
        diseName: null,
        invono: null,
        opterId: null,
        opterName: null,
        optTime: null,
        hisJsdate: null,
        brid: null,
        zyid: null,
        jzid: null
      },
      basyQueryParams: {
        pageNum: 1,
        pageSize: 10,
        jzh: null,
        brbs: null,
        brid: null,
        zyid: null,
        username: null,
        ylfkfs: null,
        jkkh: null,
        zycs: null,
        bah: null,
        xm: null,
        xb: null,
        csrq: null,
        nl: null,
        gj: null,
        bzyzsnl: null,
        xsecstz: null,
        xserytz: null,
        csd: null,
        gg: null,
        mz: null,
        sfzh: null,
        zy: null,
        hy: null,
        xzz: null,
        dh: null,
        yb1: null,
        hkdz: null,
        yb2: null,
        gzdwjdz: null,
        dwdh: null,
        yb3: null,
        lxrxm: null,
        gx: null,
        dz: null,
        dh2: null,
        rytj: null,
        rysj: null,
        rysjs: null,
        rykb: null,
        rybf: null,
        zkkb: null,
        cysj: null,
        cysjs: null,
        cykb: null,
        cybf: null,
        sjzyts: null,
        mzzd: null,
        jbbm: null,
        zyzd: null,
        jbdm: null,
        rybq: null,
        qtzd1: null,
        jbdm1: null,
        rybq1: null,
        qtzd2: null,
        jbdm2: null,
        rybq2: null,
        qtzd3: null,
        jbdm3: null,
        rybq3: null,
        qtzd4: null,
        jbdm4: null,
        rybq4: null,
        qtzd5: null,
        jbdm5: null,
        rybq5: null,
        qtzd6: null,
        jbdm6: null,
        rybq6: null,
        qtzd7: null,
        jbdm7: null,
        rybq7: null,
        qtzd8: null,
        jbdm8: null,
        rybq8: null,
        qtzd9: null,
        jbdm9: null,
        rybq9: null,
        qtzd10: null,
        jbdm10: null,
        rybq10: null,
        qtzd11: null,
        jbdm11: null,
        rybq11: null,
        qtzd12: null,
        jbdm12: null,
        rybq12: null,
        qtzd13: null,
        jbdm13: null,
        rybq13: null,
        qtzd14: null,
        jbdm14: null,
        rybq14: null,
        qtzd15: null,
        jbdm15: null,
        rybq15: null,
        wbyy: null,
        h23: null,
        blzd: null,
        jbmm: null,
        blh: null,
        ywgm: null,
        gmyw: null,
        swhzsj: null,
        xx: null,
        rh: null,
        kzr: null,
        zrys: null,
        zzys: null,
        zyys: null,
        zrhs: null,
        jxys: null,
        sxys: null,
        bmy: null,
        bazl: null,
        zkys: null,
        zkhs: null,
        zkrq: null,
        ssjczbm1: null,
        ssjczrq1: null,
        ssjb1: null,
        ssjczmc1: null,
        sz1: null,
        yz1: null,
        ez1: null,
        qkdj1: null,
        qkyhlb1: null,
        mzfs1: null,
        mzys1: null,
        ssjczbm2: null,
        ssjczrq2: null,
        ssjb2: null,
        ssjczmc2: null,
        sz2: null,
        yz2: null,
        ez2: null,
        qkdj2: null,
        qkyhlb2: null,
        mzfs2: null,
        mzys2: null,
        ssjczbm3: null,
        ssjczrq3: null,
        ssjb3: null,
        ssjczmc3: null,
        sz3: null,
        yz3: null,
        ez3: null,
        qkdj3: null,
        qkyhlb3: null,
        mzfs3: null,
        mzys3: null,
        ssjczbm4: null,
        ssjczrq4: null,
        ssjb4: null,
        ssjczmc4: null,
        sz4: null,
        yz4: null,
        ez4: null,
        qkdj4: null,
        qkyhlb4: null,
        mzfs4: null,
        mzys4: null,
        ssjczbm5: null,
        ssjczrq5: null,
        ssjb5: null,
        ssjczmc5: null,
        sz5: null,
        yz5: null,
        ez5: null,
        qkdj5: null,
        qkyhlb5: null,
        mzfs5: null,
        mzys5: null,
        ssjczbm6: null,
        ssjczrq6: null,
        ssjb6: null,
        ssjczmc6: null,
        sz6: null,
        yz6: null,
        ez6: null,
        qkdj6: null,
        qkyhlb6: null,
        mzfs6: null,
        mzys6: null,
        ssjczbm7: null,
        ssjczrq7: null,
        ssjb7: null,
        ssjczmc7: null,
        sz7: null,
        yz7: null,
        ez7: null,
        qkdj7: null,
        qkyhlb7: null,
        mzfs7: null,
        mzys7: null,
        lyfs: null,
        yzzyYljg: null,
        wsyYljg: null,
        sfzzyjh: null,
        md: null,
        ryqT: null,
        ryqXs: null,
        ryqF: null,
        ryhT: null,
        ryhXs: null,
        ryhF: null,
        zfy: null,
        zfje: null,
        ylfuf: null,
        zlczf: null,
        hlf: null,
        qtfy: null,
        blzdf: null,
        syszdf: null,
        yxxzdf: null,
        lczdxmf: null,
        fsszlxmf: null,
        wlzlf: null,
        sszlf: null,
        maf: null,
        ssf: null,
        kff: null,
        zyzlf: null,
        xyf: null,
        kjywf: null,
        zcyf: null,
        zcyf1: null,
        xf: null,
        bdblzpf: null,
        qdblzpf: null,
        nxyzlzpf: null,
        xbyzlzpf: null,
        hcyyclf: null,
        yyclf: null,
        ycxyyclf: null,
        qtf: null,
        psh: null,
        basytype: null,
        orgcode: null,
        bycode: null,
        opname: null,
        opdate: null,
        xgcs: null,
        cxflag: null,
        jsdate: null,
        cqflag: null,
        jyflag: null,
        datasrc: null,
        jxstatus: null,
        sfsslcljgl: null,
        sfwclclj: null,
        sfby: null,
        byyy: null,
        ljbzmc: null,
        rydate: null,
        cydate: null,
        drgbh: null,
        rzflag: null,
        wrzyy: null,
        tczf: null,
        drgzf: null,
        jystatus: null,
        jlly: null,
        qjcs: null,
        qjcgcs: null,
        qzrq: null,
        zyzt: null,
        zdf: null,
        hisJsdate: null
      },
      brzdxx: {
        id: null,
        brbs: null,
        brid: null,
        zyid: null,
        zdlx: null,
        zdcx: null,
        jbbm: null,
        zdmc: null,
        rybq: null,
        cyqk: null,
        fm: null,
        bz: null,
        type: null
      },
      bassjl: {
        id: null,
        brbs: null,
        brid: null,
        zyid: null,
        zdcx: null,
        ssbm: null,
        ssmc: null,
        ssrq: null,
        ssjb: null,
        sskssj: null,
        ssjssj: null,
        sz: null,
        dyzs: null,
        dezs: null,
        qkyhdj: null,
        mzfs: null,
        mzfj: null,
        mzys: null,
        ssqk: null,
        ssbw: null,
      },
      jsxx: {
        psnName: null
      },
      syjl: {
        gj: null
      },
      syzdxx:[],
      syssxx:[],
    }
  },
  methods:{
    getAnstWay(val){
      return getAnesthesiaMethod(val);
    },
    hidContent() {
      this.hid = true
    },
    showContent() {
      this.hid = false
    },
    jsxxDownload() {
      // var url = "localhost/views/jsxx/index?jsid=" + this.jsid + "&bah=" + this.bah + "&jzh=" + this.jzh
      // var url = "http://localhost/views/jsxx/index?jsid=300548119456&bah=23070758&jzh=1743600_1"
      // // var url = "/drg/bafx/index"
      // var name = "结算清单明细"
      // this.$download.zip("", name);
      getPdf("结算清单明细"+ this.bah)
    },
    DhForm() {
      this.showDhForm = true
      this.advice = ""
    },
    daHui() {
      listBrxx({
        zyid:this.zyid,
        brid:this.brid
      }).then(response => {
        if (response.total == 0) {
          this.$modal.msgWarning("患者不存在！");
          return
        } else {
          updateBrxxShzt({
            zyid:this.zyid,
            brid:this.brid,
            shzt:2,
            advice:this.advice
          }).then(response => {
            if (response.code == 200) {
              this.$modal.msgSuccess("已打回！");
              this.advice = null
              this.showDhForm = false
            }
          });
        }
      });
    },
    setAdvice(advice) {
      var advice = "[" + advice + "],"
      if (this.advice.indexOf(advice) > -1) {
        this.advice = this.advice.replace(advice,"")
        return
      }
      this.advice += advice
    },
    goBack() {
      getdk().then(response => {
        if (response == undefined || response == null || response == "") {
          this.$modal.msgError("前端端口配置错误");
          return
        }
        this.dkh = response.rows[0]
        getFzPageIp().then(response => {
          if (response == undefined || response == null || response == "") {
            this.$modal.msgError("分组页面IP地址配置错误");
            return
          }
          window.location.href = "http://" + response + ":" + this.dkh + "/views/drg/yfz/index?id=2&bah=" + this.syjl.bah + "&brbs=" + this.syjl.brbs;
          // window.open("http://" + response + ":" + this.dkh + "/views/drg/yfz/index?id=1&bah=" + this.syjl.bah + "&brbs=" + this.syjl.brbs);
        });
      });
    }
  },
  created() {
    this.brid = this.$route.query.brid
    this.zyid = this.$route.query.zyid
    this.brbs = this.$route.query.brbs


    listJsxx({brid:this.brid,zyid:this.zyid}).then(response => {
      this.jsxx = response.rows[0];
      for(let key in this.jsxx){
        if (this.jsxx[key] == null)
          this.jsxx[key] = ""
      }

    });
    listSyjl({brbs:this.brbs}).then(response => {
      this.syjl = response.rows[0];

      this.brbs = this.syjl.brbs
      this.bah = this.syjl.bah

      this.bassjl.brid = this.brid
      this.bassjl.brbs = this.brbs
      this.bassjl.zyid = this.syjl.zyid

      this.brzdxx.brid = this.brid
      this.brzdxx.brbs = this.brbs
      this.brzdxx.zyid = this.syjl.zyid

      // listBrzdxx(this.brzdxx).then(response => {
      //   this.syzdxx = response.rows
      //   listSsjl(this.bassjl).then(response => {
      //     this.syssxx = response.rows
      //   });
      // });
      selectBrzdxxByOnePatient(this.brzdxx).then(res => {
        this.syzdxx = res.data;
        listSsjl(this.bassjl).then(response => {
          this.syssxx = response.rows
        });
      })
    });

  }
}
</script>

<style lang="scss">
.main {
  padding-top: 30px;
  font-size: 12px;
  background-color: #efeff6;
}
.top {
  background-color: #2683da;
  height: 45px;
  width: 100%;
  margin-top: 40px;
  color: white;
  display: flex;
  justify-content: left;
  line-height: 45px;
  div {
    margin-left: 45px;
    font-size: 19px;
  }
}
.row2 {
  width: 100%;
  height: 280px;
  box-shadow: 2px 2px 2px #f1f1f1;
  background-color: #ffffff;
  .jbxx {
    width: 98%;
    margin-left: 1%;
    height: 250px;
    background-color: #f8f8fa;
    display: flex;
    justify-content: center;
    .left,.right {
      color: #000;
      height: 100%;
      width: 50%;
      padding-left: 5%;
      div {
        margin-top: 10px;
      }
    }
    .left {
      border-right: rgba(0,0,0,.1) solid 1px;
    }
    .right {
      border-left: rgba(0,0,0,.1) solid 1px;
    }
  }
}
.row3 {
  background-color: #ffffff;
  width: 98%;
  margin-left: 1%;
  margin-top: 30px;
  .tit {
    width: 96%;
    height: 40px;
    font-size: 15px;
    margin-left: 2%;
    margin-top: 10px;
    font-weight: bold;
    padding-left: 10px;
    background-color: #e5e8ea;
    border-left: #005bbf solid 5px;
    line-height: 40px
  }
  .jsqd {
    margin-top: 25px;
    padding-top: 15px;
    margin-bottom: 35px;
    width: 100%;
    text-align: center;
    font-size: 20px
  }
  .ssxx,.zdxx {
    margin-left: 40px;
    margin-top: 25px;
    width: 93%;
    border-spacing: 0px;
    td {
      width: 14%;
      border: #ccc solid 1px
    }
  }
  .ract {
    border-left:solid 10px #095aad;
    height: 10px;
    width: 10px;
    margin-top: 2px;
  }
  .yuan {
    width: 10px;
    height: 10px;
    border-radius: 10px;
    border: #acdaff 3px solid;
    margin-top: 3px;
  }
  .ybxx {
    width: 80%;
    margin-left: 10%;
    height: 60px;
    background-color: #f8f8f8;
    margin-top: 10px;
    .tit2 {
      display: flex;
      padding-top: 10px;
    }
    .content {
      display: flex;
      font-weight: bold;
      width: 25%
    }
  }
}
.xmjemxb {
  margin-top: 15px;
  width: 93%;
  border-spacing: 0px;
  td {
    height: 25px;
    width: 14%;
    border: #ccc solid 1px;
    //
  }
}
.sfxx {
  display: flex;
  margin-left: 40px;
  margin-top: 25px;
  .right {
    table {
      margin-top: 15px;
      width: 93%;
      border-spacing: 0px;
      td {
        height: 25px;
        width: 14%;
        border: #ccc solid 1px;
      }
    }
  }
}
.show {
  background-color: #fff;
  width: 100%;
  .showBtn {
    background-color: #f8f8fa;
    border: #f8f8fa;
  }
  .showRow2 {
    text-indent: 5em;
    margin-left: 80px;
    margin-top: 10px;
  }
  .showRow1 {
    text-indent: 5em;
    margin-left: 80px;
    color: #000;
    font-weight: bold;
    margin-top: -20px;
  }
  .showContent {
    width: 98%;
    margin-left: 1%;
    background-color: #f8f8fa;
    padding-top: 20px;
  }
}

</style>
