<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="住院号" prop="bah">
        <el-input
          v-model="queryParams.bah"
          placeholder="请输入住院号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结算号" prop="jsh">
        <el-input
          v-model="queryParams.jsh"
          placeholder="请输入结算号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医疗机构" prop="yljgbm">
        <el-input
          v-model="queryParams.yljgbm"
          placeholder="请输入医疗机构编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="险种" prop="xz">
        <el-input
          v-model="queryParams.xz"
          placeholder="请输入险种"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:tldy:export']">导出</el-button>
        <el-button type="info" icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="tldyList">
      <el-table-column label="住院号" align="center" prop="bah" />
      <el-table-column label="姓名" align="center" prop="xm"  width="150"/>
      <el-table-column label="结算号" align="center" prop="jsh"  width="150"/>
      <el-table-column label="医疗机构编码" align="center" prop="yljgbm" />
      <el-table-column label="医疗机构名称" align="center" prop="yljgmc" />
      <el-table-column label="险种" align="center" prop="xz" />
      <el-table-column label="住院总费用" align="center" prop="zfy" />
      <el-table-column label="主要疾病编码" align="center" prop="jbbm" />
      <el-table-column label="主要疾病名称" align="center" prop="zdmc"  width="150"/>
      <el-table-column label="主要手术操作编码" align="center" prop="ssbm" />
      <el-table-column label="主要手术操作名称" align="center" prop="ssmc"  width="150"/>
      <el-table-column label="DRG编码" align="center" prop="drgbm" />
      <el-table-column label="DRG名称" align="center" prop="drgmc" width="200px"/>
      <el-table-column label="DRG支付标准" align="center" prop="drgzfbz" />
      <el-table-column label="DRG职工统筹" align="center" prop="drgzgtc" />
      <el-table-column label="职工统筹" align="center" prop="zgtc" />
      <el-table-column label="DRG居民统筹" align="center" prop="drgjmtc" />
      <el-table-column label="居民统筹" align="center" prop="jmtc" />
      <el-table-column label="结算日期" align="center" prop="jsrq" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.jsrq, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单议申请时间" align="center" prop="dysqsj" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dysqsj, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请单议病例类别" align="center" prop="sqdybllb" />
      <el-table-column label="审核状态" align="center" prop="shzt" />
      <el-table-column label="倍率" align="center" prop="bl" />
      <el-table-column label="实际住院天数" align="center" prop="sjzyts" />
      <el-table-column label="出院科室" align="center" prop="cykb" />
      <el-table-column label="申请原因" align="center" prop="sqyy" />
      <el-table-column label="结果提交状态" align="center" prop="jgtjzt" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTldy, getTldy, delTldy, addTldy, updateTldy } from "@/api/jsxx/tldy";
import {getToken} from "@/utils/auth";
import {importTemplate} from "@/api/gksz/kkmx";

export default {
  name: "Tldy",
  data() {
    return {
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/jsxx/tldy/importData"
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // DRG特例单议表格数据
      tldyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        xm: null,
        jsh: null,
        yljgbm: null,
        yljgmc: null,
        xz: null,
        zfy: null,
        jbbm: null,
        zdmc: null,
        ssbm: null,
        ssmc: null,
        drgbm: null,
        drgmc: null,
        drgzfbz: null,
        drgzgtc: null,
        zgtc: null,
        drgjmtc: null,
        jmtc: null,
        jsrq: null,
        dysqsj: null,
        sqdybllb: null,
        shzt: null,
        bah: null,
        sjzyts: null,
        cykb: null,
        sqyy: null,
        jgtjzt: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleImport() {
      this.upload.title = "结算信息导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    /** 查询DRG特例单议列表 */
    getList() {
      this.loading = true;
      listTldy(this.queryParams).then(response => {
        this.tldyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        xm: null,
        jsh: null,
        yljgbm: null,
        yljgmc: null,
        xz: null,
        zfy: null,
        jbbm: null,
        zdmc: null,
        ssbm: null,
        ssmc: null,
        drgbm: null,
        drgmc: null,
        drgzfbz: null,
        drgzgtc: null,
        zgtc: null,
        drgjmtc: null,
        jmtc: null,
        jsrq: null,
        dysqsj: null,
        sqdybllb: null,
        shzt: null,
        bah: null,
        sjzyts: null,
        cykb: null,
        sqyy: null,
        jgtjzt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加DRG特例单议";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTldy(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改DRG特例单议";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTldy(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTldy(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除DRG特例单议编号为"' + ids + '"的数据项？').then(function() {
        return delTldy(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/tldy/export', {
        ...this.queryParams
      }, `tldy_${new Date().getTime()}.xlsx`)
    },
  }
};
</script>
