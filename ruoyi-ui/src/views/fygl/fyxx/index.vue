<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目编号" prop="xmbm">
        <el-input
          v-model="queryParams.xmbm"
          placeholder="请输入项目编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="xmmc">
        <el-input
          v-model="queryParams.xmmc"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位" prop="dw">
        <el-input
          v-model="queryParams.dw"
          placeholder="请输入单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用日期" prop="fyDate">
        <el-date-picker
          v-model="daterangeRydate.startDate"
          type="datetime"
          placeholder="请选择开始日期"
          @change="dataSearch"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        --
        <el-date-picker
          v-model="daterangeRydate.endDate"
          type="datetime"
          placeholder="请选择截至日期"
          @change="dataSearch"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="科室名称" prop="ksname">
        <el-input
          v-model="queryParams.ksname"
          placeholder="请输入科室名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning"  icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['gksz:fyxx:export']">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table  :data="fyxxList" @selection-change="handleSelectionChange">
      <el-table-column label="项目编号" align="center" prop="xmbm" show-overflow-tooltip width="120px"/>
      <el-table-column label="项目名称" align="center" prop="xmmc" show-overflow-tooltip width="200px"/>
      <el-table-column label="数量" align="center" prop="sl" />
      <el-table-column label="单价" align="center" prop="price" />
      <el-table-column label="金额" align="center" prop="je" />
      <el-table-column label="单位" align="center" prop="dw" />
      <el-table-column label="规格" align="center" prop="guige" />
      <el-table-column label="费用时间" align="center" prop="fydate" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.fydate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" align="center" prop="opdate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.opdate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="科室名称" align="center" prop="ksname" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listFyxx, getFyxx, delFyxx, addFyxx, updateFyxx } from "@/api/fygl/fyxx";

export default {
  name: "Fyxx",
  data() {
    return {
      daterangeRydate: {
        startDate:null,
        endDate:null
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 费用查询表格数据
      fyxxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // $comment时间范围
      daterangeFydate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        xmbm: null,
        xmmc: null,
        sl: null,
        price: null,
        je: null,
        dw: null,
        guige: null,
        fydate: null,
        opdate: null,
        ksname: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        brid: [
          { required: true, message: "病人id不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.fyxxList = null
  },
  methods: {dataSearch() {
      this.getListByDataTime();
    },
    async getListByDataTime(data) {

    },
    showDialog() {
      if (this.wanzheng) this.wanzheng = false
      else this.wanzheng = true
    },
    /** 查询费用查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      this.queryParams.params["beginFydate"] = this.daterangeRydate.startDate;
      this.queryParams.params["endFydate"] = this.daterangeRydate.endDate;
      listFyxx(this.queryParams).then(response => {
        this.fyxxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        jzh: null,
        brid: null,
        zyid: null,
        yyxmbm: null,
        xmbm: null,
        xmmc: null,
        sl: null,
        price: null,
        je: null,
        dw: null,
        guige: null,
        fydate: null,
        opdate: null,
        ksid: null,
        ksname: null,
        ysid: null,
        ysname: null,
        yzid: null,
        billno: null,
        hisid: null,
        opname: null,
        id: null,
        jzid: null,
        fykmname: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeRydate = {
          startDate:null,
          endDate:null
      },
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加费用查询";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFyxx(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改费用查询";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFyxx(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFyxx(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除费用查询编号为"' + ids + '"的数据项？').then(function() {
        return delFyxx(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('gksz/fyxx/export', {
        ...this.queryParams
      }, `fyxx_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
