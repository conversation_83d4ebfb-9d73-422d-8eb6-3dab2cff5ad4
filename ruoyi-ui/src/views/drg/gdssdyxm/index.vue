<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手术编号" prop="ssbm">
        <el-input
          v-model="queryParams.ssbm"
          placeholder="请输入手术编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手术名称" prop="ssmc">
        <el-input
          v-model="queryParams.ssmc"
          placeholder="请输入手术名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编号" prop="xmbm">
        <el-input
          v-model="queryParams.xmbm"
          placeholder="请输入项目编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="xmmc">
        <el-input
          v-model="queryParams.xmmc"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-if="showDeleteIcon"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="gdssdyxmList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column show-overflow-tooltip label="手术编号" align="center" prop="ssbm" />
      <el-table-column show-overflow-tooltip label="手术名称" align="center" prop="ssmc" />
      <el-table-column show-overflow-tooltip label="项目编号" align="center" prop="xmbm" />
      <el-table-column show-overflow-tooltip label="项目名称" align="center" prop="xmmc" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:gdssdyxm:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:gdssdyxm:remove']"
            v-if="showDeleteIcon"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes=pageSizes
      @pagination="getList"
    />

    <!-- 添加或修改手术项目对应明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="手术编号" prop="ssbm">
          <el-input v-model="form.ssbm" placeholder="请输入手术编号" />
        </el-form-item>
        <el-form-item label="手术名称" prop="ssmc">
          <el-input v-model="form.ssmc" placeholder="请输入手术名称" />
        </el-form-item>
        <el-form-item label="项目编号" prop="xmbm">
          <el-input v-model="form.xmbm" placeholder="请输入项目编号" />
        </el-form-item>
        <el-form-item label="项目名称" prop="xmmc">
          <el-input v-model="form.xmmc" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="包含到DRG" prop="drgflag">
          <el-switch
            v-model="form.drgflag"
            :active-value="1"
            :inactive-value="0">
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listGdssdyxm, getGdssdyxm, delGdssdyxm, addGdssdyxm, updateGdssdyxm } from "@/api/drg/gdssdyxm";
import { getOption } from '@/api/system/option'

export default {
  name: "Gdssdyxm",
  data() {
    return {
      pageSizes:[5],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 手术项目对应明细表格数据
      gdssdyxmList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      showDeleteIcon: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        sno: null,
        ssbm: null,
        ssmc: null,
        ssjb: null,
        sstype: null,
        c1: null,
        c2: null,
        xmbm: null,
        xmmc: null,
        xmmc1: null,
        xmbmold: null,
        flag: null,
        ybssbm: null,
        drgflag: null,
        mrflag: null,
        ssmcxmmc: null,
        xsd: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.initSystemOptions()
    this.getList();
  },
  methods: {
    initSystemOptions(){
      getOption("use_gdssdyxm_delete").then(res => {
        if(res.data && res.data.cValue == "1") {
          this.showDeleteIcon = true
        }
      })
    },
    /** 查询手术项目对应明细列表 */
    getList() {
      this.loading = true;
      listGdssdyxm(this.queryParams).then(response => {
        this.gdssdyxmList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        sno: null,
        ssbm: null,
        ssmc: null,
        ssjb: null,
        sstype: null,
        c1: null,
        c2: null,
        xmbm: null,
        xmmc: null,
        xmmc1: null,
        xmbmold: null,
        flag: null,
        ybssbm: null,
        drgflag: 1,
        mrflag: null,
        id: null,
        ssmcxmmc: null,
        xsd: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加手术项目对应明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getGdssdyxm(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改手术项目对应明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateGdssdyxm(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGdssdyxm(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除手术项目对应明细编号为"' + ids + '"的数据项？').then(function() {
        return delGdssdyxm(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/gdssdyxm/export', {
        ...this.queryParams
      }, `gdssdyxm_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
