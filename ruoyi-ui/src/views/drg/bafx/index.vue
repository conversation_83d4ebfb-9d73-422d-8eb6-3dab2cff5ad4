<template>
  <div class="content">
    <div class="mainContent">

      <div class="head">
        <div class="row1">
          <span>医疗机构</span><input disabled :value="syjl.username">(组织机构代码<input disabled
                                                                                          :value="syjl.orgcode">)
        </div>
        <div class="row2" style="display: flex;justify-content: space-between">
        <span>
          <el-button type="primary" size="mini" @click="Qualitycontrol">病案质控</el-button>
        </span>
          <span>住院病案首页</span>
          <span>
          <el-button type="danger" size="mini" @click="Grouping">智能分组</el-button>
        </span>
        </div>
        <div class="row3">
          <span>医疗付款方式</span><input style="width: 177px;" disabled :value="syjl.ylfkfs">
        </div>
        <div class="row4">
          <span>健康卡号<input style="width: 200px;" disabled :value="syjl.jkkh"></span>
          <span>第 {{ syjl.zycs }}  次住院</span>
          <span>病案号<input style="width: 200px;" disabled :value="syjl.bah"></span>
        </div>
      </div>

      <div class="main">
        <div class="row1">
          <span style="width: 20%">姓名<input disabled :value="syjl.xm" style="width: 200px"></span>
          <span style="width: 20%">性别<input disabled :value="syjl.xb" style="width: 105px"> 1.男 2.女</span>
          <span style="width: 20%">出生日期<input disabled :value="syjl.csrq!=null?syjl.csrq.slice(0,4):''"
                                                  style="width: 40px">年<input disabled
                                                                               :value="syjl.csrq!=null?syjl.csrq.slice(5,7):''"
                                                                               style="width: 40px">月<input disabled
                                                                                                            :value="syjl.csrq!=null?syjl.csrq.slice(8,10):''"
                                                                                                            style="width: 40px">日</span>
          <span style="width: 20%">年龄<input disabled :value="syjl.nl" style="width: 200px"></span>
          <span style="width: 20%">国籍<input disabled :value="syjl.gj" style="width: 200px"></span>
        </div>
        <div class="row2">
          <span style="width: 20%">(年龄不足一周岁的)年龄<input disabled :value="syjl.bzyzsnl"
                                                                style="width: 90px">月</span>
          <span style="width: 20%">(年龄不足一周岁的)年龄<input disabled style="width: 50px">天</span>
          <span style="width: 20%">新生儿出生体重<input disabled :value="syjl.xsecstz" style="width: 110px">克</span>
          <span style="width: 40%">新生儿入院体重<input disabled :value="syjl.xserytz" style="width: 135px">克</span>
        </div>
        <div class="row3">
          <span style="width: 80%">出生地<input disabled :value="syjl.csd" style="width: 715px"></span>
          <span style="width: 20%">籍贯<input disabled :value="syjl.gg" style="width: 200px"></span>
        </div>
        <div class="row4">
          <span style="width: 20%">身份证号<input disabled :value="syjl.sfzh" style="width: 170px"></span>
          <span style="width: 20%">职业<input disabled :value="syjl.zy" style="width: 200px"></span>
          <span style="width: 60%">婚姻<input disabled :value="syjl.hy" style="width: 40px">1.未婚 2.已婚 3.初婚 4.复婚 5.再婚 6.丧偶 7.离婚 9.其他</span>
        </div>
        <div class="row5">
          <span style="width: 60%">现住址<input disabled :value="syjl.xzz" style="width: 715px"></span>
          <span style="width: 20%">电话<input disabled :value="syjl.dh" style="width: 200px"></span>
          <span style="width: 20%">邮编<input disabled :value="syjl.yb1" style="width: 200px"></span>
        </div>
        <div class="row6">
          <span style="width: 80%">户口地址<input disabled :value="syjl.hkdz" style="width: 703px"></span>
          <span style="width: 20%">邮编<input disabled :value="syjl.yb2" style="width: 200px"></span>
        </div>
        <div class="row7">
          <span style="width: 60%">工作单位及地址<input disabled :value="syjl.gzdwjdz" style="width: 670px"></span>
          <span style="width: 20%">单位电话<input disabled :value="syjl.dwdh" style="width: 180px"></span>
          <span style="width: 20%">邮编<input disabled :value="syjl.yb3" style="width: 200px"></span>
        </div>
        <div class="row8">
          <span style="width: 20%">联系人姓名<input disabled :value="syjl.lxrxm" style="width: 160px"></span>
          <span style="width: 20%">关系<input disabled :value="syjl.gx" style="width: 200px"></span>
          <span style="width: 40%">地址<input disabled :value="syjl.dz" style="width: 463px"></span>
          <span style="width: 20%">电话<input disabled :value="syjl.dh2" style="width: 200px"></span>
        </div>
        <div class="row9">
          <span style="width: 100%">入院途径<input disabled :value="syjl.rytj" style="width: 40px">1.急诊 2.门诊 3.其他医疗机构转入 9.其他</span>
        </div>
        <div class="row10">
        <span style="width: 40%">入院时间<input disabled :value="syjl.rysj!=null?syjl.rysj.slice(0,4):''"
                                                style="width: 40px">年<input disabled
                                                                             :value="syjl.rysj!=null?syjl.rysj.slice(5,7):''"
                                                                             style="width: 40px">月<input disabled
                                                                                                          :value="syjl.rysj!=null?syjl.rysj.slice(8,10):''"
                                                                                                          style="width: 40px">日
        <input disabled :value="syjl.rysjs" style="width: 40px">时</span>
          <span style="width: 20%">入院科别<input disabled :value="syjl.rykb" style="width: 180px"></span>
          <span style="width: 20%">病房<input disabled :value="syjl.rybf" style="width: 200px"></span>
          <span style="width: 20%">转科科别<input disabled :value="syjl.zkkb" style="width: 180px"></span>
        </div>
        <div class="row11">
        <span style="width: 40%">出院时间<input disabled :value="syjl.cysj!=null?syjl.cysj.slice(0,4):''"
                                                style="width: 40px">年<input disabled
                                                                             :value="syjl.cysj!=null?syjl.cysj.slice(5,7):''"
                                                                             style="width: 40px">月<input disabled
                                                                                                          :value="syjl.cysj!=null?syjl.cysj.slice(8,10):''"
                                                                                                          style="width: 40px">日
        <input disabled :value="syjl.cysjs" style="width: 40px">时</span>
          <span style="width: 20%">出院科别<input disabled :value="syjl.cykb" style="width: 180px"></span>
          <span style="width: 20%">病房<input disabled :value="syjl.cybf" style="width: 200px"></span>
          <span style="width: 20%">实际住院天数<input disabled :value="syjl.sjzyts" style="width: 155px"></span>
        </div>
        <div class="row12">
          <span style="width: 80%">门（急）诊诊断<input disabled :value="syjl.mzzd" style="width: 400px"></span>
          <span style="width: 20%">疾病编码<input disabled :value="syjl.jbbm" style="width: 180px"></span>
        </div>
        <div class="row13">
          <table class="zd">
            <tr>
              <th>出院诊断</th>
              <th>疾病编码</th>
              <th>入院病情</th>
              <th>出院诊断</th>
              <th>疾病编码</th>
              <th>入院病情</th>
            </tr>
            <tr>
              <td>{{ syjl.zyzd }}</td>
              <td>{{ syjl.jbdm }}</td>
              <td>{{ syjl.rybq }}</td>
              <td>{{ syjl.qtzd1 }}</td>
              <td>{{ syjl.jbdm1 }}</td>
              <td>{{ syjl.rybq1 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.qtzd2 }}</td>
              <td>{{ syjl.jbdm2 }}</td>
              <td>{{ syjl.rybq2 }}</td>
              <td>{{ syjl.qtzd3 }}</td>
              <td>{{ syjl.jbdm3 }}</td>
              <td>{{ syjl.rybq3 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.qtzd4 }}</td>
              <td>{{ syjl.jbdm4 }}</td>
              <td>{{ syjl.rybq4 }}</td>
              <td>{{ syjl.qtzd5 }}</td>
              <td>{{ syjl.jbdm5 }}</td>
              <td>{{ syjl.rybq5 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.qtzd6 }}</td>
              <td>{{ syjl.jbdm6 }}</td>
              <td>{{ syjl.rybq6 }}</td>
              <td>{{ syjl.qtzd7 }}</td>
              <td>{{ syjl.jbdm7 }}</td>
              <td>{{ syjl.rybq7 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.qtzd8 }}</td>
              <td>{{ syjl.jbdm8 }}</td>
              <td>{{ syjl.rybq8 }}</td>
              <td>{{ syjl.qtzd9 }}</td>
              <td>{{ syjl.jbdm9 }}</td>
              <td>{{ syjl.rybq9 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.qtzd10 }}</td>
              <td>{{ syjl.jbdm10 }}</td>
              <td>{{ syjl.rybq10 }}</td>
              <td>{{ syjl.qtzd11 }}</td>
              <td>{{ syjl.jbdm11 }}</td>
              <td>{{ syjl.rybq11 }}</td>
            </tr>
            <tr>
              <td style="width: 17%">{{ syjl.qtzd12 }}</td>
              <td style="width: 17%">{{ syjl.jbdm12 }}</td>
              <td style="width: 17%">{{ syjl.rybq12 }}</td>
              <td style="width: 17%">{{ syjl.qtzd13 }}</td>
              <td style="width: 17%">{{ syjl.jbdm13 }}</td>
              <td style="width: 17%">{{ syjl.rybq13 }}</td>
            </tr>
            <tr>
              <td style="width: 17%">{{ syjl.qtzd14 }}</td>
              <td style="width: 17%">{{ syjl.jbdm14 }}</td>
              <td style="width: 17%">{{ syjl.rybq14 }}</td>
              <td style="width: 17%">{{ syjl.qtzd15 }}</td>
              <td style="width: 17%">{{ syjl.jbdm15 }}</td>
              <td style="width: 17%">{{ syjl.rybq15 }}</td>
            </tr>
            <tr>
              <td style="width: 100%;font-weight: normal" colspan="6">入院病情：1.有 2.临床未确定 3.情况未明 4.无</td>
            </tr>
          </table>
        </div>
        <div class="row14">
          <table class="data14" style="border: none">
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 60%">损伤、中毒的外部原因<input disabled :value="syjl.wbyy"
                                                                   style="width: 660px"></span>
                <span style="width: 40%">疾病编码<input disabled style="width: 200px" :value="syjl.h23"></span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 20%">病理号<input disabled :value="syjl.blh" style="width: 170px"></span>
                <span style="width: 40%">病理诊断<input disabled :value="syjl.blzd" style="width: 420px"></span>
                <span style="width: 40%">疾病编码<input disabled :value="syjl.jbmm" style="width: 200px"></span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 20%">药物过敏<input disabled :value="syjl.ywgm" style="width: 40px">1.无 2.有</span>
                <span style="width: 40%">过敏药物<input disabled :value="syjl.gmyw" style="width: 420px"></span>
                <span style="width: 40%">死亡患者尸检<input disabled :value="syjl.swhzsj" style="width: 130px">1.是 2.否</span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 60%">血型<input disabled :value="syjl.xx" style="width: 40px">1.A 2.B 3.O 4.AB 5.不详 6.未查 </span>
                <span style="width: 40%">Rh<input disabled :value="syjl.rh" style="width: 103px">1.阴 2.阳 3.不详 4.未查</span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
              <span style="width: 60%">
                输血品种--红细胞<input disabled style="width: 40px">单位
                血小板<input disabled style="width: 40px">人份
                血浆<input disabled style="width: 40px">ML
                全血<input disabled style="width: 40px">ML
                自体血回输<input disabled style="width: 40px">ML
                其他<input disabled style="width: 40px">ML
              </span>
                <span style="width: 40%">
                输血反应<input disabled style="width: 111px">1.是 2.否 3.未输
              </span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 20%">科主任<input disabled style="width: 170px" :value="syjl.kzr"></span>
                <span style="width: 20%">主任（副主任）医师<input style="width: 103px" disabled :value="syjl.zrys"></span>
                <span style="width: 20%">主治医师<input disabled :value="syjl.zzys"></span>
                <span style="width: 40%">住院医师<input disabled style="width: 200px" :value="syjl.zyys"></span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 20%">责任护士<input disabled style="width: 160px" :value="syjl.zrhs"></span>
                <span style="width: 20%">进修医师<input disabled :value="syjl.jxys"></span>
                <span style="width: 20%">实习医师<input disabled :value="syjl.sxys"></span>
                <span style="width: 40%">编码员<input disabled style="width: 210px" :value="syjl.bmy"></span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 20%">病案质量<input disabled :value="syjl.bazl"
                                                        style="width: 77px">1.甲 2.乙 3.丙</span>
                <span style="width: 20%">质控医师<input disabled :value="syjl.zkys"></span>
                <span style="width: 20%">质控护士<input disabled :value="syjl.zkhs"></span>
                <span style="width: 40%">质控日期<input disabled :value="syjl.zkrq!=null?syjl.zkrq.slice(0,4):''"
                                                        style="width: 60px">年<input disabled
                                                                                     :value="syjl.zkrq!=null?syjl.zkrq.slice(5,7):''"
                                                                                     style="width: 54px">月<input
                  disabled :value="syjl.zkrq!=null?syjl.zkrq.slice(8,10):''" style="width: 54px">日</span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 20%">是否实施临床管理路径<input disabled :value="syjl.sfsslcljgl"
                                                                    style="width: 40px">1.是 2.否</span>
                <span style="width: 20%">是否完成临床管理路径<input disabled :value="syjl.sfwclclj" style="width: 40px">1.是 2.否</span>
                <span style="width: 20%">是否变异<input disabled :value="syjl.sfby"
                                                        style="width: 110px">1.是 2.否</span>
                <span style="width: 40%">变异原因<input disabled style="width: 200px" :value="syjl.byyy"></span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 60%"></span>
                <span style="width: 40%">路径病种名称<input disabled style="width: 178px" :value="syjl.ljbzmc"></span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 60%">抢救次数<input disabled :value="syjl.qjcs"></span>
                <span style="width: 40%">抢救成功次数<input disabled style="width: 178px" :value="syjl.qjcgcs"></span>
              </td>
            </tr>
            <tr style="border: none">
              <td style="border: none">
                <span style="width: 60%">确诊日期<input disabled :value="syjl.qzrq"></span>
                <span style="width: 40%">在院状态<input disabled :value="zyztFormat(syjl.zyzt)"
                                                        style="width: 125px">1.在院 2.离院</span>
              </td>
            </tr>
          </table>
        </div>
        <div class="row15">
          <table class="ss">
            <tr>
              <th style="width: 10%" rowspan="2">手术操作编码</th>
              <th style="width: 10%" rowspan="2">手术及操作日期</th>
              <th style="width: 10%" rowspan="2">手术级别</th>
              <th style="width: 10%" rowspan="2">手术及操作名称</th>
              <th style="width: 30%" colspan="3">手术及操作医师</th>
              <th style="width: 10%" rowspan="2">切口愈合等级</th>
              <th style="width: 10%" rowspan="2">麻醉方式</th>
              <th style="width: 10%" rowspan="2">麻醉医师</th>
            </tr>
            <tr>
              <th>术者</th>
              <th>Ⅰ助</th>
              <th>Ⅱ助</th>
            </tr>
            <tr>
              <td>{{ syjl.ssjczbm1 }}</td>
              <td>{{ syjl.ssjczrq1 }}</td>
              <td>{{ syjl.ssjb1 }}</td>
              <td>{{ syjl.ssjczmc1 }}</td>
              <td>{{ syjl.sz1 }}</td>
              <td>{{ syjl.yz1 }}</td>
              <td>{{ syjl.ez1 }}</td>
              <td>{{ syjl.qkyhlb1 }}</td>
              <td>{{ syjl.mzfs1 }}</td>
              <td>{{ syjl.mzys1 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.ssjczbm2 }}</td>
              <td>{{ syjl.ssjczrq2 }}</td>
              <td>{{ syjl.ssjb2 }}</td>
              <td>{{ syjl.ssjczmc2 }}</td>
              <td>{{ syjl.sz2 }}</td>
              <td>{{ syjl.yz2 }}</td>
              <td>{{ syjl.ez2 }}</td>
              <td>{{ syjl.qkyhlb2 }}</td>
              <td>{{ syjl.mzfs2 }}</td>
              <td>{{ syjl.mzys2 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.ssjczbm3 }}</td>
              <td>{{ syjl.ssjczrq3 }}</td>
              <td>{{ syjl.ssjb3 }}</td>
              <td>{{ syjl.ssjczmc3 }}</td>
              <td>{{ syjl.sz3 }}</td>
              <td>{{ syjl.yz3 }}</td>
              <td>{{ syjl.ez3 }}</td>
              <td>{{ syjl.qkyhlb3 }}</td>
              <td>{{ syjl.mzfs3 }}</td>
              <td>{{ syjl.mzys3 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.ssjczbm4 }}</td>
              <td>{{ syjl.ssjczrq4 }}</td>
              <td>{{ syjl.ssjb4 }}</td>
              <td>{{ syjl.ssjczmc4 }}</td>
              <td>{{ syjl.sz4 }}</td>
              <td>{{ syjl.yz4 }}</td>
              <td>{{ syjl.ez4 }}</td>
              <td>{{ syjl.qkyhlb4 }}</td>
              <td>{{ syjl.mzfs4 }}</td>
              <td>{{ syjl.mzys4 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.ssjczbm5 }}</td>
              <td>{{ syjl.ssjczrq5 }}</td>
              <td>{{ syjl.ssjb5 }}</td>
              <td>{{ syjl.ssjczmc5 }}</td>
              <td>{{ syjl.sz5 }}</td>
              <td>{{ syjl.yz5 }}</td>
              <td>{{ syjl.ez5 }}</td>
              <td>{{ syjl.qkyhlb5 }}</td>
              <td>{{ syjl.mzfs5 }}</td>
              <td>{{ syjl.mzys5 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.ssjczbm6 }}</td>
              <td>{{ syjl.ssjczrq6 }}</td>
              <td>{{ syjl.ssjb6 }}</td>
              <td>{{ syjl.ssjczmc6 }}</td>
              <td>{{ syjl.sz6 }}</td>
              <td>{{ syjl.yz6 }}</td>
              <td>{{ syjl.ez6 }}</td>
              <td>{{ syjl.qkyhlb6 }}</td>
              <td>{{ syjl.mzfs6 }}</td>
              <td>{{ syjl.mzys6 }}</td>
            </tr>
            <tr>
              <td>{{ syjl.ssjczbm7 }}</td>
              <td>{{ syjl.ssjczrq7 }}</td>
              <td>{{ syjl.ssjb7 }}</td>
              <td>{{ syjl.ssjczmc7 }}</td>
              <td>{{ syjl.sz7 }}</td>
              <td>{{ syjl.yz7 }}</td>
              <td>{{ syjl.ez7 }}</td>
              <td>{{ syjl.qkyhlb7 }}</td>
              <td>{{ syjl.mzfs7 }}</td>
              <td>{{ syjl.mzys7 }}</td>
            </tr>
          </table>
        </div>
        <div class="row16">
          <table class="data16">
            <tr>
              <td>
            <span>离院方式<input disabled :value="syjl.lyfs" style="width: 70px">
              1.医嘱离院 2.医嘱转院 拟接收医疗机构名称<input disabled :value="syjl.yzzyYljg" style="width: 200px">
              3.医嘱转社区卫生服务机构/乡镇卫生院 拟接收医疗机构名称<input disabled :value="syjl.wsy_yljg"
                                                                           style="width: 200px">
              4.非医嘱离院 5.死亡 9.其他
            </span>
              </td>
            </tr>
            <tr>
              <td>
                <span>是否有出院31天内再住院计划<input disabled :value="syjl.sfzzyjh" style="width: 40px">1.有 2.无，目的<input
                  disabled :value="syjl.md" style="width: 800px"><br></span>
              </td>
            </tr>
            <tr>
              <td>
              <span>
                颅脑损伤患者昏迷时间：
                入院前
                <input disabled :value="syjl.ryq_t" style="width: 40px">天<input disabled :value="syjl.ryq_xs"
                                                                                 style="width: 40px">小时<input disabled
                                                                                                                :value="syjl.ryq_f"
                                                                                                                style="width: 40px">分钟
                入院后
                <input disabled :value="syjl.ryh_t" style="width: 40px">天<input disabled :value="syjl.ryh_xs"
                                                                                 style="width: 40px">小时<input disabled
                                                                                                                :value="syjl.ryh_f"
                                                                                                                style="width: 40px">分钟
              </span>
              </td>
            </tr>
            <tr>
              <td>
                <span>呼吸机使用时间<input disabled style="width: 180px">小时</span>
              </td>
            </tr>
          </table>
        </div>
        <div>
          <table class="data17">
            <tr>
              <td colspan="6">
                住院费用（元）：总费用<input disabled :value="syjl.zfy" style="width: 200px">
                (自付金额<input disabled :value="syjl.zfje" style="width: 200px">)
              </td>
            </tr>
            <tr style="width: 100%">
              <td class="fytype">综合医疗服务类</td>
              <td style="width: 18%">一般医疗服务费：<span class="fyvalue">{{ syjl.ylfuf ? syjl.ylfuf : 0 }}</span></td>
              <td style="width: 18%">一般治疗操作费：<span class="fyvalue">{{ syjl.zlczf ? syjl.zlczf : 0 }}</span></td>
              <td style="width: 18%">护理费：<span class="fyvalue">{{ syjl.hlf ? syjl.hlf : 0 }}</span></td>
              <td style="width: 18%">其他费用：<span class="fyvalue">{{ syjl.qtfy ? syjl.qtfy : 0 }}</span></td>
              <td style="width: 18%"></td>
            </tr>
            <tr>
              <td class="fytype">诊断类</td>
              <td>病理诊断费：<span class="fyvalue">{{ syjl.blzdf ? syjl.blzdf : 0 }}</span></td>
              <td>实验室诊断费：<span class="fyvalue">{{ syjl.syszdf ? syjl.syszdf : 0 }}</span></td>
              <td>影像学诊断费：<span class="fyvalue">{{ syjl.yxxzdf ? syjl.yxxzdf : 0 }}</span></td>
              <td>临床诊断项目费：<span class="fyvalue">{{ syjl.lczdxmf ? syjl.lczdxmf : 0 }}</span></td>
              <td></td>
            </tr>
            <tr>
              <td class="fytype">治疗类</td>
              <td>非手术治疗项目费：<span class="fyvalue">{{ syjl.fsszlxmf ? syjl.fsszlxmf : 0 }}</span></td>
              <td>临床物理治疗费：<span class="fyvalue">{{ syjl.wlzlf ? syjl.wlzlf : 0 }}</span></td>
              <td>手术治疗费：<span class="fyvalue">{{ syjl.sszlf ? syjl.sszlf : 0 }}</span></td>
              <td>麻醉费：<span class="fyvalue">{{ syjl.maf ? syjl.maf : 0 }}</span></td>
              <td>手术费：<span class="fyvalue">{{ syjl.ssf ? syjl.ssf : 0 }}</span></td>
            </tr>
            <tr>
              <td class="fytype">康复类</td>
              <td>康复费：<span class="fyvalue">{{ syjl.kff ? syjl.kff : 0 }}</span></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td class="fytype">中医类</td>
              <td>中医治疗费：<span class="fyvalue">{{ syjl.zyzlf ? syjl.zyzlf : 0 }}</span></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td class="fytype">西医类</td>
              <td>西药费：<span class="fyvalue">{{ syjl.xyf ? syjl.xyf : 0 }}</span></td>
              <td>抗菌药物费用：<span class="fyvalue">{{ syjl.kjywf ? syjl.kjywf : 0 }}</span></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td class="fytype">中药类</td>
              <td>中成药费：<span class="fyvalue">{{ syjl.zcyf ? syjl.zcyf : 0 }}</span></td>
              <td>中草药费：<span class="fyvalue">{{ syjl.zcyf1 ? syjl.zcyf1 : 0 }}</span></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td class="fytype">血液与血液制品类</td>
              <td>血费：<span class="fyvalue">{{ syjl.xf ? syjl.xf : 0 }}</span></td>
              <td>白蛋白类制品费：<span class="fyvalue">{{ syjl.bdblzpf ? syjl.bdblzpf : 0 }}</span></td>
              <td>球蛋白类制品费：<span class="fyvalue">{{ syjl.qdblzpf ? syjl.qdblzpf : 0 }}</span></td>
              <td>凝血因子类制品费：<span class="fyvalue">{{ syjl.nxyzlzpf ? syjl.nxyzlzpf : 0 }}</span></td>
              <td>细胞因子类制品费：<span class="fyvalue">{{ syjl.xbyzlzpf ? syjl.xbyzlzpf : 0 }}</span></td>
            </tr>
            <tr>
              <td class="fytype">耗材类</td>
              <td>检查用一次性医用材料费：<span class="fyvalue">{{ syjl.hcyyclf ? syjl.hcyyclf : 0 }}</span></td>
              <td>治疗用一次性医用材料费：<span class="fyvalue">{{ syjl.yyclf ? syjl.yyclf : 0 }}</span></td>
              <td>手术用一次性医用材料费：<span class="fyvalue">{{ syjl.ycxyyclf ? syjl.ycxyyclf : 0 }}</span></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td class="fytype">其他类</td>
              <td>其他费：<span class="fyvalue">{{ syjl.qtf ? syjl.qtf : 0 }}</span></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
          </table>
        </div>
        <div class="addRow18">
          <table class="addData18">
            <tr>
              <td>
                <span style="width: 100%">批次号<input disabled style="width: 205px" :value="syjl.psh"></span>
              </td>
            </tr>
            <tr>
              <td>
                <span style="width: 100%">住院病案首页类型<input disabled style="width: 150px"
                                                                 :value="syjl.basytype"></span>
              </td>
            </tr>
            <tr>
              <td>
                <span style="width: 100%">个人编码<input disabled style="width: 195px" :value="syjl.bycode"></span>
              </td>
            </tr>
            <tr>
              <td>
                <span style="width: 60%">经办人<input disabled style="width: 206px" :value="syjl.opname"></span>
                <span style="width: 40%">经办时间<input disabled style="width: 190px" :value="syjl.opdate"></span>
              </td>
            </tr>
          </table>
        </div>
        <div class="row18">
          <textarea style="width: 100%; height: 100px"></textarea>
        </div>
        <div class="row19" style="padding-bottom: 15px">
        <span>说明：<br>
          （一）医疗付款方式
          1.城镇职工基本医疗保险 2.城镇居民基本医疗保险 3.新型农村合作医疗
          4.贫困救助 5.商业医疗保险 6.全公费 7.全自费 8.其他社会保险 99.其他
          <br>
          （二）凡可由医院信息系统提供住院费用清单的，住院病案首页中可不填写“住院费用”。
        </span>
        </div>
      </div>

      <el-dialog title="质控信息" :visible.sync="open" width="1000px" append-to-body>
        <el-table :data="wtList" height="450">
          <el-table-column label="错误信息" align="center" prop="errordes"/>
          <el-table-column label="错误编码" align="center" prop="field"/>
          <el-table-column label="扣分" align="center" prop="score"/>
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
    <div class="rightContent">
      <div class="fzxx">
        <div class="fzxxItem"
             style="text-align: center; font-size: 23px; font-weight: bold; margin-top: 10px;margin-bottom: 10px"></div>
        <div>
          <span>DRG编号：</span>
          <span>{{ this.brzyxx.drgbh }}</span>
        </div>
        <div>
          <span>DRG名称：</span>
          <span>{{ this.brzyxx.drgmc }}</span>
        </div>
        <div>
          <span>DRG权重：</span>
          <span>{{ this.brzyxx.zfqz }}</span>
        </div>
        <div>
          <span>首页评分：</span>
          <span>{{ this.bafs }}分</span>
        </div>
        <div style="display: flex; justify-content: space-between">
          <div>
            <span>总费用：</span>
            <span>{{ this.brzyxx.zfy == null ? "未找到" : this.brzyxx.zfy + "元" }}</span>
          </div>
          <div>
            <span>标杆：</span>
            <span>{{ this.brzyxx.zfbz == null ? "未找到" : this.brzyxx.zfbz + "元" }}</span>
          </div>
        </div>
        <div style="display: flex; justify-content: space-between">
          <div>
            <span>药材占比：</span>
            <span>{{ this.brzyxx.ypf == null ? "未找到" : (this.brzyxx.ypf / this.brzyxx.zfy).toFixed(2) + "%" }}</span>
          </div>
          <div>
            <span>标杆：</span>
            <span>{{
                this.brzyxx.bgypf == null ? "未找到" : (this.brzyxx.bgypf / this.brzyxx.zfy).toFixed(2) + "%"
              }}</span>
          </div>
        </div>
        <div style="display: flex; justify-content: space-between">
          <div>
            <span>耗材占比：</span>
            <span>{{ this.brzyxx.hcf == null ? "未找到" : (this.brzyxx.hcf / this.brzyxx.zfy).toFixed(2) + "%" }}</span>
          </div>
          <div>
            <span>标杆：</span>
            <span>{{
                this.brzyxx.bghcf == null ? "未找到" : (this.brzyxx.bghcf / this.brzyxx.zfy).toFixed(2) + "%"
              }}</span>
          </div>
        </div>

      </div>
      <div class="zkxx">
        <div
          style="width: 100%;background-color: #ccc;margin-top: 10px;height: 25px;line-height: 25px;text-align: center">
          质控信息
        </div>
        <div class="zkList">
          <div class="zkxxItem" v-for="(item,index) in zkxx">
            <div>
              <span>信息：</span>
              <span>{{ item.errordes }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {bazk, getdk, getFzPageIp, listSyjl} from "@/api/drg/syjl";
import {listZyjc} from "@/api/zyjc/zyjc";
import {getOption} from "@/api/system/option";

const statusMappings = {
  "1": "有",
  "2": "情况不明",
  "3": "临床未确定",
  "4": "院内发病",
};

export default {
  name: "index",
  data() {
    return {
      loading: true,
      open: false,
      wtList: [],
      dkh: '',
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jzh: null,
        brbs: null,
        brid: null,
        zyid: null,
        username: null,
        ylfkfs: null,
        jkkh: null,
        zycs: null,
        bah: null,
        xm: null,
        xb: null,
        csrq: null,
        nl: null,
        gj: null,
        bzyzsnl: null,
        xsecstz: null,
        xserytz: null,
        csd: null,
        gg: null,
        mz: null,
        sfzh: null,
        zy: null,
        hy: null,
        xzz: null,
        dh: null,
        yb1: null,
        hkdz: null,
        yb2: null,
        gzdwjdz: null,
        dwdh: null,
        yb3: null,
        lxrxm: null,
        gx: null,
        dz: null,
        dh2: null,
        rytj: null,
        rysj: null,
        rysjs: null,
        rykb: null,
        rybf: null,
        zkkb: null,
        cysj: null,
        cysjs: null,
        cykb: null,
        cybf: null,
        sjzyts: null,
        mzzd: null,
        jbbm: null,
        zyzd: null,
        jbdm: null,
        rybq: null,
        qtzd1: null,
        jbdm1: null,
        rybq1: null,
        qtzd2: null,
        jbdm2: null,
        rybq2: null,
        qtzd3: null,
        jbdm3: null,
        rybq3: null,
        qtzd4: null,
        jbdm4: null,
        rybq4: null,
        qtzd5: null,
        jbdm5: null,
        rybq5: null,
        qtzd6: null,
        jbdm6: null,
        rybq6: null,
        qtzd7: null,
        jbdm7: null,
        rybq7: null,
        qtzd8: null,
        jbdm8: null,
        rybq8: null,
        qtzd9: null,
        jbdm9: null,
        rybq9: null,
        qtzd10: null,
        jbdm10: null,
        rybq10: null,
        qtzd11: null,
        jbdm11: null,
        rybq11: null,
        qtzd12: null,
        jbdm12: null,
        rybq12: null,
        qtzd13: null,
        jbdm13: null,
        rybq13: null,
        qtzd14: null,
        jbdm14: null,
        rybq14: null,
        qtzd15: null,
        jbdm15: null,
        rybq15: null,
        wbyy: null,
        h23: null,
        blzd: null,
        jbmm: null,
        blh: null,
        ywgm: null,
        gmyw: null,
        swhzsj: null,
        xx: null,
        rh: null,
        kzr: null,
        zrys: null,
        zzys: null,
        zyys: null,
        zrhs: null,
        jxys: null,
        sxys: null,
        bmy: null,
        bazl: null,
        zkys: null,
        zkhs: null,
        zkrq: null,
        ssjczbm1: null,
        ssjczrq1: null,
        ssjb1: null,
        ssjczmc1: null,
        sz1: null,
        yz1: null,
        ez1: null,
        qkdj1: null,
        qkyhlb1: null,
        mzfs1: null,
        mzys1: null,
        ssjczbm2: null,
        ssjczrq2: null,
        ssjb2: null,
        ssjczmc2: null,
        sz2: null,
        yz2: null,
        ez2: null,
        qkdj2: null,
        qkyhlb2: null,
        mzfs2: null,
        mzys2: null,
        ssjczbm3: null,
        ssjczrq3: null,
        ssjb3: null,
        ssjczmc3: null,
        sz3: null,
        yz3: null,
        ez3: null,
        qkdj3: null,
        qkyhlb3: null,
        mzfs3: null,
        mzys3: null,
        ssjczbm4: null,
        ssjczrq4: null,
        ssjb4: null,
        ssjczmc4: null,
        sz4: null,
        yz4: null,
        ez4: null,
        qkdj4: null,
        qkyhlb4: null,
        mzfs4: null,
        mzys4: null,
        ssjczbm5: null,
        ssjczrq5: null,
        ssjb5: null,
        ssjczmc5: null,
        sz5: null,
        yz5: null,
        ez5: null,
        qkdj5: null,
        qkyhlb5: null,
        mzfs5: null,
        mzys5: null,
        ssjczbm6: null,
        ssjczrq6: null,
        ssjb6: null,
        ssjczmc6: null,
        sz6: null,
        yz6: null,
        ez6: null,
        qkdj6: null,
        qkyhlb6: null,
        mzfs6: null,
        mzys6: null,
        ssjczbm7: null,
        ssjczrq7: null,
        ssjb7: null,
        ssjczmc7: null,
        sz7: null,
        yz7: null,
        ez7: null,
        qkdj7: null,
        qkyhlb7: null,
        mzfs7: null,
        mzys7: null,
        lyfs: null,
        yzzyYljg: null,
        wsyYljg: null,
        sfzzyjh: null,
        md: null,
        ryqT: null,
        ryqXs: null,
        ryqF: null,
        ryhT: null,
        ryhXs: null,
        ryhF: null,
        zfy: null,
        zfje: null,
        ylfuf: null,
        zlczf: null,
        hlf: null,
        qtfy: null,
        blzdf: null,
        syszdf: null,
        yxxzdf: null,
        lczdxmf: null,
        fsszlxmf: null,
        wlzlf: null,
        sszlf: null,
        maf: null,
        ssf: null,
        kff: null,
        zyzlf: null,
        xyf: null,
        kjywf: null,
        zcyf: null,
        zcyf1: null,
        xf: null,
        bdblzpf: null,
        qdblzpf: null,
        nxyzlzpf: null,
        xbyzlzpf: null,
        hcyyclf: null,
        yyclf: null,
        ycxyyclf: null,
        qtf: null,
        psh: null,
        basytype: null,
        orgcode: null,
        bycode: null,
        opname: null,
        opdate: null,
        xgcs: null,
        cxflag: null,
        jsdate: null,
        cqflag: null,
        jyflag: null,
        datasrc: null,
        jxstatus: null,
        sfsslcljgl: null,
        sfwclclj: null,
        sfby: null,
        byyy: null,
        ljbzmc: null,
        rydate: null,
        cydate: null,
        drgbh: null,
        rzflag: null,
        wrzyy: null,
        tczf: null,
        drgzf: null,
        jystatus: null,
        jlly: null,
        qjcs: null,
        qjcgcs: null,
        qzrq: null,
        zyzt: null,
        zdf: null,
        hisJsdate: null
      },
      syjl: [],
      zkxx: [],
      ybzdxx: [],
      ybssxx: [],
      brzyxx: {
        code: null,
        drgbh: null,
        drgmc: null,
        fztype: null,
        message: null,
        pjdays: null,
        zfbz: null,
        zfqz: null,
        zfy: null,
        zydays: null,
        num: null
      },
      bafs: 100
    }
  },
  methods: {
    getBaData() {
      listSyjl(this.queryParams).then(response => {
        this.syjl = response.rows[0];


        for (var prop in this.syjl) {
          if (prop.includes('rybq') && typeof this.syjl[prop] === 'string' && statusMappings[this.syjl[prop]]) {
            this.syjl[prop] = statusMappings[this.syjl[prop]];
          }
        }

        this.qualitycontrol()
        listZyjc({bah: this.syjl.bah, brbs: this.syjl.brbs}).then(response => {
          if (response.total > 0) {
            this.brzyxx = response.rows[0]
          }
        });
      });
    },
    zyztFormat(val) {
      if(val == 1) {
        return '离院'
      } else if(val == 0) {
        return '在院'
      } else {
        return val
      }
    },
    async qualitycontrol() {

      console.log(this.$store.state.user)

      let cCode = "";
      let show_syybkfxx = "0";
      if (this.$store.state.user.roles[0] == 'bary' && this.$store.state.user.roles.length == 1) {
        cCode = "show_syybkfxx_ba";
      } else {
        cCode = "show_syybkfxx";
      }
      const option = await getOption(cCode);
      if (option.hasOwnProperty("data")) {
        show_syybkfxx = option.data.cValue;
      }

      this.bafs = 100
      this.zkxx = []
      bazk({brbs: this.syjl.brbs, bah: this.syjl.bah, brid: this.syjl.brid}).then(response => {
        let zkxxRes = response.rows;
        for (let i = 0; i < zkxxRes.length; i++) {
          if (show_syybkfxx == "0" && zkxxRes[i].code.indexOf("lzd") > -1) {
            continue
          }

          if (zkxxRes[i].code.indexOf("tip") > -1) {
            continue
          }
          if (zkxxRes[i].score == null) {
            zkxxRes[i].score = ''
          } else {
            this.bafs -= zkxxRes[i].score
          }
          this.zkxx.push({
            field: zkxxRes[i].field,
            errordes: zkxxRes[i].errordes,
          })
        }
        if (this.zkxx.length == 0) {
          this.zkxx.push({
            field: "病案问题数量为零",
            errordes: "病案问题数量为零"
          })
        }
      });
    },
    formatDate(date) {
      if (date < 10) {
        return '0' + date
      } else
        return date
    },
    Grouping() {
      getdk().then(response => {
        if (response == undefined || response == null || response == "") {
          this.$modal.msgError("前端端口配置错误");
          return
        }
        this.dkh = response.rows[0]
        getFzPageIp().then(response => {
          if (response == undefined || response == null || response == "") {
            this.$modal.msgError("分组页面IP地址配置错误");
            return
          }
          window.location.href = "http://" + response + ":" + this.dkh + "/views/drg/yfz/index?id=2&bah=" + this.syjl.bah + "&brbs=" + this.syjl.brbs;
          // window.open("http://" + response + ":" + this.dkh + "/views/drg/yfz/index?id=1&bah=" + this.syjl.bah + "&brbs=" + this.syjl.brbs);
        });
      });
    },
    Qualitycontrol() {
      var zk = this.queryParams
      zk.brbs = this.syjl.brbs
      zk.brid = this.syjl.brid
      zk.bah = this.syjl.bah
      bazk(zk).then(response => {
        if (response.rows.length == 0) {
          this.$modal.msgSuccess("病人标识：" + this.syjl.brbs + "，问题数量：0");
        } else {
          this.wtList = []
          this.open = true
          this.wtList = response.rows
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
  },
  created() {
    // this.queryParams.bah = this.$route.query.bah
    if (this.$route.query.bah) {
      this.queryParams.bah = this.$route.query.bah
    }
    if (this.$route.query.brbs) {
      this.queryParams.brbs = this.$route.query.brbs
    }
    this.getBaData()
  }
}
</script>


<style lang="scss">
.content {
  display: flex;
}

.rightContent {
  width: 20%;
  min-height: 500px;
  border: solid 1px rgba(0, 0, 0, 0.7);
  margin: 5px;
  padding: 10px;

  .fzxx {
    div {
      span:nth-child(1) {
        font-weight: bold;
      }
    }
  }

  .zkxx {
    .zkxxItem {
      margin-top: 10px;
      border-left: solid 4px #3A71A8;

      div {
        span:nth-child(1) {
          font-weight: bold;
        }
      }
    }
  }
}

.mainContent {
  border: solid 1px rgba(0, 0, 0, 0.7);
  width: 80%;
  min-width: 70%;
  margin: 5px;
  padding: 10px;
  font-family: '幼圆';
  font-size: 11px;
}

input {
  border: none;
  border-bottom: solid 1px #000;
  font-weight: bold;
  font-family: '幼圆';
  background-color: #FFFFFF;
  text-align: center;
}

.head {
  width: 100%;

  .row1 {
    text-align: center;
  }

  .row2 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-top: 5px;
    text-align: center;
  }

  .row4 {
    display: flex;
    justify-content: space-between;
  }
}

.main {
  border: solid 1px #000;
  margin-top: 5px;

  div {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    padding-right: 5px;
    padding-left: 5px;
  }

  .zd, .ss, .data14, .data16, .addData18, .data17 {
    border-top: solid 1px #000;
    border-left: solid 1px #000;
    width: 100%;
    border-spacing: 0px;

    td, th {
      border-bottom: solid 1px #000;
      border-right: solid 1px #000;
      height: 25px;
      font-weight: normal;
    }
  }

  .zd, .ss {
    td {
      font-weight: bold;
    }
  }


  .data14, .addData18 {
    tr {
      td {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}

.fytype {
  background-color: #f8f8f8;
  width: 10% !important;
}
.fyvalue {
  font-weight: bold;
  font-size: 13px;
  color: rgba(0,0,0,.7);
}
</style>


