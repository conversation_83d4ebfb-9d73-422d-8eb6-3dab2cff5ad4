<template>
  <div class="content" v-loading="loading">
    <el-tabs type="card" @tab-click="handleTabClick" v-model="zyjcactiveName">
      <el-tab-pane label="分组信息" name="fzxx">
        <el-form v-if="brshow" style="border-bottom: solid #ccc 1px;padding-top: 10px" :model="queryParams"
                 ref="queryForm"
                 size="small" :inline="true" label-width="68px">

          <el-form-item label="病案号" prop="zyh">
            <el-input
              style="width: 100px"
              v-model="queryParams.bah"
              placeholder="病案号"
              clearable
              @keyup.enter.native="searchBr"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="xm">
            <el-input
              style="width: 120px"
              v-model="queryParams.xm"
              placeholder="姓名"
              clearable
              @keyup.enter.native="searchBr"
            />
          </el-form-item>
          <el-form-item label="在院状态" prop="zyzt">
            <el-select style="width: 120px" v-model="queryParams.zyzt" @change="updateZyzt" placeholder="在院状态"
                       clearable
                       class="input-with-select">
              <el-option value="0" label="出院">出院</el-option>
              <el-option value="1" label="在院"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="科室" prop="cykb">
            <el-select style="width: 120px" v-model="queryParams.cykb" placeholder="科室" clearable
                       class="input-with-select">
              <el-option v-for="item in ksList" :value="item.cykb">{{ item.cykb }}</el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="审核状态" v-if="checkRoleItem('bary')">
            <el-select v-model="auditStatus" placeholder="请选择" clearable
                       class="input-with-select" style="width: 120px">
              <el-option v-for="(item, index) in ybqdStatusOptions" :label="item.label" :value="item.value"/>
<!--              <el-option label="未提交" value="9" />-->
<!--              <el-option label="未审核" value="0" />-->
<!--              <el-option label="已通过" value="1" />-->
<!--              <el-option label="已打回" value="2" />-->
            </el-select>
          </el-form-item>
          <el-form-item label="出院时间" prop="cydate" v-if="cydateShow">
            <el-date-picker
              v-model="queryParams.cydateStart"
              type="datetime"
              placeholder="请选择开始日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
            -
            <el-date-picker
              v-model="queryParams.cydateEnd"
              type="datetime"
              placeholder="请选择截至日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <el-button type="primary" icon="el-icon-search" size="mini" @click="searchBr">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>

        </el-form>


        <div class="main">

          <el-button v-if="!isOpenBrList" style="font-size: 15px;color: #000;margin-left: 0;margin-top: 15px;"
                     size="mini" @click="updateBrListStatus">
            患<br>者<br>列<br>表<br>
          </el-button>
          <div v-if="brshow&&isOpenBrList" class="brList" style="margin-top: 15px;">
            <el-table v-loading="brListLoading" height="550" :data="brList" highlight-current-row @row-click="setBrxx">
              <el-table-column label="病案号" align="center" prop="bah">
                <template slot-scope="scope">
                  <span :class="getBahHighlightClass(scope.row)">{{ scope.row.bah }}</span>
                </template>
              </el-table-column>
              <el-table-column label="姓名" align="center" prop="xm"/>
            </el-table>

            <el-pagination
              style="text-align: center"
              small
              :page-size.sync="queryParams.pageSize"
              :current-page.sync="queryParams.pageNum"
              :total="total"
              :pager-count="5"
              @current-change="getBrList"
              layout="prev, pager, next">
            </el-pagination>

            <el-button
              style="width:100%; border: none; font-size: 15px;color: #000;margin-left: 0;border-bottom: solid 1px #ccc"
              size="mini" @click="updateBrListStatus">
              隐藏患者列表
            </el-button>
          </div>

          <div v-loading="brInfoLoading" style="display: flex; width: 100%">

            <div class="brxx">
              <table style="width: 98%">
                <tr>
                  <td style="background-color: #f8f8f9">姓名</td>
                  <td style="min-width: 40px">{{ brxx.xm }}</td>
                  <td style="background-color: #f8f8f9">住院号</td>
                  <td style="min-width: 40px">{{ brxx.bah }}</td>
                  <td style="background-color: #f8f8f9">性别</td>
                  <td style="min-width: 40px">{{ brxx.xb }}</td>
                  <td style="background-color: #f8f8f9">年龄</td>
                  <td style="min-width: 40px">{{ brxx.nl }}</td>
                </tr>

                <tr>
                  <td style="background-color: #f8f8f9">住院科室</td>
                  <td colspan="3">{{ brxx.cykb == null ? brxx.rykb : brxx.cykb }}</td>
                  <td style="background-color: #f8f8f9">住院医生</td>
                  <td>{{ brxx.zyys }}</td>
                  <td style="background-color: #f8f8f9">住院次数</td>
                  <td>{{ brxx.zycs }}</td>
                </tr>

                <tr>
                  <td style="background-color: #f8f8f9">入院时间</td>
                  <td colspan="2">{{ brxx.rydate }}</td>
                  <td style="background-color: #f8f8f9">出院时间</td>
                  <td colspan="4">{{ brxx.zyzt == 1 ? '' : brxx.cydate }}</td>

                  <!-- <td  style="background-color: #f8f8f9">结算时间</td>
                  <td>{{brxx.jsdate}}</td> -->

                </tr>

                <!--          <tr>-->
                <!--            <td colspan="8" v-if="shzt==0"></td>-->
                <!--            <td colspan="8" v-if="shzt==1" style="color:#00afff;">已重新提交</td>-->
                <!--            <td colspan="8" v-if="shzt==2" style="color:#ff0000;">已被打回：{{advice}}</td>-->
                <!--          </tr>-->
              </table>

              <table style="width: 98%">
                <tr style="background-color: #f8f8f9">
                  <th>分组名称</th>
                  <th>总费用/标杆费用</th>
                  <th>床日/标杆床日</th>
                </tr>
                <tr>
                  <td style="width: 60%">
                    <span v-if="fzxx.drgbh!='unidifined'&&fzxx.drgbh!=''&&fzxx.drgbh!=null">[{{ fzxx.drgbh }}]</span>
                    <span v-if="fzxx.drgmc!='unidifined'&&fzxx.drgmc!=''&&fzxx.drgmc!=null">{{ fzxx.drgmc }}</span>
                  </td>
                  <td style="width: 20%">
                    <span>{{ fzxx.zfy }}</span>
                    <span v-if="fzxx.drgmc!='unidifined'&&fzxx.drgmc!=''&&fzxx.drgmc!=null">{{ '/' + (fzxx.zfbz|| '未知') }}</span>
                  </td>
                  <td style="width: 20%">
              <span v-if="fzxx.drgmc!='unidifined'&&fzxx.drgmc!=''&&fzxx.drgmc!=null">{{
                  fzxx.zydays + '/' + (fzxx.pjdays|| '未知')
                }}</span>
                  </td>
                </tr>
              </table>

              <br>

              <div>
                <el-button type="info" plain @click="sync">同步</el-button>
                <!--                    <el-button type="warning" plain @click="zzdtj">诊断推荐</el-button>-->
                <el-button type="primary" plain @click="toJsQd">结算清单</el-button>
                <el-button v-if="btnDisplay != '0' && showBasyBtn == '1'" type="success" plain @click="toBasy">病案首页</el-button>
                <el-button v-if="btnDisplay != '0' && showYjbm" type="warning" plain @click="oneClickEncoding">AI编码</el-button>
                <!--          <el-button type="primary" plain>查看评价结果</el-button>-->
                <el-button v-if="qdflag=='1'" type="success" plain @click="medicalListSave">保存清单</el-button>
                <!-- 医生保存清单 -->
                <el-button v-if="(yhbs === '1' || yhbs === '') && isDoctor" type="warning" plain @click="doctorSaveList">医生保存清单</el-button>
                <el-button v-if="qdflag=='1' && showListSaveCancel" type="success" plain @click="listSaveCancel">取消保存</el-button>
                <!--          <el-button v-else type="success" plain @click="save">保存</el-button>-->
                <el-button type="danger" plain @click="grouping()">智能分组</el-button>
                <el-button v-if="checkRoleItem('bary') && useYbqd == '0'" type="success" plain @click="submitCheck">提交审核</el-button>
                <el-button v-if="audit == 1" type="primary" plain @click="handleAudit('1')">通过</el-button>
                <el-button v-if="audit == 1" type="warning" plain @click="handleAudit('2')">打回</el-button>
                <el-button v-if="this.role == 'bary' && fzshStatus !== undefined" plain>{{
                    handleFzshStatusShow()
                  }}
                </el-button>
                <el-button v-if="qdShzt == 1" type="text" plain><span style="color: red">*清单已保存</span></el-button>
                <!--          <el-button v-if="role !== 'ys' && btnDisplay != '0'" type="primary" plain @click="saveToHisList">提交到清单</el-button>-->
              </div>


              <br>

              <div>
                <el-checkbox v-if="yhbs==1" border label="使用ICD临床码" @change="setIcd"
                             v-model="useICD"></el-checkbox>
                <el-checkbox v-else border label="使用ICD医保码" @change="setIcd" v-model="useICD"></el-checkbox>
                <el-checkbox border label="显示推荐手术" @change="setTjss" v-model="isShowTjss"></el-checkbox>
              </div>


              <div v-if="!useICD" class="zdsj" style="display: flex">
                <div style="width: 98%">
                  <table>
                    <tr style="background-color: #f8f8f9">
                      <td>次序</td>
                      <td>疾病编码</td>
                      <td>编码名称</td>
                      <td>入院病情</td>
                      <td>CC/MCC表</td>
                      <td></td>
                    </tr>
                    <tr v-if="bah==null&&brbs==null">
                      <td colspan="6">请选择患者</td>
                    </tr>
                    <tr v-if="item.zdmc!=null&&item.jbbm!=null" v-for="(item,index) in ybzdxx2">
                      <td>{{ item.zdcx }}</td>
                      <td @dblclick="displayDyxx(item,1)">{{ item.jbbm }}</td>
                      <!--                <td style="font-size: 13px" @dblclick="displayDyxx(item,1)">{{ item.zdmc }}</td>-->
                      <!--                <td style="font-size: 13px" @dblclick="displayDyxx(item,1)">{{ item.zdmc }}</td>-->
                      <td style="font-size: 13px" @dblclick="displayDyxx(item,3)">
                        <el-autocomplete
                          :popper-append-to-body="false"
                          class="inline-input"
                          v-model="item.zdmc"
                          :fetch-suggestions="getZdxx"
                          placeholder="请输入出院诊断"
                          :trigger-on-focus="false"
                          @select="updateZdxx(index,$event)"
                          style="width: 100%"
                          popper-class="custom-autocomplete-popper"
                        ></el-autocomplete>
                      </td>
                      <td style="width:20%;font-size: 13px">
                        <el-select placeholder="入院病情" v-model="item.rybq" style="width: 100%">
                          <el-option value="1" label="有">有</el-option>
                          <el-option value="2" label="情况不明">情况不明</el-option>
                          <el-option value="3" label="临床未确定">临床未确定</el-option>
                          <el-option value="4" label="院内发病">院内发病</el-option>
                        </el-select>
                      </td>
                      <td v-if="index!=0">{{ item.type == 1 ? 'MCC' : item.type == 2 ? 'CC' : '' }}</td>
                      <td v-if="index==0"></td>
                      <td>
                        <button v-if="1>0" @click="moveUp(index,1)">
                          <i class="el-icon-top"></i>
                        </button>
                        <button @click="moveDown(index,1)">
                          <i class="el-icon-bottom"></i>
                        </button>
                        <button @click="insert(index,1)">
                          <i class="el-icon-plus"></i>
                        </button>
                        <button v-if="1>0" @click="remove(index,1)">
                          <i class="el-icon-close"></i>
                        </button>
                        <button v-if="1>0" @click="top(index,1)">
                          <i class="el-icon-arrow-up"></i>
                        </button>
                      </td>
                    </tr>
                    <tr v-else>
                      <td>{{ item.zdcx }}</td>
                      <td>{{ newzdxx.jbbm }}</td>
                      <td>
                        <el-autocomplete
                          :popper-append-to-body="false"
                          class="inline-input"
                          v-model="newzdxx.zdmc"
                          :fetch-suggestions="getZdxx"
                          placeholder="请输入出院诊断"
                          :trigger-on-focus="false"
                          @select="handleSelectZd"
                          style="width: 100%"
                          popper-class="custom-autocomplete-popper"
                        ></el-autocomplete>
                      </td>
                      <td>

                      </td>
                      <td>{{ newzdxx.type == 1 ? 'MCC' : newzdxx.type == 2 ? 'CC' : '' }}</td>
                      <td>
                        <button @click="cancel(index,1)">
                          <i class="el-icon-close"></i>
                        </button>
                      </td>
                    </tr>
                  </table>
                  <table>
                    <tr style="background-color: #f8f8f9">
                      <td>次序</td>
                      <td>疾病编码</td>
                      <td>编码名称</td>
                      <td></td>
                    </tr>
                    <tr v-if="bah==null&&brbs==null">
                      <td colspan="5">请选择患者</td>
                    </tr>
                    <tr v-if="item.ssmc!=null&&item.ssbm!=null" v-for="(item,index) in ybssxx2">
                      <td>{{ item.sscx }}</td>
                      <td @dblclick="displayDyxx(item,2)">{{ item.ssbm }}</td>
                      <!--                <td style="font-size: 13px"  @dblclick="displayDyxx(item,2)">{{ item.ssmc }}</td>-->
                      <td style="font-size: 13px" @dblclick="displayDyxx(item,2)">
                        <el-autocomplete
                          :popper-append-to-body="false"
                          class="inline-input"
                          v-model="item.ssmc"
                          :fetch-suggestions="getSsxx"
                          placeholder="请输入出院诊断"
                          :trigger-on-focus="false"
                          @select="updateSsxx(index,$event)"
                          style="width: 100%"
                          popper-class="custom-autocomplete-popper"
                        ></el-autocomplete>
                      </td>
                      <td>
                        <button @click="moveUp(index,2)">
                          <i class="el-icon-top"></i>
                        </button>
                        <button @click="moveDown(index,2)">
                          <i class="el-icon-bottom"></i>
                        </button>
                        <button @click="insert(index,2)">
                          <i class="el-icon-plus"></i>
                        </button>
                        <button @click="remove(index,2)">
                          <i class="el-icon-close"></i>
                        </button>
                        <button @click="top(index,2)">
                          <i class="el-icon-arrow-up"></i>
                        </button>
                      </td>
                    </tr>
                    <tr v-else>
                      <td>{{ item.sscx }}</td>
                      <td>{{ newssxx.ssbm }}</td>
                      <td>
                        <el-autocomplete
                          :popper-append-to-body="false"
                          class="inline-input"
                          v-model="newssxx.ssmc"
                          :fetch-suggestions="getSsxx"
                          placeholder="手术名称"
                          :trigger-on-focus="false"
                          @select="handleSelectSs"
                          style="width: 100%;"
                          popper-class="custom-autocomplete-popper"
                        ></el-autocomplete>
                      </td>
                      <td>
                        <button>
                          <i class="el-icon-close" @click="cancel(index,2)"></i>
                        </button>
                      </td>
                    </tr>
                  </table>

                  <table class="tjss" v-if="isShowTjss">
                    <tr v-if="tjss.length > 0">
                      <td colspan="7">
                        <el-button style="width: 100%;font-size: 15px" type="info" plain @click="oneClickAddSs">一键添加手术
                        </el-button>
                      </td>
                    </tr>
                    <tr style="background-color: #f8f8f9">
                      <td>诊断编码</td>
                      <td>诊断名称</td>
                      <td>推荐手术编码</td>
                      <td>推荐手术名称</td>
                      <td>费用项目名称</td>
                      <td>金额</td>
                      <td></td>
                    </tr>
                    <tr v-for="(item,index) in tjss">
                      <td>{{ item.bzbm }}</td>
                      <td>{{ item.bzmc }}</td>
                      <td>{{ item.ssbm }}</td>
                      <td @dblclick="displayDyxx(item,2)">{{ item.ssmc }}</td>
                      <td>{{ item.xmmc }}</td>
                      <td>{{ item.je }}</td>
                      <td>
                        <button @click="insertTjss(index)">
                          <i class="el-icon-plus"></i>
                        </button>
                      </td>
                    </tr>
                    <tr v-if="tjss.length == 0">
                      <td colspan="7">没有推荐手术</td>
                    </tr>
                  </table>
                </div>
              </div>
              <div v-else class="zdsj" style="display: flex">
                <div style="width: 98%">
                  <table>
                    <tr style="background-color: #f8f8f9">
                      <td>次序</td>
                      <td>疾病编码</td>
                      <td>编码名称</td>
                      <td>CC/MCC表</td>
                    </tr>
                    <tr v-for="(item,index) in icd10">
                      <td>{{ item.zdcx }}</td>
                      <td>{{ item.jbbm }}</td>
                      <td style="font-size: 13px">{{ item.zdmc }}</td>
                      <td v-if="index!=0">{{ item.type == 1 ? 'MCC' : item.type == 2 ? 'CC' : '' }}</td>
                      <td v-if="index==0"></td>
                    </tr>
                  </table>
                  <table>
                    <tr style="background-color: #f8f8f9;">
                      <td>次序</td>
                      <td>疾病编码</td>
                      <td>编码名称</td>
                    </tr>
                    <tr v-for="(item,index) in icd09">
                      <td>{{ item.sscx }}</td>
                      <td>{{ item.ssbm }}</td>
                      <td style="font-size: 13px">{{ item.ssmc }}</td>
                    </tr>
                  </table>
                </div>
              </div>
              <div v-if="tip" class="tip">
                <ul>
                  <li v-if="fzshow == 2 && item.indexOf('null') == -1" v-for="item in fztip">
                    {{ item }}
                  </li>
                  <li>
                    质控信息:
                    <div style="margin-top: 5px;" v-for="(item,index) in zkxx">
                      <span style="color: black;">[{{ index + 1 }}] </span>
                      <span>{{ item }}</span>
                    </div>
                  </li>
                </ul>
                <div style="margin-left: 15px;margin-bottom: 10px;color: red;" v-if="fzxx.drgmc!=null&&fzxx.drgmc!=''">
                  {{ fzxx.drgmc }}-----{{ fzxx.drgbh }}
                </div>
              </div>
            </div>

            <el-button v-if="!isOpenRightTab" style="font-size: 15px;color: #000;margin-left: 0;margin-top: 15px;"
                       size="mini" @click="showRightTab">
              患<br>者<br>详<br>情<br>
            </el-button>
            <div v-if="isOpenRightTab" class="tab" style="margin-top: 15px;border-bottom: solid 1px #ccc">
              <el-tabs v-loading="tabLoading" style="width: 100%;min-height: 600px" type="card" v-model="activeName"
                       @tab-click="handleClick">
                <el-tab-pane label="诊断费用" name="zdFyxx">
                  <div style="display: flex;align-items: center">
                    <el-button style="margin-left: 20px" plain @click="getDetails">诊断详情</el-button>
                    <el-button style="margin-left: 20px" plain @click="getfyfx_dzsz_fyxx">费用分析</el-button>
                    <el-button style="margin-left: 20px" plain @click="toFysh">费用审核</el-button>
                    <el-button v-if="!settlePlaceShow" @click="showSettlePlace" type="text" plain size="mini">查看极高费用情况</el-button>
                    <span v-if="settlePlaceShow" @click="showSettlePlace">当前极高费用情况 {{ exInfo }}</span>
                  </div>
                  <!-- 费用进度条 -->
                  <div style="margin-top: 10px; padding: 0 40px;">
                    <cost-progress-bar :total-cost="fzxx.zfy || 0"
                                       :low-cost="fzxx.jdje || 0"
                                       :high-cost="fzxx.jgje || 0"/>
                  </div>
<!--                  <div style="display: flex;align-items: center; margin-top: 10px">-->
<!--                    <el-button v-if="brxx.drgzf < brxx.tczf" style="margin-left: 20px" plain @click="getBaKsyy">亏损分析</el-button>-->
<!--                  </div>-->
                  <!-- <el-button style="margin-right: 20px" primary @click="showPayDetail">付费详情</el-button> -->
                  <el-table v-loading="zdFyxxLoading" style="margin-top: 10px" :data="zdFyxx" @row-click="getZdItemFyxx">
                    <el-table-column show-overflow-tooltip label="诊断名称" align="center" prop="zdmc"/>
                    <el-table-column show-overflow-tooltip label="诊断编码" align="center" prop="jbbm"/>
                    <el-table-column show-overflow-tooltip label="费用金额" align="center" prop="je"/>
                  </el-table>
                  <br>
                  <div>
                    <div style="width: 100%;height: 40px;line-height: 40px;text-align: left;margin-left: 10px;">
                      病案校验提示
                      <el-button style="margin-left: 20px" plain @click="displaySelectInfo">查看选择原则</el-button>
                      <el-button  v-if="drgqy === '重庆'"  style="margin-left: 20px" plain @click="displayzybztip">中医病组说明</el-button>
                    </div>
                    <el-table height="500" :data="bajytip" @row-click="getTipLinkData">
                      <el-table-column width="100px;" show-overflow-tooltip label="提示对象" align="left"
                                       prop="tipObject"/>
                      <el-table-column label="提示" align="left" prop="errordes"/>
                    </el-table>
                  </div>
                  <div v-if="zdtjtip.length > 0 && this.znbmFlagVal == 1">
                    <div style="width: 100%;height: 40px;line-height: 40px;text-align: left;margin-left: 10px;">诊断推荐提示
                    </div>
                    <el-table :data="zdtjtip">
                      <el-table-column label="提示" align="center" prop="tip"/>
                    </el-table>
                  </div>
                  <div v-if="zntjzd.length > 0 && this.znbmFlagVal == 1">
                    <div style="width: 100%;height: 40px;line-height: 40px;text-align: left;margin-left: 10px;">诊断推荐
                    </div>
                    <el-table :data="zntjzd">
                      <el-table-column show-overflow-tooltip label="诊断名称" align="center" prop="zdmc"/>
                      <el-table-column show-overflow-tooltip label="诊断编码" align="center" prop="jbbm"/>
                      <el-table-column show-overflow-tooltip label="诊断类型" align="center">
                        <template slot-scope="scope">
                          {{ scope.$index == 0 ? "主诊断" : "其他诊断" }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <div v-if="zntjss.length > 0 && this.znbmFlagVal == 1">
                    <div style="width: 100%;height: 40px;line-height: 40px;text-align: left;margin-left: 10px;">手术推荐
                    </div>
                    <el-table :data="zntjss">
                      <el-table-column show-overflow-tooltip label="手术名称" align="center" prop="ssmc"/>
                      <el-table-column show-overflow-tooltip label="手术编码" align="center" prop="ssbm"/>
                    </el-table>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="病程记录" name="bcjl">
                  <el-button style="margin-left: 20px" plain @click="identifyZdxx">识别诊断</el-button>
                 <el-button style="margin-left: 20px" plain @click="openDialog" >AI助手</el-button>
                  <el-button style="margin-left: 20px" plain @click="openccmcc" >CCMCC查询</el-button>
                  <el-descriptions :column="1" style="margin-top: 10px" border>
                    <el-descriptions-item label-class-name="descriptions-item-label" label="主述">
                      {{ this.brData ? this.brData.blzs : null }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="descriptions-item-label" label="入院诊断">
                      {{ this.brData ? this.brData.blryzd : null }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="descriptions-item-label" label="出院诊断">
                      {{ this.brData ? this.brData.blcyzd : null }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="descriptions-item-label" label="手术记录">
                      {{ this.brData ? this.brData.blssjl : null }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="descriptions-item-label" label="现病史">
                      {{ this.brData ? this.brData.blxbs : null }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="descriptions-item-label" label="既往史">
                      {{ this.brData ? this.brData.bljws : null }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="descriptions-item-label" label="诊疗经过">
                      {{ this.brData ? this.brData.blzljg : null }}
                    </el-descriptions-item>
                  </el-descriptions>
                </el-tab-pane>
                <el-tab-pane label="费用信息" name="fyxx">
                  <el-table style="margin-top: 10px" :data="kmfyandbg">
                    <el-table-column show-overflow-tooltip label="科目" align="center" prop="fykmname"/>
                    <el-table-column show-overflow-tooltip label="费用" align="center" prop="je"/>
                    <el-table-column show-overflow-tooltip label="标杆" align="center" prop="sl"/>
                  </el-table>
                  <br>
                  <el-table :data="fyxx" height="600">
                    <el-table-column show-overflow-tooltip label="项目名称" sortable align="center" prop="xmmc"
                                     width="200px"/>
                    <el-table-column show-overflow-tooltip label="费用类别" sortable align="center" prop="fykmname"
                                     :filters="[{ text: '西药费', value: '西药费' }, { text: '中药费', value: '中药费' }, { text: '中草药费', value: '中草药费' }, { text: '实验室诊断费', value: '实验室诊断费' }
              , { text: '影像学诊断费', value: '影像学诊断费' }, { text: '一般治疗操作费', value: '一般治疗操作费' }
              , { text: '一般医疗服务费', value: '一般医疗服务费' }
              , { text: '手术费', value: '手术费' }
              , { text: '治疗费', value: '治疗费' }
              , { text: '非手术治疗项目费', value: '非手术治疗项目费' }
              , { text: '手术用一次性医用材料费', value: '手术用一次性医用材料费' }
              , { text: '检查用一次性医用材料费', value: '检查用一次性医用材料费' }
              , { text: '治疗用一次性医用材料费', value: '治疗用一次性医用材料费' }
              , { text: '血费', value: '血费' }]"
                                     :filter-method="filterfyxx_fykmname_Tag"/>
                    <el-table-column width="80px" show-overflow-tooltip label="金额" sortable align="center" prop="je"/>
                    <el-table-column width="70px" show-overflow-tooltip label="数量" sortable align="center" prop="sl"/>
                    <el-table-column width="50px" show-overflow-tooltip label="价格" sortable align="center"
                                     prop="price"/>






                  </el-table>
                </el-tab-pane>
                <el-tab-pane  label="临床路径" name="lclj">
                  <div v-if="path != null">
                    <el-tabs type="card" v-model="activeName2">
                      <el-tab-pane v-for="(item,index) in path" :label="item.name" :name="index + ''">
                        <el-scrollbar style="height: 650px;">
                          <div v-html="item.content"></div>
                        </el-scrollbar>
                      </el-tab-pane>
                    </el-tabs>
                  </div>
                  <div v-else>
                    <el-empty :description="pathDesc"></el-empty>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="检验信息" name="jyxx">
                  <el-table :data="jyxx" height="600">
                    <el-table-column show-overflow-tooltip label="项目名称" align="center" prop="xmmc"/>
                    <el-table-column show-overflow-tooltip width="80px" label="合格范围" align="center" prop="jyfw"/>
                    <el-table-column show-overflow-tooltip width="60px" label="结果" align="center" prop="jyjg"/>
                    <el-table-column show-overflow-tooltip width="60px" label="单位" align="center" prop="jydw"/>
                    <el-table-column show-overflow-tooltip width="60px" label="标志" align="center" prop="jgbz"/>
                    <!--              <el-table-column show-overflow-tooltip width="60px" label="医生" align="center" prop="doctor" />-->
                  </el-table>
                </el-tab-pane>
              </el-tabs>
              <el-button
                style="width:100%; border: none; font-size: 15px;color: #000;margin-left: 0;border-bottom: solid 1px #ccc"
                size="mini" @click="showRightTab">
                隐藏患者详情
              </el-button>
            </div>


          </div>

        </div>
      </el-tab-pane>
      <el-tab-pane label="病人列表" name="zyjcbrlist">
        <div style="margin-bottom: 5px; margin-left: 5px">
          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleZyjcExport">导出
          </el-button>
        </div>
        <el-table :border="true" v-loading="zyjcbrlistLoading" :data="zyjcList">
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100px">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="zyjcyfz(scope.row)"
              >预分组
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="住院号" align="center" prop="zyh" show-overflow-tooltip/>
          <el-table-column label="姓名" align="center" prop="xm" show-overflow-tooltip/>
          <el-table-column label="性别" align="center" prop="xb" show-overflow-tooltip/>
          <!--  <el-table-column label="年龄" align="center" width="60px" prop="nl" show-overflow-tooltip/> -->
          <el-table-column label="科室" align="center" prop="cykb" show-overflow-tooltip/>
          <el-table-column label="医生" align="center" prop="zyys" show-overflow-tooltip/>
          <!--     <el-table-column label="DRG名称" align="center" prop="drgmc" width="200px" show-overflow-tooltip/> -->
          <el-table-column label="DRG编号" align="center" prop="drgbh" show-overflow-tooltip/>
          <el-table-column label="距上次天数" align="center" prop="jlsccyts" width="120px" show-overflow-tooltip>
            <template slot-scope="scope">
              <span :class="scope.row.jlsccyts<32?'blueFont':scope.row.jlsccyts">{{
                  scope.row.jlsccyts == null ? `/` : scope.row.jlsccyts
                }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总费用" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
          <span
            :class="scope.row.zfy>scope.row.zfbz?'redFont':scope.row.zfy>(scope.row.zfbz*0.8)?'yellowFont':''">{{
              scope.row.zfy
            }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标杆费用" align="center" prop="zfbz" show-overflow-tooltip/>

          <el-table-column label="费用进度" align="center" width="140px">
            <template slot-scope="scope">
              <el-progress v-if="scope.row.zfy!=null&&scope.row.zfbz!=null&&scope.row.zfy!=0&&scope.row.zfbz!=0"
                           :percentage="Math.round(scope.row.zfy / scope.row.zfbz * 100)>100?100:Math.round(scope.row.zfy / scope.row.zfbz * 100)"></el-progress>
            </template>
          </el-table-column>


          <el-table-column label="耗材费" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
          <span
            :class="scope.row.hcf>scope.row.bghcf?'redFont':scope.row.hcf>(scope.row.bghcf*0.8)?'yellowFont':''">{{
              scope.row.hcf
            }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标杆耗材" align="center" prop="bghcf" show-overflow-tooltip/>
          <el-table-column label="药品费" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
          <span
            :class="scope.row.ypf>scope.row.bgypf?'redFont':scope.row.ypf>(scope.row.bgypf*0.8)?'yellowFont':''">{{
              scope.row.ypf
            }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标杆药品费" align="center" prop="bgypf" width="120px" show-overflow-tooltip/>
          <el-table-column label="检查费" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
          <span
            :class="scope.row.jcf>scope.row.bgjcf?'redFont':scope.row.jcf>(scope.row.bgjcf*0.8)?'yellowFont':''">{{
              scope.row.jcf
            }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标杆检查费" align="center" prop="bgjcf" width="120px" show-overflow-tooltip/>
          <el-table-column label="检验费" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
          <span
            :class="scope.row.jyf>scope.row.bgjyf?'redFont':scope.row.jyf>(scope.row.bgjyf*0.8)?'yellowFont':''">{{
              scope.row.jyf
            }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标杆检验费" align="center" prop="bgjyf" width="120px" show-overflow-tooltip/>
          <el-table-column label="诊断名称" align="center" prop="zdmc" width="200px" show-overflow-tooltip/>
          <el-table-column label="手术名称" align="center" prop="ssmc" width="200px" show-overflow-tooltip/>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="fetchzycjbrlist"
        />
      </el-tab-pane>

      <el-tab-pane label="科室在院病人统计" name="deptDrgZyQueryList">
        <el-table :border="true" v-loading="deptDrgZyQueryLoading" :data="deptDrgZyQueryList">
          <el-table-column sortable width="250px" label="科室" align="center" prop="cykb"/>
          <el-table-column sortable label="在院人数" align="center" prop="zyrs">
            <template slot-scope="scope">
              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'zyrs')"> {{ scope.row.zyrs }}</div>
            </template>
          </el-table-column>
          <el-table-column sortable label="未入组人数" align="center" prop="wrzrs">
            <template slot-scope="scope">
              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'wrzrs')"> {{ scope.row.wrzrs }}</div>
            </template>
          </el-table-column>
          <el-table-column sortable label="超标杆人数" align="center" prop="cbgrs">
            <template slot-scope="scope">
              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'cbgrs')"> {{ scope.row.cbgrs }}</div>
            </template>
          </el-table-column>
          <el-table-column sortable label="重复住院人数" align="center" prop="cfzyrs">
            <template slot-scope="scope">
              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'cfzyrs')"> {{ scope.row.cfzyrs }}</div>
            </template>
          </el-table-column>

<!--          <el-table-column sortable label="无/低指征人数" align="center" prop="wzzrs">-->
<!--            <template slot-scope="scope">-->
<!--              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'wzzrs')"> {{ scope.row.wzzrs }}</div>-->
<!--            </template>-->
<!--          </el-table-column>-->


          <el-table-column sortable label="费用预警(超0.8)" align="center" prop="fyyjrs" width="200px">
            <template slot-scope="scope">
              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'fyyjrs')"> {{ scope.row.fyyjrs }}</div>
            </template>
          </el-table-column>
          <el-table-column sortable label="低倍率人数(住院天数过半)" align="center" prop="tsgbrs" width="210px">
            <template slot-scope="scope">
              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'tsgbrs')"> {{ scope.row.tsgbrs }}</div>
            </template>
          </el-table-column>
          <el-table-column sortable label="高倍率人数" align="center" prop="gblrs">
            <template slot-scope="scope">
              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'gblrs')"> {{ scope.row.gblrs }}</div>
            </template>
          </el-table-column>
          <el-table-column sortable label="违规数" align="center" prop="wgs">
            <template slot-scope="scope">
              <div class="dept-drg-num" @click="deptZyBrListQuery(scope.row,'wgs')"> {{ scope.row.wgs }}</div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>


    <el-dialog title="识别诊断" :visible.sync="open" :close-on-click-modal="false" v-dialogDrag append-to-body>
      <div v-for="(item,index) in allZdList">
        <div v-if="index==0 && allZdList[index].length > 0">出院诊断</div>
        <div v-if="index==1 && allZdList[index].length > 0">入院诊断</div>
        <div v-if="index==2 && allZdList[index].length > 0">现病史</div>
        <div v-if="index==3 && allZdList[index].length > 0">诊疗经过</div>
        <span v-for="(item2,index2) in item" v-if="allZdList[index].length > 0">
          <el-checkbox v-if="item2.zdmc!=null&&item2.zdmc!=''" class="zdsb" :label="item2.zdmc"
                       @change="changeZdsbSelect(index,index2)" border></el-checkbox>
        </span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="诊断费用" :visible.sync="zdItemFyOpen" @close="closeZdFyxx" :close-on-click-modal="false" append-to-body>
      <el-table :data="zdItemFyxx" height="400px">
        <el-table-column label="项目名称" align="center" prop="xmmc"/>
        <el-table-column label="诊断名称" align="center" prop="bzmc"/>
        <el-table-column label="疾病编码" align="center" prop="bzbm"/>
        <el-table-column label="金额" align="center" prop="je"/>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              v-if="allRoleDelZyxh"
              @click="delProFeeItem(scope.row,scope.$index)"
            >删除</el-button>
            <el-button
              size="mini"
              v-else
              type="text"
              @click="delProFeeItem(scope.row,scope.$index)"
              v-has-role="['admin']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog title="费用分析" :visible.sync="fyxx_dzsz_open" :close-on-click-modal="false" append-to-body width="80%">
      <el-button
        style="margin-bottom: 10px"
        type="warning"
        plain
        icon="el-icon-download"
        size="mini"
        @click="exportFyfx"
      >导出
      </el-button>
      <el-table :data="fyxx_dzsz" height="600">
        <el-table-column sortable label="使用人数占比[总人数]" align="center" prop="syrszb"/>
        <el-table-column sortable label="项目名称" align="center" prop="xmmc"/>
        <el-table-column sortable label="科目" align="center" prop="fykmname"/>
        <el-table-column sortable label="其他人平均金额" align="center" prop="qtrpjje"/>
        <el-table-column sortable label="该病人金额" align="center" prop="gbrje"/>
        <el-table-column sortable label="其他人平均使用天数" align="center" prop="qtrpjts"/>
        <el-table-column sortable label="该病人使用天数" align="center" prop="gbrts"/>
        <el-table-column sortable label="标志" align="center" prop="bz"
                         :filters="[{ text: '多做', value: '多做' }, { text: '未做', value: '未做' }, { text: '费用较多', value: '费用较多' }, { text: '费用较少', value: '费用较少' }]"
                         :filter-method="filterfyxx_dzsz_Tag"/>
      </el-table>
    </el-dialog>


    <el-dialog title="诊断详情" :visible.sync="zdDetailsOpen" width="1500px" :close-on-click-modal="false"
               append-to-body>
      <tabs ref="tabs" :jzh="jzh"></tabs>
    </el-dialog>

    <el-dialog title="项目付费详情" :visible.sync="settleInfoDetailShow" width="1300px">
      <el-button
        style="margin-bottom: 10px"
        type="warning"
        plain
        icon="el-icon-download"
        size="mini"
        @click="handleSettleExport"
      >导出
      </el-button>
      <el-table :data="settleInfoDetail">
        <el-table-column label="姓名" align="center" prop="xm"/>
        <el-table-column label="病人标识" align="center" prop="brbs"/>
        <el-table-column label="病人id" align="center" prop="brid"/>
        <el-table-column label="出院日期" align="center" prop="cydate" width="180px"/>
        <el-table-column label="总费用" align="center" prop="zfy"/>
        <el-table-column label="支付标准" align="center" prop="zfbz"/>
        <el-table-column label="住院id" align="center" prop="zyid"/>
        <el-table-column label="主要诊断" align="center" prop="zyzd"/>
        <el-table-column label="疾病代码" align="center" prop="jbdm"/>
        <el-table-column label="DRG编号" align="center" prop="drgbh"/>
        <el-table-column label="主要手术名称" align="center" prop="ssjczmc"/>
        <el-table-column label="主要手术编码" align="center" prop="ssjczbm"/>
      </el-table>

      <div class="page">
        <pagination
          v-show="settleInfoDetailTotal>1"
          :total="settleInfoDetailTotal"
          :page.sync="settleQueryParam.pageNum"
          :limit.sync="settleQueryParam.pageSize"
          @pagination="getSettleInfoDetail"
        />
      </div>
    </el-dialog>

    <el-dialog :title="dyxxTitle" :visible.sync="dyxxShow" width="1300px" @close="clearDyxx">

      <div v-if="role == 'admin'">
        <el-autocomplete
          v-if="dyxxFlag == 1"
          :popper-append-to-body="false"
          class="inline-input"
          v-model="newDyxx.value"
          :fetch-suggestions="getSsxx"
          placeholder="对应手术"
          :trigger-on-focus="false"
          @select="dyzdSelect"
          style="width: 80%; margin-left: 5%;"
        ></el-autocomplete>
        <el-autocomplete
          v-if="dyxxFlag == 2"
          :popper-append-to-body="false"
          class="inline-input"
          v-model="newDyxx.value"
          :fetch-suggestions="getZdxx"
          placeholder="对应诊断"
          :trigger-on-focus="false"
          @select="dyssSelect"
          style="width: 80%; margin-left:5%;"
        ></el-autocomplete>
        <el-button type="primary" @click="addDyxx" style="margin-bottom:20px">添加</el-button>
      </div>

      <el-table :data="dyxxList" height="450px">
        <el-table-column label="诊断编码" align="center" prop="bzbm"/>
        <el-table-column label="诊断名称" align="center" prop="bzmc"/>
        <el-table-column label="手术编码" align="center" prop="ssbm"/>
        <el-table-column label="手术名称" align="center" prop="ssmc"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="role == 'admin'" size="mini" type="text" @click="delDyxx(scope.row)">删除</el-button>
            <el-button size="mini" type="text" @click="selectDyxx(scope.row)">添加</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog title="治疗方案" :visible.sync="lcljFyxmShow" width="1300px" @close="clearDyxx">
      <el-table :data="lcljFyxmList" height="450px">
        <el-table-column sortable show-overflow-tooltip label="项目名称" align="left" prop="xmmc"/>
        <el-table-column sortable show-overflow-tooltip label="首次使用时间" align="left" prop="scsyts"/>
        <el-table-column sortable show-overflow-tooltip label="使用总天数" align="left" prop="syzts"/>
        <el-table-column sortable show-overflow-tooltip label="平均用量" align="left" prop="pjyl"/>
        <el-table-column sortable show-overflow-tooltip label="平均费用" align="left" prop="pjfy"/>
        <el-table-column sortable show-overflow-tooltip label="费用类别" align="left" prop="fykmname"/>
      </el-table>
    </el-dialog>

    <el-dialog title="提示信息" :visible.sync="displayTipData" width="1200px">
      <div v-html="tipData"></div>
    </el-dialog>


    <el-dialog title="诊断操作选择原则" :visible.sync="selectInfoOpen" width="1200px">
      <disAndOperSelectInfo></disAndOperSelectInfo>
    </el-dialog>


    <el-drawer
      :title="deptZyBrListDrawerTitle"
      :visible.sync="deptZyBrListVisible"
      direction="rtl"
      :wrapper-closable="false"
      size="95%">
      <div v-loading="deptZyBrQueryLoading">
        <el-table :border="true" :data="deptZyBrList">
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100px">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="zyjcyfz(scope.row)"
              >预分组
              </el-button>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="住院号" prop="bah"/>
          <el-table-column show-overflow-tooltip label="姓名" prop="xm"/>
          <el-table-column show-overflow-tooltip label="主要诊断" prop="zyzd"/>
          <el-table-column show-overflow-tooltip label="主要手术" prop="ssjczmc1"/>
          <el-table-column show-overflow-tooltip label="科室" prop="cykb"/>
          <el-table-column show-overflow-tooltip label="医生" prop="zyys"/>
          <el-table-column show-overflow-tooltip label="总费用" prop="zfy"/>
          <el-table-column show-overflow-tooltip label="标杆费用" prop="zfbz"/>
          <el-table-column label="费用进度" align="center" width="140px">
            <template slot-scope="scope">
              <el-progress
                v-if="scope.row.zfy != null && scope.row.zfbz != null && scope.row.zfy != 0 && scope.row.zfbz != 0"
                :percentage="Math.round(scope.row.zfy / scope.row.zfbz * 100)>100?100:Math.round(scope.row.zfy / scope.row.zfbz * 100)"></el-progress>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="DRG编码" prop="drgbh"/>
          <el-table-column show-overflow-tooltip label="DRG名称" prop="drgmc"/>
          <el-table-column show-overflow-tooltip label="住院天数" prop="sjzyts"/>
          <el-table-column show-overflow-tooltip label="标准床日" prop="pjdays"/>
          <el-table-column show-overflow-tooltip label="违规数" prop="wgs"/>
        </el-table>

        <pagination
          :total="deptZyBrListTotal"
          :page.sync="deptZyBrParam.pageNum"
          :limit.sync="deptZyBrParam.pageSize"
          @pagination="getDeptZyBrList"
        />
      </div>
    </el-drawer>


    <el-drawer
      title="亏损原因"
      :visible.sync="ksyyListVisible"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeKsyyDrawer"
      size="80%">
      <div style="margin: 1%">
        <el-table border ref="ksyyTable" :data="ksyyList">
          <el-table-column width="300px" sortable label="类型" align="left" prop="type"
                           :filters="[
                             { text: '超标杆床日', value: '超标杆床日' },
                             { text: '超科目费用', value: '超科目费用' },
                             { text: '项目多做', value: '项目多做' },
                             { text: '项目费用较多', value: '项目费用较多' },
                             { text: '并发症费用较多', value: '并发症费用较多' }]"
                           :filter-method="filterKsyyType"/>
          <el-table-column show-overflow-tooltip label="详情" prop="details"/>
        </el-table>
      </div>
    </el-drawer>

    <el-dialog
      title="AI智能助理"
      :visible.sync="chatVisible"
      width="1100px"
      custom-class="ai-dialog"
      @closed="handleClose">
      <div class="chat-container">
        <!-- 消息展示区域 -->
        <el-scrollbar ref="scrollbar" class="message-area" :wrap-style="[{ 'max-height': '1200px' }]">
          <div
            v-for="(msg, index) in messages"
            :key="index"
            class="message-wrapper"
            :class="[msg.type === 'user' ? 'user-message' : 'ai-message']"
          >
            <div class="message-bubble">
              <div v-if="msg.type === 'ai'" class="ai-avatar">
                <i class="el-icon-cpu"></i>
              </div>
              <div class="message-content">
                <div v-if="msg.type === 'ai'" class="message-header">智能助手</div>
                <div class="message-text" v-html="markdownToHtml(msg)"></div>
              </div>
              <div v-if="msg.type === 'user'" class="user-avatar">
                <i class="el-icon-user"></i>
              </div>
            </div>
          </div>
          <div v-if="chatLoading" class="loading-wrapper">
            <i class="el-icon-loading"></i>
            <span class="loading-text">请稍等...</span>
          </div>
        </el-scrollbar>

        <div class="btn-area">
          <el-select v-model="selectedQuestion" clearable
                     placeholder="请选择" size="mini"
                     @change="selectChatQuestion"
                     round
                     style="margin-right: 10px">
            <el-option
              v-for="item in questionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

<!--          <el-button-->
<!--            size="mini" round-->
<!--            :type="chatAI.withMr ? 'primary' : 'default'"-->
<!--            @click="getALlBlnr"-->
<!--          >完整病历</el-button>-->

          <el-button
            size="mini" round
            :type="chatAI.withMr ? 'primary' : 'default'"
            @click="getBlzs"
          >主述</el-button>

          <el-button
            size="mini" round
            :type="chatAI.withMr ? 'primary' : 'default'"
            @click="getBljws"
          >既往史</el-button>

          <el-button
            size="mini" round
            :type="chatAI.withMr ? 'primary' : 'default'"
            @click="getBlxbs"
          >现病史</el-button>

          <el-button
            size="mini" round
            :type="chatAI.withMr ? 'primary' : 'default'"
            @click="getBlzljg"
          >诊疗经过</el-button>
          <el-button
            size="mini" round
            :type="chatAI.withFee ? 'primary' : 'default'"
            @click="getChatFyxx"
          >费用</el-button>
          <el-button
            size="mini" round
            :type="chatAI.withDiag ? 'primary' : 'default'"
            @click="getChatDiag"
          >诊断</el-button>

          <el-button
            size="mini" round
            :type="chatAI.withMr ? 'primary' : 'default'"
            @click="getChatJyxx"
          >检验</el-button>

          <el-button
            size="mini" round
            :type="chatAI.withMr ? 'primary' : 'default'"
            @click="getChatJcxx"
          >检查</el-button>
          <el-button
            style="margin-left: auto;margin-right: 10px"
          type="primary"
          :loading="chatLoading"
          @click="sendMessage"
          icon="el-icon-s-promotion"
        ></el-button>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <el-input
            v-model="chatInputMsg"
            :autosize="{ minRows: 2, maxRows: 12}"
            placeholder="请输入内容"
            type="textarea"
            style="flex-grow: 1;margin-right: 10px;margin-left: 10px"
          >

          </el-input>
<!--          <el-button-->
<!--            style="flex-shrink: 0"-->
<!--            size="small"-->
<!--            type="primary"-->
<!--            :loading="chatLoading"-->
<!--            @click="sendMessage"-->
<!--            icon="el-icon-s-promotion"-->
<!--          ></el-button>-->
        </div>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import {basySync} from "@/api/system/sync"
import {
  bazk,
  getBaKsyy,
  drgfz,
  getBrInfo,
  getdk,
  getFzPageIp,
  getJsId,
  listCykb,
  listSyjl,
  selectDeptDrgZyQueryVo,
  selectDeptZyBrList,
  sync,
  patList
} from '@/api/drg/syjl'
import {listBrxx, updateBrxxShzt} from "@/api/mlcx/brxx";
import {getjbbmmlList} from "@/api/mlcx/bzml";
import {listSsxxByLc, listSsxxByYb, listSsxxSy, listSsxxYb, listTjss} from "@/api/drg/ssjl";
import { icd09yb, icd10yb, listBzType, listZdxxByLc, listZdxxByYb, listZdxxSy, listZdxxYb, saveZd, selectBrzdfy, selectYbzdByLczd, selectZdFyxx, zzdtj} from "@/api/drg/brzdxx";
import {getOption} from "@/api/system/option";
import Icons from "@/views/components/icons";
import {listDrgdict} from "@/api/drg/drgdict";
import {jyxxAll} from '@/api/mlcx/jyxx'
import {allJcxx} from '@/api/mlcx/jcxx'
import tabs from "@/components/DetailsTabs/tabs"
import {getInfoCost, getMonthInfoDetail} from '@/api/drg/xmffMx'
import { listCancel, saveDoctorList, saveToList } from '@/api/drg/yfz'
import {addIcddyss, delIcddyss, listIcddyss2} from "@/api/system/icddyss";
import {getPathWay} from "@/api/clinicalPath/pathways"
import {delProFeeItem, selectFyxxByBr, selectkmfyandbg, selectzdlcljfy} from "@/api/fygl/fyxx";
import disAndOperSelectInfo from "@/views/drg/yfz/disAndOperSelectInfo.vue"
import {handleAudits, listFzsh, submitFzsh} from '@/api/system/fzsh'
import {listLcljFyxmByzd} from "@/api/clinicalPath/lcljfyxm";
import {getUserDeptList} from "@/api/system/hdept";
import {listZyjc} from "@/api/zyjc/zyjc";
import { getCurDayStr, getMonthAgoDateStr, getTodayLastSecondStr } from '@/utils/dateUtils'
import {checkRoleItem, checkRoleYsAndKzr} from "@/utils/permission";
import { chat, chatTest, chatV1, getFyxxTen, getJcxxForChat, getJyxxForChat } from '@/api/llm/ai'
import { marked } from 'marked'
import { getLlmChat, listLlmChat } from '@/api/system/llmChat'
import { getYfzOptions } from '@/utils/ybqdStatusOptions'
import { MessageBox } from 'element-ui'
import {
    getMenuIp,
  } from "@/api/system/menu";
  import {getToken} from "@/utils/auth";
import CostProgressBar from "@/components/CostProgressBar/CostProgressBar.vue";

export default {
  name: "index",
  components: {CostProgressBar, Icons, tabs, disAndOperSelectInfo},
  inject: ['reload'],
  data() {
    return {
      takeData: 0,
      takeDiagInfo: [],
      takeSurgInfo: [],
      ybqdTimeout: '9',
      showBasyBtn: '0',
      useYbqd: '0',
      ybqdStatusOptions: [],
      selectedQuestion: '',
      questionOptions: [],
      brListLoading: false,
      chatAI: {
        withMr: false,
        withFee: false,
        withDiag: false,
        brid: '',
        zyid: '',
        message: ''
      },
      chatInputMsg: '',
      messages: [],
      chatLoading: false,
      showYbkfxx: true,
      isOpenRightTab: true,
      brInfoLoading: false,
      tabLoading: false,
      deptZyBrParam: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        cykb: null,
        zyys: checkRoleYsAndKzr('ys') ? this.$store.state.user.nickName : null
      },
      deptZyBrListTotal: 0,
      deptZyBrList: [],
      deptZyBrQueryLoading: false,
      deptZyBrListVisible: false,
      deptZyBrListDrawerTitle: '',
      deptDrgQueryParam: {
        cykbList: null,
        zyys: checkRoleYsAndKzr('ys') ? this.$store.state.user.nickName : null
      },
      audit: 0,
      znbmFlagVal: null,
      pathInfo: "临床路径信息",
      qdShzt: 0,
      path: null,
      pathDesc: "请选择患者",
      newDyxx: {
        value: null,
        mc: null,
        bm: null,
        ssbm: null,
        ssmc: null,
        bzbm: null,
        bzmc: null,
        xmbm: null,
        bzbmssbm: null,
        flag: 100
      },
      btnDisplay: "1",
      hasMultiOrg: "0",
      drgqy:"",
      settleInfoDetailShow: false,
      cydateShow: true,
      zdDetailsOpen: false,
      fyxx_dzsz_open: false,
      zdItemFyOpen: false,
      zdItemFyxx: [],
      fyxx_dzsz: [],
      pxzd: "",
      zntjzd: [],
      zntjss: [],
      zdtjtip: [],  //诊断推荐提示
      bajytip: [],  //病案校验提示
      dyxxList: [],
      dyxxTitle: "对应信息",
      dyxxFlag: null,
      dyxxShow: false,
      fzq: "默认分组器", //分组器
      ksList: [],
      role: "",
      yhbs: null,   //用户标识
      qdflag: null,   //清单标识
      yhxm: null,   //用户姓名
      yhkb: null,   //用户科室
      loading: false,
      zyjcbrlistLoading: false,
      deptDrgZyQueryLoading: false,
      zdFyxxLoading: false,
      allZdList: [],
      open: false,
      zdFyxx: [],
      jcxx: [],
      jyxx: null,
      zkxx: [],
      tipList: [],
      tjss: [],
      tjssTem: [],
      isShowTjss: false,
      isOpenBrList: true,
      activeName: 'zdFyxx',
      activeName2: '1',
      zyjcactiveName: 'fzxx',
      fyxx: null,
      kmfyandbg: [],
      brxxIsNull: false,
      advice: null,
      shzt: null,
      fztip: [],
      fzshow: '',
      tipzyzdmc: '',
      tipzyzdbm: '',
      tip: false,
      icd10: [],
      icd09: [],
      useICD: false,
      brshow: false,
      bah: null,
      jgid: null,
      brbs: null,
      brid: null,
      zyid: null,
      jzh: null,
      brList: [],
      deptDrgZyQueryList: [],
      zyjcList: [],
      total: 0,
      brxx: {},
      ybssxx: [],
      ybssxx2: [],
      ybzdxx: [],
      ybzdxx2: [],
      oldZdxx: [],
      oldSsxx: [],
      syssxxBm: '',
      syzdxxBm: '',
      brData: null,
      newzdxx: {
        zdmc: null,
        jbbm: null,
        type: null,
        rybq: null
      },
      lcljFyxmList: [],
      lcljFyxmShow: false,
      newssxx: {
        ssmc: null,
        ssbm: null,
        type: null
      },
      fzxx: {
        code: '',
        drgbh: '',
        drgmc: '',
        fztype: '',
        message: '',
        pjdays: '',
        zfbz: '',
        zfqz: '',
        zfy: '',
        zydays: '',
        num: '',
        jgje: 0.0,
        jdje: 0.0
      },
      bassjl: {
        id: null,
        brbs: null,
        brid: null,
        zyid: null,
        zdcx: null,
        ssbm: null,
        ssmc: null,
        ssrq: null,
        ssjb: null,
        sskssj: null,
        ssjssj: null,
        sz: null,
        dyzs: null,
        dezs: null,
        qkyhdj: null,
        mzfs: null,
        mzfj: null,
        mzys: null,
        ssqk: null,
        ssbw: null,
      },
      brzdxx: {
        id: null,
        brbs: null,
        brid: null,
        zyid: null,
        zdlx: null,
        zdcx: null,
        jbbm: null,
        zdmc: null,
        rybq: null,
        cyqk: null,
        fm: null,
        bz: null,
        type: null
      },
      auditStatus: null,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jzh: null,
        brbs: null,
        brid: null,
        zyid: null,
        username: null,
        ylfkfs: null,
        jkkh: null,
        zycs: null,
        bah: null,
        xm: null,
        xb: null,
        csrq: null,
        nl: null,
        gj: null,
        bzyzsnl: null,
        xsecstz: null,
        xserytz: null,
        csd: null,
        gg: null,
        mz: null,
        sfzh: null,
        zy: null,
        hy: null,
        xzz: null,
        dh: null,
        yb1: null,
        hkdz: null,
        yb2: null,
        gzdwjdz: null,
        dwdh: null,
        yb3: null,
        lxrxm: null,
        gx: null,
        dz: null,
        dh2: null,
        rytj: null,
        rysj: null,
        rysjs: null,
        rykb: null,
        rybf: null,
        zkkb: null,
        cysj: null,
        cysjs: null,
        cykb: null,
        cybf: null,
        cydateStart: getMonthAgoDateStr(),
        cydateEnd: getTodayLastSecondStr(),
        sjzyts: null,
        mzzd: null,
        jbbm: null,
        zyzd: null,
        jbdm: null,
        rybq: null,
        qtzd1: null,
        jbdm1: null,
        rybq1: null,
        qtzd2: null,
        jbdm2: null,
        rybq2: null,
        qtzd3: null,
        jbdm3: null,
        rybq3: null,
        qtzd4: null,
        jbdm4: null,
        rybq4: null,
        qtzd5: null,
        jbdm5: null,
        rybq5: null,
        qtzd6: null,
        jbdm6: null,
        rybq6: null,
        qtzd7: null,
        jbdm7: null,
        rybq7: null,
        qtzd8: null,
        jbdm8: null,
        rybq8: null,
        qtzd9: null,
        jbdm9: null,
        rybq9: null,
        qtzd10: null,
        jbdm10: null,
        rybq10: null,
        qtzd11: null,
        jbdm11: null,
        rybq11: null,
        qtzd12: null,
        jbdm12: null,
        rybq12: null,
        qtzd13: null,
        jbdm13: null,
        rybq13: null,
        qtzd14: null,
        jbdm14: null,
        rybq14: null,
        qtzd15: null,
        jbdm15: null,
        rybq15: null,
        wbyy: null,
        h23: null,
        blzd: null,
        jbmm: null,
        blh: null,
        ywgm: null,
        gmyw: null,
        swhzsj: null,
        xx: null,
        rh: null,
        kzr: null,
        zrys: null,
        zzys: null,
        zyys: null,
        zrhs: null,
        jxys: null,
        sxys: null,
        bmy: null,
        bazl: null,
        zkys: null,
        zkhs: null,
        zkrq: null,
        ssjczbm1: null,
        ssjczrq1: null,
        ssjb1: null,
        ssjczmc1: null,
        sz1: null,
        yz1: null,
        ez1: null,
        qkdj1: null,
        qkyhlb1: null,
        mzfs1: null,
        mzys1: null,
        ssjczbm2: null,
        ssjczrq2: null,
        ssjb2: null,
        ssjczmc2: null,
        sz2: null,
        yz2: null,
        ez2: null,
        qkdj2: null,
        qkyhlb2: null,
        mzfs2: null,
        mzys2: null,
        ssjczbm3: null,
        ssjczrq3: null,
        ssjb3: null,
        ssjczmc3: null,
        sz3: null,
        yz3: null,
        ez3: null,
        qkdj3: null,
        qkyhlb3: null,
        mzfs3: null,
        mzys3: null,
        ssjczbm4: null,
        ssjczrq4: null,
        ssjb4: null,
        ssjczmc4: null,
        sz4: null,
        yz4: null,
        ez4: null,
        qkdj4: null,
        qkyhlb4: null,
        mzfs4: null,
        mzys4: null,
        ssjczbm5: null,
        ssjczrq5: null,
        ssjb5: null,
        ssjczmc5: null,
        sz5: null,
        yz5: null,
        ez5: null,
        qkdj5: null,
        qkyhlb5: null,
        mzfs5: null,
        mzys5: null,
        ssjczbm6: null,
        ssjczrq6: null,
        ssjb6: null,
        ssjczmc6: null,
        sz6: null,
        yz6: null,
        ez6: null,
        qkdj6: null,
        qkyhlb6: null,
        mzfs6: null,
        mzys6: null,
        ssjczbm7: null,
        ssjczrq7: null,
        ssjb7: null,
        ssjczmc7: null,
        sz7: null,
        yz7: null,
        ez7: null,
        qkdj7: null,
        qkyhlb7: null,
        mzfs7: null,
        mzys7: null,
        lyfs: null,
        yzzyYljg: null,
        wsyYljg: null,
        sfzzyjh: null,
        md: null,
        ryqT: null,
        ryqXs: null,
        ryqF: null,
        ryhT: null,
        ryhXs: null,
        ryhF: null,
        zfy: null,
        zfje: null,
        ylfuf: null,
        zlczf: null,
        hlf: null,
        qtfy: null,
        blzdf: null,
        syszdf: null,
        yxxzdf: null,
        lczdxmf: null,
        fsszlxmf: null,
        wlzlf: null,
        sszlf: null,
        maf: null,
        ssf: null,
        kff: null,
        zyzlf: null,
        xyf: null,
        kjywf: null,
        zcyf: null,
        zcyf1: null,
        xf: null,
        bdblzpf: null,
        qdblzpf: null,
        nxyzlzpf: null,
        xbyzlzpf: null,
        hcyyclf: null,
        yyclf: null,
        ycxyyclf: null,
        qtf: null,
        psh: null,
        basytype: null,
        orgcode: null,
        bycode: null,
        opname: null,
        opdate: null,
        xgcs: null,
        cxflag: null,
        jsdate: null,
        cqflag: null,
        jyflag: null,
        datasrc: null,
        jxstatus: null,
        sfsslcljgl: null,
        sfwclclj: null,
        sfby: null,
        byyy: null,
        ljbzmc: null,
        rydate: null,
        cydate: null,
        drgbh: null,
        rzflag: null,
        wrzyy: null,
        tczf: null,
        drgzf: null,
        jystatus: null,
        jlly: null,
        qjcs: null,
        qjcgcs: null,
        qzrq: null,
        zyzt: null,
        zdf: null,
        hisJsdate: null
      },
      settlePlace: {          //极高费用限制信息
        thisMonthSettle: null,
        place: null,
        ExHigh: null
      },
      settleInfoDetail: [],  //当前病人付费详情
      settleInfoDetailTotal: 0,
      //极高费用限制显示
      settlePlaceShow: false,
      settleQueryParam: {
        pageNum: 1,
        pageSize: 20
      },
      exInfo: null,
      rowIndex: -1,
      displayTipData: false,
      tipData: "",
      selectInfoOpen: false,
      fzshStatus: undefined,
      showYjbm: true,
      allRoleDelZyxh: false,
      isDelProFeeItem: false,
      ksyyList: [],
      ksyyListVisible: false,
      chatVisible: false,
      showListSaveCancel: false
    }
  },
  methods: {
    isDoctor(){
      // console.log(this.yhbs, '222')
      return this.yhbs === "" || checkRoleYsAndKzr("ys") || checkRoleYsAndKzr("kzrys");
    },
    async doctorSaveList(){
      // console.log(this.isDoctor(), '888', this.yhbs, this.useYbqd)
      const res = await this.save();
      if(res !== 1) {
        this.$modal.msgError("保存清单失败")
      }

      //未选择病人时取消提交操作
      if (!this.brxx.brbs) {
        this.$modal.msgWarning("请先选择患者")
        return
      }
      // console.log('进入保存清单流程', this.brxx.brbs)

      const saveRes = await saveDoctorList(this.brxx.brbs)
      if(saveRes && saveRes.code == '200') {
        this.$modal.msgSuccess('保存成功');
      }else {
        this.$modal.msgError("保存失败")
      }
    },
    getLlmChatDict(){
      this.questionOptions = [];
      listLlmChat({pageSize: 50}).then(res => {
        res.rows.forEach(row => {
          this.questionOptions.push({
            key: row.value,
            value: row.value,
            label: row.label
          })
        })
      })
    },
    selectChatQuestion(){
      this.chatInputMsg = this.selectedQuestion + '\n' + this.chatInputMsg
    },
    getBljws(){
      if (this.brData.bljws === null) {
        return ;
      }
      this.chatInputMsg += '\n既往史：\n' + this.brData.bljws
    },
    getBlxbs(){
      if (this.brData.blxbs === null) {
        return ;
      }
      this.chatInputMsg += '\n现病史：\n' + this.brData.blxbs
    },
    getBlzljg(){
      if (this.brData.blzljg === null) {
        return ;
      }
      this.chatInputMsg += '\n诊疗经过：\n' + this.brData.blzljg
    },
    getBlzs(){
      if (this.brData.blzs === null) {
        return ;
      }
      this.chatInputMsg += '\n主述：\n' + this.brData.blzs
    },
    getAllBlnr(){
      this.chatInputMsg += '\n病历内容：\n' + this.brData.blxbs
    },
    getChatJyxx(){
      getJyxxForChat(this.brData.jzh).then(res => {
        let jyxxList = res.list
        if(jyxxList != null) {
          this.chatInputMsg += '\n病人检验信息：'
          jyxxList.forEach(jy =>{
            this.chatInputMsg += '\n' +
              jy.xmbh + '\t' +
              jy.xmmc + '\t' +
              jy.jyfw + '\t' +
              jy.jyjg + '\t' +
              jy.jgbz
          })
        }
      })
    },
    getChatJcxx(){
      getJcxxForChat(this.brData.jzh).then(res => {
        let jcxxList = res.list
        if(jcxxList != null) {
          this.chatInputMsg += '\n病人检查信息：'
          jcxxList.forEach(jc =>{
            this.chatInputMsg += '\n' +
              jc.xmmc + '\t' +
              jc.jcsj + '\t' +
              jc.jcjl
          })
        }
      })
    },
    getChatFyxx(){
      getFyxxTen(this.brData.brid, this.brData.zyid)
        .then(res => {
          let fyxxList = res.list
          if(fyxxList != null) {
            this.chatInputMsg += '\n病人费用信息：'
            this.chatInputMsg += '\n项目\t总金额\t总数量'
            fyxxList.forEach(fy =>{
              this.chatInputMsg += '\n' +
                fy.xmmc + '\t' +
                fy.je + '元\t' +
                fy.sl + fy.dw
            })
          }
        })
    },
    getChatDiag(){
      if(this.ybzdxx2 == null) {
        return
      }
      this.chatInputMsg += '\n病人诊断信息：'
      this.ybzdxx2.forEach(zd => {
        this.chatInputMsg += '\n' +
          zd.zdmc + '\t' + zd.jbbm
      })
    },
    selectChatWithMr() {
      this.chatAI.withMr = !this.chatAI.withMr
      console.log(this.chatAI)
    },
    selectChatWithFee() {
      this.chatAI.withFee = !this.chatAI.withFee
    },
    selectChatWithDiag() {
      this.chatAI.withDiag = !this.chatAI.withDiag
    },
    markdownToHtml(msg){
      if(msg.type === 'user') {
        return msg.content
      }
      let content = msg.content.replace('<br>', '')
      return marked(content)
    },
    openDialog() {
      this.chatVisible = true
      this.getLlmChatDict()
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    openccmcc() {
      getFzPageIp().then(response => {
        this.url = 'http://' + response + ':8096/jmreport/view/1106820316197036032';
        let token = getToken()
        if(token != undefined){
          this.url = this.url + '?token=' + token
        }
         window.open(this.url );
      })
    },

    handleClose() {
      this.selectedQuestion = ''
      this.messages = []
      this.chatInputMsg = ''
    },

    async sendMessage() {
      if (!this.chatInputMsg.trim()) return

      // 添加用户消息
      this.messages.push({
        type: 'user',
        content: this.chatInputMsg.trim()
      })

      let question = this.chatInputMsg
      this.chatInputMsg = ''
      this.chatLoading = true

      try {
        // 模拟AI回复（实际应替换为API调用）
        // console.log(question)
        // this.chatAI.brid = this.brxx.brid
        // this.chatAI.zyid = this.brxx.zyid
        // this.chatAI.message = question
        // console.log(this.chatAI)
        const res = await chatV1(question)

        // console.log(res, 'res')
        this.messages.push({
          type: 'ai',
          content: res.data.content
        })
      } finally {
        this.chatLoading = false
        this.scrollToBottom()
      }
    },

    scrollToBottom() {
      this.$nextTick(() => {
        const scrollbar = this.$refs.scrollbar
        if (scrollbar) {
          scrollbar.wrap.scrollTop = scrollbar.wrap.scrollHeight
        }
      })
    },
    async getBaKsyy() {
      this.loading = true
      if (!this.brxx.brbs) {
        this.$modal.msgWarning("请先选择病人")
        return
      }
      const res = await getBaKsyy(this.brxx.brbs).catch(err => {
        this.loading = false
      })
      if (res.code == 200) {
        this.loading = false
        this.ksyyList = res.rows
        this.ksyyListVisible = true
      }
    },
    closeZdFyxx() {
      if (this.isDelProFeeItem) {
        this.getZdFyxx()
        this.isDelProFeeItem = false
      }
    },
    async delProFeeItem(row,index) {
      const res = await delProFeeItem(row).catch(err => {
        this.$modal.msgError("删除失败！")
      })
      if (res.code == 200) {
        this.isDelProFeeItem = true
        this.$modal.msgSuccess("删除成功！")
        this.zdItemFyxx.splice(index,1)
      }
    },
    getPathCon() {
      const routeUrl = this.$router.resolve({
        path: "/views/pathway/pdf",
        query: {id: this.path.id}
      })
      window.open(routeUrl.href, '_blank')
    },
    checkRoleItem,
    showRightTab() {
      this.isOpenRightTab = !this.isOpenRightTab
    },
    deptZyBrListQuery(row, type) {
      this.deptZyBrParam.pageNum = 1
      this.deptZyBrParam.pageSize = 10
      this.deptZyBrParam.type = type
      this.deptZyBrParam.cykb = row.cykb
      this.deptZyBrParam.jgid = this.jgid
      let dec = ""
      if (type == "zyrs") {
        dec = "在院病人"
      } else if (type == "wrzrs") {
        dec = "在院未入组病人"
      } else if (type == "cbgrs") {
        dec = "在院超标杆病人"
      } else if (type == "cfzyrs") {
        dec = "在院重复住院病人"
      } else if (type == "fyyjrs") {
        dec = "在院费用预警人"
      } else if (type == "tsgbrs") {
        dec = "在院住院天数过半病人"
      } else if (type == "wgs") {
        dec = "在院病人违规数"
      }
      this.deptZyBrListDrawerTitle = row.cykb + '--' + dec
      this.getDeptZyBrList()
    },
    getDeptZyBrList() {
      this.deptZyBrListVisible = true
      this.deptZyBrQueryLoading = true
      selectDeptZyBrList(this.deptZyBrParam).then(res => {
        if (res.code == 200) {
          this.deptZyBrListTotal = res.total
          this.deptZyBrQueryLoading = false
          this.deptZyBrList = res.rows
        }
      })
    },
    submitCheck() {
      console.log("正在提交审核")
      console.log(this.fzxx, '分组信息')
      if (!this.fzxx.drgbh) {
        this.$modal.msgWarning("还未分组！")
        return
      }

      const params = {
        brid: this.brid,
        zyid: this.zyid,
        bah: this.bah,
        jzh: this.brbs,
        drgbh: this.fzxx.drgbh,
        drgmc: this.fzxx.drgmc,
        jbdm: this.ybzdxx2[0].jbbm,
        ssbm: this.ybssxx2[0].ssbm,
        zyzd: this.ybzdxx2[0].zdmc,
        zyss: this.ybssxx2[0].ssmc,
        zfy: this.fzxx.zfy,
        zfbz: this.fzxx.zfbz,
        zyh: this.brxx.bah,
        submitBy: this.$store.state.user.nickName  ? this.$store.state.user.nickName : this.$store.state.user.name,
        xm: this.brxx.xm
      }

      submitFzsh(params).then(res => {
        if (res.code === 200) {
          this.$modal.msgSuccess("提交审核成功")
        }
      })

      console.log(params, '提交参数')
    },
    toFysh() {
      if (!this.brxx.brbs) {
        this.$modal.msgWarning("请先选择病人")
        return
      }
      getFzPageIp().then(response => {
        // window.open("http://" + response + ":" + 8096 + "/gksz/fysh/getBrShxx?brbs=" + this.brxx.brbs);
        window.location.href="http://" + response + ":" + 8096 + "/gksz/fysh/getBrShxx?brbs=" + this.brxx.brbs
      });

    },
    handleAudit(val) {
      handleAudits({
        status: val,
        jzh: this.brbs
      }).then(res => {
        if (res.code === 200) {
          this.$modal.msgSuccess('操作成功')
        }
      })
    },
    displaySelectInfo() {
      this.selectInfoOpen = true;
    },
    displayzybztip() {
        getFzPageIp().then(response => {
        // window.open("http://" + response + ":" + 8096 + "/gksz/fysh/getBrShxx?brbs=" + this.brxx.brbs);
        window.location.href="http://" + response + ":" + 8096 + "/drg/syjl/pdf"
      });
    },
    patientExists() {
      if (this.bah == null && this.brbs == null) {
        this.$modal.msgWarning("请选择患者！")
        return false;
      }

      if ((this.bah == null && this.brbs != null) || (this.bah != null && this.brbs == null)) {
        if (this.brid == null && this.zyid == null) {
          this.$modal.msgWarning("当前患者不存在！")
          return false;
        }
      }

      return true;
    },
    checkParam() {
      if (this.bah == null && this.brbs == null) {
        this.$modal.msgWarning("请选择患者！")
        return false;
      }
      return true;
    },
    getTipLinkData(row) {
      if (row.islink == 1 && row.linkData != null) {
        this.displayTipData = true
        this.tipData = row.linkData
      }
    },
    updateZdxx(index, item) {
      this.ybzdxx2[index].zdmc = item.zdmc
      this.ybzdxx2[index].jbbm = item.jbbm
      listBzType(this.ybzdxx2[index]).then(response => {
        if (response.rows.length > 0) {
          this.ybzdxx2[index].type = response.rows[0].type
        }
      });
      this.ybzdxx = this.ybzdxx2
    },
    updateSsxx(index, item) {
      this.ybssxx2[index].ssmc = item.zdmc
      this.ybssxx2[index].ssbm = item.jbbm
      this.ybssxx = this.ybssxx2
    },
    async getClinicalPath() {
      if (!this.brxx) {
        this.$modal.msgWarning("请选择患者")
        return
      }
      const data = this.brxx
      data.baBrzdxxList = this.ybzdxx2
      data.baSsjlList = this.ybssxx2
      const res = await getPathWay(data).catch(err => {
        this.tabLoading = false
      })
      console.log(res)
      if (res.code == 200 && res.rows && res.rows.length > 0) {
        this.path = res.rows
        this.activeName2 = "0"
        this.tabLoading = false
      } else {
        this.path = null
        this.tabLoading = false
        this.pathDesc = "当前患者未入径"
      }
    },
    // 获取病人信息
    async getBrList() {
      this.brListLoading = true
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        zyys: this.yhxm == null ? null : this.yhxm,
        cykb: this.yhkb == null ? this.queryParams.cykb : this.yhkb,
        cykbList: this.deptDrgQueryParam.cykbList,
        zyzt: this.queryParams.zyzt ? this.queryParams.zyzt : null,
        cydateStart: this.queryParams.cydateStart,
        cydateEnd: this.queryParams.cydateEnd,
        auditStatus: this.auditStatus
      }
      const res = await patList(queryParams)
      this.brList = res.rows
      this.total = res.total
      this.brListLoading = false
    },
    // 根据条件查询全部病人
    async searchBr() {
      // 重置当前页
      this.brListLoading = true
      this.queryParams.pageNum = 1
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        bah: this.queryParams.bah,
        zyys: this.yhxm == null ? null : this.yhxm,
        cykb: this.yhkb == null ? this.queryParams.cykb : this.yhkb,
        cykbList: this.deptDrgQueryParam.cykbList,
        zyzt: this.queryParams.zyzt ? this.queryParams.zyzt : null,
        cydateStart: this.queryParams.cydateStart,
        cydateEnd: this.queryParams.cydateEnd,
        xm: this.queryParams.xm,
        auditStatus: this.auditStatus
      }
      const res = await patList(queryParams)
      this.brList = res.rows
      this.total = res.total
      this.brListLoading = false
    },
    setBrxx(row) {
      this.brInfoLoading = true
      this.qdShzt = 0
      this.rowIndex = this.brList.indexOf(row);
      this.clearBrData()
      this.bah = row.bah
      this.brid = row.brid
      this.brbs = row.brbs
      this.zyid = row.zyid
      this.tip = false
      this.getBrxx()
      // if(row.hisJsdate && row.hisJsdate !== '' && this.qdflag == 1) {
      //   // 获取当前时间
      //   const now = new Date();
      //   const hisJsdate = new Date(row.hisJsdate);
      //   // 计算天数差异
      //   const diffDays = Math.floor((now - hisJsdate) / (1000 * 60 * 60 * 24));
      //   // console.log(diffDays)
      //   if(diffDays >= 7) {
      //     this.$modal.msgError(`当前病人结算已超过7天未保存清单`)
      //   }
      // }
    },
    handleTabClick(tab) {
      if (this.hasMultiOrg === '1' && !this.jgid && this.$route.query.id == 1) {
        this.$modal.msgWarning("无法获取患者机构ID！")
        return
      }
      // 根据 tab 的 name 来判断需要查询哪个 tab 的数据
      if (tab.name === 'zyjcbrlist') {
        this.fetchzycjbrlist();
      } else if (tab.name === 'deptDrgZyQueryList') {
        this.deptDrgZyQueryLoading = true
        this.getDeptDrgZyQueryList()
      }
    },
    getDeptDrgZyQueryList() {
      console.log(this.deptDrgQueryParam)
      this.deptDrgQueryParam.jgid = this.jgid

      if (this.$route.query.id == 1) {
        this.deptDrgQueryParam.zyys = this.brxx.zyys;
        this.deptDrgQueryParam.cykbList = [this.brxx.cykb];
      }

      selectDeptDrgZyQueryVo(this.deptDrgQueryParam).then(res => {
        console.log(this.$store.state.user)
        if (res.code == 200) {
          this.deptDrgZyQueryList = res.rows
          this.deptDrgZyQueryLoading = false
        }
      })
    },
    /** 查询Zyjc列表 */
    fetchzycjbrlist() {
      this.zyjcbrlistLoading = true;
      listZyjc({
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        zyzt: '1',
        zyys: this.$route.query.id == 1 ? this.brxx.zyys : this.yhxm,
        cykb: this.$route.query.id == 1 ? this.brxx.cykb : this.yhkb,
        jgid: this.jgid
      }).then(response => {
        console.log("获取数据成功")
        this.zyjcList = response.rows;
        this.total = response.total;
        this.zyjcbrlistLoading = false;
      });
    },
    zyjcyfz(row) {
      console.log(row)
      getdk().then(response => {
        var dkh = response.rows[0]
        getFzPageIp().then(response => {
          // window.open("http://" + response + ":" + dkh + "/views/drg/yfz/index?brid=" + row.brid + "&brbs=" + row.brbs + "&bah=" + row.bah + "&id=1");
          window.location.href="http://" + response + ":" + dkh + "/views/drg/yfz/index?brid=" + row.brid + "&brbs=" + row.brbs + "&bah=" + row.bah + "&id=1"
        });
      });
    },
    async getBcjl() {
      let param = {
        brid: this.brid,
        zyid: this.zyid
      }
      const res = await listBrxx(param).catch(err => {
        this.tabLoading = false
      })
      if (res.code == 200 && res.total > 0) {
        this.brData = res.rows[0]
        var xbs = this.brData.blxbs
        if (xbs != null && xbs != "") {
          var xbsArr = xbs.split(/[，。]/);
          for (let i = 0; i < xbsArr.length; i++) {
            if (xbsArr[i].indexOf("无") != -1 || xbsArr[i].indexOf("没有") != -1) {
              xbsArr.splice(i, 1);
              i--
            }
          }
          this.brData.blxbs = xbsArr.join(",")
        }
        this.qdShzt = res.rows[0].shzt
        this.tabLoading = false
      } else {
        this.brData = null
        this.tabLoading = false
      }
    },
    async getBrxx(sync) {
      try {
        if ((this.$route.query.id == 1 && sync != 1 && this.$route.query.sync == "true") || (this.takeData == 1 && sync != 1)) {
          this.sync()
        }
        this.icd09 = [] //临床诊断
        this.icd10 = [] //临床手术
        if (!this.checkParam()) {
          return
        }
        let param = {
          brbs: this.brbs ? this.brbs : null,
          bah: this.bah && !this.brbs ? this.bah : null,
          type: this.yhbs
        }
        const res = await getBrInfo(param);
        if (res.code == 200 && res.total > 0) {
          this.brxx = res.rows[0]

          this.brid = this.brxx.brid
          this.zyid = this.brxx.zyid
          this.jzh = this.brxx.jzh
          this.bah = this.brxx.bah
          this.brbs = this.brxx.brbs
          this.jgid = this.brxx.username

          if(this.takeData == 1) {
            this.brxx.baBrzdxxList = this.takeDiagInfo
            this.brxx.baSsjlList = this.takeSurgInfo
          }
          if (this.brxx.baBrzdxxList) {
            this.ybzdxx = this.brxx.baBrzdxxList
            this.ybzdxx2 = this.ybzdxx
            this.oldZdxx = JSON.parse(JSON.stringify(this.ybzdxx)) //后面用来判断诊断、手术是否发生过修改
          }

          if (this.brxx.baSsjlList) {
            this.ybssxx = this.brxx.baSsjlList
            this.ybssxx2 = this.ybssxx
            this.oldSsxx = JSON.parse(JSON.stringify(this.ybssxx))//后面用来判断诊断、手术是否发生过修改
          }
          // console.log(this.ybssxx2, this.brxx.baSsjlList)

          if (this.ybzdxx2.length == 0) {
            this.insert(0, 1)
          }

          if (this.ybssxx2.length == 0) {
            this.insert(0, 2)
          }

          this.getZdFyxx()
          this.getFzshStatus()

          this.brInfoLoading = false

          if(this.takeData == 1) {
            this.oldZdxx = '{}'
            this.takeData = 0
            await this.grouping()
          }

        } else {
          this.$modal.alertError("患者不存在");
          this.brInfoLoading = false
          return
        }
        this.queryParams.bah = null
      } catch (e) {
        this.brInfoLoading = false
      }
    },
    handleClick(tab) {
      if (this.brbs) {
        this.tabLoading = true
        console.log(this.path)
        if (tab.name === 'zdFyxx') {
          this.tabLoading = false
        } else if (tab.name === 'bcjl' && this.brData === null) {
          this.getBcjl()
        } else if (tab.name === 'fyxx' && this.fyxx === null) {
          this.getFyxx()
          this.getkmfyandbg()
        } else if (tab.name === 'lclj' && this.path === null) {
          this.getClinicalPath()
        } else if (tab.name === 'jyxx' && this.jyxx === null) {
          this.getJyxx()
        } else {
          this.tabLoading = false
        }
      }
    },
    getFzshStatus() {
      this.fzshStatus = undefined
      listFzsh({
        jzh: this.jzh
      }).then(res => {
        if (res.rows.length > 0) {
          this.fzshStatus = res.rows[0].status
          if(this.fzshStatus == '0' || this.fzshStatus == '1') {
            this.qdShzt = 1
          }
        } else {
          if(this.brxx.hisJsdate && this.brxx.hisJsdate !== '' && this.qdflag == 1) {
            // 获取当前时间
            const now = new Date();
            const hisJsdate = new Date(this.brxx.hisJsdate);
            // 计算天数差异
            const diffDays = Math.floor((now - hisJsdate) / (1000 * 60 * 60 * 24));
            // console.log(diffDays)
            if(diffDays >= 7) {
              this.$modal.msgError(`当前病人结算已超过7天未保存清单`)
            }
          }
          this.fzshStatus = null
          console.log('未提交')
        }
      })
    },
    handleFzshStatusShow() {
      if (this.fzshStatus == '0') {
        return '已提交';
      } else if (this.fzshStatus == '1') {
        return '已通过';
      } else if (this.fzshStatus == '2') {
        return '已打回'
      } else {
        return '未提交'
      }
    },
    qualitycontrol() {
      this.bajytip = []
      this.zkxx = []
      var zk = this.queryParams
      zk.brbs = this.brbs
      zk.bah = this.bah
      zk.brid = this.brid
      zk.jlly = this.brxx.jlly
      bazk(zk).then(response => {
        if (response.total == 0) {
          this.zkxx.push("病案问题数量为零")
        } else {
          for (let i = 0; i < response.rows.length; i++) {
            let zkTip = response.rows[i]
            if (zkTip.code.indexOf("tip") > -1) {
              this.bajytip.push(zkTip)
              continue
            }
            if(zkTip.code.indexOf("lzd") > -1 && !this.showYbkfxx) {
              continue;
            }
            if (zkTip.score == null) {
              zkTip.score = ''
            }
            if (zkTip.score == '' || zkTip.score == '0') {
              this.zkxx.push(zkTip.source == '1' ? '[手动添加]' + zkTip.errordes : zkTip.errordes);
            } else {
              this.zkxx.push(zkTip.source == '1' ? ('[手动添加]' + zkTip.errordes + " 扣分:" + zkTip.score) : zkTip.errordes + " 扣分:" + zkTip.score)
            }
          }
          if (this.zkxx.length == 0) {
            this.zkxx.push("病案问题数量为零")
          }
        }
      });
    },
    closeKsyyDrawer() {
      this.$refs.ksyyTable.clearFilter();
    },
    filterKsyyType(value, row) {
      return row.type === value;
    },
    exportFyfx() {
      this.download('/gksz/fyxx/exportzdlcljfy', {
        brid: this.brid,
        zyid: this.zyid,
        jzh: this.jzh,
      }, `${this.brxx.xm}[${this.brxx.bah}]_费用分析_${new Date().getTime()}.xlsx`)
    },
    filterfyxx_dzsz_Tag(value, row) {
      return row.bz === value;
    },
    filterfyxx_fykmname_Tag(value, row) {
      return row.fykmname === value;
    },
    getJcxx() {
      allJcxx({jzh: this.jzh}).then(response => {
        this.jcxx = response.rows
      })
    },
    getJyxx() {
      jyxxAll({jzh: this.jzh}).then(response => {
        this.jyxx = response.rows
        this.tabLoading = false
      }).catch(err => {
        this.tabLoading = false
      })
    },
    getZdFyxx() {
      this.zdFyxxLoading = true
      var brxx = {
        brid: this.brid,
        brbs: this.brbs,
        zyid: this.brxx.zyid
      }
      selectZdFyxx(brxx).then(response => {
        this.zdFyxx = response.rows
        this.zdFyxxLoading = false
      }).catch(err => {
        this.zdFyxxLoading = false
      });
    },
    dataIsUpdate() {
      let newZdxx = (this.ybzdxx2.length == 1 && this.ybzdxx2[0].jbbm == null) ? [] : this.ybzdxx2;
      let newSsxx = (this.ybssxx2.length == 1 && this.ybssxx2[0].ssbm == null) ? [] : this.ybssxx2;

      let zdIsUpdate = !(JSON.stringify(this.oldZdxx) === JSON.stringify(newZdxx));
      let ssIsUpdate = !(JSON.stringify(this.oldSsxx) === JSON.stringify(newSsxx));

      return zdIsUpdate || ssIsUpdate
    },
    async grouping() {
      // plfz2().then(res=>{
      //
      // })
      //
      // return

      if (this.dataIsUpdate()) {
        // console.log("修改过")
        const saveRes = await this.save()
        if (saveRes !== 1) {
          return
        }
      }

      // console.log("分组")

      let i = 1;

      if (!this.patientExists()) {
        return
      }


      if (this.ybzdxx2.length == 0 || (this.ybzdxx2.length == 1 && (this.ybzdxx2[0].jbbm == "" || this.ybzdxx2[0].jbbm == null))) {
        this.$modal.msgWarning("诊断为空");
        return
      }

      this.loading = true

      getOption("fzq").then(response => {
        if (response.data.cValue != null && response.data.cValue != "") {
          i = response.data.cValue
        }
        if (i != 2 && i != 1 && i != 3) {
          i = 1;
        }
        this.qualitycontrol()
        this.fzshow = i       //选择显示分组器相应的提示界面
        this.tip = false      //隐藏提示

        let zdxx = ""
        let ssxx = ""
        for (let i = 1; i < this.ybzdxx2.length; i++) {
          if (this.ybzdxx2[i].jbbm != "" && this.ybzdxx2[i].jbbm != null)
            zdxx += this.ybzdxx2[i].jbbm.trim() + ','
        }
        for (let i = 0; i < this.ybssxx2.length; i++) {
          if (this.ybssxx2[i].ssbm != "" && this.ybssxx2[i].ssbm != null)
            ssxx += this.ybssxx2[i].ssbm.trim() + ','
        }
        if (zdxx.trim() != "")
          zdxx = zdxx.substring(0, zdxx.length - 1)
        if (ssxx.trim() != "")
          ssxx = ssxx.substring(0, ssxx.length - 1)

        let zzdbm = this.ybzdxx2[0].jbbm.trim()
        drgfz({
          brid: this.brid,
          zyid: this.zyid,
          bs: i,
          zzdbm: zzdbm,
          zdxx: zdxx,
          ssxx: ssxx,
          ybflag: this.yhbs,
          source: 'yfz'
        }).then(response => {
          console.log(response)
          if (response.code != 200) {
            this.$modal.msgWarning("分组失败");
            this.loading = false
            return
          }

          this.fzxx = {
            code: '',
            drgbh: '',
            drgmc: '',
            fztype: '',
            message: '',
            pjdays: '',
            zfbz: '',
            zfqz: '',
            zfy: '',
            zydays: '',
            num: '',
            jgje: 0.0,
            jdje: 0.0
          }

          if (i == 1) {
            this.fzxx = response.rows[0]
          } else if (i == 3) {
            this.fzxx = JSON.parse(response.rows[0])
            if (this.fzxx.pjdays != null && this.fzxx.pjdays != "") {
              if (this.fzxx.pjdays.indexOf("|") > -1) {
                this.fzxx.pjdays = this.fzxx.pjdays.split("|")[0]
              }
            }
          } else if (i == 2) {
            this.fztip = response.rows[0].messages
            if (this.fztip != null) {
              let drgbh = this.fztip[this.fztip.length - 1].replaceAll("*", "")
              listDrgdict({drgbh: drgbh.split(" ")[0]}).then(response => {
                if (response.rows.length > 0) {
                  let drgDict = response.rows[0]
                  this.fzxx = {
                    code: '',
                    drgbh: drgbh.split(" ")[0],
                    drgmc: drgbh.split(" ")[1],
                    fztype: '',
                    message: '',
                    pjdays: drgDict.pjdays,
                    zfbz: drgDict.zfbz,
                    zfqz: drgDict.zfqz,
                    zfy: this.brxx.zfy,
                    zydays: this.brxx.sjzyts,
                  };
                } else {
                  this.fzxx.drgbh = drgbh
                }
              })
            }
          }
          this.tip = true  //显示提示信息
          this.loading = false


        }).catch(err => {
          this.loading = false
          this.$modal.msgError("分组失败")
          this.$modal.msgError(err)
        })

      }).catch(err => {
        this.loading = false
        this.$modal.msgError(err)
      })
    },
    formatDate(date) {
      if (date < 10) {
        return '0' + date
      } else
        return date
    },
    moveUp(index, i) {
      if (index == 0) {
        this.$modal.msgWarning("已经到顶了！");
        return
      }
      if (i == 1) {
        var zdcx = this.ybzdxx[index].zdcx
        var zdcx2 = this.ybzdxx[index - 1].zdcx
        var temp = {}
        temp = this.ybzdxx[index]
        this.ybzdxx[index] = this.ybzdxx[index - 1]
        this.ybzdxx[index].zdcx = zdcx
        this.ybzdxx[index - 1] = temp
        this.ybzdxx[index - 1].zdcx = zdcx2
        this.ybzdxx2 = []
        this.ybzdxx2 = this.ybzdxx
      }
      if (i == 2) {
        var zdcx = this.ybssxx[index].sscx
        var zdcx2 = this.ybssxx[index - 1].sscx
        var temp = {}
        temp = this.ybssxx[index]
        this.ybssxx[index] = this.ybssxx[index - 1]
        this.ybssxx[index].sscx = zdcx
        this.ybssxx[index - 1] = temp
        this.ybssxx[index - 1].sscx = zdcx2
        this.ybssxx2 = []
        this.ybssxx2 = this.ybssxx
      }
    },
    moveDown(index, i) {
      if (i == 1) {
        if (index == this.ybzdxx.length - 1) {
          this.$modal.msgWarning("已经到底了！");
          return
        }
        var zdcx = this.ybzdxx[index].zdcx
        var zdcx2 = this.ybzdxx[index + 1].zdcx
        var temp = {}
        temp = this.ybzdxx[index]
        this.ybzdxx[index] = this.ybzdxx[index + 1]
        this.ybzdxx[index].zdcx = zdcx
        this.ybzdxx[index + 1] = temp
        this.ybzdxx[index + 1].zdcx = zdcx2
        this.ybzdxx2 = []
        this.ybzdxx2 = this.ybzdxx
      }
      if (i == 2) {
        if (index == this.ybssxx.length - 1) {
          this.$modal.msgWarning("已经到底了！");
          return
        }
        var zdcx = this.ybssxx[index].sscx
        var zdcx2 = this.ybssxx[index + 1].sscx
        var temp = {}
        temp = this.ybssxx[index]
        this.ybssxx[index] = this.ybssxx[index + 1]
        this.ybssxx[index].sscx = zdcx
        this.ybssxx[index + 1] = temp
        this.ybssxx[index + 1].sscx = zdcx2
        this.ybssxx2 = []
        this.ybssxx2 = this.ybssxx
      }
    },
    async remove(index, i) {
      let length = this.ybzdxx2.length
      if (i == 1) {
        this.ybzdxx.splice(index, 1)
        for (let j = index; j < this.ybzdxx.length; j++) {
          this.ybzdxx[j].zdcx--
        }
        this.ybzdxx2 = []
        this.ybzdxx2 = this.ybzdxx
        if (this.ybzdxx2.length == 0) {
          const res = await this.save()
          this.insert(0, 1)
          if (res === 1)
            this.$modal.msgSuccess("保存成功")
        }
      }
      if (i == 2) {
        this.ybssxx.splice(index, 1)
        for (let j = index; j < this.ybssxx.length; j++) {
          this.ybssxx[j].sscx--
        }
        this.ybssxx2 = []
        this.ybssxx2 = this.ybssxx
        if (this.ybssxx2.length == 0) {
          const res = await this.save()
          this.insert(0, 2)
          if (res === 1)
            this.$modal.msgSuccess("保存成功")
        }
      }
    },
    insert(index, i) {
      if (i == 1) {
        if (this.ybzdxx.length > 0) {
          if (this.ybzdxx[this.ybzdxx.length - 1].zdmc == null) {
            this.$modal.msgWarning("请填写空白记录");
            return
          }
        }
        this.ybzdxx.push({
          id: null,
          brbs: this.brbs,
          brid: this.brid,
          zyid: this.brxx.zyid,
          zdlx: '出院诊断',
          zdcx: this.ybzdxx2.length + 1,
          jbbm: null,
          zdmc: null,
          rybq: null,
          cyqk: null,
          fm: null,
          bz: null,
          type: null
        })
        this.ybzdxx2 = []
        this.ybzdxx2 = this.ybzdxx
      }
      if (i == 2) {
        if (this.ybssxx.length > 0) {
          if (this.ybssxx[this.ybssxx.length - 1].ssmc == null) {
            this.$modal.msgWarning("请填写空白记录");
            return
          }
        }
        this.ybssxx.push({
          id: null,
          brbs: this.brbs,
          brid: this.brid,
          zyid: this.brxx.zyid,
          sscx: this.ybssxx2.length + 1,
          ssbm: null,
          ssmc: null,
          ssrq: null,
          ssjb: null,
          sskssj: null,
          ssjssj: null,
          sz: null,
          dyzs: null,
          dezs: null,
          qkyhdj: null,
          mzfs: null,
          mzfj: null,
          mzys: null,
          ssqk: null,
          ssbw: null,
          type: null
        })
        this.ybssxx2 = []
        this.ybssxx2 = this.ybssxx
      }
    },
    top(index, i) {
      if (index == 0) {
        this.$modal.msgWarning("已经到顶了！");
        return
      }
      if (i == 1) {
        var el = this.ybzdxx[index]
        el.zdcx = 1
        this.remove(index, i)
        this.ybzdxx.unshift(el)
        for (let j = 1; j < this.ybzdxx.length; j++) {
          this.ybzdxx[j].zdcx++
        }
        this.ybzdxx2 = []
        this.ybzdxx2 = this.ybzdxx
      }
      if (i == 2) {
        var el = this.ybssxx[index]
        el.sscx = 1
        this.remove(index, i)
        this.ybssxx.unshift(el)
        for (let j = 1; j < this.ybssxx.length; j++) {
          this.ybssxx[j].sscx++
        }
        this.ybssxx2 = []
        this.ybssxx2 = this.ybssxx
      }
    },
    getZdxx(queryString, cb) {
      const fetchZdxx = this.yhbs === "1" ? listZdxxByYb : listZdxxByLc;
      fetchZdxx(queryString)
        .then(response => {
          const zdList = response.rows.map(item => ({
            value: item.zdstr,
            zdmc: item.zdmc,
            jbbm: item.jbbm
          }));
          cb(zdList);
        });
    },
    getSsxx(queryString, cb) {
      const fetchSsxx = this.yhbs === "1" ? listSsxxByYb : listSsxxByLc;
      fetchSsxx(queryString)
        .then(response => {
          const ssList = response.rows.map(item => ({
            value: item.ssstr,
            zdmc: item.ssmc,
            jbbm: item.ssbm
          }));
          cb(ssList);
        });
    },
    handleSelectZd(item) {
      for (let i = 0; i < this.ybzdxx2.length; i++) {
        if (this.ybzdxx2[i].zdmc == item.zdmc) {
          this.$modal.msgWarning("该诊断已存在");
          this.newzdxx.zdmc = null
          this.newzdxx.rybq = null
          return
        }
      }
      var length = this.ybzdxx2.length - 1
      this.ybzdxx2[length].zdmc = item.zdmc
      this.ybzdxx2[length].jbbm = item.jbbm
      this.ybzdxx2[length].rybq = this.newzdxx.rybq
      listBzType(this.ybzdxx2[length]).then(response => {
        if (response.rows.length > 0) {
          this.ybzdxx2[length].type = response.rows[0].type
        } else {
          this.ybzdxx2[length].type = null
        }
      });
      this.newzdxx.zdmc = null
      this.newzdxx.rybq = null
    },
    handleSelectSs(item) {
      for (let i = 0; i < this.ybssxx2.length; i++) {
        if (this.ybssxx2[i].ssmc == item.zdmc) {
          this.$modal.msgWarning("该手术已存在");
          this.newssxx.ssmc = null
          return
        }
      }
      var length = this.ybssxx2.length - 1
      this.ybssxx2[length].ssmc = item.zdmc
      this.ybssxx2[length].ssbm = item.jbbm
      this.newssxx.ssmc = null
    },
    confirm(index, i) {
      if (i == 1) {
        this.ybzdxx[index].zdmc = this.newzdxx.zdmc
        this.ybzdxx[index].jbbm = this.newzdxx.jbbm
        this.ybzdxx[index].type = this.newzdxx.type
        this.newzdxx = {
          zdmc: null,
          jbbm: null,
          type: null
        }
        this.ybzdxx2 = []
        this.ybzdxx2 = this.ybzdxx
      }
      if (i == 2) {
        this.ybssxx[index].ssmc = this.newssxx.ssmc
        this.ybssxx[index].ssbm = this.newssxx.ssbm
        this.ybssxx[index].type = this.newssxx.type
        this.newssxx = {
          ssmc: null,
          ssbm: null,
          type: null
        }
        this.ybssxx2 = []
        this.ybssxx2 = this.ybssxx
      }
    },
    cancel(index, i) {
      if (i == 1) {
        if (index == 0 && this.ybzdxx2[index].jbbm == null) {
          return
        }
        this.ybzdxx.splice(index, 1)
        for (let j = index; j < this.ybzdxx.length; j++) {
          this.ybzdxx[j].zdcx--
        }
        this.newzdxx = {
          zdmc: null,
          jbbm: null,
          type: null
        }
        this.ybzdxx2 = []
        this.ybzdxx2 = this.ybzdxx
      }
      if (i == 2) {
        if (index == 0 && this.ybssxx2[index].ssbm == null) {
          return
        }
        this.ybssxx.splice(index, 1)
        for (let j = index; j < this.ybssxx.length; j++) {
          this.ybssxx[j].sscx--
        }
        this.newssxx = {
          ssmc: null,
          ssbm: null,
          type: null
        }
        this.ybssxx2 = []
        this.ybssxx2 = this.ybssxx
      }
    },
    toJsQd() {
      if (!this.patientExists()) {
        return
      }
      getJsId({brid: this.brxx.brid, zyid: this.brxx.zyid}).then(response => {
        if (response == null || response == "") {
          this.$modal.msgWarning("该患者没有结算信息");
        } else {
          var jsid = response
          getdk().then(response => {
            var dkh = response.rows[0]
            getFzPageIp().then(response => {
              // window.open("http://" + response + ":" + dkh + "/views/jsxx/index?brid=" + this.brxx.brid + "&zyid=" + this.brxx.zyid + "&brbs=" + this.brxx.brbs);
              window.location.href="http://" + response + ":" + dkh + "/views/jsxx/index?brid=" + this.brxx.brid + "&zyid=" + this.brxx.zyid + "&brbs=" + this.brxx.brbs
            });
          })
        }
      })

    },
    toBasy() {
      if (!this.patientExists()) {
        return
      }
      getdk().then(response => {
        var dkh = response.rows[0]
        getFzPageIp().then(response => {
          console.log(this.bah)
          console.log(this.brbs)
          window.location.href="http://" + response + ":" + dkh + "/views/drg/bafx/index?bah=" + this.bah + "&brbs=" + this.brbs
          // window.open("http://" + response + ":" + dkh + "/views/drg/bafx/index?bah=" + this.bah + "&brbs=" + this.brbs);
        });
      })
    },
    async save() {
      if (!this.patientExists()) {
        return
      }

      this.loading = true
      if (this.newzdxx.jbbm != null) {
        this.confirm(this.ybzdxx2.length - 1, 1)
      }
      if (this.newssxx.ssbm != null) {
        this.confirm(this.ybssxx2.length - 1, 2)
      }

      var zdxxArr = []
      var ssxxArr = []
      for (let i = 0; i < this.ybzdxx2.length; i++) {
        var item = this.ybzdxx2[i]
        zdxxArr.push({zdmc: item.zdmc, jbbm: item.jbbm, zdcx: item.zdcx, rybq: item.rybq, jlr: item.jlr})
      }
      // console.log(this.ybssxx, '医保')
      // console.log(this.ybssxx2, '页面')
      for (let i = 0; i < this.ybssxx2.length; i++) {
        var item = this.ybssxx2[i]
        console.log(item, i)
        ssxxArr.push({
          ssmc: item.ssmc,
          ssbm: item.ssbm,
          sscx: item.sscx,
          sskssj: item.sskssj,
          ssjssj: item.ssjssj,
          mzkssj: item.mzkssj,
          mzjssj: item.mzjssj,
          mzfs: item.mzfs,
          mzys: item.mzys,
          sz: item.sz,
          ssrq: item.ssrq,
          mzysdm: item.mzysdm,
          zdysdm: item.zdysdm,
          ssjb: item.ssjb
        })
      }
      console.log(ssxxArr, '要保存的手术信息')

      var zdxx = JSON.stringify(zdxxArr)
      var ssxx = JSON.stringify(ssxxArr)

      const res = await saveZd(zdxx, ssxx, this.brid == null ? "" : this.brid, this.zyid == null ? "" : this.zyid, this.yhbs)

      // const listRes = await this.saveToHisList()
      // if(listRes.code === 200) {
      //   this.$modal.msgSuccess("提交成功，请注意提交后his端修改无法影响到分组页面的医保诊断")
      // }

      console.log('准备保存诊断')
      console.log(res)
      if (res == 1) {
        this.getZdFyxx()

        if (this.brData != null) {
          if (this.brData.shzt == "2") {
            this.brData.shzt == "1"
            updateBrxxShzt({shzt: "1", advice: "", brid: this.brid, zyid: this.zyid}).then(response => {
            })
          }
        }
        console.log('诊断保存成功，重新获取病人信息')

        await this.getBrxx()
      } else {
        this.$modal.msgError("保存失败");
        this.loading = false
        return 0
      }
      this.loading = false
      // console.log("保存完成")

      this.getClinicalPath()


      return 1

    },
    resetQuery() {
      this.queryParams.cydateStart = getMonthAgoDateStr()
      this.queryParams.cydateEnd = getTodayLastSecondStr()
      this.queryParams.bah = null
      this.queryParams.cykb = null
      this.cydateShow = true
      this.queryParams.zyzt = null
      this.getBrList()
    },
    setIcd() {
      if (!this.patientExists()) {
        return
      }
      if (this.yhbs == "1") {
        listZdxxSy({brid: this.brid, zyid: this.zyid}).then(response => {
          this.icd10 = response.rows
        });
        listSsxxSy({brid: this.brid, zyid: this.zyid}).then(response => {
          this.icd09 = response.rows
        });
      } else {
        if (this.useICD) {
          listZdxxYb({brid: this.brid, zyid: this.zyid}).then(response => {
            this.icd10 = response.rows
          });
          listSsxxYb({brid: this.brid, zyid: this.zyid}).then(response => {
            this.icd09 = response.rows
          });
        }
      }
    },
    async oneClickEncoding() {
      if (!this.patientExists()) {
        return
      }

      if (this.znbmFlagVal == null) {
        const znbmFlag = await getOption("znbm_flag");
        if (znbmFlag.hasOwnProperty("data")) {
          this.znbmFlagVal = znbmFlag.data.cValue;
          if (this.znbmFlagVal == null) {
            this.znbmFlagVal = 1;
          }
        } else {
          this.znbmFlagVal = 1;
        }
      }

      console.log(this.znbmFlagVal)

      this.zntjzd = []
      this.zntjss = []

      this.loading = true
      zzdtj(this.brxx.brid == null ? "" : this.brxx.brid, this.brxx.zyid == null ? "" : this.brxx.zyid, this.yhbs == null ? "" : this.yhbs).then(response => {
        if (response.total == 1) {
          this.$modal.msgWarning(response.rows[0]);
          this.loading = false;
          return
        }
        if (response.code != 200) {
          this.loading = false
          return;
        }
        this.ybzdxx = response.rows[0]
        this.ybzdxx2 = this.ybzdxx
        this.ybssxx = response.rows[1]
        this.ybssxx2 = this.ybssxx
        if (this.ybzdxx2.length == 0) {
          this.insert(0, 1)
        }
        if (this.ybssxx2.length == 0) {
          this.insert(0, 2)
        }
        if (this.znbmFlagVal != 0) {
          this.zntjzd = JSON.parse(JSON.stringify(this.ybzdxx2))
          this.zntjss = JSON.parse(JSON.stringify(this.ybssxx2))
          this.zdtjtip = response.rows[2]
          console.log(this.zdtjtip)
          if (this.zdtjtip.length > 0) {
            var tip = []
            for (let i = 0; i < this.zdtjtip.length; i++) {
              tip.push(this.zdtjtip[i].tip)
            }
            this.$alert(tip)
          }
          this.getZdFyxx()
        }
        this.loading = false
      }).catch(error => {
        this.loading = false
        // this.$modal.msgError(error)
      })
    },
    getFyxx() {
      selectFyxxByBr({
        brid: this.brid,
        zyid: this.zyid,
        jzh: this.brxx.jzh
      }).then(response => {
        this.fyxx = response.rows
      }).catch(err => {
        this.tabLoading = false
      });
    },
    getkmfyandbg() {
      selectkmfyandbg({
        brid: this.brid,
        zyid: this.zyid,
        jzh: this.brxx.jzh
      }).then(response => {
        this.kmfyandbg = response.rows
        this.tabLoading = false
      }).catch(err => {
        this.tabLoading = false
      });
    },
    sync() {
      if (!this.checkParam()) {
        return
      }

      if (this.brbs == "" || this.brbs == null) {
        if (this.brid != null && this.zyid != null) {
          this.brbs = this.brid.trim() + "_" + this.zyid.trim();
        }
      }

      if (this.brbs == null) {
        this.$modal.msgWarning("当前病案未同步，请通过brbs访问并同步！");
        return;
      }

      getOption("sync_version").then(res => {
        if (res.data.cValue == "java") {
          this.loading = true
          basySync(this.brbs).then(res => {
            this.loading = false
            if (res.code == 200) {
              this.$modal.msgSuccess("同步成功")
              this.getBrxx(1)
            } else {
              this.$modal.msgError("同步失败")
            }
          }).catch(error => {
            this.loading = false;
            this.$modal.msgError("同步失败！");
          })
        } else {
          sync({brbs: this.brbs}).then(response => {
            console.log(response)
            if (response.message == "更新成功") {
              this.getBrxx()
              this.$modal.msgSuccess("同步成功！");
            } else {
              this.$modal.msgError("同步失败！");
            }
          }).catch(err => {
            this.$modal.msgError("同步失败！");
          });
        }
      }).catch(error => {
        sync({brbs: this.brbs}).then(response => {
          console.log(response)
          if (response.message == "更新成功") {
            this.getBrxx()
            this.$modal.msgSuccess("同步成功！");
          } else {
            this.$modal.msgError("同步失败！");
          }
        }).catch(err => {
          this.$modal.msgError("同步失败！");
        });
      })
    },
    updateBrListStatus() {
      this.isOpenBrList = !this.isOpenBrList
    },
    setTjss() {
      if (!this.patientExists()) {
        return
      }
      this.tjss = []
      if (this.isShowTjss) {
        var brxx = {
          brid: this.brid,
          brbs: this.brbs,
          zyid: this.brxx.zyid
        }
        listTjss(brxx).then(response => {
          this.tjss = response.rows

          //如果手术的收费项目，已经有对应的诊断，则诊断为空就不显示 add by lt 删除此代码，有问题
          // for (let i = 0; i < this.tjss.length; i++) {
          //   if (this.tjss[i].xmmc != null && this.tjss[i].bzbm != null) {
          //     var xmmc = this.tjss[i].xmmc
          //     for (let j = 0; j < this.tjss.length; j++) {
          //       if (this.tjss[j].xmmc == xmmc && this.tjss[j].bzbm == null) {
          //         this.tjss.splice(j, 1)
          //         j--
          //       }
          //     }
          //   }
          // }

          for (let i = 0; i < this.tjss.length; i++) {
            this.tjssTem.push(this.tjss[i])
          }
        });
      }
    },
    oneClickAddSs() {
      var tjss = this.tjss
      if (tjss.length == 0) {
        this.$modal.msgWarning("推荐手术为空");
        return
      }
      var addSsArr = []

      for (let i = 0; i < tjss.length; i++) {
        var xmmc = tjss[i].xmmc
        var arr = []
        //获取项目名称相同的手术
        for (let j = 0; j < tjss.length; j++) {
          if (tjss[j].xmmc == xmmc) {
            arr.push(tjss[j])
          }
        }

        //如果有多个手术项目相同
        if (arr.length > 1) {
          //判断这些手术中那些诊断不为空
          var zdNotNullArr = []
          for (let j = 0; j < arr.length; j++) {
            if (arr[j].bzbm != null) {
              zdNotNullArr.push(arr[j])
            }
          }
          //如果只有一个，则添加此诊断
          if (zdNotNullArr.length == 1) {
            addSsArr.push(zdNotNullArr[0])
          }
          //如果有多个，则添加mrflag为1的手术  如果没有marflag为1的手术，则添加第一条手术
          if (zdNotNullArr.length > 1) {
            var flag = 0
            for (let j = 0; j < zdNotNullArr.length; j++) {
              if (zdNotNullArr[j].mrflag == 1) {
                addSsArr.push(zdNotNullArr[j])
                flag = 1
                break
              }
            }
            if (flag == 0) {
              addSsArr.push(zdNotNullArr[0])
            }
          }
          //如果一个也没有，则添加mrflag为1的手术  如果没有marflag为1的手术，则添加第一条手术
          if (zdNotNullArr.length == 0) {
            var flag = 0
            for (let j = 0; j < arr.length; j++) {
              if (arr[j].marflag == 1) {
                flag = 1
                addSsArr.push(arr[j])
                break
              }
            }
            if (flag == 0) {
              addSsArr.push(arr[0])
            }
          }

          for (let j = 0; j < tjss.length; j++) {
            if (tjss[j].xmmc == xmmc) {
              tjss.splice(j, 1)
              j--
            }
          }
        }

        if (arr.length == 1) {
          addSsArr.push(arr[0])
          tjss.splice(i, 1)
        }

        i = -1
      }

      //更新手术列表
      if (this.ybssxx2.length == 1 && this.ybssxx2[0].ssbm == null && addSsArr.length > 0) {
        this.ybssxx2 = []
      }
      if (addSsArr.length > 0) {
        for (let i = 0; i < addSsArr.length; i++) {
          var flag = 0
          for (let j = 0; j < this.ybssxx2.length; j++) {
            if (addSsArr[i].ssbm == this.ybssxx2[j].ssbm) {
              flag = 1
            }
          }
          if (flag == 0) {
            this.ybssxx2.push({
              brbs: this.brbs,
              brid: this.brid,
              zyid: this.brxx.zyid,
              sscx: this.ybssxx2.length + 1,
              ssbm: addSsArr[i].ssbm,
              ssmc: addSsArr[i].ssmc,
            })
          }
        }
      }

      this.ybssxx = this.ybssxx2
      this.tjss = this.tjssTem
    },
    insertTjss(index) {
      for (let i = 0; i < this.ybssxx2.length; i++) {
        if (this.ybssxx2[i].ssbm == this.tjss[index].ssbm) {
          this.$modal.msgWarning("当前手术已存在");
          return
        }
      }

      if (this.ybssxx2.length == 1 && this.ybssxx2[0].ssbm == null) {
        this.ybssxx2 = []
      }
      this.ybssxx2.push({
        brbs: this.brbs,
        brid: this.brid,
        zyid: this.brxx.zyid,
        sscx: this.ybssxx2.length + 1,
        ssbm: this.tjss[index].ssbm,
        ssmc: this.tjss[index].ssmc,
      })
      this.tjss.splice(index, 1)
    },
    identifyZdxx() {
      if (this.brData == null) {
        this.$modal.msgWarning("患者数据不存在");
        return
      }
      this.loading = true
      var notMaleZd = ['子宫', '月经', '阴道', '流产', '宫骶', '分娩', '宫腔', '人工授精', '胚胎', '宫颈', '盆底', '会阴', '外阴', '前庭大腺', '阴蒂', '引产', '助产', '引产', '剖宫产', '妊娠']
      var notFemalezd = ['前列腺', '精囊', '包皮', '阴茎', '输精管', '精索', '附睾', '睾丸', '阴囊']
      this.allZdList = []
      getjbbmmlList({jzh: this.brData.jzh}).then(response => {
        if (response.code != 200) {
          this.$modal.msgWarning("诊断识别失败");
          this.loading = false
          return
        }
        var zdxxList = response.rows
        for (let i = 0; i < zdxxList.length; i++) {
          this.allZdList.push([])
          var zdxxArr = zdxxList[i].split("||")
          var zdbmArr = zdxxArr[0].split(",")
          var zdmcArr = zdxxArr[1].split(",")
          for (let j = 0; j < zdmcArr.length; j++) {
            if (zdmcArr[j] == undefined || zdbmArr[j] == undefined) {
              continue
            }
            var zdmc = zdmcArr[j].replaceAll(" ", "")
            var jbbm = zdbmArr[j].replaceAll(" ", "")
            if (zdmc != '' && jbbm != '') {
              var xb = this.brxx.xb
              if (xb == "男") {
                var flag = 0
                for (let k = 0; k < notMaleZd.length; k++) {
                  if (zdmc.indexOf(notMaleZd[k]) != -1) {
                    flag = 1
                  }
                }
                if (flag == 0) {
                  this.allZdList[i].push({zdmc: zdmc, jbbm: jbbm, status: false})
                }
              } else if (xb == "女") {
                for (let k = 0; k < notFemalezd.length; k++) {
                  var flag = 0
                  if (zdmc.indexOf(notFemalezd[k]) != -1) {
                    flag = 1
                  }
                }
                if (flag == 0) {
                  this.allZdList[i].push({zdmc: zdmc, jbbm: jbbm, status: false})
                }
              } else {
                this.allZdList[i].push({zdmc: zdmc, jbbm: jbbm, status: false})
              }
            }
          }
        }
        this.loading = false
        this.open = true
      });
    },
    OpenChat(){
      this.chatVisible = true;
    },
    changeZdsbSelect(index, index2) {
      this.allZdList[index][index2].status = !this.allZdList[index][index2].status
    },
    submitFileForm() {
      this.open = false
      for (let i = 0; i < this.allZdList.length; i++) {
        for (let j = 0; j < this.allZdList[i].length; j++) {
          var zd = this.allZdList[i][j]
          if (zd.status) {
            if (this.yhbs == "1") {
              selectYbzdByLczd({zdmc: zd.zdmc, jbbm: zd.jbbm}).then(response => {
                var zdItem = response.rows[0]

                var flag = true
                for (let k = 0; k < this.ybzdxx2.length; k++) {
                  if (this.ybzdxx2[k].zdmc == zdItem.zdmc && this.ybzdxx2[k].jbbm == zdItem.jbbm) {
                    flag = false
                  }
                }
                if (flag) {
                  if (this.ybzdxx2.length == 1 && this.ybzdxx2[0].zdmc == null) {
                    this.ybzdxx2[0].zdmc = zdItem.zdmc
                    this.ybzdxx2[0].jbbm = zdItem.jbbm
                  } else {
                    this.ybzdxx2.push({
                      id: null,
                      brbs: this.brbs,
                      brid: this.brid,
                      zyid: this.brxx.zyid,
                      zdlx: '出院诊断',
                      zdcx: this.ybzdxx2.length + 1,
                      jbbm: zdItem.jbbm,
                      zdmc: zdItem.zdmc,
                      rybq: null,
                      cyqk: null,
                      fm: null,
                      bz: null,
                    })
                  }
                }
              });
            } else {
              var flag = true
              for (let k = 0; k < this.ybzdxx2.length; k++) {
                if (this.ybzdxx2[k].zdmc == zd.zdmc && this.ybzdxx2[k].jbbm == zd.jbbm) {
                  flag = false
                }
              }
              if (flag) {
                if (this.ybzdxx2.length == 1 && this.ybzdxx2[0].zdmc == null) {
                  this.ybzdxx2[0].zdmc = zd.zdmc
                  this.ybzdxx2[0].jbbm = zd.jbbm
                } else {
                  this.ybzdxx2.push({
                    id: null,
                    brbs: this.brbs,
                    brid: this.brid,
                    zyid: this.brxx.zyid,
                    zdlx: '出院诊断',
                    zdcx: this.ybzdxx2.length + 1,
                    jbbm: zd.jbbm,
                    zdmc: zd.zdmc,
                    rybq: null,
                    cyqk: null,
                    fm: null,
                    bz: null,
                  })
                }
              }
            }

          }
        }
      }
      this.ybzdxx = this.ybzdxx2
    },
    formatDateTwoWords(data) {
      if (data.length < 2) {
        return '0' + data
      } else {
        return data;
      }
    },
    clearBrData() {
      this.activeName = 'zdFyxx'
      this.pathDesc = "请选择患者"
      this.ybzdxx = []
      this.ybzdxx2 = []
      this.ybssxx = []
      this.ybssxx2 = []
      this.brxx = []
      this.brData = null
      this.zntjzd = []
      this.zntjss = []
      this.zdtjtip = []
      this.bajytip = []
      this.zdFyxx = []
      this.bah = null
      this.brbs = null
      this.brid = null
      this.advice = null
      this.zkxx = []
      this.tjss = []
      this.isShowTjss = false
      this.useICD = false
      this.fyxx = null
      this.kmfyandbg = null
      this.pxzd = null
      this.fzxx = {
        code: '',
        drgbh: '',
        drgmc: '',
        fztype: '',
        message: '',
        pjdays: '',
        zfbz: '',
        zfqz: '',
        zfy: '',
        zydays: '',
        num: ''
      }
      this.missingProjects = []
      this.excessProjects = []
      this.path = null
      this.settlePlaceShow = false
      this.exInfo = null
    },
    getDeptList() {
      listCykb().then(response => {
        this.ksList = response.rows;
      });
      this.getBrList()
      this.brshow = true
    },
    async identityConfirm() {
      if (this.$route.query.id == 2) {
        if (this.$store.state.user.roles.length == 0) {
          this.$modal.alertError("用户不属于任何角色！")
          this.clearBrData()
          return
        }
        if (checkRoleItem('ybry')) {  //医保角色
          console.log("ybry")
          this.yhbs = "1"
          this.getDeptList()
        } else {
          this.brshow = true
          this.yhbs = "2"
          this.qdflag = this.$route.query.qdflag
          // this.yhbs = this.$route.query.qdflag == 1 ? "1" : "2"  //医保清单
          if (checkRoleYsAndKzr('kzrys')) { //科主任角色
            console.log("kzrys")
            this.yhkb = this.$store.state.user.dept?.deptName
            this.deptDrgQueryParam.cykbList = [this.yhkb]
            this.yhxm = null
          } else if (checkRoleYsAndKzr('ys')) {
            console.log("ys")
            this.yhxm = this.$store.state.user.nickName
            const res = await getUserDeptList()
            if (res.code == 200) {
              const deptList = res.deptList ? res.deptList : null
              if (deptList && deptList.length > 0) {
                if (deptList.length == 1) {
                  this.yhkb = deptList[0].hDeptName
                  this.deptDrgQueryParam.cykbList = [this.yhkb]
                } else {
                  this.deptDrgQueryParam.cykbList = []
                  deptList.forEach(item => {
                    console.log(item)
                    this.ksList.push({cykb: item.hDeptName})
                    this.deptDrgQueryParam.cykbList.push(item.hDeptName)
                  })
                  this.yhkb = null
                }
              } else {
                this.yhkb = null
              }
            }
          } else {
            console.log(this.$store.state.user.roles)
            this.yhkb = null
            this.yhxm = null
            this.getDeptList()
          }
          await this.getBrList()
          if (this.$route.query.brid || this.$route.query.jzh || this.$route.query.brbs) {
            this.setBrxx({
              brid: this.$route.query.brid,
              zyid: this.$route.query.zyid,
              brbs: this.$route.query.brbs,
              jzh: this.$route.query.jzh
            })
          }
        }
      } else if (this.$route.query.id == 1) {
        this.clearBrData()
        if (this.$route.query.bah != undefined) {
          this.bah = this.$route.query.bah
        } else {
          this.bah = null
        }

        if (this.$route.query.brbs != undefined) {
          this.brbs = this.$route.query.brbs
        } else {
          if (this.$route.query.brid != undefined && this.$route.query.zyid != undefined) {
            this.brbs = this.$route.query.brid + '_' + this.$route.query.zyid
          } else {
            this.brbs = null
          }
        }
        if ((this.bah == null || this.bah == "") && (this.brbs == null || this.brbs == '')) {
          this.$modal.alertError("该用户不存在，请输入正确信息")
          return
        }
        this.yhbs = ""
        if(this.takeData == 1) {
          this.sync()
        } else {
          await this.getBrxx()
        }
      }
    },
    getZdItemFyxx(row) {
      this.loading = true
      this.zdItemFyxx = []
      var jbbm = row.jbbm.trim()
      selectBrzdfy({brid: this.brid, zyid: this.zyid}).then(response => {
        if (response.code != 200) {
          this.$modal.msgError("费用查询失败")
          this.loading = false
          return
        }
        var zdfyxx = response.rows
        console.log(zdfyxx)
        for (let i = 0; i < zdfyxx.length; i++) {
          if (zdfyxx[i].bzbm == null || zdfyxx[i].bzbm == "") continue
          if (zdfyxx[i].bzbm.trim() == jbbm) {
            this.zdItemFyxx.push(zdfyxx[i])
          }
        }
        this.zdItemFyOpen = true
        this.loading = false
      })
    },
    getfyfx_dzsz_fyxx() {
      if (this.jzh == null) {
        this.$modal.msgWarning("请选择患者，就诊号为空")
        return
      }
      this.loading = true
      this.fyxx_dzsz = []
      selectzdlcljfy({brid: this.brid, zyid: this.zyid, jzh: this.jzh}).then(response => {
        if (response.code != 200) {
          this.$modal.msgError("费用查询失败")
          this.loading = false
          return
        }
        this.fyxx_dzsz = response.rows
        this.fyxx_dzsz_open = true
        this.loading = false
      })
    },
    getDetails() {
      if (this.jzh == null) {
        this.$modal.msgWarning("请选择患者，就诊号为空")
        return
      }
      this.zdDetailsOpen = true
      this.$refs.tabs.getData();
    },
    updateZyzt(e) {
      if (e == 1) {
        this.cydateShow = false
        this.queryParams.cydateStart = null;
        this.queryParams.cydateEnd = null;
      } else {
        this.cydateShow = true
        this.queryParams.cydateStart = getMonthAgoDateStr()
        this.queryParams.cydateEnd = getTodayLastSecondStr()
      }
    },
    showPayDetail() {
      this.settleInfoDetailShow = true
      this.getSettleInfoDetail()
    },
    showSettlePlace() {
      if (this.fzxx.drgbh == null || this.fzxx.drgbh == undefined || this.fzxx.drgbh == '') {
        this.$modal.msgWarning("提示信息，请先分组后再查看")
        return
      }

      this.settlePlaceShow = !this.settlePlaceShow

      if (this.exInfo == null) {
        getInfoCost(this.fzxx.drgbh).then(res => {
          this.exInfo = res.info
        })
      }
    },
    //获取到病人付费项目列表
    getSettleInfoDetail() {
      this.settleInfoDetailTotal = 0
      this.settleInfoDetail = []
      getMonthInfoDetail({
        brbs: this.brbs,
        zyys: this.role === null ? null : this.yhxm,
        cykb: this.role === null ? null : this.yhkb,
        pageNum: this.settleQueryParam.pageNum,
        pageSize: this.settleQueryParam.pageSize
      }).then(res => {
        this.settleInfoDetail = res.rows
        this.settleInfoDetailTotal = res.total
      })
    },
    /** 导出按钮操作 */
    handleSettleExport() {
      this.download('/drg/syjl/month/list/export', {
        brbs: this.brbs,
        zyys: this.role === null ? null : this.yhxm,
        cykb: this.role === null ? null : this.yhkb,
        pageNum: this.settleQueryParam.pageNum,
        pageSize: this.settleQueryParam.pageSize
      }, `patient_${new Date().getTime()}.xlsx`)
    },
    medicalListSave() {
      if(this.brxx.hisJsdate && this.brxx.hisJsdate !== '') {
        let jsdate = new Date(this.brxx.hisJsdate)
        let now = new Date()

        const diffDays = Math.floor((now - jsdate) / (1000 * 60 * 60 * 24));
        console.log(parseInt(this.ybqdTimeout), '超时时间', diffDays, '结算天数')
        if(diffDays >= parseInt(this.ybqdTimeout)) {
          console.log('进入提示')
          MessageBox.alert(`该病人距结算已超过${this.ybqdTimeout}天，是否继续保存？`, {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            showCancelButton: true
          }).then(res => {
            console.log(res)
            this.saveToHisList()
          }).catch(err => {
            console.log(err)
          })
        } else {
          this.saveToHisList()
        }
      } else {
        this.saveToHisList()
      }
      // this.saveToHisList()
    },
    /**
     * 保存到清单
     */
    async saveToHisList() {
      const res = await this.save()

      if (res !== 1) {
        return
      }

      //未选择病人时取消提交操作
      if (!this.brxx.brbs) {
        this.$modal.msgWarning("请先选择患者")
        return
      }
      saveToList({
        brbs: this.brxx.brbs
      }).then(res => {
        if (res.code === 200) {
          // console.log("提交成功")
          this.$modal.msgSuccess("提交成功，医保诊断已锁定")
        } else {
          this.$modal.msgError("提交失败")
        }
        this.searchBr()
      })
    },
    listSaveCancel(){
      listCancel({
        brbs: this.brxx.brbs
      }).then(res => {
        if(res.code === 200) {
          this.$modal.msgSuccess("取消成功")
        } else {
          this.$modal.msgError("取消失败，请重试，多次失败请联系管理员")
        }
      })
    },
    async init() {
      getOption("use_multi_tenant").then(res => {
        var option = res.data.cValue
        if (option != "") {
          this.hasMultiOrg = option
        }
      }).catch(err => {
        this.hasMultiOrg = "0"
      })

      getOption("region").then(res => {
        var option = res.data.cValue
        if (option != "") {
          this.drgqy = option
        }
      }).catch(err => {
        this.drgqy = "重庆"
      })
      // this.$modal.alertError(this.drgqy);
      getOption("yfz_btn_display").then(res => {
        var option = res.data.cValue
        if (option != "") {
          this.btnDisplay = option
        }
      }).catch(err => {
        this.btnDisplay = "1"
      })
      getOption("show_syybkfxx").then(res => {
        let option = res.data.cValue
        if (option != "") {
          this.showYbkfxx = false
        }
      }).catch(err => {
        this.showYbkfxx = true
      })
      getOption("show_yjbm").then(res => {
        let option = res.data.cValue
        if (option != "") {
          this.showYjbm = option == "1" ? true : false
        } else {
          this.showYjbm = true
        }
      })
      getOption("show_listsave_cancel").then(res => {
        let option = res.data.cValue
        if (option != "") {
          this.showListSaveCancel = option == "1" ? true : false
        } else {
          this.showListSaveCancel = false
        }
      })
      getOption("use_ybqd").then(res => {
        this.useYbqd = res.data == null ? '0' : res.data.cValue
        this.ybqdStatusOptions = getYfzOptions(this.useYbqd)
      })
      getOption("ybqd_timeout").then(res => {
        this.ybqdTimeout = res.data ? '9' : res.data.cValue
      })
      getOption("show_basy_btn").then(res => {
        this.showBasyBtn = res.data ? res.data.cValue : '0'
      })
      const res = await getOption("all_role_del_zyxh")
      if (res.code == 200 && res.data && res.data.cValue) {
        this.allRoleDelZyxh = (res.data.cValue == "1")
      }
      this.identityConfirm()
    },
    displayDyxx(item, flag) {
      this.dyxxFlag = flag
      console.log(item)
      if (flag == 1) {
        let zdmc = item.zdmc
        let jbbm = item.jbbm
        this.newDyxx.bzmc = zdmc
        this.newDyxx.bzbm = jbbm
        this.dyxxTitle = zdmc + "[" + jbbm + "]对应的手术信息"
        if (this.yhbs == "1") {
          listIcddyss2({bzbm: jbbm, bzmc: zdmc}).then(res => {
            this.dyxxList = res.rows
            this.dyxxShow = true
          })
        } else {
          icd10yb({jbbm: jbbm, zdmc: zdmc}).then(res => {
            if (res.total > 1) {
              listIcddyss2({bzbm: res.rows[0].jbbm, bzmc: res.rows[0].zdmc}).then(res => {
                this.dyxxList = res.rows
                this.newDyxx.bzmc = res.rows[0].zdmc
                this.newDyxx.bzbm = res.rows[0].jbbm
              })
            } else {
              listIcddyss2({bzbm: jbbm, bzmc: zdmc}).then(res => {
                this.dyxxList = res.rows
              })
            }
            this.dyxxShow = true
          })
        }
      } else if (flag == 2) {
        let ssmc = item.ssmc
        let ssbm = item.ssbm
        this.newDyxx.ssmc = ssmc
        this.newDyxx.ssbm = ssbm
        this.dyxxTitle = ssmc + "[" + ssbm + "]对应的诊断信息"
        if (this.yhbs == "1") {
          listIcddyss2({ssbm: ssbm, ssmc: ssmc}).then(res => {
            this.dyxxList = res.rows
            this.dyxxShow = true
          })
        } else {
          icd09yb({jbbm: ssbm, zdmc: ssmc}).then(res => {
            if (res.total > 1) {
              listIcddyss2({ssbm: res.rows[0].jbbm, ssmc: res.rows[0].zdmc}).then(res => {
                this.dyxxList = res.rows
                this.newDyxx.ssmc = res.rows[0].zdmc
                this.newDyxx.ssbm = res.rows[0].jbbm
              })
            } else {
              listIcddyss2({ssbm: ssbm, ssmc: ssmc}).then(res => {
                this.dyxxList = res.rows
              })
            }
            this.dyxxShow = true
          })
        }
      } else if (flag == 3) {
        let zdmc = item.zdmc
        let jbbm = item.jbbm
        this.newDyxx.bzmc = zdmc
        this.newDyxx.bzbm = jbbm
        this.dyxxTitle = zdmc + "[" + jbbm + "]对应的手术信息"

        listLcljFyxmByzd({bzbm: jbbm}).then(res => {
          this.lcljFyxmList = res.rows
          this.lcljFyxmShow = true
        })
      }
    },
    addDyxx() {
      if (this.newDyxx.value == null) {
        this.$modal.msgWarning("请选择对应信息")
        return
      }

      if (this.dyxxFlag == 1) {
        this.newDyxx.ssbm = this.newDyxx.bm
        this.newDyxx.ssmc = this.newDyxx.mc
      } else if (this.dyxxFlag == 2) {
        this.newDyxx.bzbm = this.newDyxx.bm
        this.newDyxx.bzmc = this.newDyxx.mc
      }
      this.newDyxx.bzbmssbm = this.newDyxx.bzbm + "_" + this.newDyxx.ssbm
      addIcddyss(this.newDyxx).then(res => {
        if (res.code == 200) {
          if (this.dyxxFlag == 1) {
            this.displayDyxx({zdmc: this.newDyxx.bzmc, jbbm: this.newDyxx.bzbm}, this.dyxxFlag)
          } else if (this.dyxxFlag == 2) {
            this.displayDyxx({ssmc: this.newDyxx.ssmc, ssbm: this.newDyxx.ssbm}, this.dyxxFlag)
          }
          this.$modal.msgSuccess("添加成功")
          this.newDyxx.value = null
          this.newDyxx.bm = null
          this.newDyxx.mc = null
        }
      })
      console.log(this.newDyxx)
    },
    delDyxx(item) {
      delIcddyss(item).then(res => {
        if (res.code == 200) {
          if (this.dyxxFlag == 1) {
            this.displayDyxx({zdmc: this.newDyxx.bzmc, jbbm: this.newDyxx.bzbm}, this.dyxxFlag)
          } else if (this.dyxxFlag == 2) {
            this.displayDyxx({ssmc: this.newDyxx.ssmc, ssbm: this.newDyxx.ssbm}, this.dyxxFlag)
          }
          this.$modal.msgSuccess("删除成功");
        }
      })
    },
    selectDyxx(item) {
      if (this.dyxxFlag == 1) {
        for (let i = 0; i < this.ybssxx2.length; i++) {
          if (this.ybssxx2[i].ssbm == item.ssbm && this.ybssxx2[i].ssmc == item.ssmc) {
            this.$modal.msgWarning("该手术已存在");
            return
          }
        }

        if (this.ybssxx2.length == 1 && this.ybssxx2[0].ssbm == null) {
          this.ybssxx2 = []
        }

        this.ybssxx2.push({
          id: null,
          brbs: this.brbs,
          brid: this.brid,
          zyid: this.brxx.zyid,
          sscx: this.ybssxx2.length + 1,
          ssbm: item.ssbm,
          ssmc: item.ssmc,
          ssrq: null,
          ssjb: null,
          sskssj: null,
          ssjssj: null,
          sz: null,
          dyzs: null,
          dezs: null,
          qkyhdj: null,
          mzfs: null,
          mzfj: null,
          mzys: null,
          ssqk: null,
          ssbw: null,
          type: null
        })
        this.ybssxx = this.ybssxx2

      } else if (this.dyxxFlag == 2) {
        for (let i = 0; i < this.ybzdxx2.length; i++) {
          if (this.ybzdxx2[i].jbbm == item.bzbm && this.ybzdxx2[i].zdmc == item.bzmc) {
            this.$modal.msgWarning("该诊断已存在");
            return
          }
        }

        if (this.ybzdxx2.length == 1 && this.ybzdxx2[0].jbbm == null) {
          this.ybzdxx2 = []
        }

        this.ybzdxx2.push({
          id: null,
          brbs: this.brbs,
          brid: this.brid,
          zyid: this.brxx.zyid,
          zdlx: '出院诊断',
          zdcx: this.ybzdxx2.length + 1,
          jbbm: item.bzbm,
          zdmc: item.bzmc,
          rybq: null,
          cyqk: null,
          fm: null,
          bz: null,
          type: null
        })
        this.ybzdxx = this.ybzdxx2
      }
    },
    dyzdSelect(item) {
      console.log(item)
      for (let i = 0; i < this.dyxxList.length; i++) {
        if (this.dyxxList[i].bzbm == item.jbbm) {
          this.$modal.msgWarning("该诊断已存在");
          this.newDyxx.mc = null
          this.newDyxx.bm = null
          this.newDyxx.value = null
          return
        }
      }
      this.newDyxx.bm = item.jbbm
      this.newDyxx.mc = item.zdmc
    },
    dyssSelect(item) {
      console.log(item)
      for (let i = 0; i < this.dyxxList.length; i++) {
        if (this.dyxxList[i].ssbm == item.jbbm) {
          this.$modal.msgWarning("该诊断已存在");
          this.newDyxx.mc = null
          this.newDyxx.bm = null
          this.newDyxx.value = null
          return
        }
      }
      this.newDyxx.bm = item.jbbm
      this.newDyxx.mc = item.zdmc
    },
    clearDyxx() {
      this.newDyxx = {
        value: null,
        mc: null,
        bm: null,
        ssbm: null,
        ssmc: null,
        bzbm: null,
        bzmc: null,
        xmbm: null,
        bzbmssbm: null,
        flag: 100
      }
      this.dyxxFlag = null
      this.dyxxTitle = null
      this.dyxxList = []
    },
    handleZyjcExport(){
      this.download('/zyjc/zyjc/export', {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        zyzt: '1',
        zyys: this.$route.query.id == 1 ? this.brxx.zyys : this.yhxm,
        cykb: this.$route.query.id == 1 ? this.brxx.cykb : this.yhkb,
        jgid: this.jgid
      }, `在院检测_${getCurDayStr()}.xlsx`)
    },
    getBahHighlightClass(row) {
      // 获取当前时间
      const now = new Date();
      // 解析hisJsdate
      if (!row.hisJsdate) {
        return '';
      }

      const hisJsdate = new Date(row.hisJsdate);
      // 计算天数差异
      const diffDays = Math.floor((now - hisJsdate) / (1000 * 60 * 60 * 24));

      // 判断高亮级别
      if (diffDays <= 3 || !this.qdflag || this.qdflag != 1) {
        // 3天内，正常显示
        return '';
      } else if (diffDays > 3 && diffDays <= 7 && row.auditStatus !== 1) {
        // 3-7天且shzt不等于1，提醒显示
        return 'warning-light';
      } else if (diffDays > 7 && diffDays <= 10 && row.auditStatus !== 1) {
        // 7-10天且shzt不等于1，警告显示
        return 'warning-medium';
      } else if (diffDays > 10 && row.auditStatus !== 1) {
        // 超过10天且shzt不等于1，严重警告
        return 'warning-severe';
      }

      return '';
    }
  },
  created() {
    this.yhkb = "";
    this.yhxm = "";
    if (this.$route.query.audit != undefined) {
      this.audit = this.$route.query.audit
    } else {
      this.audit = null
    }
    if (this.$route.query.takeData  == 1) {
      this.takeData = 1
      const fragment = this.$route.hash; // 获取hash，如#section1#subsection
      if (fragment) {
        // 去掉开头的#并解码
        const decoded = decodeURIComponent(fragment.substring(1));
        // 替换#为@
        this.modifiedFragment = decoded.replace(/#/g, '@');
      } else {
        this.modifiedFragment = '没有片段标识符';
      }
      console.log('处理后的片段：', this.modifiedFragment);
      const drgData = this.$route.query.diagInfo + '@' + this.modifiedFragment;
      console.log('完整参数', drgData);

      let drgDataSplit = drgData.split('&surgInfo=')
      let brbs = this.$route.query.brbs
      let brbsSplit = brbs.split('_')




      let diagArr = drgDataSplit[0].split('@')
      for (let i = 0; i < diagArr.length; i++) {
        if(!diagArr[i] || diagArr[i].length === 0) {
          continue
        }
        let fields = diagArr[i].split('|')
        this.takeDiagInfo.push( {
          brbs : brbs,
          brid : brbsSplit[0],
          zyid : brbsSplit[1],
          rybq : fields[2],
          jbbm : fields[0],
          zdmc : fields[1],
          zdcx : i + 1
        } )
      }

      if(drgDataSplit[1] && drgDataSplit[1].length > 0) {
        let surgArr = drgDataSplit[1].split('@')
        for (let i = 0; i < surgArr.length; i++) {
          if(!surgArr[i].length > 0) {
            continue;
          }
          let fields = surgArr[i].split('|')
          this.takeSurgInfo.push( {
            brbs : brbs,
            brid : brbsSplit[0],
            zyid : brbsSplit[1],
            ssbm : fields[0],
            ssmc : fields[1],
            sscx : i + 1
          })
        }
        // console.log(this.takeSurgInfo)
      }
      // console.log(this.takeDiagInfo)
    }
    console.log(this.audit, '是否')
    this.init();

    getCurDayStr()
  }
}
</script>

<style lang="scss">
.content {
  width: 98%;
  margin-left: 1%;
  border: 1px solid #ccc;
  margin-top: 20px;
  padding-bottom: 10px;

  .main {
    display: flex;
    width: 100%;
    height: 100%;

    .tab {
      width: 35%;
      border-left: 1px solid #ccc;

      .bcjl {
        width: 100%;
        font-size: 15px;
        border-spacing: 0px;

        td {
          border-bottom: solid 1px #ccc;
          height: 25px;
          font-weight: normal;
        }

        tr {
          td:nth-child(1) {
            border-right: 1px solid #ccc;
            text-align: center;
            width: 20%;
          }
        }
      }

      .tabButton {
        margin-top: 5px;
        margin-left: 5px;

        button {
          border: none;
        }
      }
    }

    .brList {
      width: 25%;
      border-right: 1px solid #ccc;
    }

    .brxx {
      width: 100%;
      margin-left: 1%;

      table {
        margin-top: 15px;
        border-top: solid 1px #ccc;
        border-left: solid 1px #ccc;
        width: 100%;
        border-spacing: 0px;

        td, th {
          text-align: center;
          border-bottom: solid 1px #ccc;
          border-right: solid 1px #ccc;
          height: 35px;
          font-weight: normal;
        }

        button {
          font-size: 3px;
        }
      }
    }

  }

  .page {
    width: 100%;
    height: 50px;
    border-top: solid 1px #ccc;
    margin-top: 10px;
  }
}

input {
  border: none;
  border-bottom: solid 1px #ccc;
  background-color: #FFFFFF;
  text-align: center;
}

button {
  background-color: #FFFFFF;
  border: none;
  font-size: 10px;
  text-decoration: none;
}

.el-popper[x-placement^=bottom] {
  width: 600px !important;
  text-align: left;
}

.tip {
  width: 98%;
  border: solid 1px palegoldenrod;
  margin-top: 15px;
  background-color: palegoldenrod;
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
}

button {
  i {
    font-size: 10px;
  }
}

.tjss {
  tr {
    font-size: 13px;
  }
}

.zdsb {
  margin-top: 2px;
  margin-left: 2px;
}

input, el-table, textarea {
  -webkit-user-select: auto; /*webkit浏览器*/
}

.path-name {
  width: 100%;
  border: none;
  border-bottom: 1px solid #ccc;
  border-radius: 0;
  text-align: center;
  min-height: 20px;
}

.custom-autocomplete-popper {
  width: 500px !important; /* 设置弹出框宽度，根据需要调整 */
}


.dept-drg-num {
  text-decoration: underline;
}


.descriptions-item-label {
  width: 100px !important;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 750px;
}

.message-area {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}

.message-wrapper {
  margin: 12px 0;

  &.user-message {
    .message-bubble {
      flex-direction: row-reverse;
      background: #f0f7ff;
      margin-left: 35%;
      margin-right: 2%;
    }
  }

  &.ai-message {
    .message-bubble {
      background: #f5f7fa;
      margin-right: 20%;
    }
  }
}

.message-bubble {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-radius: 12px;
  transition: all 0.3s;
}

.ai-avatar, .user-avatar {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #409EFF;
  color: white;

  i {
    font-size: 18px;
  }
}

.user-avatar {
  background: #67C23A;
}

.message-content {
  margin: 0 12px;
  flex: 1;
}

.message-header {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.message-text {
  padding-top: 5px;
  font-size: 15px;
  line-height: 1.2;
  color: #303133;
  white-space: pre-wrap;
}

.message-text * {
  margin: 0;
}

.message-text h3 {
  margin-top: 10px;
  margin-bottom: 3px;
}

.message-text ol {
  line-height: 1;
  margin-top: 5px;
}

.message-text table {

}

.message-text p {
  /* 移除默认的外边距 */
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.message-text br {
  line-height: 2px;
  content: "";
  display: block;
  margin: 1px; /* 可以根据需要调整这个值 */
}

.markdown-text br + * {
  margin-top: 0.5em; /* 调整br后面元素的上边距 */
}

.loading-wrapper {
  text-align: center;
  padding: 16px;
  color: #909399;

  .el-icon-loading {
    margin-right: 8px;
    font-size: 16px;
  }
}

.btn-area {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
  border-radius: 5px;
  margin-left: 10px;
}

.input-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #ebeef5;
  padding-top: 12px;

  ::v-deep .el-textarea__inner {
    border: none;
    box-shadow: none !important;
    border-radius: 95px;
  }

  ::v-deep .el-input-group__append {
    background: transparent;
    border: none;
    padding: 0 10px;
    border-radius: 95px;
  }
}

.ai-dialog {
  ::v-deep .el-dialog__body {
    padding: 16px 20px;
  }
}

/* 病案号高亮警告样式 */
.warning-light {
  color: #E6A23C;
  font-weight: bold;
}

.warning-medium {
  color: #F56C6C;
  font-weight: bold;
}

.warning-severe {
  color: #F56C6C;
  font-weight: bold;
  background-color: #FEF0F0;
  padding: 2px 4px;
  border-radius: 3px;
}

.dept-drg-num {
// ... existing code ...
}
</style>
