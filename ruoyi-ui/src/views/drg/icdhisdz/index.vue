<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="ICD类型" prop="type">
        <el-select v-model="queryParams.type" clearable>
          <el-option label="诊断" value="1">诊断</el-option>
          <el-option label="手术" value="2">手术</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="医院编码" prop="bm">
        <el-input placeholder="医院编码" v-model="queryParams.bm" clearable/>
      </el-form-item>
      <el-form-item label="医院名称" prop="mc">
        <el-input placeholder="医院名称" v-model="queryParams.mc" clearable/>
      </el-form-item>
      <el-form-item label="助记码" prop="nccd">
        <el-input placeholder="助记码" v-model="queryParams.nccd" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table :border="true" v-loading="loading" :data="dataList">
      <el-table-column label="医院编码" align="center" prop="bm" />
      <el-table-column label="医院名称" align="center" prop="mc" />
      <el-table-column label="医保编码" align="center" prop="ybbm" />
      <el-table-column label="医保名称" align="center" prop="ybmc" />
      <el-table-column label="医保编码HIS" align="center" prop="ybbmHis" />
      <el-table-column label="医保名称HIS" align="center" prop="ybmcHis" />
    </el-table>

  </div>
</template>

<script>
import { compareIcdHis } from "@/api/drg/icd";

export default {
  name: "Icdhisdz",
  data() {
    return {
      loading: false,
      dataList: [],
      queryParams: {
        type: null,
        bm: null,
        mc: null,
        nccd: null,
      }
    };
  },
  created() {
  },
  methods: {
    getList() {
      this.loading = true;
      compareIcdHis(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
      });
    },
    handleExport() {
      if (this.queryParams.type) {
        this.download('system/icd10ybdy/exportCompareIcdHis', {
          ...this.queryParams
        }, `ICD-HIS医保码对照_${new Date().getTime()}.xlsx`)
      } else {
        this.$modal.msgWarning("请选择ICD类型")
      }
    },
    resetQuery() {
      this.queryParams.bm = null
      this.queryParams.mc = null
      this.queryParams.nccd = null
      this.getList()
    }
  }
};
</script>
