<script>
import { getIcdList, icdAdd, updateIcdInfo } from '@/api/drg/icd'

export default {
  name: 'IcdMaintain',
  dicts: ['icd_type'],

  data(){
    return {
      icdList: [
        {
          yybm: 'TEST1.1',
          yymc: 'test',
          ybbm: 'TESTx1.1',
          ybmc: 'test1'
        }
      ],
      queryParam: {
        yybm: '',
        ybbm: '',
        ybmc: '',
        yymc: '',
        type: 1
      },
      updateForm: {

      },
      icdSwap: {},
      pageSize: 10,
      pageNum: 1,
      total: 0,
      loading: false,
      showDialog: false,
      ybbmDisabledUpdate: false, //医保码是否可修改
      yybmDisabledUpdate: false,
      submitType: null //表单操作类型 1:修改 2:新增
    }
  },
  methods: {
    async getIcdData(){
      this.loading = true;
      const res = await getIcdList(this.queryParam, this.pageNum, this.pageSize);
      this.icdList = res.rows
      this.total = res.total
      this.loading = false;
    },
    async search(){
      await this.getIcdData()
    },
    hmFormatter(row, column, callValue) {
      const hmMap = {
        1 : '是',
        2 : '否'
      };
      return hmMap[callValue] || '否'
    },
    handleTypeChange(){
      this.queryParam.type = this.queryParam.type === 1 ? 2 : 1;
      this.getIcdData()
    },
    openUpdateForm(row){
      this.submitType = 1
      // 设置swap对象，保证源数据不丢失，确保临床和医保至少有一个不变
      this.icdSwap = row
      this.updateForm = {
        ybbm : row.ybbm,
        ybmc : row.ybmc,
        yybm : row.yybm,
        yymc : row.yymc,
        hm : row.hm,
        status : row.status
      }
      console.log(this.updateForm)
      this.showDialog = true

    },
    resetUpdateForm(){
      this.updateForm = {
        ybbm: this.icdSwap.ybbm,
        ybmc: this.icdSwap.ybmc,
        yybm: this.icdSwap.yybm,
        yymc: this.icdSwap.yymc,
        hm: this.icdSwap.hm,
        status: this.icdSwap.status
      }
    },
    submitUpdate(){
      updateIcdInfo({
        ...this.updateForm,
        type: this.queryParam.type,
        bmType: this.yybmDisabledUpdate ? 2 : 1,
        oldYybm: this.icdSwap.yybm,
        oldYbbm: this.icdSwap.ybbm,
        oldYymc: this.icdSwap.yymc,
        oldYbmc: this.icdSwap.ybmc
      }).then(res => {
        console.log(res)
        if(res.code == 200) {
          this.$modal.msgSuccess("操作成功")
          this.showDialog = false
          this.search()
        }
      }).catch(res => {
        console.log(res)
        this.$modal.msgError("操作失败：" + res.msg)
      })
      console.log(this.updateForm, this.yybmDisabledUpdate ? 1 : 2)
    },
    getChangeText(){
      return this.queryParam.type === 1 ? '切换ICD手术' : '切换ICD诊断'
    },
    submitAdd(){
      console.log(this.updateForm)
      icdAdd({
        ...this.updateForm,
        type : this.queryParam.type
      }).then(res => {
        if(res.code === 200) {
          this.$modal.msgSuccess("操作成功")
          this.showDialog = false
        }
        this.getIcdData()
      }).catch(err => {
        this.$modal.msgError("操作失败：" + err.msg)
      })
    },
    handleNewIcd(){
      this.submitType = 2
      this.icdSwap = {}
      this.resetUpdateForm()
      this.showDialog = true
    },
    saveOrUpdate(){
      if(this.submitType === 2) {
        this.submitAdd()
      } else {
        this.submitUpdate()
      }
    }
  },
  created() {
    this.getIcdData()
  },
  // watch: {
  //   updateForm: {
  //     handler(newValue, oldValue){
  //       if(newValue.ybbm !== this.icdSwap.ybbm) {
  //         this.yybmDisabledUpdate = true;
  //       } else {
  //         this.yybmDisabledUpdate = false
  //       }
  //       if(newValue.yybm !== this.icdSwap.yybm) {
  //         this.ybbmDisabledUpdate = true;
  //       } else {
  //         this.ybbmDisabledUpdate = false;
  //       }
  //     },
  //     deep: true,
  //     immediate: true
  //   }
  // }
}
</script>

<template>

  <div class="app-container">
    <div>
      <el-form :model="queryParam" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="医院编码" prop="yybm">
          <el-input
          clearable
          placeholder="输入医院编码"
          @keyup.enter.native="search"
          v-model="queryParam.yybm"/>
        </el-form-item>
        <el-form-item label="医院名称" prop="yymc">
          <el-input
            clearable
            placeholder="输入医院名称"
            @keyup.enter.native="search"
            v-model="queryParam.yymc"/>
        </el-form-item>
        <el-form-item label="医保编码" prop="ybbm">
          <el-input
            clearable
            placeholder="输入医保编码"
            @keyup.enter.native="search"
            v-model="queryParam.ybbm"/>
        </el-form-item>
        <el-form-item label="医保名称" prop="ybmc">
          <el-input
            clearable
            placeholder="输入医保名称"
            @keyup.enter.native="search"
            v-model="queryParam.ybmc"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search" size="mini">搜索</el-button>
          <el-button type="warning" @click="handleTypeChange" size="mini">{{ getChangeText() }}</el-button>
          <el-button type="success" @click="handleNewIcd" size="mini">新增</el-button>
        </el-form-item>

      </el-form>
    </div>

    <div>
      <div>
        <el-table :data="icdList" v-loading="loading">
          <el-table-column prop="yybm" label="医院编码" align="center"/>
          <el-table-column prop="yymc" label="医院名称" align="center"/>
          <el-table-column prop="ybbm" label="医保编码" align="center"/>
          <el-table-column prop="ybmc" label="医保名称" align="center"/>
          <el-table-column prop="hm" label="灰码" align="center" :formatter="hmFormatter"/>
          <el-table-column prop="status" llabel="状态" align="center">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.icd_type" :value="scope.row.status"/>
              </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="scope">
              <el-button type="text" @click="openUpdateForm(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div>
        <pagination
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="search"/>
      </div>
    </div>

    <div>
      <el-dialog :title="'修改对照'" :visible.sync="showDialog" width="700px">
        <div style="width: 100%">
          <el-form ref="dialogForm" size="small" :model="updateForm" label-width="80px" :inline="true">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="yybm" label="医院编码" style="width: 100%">
                  <el-input v-model="updateForm.yybm" :disabled="yybmDisabledUpdate && submitType !== 2"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="yymc" label="医院名称">
                  <el-input v-model="updateForm.yymc" :disabled="yybmDisabledUpdate && submitType !== 2"/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="ybbm" label="医保编码">
                  <el-input v-model="updateForm.ybbm" :disabled="ybbmDisabledUpdate && submitType !== 2"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="ybmc" label="医保名称">
                  <el-input v-model="updateForm.ybmc" :disabled="yybmDisabledUpdate && submitType !== 2"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="ybmc" label="编码状态">
                  <el-radio-group v-model="updateForm.status">
                    <el-radio
                      v-for="dict in dict.type.icd_type"
                      :key="dict.value"
                      :label="dict.value"
                    >{{dict.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-collapse :span="12">
                <el-form-item prop="hm" label="是否灰码">
                  <el-select v-model="updateForm.hm">
                    <el-option :label="'是'" :value="1" :key="1"/>
                    <el-option :label="'否'" :value="0" :key="0"/>
                  </el-select>
                </el-form-item>
              </el-collapse>
            </el-row>



            <div style="text-align: right; margin-right: 60px; margin-top: 40px">
              <el-button type="warning" @click="resetUpdateForm">重置</el-button>
              <el-button type="primary" @click="saveOrUpdate">提交</el-button>
            </div>
          </el-form>
        </div>
      </el-dialog>
    </div>
  </div>

</template>

<style scoped lang="scss">

</style>
