<template>
  <div class="app-container home">
    <iframe style="border: none" :src="url" :width="width" :height="heigth"></iframe>
  </div>
</template>

<script>
  import {
    getMenuIp,
    updateMenuByIP
  } from "@/api/system/menu";
  import {
    getOption
  } from "@/api/system/option"
  import {getToken} from "@/utils/auth";

  export default {
    name: "Index",
    data() {
      return {
        // 版本号
        version: "3.8.5",
        url: '',
        heigth: '',
        width: '',
        sybb:"834910560458747904"
      };
    },
    methods: {
      setSystemTitle(){
        getOption("system_main_title").then(res => {
          if(res.code !== 200) return;
          if (res.data != null && res.data != undefined) {
            this.$store.dispatch("settings/setTitle", res.data.cValue)
            this.$store.dispatch("tagsView/updateVisitedView", res.data.cValue)
            document.title = res.data.cValue
          }
        })
      },
      setSyReport() {
        getOption("sybb").then(response => {
          console.log(response)
          if (response.data != undefined && response.data != null && response.data != "") {
            this.sybb = response.data.cValue;
          }
          getMenuIp().then(response => {
            this.url = 'http://' + response + ':8096/jmreport/view/' + this.sybb + "?token=" + getToken()
          })
        });
      },
      updateMenuByIP() {
        updateMenuByIP().then(response => {});
      }
    },
    created() {
      this.updateMenuByIP()
      this.setSyReport()
      this.setSystemTitle()
      this.heigth = screen.height
      this.width = screen.width
    }
  };
</script>

<style scoped lang="scss">
  .home {
    iframe {
      border: 0px;
      padding: 0px;
      margin-top: -19px;
      margin-left: -18px;
    }

  }
</style>
