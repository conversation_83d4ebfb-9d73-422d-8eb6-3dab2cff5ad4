<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" abel-width="68px">
      <el-form-item label="科室">
        <el-select v-model="queryParams.cykb"
                   filterable
                   placeholder="科室"
                   clearable
                   style="width: 150px">
          <el-option v-for="(item,index) in deptList" :value="item.hDeptName" :key="index"
                     :label="item.hDeptName"/>
        </el-select>
      </el-form-item>

      <el-form-item label="记录来源">
        <el-select v-model="queryParams.jlly" placeholder="记录来源">
          <el-option v-for="item in jllyList" :value="item.value" :label="item.name">{{ item.name }}</el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="日期范围" prop="cydate">
        <el-date-picker
          v-model="queryParams.adtFrom"
          type="datetime"
          placeholder="请选择开始日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        -
        <el-date-picker
          v-model="queryParams.adtTo"
          type="datetime"
          placeholder="请选择截至日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="日期类型">
        <el-select v-model="queryParams.dateType" placeholder="日期类型" clearable>
          <el-option v-for="item in datetypeList" :value="item.value" :label="item.name">{{ item.name }}</el-option>
        </el-select>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <virtual-scroll
      ref="virtualScroll"
      :data="dataList"
      :item-size="70"
      key-prop="brbs"
      @change="onChange">
      <el-table :border="true" height="700px" v-loading="loading" :data="virtualList">
        <el-table-column fixed="left" class-name="small-padding fixed-width" show-overflow-tooltip width="100px" label="病案号" align="left" prop="bah"/>
        <el-table-column show-overflow-tooltip width="150px" label="出院科别" align="left" prop="cykb"/>
        <el-table-column show-overflow-tooltip width="100px" label="住院医师" align="left" prop="zyys"/>
        <el-table-column show-overflow-tooltip width="100px" label="主治医师" align="left" prop="zzys"/>
        <el-table-column label="A01基础信息" align="left" prop="a01"/>
        <el-table-column label="A02新生儿出生体重" align="left" prop="a02"/>
        <el-table-column label="A03新生儿入院体重" align="left" prop="a03"/>
        <el-table-column label="A04病案号" align="left" prop="a04"/>
        <el-table-column label="A05性别" align="left" prop="a05"/>
        <el-table-column label="A06出生日期" align="left" prop="a06"/>
        <el-table-column label="A07医疗付费方式" align="left" prop="a07"/>
        <el-table-column label="A08出院时间" align="left" prop="a08"/>
        <el-table-column label="A09实际住院天数" align="left" prop="a09"/>
        <el-table-column label="A10入院途径" align="left" prop="a10"/>
        <el-table-column label="A11入院时情况" align="left" prop="a11"/>
        <el-table-column label="A12是否为日间手术" align="left" prop="a12"/>
        <el-table-column label="A13门诊诊断及编码" align="left" prop="a13"/>
        <el-table-column label="A14入院诊断及编码" align="left" prop="a14"/>
        <el-table-column label="A15出院主要诊断" align="left" prop="a15"/>
        <el-table-column label="A16主要诊断编码" align="left" prop="a16"/>
        <el-table-column label="A17入院病情" align="left" prop="a17"/>
        <el-table-column label="A18出院病情" align="left" prop="a18"/>
        <el-table-column label="A19损伤中毒（外部）原因" align="left" prop="a19"/>
        <el-table-column label="A20损伤中毒（外部）原因疾病编码" align="left" prop="a20"/>
        <el-table-column label="A21其他诊断" align="left" prop="a21"/>
        <el-table-column label="A22其他诊断编码" align="left" prop="a22"/>
        <el-table-column width="400px" label="A23 1、药物过敏史2、过敏药物3、尸检记录4、血型5、Rh因子6、手术级别7、术者8、Ⅰ助9、Ⅱ助10、麻醉医师" align="left" prop="a23"/>
        <el-table-column width="400px" label="A24 1、科主任2、主任/副主任医师3、主治医师4、住院医师5、进修医师6、实习医师7、责任护士8、病案质量9、质控医师10、质控护士11、质控日期12、(医嘱转院)拟接受医疗机构名称13、(医嘱转社区卫生服务机构/乡镇卫生院)拟接收医疗机构名称" align="left" prop="a24"/>
        <el-table-column label="A25病理号" align="left" prop="a25"/>
        <el-table-column label="A26病理诊断" align="left" prop="a26"/>
        <el-table-column label="A27病理诊断编码" align="left" prop="a27"/>
        <el-table-column label="A28主要手术或操作名称" align="left" prop="a28"/>
        <el-table-column label="A29主要手术或操作编码" align="left" prop="a29"/>
        <el-table-column label="A30切口/愈合等级" align="left" prop="a30"/>
        <el-table-column label="A31是否手术部位感染" align="left" prop="a31"/>
        <el-table-column label="A32其他手术或操作名称" align="left" prop="a32"/>
        <el-table-column label="A33其他手术或操作编码" align="left" prop="a33"/>
        <el-table-column label="A34麻醉方式" align="left" prop="a34"/>
        <el-table-column label="A35手术及操作日期操作类型" align="left" prop="a35"/>
        <el-table-column label="A36颅脑损伤患者昏迷时间" align="left" prop="a36"/>
        <el-table-column label="A37抢救次数/抢救成功次数" align="left" prop="a37"/>
        <el-table-column label="A38离院方式" align="left" prop="a38"/>
        <el-table-column label="A39是否有31天内再入院计划及目的" align="left" prop="a39"/>
        <el-table-column label="A40非计划再次手术" align="left" prop="a40"/>
        <el-table-column label="A41输血反应" align="left" prop="a41"/>
        <el-table-column label="A42是否有空项" align="left" prop="a42"/>
        <el-table-column label="总扣分" align="left" prop="zkf"/>
        <el-table-column label="总得分" align="left" prop="zdf"/>
        <el-table-column width="1000px" label="备注" align="left" prop="bz"/>
      </el-table>
    </virtual-scroll>

  </div>
</template>

<script>
import {listErrorCjq} from "@/api/baxy/error";
import {getMonthFirstDayStr, getTodayLastSecondStr} from "@/utils/dateUtils";
import {selectHDeptNameList} from "@/api/system/hdept";
import VirtualScroll from "el-table-virtual-scroll";

export default {
  name: "Bazkcjq",
  components: {VirtualScroll},
  data() {
    return {
      deptList: [],
      datetypeList: [
        {name: "出院时间", value: "cydate"},
        {name: "结算时间", value: "jsdate"}
      ],
      jllyList: [
        {name: "医生", value: "3"},
        {name: "病案室", value: "4"}
      ],
      loading: true,
      dataList: [],
      virtualList: [],
      queryParams: {
        cykb: null,
        adtFrom: getMonthFirstDayStr(),
        adtTo: getTodayLastSecondStr(),
        dateType: 'cydate',
        jlly: '4',
      },
    };
  },
  created() {
    this.getList();
    this.getDeptList()
  },
  methods: {
    onChange(dataList) {
      this.virtualList = dataList
    },
    async getDeptList() {
      const res = await selectHDeptNameList()
      if (res.code == 200) {
        this.deptList = res.rows
      }
    },
    getList() {
      this.loading = true;
      listErrorCjq(this.queryParams).then(res => {
        this.dataList = res.rows;
        this.loading = false;
      });
    },
    handleQuery() {
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        cykb: null,
        adtFrom: getMonthFirstDayStr(),
        adtTo: getTodayLastSecondStr(),
        dateType: 'cydate',
        jlly: '4',
      }
      this.handleQuery();
    },
    handleExport() {
      this.download('/baxy/error/exportCjq', {
        ...this.queryParams
      }, `病案首页质控信息_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
