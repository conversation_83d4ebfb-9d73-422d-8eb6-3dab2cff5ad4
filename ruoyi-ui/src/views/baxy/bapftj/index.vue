<template>
  <div class="app-container">
    <div>
      <el-form
        style="margin-top: 20px; margin-left: 1%; width: 98%;"
        :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="科室">
          <el-select v-model="queryParams.cykb"
                     @focus="resetDept"
                     @change="getDoctorByDept(queryParams.cykb)"
                     filterable
                     :filter-method="deptFilter"
                     placeholder="科室"
                     clearable
          >
            <el-option v-for="(item,index) in deptList" :value="item.cykb" :key="index" :label="item.cykb">
              {{ item.cykb }}
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="医生">
          <el-select v-model="queryParams.zyys"
                     @focus="resetDoctor"
                     @change="resetDoctor"
                     filterable
                     :filter-method="doctorFilter"
                     placeholder="医生"
                     clearable
          >
            <el-option v-for="(item,index) in doctorList" :value="item.zyys" :key="index" :label="item.zyys">
              {{ item.zyys }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期类型">
          <el-select v-model="queryParams.dateType" placeholder="日期类型" clearable>
            <el-option v-for="item in datetypeList" :value="item.value" :label="item.name">{{ item.name }}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围" prop="cydate">
          <el-date-picker
            v-model="queryParams.adtFrom"
            type="datetime"
            placeholder="请选择开始日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
          -
          <el-date-picker
            v-model="queryParams.adtTo"
            type="datetime"
            placeholder="请选择截至日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="住院号">
          <el-input
            v-model="queryParams.bah"
            placeholder="住院号"
            clearable
          />
        </el-form-item>
        <el-form-item label="病人姓名">
          <el-input
            v-model="queryParams.xm"
            placeholder="病人姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="病案评分">
          <el-input-number v-model="queryParams.minScore"></el-input-number>
          <=评分<=
          <el-input-number v-model="queryParams.maxScore"></el-input-number>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="reset">重置</el-button>
          <el-button type="success" icon="el-icon-file" size="mini" @click="handleExportScoreStats">导出</el-button>
        </el-form-item>
      </el-form>


      <el-table v-loading="loading" :data="basyList" :border="true">
        <el-table-column width="110px" label="住院号" align="center" prop="bah" show-overflow-tooltip/>
        <el-table-column width="110px" label="姓名" align="center" prop="xm" show-overflow-tooltip/>
        <el-table-column width="110px" label="床日" align="center" prop="sjzyts" show-overflow-tooltip/>
        <el-table-column label="科室" align="center" prop="cykb" show-overflow-tooltip/>
        <el-table-column width="110px" label="医生" align="center" prop="zyys" show-overflow-tooltip/>
        <el-table-column width="110px" label="入院日期" align="center" prop="rydate" show-overflow-tooltip/>
        <el-table-column width="110px" label="出院日期" align="center" prop="cydate" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.zyzt == '0' ? scope.row.cydate : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column width="100px" label="结算日期" align="center" prop="jsdate" show-overflow-tooltip/>
        <el-table-column width="100px" label="DRG编号" align="center" prop="drgbh" show-overflow-tooltip/>
        <el-table-column width="100px" label="来源" align="center" prop="jlly" :formatter="stateFormat" show-overflow-tooltip/>
        <el-table-column width="100px" label="分数" align="center" prop="score" show-overflow-tooltip/>
        <el-table-column width="125px" fixed="right" label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="scoreDetail(scope.row)">评分详情</el-button>
            <el-button size="mini" type="text" @click="patientDetail(scope.row)">病人详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <el-drawer
      :title="scoreDetailTitle"
      :visible.sync="scoreDetailOpenCjq"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeScoreDetail"
      size="80%">
      <el-table :span-method="mergeCells" :border="true" v-loading="scoreDetailLoading" height="700px" :data="errorList">
        <el-table-column width="200px" label="违规类型" align="left" prop="field"/>
        <el-table-column label="错误描述" align="left" prop="errordes"/>
        <el-table-column width="200px" label="错误类型" align="left" prop="errortype" show-overflow-tooltip/>
        <el-table-column width="100px" label="扣分" align="left" prop="score" show-overflow-tooltip/>
        <el-table-column width="100px" label="记录来源" align="left" prop="jlly" show-overflow-tooltip/>
      </el-table>
    </el-drawer>


    <el-drawer
      :title="scoreDetailTitle"
      :visible.sync="scoreDetailOpen"
      direction="rtl"
      @close="closeScoreDetail"
      :wrapper-closable="false"
      size="60%">
      <div class="drawer-container">
        <div v-loading="scoreDetailLoading">
          <el-tabs type="card" v-model="activeName">
            <el-tab-pane label="错误列表" name="errors">
              <el-table height="600" :border="true" :data="errorList">
                <el-table-column label="错误描述" align="center" prop="errordes" show-overflow-tooltip/>
                <el-table-column width="100"  label="错误类型" align="center" prop="errortype" show-overflow-tooltip/>
                <el-table-column width="100"  label="出错字段" align="center" prop="field" show-overflow-tooltip/>
                <el-table-column width="100"  label="扣分" align="center" prop="score" show-overflow-tooltip/>
                <el-table-column width="100" label="记录来源" align="center" prop="jlly" show-overflow-tooltip/>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="评分明细" name="deductScores">
              <el-button style="margin: 5px;" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
              <el-table height="600" :border="true" :data="deductScoreList">
                <el-table-column width="100" label="序号" align="center" prop="id" show-overflow-tooltip/>
                <el-table-column label="评分项" align="center" prop="name" show-overflow-tooltip/>
                <el-table-column width="100" label="分数" align="center" prop="score" show-overflow-tooltip/>
                <el-table-column width="100" label="扣分" align="center" prop="deductScore" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <div v-if="scope.row.deductScore > 0" style="color: red;font-weight: bolder;">{{ scope.row.deductScore }}</div>
                    <div v-else>{{ scope.row.deductScore }}</div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-drawer>


    <el-drawer
      :title="patientDetailTitle"
      :visible.sync="patientDetailOpen"
      direction="rtl"
      :wrapper-closable="false"
      size="70%">
      <tabs :jzh="jzh"></tabs>
    </el-drawer>

  </div>
</template>

<script>
import PinYinMatch from "pinyin-match";
import {getBaSyjlScoreStats, getDeptList, getDoctorByDept, getBaSyjlScore} from "@/api/drg/syjl";
import tabs from "@/components/DetailsTabs/tabs.vue";
import {listError, deductScoreDetails} from "@/api/baxy/error";
import {getOption, getYyMame} from "@/api/system/option";
import {getMonthFirstDayStr, getTodayLastSecondStr} from "@/utils/dateUtils";
import {errList} from "@/api/baxy/checkpf";

export default {
  name: "index",
  components: {tabs},
  data() {
    return {
      activeName: "errors",
      currentRow: null,
      patientDetailOpen: false,
      patientDetailTitle: null,
      scoreDetailOpen: false,
      scoreDetailOpenCjq: false,
      scoreDetailTitle: null,
      jzh: null,
      yyName: null,
      doctorList: [],
      doctorListby: [],
      deptList: [],
      deptListby: [],
      basyList: [],
      errorList: [],
      deductScoreList: [],
      checkbzList: [],
      total: 0,
      loading: true,
      scoreDetailLoading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        date: null,
        cblb: null,
        dateType: 'cydate',
        zyys: null,
        adtFrom: getMonthFirstDayStr(),
        adtTo: getTodayLastSecondStr(),
        bah: null,
        xm: null,
        minScore: null,
        maxScore: null,
      },
      errorQueryParams: {
        pageNum: 1,
        pageSize: 10,
        bah: null,
        brbs: null,
        jlly: null
      },
      checkbzQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      datetypeList: [
        {name: "出院时间", value: "cydate"},
        {name: "结算时间", value: "jsdate"}
      ],
    }
  },
  methods: {
    async initData() {
      this.yyName = await getYyMame()
      getDeptList({}).then(res => {
        this.deptList = res.rows
        this.deptListby = JSON.parse(JSON.stringify(this.deptList))
      })
      this.getList()
    },
    mergeCells({ row, column, rowIndex, columnIndex }) {
      let cellMerge = { rowspan: 1, colspan: 1 };
      if (columnIndex !== 0) return cellMerge;
      const currentFieldValue = row.field;
      if (rowIndex > 0 && this.errorList[rowIndex - 1].field === currentFieldValue) {
        return { rowspan: 0, colspan: 0 };
      } else {
        for (let i = rowIndex + 1; i < this.errorList.length; i++) {
          if (this.errorList[i].field === currentFieldValue) {
            cellMerge.rowspan++;
          } else {
            break;
          }
        }
      }
      return cellMerge;
    },
    handleExport() {
      this.download('baxy/error/exportDeductScoreDetails', {
        ...this.errorQueryParams
      }, `${this.currentRow.xm}[${this.errorQueryParams.bah}]-病案评分明细.xlsx`)
    },
    closeScoreDetail() {
      this.errorQueryParams.bah = null
      this.errorQueryParams.brbs = null
      this.currentRow = null
      this.activeName = "errors"
    },
    stateFormat(row, column) {
      if (row.jlly === "3") {
        return '医生'
      } else if (row.jlly === "4") {
        return '病案室'
      } else {
        return ""
      }
    },
    getErrorList() {
      const query = {
        brbs: this.currentRow.brbs,
        bah: this.currentRow.bah,
        xm: this.currentRow.xm,
        jlly: this.currentRow.jlly,
      }
      if (this.yyName == '重庆市沙坪坝区陈家桥医院') {
        errList(query).then(res => {
          this.errorList = res.rows
          this.scoreDetailLoading = false
        }).catch(err => {
          this.scoreDetailLoading = false
        })
      } else {
        listError(query).then(res => {
          this.errorList = res.rows
          deductScoreDetails(query).then(res => {
            this.deductScoreList = res.rows
            this.scoreDetailLoading = false
          })
        }).catch(err => {
          this.scoreDetailLoading = false
        })
      }
    },
    openScoreDetail() {
      if (this.yyName == '重庆市沙坪坝区陈家桥医院') {
        this.scoreDetailOpenCjq = true
      } else {
        this.scoreDetailOpen = true
      }
    },
    scoreDetail(row) {
      this.scoreDetailTitle = row.xm + '--评分详情'
      this.openScoreDetail()
      this.currentRow = row
      this.scoreDetailLoading = true
      this.getErrorList()
    },
    patientDetail(row) {
      this.jzh = ""
      this.jzh = row.jzh

      this.patientDetailOpen = true
      this.patientDetailTitle = row.xm + '--病人详情'
    },
    refresh(row, index) {
      getBaSyjlScoreStats(row).then(res => {
        if (res.total < 0) {
          this.$modal.msgError("该病案首页已被删除")
          return
        }
        this.$set(this.basyList, index, res.rows[0])
        this.$modal.msgSuccess("刷新成功")
      }).catch(error => {
        this.$modal.msgError("刷新失败")
      })
    },
    handleExportScoreStats(){
      this.download('/drg/syjl/getBaSyjlScoreStats/export', {
        ...this.queryParams
      }, `病案评分查询.xlsx`)
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList()
    },
    getList() {
      if (!this.queryParams.dateType && (this.queryParams.adtFrom || this.queryParams.adtTo)) {
        this.queryParams.dateType = 'cydate'
      }
      this.loading = true
      getBaSyjlScoreStats(this.queryParams).then(res => {
        this.basyList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(error => {
        this.loading = false
      })
    },
    resetDept() {
      this.deptList = this.deptListby
    },
    resetDoctor() {
      this.doctorList = this.doctorListby
      if (this.queryParams.cykb == null) {
        this.$modal.msgWarning("请先选择科室")
        this.queryParams.zyys = null
        return
      }

      if (this.doctorList.length == 0) {
        this.getDoctorByDept(this.queryParams.cykb)
      }
    },
    deptFilter(val) {
      this.queryParams.cykb = val
      if (val) {
        this.deptList = []
        var deptList = this.deptListby.filter((item) => {
          if (PinYinMatch.match(item.cykb, val)) {
            return true
          }
        })
        this.deptList = deptList
      } else {
        this.deptList = this.deptListby
      }
    },
    doctorFilter(val) {
      this.queryParams.zyys = val
      if (val) {
        this.doctorList = []
        var doctorList = this.doctorListby.filter((item) => {
          if (PinYinMatch.match(item.zyys, val)) {
            return true
          }
        })
        this.doctorList = doctorList
      } else {
        this.doctorList = this.doctorListby
      }
    },
    getDoctorByDept(cykb) {
      this.queryParams.zyys = null
      this.deptList = this.deptListby
      getDoctorByDept({cykb: cykb}).then(response => {
        this.doctorList = response.rows;
        this.doctorListby = JSON.parse(JSON.stringify(this.doctorList))
      });
    },
    reset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        date: null,
        cblb: null,
        dateType: 'cydate',
        zyys: null,
        adtFrom: getMonthFirstDayStr(),
        adtTo: getTodayLastSecondStr(),
        bah: null,
        xm: null,
        minScore: null,
        maxScore: null,
      }
      this.doctorList = []
      this.handleQuery()
    },
  },
  created() {
    this.initData()
  }

}
</script>

<style scoped lang="scss">

</style>
