<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="病案号" prop="bah">
        <el-input
          v-model="queryParams.bah"
          placeholder="请输入病案号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="扣分" prop="score">
        <el-input
          v-model="queryParams.score"
          placeholder="请输入扣分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreatetime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport" >导出</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="errorList" @selection-change="handleSelectionChange">
      <el-table-column label="错误描述" align="center" prop="errordes" show-overflow-tooltip/>
      <el-table-column label="错误类型" align="center" prop="errortype" show-overflow-tooltip/>
      <el-table-column label="出错字段" align="center" prop="field" show-overflow-tooltip/>
      <el-table-column label="扣分" align="center" prop="score" show-overflow-tooltip/>
      <el-table-column label="记录来源" align="center" prop="jlly" show-overflow-tooltip/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listError, getError, delError, addError, updateError } from "@/api/baxy/error";

export default {
  name: "Error",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质控信息表格数据
      errorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否关心这个错时间范围
      daterangeCreatetime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        brbs: null,
        bah: null,
        code: null,
        errordes: null,
        type: null,
        trust: null,
        errortype: null,
        field: null,
        effectdrg: null,
        coltype: null,
        displayorder: null,
        originval: null,
        score: null,
        care: null,
        updatetime: null,
        createtime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        brbs: [
          { required: true, message: "病人标识不能为空", trigger: "blur" }
        ],
        trust: [
          { required: true, message: "所犯错误类型：强制还是疑似，0：疑似，1：强制不能为空", trigger: "blur" }
        ],
        errortype: [
          { required: true, message: "错误类型，6 多填 5 漏填, 4, 过滤, 3主诊, 2 清单生成错误, 1：编码错误，0：非编码错误 不能为空", trigger: "change" }
        ],
        effectdrg: [
          { required: true, message: "是否影响drg不能为空", trigger: "blur" }
        ],
        displayorder: [
          { required: true, message: "-1 无效order不能为空", trigger: "blur" }
        ],
        updatetime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" }
        ],
        createtime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询质控信息列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreatetime && '' != this.daterangeCreatetime) {
        this.queryParams.params["beginCreatetime"] = this.daterangeCreatetime[0];
        this.queryParams.params["endCreatetime"] = this.daterangeCreatetime[1];
      }
      listError(this.queryParams).then(response => {
        this.errorList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        brbs: null,
        bah: null,
        code: null,
        errordes: null,
        type: null,
        trust: null,
        errortype: null,
        field: null,
        effectdrg: null,
        coltype: null,
        displayorder: null,
        originval: null,
        score: null,
        care: null,
        updatetime: null,
        createtime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreatetime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加质控信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getError(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改质控信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateError(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addError(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除质控信息编号为"' + ids + '"的数据项？').then(function() {
        return delError(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('baxy/error/export', {
        ...this.queryParams
      }, `error_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
