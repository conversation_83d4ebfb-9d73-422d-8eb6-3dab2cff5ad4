<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="字段名称" prop="columnName">
        <el-input
          v-model="queryParams.columnName"
          placeholder="请输入字段名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="质控信息或提示" prop="tip">
        <el-input
          v-model="queryParams.tip"
          placeholder="请输入质控信息或提示"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="错误类型" prop="errtype">
        <el-select label="错误类型" v-model="queryParams.errtype">
          <el-option v-for="(item,index) in errtypeList" :label="item.label" :value="item.errtype"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="质控条件" prop="comments">
        <el-input
          v-model="queryParams.comments"
          placeholder="请输入质控条件"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-if="showDeleteIcon"
              >删除</el-button>
            </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="baxyList" @selection-change="handleSelectionChange">
      <el-table-column show-overflow-tooltip type="selection" width="55" align="center"/>
      <!--      <el-table-column show-overflow-tooltip label="记录ID" align="center" prop="id" />-->
      <el-table-column show-overflow-tooltip label="字段名称" align="center" prop="columnName"/>
      <el-table-column show-overflow-tooltip label="质控条件" align="center" prop="comments"/>
      <!--      <el-table-column show-overflow-tooltip label="是否必填" align="center" prop="ismust" />-->
      <!--      <el-table-column show-overflow-tooltip label="必填描述" align="center" prop="ismustdesc" />-->
      <!--      <el-table-column show-overflow-tooltip label="详细" align="center" prop="note" />-->
      <el-table-column show-overflow-tooltip label="错误类型" align="center" prop="errtype"/>
      <el-table-column width="400px" show-overflow-tooltip label="质控信息或提示" align="center" prop="tip"/>
      <el-table-column show-overflow-tooltip label="分数" align="center" prop="score"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDelete(scope.row)"
                      v-if="showDeleteIcon"
                    >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes=pageSizes
      @pagination="getList"
    />

    <!-- 添加或修改病案校验对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字段名称" prop="columnName">
          <el-input v-model="form.columnName" placeholder="请输入字段名称"/>
        </el-form-item>
        <el-form-item label="质控条件" prop="comments">
          <el-input v-model="form.comments" type="textarea" placeholder="请输入质控条件"/>
        </el-form-item>
        <!--        <el-form-item label="是否必填" prop="ismust">-->
        <!--          <el-input v-model="form.ismust" placeholder="请输入是否必填" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="必填描述" prop="ismustdesc">-->
        <!--          <el-input v-model="form.ismustdesc" placeholder="请输入必填描述" />-->
        <!--        </el-form-item>-->
        <el-form-item label="错误类型" prop="errtype">
          <!--          <el-input v-model="form.errtype" placeholder="请输入错误类型" />-->

          <el-select label="错误类型" v-model="form.errtype">
            <el-option v-for="(item,index) in errtype" :label="item.label" :value="item.value"></el-option>
          </el-select>


        </el-form-item>
        <!--        <el-form-item label="详细" prop="note">-->
        <!--          <el-input v-model="form.note" placeholder="请输入详细" />-->
        <!--        </el-form-item>-->
        <el-form-item label="质控信息或提示" prop="tip">
          <el-input v-model="form.tip" type="textarea" placeholder="请输入质控信息或提示"/>
        </el-form-item>
        <el-form-item label="分数" prop="score">
          <el-input type="number" v-model="form.score" placeholder="请输入分数"/>
        </el-form-item>
        <!--        <el-form-item label="合并编码" prop="hbbzbm">-->
        <!--          <el-input v-model="form.hbbzbm" placeholder="请输入合并编码" />-->
        <!--        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCheckbz,
  getCheckbz,
  delCheckbz,
  addCheckbz,
  updateCheckbz,
  getBBasyCheckBzErrortype
} from "@/api/baxy/check";
import { getOption } from '@/api/system/option'


const errtypeMappings = {
  "must": "必填",
  "hb": "合并",
  "man": "限男性",
  "woman": "限女性",
  "tip": "诊断提示",
  "zdmctip": "诊断名称提示",
  "notzzd": "不能作主要诊断",
  "zzdtip": "主要诊断提示",
  "notqtzd": "不能作其他诊断",
  "rybq": "入院病情判断",
  "sshb": "手术合并",
  "sstip": "手术提示",
  "zsstip": "主要手术提示",
  "zzdmctip": "主要诊断名称提示",
  "together": "同时存在的诊断",
  "zzdnottogether": "不能与主要诊断同时存在的诊断",
  "nottogether": "不能同时存在的诊断",
  "sstogether": "同时存在的手术",
};

export default {
  name: "Baxy",
  data() {
    return {
      errtype: [
        {value:"must",lable:"必填"},
        {value:"hb",lable:"合并"},
        {value:"man",lable:"限男性"},
        {value:"woman",lable:"限女性"},
        {value:"tip",lable:"诊断提示"},
        {value:"zdmctip",lable:"诊断名称提示"},
        {value:"notzzd",lable:"不能作主要诊断"},
        {value:"zzdtip",lable:"主要诊断提示"},
        {value:"notqtzd",lable:"不能作其他诊断"},
        {value:"sshb",lable:"手术合并"},
        {value:"sstip",lable:"手术提示"},
        {value:"zsstip",lable:"主要手术提示"},
        {value:"zzdmctip",lable:"主要诊断名称提示"},
        {value:"together",lable:"同时存在的诊断"},
        {value:"zzdnottogether",lable:"不能与主要诊断同时存在的诊断"},
        {value:"nottogether",lable:"不能同时存在的诊断"},
        {value:"sstogether",lable:"同时存在的手术"},
        {value:"rybq",lable:"入院病情判断"},
      ],
      pageSizes: [5],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 病案校验表格数据
      baxyList: [],
      errtypeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 5,
        columnName: null,
        comments: null,
        ismust: null,
        ismustdesc: null,
        note: null,
        errtype: null,
        tip: null,
        score: null,
        hbbzbm: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        comments: [
          {required: true, message: "内容不能为空", trigger: "blur"}
        ]
      },
      showDeleteIcon: false
    };
  },
  created() {
    this.initSystemOptions();
    this.getList();
    this.getBBasyCheckBzErrortype();
  },
  methods: {
    initSystemOptions(){
      getOption("use_baxy_rule_delete").then( res => {
        if(res.data && res.data.cValue == "1") {
          this.showDeleteIcon = true
        }
      })
    },
    /** 查询病案校验列表 */
    getList() {
      this.loading = true;
      listCheckbz(this.queryParams).then(response => {
        this.total = response.total;
        this.loading = false;
        let baxyList = response.rows;
        for (let i = 0; i < baxyList.length; i++) {
          if (errtypeMappings[baxyList[i].errtype] && baxyList[i].errtype != "zzdhb") {
            baxyList[i].errtype = errtypeMappings[baxyList[i].errtype]
          }
        }
        this.baxyList = baxyList;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        columnName: null,
        comments: null,
        ismust: null,
        ismustdesc: null,
        note: null,
        errtype: null,
        tip: null,
        score: null,
        hbbzbm: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加质控规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCheckbz(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改质控规则";
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (this.form.errtype == 'must' && (this.form.columnName == null || this.form.columnName == "")) {
        this.$modal.msgWarning("必填类型的质控规则字段名称不能为空");
        return;
      }


      if (this.form.columnName == null) {
        this.form.columnName = ""
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCheckbz(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCheckbz(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除质控规则编号为"' + ids + '"的数据项？').then(function () {
        return delCheckbz(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    getBBasyCheckBzErrortype() {
      getBBasyCheckBzErrortype().then(res => {
        this.errtypeList = res.rows
        for (let i = 0; i < this.errtypeList.length; i++) {
          if (errtypeMappings[this.errtypeList[i].errtype] && this.errtypeList[i].errtype != "zzdhb") {
            this.errtypeList[i]["label"] = errtypeMappings[this.errtypeList[i].errtype]
          }
        }
      })
    }
  }
};
</script>

<style>
.el-tooltip__popper {
  max-width: 400px !important; /* 您希望设置的最大宽度 */
}
</style>
