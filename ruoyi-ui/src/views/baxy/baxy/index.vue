<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">

      <el-form-item label="病案号" prop="bah">
        <el-input
          v-model="queryParams.bah"
          placeholder="请输入病案号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="科室">
        <el-select v-model="queryParams.cykb"
                   @focus="resetDept"
                   filterable
                   :filter-method="deptFilter"
                   placeholder="科室"
                   clearable
        >
          <el-option v-for="(item,index) in deptList" :value="item.cykb" :key="index" :label="item.cykb">
            {{ item.cykb }}
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="记录来源">
        <el-select v-model="queryParams.jlly" placeholder="记录来源" clearable>
          <el-option v-for="item in jllyList" :value="item.value" :label="item.name">{{ item.name }}</el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="日期范围" prop="cydate">
        <el-date-picker
          v-model="queryParams.adtFrom"
          type="datetime"
          placeholder="请选择开始日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        -
        <el-date-picker
          v-model="queryParams.adtTo"
          type="datetime"
          placeholder="请选择截至日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="日期类型">
        <el-select v-model="queryParams.dateType" placeholder="日期类型" clearable>
          <el-option v-for="item in datetypeList" :value="item.value" :label="item.name">{{ item.name }}</el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-table :border="true" v-loading="loading" :data="syjlList">
      <el-table-column label="病案号" align="center" prop="bah"/>
      <el-table-column label="姓名" align="center" prop="xm"/>
      <el-table-column label="性别" align="center" prop="xb"/>
      <el-table-column label="年龄" align="center" prop="nl"/>
      <el-table-column show-overflow-tooltip label="科别" align="center" prop="cykb"/>
      <el-table-column label="实际住院(天)" align="center" prop="sjzyts"/>
      <el-table-column label="主要诊断" align="center" prop="jbdm"/>
      <el-table-column label="主要手术" align="center" prop="ssjczbm1"/>
      <el-table-column label="入院时间" align="center" prop="rydate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.rydate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分数" align="center" prop="score">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="scoreDetail(scope.row)"
          >{{ scope.row.score }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="baxy(scope.row,scope.$index)"
          >校验
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="score(scope.row,scope.$index)"
          >评分
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-drawer
      :title="title"
      :visible.sync="open"
      direction="rtl"
      :wrapper-closable="false"
      size="80%">
      <el-button style="float: right;margin: 10px;" type="primary" @click="addError">添加违规记录</el-button>
      <el-table :border="true" v-loading="loading" :data="errList" height="450px">
        <el-table-column label="错误信息" align="left" prop="errordes"/>
        <el-table-column width="100px" label="扣分" align="center" prop="score"/>
        <el-table-column width="100px" label="错误来源" align="center" prop="source">
          <template slot-scope="{ row }">
            <div> {{ row.source == '1' ? '手动添加' : '系统质控'}}  </div>
          </template>
        </el-table-column>
        <el-table-column width="100px" label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.id"
              @click="delError(scope.row,scope.$index)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-table :border="true" v-loading="loading" :data="tipList">
        <el-table-column label="提示信息" align="left" prop="errordes"/>>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>

    <el-drawer
      :title="title"
      :visible.sync="openCjq"
      direction="rtl"
      :wrapper-closable="false"
      size="80%">
      <el-button style="float: right;margin: 10px;" type="primary" @click="addError">添加违规记录</el-button>
      <el-table :span-method="mergeCells2" :border="true" v-loading="loading" :data="errList" height="450px">
        <el-table-column width="200px" label="违规类型" align="left" prop="field" />
        <el-table-column label="错误信息" align="left" prop="errordes"/>
        <el-table-column width="100px" label="扣分" align="center" prop="score"/>
        <el-table-column width="100px" label="错误来源" align="center" prop="source">
          <template slot-scope="{ row }">
            <div> {{ row.source == '1' ? '手动添加' : '系统质控'}}  </div>
          </template>
        </el-table-column>
        <el-table-column width="100px" label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.id"
              @click="delError(scope.row,scope.$index)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-table :border="true" v-loading="loading" :data="tipList">
        <el-table-column label="提示信息" align="left" prop="errordes"/>>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>


    <el-dialog  :close-on-click-modal="false"  title="添加违规记录" :visible.sync="addErrorOpenCjq" append-to-body @close="addErrorClose">
      <el-form :rules="newErrorFormRules" ref="newError" label-positio="top" :model="newError">
        <el-form-item label="违规类型" prop="field">
          <el-cascader
            style="width: 100%"
            ref="refCascader"
            @change="setNewErrorScoreCjq"
            v-model="newError.field"
            :props="{ checkStrictly: true }"
            :options="typeOptions"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="错误类型" prop="errortype">
          <el-select  style="width: 100%" v-model="newError.errortype" placeholder="错误类型">
            <el-option label="名称错误" value="7"></el-option>
            <el-option label="编码错误" value="1"></el-option>
            <el-option label="漏填错误" value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="违规内容" prop="errordes">
          <br>
          <el-tabs v-model="changeOrAdd">
            <el-tab-pane label="调整" name="change">
              <el-input type="text" v-model="errContent.changeValue1"/>
              调整为
              <el-input type="text" v-model="errContent.changeValue2"/>
            </el-tab-pane>
            <el-tab-pane label="增加" name="add">
              增加<el-input type="text" v-model="errContent.addValue"/>
            </el-tab-pane>
          </el-tabs>
        </el-form-item>
        <el-form-item label="扣分" prop="score">
          <el-input type="number" v-model="newError.score" placeholder="扣分"/>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-plus" type="primary" @click="saveNewError">添加</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>


    <el-dialog   :close-on-click-modal="false" title="添加违规记录" :visible.sync="addErrorOpen" append-to-body @close="addErrorClose">
      <el-form :rules="newErrorFormRules" ref="newError" label-positio="top" :model="newError">
        <el-form-item label="违规类型" prop="field">
          <el-select  filterable  style="width: 100%" v-model="newError.field" placeholder="违规类型">
            <el-option
              v-for="dict in typeOptions"
              :key="dict.columnName"
              :label="dict.comments"
              :value="dict.columnName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="错误类型" prop="errortype">
          <el-select  style="width: 100%" v-model="newError.errortype" placeholder="错误类型">
            <el-option label="非编码错误" value="0"></el-option>
            <el-option label="编码错误" value="1"></el-option>
            <el-option label="清单生成错误" value="2"></el-option>
            <el-option label="主要诊断错误" value="3"></el-option>
            <el-option label="过滤错误" value="4"></el-option>
            <el-option label="漏填错误" value="5"></el-option>
            <el-option label="多填错误" value="6"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="违规内容" prop="errordes">
          <el-input type="textarea" :maxlength="500" :show-word-limit="true" v-model="newError.errordes" placeholder="违规内容"/>
        </el-form-item>
        <el-form-item label="扣分" prop="score">
          <el-input type="number" v-model="newError.score" placeholder="扣分"/>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-plus" type="primary" @click="saveNewError">添加</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>



    <el-drawer
      :title="scoreDetailTitle"
      :visible.sync="scoreDetailOpenCjq"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeScoreDetail"
      size="80%">
      <el-table :span-method="mergeCells" :border="true" v-loading="scoreDetailLoading" height="700px" :data="errorList">
        <el-table-column width="200px" label="违规类型" align="left" prop="field"/>
        <el-table-column label="错误描述" align="left" prop="errordes"/>
        <el-table-column width="200px" label="错误类型" align="left" prop="errortype" show-overflow-tooltip/>
        <el-table-column width="100px" label="扣分" align="left" prop="score" show-overflow-tooltip/>
        <el-table-column width="100px" label="记录来源" align="left" prop="jlly" show-overflow-tooltip/>
      </el-table>
    </el-drawer>

    <el-drawer
      :title="scoreDetailTitle"
      :visible.sync="scoreDetailOpen"
      direction="rtl"
      :wrapper-closable="false"
      @close="closeScoreDetail"
      size="80%">
      <el-table :border="true" v-loading="scoreDetailLoading" height="700px" :data="errorList">
        <el-table-column label="错误描述" align="left" prop="errordes" show-overflow-tooltip/>
        <el-table-column width="200px" label="错误类型" align="left" prop="errortype" show-overflow-tooltip/>
        <el-table-column width="100px" label="扣分" align="center" prop="score" show-overflow-tooltip/>
        <el-table-column width="100px" label="记录来源" align="left" prop="jlly" show-overflow-tooltip/>
      </el-table>
    </el-drawer>


  </div>
</template>



<script>
import {baxy, getBajyList, getBaSyjlScore, getDeptList} from "@/api/drg/syjl";
import PinYinMatch from "pinyin-match";
import {listError, delError, addErrorGetId} from "@/api/baxy/error";
import {baScore, errList} from "@/api/baxy/checkpf";
import {listCheckbz2, listCheckbzPf} from "@/api/baxy/check";
import {getMonthFirstDayStr, getTodayLastSecondStr} from "@/utils/dateUtils";
import {getYyMame} from "@/api/system/option";

export default {
  name: "Syjl",
  data() {
    return {
      yyName: null,
      changeOrAdd: 'change',
      errContent: {
        changeValue1: null,
        changeValue2: null,
        addValue: null
      },
      newError: {
        field: null,
        errordes: null,
        errortype: null,
        score: 0
      },
      addErrorOpen: false,
      addErrorOpenCjq: false,
      loading: true,
      typeOptions: [],
      // 总条数
      total: 0,
      // 病组分析表格数据
      syjlList: [],
      deptList: [],
      deptListby: [],
      datetypeList: [
        {name: "出院时间", value: "cydate"},
        {name: "结算时间", value: "jsdate"}
      ],
      jllyList: [
        {name: "医生", value: "3"},
        {name: "病案室", value: "4"}
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openCjq: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        adtFrom: getMonthFirstDayStr(),
        adtTo: getTodayLastSecondStr(),
        dateType: 'cydate',
        jzh: null,
        brbs: null,
        brid: null,
        zyid: null,
        username: null,
        ylfkfs: null,
        jkkh: null,
        zycs: null,
        bah: null,
        xm: null,
        xb: null,
        csrq: null,
        nl: null,
        gj: null,
        bzyzsnl: null,
        xsecstz: null,
        xserytz: null,
        csd: null,
        gg: null,
        mz: null,
        sfzh: null,
        zy: null,
        hy: null,
        xzz: null,
        dh: null,
        yb1: null,
        hkdz: null,
        yb2: null,
        gzdwjdz: null,
        dwdh: null,
        yb3: null,
        lxrxm: null,
        gx: null,
        dz: null,
        dh2: null,
        rytj: null,
        rysj: null,
        rysjs: null,
        rykb: null,
        rybf: null,
        zkkb: null,
        cysj: null,
        cysjs: null,
        cykb: null,
        cybf: null,
        sjzyts: null,
        mzzd: null,
        jbbm: null,
        zyzd: null,
        jbdm: null,
        rybq: null,
        qtzd1: null,
        jbdm1: null,
        rybq1: null,
        qtzd2: null,
        jbdm2: null,
        rybq2: null,
        qtzd3: null,
        jbdm3: null,
        rybq3: null,
        qtzd4: null,
        jbdm4: null,
        rybq4: null,
        qtzd5: null,
        jbdm5: null,
        rybq5: null,
        qtzd6: null,
        jbdm6: null,
        rybq6: null,
        qtzd7: null,
        jbdm7: null,
        rybq7: null,
        qtzd8: null,
        jbdm8: null,
        rybq8: null,
        qtzd9: null,
        jbdm9: null,
        rybq9: null,
        qtzd10: null,
        jbdm10: null,
        rybq10: null,
        qtzd11: null,
        jbdm11: null,
        rybq11: null,
        qtzd12: null,
        jbdm12: null,
        rybq12: null,
        qtzd13: null,
        jbdm13: null,
        rybq13: null,
        qtzd14: null,
        jbdm14: null,
        rybq14: null,
        qtzd15: null,
        jbdm15: null,
        rybq15: null,
        wbyy: null,
        h23: null,
        blzd: null,
        jbmm: null,
        blh: null,
        ywgm: null,
        gmyw: null,
        swhzsj: null,
        xx: null,
        rh: null,
        kzr: null,
        zrys: null,
        zzys: null,
        zyys: null,
        zrhs: null,
        jxys: null,
        sxys: null,
        bmy: null,
        bazl: null,
        zkys: null,
        zkhs: null,
        zkrq: null,
        ssjczbm1: null,
        ssjczrq1: null,
        ssjb1: null,
        ssjczmc1: null,
        sz1: null,
        yz1: null,
        ez1: null,
        qkdj1: null,
        qkyhlb1: null,
        mzfs1: null,
        mzys1: null,
        ssjczbm2: null,
        ssjczrq2: null,
        ssjb2: null,
        ssjczmc2: null,
        sz2: null,
        yz2: null,
        ez2: null,
        qkdj2: null,
        qkyhlb2: null,
        mzfs2: null,
        mzys2: null,
        ssjczbm3: null,
        ssjczrq3: null,
        ssjb3: null,
        ssjczmc3: null,
        sz3: null,
        yz3: null,
        ez3: null,
        qkdj3: null,
        qkyhlb3: null,
        mzfs3: null,
        mzys3: null,
        ssjczbm4: null,
        ssjczrq4: null,
        ssjb4: null,
        ssjczmc4: null,
        sz4: null,
        yz4: null,
        ez4: null,
        qkdj4: null,
        qkyhlb4: null,
        mzfs4: null,
        mzys4: null,
        ssjczbm5: null,
        ssjczrq5: null,
        ssjb5: null,
        ssjczmc5: null,
        sz5: null,
        yz5: null,
        ez5: null,
        qkdj5: null,
        qkyhlb5: null,
        mzfs5: null,
        mzys5: null,
        ssjczbm6: null,
        ssjczrq6: null,
        ssjb6: null,
        ssjczmc6: null,
        sz6: null,
        yz6: null,
        ez6: null,
        qkdj6: null,
        qkyhlb6: null,
        mzfs6: null,
        mzys6: null,
        ssjczbm7: null,
        ssjczrq7: null,
        ssjb7: null,
        ssjczmc7: null,
        sz7: null,
        yz7: null,
        ez7: null,
        qkdj7: null,
        qkyhlb7: null,
        mzfs7: null,
        mzys7: null,
        lyfs: null,
        yzzyYljg: null,
        wsyYljg: null,
        sfzzyjh: null,
        md: null,
        ryqT: null,
        ryqXs: null,
        ryqF: null,
        ryhT: null,
        ryhXs: null,
        ryhF: null,
        zfy: null,
        zfje: null,
        ylfuf: null,
        zlczf: null,
        hlf: null,
        qtfy: null,
        blzdf: null,
        syszdf: null,
        yxxzdf: null,
        lczdxmf: null,
        fsszlxmf: null,
        wlzlf: null,
        sszlf: null,
        maf: null,
        ssf: null,
        kff: null,
        zyzlf: null,
        xyf: null,
        kjywf: null,
        zcyf: null,
        zcyf1: null,
        xf: null,
        bdblzpf: null,
        qdblzpf: null,
        nxyzlzpf: null,
        xbyzlzpf: null,
        hcyyclf: null,
        yyclf: null,
        ycxyyclf: null,
        qtf: null,
        psh: null,
        basytype: null,
        orgcode: null,
        bycode: null,
        opname: null,
        opdate: null,
        xgcs: null,
        cxflag: null,
        jsdate: null,
        cqflag: null,
        jyflag: null,
        datasrc: null,
        jxstatus: null,
        sfsslcljgl: null,
        sfwclclj: null,
        sfby: null,
        byyy: null,
        ljbzmc: null,
        rydate: null,
        cydate: null,
        drgbh: null,
        rzflag: null,
        wrzyy: null,
        tczf: null,
        drgzf: null,
        jystatus: null,
        jlly: null,
        qjcs: null,
        qjcgcs: null,
        qzrq: null,
        zyzt: null,
        zdf: null,
        hisJsdate: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        jzh: [
          {required: true, message: "医保就诊号不能为空", trigger: "blur"}
        ],
        brbs: [
          {required: true, message: "病人标识不能为空", trigger: "blur"}
        ],
        score: [
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error('扣分不能小于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      },
      errList: [],
      tipList: [],
      scoreDetailOpen: false,
      scoreDetailOpenCjq: false,
      scoreDetailTitle: null,
      currentRow: null,
      errorList: [],
      scoreDetailLoading: true,
      newErrorFormRules: {
        field: [
          {required: true, message: '请选择违规类型', trigger: 'blur'},
        ],
        errordes: [
          {required: true, message: '请输入违规内容', trigger: 'submit'},
        ],
        errortype: [
          {required: true, message: "请选择错误类型", trigger: "blur"}
        ],
        score: [
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error('扣分不能小于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      },
    };
  },
  computed: {
    totalScore() {
      return 100 - this.errList.reduce((accumulator, currentValue) => {
        return accumulator + (currentValue.score || 0);
      }, 0);
    }
  },
  created() {
    this.initDate()
  },
  methods: {
    mergeCells2({ row, column, rowIndex, columnIndex }) {
      let cellMerge = { rowspan: 1, colspan: 1 };
      if (columnIndex !== 0) return cellMerge;
      const currentFieldValue = row.field;
      if (rowIndex > 0 && this.errList[rowIndex - 1].field === currentFieldValue) {
        return { rowspan: 0, colspan: 0 };
      } else {
        for (let i = rowIndex + 1; i < this.errList.length; i++) {
          if (this.errList[i].field === currentFieldValue) {
            cellMerge.rowspan++;
          } else {
            break;
          }
        }
      }
      return cellMerge;
    },
    mergeCells({ row, column, rowIndex, columnIndex }) {
      let cellMerge = { rowspan: 1, colspan: 1 };
      if (columnIndex !== 0) return cellMerge;
      const currentFieldValue = row.field;
      if (rowIndex > 0 && this.errorList[rowIndex - 1].field === currentFieldValue) {
        return { rowspan: 0, colspan: 0 };
      } else {
        for (let i = rowIndex + 1; i < this.errorList.length; i++) {
          if (this.errorList[i].field === currentFieldValue) {
            cellMerge.rowspan++;
          } else {
            break;
          }
        }
      }
      return cellMerge;
    },
    async initDate() {
      this.yyName = await getYyMame()
      getDeptList({}).then(res => {
        this.deptList = res.rows
        this.deptListby = JSON.parse(JSON.stringify(this.deptList))
      })
      this.getTypeOptions()
      this.getList();
    },
    delError(row, index) {
      this.$modal.confirm('请确认是否删除当前错误信息！').then(() => {
        delError(row.id).then(res => {
          if (res.code == 200) {
            this.$modal.msgSuccess("删除成功")
            this.getErrorList(1)
          }
        })
      }).catch(() => {
      });
    },
    setNewErrorScoreCjq(value) {
      this.$refs.refCascader.dropDownVisible = false;
      const parent = this.typeOptions.find(option => option.value === value[0])
      if (value.length > 1) {
        const children = parent.children.find(option => option.value === value[1])
        this.newError.score = children.score
      } else {
        this.newError.score = parent.score
      }
      console.log(this.newError.field)
    },
    async getTypeOptions() {
      console.log(this.yyName)
      if (this.yyName == '重庆市沙坪坝区陈家桥医院') {
        const res = await listCheckbzPf()
        if (res.code == 200) {
          this.typeOptions = res.rows;
        }
      } else {
        const res = await listCheckbz2({errtype: 'must'})
        if (res.code == 200) {
          this.typeOptions = res.rows
        }
      }
    },
    saveNewError() {
      if (this.yyName == '重庆市沙坪坝区陈家桥医院') {
        if (this.errContent.addValue) {
          this.newError.errordes = "增加" + this.errContent.addValue
        } else if (this.errContent.changeValue1 && this.errContent.changeValue2) {
          this.newError.errordes = this.errContent.changeValue1 + '调整为：' + this.errContent.changeValue2
        } else {
          this.newError.errordes = null
        }
      }
      if (!this.newError.score) {
        this.newError.score = 0
      }
      this.$refs['newError'].validate((valid) => {
        if (valid) {
          const field = this.newError.field
          this.newError.field = field[field.length - 1]
          this.newError.bah = this.currentRow.bah
          this.newError.brbs = this.currentRow.brbs
          this.newError.jlly = this.currentRow.jlly
          this.newError.code = this.newError.field
          this.newError.source = '1'
          addErrorGetId(this.newError).then(res => {
            if (res.code == 200) {
              this.$modal.msgSuccess("添加成功")
              this.getErrorList(1)
              this.addErrorOpen = false
              this.addErrorOpenCjq = false
            }
          }).catch(err => {
            this.$modal.msgSuccess("添加失败")
          })
        } else {
          return false;
        }
      });
    },
    addErrorClose() {
      this.newError = {
        field: null,
        errordes: null,
        errortype: null,
        score: 0
      }
      this.errContent = {
        changeValue1: null,
        changeValue2: null,
        addValue: null
      }
    },
    addError() {
      if (this.yyName == '重庆市沙坪坝区陈家桥医院') {
        this.addErrorOpenCjq = true
      } else {
        this.addErrorOpen = true
      }
    },
    closeScoreDetail() {
      this.currentRow = null
    },
    scoreDetail(row) {
      this.scoreDetailLoading = true
      this.scoreDetailTitle = row.xm + '--评分明细'
      this.currentRow = row
      this.getErrorList()
    },
    getErrorList(flag) {
      const query = {brbs: this.currentRow.brbs, jlly: this.currentRow.jlly}
      if (this.yyName == "重庆市沙坪坝区陈家桥医院") {
        errList(query).then(res => {
          if (flag != 1) {
            this.errorList = res.rows
            this.openScoreDetail()
          } else {
            this.errList = res.rows
          }
          this.scoreDetailLoading = false
        }).catch(err => {
          this.scoreDetailLoading = false
        })
      } else {
        listError(query).then(res => {
          if (flag != 1) {
            this.errorList = res.rows
            this.openScoreDetail()
          } else {
            this.errList = res.rows
          }
          this.scoreDetailLoading = false
        }).catch(err => {
          this.scoreDetailLoading = false
        })
      }
    },
    openScoreDetail() {
      if (this.yyName == '重庆市沙坪坝区陈家桥医院') {
        this.scoreDetailOpenCjq = true
      } else {
        this.scoreDetailOpen = true
      }
    },
    resetDept() {
      this.deptList = this.deptListby
    },
    deptFilter(val) {
      this.queryParams.cykb = val
      if (val) {
        this.deptList = []
        var deptList = this.deptListby.filter((item) => {
          if (PinYinMatch.match(item.cykb, val)) {
            return true
          }
        })
        this.deptList = deptList
      } else {
        this.deptList = this.deptListby
      }
    },
    async score(row, index) {
      const query = {brbs: row.brbs,jlly: row.jlly}
      if (this.yyName == '重庆市沙坪坝区陈家桥医院') {
        const res = await baScore(query)
        if (res.code == 200) {
          this.syjlList[index].score = res.data
        }
        this.$modal.msgSuccess("刷新成功")
      } else {
        const res = await getBaSyjlScore(query)
        if (res.code == 200) {
          this.syjlList[index].score = res
        }
        this.$modal.msgSuccess("刷新成功")
      }
    },
    /** 查询病组分析列表 */
    getList() {
      this.loading = true;
      if (!this.queryParams.dateType && (this.queryParams.adtFrom || this.queryParams.adtTo)) {
        this.queryParams.dateType = 'cydate'
      }
      getBajyList(this.queryParams).then(response => {
        this.syjlList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        jzh: null,
        brbs: null,
        brid: null,
        zyid: null,
        username: null,
        ylfkfs: null,
        jkkh: null,
        zycs: null,
        bah: null,
        xm: null,
        xb: null,
        csrq: null,
        nl: null,
        gj: null,
        bzyzsnl: null,
        xsecstz: null,
        xserytz: null,
        csd: null,
        gg: null,
        mz: null,
        sfzh: null,
        zy: null,
        hy: null,
        xzz: null,
        dh: null,
        yb1: null,
        hkdz: null,
        yb2: null,
        gzdwjdz: null,
        dwdh: null,
        yb3: null,
        lxrxm: null,
        gx: null,
        dz: null,
        dh2: null,
        rytj: null,
        rysj: null,
        rysjs: null,
        rykb: null,
        rybf: null,
        zkkb: null,
        cysj: null,
        cysjs: null,
        cykb: null,
        cybf: null,
        sjzyts: null,
        mzzd: null,
        jbbm: null,
        zyzd: null,
        jbdm: null,
        rybq: null,
        qtzd1: null,
        jbdm1: null,
        rybq1: null,
        qtzd2: null,
        jbdm2: null,
        rybq2: null,
        qtzd3: null,
        jbdm3: null,
        rybq3: null,
        qtzd4: null,
        jbdm4: null,
        rybq4: null,
        qtzd5: null,
        jbdm5: null,
        rybq5: null,
        qtzd6: null,
        jbdm6: null,
        rybq6: null,
        qtzd7: null,
        jbdm7: null,
        rybq7: null,
        qtzd8: null,
        jbdm8: null,
        rybq8: null,
        qtzd9: null,
        jbdm9: null,
        rybq9: null,
        qtzd10: null,
        jbdm10: null,
        rybq10: null,
        qtzd11: null,
        jbdm11: null,
        rybq11: null,
        qtzd12: null,
        jbdm12: null,
        rybq12: null,
        qtzd13: null,
        jbdm13: null,
        rybq13: null,
        qtzd14: null,
        jbdm14: null,
        rybq14: null,
        qtzd15: null,
        jbdm15: null,
        rybq15: null,
        wbyy: null,
        h23: null,
        blzd: null,
        jbmm: null,
        blh: null,
        ywgm: null,
        gmyw: null,
        swhzsj: null,
        xx: null,
        rh: null,
        kzr: null,
        zrys: null,
        zzys: null,
        zyys: null,
        zrhs: null,
        jxys: null,
        sxys: null,
        bmy: null,
        bazl: null,
        zkys: null,
        zkhs: null,
        zkrq: null,
        ssjczbm1: null,
        ssjczrq1: null,
        ssjb1: null,
        ssjczmc1: null,
        sz1: null,
        yz1: null,
        ez1: null,
        qkdj1: null,
        qkyhlb1: null,
        mzfs1: null,
        mzys1: null,
        ssjczbm2: null,
        ssjczrq2: null,
        ssjb2: null,
        ssjczmc2: null,
        sz2: null,
        yz2: null,
        ez2: null,
        qkdj2: null,
        qkyhlb2: null,
        mzfs2: null,
        mzys2: null,
        ssjczbm3: null,
        ssjczrq3: null,
        ssjb3: null,
        ssjczmc3: null,
        sz3: null,
        yz3: null,
        ez3: null,
        qkdj3: null,
        qkyhlb3: null,
        mzfs3: null,
        mzys3: null,
        ssjczbm4: null,
        ssjczrq4: null,
        ssjb4: null,
        ssjczmc4: null,
        sz4: null,
        yz4: null,
        ez4: null,
        qkdj4: null,
        qkyhlb4: null,
        mzfs4: null,
        mzys4: null,
        ssjczbm5: null,
        ssjczrq5: null,
        ssjb5: null,
        ssjczmc5: null,
        sz5: null,
        yz5: null,
        ez5: null,
        qkdj5: null,
        qkyhlb5: null,
        mzfs5: null,
        mzys5: null,
        ssjczbm6: null,
        ssjczrq6: null,
        ssjb6: null,
        ssjczmc6: null,
        sz6: null,
        yz6: null,
        ez6: null,
        qkdj6: null,
        qkyhlb6: null,
        mzfs6: null,
        mzys6: null,
        ssjczbm7: null,
        ssjczrq7: null,
        ssjb7: null,
        ssjczmc7: null,
        sz7: null,
        yz7: null,
        ez7: null,
        qkdj7: null,
        qkyhlb7: null,
        mzfs7: null,
        mzys7: null,
        lyfs: null,
        yzzyYljg: null,
        wsyYljg: null,
        sfzzyjh: null,
        md: null,
        ryqT: null,
        ryqXs: null,
        ryqF: null,
        ryhT: null,
        ryhXs: null,
        ryhF: null,
        zfy: null,
        zfje: null,
        ylfuf: null,
        zlczf: null,
        hlf: null,
        qtfy: null,
        blzdf: null,
        syszdf: null,
        yxxzdf: null,
        lczdxmf: null,
        fsszlxmf: null,
        wlzlf: null,
        sszlf: null,
        maf: null,
        ssf: null,
        kff: null,
        zyzlf: null,
        xyf: null,
        kjywf: null,
        zcyf: null,
        zcyf1: null,
        xf: null,
        bdblzpf: null,
        qdblzpf: null,
        nxyzlzpf: null,
        xbyzlzpf: null,
        hcyyclf: null,
        yyclf: null,
        ycxyyclf: null,
        qtf: null,
        psh: null,
        basytype: null,
        orgcode: null,
        bycode: null,
        opname: null,
        opdate: null,
        xgcs: null,
        cxflag: null,
        jsdate: null,
        cqflag: null,
        jyflag: null,
        datasrc: null,
        jxstatus: null,
        sfsslcljgl: null,
        sfwclclj: null,
        sfby: null,
        byyy: null,
        ljbzmc: null,
        rydate: null,
        cydate: null,
        drgbh: null,
        rzflag: null,
        wrzyy: null,
        tczf: null,
        drgzf: null,
        jystatus: null,
        jlly: null,
        qjcs: null,
        qjcgcs: null,
        qzrq: null,
        zyzt: null,
        zdf: null,
        hisJsdate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.cykb = null
      this.queryParams.bah = null
      this.queryParams.dateType = 'cydate'
      this.queryParams.adtFrom = getMonthFirstDayStr()
      this.queryParams.adtTo = getTodayLastSecondStr()
      this.handleQuery();
    },
    async baxy(row, index) {
      this.loading = true
      this.title = row.xm + '[' + row.bah + ']-质控结果'
      this.currentRow = row
      const data = {
        brbs: row.brbs
      }
      const res = await baxy(data).catch(err => {
        this.loading = false
      })
      console.log(res)
      if (res.code == 200) {
        this.loading = false
        let zkInfo = res.rows
        this.errList = zkInfo.filter(item => item.id != null && item.id !== '');
        this.tipList = zkInfo.filter(item => item.id == null || item.id === '');
        if (this.yyName == '重庆市沙坪坝区陈家桥医院') {
          this.openCjq = true
          const query = {
            brbs: row.brbs,
            jlly: row.jlly
          }
          const res = await baScore(query)
          if (res.code == 200) {
            this.syjlList[index].score = res.data
          }
        } else {
          this.open = true
          this.syjlList[index].score = this.totalScore
        }
      }
    },
  }
};
</script>


<style>
.el-radio input[aria-hidden="true"] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}
</style>
