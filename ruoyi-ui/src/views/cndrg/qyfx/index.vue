<template>
  <div class="medical-report-container">
    <!-- 页面头部 -->
    <el-row :gutter="20" class="page-header" >
      <el-col :span="24">
        <el-page-header
          @back="handleBack"
          content="全院CNDRG统计分析">
        </el-page-header>
      </el-col>
    </el-row>

    <!-- 筛选和操作区 -->
    <el-row :gutter="20" class="filter-bar" >
      <el-col :span="6" :xs="24">
        <el-select
          v-model="queryParams.datatype"
          placeholder="请选择日期类型"
          clearable
          style="width: 100%;">
          <el-option label="结算时间" value="jsdate"></el-option>
          <el-option label="出院时间" value="cydate"></el-option>
        </el-select>
      </el-col>
      <el-col :span="6" :xs="24">
        <el-date-picker
          v-model="queryParams.startDate"
          type="date"
          placeholder="开始日期"
          value-format="yyyy-MM-dd"
          style="width: 100%;">
        </el-date-picker>
      </el-col>

      <el-col :span="6" :xs="24">
        <el-date-picker
          v-model="queryParams.endDate"
          type="date"
          placeholder="截止日期"
          value-format="yyyy-MM-dd"
          style="width: 100%;">
        </el-date-picker>
      </el-col>

      <el-col :span="6" :xs="24">
        <el-select
          v-model="queryParams.ksname"
          placeholder="请选择科室"
          clearable
          style="width: 100%;">
          <el-option v-for="item in deptList" :value="item.cykb" :key="item.cykb">{{ item.cykb }}</el-option>
        </el-select>
      </el-col>
      <el-col style="margin-top: 10px" :span="6" :xs="24">
        <el-select
          v-model="queryParams.as_cblb"
          placeholder="参保类别"
          clearable
          style="width: 100%;">
          <el-option
            v-for="dict in dict.type.cblb"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-col>

      <el-col :span="24" style="margin-top: 10px;">
        <el-button type="primary" @click="getList">
          <i class="el-icon-search"></i> 查询
        </el-button>
        <el-button @click="resetFilters">
          <i class="el-icon-refresh"></i> 重置
        </el-button>
<!--        <el-button type="success" @click="exportData" style="margin-left: 10px;">-->
<!--          <i class="el-icon-download"></i> 导出数据-->
<!--        </el-button>-->
      </el-col>
    </el-row>

    <!-- 数据概览卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6" :xs="12" :sm="6">
        <el-card class="stat-card" :body-style="{ padding: '15px' }">
          <div class="stat-item">
            <div class="stat-info">
              <p class="stat-label">总病案数</p>
              <h3 class="stat-value">{{qyData.allCndrgGroupCount}}</h3>
              <p class="stat-change positive">
                <i class="el-icon-arrow-up"></i>
              </p>
            </div>
            <div class="stat-icon primary">
              <i class="el-icon-document"></i>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6" :xs="12" :sm="6">
        <el-card class="stat-card" :body-style="{ padding: '15px' }">
          <div class="stat-item">
            <div class="stat-info">
              <p class="stat-label">总费用(万元)</p>
              <h3 class="stat-value">{{qyData.zfy}}</h3>
              <p class="stat-change positive">
                <i class="el-icon-arrow-up"></i>
              </p>
            </div>
            <div class="stat-icon success">
              <i class="el-icon-money"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6" :xs="12" :sm="6">
        <el-card class="stat-card" :body-style="{ padding: '15px' }">
          <div class="stat-item">
            <div class="stat-info">
              <p class="stat-label">平均CMI</p>
              <h3 class="stat-value">{{qyData.cmi}}</h3>
              <p class="stat-change positive">
                <i class="el-icon-arrow-up"></i>
              </p>
            </div>
            <div class="stat-icon warning">
              <i class="el-icon-s-management"></i>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6" :xs="12" :sm="6">
        <el-card class="stat-card" :body-style="{ padding: '15px' }">
          <div class="stat-item">
            <div class="stat-info">
              <p class="stat-label">入组率</p>
              <h3 class="stat-value">{{toFixed(qyData.groupCount/qyData.allCndrgGroupCount,2)*100}}%</h3>
              <p class="stat-change positive">
                <i class="el-icon-arrow-up"></i>
              </p>
            </div>
            <div class="stat-icon info">
              <i class="el-icon-pie-chart"></i>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 新增的统计卡片 -->
      <el-col style="margin-top: 10px" :span="6" :xs="12" :sm="6">
        <el-card class="stat-card" :body-style="{ padding: '15px' }">
          <div class="stat-item">
            <div class="stat-info">
              <p class="stat-label">CNDRG组数</p>
              <h3 class="stat-value">{{qyData.cndrgGroupCount}}</h3>
              <p class="stat-change positive">
                <i class="el-icon-arrow-up"></i>
              </p>
            </div>
            <div class="stat-icon primary">
              <i class="el-icon-collection"></i>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col  style="margin-top: 10px" :span="6" :xs="12" :sm="6">
        <el-card class="stat-card" :body-style="{ padding: '15px' }">
          <div class="stat-item">
            <div class="stat-info">
              <p class="stat-label">低风险数</p>
              <h3 class="stat-value">{{qyData.dfxs}}</h3>
              <p class="stat-change positive">
                <i class="el-icon-arrow-up"></i>
              </p>
            </div>
            <div class="stat-icon success">
              <i class="el-icon-bank-card"></i>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col  style="margin-top: 10px"  :span="6" :xs="12" :sm="6">
        <el-card class="stat-card" :body-style="{ padding: '15px' }">
          <div class="stat-item">
            <div class="stat-info">
              <p class="stat-label">死亡低风险数</p>
              <h3 class="stat-value">{{qyData.swdfxs}}</h3>
              <p class="stat-change negative">
                <i class="el-icon-arrow-down"></i>
              </p>
            </div>
            <div class="stat-icon warning">
              <i class="el-icon-trophy"></i>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col  style="margin-top: 10px"  :span="6" :xs="12" :sm="6">
        <el-card class="stat-card" :body-style="{ padding: '15px' }">
          <div class="stat-item">
            <div class="stat-info">
              <p class="stat-label">总权重</p>
              <h3 class="stat-value">{{qyData.allQz}}</h3>
              <p class="stat-change positive">
                <i class="el-icon-arrow-up"></i>
              </p>
            </div>
            <div class="stat-icon info">
              <i class="el-icon-data-line"></i>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-area">
      <el-col :span="12" :xs="24">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>各科室总病案数对比</span>
          </div>
          <div class="chart-container">
            <div ref="departmentChart" class="chart-wrapper"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12" :xs="24">
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>科室CMI指数</span>
          </div>
          <div class="chart-container">
            <div ref="cmiChart" class="chart-wrapper"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-row class="data-table">
      <el-col :span="24">
        <el-card class="table-card">
          <div slot="header" class="table-header">
            <span>详细数据列表</span>
          </div>
          <el-table
            :data="tableData"
            border            style="width: 100%"
            highlight-current-row
            @sort-change="handleSortChange"
            :scroll="{ x: 'max-content' }">
            <el-table-column
              prop="ksname"
              label="出院科别"
              sortable
              min-width="120">
            </el-table-column>
            <el-table-column
              prop="cndrgGroupCount"
              label="CNDRG组数"
              sortable
              min-width="100">
            </el-table-column>
            <el-table-column
              prop="allCndrgGroupCount"
              label="总病案数"
              sortable
              min-width="100">
            </el-table-column>

            <el-table-column
              prop="groupCount"
              label="入组病案数"
              sortable
              min-width="110">
            </el-table-column>
            <el-table-column
              prop="allQz"
              label="总权重"
              sortable
              min-width="100">
            </el-table-column>
            <el-table-column
              prop="avgZyr"
              label="平均住院日"
              sortable
              min-width="100">
            </el-table-column>
            <el-table-column
              prop="zfy"
              label="总费用(元)"
              sortable
              min-width="120">
              <template slot-scope="scope">
                {{ scope.row.zfy }}
              </template>
            </el-table-column>
            <el-table-column
              prop="dfxs"
              label="低风险数"
              sortable
              min-width="110">
              <template slot-scope="scope">
                {{ scope.row.dfxs}}
              </template>
            </el-table-column>
            <el-table-column
              prop="swdfxs"
              label="死亡低风险数"
              sortable
              min-width="110">
              <template slot-scope="scope">
                {{ scope.row.swdfxs}}
              </template>
            </el-table-column>
            <el-table-column
              prop="cmi"
              label="CMI"
              sortable
              min-width="80">
              <template slot-scope="scope">
                <span>{{ scope.row.cmi }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getDeptListFromSy } from '@/api/tjfx/tjfx'
import { getCndrgQyfx } from '@/api/drg/cndrg'
import toFixed from 'xe-utils/toFixed'

export default {
  name: 'MedicalReport',
  dicts: ['cblb', 'sys_normal_disable'],

  data() {
    return {
      ksnameList: [],
      cndrgGroupCountList: [],
      cmiList: [],
      zfyList: [],
      zqzList: [],
      // 筛选条件
      deptFilter: '',
      startDate: '',
      endDate: '',
      insuranceType: '',
      searchKeyword: '',

      // 图表相关
      deptChartType: 'month',
      cmiChartType: '6month',
      charts: {},
      queryParams: {
        startDate: '',
        endDate: '',
        datatype: 'cydate',
        as_cblb: '%',
        ksname: '所有',
        qsfs: '%',
      },
      deptList: [],
      // 表格相关
      tableData: [

      ],
      qyData: {
        allCndrgGroupCount: 0,
        zfy: 0,
        cmi: 0,
        groupPercent: 0,
        cndrgGroupCount: 0,
        tc: 0,
        de: 0,
        allQz: 0
      },
      currentPage: 1,
      pageSize: 5,
      sortProp: '',
      sortOrder: '',
      loading: true
    }
  },
  computed: {
    // 筛选后的表格数据
    filteredTableData() {
      let data = [...this.tableData]

      // 搜索筛选
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        data = data.filter(item =>
          item.deptName.toLowerCase().includes(keyword)
        )
      }

      // 科室筛选
      if (this.deptFilter) {
        data = data.filter(item => item.deptName === this.deptFilter)
      }

      // 参保类别筛选 (根据实际数据结构可能需要调整)
      if (this.insuranceType) {
        // 这里需要根据实际数据结构调整筛选逻辑
        // 示例：假设表格数据中有 insuranceType 字段
        // data = data.filter(item => item.insuranceType === this.insuranceType)
      }

      // 排序
      if (this.sortProp && this.sortOrder) {
        data.sort((a, b) => {
          if (this.sortOrder === 'ascending') {
            return a[this.sortProp] - b[this.sortProp]
          } else {
            return b[this.sortProp] - a[this.sortProp]
          }
        })
      }

      // 分页
      const startIndex = (this.currentPage - 1) * this.pageSize
      return data.slice(startIndex, startIndex + this.pageSize)
    }
  },
  mounted() {
    // 初始化图表
    this.initDept()
    this.loadData()
    this.getList()
    // 监听窗口大小变化，重绘图表
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleWindowResize)

    // 销毁图表
    Object.values(this.charts).forEach(chart => {
      chart.dispose()
    })
  },
  methods: {
    toFixed,
    getList(){
      getCndrgQyfx(this.queryParams).then(res => {
        this.tableData = res.data.rows
        this.qyData = this.tableData[this.tableData.length - 1];
        this.ksnameList = res.data.ksnameList
        this.cmiList = res.data.cmiList
        this.cndrgGroupCountList = res.data.cndrgGroupCountList
        this.initCharts()
        this.loading = false;
      })
    },

    formatDate(date) {
      const year = date.getFullYear();
      const month = ('0' + (date.getMonth() + 1)).slice(-2);
      const day = ('0' + date.getDate()).slice(-2);
      return year + '-' + month + '-' + day;
    },
    loadData(){
      // 设置默认时间范围为上个月1号到上个月月底
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const end = new Date(now.getFullYear(), now.getMonth(), 0);
      this.queryParams.startDate = this.formatDate(start);
      this.queryParams.endDate = this.formatDate(end);
    },
    async initDept(){
      const res = await getDeptListFromSy()
      this.deptList.push({
        cykb: '所有',
        value: '所有'
      })
      res.list.forEach(item => {
        this.deptList.push({
          cykb: item
        })
      })

    },
    // 返回上一页
    handleBack() {
      this.$router.go(-1)
    },



    // 重置筛选条件
    resetFilters() {
      this.queryParams =  {
        startDate: '',
          endDate: '',
          datatype: 'cydate',
          as_cblb: '%',
          ksname: '所有',
          qsfs: '%',
      },
        this.loadData()
      this.getList()
    },

    // 导出数据
    exportData() {
      this.$message.success('数据导出成功')
      // 实际项目中这里会实现数据导出逻辑
    },

    // 处理图表类型变化
    handleChartTypeChange(type) {
      if (type === 'dept') {
        this.initDepartmentChart()
      } else if (type === 'cmi') {
        this.initCmiChart()
      }
    },

    // 初始化所有图表
    initCharts() {
      this.initDepartmentChart()
      this.initCmiChart()
    },

    // 初始化科室病案数图表
    initDepartmentChart() {
      // 如果图表已存在则销毁
      if (this.charts.departmentChart) {
        this.charts.departmentChart.dispose()
      }

      // 创建图表实例
      const chart = echarts.init(this.$refs.departmentChart)
      this.charts.departmentChart = chart

      // 配置图表
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          padding: 10,
          borderRadius: 4
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.ksnameList,
            axisTick: {
              alignWithLabel: true
            },
            axisLine: {
              lineStyle: {
                color: '#e6e6e6'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#f5f5f5'
              }
            }
          }
        ],
        series: [
          {
            name: '总病案数',
            type: 'bar',
            barWidth: '60%',
            data: this.cndrgGroupCountList,
            itemStyle: {
              color: '#1890ff'
            },
            emphasis: {
              itemStyle: {
                color: '#096dd9'
              }
            }
          }
        ]
      }

      // 设置图表配置
      chart.setOption(option)
    },

    // 初始化科室CMI指数图表
    initCmiChart() {
      // 如果图表已存在则销毁
      if (this.charts.cmiChart) {
        this.charts.cmiChart.dispose()
      }

      // 创建图表实例
      const chart = echarts.init(this.$refs.cmiChart)
      this.charts.cmiChart = chart

      // 配置图表
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          padding: 10,
          borderRadius: 4
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.ksnameList,
            axisTick: {
              alignWithLabel: true
            },
            axisLine: {
              lineStyle: {
                color: '#e6e6e6'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#f5f5f5'
              }
            }
          }
        ],
        series: [
          {
            name: 'CMI指数',
            type: 'bar',
            barWidth: '60%',
            data: this.cmiList,
            itemStyle: {
              color: '#1890ff'
            },
            emphasis: {
              itemStyle: {
                color: '#096dd9'
              }
            }
          }
        ]
      }

      // 设置图表配置
      chart.setOption(option)
    },

    // 初始化科室权重指数趋势图表
    initWeightChart() {
      // 如果图表已存在则销毁
      if (this.charts.weightChart) {
        this.charts.weightChart.dispose()
      }

      // 创建图表实例
      const chart = echarts.init(this.$refs.weightChart)
      this.charts.weightChart = chart

      // 配置图表
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          padding: 10,
          borderRadius: 4
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.ksnameList,
            axisTick: {
              alignWithLabel: true
            },
            axisLine: {
              lineStyle: {
                color: '#e6e6e6'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#f5f5f5'
              }
            }
          }
        ],
        series: [
          {
            name: '权重指数',
            type: 'bar',
            barWidth: '60%',
            data: this.zqzList,
            itemStyle: {
              color: '#faad14'
            },
            emphasis: {
              itemStyle: {
                color: '#d48806'
              }
            }
          }
        ]
      }

      // 设置图表配置
      chart.setOption(option)
    },

    // 初始化科室费用指数趋势图表
    initCostChart() {
      // 如果图表已存在则销毁
      if (this.charts.costChart) {
        this.charts.costChart.dispose()
      }

      // 创建图表实例
      const chart = echarts.init(this.$refs.costChart)
      this.charts.costChart = chart

      // 配置图表
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          padding: 10,
          borderRadius: 4
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.ksnameList,
            axisTick: {
              alignWithLabel: true
            },
            axisLine: {
              lineStyle: {
                color: '#e6e6e6'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#f5f5f5'
              }
            }
          }
        ],
        series: [
          {
            name: '费用指数',
            type: 'bar',
            barWidth: '60%',
            data: this.zfyList,
            itemStyle: {
              color: '#52c41a'
            },
            emphasis: {
              itemStyle: {
                color: '#389e0d'
              }
            }
          }
        ]
      }

      // 设置图表配置
      chart.setOption(option)
    },

    // 处理窗口大小变化
    handleWindowResize() {
      Object.values(this.charts).forEach(chart => {
        chart.resize()
      })
    },

    // 表格排序变化
    handleSortChange({ prop, order }) {
      this.sortProp = prop
      this.sortOrder = order
      this.currentPage = 1 // 重置到第一页
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1 // 重置到第一页
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 格式化数字为带千分位的格式
    formatNumber(num) {
      return num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    // 根据CMI值获取样式类
    getCmiClass(cmi) {
      if (cmi > 1.5) {
        return 'text-success'
      } else if (cmi < 1.2) {
        return 'text-danger'
      }
      return ''
    }
  }
}
</script>

<style scoped>
.medical-report-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.filter-bar {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0 0 5px 0;
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin: 0 0 5px 0;
}

.stat-change {
  font-size: 12px;
  margin: 0;
}

.stat-change.positive {
  color: #52c41a;
}

.stat-change.negative {
  color: #f5222d;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.stat-icon.primary {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.stat-icon.success {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.stat-icon.warning {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.stat-icon.info {
  background-color: rgba(15, 157, 255, 0.1);
  color: #00a8ff;
}

.charts-area {
  margin-bottom: 20px;
}

.chart-card {
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  width: 100%;
  height: 350px;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
}

.table-card {
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  width: 200px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.text-success {
  color: #52c41a !important;
}

.text-danger {
  color: #f5222d !important;
}
.table-card {
  overflow: hidden;
}

.table-card :deep(.el-table) {
  width: 100% !important;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  width: 200px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.text-success {
  color: #52c41a !important;
}

.text-danger {
  color: #f5222d !important;
}
</style>
