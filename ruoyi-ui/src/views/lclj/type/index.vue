<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="拼音码" prop="cNccd">
        <el-input
          v-model="queryParams.cNccd"
          placeholder="请输入拼音码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="路径名称" prop="cName">
        <el-input
          v-model="queryParams.cName"
          placeholder="请输入临床路径名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning"  icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['lclj:type:export']">导出</el-button>
        <el-button icon="el-icon-edit" type="success" size="mini" @click="showlcljData">查看</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="typeList"  highlight-current-row @row-click="setlcljDate">
      <el-table-column label="有效" align="center" prop="cStatus" >
        <template slot-scope="scope">
          <input type="checkbox" :checked="scope.row.cStatus==1" @input="setStatus(scope.row)">
        </template>
      </el-table-column>
      <el-table-column label="编码" align="center" prop="cCode"/>
      <el-table-column label="临床路径名称" align="center" prop="cName"  show-overflow-tooltip width="200px"/>
      <!--      <el-table-column label="拼音码" align="center" prop="cNccd" />
            <el-table-column label="五笔码" align="center" prop="cWbcode" /> -->
      <el-table-column label="病种编码" align="center" prop="cIcdCode" show-overflow-tooltip />
      <!--   <el-table-column label="中医病种编码" align="center" prop="cIcdCodeZy" /> -->
      <!-- <el-table-column label="科室" align="center" prop="cDeptId" /> -->
      <el-table-column label="最少天数" align="center" prop="cMinDays" />
      <el-table-column label="最多天数" align="center" prop="cMaxDays" />
      <el-table-column label="延期天数" align="center" prop="cYxYqdays" />
      <el-table-column label="最高费用" align="center" prop="cMaxCost"  />
      <el-table-column label="最低费用" align="center" prop="cMinCost"  />
      <el-table-column label="是否单病种" align="center" prop="cDbzFlag" >
        <template slot-scope="scope">
          <input type="checkbox" :checked="scope.row.cDbzFlag==1" @input="setDbz(scope.row)">
        </template>
      </el-table-column>
      <el-table-column label="单病种名称" align="center" prop="cDbzName"/>
      <el-table-column label="单病种限价" align="center" prop="cDbzCost"/>
      <el-table-column label="专科病例" align="center" prop="cZkblId"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :visible.sync="open" width="1500px" append-to-body @close="handleCancle">
      <div style="display: flex">
        <div>
          <div style="display: flex">
            <el-button style="width: 150px;" @click="getDate" type="primary">日期设置</el-button>
            <el-button style="width: 150px;" @click="insertData" type="primary">添加</el-button>
          </div>
          <el-menu
            default-active="2"
            class="el-menu-vertical-demo"
            @open="handleOpen"
            @close="handleClose">

            <el-submenu  style="width: 300px !important;" v-for="(item,index) in dateStrList" :index="'' + index">
              <template slot="title" >
                <span>{{ (index + 1) + '. ' + item.date }}</span>
              </template>
              <el-menu-item-group v-for="(item1,index1) in item.lcjlItem" :key="index + '-' + index1">
                <el-menu-item style="font-size:12px;white-space: normal !important;word-break: break-all !important;line-height: normal !important;"
                              @click="handleOpen(index + '-' + index1)" :index="index + '-' + index1">{{ (index1 + 1) + '. ' + item1.cName }}</el-menu-item>
              </el-menu-item-group>
            </el-submenu>
          </el-menu>

        </div>

        <div style="margin-left: 40px;width: 1000px">
          <el-table v-if="dateopen"  :data="dateList" @selection-change="handleSelectionChange"  style="width: 1400px">
            <el-table-column label="时间类型" align="center" prop="cDayType" >
              <template v-if="dateopen" slot-scope="scope">
                <el-select v-model="scope.row.cDayType">
                  <el-option v-for="item in ljdateType" :value="item" @click.native="setcDate(scope.row)"> </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="开始时间" align="center" prop="cDateFrom" >
              <template v-if="dateopen" slot-scope="scope">
                <el-input type="number" v-model="scope.row.cDateFrom" @change="setcDate(scope.row)" style="width: 100px;"/>
              </template>
            </el-table-column>
            <el-table-column label="结束时间" align="center" prop="cDateTo" >
              <template v-if="dateopen" slot-scope="scope">
                <el-input type="number" v-model="scope.row.cDateTo" @change="setcDate(scope.row)" style="width: 100px;"/>
              </template>
            </el-table-column>
            <el-table-column label="时间单位" align="center" prop="cSjUnit" >
              <template v-if="dateopen" slot-scope="scope">
                <el-select v-model="scope.row.cSjUnit">
                  <el-option v-for="item in cSjUnit" :value="item" @click.native="setcDate(scope.row)"> </el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>

          <el-table v-if="mopen"  :data="lcljItemMList" style="width: 1400px">
            <el-table-column label="序号" align="center" prop="cSno"/>
            <el-table-column label="项目名称" align="center" prop="cName" width="350px">
              <template v-if="mopen" slot-scope="scope">
                <el-input v-model="scope.row.cName" @change="setTsDate(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="婴儿" align="center" prop="cIschild">
              <template v-if="mopen" slot-scope="scope">
                <input type="checkbox" :checked="scope.row.cIschild==1" @input="setYr(scope.row)">
              </template>
            </el-table-column>
            <el-table-column label="特殊类型" align="center" prop="cTsType" >
              <template v-if="mopen" slot-scope="scope">
                <el-select v-model="scope.row.cTsType">
                  <el-option v-for="item in cTsType" :value="item" @click.native="setTsDate(scope.row)"> </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作员" align="center" prop="cOpId" />
            <el-table-column label="操作时间" align="center" prop="cOpDate" width="180">
              <template v-if="mopen" slot-scope="scope">
                <span>{{ parseTime(scope.row.cOpDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
          </el-table>

          <el-table v-if="dopen" :data="hlcljItemDList" style="width: 1600px">
            <el-table-column label="类型" align="center" prop="cType" width="100px" >
              <template v-if="dopen" slot-scope="scope">
                <el-select v-model="scope.row.cType">
                  <el-option v-for="item in cType" :value="item" @click.native="setD(scope.row)"> </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="医嘱类型" align="center" prop="cItemtype" width="100px" >
              <template v-if="dopen" slot-scope="scope">
                <el-select v-model="scope.row.cItemtype">
                  <el-option v-for="item in cItemtype" :value="item" @click.native="setD(scope.row)"> </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="药品类型" align="center" prop="cYpType" />
            <el-table-column label="项目名称" align="center" prop="cName" width="300px">
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cName" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="医嘱组号" align="center" prop="cItemgroupno" >
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cItemgroupno" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="每次计量" align="center" prop="cDose" >
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cDose" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="计量单位" align="center" prop="cDoseUnit" >
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cDoseUnit" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="总数量" align="center" prop="cAmount">
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cAmount" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="用药途径" align="center" prop="cUsestyle">
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cUsestyle" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="用药频次" align="center" prop="cPinci">
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cPinci" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="副数" align="center" prop="cFs">
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cFs" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
            <el-table-column label="主要费用名称" align="center" prop="cMainFyname" width="120px">
              <template v-if="dopen" slot-scope="scope">
                <el-input v-model="scope.row.cMainFyname" @change="setD(scope.row)"/>
              </template>
            </el-table-column>
          </el-table>

          <el-dialog  :visible.sync="insertdateopen" width="900px" append-to-body @close="cancel">
            <el-form ref="form" :model="dateForm" :rules="rules" label-width="80px">
              <el-form-item label="时间类型" prop="cDayType">
                <el-select v-model="dateForm.cDayType" placeholder="请选择时间类型">
                  <el-option v-for="item in ljdateType" :value="item"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="开始时间" prop="cDateFrom">
                <el-input type="number" v-model="dateForm.cDateFrom"  placeholder="请输入开始时间"/>
              </el-form-item>
              <el-form-item label="结束时间" prop="cDateTo">
                <el-input type="number" v-model="dateForm.cDateTo" placeholder="请输入结束时间"/>
              </el-form-item>
              <el-form-item label="时间单位" prop="cSjUnit">
                <el-select v-model="dateForm.cSjUnit" placeholder="请输入时间单位" >
                  <el-option v-for="item in cSjUnit" :value="item"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="最大花费" prop="cMaxCost">
                <el-input v-model="dateForm.cMaxCost" placeholder="请输入最大花费" />
              </el-form-item>
              <el-form-item label="最低花费" prop="cMinCost">
                <el-input v-model="dateForm.cMinCost" placeholder="请输入最低花费" />
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </el-dialog>


          <el-dialog  :visible.sync="insertmopen" width="900px" append-to-body  @close="cancelM">
            <el-form ref="form" :model="mForm"  label-width="80px">
              <el-form-item label="项目名称" prop="cName">
                <el-input v-model="mForm.cName" placeholder="请输入项目名称" />
              </el-form-item>
              <el-form-item label="特殊类型" prop="cTsType">
                <el-select v-model="mForm.cTsType"  placeholder="请选择特殊类型" >
                  <el-option v-for="item in cTsType" :value="item"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="婴儿" prop="cIschild">
                <el-select v-model="mForm.cIschild"  placeholder="请选择是否适用于婴儿" >
                  <el-option value="是">是</el-option>
                  <el-option value="否">否</el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitFormM">确 定</el-button>
              <el-button @click="cancelM">取 消</el-button>
            </div>
          </el-dialog>

          <el-dialog :visible.sync="insertdopen" width="900px" append-to-body>
            <el-form ref="form" :model="dForm"  label-width="80px">
              <el-form-item label="项目名称" prop="cName">
                <el-input v-model="dForm.cName" placeholder="请输入项目名称" />
              </el-form-item>
              <el-form-item label="类型" prop="cType">
                <el-select v-model="dForm.cType">
                  <el-option v-for="item in cType" :value="item"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="医嘱类型" prop="cItemtype">
                <el-select v-model="dForm.cItemtype">
                  <el-option v-for="item in cItemtype" :value="item" > </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="用药途径" prop="cUsestyle">
                <el-input v-model="dForm.cUsestyle" placeholder="请输入用药途径" />
              </el-form-item>
              <el-form-item label="用药频次" prop="cPinci">
                <el-input v-model="dForm.cPinci" placeholder="请输入用药频次" />
              </el-form-item>
              <el-form-item label="医嘱" prop="cDoctornote">
                <el-input v-model="dForm.cDoctornote" placeholder="请输入医嘱" />
              </el-form-item>
              <el-form-item label="组号" prop="cItemgroupno">
                <el-input v-model="dForm.cItemgroupno" placeholder="请输入组号" />
              </el-form-item>
              <el-form-item label="每次计量" prop="cDose">
                <el-input v-model="dForm.cDose" placeholder="请输入每次计量" />
              </el-form-item>
              <el-form-item label="计量单位" prop="cDoseUnit">
                <el-input v-model="dForm.cDoseUnit" placeholder="请输入计量单位" />
              </el-form-item>
              <el-form-item label="总数量" prop="cAmount">
                <el-input v-model="dForm.cAmount" placeholder="请输入总数量" />
              </el-form-item>
              <el-form-item label="副数" prop="cFs">
                <el-input v-model="dForm.cFs" placeholder="请输入副数" />
              </el-form-item>
              <el-form-item label="主要费用名称" prop="cMainFyname">
                <el-input v-model="dForm.cMainFyname" placeholder="请输入主要费用名称" />
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitFormD">确 定</el-button>
              <el-button @click="cancelD">取 消</el-button>
            </div>
          </el-dialog>


        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listType, getType, delType, addType, updateType } from "@/api/lclj/type";
import {addDate, listDate, updateDate} from "@/api/lclj/date";
import {listLcljItemM, updateLcljItemM,addLcljItemM } from "@/api/lclj/lcljItemM";
import {addHlcljItemD, listHlcljItemD, updateHlcljItemD} from "@/api/lclj/lcljItemD";
import {getUserProfile} from "@/api/system/user";

export default {
  name: "Type",
  data() {
    return {
      insertmopen:false,
      insertdopen:false,
      insertdateopen:false,
      insertFlag:null,
      dateClass:'click',
      dopen:false,
      mopen:false,
      dateopen:true,
      //时间类型
      cTsType:['常规','特殊'],
      ljdateType: ['入院','术前','术后','出院前','出院'],
      cSjUnit:['日','小时','分钟'],
      cType:['长期','临时'],
      cItemtype:['检查','治疗','药品'],
      currentRow:null,
      currentSecondRowID:null,
      currentThirdRowID:null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 临床路径管理表格数据
      typeList: [],
      dateList: [],
      dateStrList:[],
      lcljItemMList:[],
      hlcljItemDList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        cCode: null,
        cNccd: null,
        cWbcode: null,
        cName: null,
        cIcdCode: null,
        cDeptId: null,
        cMinDays: null,
        cMaxDays: null,
        cStatus: null,
        cMaxCost: null,
        cMinCost: null,
        cZkblId: null,
        cYxYqdays: null,
        cDbzName: null,
        cDbzCost: null,
        cIcdCodeZy: null,
        cDbzFlag: null,
      },
      datequeryParams: {
        pageNum: 1,
        pageSize: 1000,
        cLcljId: null,
        cDayType: null,
        cSjUnit: null,
        cDateFrom: null,
        cDateTo: null,
        cMaxCost: null,
        cMinCost: null
      },
      mqueryParams: {
        pageNum: 1,
        pageSize: 100000000,
        cSno:null,
        cSjId: null,
        cName: null,
        cType: null,
        cKzType: null,
        cPdType: null,
        cOpId: null,
        cOpDate: null,
        cIschild: null,
        cTsType: null
      },
      dqueryParams: {
        pageNum: 1,
        pageSize: 100000000,
        cBillId: null,
        cDsno: null,
        cName: null,
        cSno: null,
        cKzType: null,
        cPdType: null,
        cPdItem: null,
        cItemtype: null,
        cYpType: null,
        cItemid: null,
        cItemgroupno: null,
        cUsestyle: null,
        cPinci: null,
        cDoctornote: null,
        cDose: null,
        cDoseUnit: null,
        cAmount: null,
        cFs: null,
        cType: null,
        cStoreId: null,
        cName2017: null,
        cItemid2017: null,
        cMainFyname: null
      },
      // 表单参数
      form: {},
      dateForm: {},
      mForm:{},
      dForm:{},
      key1:null,
      key2:null,

      // 表单校验
      rules: {
        cCode: [
          { required: true, message: "编码不能为空", trigger: "blur" }
        ],
        cNccd: [
          { required: true, message: "拼音码不能为空", trigger: "blur" }
        ],
        cWbcode: [
          { required: true, message: "五笔码不能为空", trigger: "blur" }
        ],
        cName: [
          { required: true, message: "临床路径名称不能为空", trigger: "blur" }
        ],
        cMinDays: [
          { required: true, message: "最少治疗天数不能为空", trigger: "blur" }
        ],
        cMaxDays: [
          { required: true, message: "最多治疗天数不能为空", trigger: "blur" }
        ],
        cStatus: [
          { required: true, message: "有效不能为空", trigger: "change" }
        ],
        cOpId: [
          { required: true, message: "操作员不能为空", trigger: "blur" }
        ],
        cOpDate: [
          { required: true, message: "操作日期不能为空", trigger: "blur" }
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleOpen(key) {
      if (key.indexOf('-') == -1) {
        this.key1 = key
        this.getContent(this.dateStrList[this.key1])
      } else {
        this.key1 = Number(key.split("-")[0])
        this.key2 = Number(key.split("-")[1])
        this.getlcljItemD(this.dateStrList[this.key1].lcjlItem[this.key2])
      }
    },
    handleClose(key) {
      if (key.indexOf('-') == -1) {
        this.key1 = key
        this.getContent(this.dateStrList[this.key1])
      } else {
        this.key1 = Number(key.split("-")[0])
        this.key2 = Number(key.split("-")[1])
        this.getlcljItemD(this.dateStrList[this.key1].lcjlItem[this.key2])
      }
    },
    insertData() {
      if (this.insertFlag == 1) {
        this.insertdateopen = true
      } else if (this.insertFlag == 2) {
        this.insertmopen = true
      } else if (this.insertFlag == 3) {
        this.insertdopen = true
      }
    },
    setD(row) {
      if (row.cType == '临时') {
        row.cType = 1
      }
      else if (row.cType == '长期') {
        row.cType = 2
      }
      updateHlcljItemD(row).then(response => {
      });
      if (row.cType == 1) {
        row.cType = '临时'
      }
      else if (row.cType == 2) {
        row.cType = '长期'
      }
    },
    getlcljItemD(item){
      this.currentThirdRowID = item.cId
      this.insertFlag = 3
      this.dqueryParams.cBillId = item.cId
      listHlcljItemD(this.dqueryParams).then(response => {
        this.hlcljItemDList = response.rows;
        for (let i = 0; i < this.hlcljItemDList.length; i++) {
          if (this.hlcljItemDList[i].cType == 1) {
            this.hlcljItemDList[i].cType = '临时'
            continue
          }
          if (this.hlcljItemDList[i].cType == 2) {
            this.hlcljItemDList[i].cType = '长期'
          }
        }
      })
      this.dateopen = false
      this.mopen = false
      this.dopen = true
    },
    getDate() {
      this.insertFlag = 1
      this.dateopen = true
      this.mopen = false
      this.dopen = false
    },
    setTsDate(row) {
      let date = row
      if (date.cTsType == '常规')
        date.cTsType = 0
      else date.cTsType = 1
      updateLcljItemM(date).then(response => {

      });
      if (date.cTsType == 0)
        date.cTsType = '常规'
      else date.cTsType = '特殊'
    },
    setYr(row) {
      if (row.cIschild == 1)
        row.cIschild = 0
      else if (row.cIschild == 0)
        row.cIschild = 1

      if (row.cTsType == '常规')
        row.cTsType = 0
      else row.cTsType = 1
      updateLcljItemM(row).then(response => {
      });
      if (row.cTsType == 0)
        row.cTsType = '常规'
      else row.cTsType = '特殊'
    },
    getContent(item){
      this.currentSecondRowID = item.id
      this.insertFlag = 2
      this.mopen = true
      this.dateopen = false
      this.dopen = false
      this.lcljItemMList = item.lcjlItem
    },
    showlcljData() {
      if (this.currentRow == null) {
        this.$modal.msgWarning("请选择临场路径");
        return
      }
      this.insertFlag = 1
      this.open = true
      this.dateStrList = []
      for (let i = 0; i < this.dateList.length; i++) {
        let str = this.dateList[i].cDayType + '第'
        if (this.dateList[i].cDateFrom == this.dateList[i].cDateTo)
          str = str + this.dateList[i].cDateFrom + this.dateList[i].cSjUnit
        else str = str + this.dateList[i].cDateFrom  + '-' + this.dateList[i].cDateTo + this.dateList[i].cSjUnit

        this.mqueryParams.cSjId = this.dateList[i].cId
        listLcljItemM(this.mqueryParams).then(response => {
          var arr = []
          for (let i = 0; i < response.rows.length; i++) {
            if (response.rows[i].cTsType == 0)
              response.rows[i].cTsType = '常规'
            else response.rows[i].cTsType = '特殊'
            arr.push(response.rows[i])
          }
          this.dateStrList.push({
            date:str,
            id:this.dateList[i].cId,
            class:'normal',
            lcjlItem:arr
          })
        });
      }
    },
    setcDate(row) {
      if (row.insertFlag) {
        return
      }
      let data = row
      if (data.cDayType == '入院')
        data.cDayType = '1'
      else if (data.cDayType == '术前')
        data.cDayType = '2'
      else if (data.cDayType == '术后')
        data.cDayType = '3'
      else if (data.cDayType == '出院前')
        data.cDayType = '4'
      else if (data.cDayType == '出院')
        data.cDayType = '5'

      if (data.cSjUnit == '日')
        data.cSjUnit = '1'
      else if (data.cSjUnit == '小时')
        data.cSjUnit = '2'
      else if (data.cSjUnit == '分钟')
        data.cSjUnit = '3'
      updateDate(data).then(response => {
        this.formatData()
      });
      this.formatData()
    },
    setlcljDate(row) {
      this.insertFlag = 1
      this.currentRow = row
      this.datequeryParams.cLcljId = row.cId
      listDate(this.datequeryParams).then(response => {
        this.dateList = response.rows;
        this.formatData()
      });
    },
    formatData(){
      for (let i = 0; i < this.dateList.length; i++) {
        if (this.dateList[i].cDayType == '1')
          this.dateList[i].cDayType = '入院'
        else if (this.dateList[i].cDayType == '2')
          this.dateList[i].cDayType = '术前'
        else if (this.dateList[i].cDayType == '3')
          this.dateList[i].cDayType = '术后'
        else if (this.dateList[i].cDayType == '4')
          this.dateList[i].cDayType = '出院前'
        else if (this.dateList[i].cDayType == '5')
          this.dateList[i].cDayType = '出院'


        if (this.dateList[i].cSjUnit == '1')
          this.dateList[i].cSjUnit = '日'
        else if (this.dateList[i].cSjUnit == '2')
          this.dateList[i].cSjUnit = '小时'
        else if (this.dateList[i].cSjUnit == '3')
          this.dateList[i].cSjUnit = '分钟'
      }
    },
    setStatus(row) {
      if (row.cStatus == 1)
        row.cStatus = 0
      else if (row.cStatus == 0)
        row.cStatus = 1
      updateType(row).then(response => {
        this.getList()
      });
    },
    setDbz(row) {
      if (row.cDbzFlag == 1)
        row.cDbzFlag = 0
      else if (row.cDbzFlag == 0)
        row.cDbzFlag = 1
      updateType(row).then(response => {
        this.getList()
      });
    },
    /** 查询临床路径管理列表 */
    getList() {
      this.loading = true;
      listType(this.queryParams).then(response => {
        this.typeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.insertdateopen = false;
      this.dateForm = {}
    },
    cancelM() {
      this.insertmopen = false;
      this.mForm = {}
    },
    cancelD() {
      this.insertdopen = false;
      this.dForm = {}
    },
    // 表单重置
    reset() {
      this.form = {
        cId: null,
        cCode: null,
        cNccd: null,
        cWbcode: null,
        cName: null,
        cIcdCode: null,
        cDeptId: null,
        cMinDays: null,
        cMaxDays: null,
        cNote: null,
        cStatus: null,
        cOpId: null,
        cOpDate: null,
        cMaxCost: null,
        cMinCost: null,
        cZkblId: null,
        cExpression: null,
        cYxYqdays: null,
        cDbzName: null,
        cDbzCost: null,
        cIcdCodeZy: null,
        cDbzFlag: null,
        cBfzFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加临床路径管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const cId = row.cId || this.ids
      getType(cId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改临床路径管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (this.dateForm.cDayType == null || this.dateForm.cDayType == '') {
        this.$modal.msgWarning("请选择时间类型");
        return
      }
      if (this.dateForm.cDateFrom == null || this.dateForm.cDateFrom == '') {
        this.$modal.msgWarning("请输入开始时间");
        return
      }
      if (this.dateForm.cDateTo == null || this.dateForm.cDateTo == '') {
        this.$modal.msgWarning("请输入结束时间");
        return
      }
      if (this.dateForm.cSjUnit == null || this.dateForm.cSjUnit == '') {
        this.$modal.msgWarning("请选择时间单位");
        return
      }
      listDate({
        pageNum: 1,
        pageSize: 10000,
      }).then(response => {
        var dateList = response.rows
        console.log(dateList)
        var maxCid = parseInt(dateList[0].cId)
        for (let i = 1; i < dateList.length; i++) {
          if (parseInt(dateList[i].cId) > maxCid)
            maxCid = parseInt(dateList[i].cId)
        }
        this.dateForm['cId'] = maxCid + 1
        this.dateForm['cLcljId'] = this.currentRow.cId
        if (this.dateForm.cDayType == '入院')
          this.dateForm.cDayType = '1'
        else if (this.dateForm.cDayType == '术前')
          this.dateForm.cDayType = '2'
        else if (this.dateForm.cDayType == '术后')
          this.dateForm.cDayType = '3'
        else if (this.dateForm.cDayType == '出院前')
          this.dateForm.cDayType = '4'
        else if (this.dateForm.cDayType == '出院')
          this.dateForm.cDayType = '5'

        if (this.dateForm.cSjUnit == '日')
          this.dateForm.cSjUnit = '1'
        else if (this.dateForm.cSjUnit == '小时')
          this.dateForm.cSjUnit = '2'
        else if (this.dateForm.cSjUnit == '分钟')
          this.dateForm.cSjUnit = '3'
        addDate(this.dateForm).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.insertdateopen = false;
          listDate({
            pageNum: 1,
            pageSize: 1000,
            cLcljId: this.currentRow.cId,
          }).then(response => {
            this.dateList = response.rows;
            this.formatData()
            this.dateStrList = []
            for (let i = 0; i < this.dateList.length; i++) {
              let str = this.dateList[i].cDayType + '第'
              if (this.dateList[i].cDateFrom == this.dateList[i].cDateTo)
                str = str + this.dateList[i].cDateFrom + this.dateList[i].cSjUnit
              else str = str + this.dateList[i].cDateFrom  + '-' + this.dateList[i].cDateTo + this.dateList[i].cSjUnit
              this.mqueryParams.cSjId = this.dateList[i].cId
              listLcljItemM(this.mqueryParams).then(response => {
                var arr = []
                for (let i = 0; i < response.rows.length; i++) {
                  if (response.rows[i].cTsType == 0)
                    response.rows[i].cTsType = '常规'
                  else response.rows[i].cTsType = '特殊'
                  arr.push(response.rows[i])
                }

                this.dateStrList.push({
                  date:str,
                  id:this.dateList[i].cId,
                  class:'normal',
                  lcjlItem:arr
                })
                console.log(this.dateStrList);
              });
            }
          });
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const cIds = row.cId || this.ids;
      this.$modal.confirm('是否确认删除临床路径管理编号为"' + cIds + '"的数据项？').then(function() {
        return delType(cIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('lclj/type/export', {
        ...this.queryParams
      }, `type_${new Date().getTime()}.xlsx`)
    },
    handleCancle(){
      this.dateStrList = []
      this.lcljItemMList = []
      this.hlcljItemDList = []
      this.mopen = false
      this.dopen = false
      this.dateopen = true
    },
    submitFormD() {
      if (this.dForm.cName == null) {
        this.$modal.msgWarning("内容为空");
        return
      }
      if (this.dForm.cType == '临时') {
        this.dForm.cType = 1
      }
      else if (this.dForm.cType == '长期') {
        this.dForm.cType = 2
      }
      this.dForm['cBillId'] = this.currentThirdRowID
      this.dForm['cDsno'] = this.hlcljItemDList.length + 1
      this.dForm['cSno'] = this.hlcljItemDList.length + 1
      console.log(this.dForm)
      addHlcljItemD(this.dForm).then(response => {
        this.$modal.msgSuccess("新增成功");
        this.dForm = {}
        this.insertdopen = false
        this.dqueryParams.cBillId = this.currentThirdRowID
        listHlcljItemD(this.dqueryParams).then(response => {
          this.hlcljItemDList = response.rows;
          for (let i = 0; i < this.hlcljItemDList.length; i++) {
            if (this.hlcljItemDList[i].cType == 1) {
              this.hlcljItemDList[i].cType = '临时'
              continue
            }
            if (this.hlcljItemDList[i].cType == 2) {
              this.hlcljItemDList[i].cType = '长期'
            }
          }
        })
      })
    },
    submitFormM() {
      if (this.mForm.cName == null) {
        this.$modal.msgWarning("内容为空");
        return
      }
      if (this.mForm.cTsType == '常规')
        this.mForm.cTsType = 0
      else if (this.mForm.cTsType == '特殊') this.mForm.cTsType = 1
      if (this.mForm.cIschild == '是')
        this.mForm.cIschild = 1
      else if (this.mForm.cIschild == '否') this.mForm.cIschild = 0
      var date = new Date()
      date = this.formatDate(date)
      this.mForm['cOpDate'] = date
      this.mForm['cSjId'] = this.currentSecondRowID
      getUserProfile().then(response => {
        this.mForm['cOpId'] = response.data.userName
      })
      listLcljItemM({}).then(response=> {
        var maxCid = parseInt(response.rows[0].cId)
        for (let i = 1; i < response.rows.length; i++) {
          if (maxCid < parseInt(response.rows[i].cId))
            maxCid = parseInt(response.rows[i].cId)
        }
        this.mForm['cId'] = maxCid + 1
        this.mForm['cSno'] = this.dateStrList[this.key1].lcjlItem.length + 1
        addLcljItemM(this.mForm).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.mForm = {}
          this.insertmopen = false
          this.mqueryParams.cSjId = this.currentSecondRowID
          listLcljItemM(this.mqueryParams).then(response => {
            this.lcljItemStrMList = []
            for (let i = 0; i < response.rows.length; i++) {
              if (response.rows[i].cTsType == 0)
                response.rows[i].cTsType = '常规'
              else response.rows[i].cTsType = '特殊'
            }
            this.lcljItemMList = response.rows;
            this.dateStrList[this.key1].lcjlItem = this.lcljItemMList
          });
        });
      })
    },
    formatDate(date){
      var str = ''
      var year = date.getFullYear()
      var month = date.getMonth()+1
      var day = date.getDate()
      var hour = date.getHours()
      var min = date.getMinutes()
      var sec = date.getSeconds()
      str += year + '-' + this.twoDigits(month) + '-' + this.twoDigits(day)
        + ' ' + this.twoDigits(hour) + ':' + this.twoDigits(min) + ':' + this.twoDigits(sec)
      return str
    },
    twoDigits(value) {
      return value>=10?value:'0'+value
    },
  }
};
</script>
<style>
ul,li {
  list-style: none;
}

</style>

