<template>
  <div class="login">
    <div  v-if="isUseMultiTenant">
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="title">{{ title }}</h3>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="普通登录" name="commonLogin">
            <el-form-item prop="tenantId">
              <el-select v-model="loginForm.tenantId" filterable placeholder="请选择机构" style="width: 100%">
                <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName"
                           :value="item.tenantId"></el-option>
                <svg-icon slot="prefix" icon-class="education" class="el-input__icon input-icon"/>
              </el-select>
            </el-form-item>
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                type="text"
                auto-complete="off"
                placeholder="账号"
              >
                <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                auto-complete="off"
                placeholder="密码"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
              </el-input>
            </el-form-item>
            <el-form-item prop="code" v-if="captchaEnabled">
              <el-input
                v-model="loginForm.code"
                auto-complete="off"
                placeholder="验证码"
                style="width: 63%"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>
              </el-input>
              <div class="login-code">
                <img :src="codeUrl" @click="getCode" class="login-code-img"/>
              </div>
            </el-form-item>
            <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
            <el-form-item style="width:100%;">
              <el-button
                :loading="loading"
                size="medium"
                type="primary"
                style="width:100%;"
                @click.native.prevent="handleLogin"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
              <div style="float: right;" v-if="register">
                <router-link class="link-type" :to="'/register'">立即注册</router-link>
              </div>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="管理员登录" name="adminLogin">
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                type="text"
                auto-complete="off"
                placeholder="账号"
              >
                <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                auto-complete="off"
                placeholder="密码"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
              </el-input>
            </el-form-item>
            <el-form-item prop="code" v-if="captchaEnabled">
              <el-input
                v-model="loginForm.code"
                auto-complete="off"
                placeholder="验证码"
                style="width: 63%"
                @keyup.enter.native="handleLogin"
              >
                <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>
              </el-input>
              <div class="login-code">
                <img :src="codeUrl" @click="getCode" class="login-code-img"/>
              </div>
            </el-form-item>
            <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
            <el-form-item style="width:100%;">
              <el-button
                :loading="loading"
                size="medium"
                type="primary"
                style="width:100%;"
                @click.native.prevent="handleLogin"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
              <div style="float: right;" v-if="register">
                <router-link class="link-type" :to="'/register'">立即注册</router-link>
              </div>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div v-if="showSysLoginTip" class="tip">
        <span>禁止使用本系统处理处理涉密信息</span>
      </div>
    </div>

    <div  v-else>
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="title">{{ title }}</h3>
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            type="text"
            auto-complete="off"
            placeholder="账号"
          >
            <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            auto-complete="off"
            placeholder="密码"
            @keyup.enter.native="handleLogin"
          >
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
          </el-input>
        </el-form-item>
        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input
            v-model="loginForm.code"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter.native="handleLogin"
          >
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon"/>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img"/>
          </div>
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
        <el-form-item style="width:100%;">
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            style="width:100%;"
            @click.native.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div style="float: right;" v-if="register">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
      <div v-if="showSysLoginTip" class="tip">
        <span>禁止使用本系统处理处理涉密信息</span>
      </div>
    </div>

  </div>
</template>

<script>
import {getCodeImg} from "@/api/login";
import Cookies from "js-cookie";
import {decrypt, encrypt} from '@/utils/jsencrypt'
import {getOption} from '@/api/system/option'
import {listTenant} from '@/api/system/tenant'
import {removeToken} from '@/utils/auth'

export default {
  name: "Login",
  data() {
    return {
      activeName: "commonLogin",
      title: ' ',
      isUseMultiTenant: false,
      showSysLoginTip: false,
      tenantList: [],
      jgid: null,
      codeUrl: "",
      loginForm: {
        username: "admin",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
        tenantId: ""
      },
      loginRules: {
        username: [
          {required: true, trigger: "blur", message: "请输入您的账号"}
        ],
        password: [
          {required: true, trigger: "blur", message: "请输入您的密码"}
        ],
        code: [{required: true, trigger: "change", message: "请输入验证码"}]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getUseMultiTenantFlag()
    this.getCode();
    this.getCookie();
    this.getOption();
    // this.getSysTitle();
  },
  mounted() {
    this.getSysTitle();
  },
  methods: {
    async getOption() {
      const res = await getOption("show_sys_login_tip")
      if (res && res.data && res.data.cValue) {
        this.showSysLoginTip = res.data.cValue == '1' ? true : false
      }
    },
    handleClick() {
    },
    getUseMultiTenantFlag() {
      getOption("use_multi_tenant").then(res => {
        if(res.code !== 200) return;
        if (!res.data) {
          this.isUseMultiTenant = false;
        } else {
          this.isUseMultiTenant = res.data.cValue == 1 ? true : false
        }
        console.log("是否开启多租户模式：" + this.isUseMultiTenant)
        if (this.isUseMultiTenant) {
          this.jgid = this.$route.query.jgid ? this.$route.query.jgid : null
          listTenant({}).then(res => {
            if (this.jgid) {
              this.tenantList = res.rows.filter(item => item.tenantId == this.jgid)
              if (this.tenantList.length == 0) {
                this.tenantList = res.rows
              }
              this.loginForm.tenantId = this.tenantList[0].tenantName
            } else {
              this.tenantList = res.rows
            }
          })
        }
      })
    },
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      if (this.activeName == "adminLogin") {
        this.loginForm.tenantId = "admin"
      }

      if (this.isUseMultiTenant && (this.loginForm.tenantId == null || this.loginForm.tenantId == "") && this.activeName != "adminLogin") {
        this.$modal.msgWarning("请选择机构！")
        return
      }

      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            removeToken();
            Cookies.remove('tenantId');
            Cookies.set("username", this.loginForm.username, {expires: 30});
            Cookies.set("password", encrypt(this.loginForm.password), {expires: 30});
            Cookies.set('rememberMe', this.loginForm.rememberMe, {expires: 30});
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          if (this.isUseMultiTenant) {
            Cookies.remove('tenantId');
            Cookies.set('tenantId', this.loginForm.tenantId, {expires: 30});
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.setSystemTitle()
            this.$router.push({path: this.redirect || "/"}).catch(() => {
            });
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    },
    setSystemTitle() {
      getOption("system_main_title").then(res => {
        if (res.code !== 200) return;
        this.$store.dispatch("settings/setTitle", res.data.cValue)
        this.$store.dispatch("tagsView/updateVisitedView", res.data.cValue)
        document.title = res.data.cValue
      })
    },
    getSysTitle() {
      getOption("system_main_title").then(res => {
        if(res.data && res.data.cValue) {
          this.title = res.data.cValue
        } else {
          this.title = process.env.VUE_APP_TITLE
        }
      }).catch(err => {
        this.title = process.env.VUE_APP_TITLE
      })
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: url("../assets/images/bj.jpg");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}

.tip {
  height: 40px;
  font-weight: bold;
  line-height: 40px;
  width: 100%;
  text-align: center;
  color: #fff;
  font-size: 15px;
}
</style>
