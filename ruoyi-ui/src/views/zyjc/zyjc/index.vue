<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="住院号" prop="zyh">
        <el-input
          v-model="queryParams.zyh"
          placeholder="请输入住院号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="科室" prop="cykb">
        <el-select v-model="queryParams.cykb"
                   @change="changeDept"
                   placeholder="科室" clearable>
          <el-option v-for="item in deptList" :value="item.cykb" :key="item.cykb">{{ item.cykb }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="医生" prop="zyys">
        <el-select v-model="queryParams.zyys"
                   placeholder="医生" clearable>
          <el-option v-for="item in doctorList" :value="item.zyys">{{ item.zyys }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出
        </el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="zyjcList" @sort-change="handleSortChange">
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100px">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="yfz(scope.row)"
          >预分组
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="住院号" align="center" prop="zyh" show-overflow-tooltip/>
      <el-table-column label="姓名" align="center" prop="xm" show-overflow-tooltip/>
      <el-table-column label="性别" align="center" prop="xb" show-overflow-tooltip/>
      <!--  <el-table-column label="年龄" align="center" width="60px" prop="nl" show-overflow-tooltip/> -->
      <el-table-column label="科室" align="center" prop="cykb" show-overflow-tooltip/>
      <el-table-column label="医生" align="center" prop="zyys" show-overflow-tooltip/>
      <!--     <el-table-column label="DRG名称" align="center" prop="drgmc" width="200px" show-overflow-tooltip/> -->
      <el-table-column label="DRG编号" align="center" prop="drgbh" show-overflow-tooltip/>

      <el-table-column label="总费用" align="center" prop="zfy" show-overflow-tooltip sortable="custom" width="120px">
        <template slot-scope="scope">
          <span
            :class="scope.row.zfy>scope.row.zfbz?'redFont':scope.row.zfy>(scope.row.zfbz*0.8)?'yellowFont':''">{{
              scope.row.zfy
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标杆费用" align="center" prop="zfbz" show-overflow-tooltip sortable="custom" width="120px"/>

      <el-table-column label="费用进度" align="center" width="140px">
        <template slot-scope="scope">
          <el-progress v-if="scope.row.zfy!=null&&scope.row.zfbz!=null&&scope.row.zfy!=0&&scope.row.zfbz!=0"
                       :percentage="Math.round(scope.row.zfy / scope.row.zfbz * 100)>100?100:Math.round(scope.row.zfy / scope.row.zfbz * 100)"></el-progress>
        </template>
      </el-table-column>


      <el-table-column label="耗材费" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :class="scope.row.hcf>scope.row.bghcf?'redFont':scope.row.hcf>(scope.row.bghcf*0.8)?'yellowFont':''">{{
              scope.row.hcf
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标杆耗材" align="center" prop="bghcf" show-overflow-tooltip/>
      <el-table-column label="药品费" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :class="scope.row.ypf>scope.row.bgypf?'redFont':scope.row.ypf>(scope.row.bgypf*0.8)?'yellowFont':''">{{
              scope.row.ypf
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标杆药品费" align="center" prop="bgypf" width="120px" show-overflow-tooltip/>
      <el-table-column label="检查费" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :class="scope.row.jcf>scope.row.bgjcf?'redFont':scope.row.jcf>(scope.row.bgjcf*0.8)?'yellowFont':''">{{
              scope.row.jcf
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标杆检查费" align="center" prop="bgjcf" width="120px" show-overflow-tooltip/>
      <el-table-column label="检验费" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :class="scope.row.jyf>scope.row.bgjyf?'redFont':scope.row.jyf>(scope.row.bgjyf*0.8)?'yellowFont':''">{{
              scope.row.jyf
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标杆检验费" align="center" prop="bgjyf" width="120px" show-overflow-tooltip/>
      <el-table-column label="诊断名称" align="center" prop="zdmc" width="200px" show-overflow-tooltip/>
      <el-table-column label="手术名称" align="center" prop="ssmc" width="200px" show-overflow-tooltip/>
      <el-table-column label="距上次天数" align="center" prop="jlsccyts" width="120px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span :class="scope.row.jlsccyts<32?'blueFont':scope.row.jlsccyts">{{ scope.row.jlsccyts == null ? `/` : scope.row.jlsccyts }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="距上次天数" align="center" width="120px" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--&lt;!&ndash;          <span :class="scope.row.jlsccyts<32?'blueFont':scope.row.jlsccyts">{{ scope.row.jlsccyts }}</span>&ndash;&gt;-->
<!--          <span>{{scope.row.sccysj}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="getDetails(scope.row)"
          >查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="1500px" append-to-body>
      <el-tabs type="card" v-model="activeName">
        <!--        <el-tab-pane label="诊断记录" name="zdjl"  >-->
        <!--          <el-table :data="zdxxList" height="450">-->
        <!--            <el-table-column label="住院号" align="center" prop="zyh" />-->
        <!--            <el-table-column label="诊断名称" align="center" prop="zdname"/>-->
        <!--            <el-table-column label="诊断编号" align="center" prop="zdcode" />-->
        <!--          </el-table>-->
        <!--        </el-tab-pane>-->
        <el-tab-pane label="病历记录" name="bljl">
          <el-table :data="bljlList" height="450">
            <el-table-column label="住院号" align="center" prop="zyh"/>
            <el-table-column label="病历名称" align="left" prop="blname"/>
            <el-table-column label="内容信息" align="left" prop="blnr" show-overflow-tooltip width="950px"/>
            <el-table-column label="创建时间" align="center" prop="createdate"/>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="费用信息" name="fyxx">

          <el-form style="display: flex" :model="fyxx">
            <el-form-item label="费用汇总" prop="query" style="width: 350px;">
              <el-select v-model="fyxx.query" clearable>
                <el-option value="明细费用">明细费用</el-option>
                <el-option value="分项汇总">分项汇总</el-option>
              </el-select>
            </el-form-item>
            <el-form-item style="width: 250px">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="fyxxHz">搜索</el-button>
            </el-form-item>
          </el-form>


          <el-table :data="fyxxList">
            <el-table-column label="科目名称" align="center" prop="fykmname" width="150px" show-overflow-tooltip />
            <el-table-column label="医生名称" align="center" prop="ysname" show-overflow-tooltip />
            <el-table-column label="项目编号" align="center" prop="xmbm" show-overflow-tooltip width="200px"/>
            <el-table-column label="项目名称" align="left" prop="xmmc" show-overflow-tooltip width="450px"/>
            <el-table-column label="数量" align="center" prop="sl" show-overflow-tooltip />
            <el-table-column label="单价" align="center" prop="price" show-overflow-tooltip />
            <el-table-column label="金额" align="center" prop="je" show-overflow-tooltip />
            <el-table-column label="单位" align="center" prop="dw" show-overflow-tooltip />
            <el-table-column label="规格" align="center" prop="guige" show-overflow-tooltip />
            <el-table-column label="费用时间" align="center" prop="fydate" show-overflow-tooltip />
          </el-table>

          <pagination
            v-show="fyxxTotal>0"
            :total="fyxxTotal"
            :page.sync="fyxxParams.pageNum"
            :limit.sync="fyxxParams.pageSize"
            @pagination="fyxxHz"
          />


        </el-tab-pane>
        <!--        <el-tab-pane label="病人基本信息" name="brjbxx">-->
        <!--          <el-table :data="brxxList" height="450">-->
        <!--            <el-table-column label="住院号" align="center" prop="zyh"/>-->
        <!--            <el-table-column label="姓名" align="center" prop="name" />-->
        <!--            <el-table-column label="床号" align="center" prop="bed" />-->
        <!--            <el-table-column label="年龄" align="center" prop="age" />-->
        <!--            <el-table-column label="性别" align="center" prop="sex" />-->
        <!--            <el-table-column label="电话" align="center" prop="tel" />-->
        <!--            <el-table-column label="医保号" align="center" prop="ybh" />-->
        <!--            <el-table-column label="入院时间" align="center" prop="rydate" />-->
        <!--            <el-table-column label="医生名称" align="center" prop="doctorname" />-->
        <!--            <el-table-column label="科室名称" align="center" prop="deptname" />-->
        <!--          </el-table>-->
        <!--        </el-tab-pane>-->
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {listZyjc,getJlsccyts} from "@/api/zyjc/zyjc";
import {getbljl, getBrxx, getFyxx, getFyxxByKs, getZdjl} from "@/api/gksz/jklog";
import {getdk, getFzPageIp, getDeptList, getDoctorList} from "@/api/drg/syjl";
import {listUser} from "@/api/system/user";
import { getDeptListFromSy, getDoctorListByDept } from '@/api/tjfx/tjfx'
import { getCurDayStr } from '@/utils/dateUtils'

export default {
  name: "Zyjc",
  data() {
    return {
      fyxx: {
        query: null
      },
      currentRow: null,
      activeName: 'zdjl',
      //诊断信息
      zdxxList: [],
      //费用信息
      fyxxList: [],
      //病人信息
      brxxList: [],
      //病例信息
      bljlList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      fyxxTotal: 0,
      // Zyjc表格数据
      zyjcList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 排序字段
      sortColumn: '',
      // 排序方式
      sortOrder: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jzh: null,
        zyh: null,
        ybh: null,
        brid: null,
        zyid: null,
        xm: null,
        xb: null,
        nl: null,
        cykb: null,
        zyys: null,
        drgbh: null,
        zfy: null,
        hcf: null,
        ypf: null,
        jcf: null,
        jyf: null,
        zfbz: null,
        drgmc: null,
        zdmc: null,
        ssmc: null,
        jlsccyts: null,
        bghcf: null,
        bgypf: null,
        bgjcf: null,
        bgjyf: null,
        zyzt: '1',
      },
      fyxxParams: {
        pageNum: 1,
        pageSize: 10,
        jzh: null,
        xmmc:'',
      },
      // 表单参数
      form: {},
      doctorList: [],
      deptList: [],
      yhxm: null,
      yhkb: null,
      //当前用户是否是医生
      isDoctor: false,
      // 表单校验
      rules: {
        brid: [
          {required: true, message: "病人id不能为空", trigger: "blur"}
        ],
      }
    };
  },
  async created() {
    // this.verifyUserIdentity();
    await this.initSearchCondition()
    console.log(this.queryParams, '当前条件')
    this.getList()
  },
  methods: {
    //初始化查询条件
    async initSearchCondition() {
      // getDeptList().then(res => {
      //   this.deptList = res.rows;
      // })
      const res = await getDeptListFromSy()
      res.list.forEach(item => {
        this.deptList.push({
          cykb: item
        })
      })
      this.isDoctor = res.is_ys
      // this.queryParams.cykb = this.deptList[0].cykb
      // await this.getDoctorList()
      if(!this.isDoctor) {
        this.queryParams.cykb = ''
      } else {
        this.changeDept(this.deptList[0].cykb)
      }
      // await this.getDoctorList()
    },
    async getDoctorList(){
      this.doctorList = []
      const res = await getDoctorListByDept(this.queryParams.cykb)
      res.list.forEach(item => {
        this.doctorList.push({
          zyys: item
        })
      })
      // if(this.isDoctor === true) {
      //   this.queryParams.zyys = this.doctorList[0].zyys
      // }
    },
    changeDept(val){
      // console.log(val, '当前选择的科室')
      this.queryParams.cykb = val
      if(!val) {
        return
      }
      this.queryParams.zyys = null
      this.getDoctorList()
    },
    //验证用户身份
    verifyUserIdentity() {
      this.role = this.$store.state.user.roles[0];
      if (this.role == "ys") {
        listUser({userName: this.$store.state.user.name}).then(response => {
          console.log(response)
          this.yhxm = response.rows[0].nickName;
          if (response.rows[0].dept == null) {
            this.yhkb = null;
          } else {
            this.yhkb = response.rows[0].dept.deptName;
          }

          if ((this.yhxm == null || this.yhxm == "") && (this.yhkb == null || this.yhkb == "")) {
            this.$modal.msgWarning("医生信息为空，无法获取患者数据");
            return
          } else if (this.yhkb == null || this.yhkb == "") {
            this.$modal.msgWarning("医生科室信息为空，仅根据医生信息获取数据");
          }


          this.queryParams.zyys = this.yhxm
          this.queryParams.cykb = this.yhkb

          this.getList()

        }).catch(err => {
          this.$modal.msgError("医生信息获取失败，显示所有数据");
          this.getList()
        })

      } else {
        this.getList()
      }

    },
    /** 查询Zyjc列表 */
    getList() {
      console.log(this.queryParams)
      if(this.isDoctor && !this.queryParams.cykb) {
        // this.queryParams.cykb = this.deptList[0].cykb
        console.log("锁定医生用户的科室")
        this.changeDept(this.deptList[0].cykb)
      }
      this.loading = true;
      console.log(this.queryParams, '当前条件，准备获取数据')
      listZyjc({
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        zyzt: this.queryParams.zyzt,
        cykb: this.queryParams.cykb,
        zyys: this.queryParams.zyys,
        zyh: this.queryParams.zyh,
        sortColumn: this.sortColumn,
        sortOrder: this.sortOrder
      }).then(response => {
        console.log("获取数据成功")
        this.zyjcList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        jzh: null,
        zyh: null,
        ybh: null,
        brid: null,
        brbs: null,
        bah: null,
        zyid: null,
        xm: null,
        xb: null,
        nl: null,
        cykb: null,
        zyys: null,
        drgbh: null,
        zfy: null,
        hcf: null,
        ypf: null,
        jcf: null,
        jyf: null,
        zfbz: null,
        drgmc: null,
        zdmc: null,
        ssmc: null,
        jlsccyts: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      if (this.role == 'ys') {
        this.queryParams.zyys = this.yhxm
        this.queryParams.cykb = this.yhkb
      }
      this.resetForm("queryForm");
      this.queryParams.zyzt = '1'
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/zyjc/zyjc/export', {
        ...this.queryParams
      }, `在院检测_${getCurDayStr()}.xlsx`)
    },
    getDetails(row) {
      this.currentRow = null
      this.currentRow = row
      this.activeName = "zdjl"
      this.open = true
      this.title = '详情'
      // this.getZdjlList(row)
      this.getBljlList(row)
      // this.getBrxxList(row)
      this.getFyxxList(row)
    },
    //获取病例信息
    getBljlList(row) {
      getbljl(row.jzh).then(response => {
        this.bljlList = response.rows;
        this.bljlTotal = response.total;
      });
    },
    //获取诊断信息
    getZdjlList(row) {
      getZdjl(row.jzh).then(response => {
        this.zdxxList = response.rows;
        this.zdxxTotal = response.total;
      });
    },
    //获取费用信息
    getFyxxList(row) {
      this.fyxxParams.jzh = row.jzh
      console.log(this.fyxxParams)
      getFyxx(this.fyxxParams).then(response => {
        this.fyxxList = response.rows;
        this.fyxxTotal = response.total;
      });
    },
    //获取病人信息
    getBrxxList(row) {
      getBrxx(row.jzh).then(response => {
        this.brxxList = response.rows;
        this.brxxTotal = response.total;
      });
    },
    fyxxHz() {
      if (this.fyxx.query == "分项汇总") {
        this.fyxxParams.jzh = this.currentRow.jzh
        getFyxxByKs(this.fyxxParams).then(response => {
          this.fyxxList = response.rows;
          this.fyxxTotal = response.total;
        });
      } else {
        this.getFyxxList(this.currentRow)
      }
    },
    yfz(row) {
      console.log(row)
      getdk().then(response => {
        var dkh = response.rows[0]
        getFzPageIp().then(response => {
          window.open("http://" + response + ":" + dkh + "/views/drg/yfz/index?brid=" + row.brid + "&brbs=" + row.brbs + "&bah=" + row.bah + "&id=1");
        });
      });
    },
    handleSortChange(column) {
      if (column.prop) {
        this.sortColumn = column.prop;
        this.sortOrder = column.order === 'ascending' ? 'asc' : 'desc';
      } else {
        this.sortColumn = '';
        this.sortOrder = '';
      }
      this.getList();
    }
  }
};
</script>

<style>
.redFont {
  color: red;
}

.yellowFont {
  color: sandybrown;
}

.blueFont {
  color: blue;
}
</style>
