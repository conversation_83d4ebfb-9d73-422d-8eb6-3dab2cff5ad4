{"name": "ruoyi", "version": "3.8.5", "description": "医保管控系统", "author": "若依", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "@tinymce/tinymce-vue": "^3.2.8", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.25.3", "echarts": "5.4.0", "echarts-stat": "^1.2.0", "el-table-horizontal-scroll": "^1.2.5", "el-table-virtual-scroll": "^1.4.4", "element-ui": "2.15.13", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "jspdf": "^2.5.1", "marked": "^5.1.2", "nprogress": "0.2.0", "pinyin-match": "^1.2.5", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "tinymce": "^5.0.11", "vant": "^2.13.0", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue-virtual-scroller": "^1.1.2", "vuedraggable": "2.24.3", "vuex": "3.6.0", "vxe-table": "^2.11.0", "watermark-dom": "2.3.0", "xe-utils": "^3.5.31"}, "devDependencies": {"@babel/plugin-syntax-import-attributes": "^7.23.3", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "html-docx-js-typescript": "^0.1.5", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}