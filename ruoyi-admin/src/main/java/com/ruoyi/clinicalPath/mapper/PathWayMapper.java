package com.ruoyi.clinicalPath.mapper;

import java.io.InputStream;
import java.sql.Blob;
import java.util.List;
import com.ruoyi.clinicalPath.domain.PathWay;

/**
 * 临床路径数据管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface PathWayMapper
{
    /**
     * 查询临床路径数据管理
     *
     * @param id 临床路径数据管理主键
     * @return 临床路径数据管理
     */
    public PathWay selectPathWayById(Long id);


    public InputStream selectPathPdfCon(Long id);

    /**
     * 查询临床路径数据管理列表
     *
     * @param pathWay 临床路径数据管理
     * @return 临床路径数据管理集合
     */
    public List<PathWay> selectPathWayList(PathWay pathWay);

    public List<PathWay> searchPath(String pathName);

    public List<PathWay> selectPathWayList2(PathWay pathWay);

    public String selectContentById(Long id);
    /**
     * 新增临床路径数据管理
     *
     * @param pathWay 临床路径数据管理
     * @return 结果
     */
    public int insertPathWay(PathWay pathWay);

    /**
     * 修改临床路径数据管理
     *
     * @param pathWay 临床路径数据管理
     * @return 结果
     */
    public int updatePathWay(PathWay pathWay);

    /**
     * 删除临床路径数据管理
     *
     * @param id 临床路径数据管理主键
     * @return 结果
     */
    public int deletePathWayById(Long id);

    /**
     * 批量删除临床路径数据管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePathWayByIds(Long[] ids);
}
