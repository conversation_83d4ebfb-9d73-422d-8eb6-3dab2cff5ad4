package com.ruoyi.clinicalPath.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.ruoyi.clinicalPath.domain.LcljFyxmSh;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.clinicalPath.domain.LcljFyxm;
import com.ruoyi.clinicalPath.service.ILcljFyxmService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 临床路径费用项目Controller
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Anonymous
@RestController
@RequestMapping("/clinicalPath/lcljFyxm")
public class LcljFyxmController extends BaseController {
  @Autowired
  private ILcljFyxmService lcljFyxmService;

  /**
   * 查询临床路径费用项目列表
   */
  @GetMapping("/list")
  public TableDataInfo list(LcljFyxmSh lcljFyxmSh) {
    List<LcljFyxm> list = lcljFyxmService.selectLcljFyxmList(lcljFyxmSh);
    return getDataTable(list);
  }

  /**
   * 导出临床路径费用项目列表
   */
  @Log(title = "临床路径费用项目", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, LcljFyxmSh lcljFyxmSh) {
    List<LcljFyxm> list = lcljFyxmService.selectLcljFyxmByLclj(lcljFyxmSh);
    ExcelUtil<LcljFyxm> util = new ExcelUtil<LcljFyxm>(LcljFyxm.class);
    util.exportExcel(response, list, "临床路径费用项目数据");
  }

  /**
   * 查询是否已经提交当前方案/路径
   */
  @RequestMapping("/selectLcljItem")
  public LcljFyxmSh selectLcljItem(LcljFyxmSh lcljFyxmSh) {
    return lcljFyxmService.selectLcljItem(lcljFyxmSh);
  }

  /**
   * 保存方案信息及子费用项目记录
   */
  @RequestMapping("/saveLcljAndFyxms")
  public AjaxResult saveLcljAndFyxms(@RequestBody String lcljStr) {
    LcljFyxmSh lcljFyxmSh = JSON.parseObject(lcljStr, LcljFyxmSh.class);
    return toAjax(lcljFyxmService.insertLcljAndFyxms(lcljFyxmSh));
  }


  /**
   * 查询临床路径费用项目列表
   */
  @GetMapping("/listfyxmByPathId")
  public TableDataInfo listfyxmByPathId(@RequestParam("pathId") String pathId) {
    List<LcljFyxm> list = lcljFyxmService.selectLcljFyxmByPathId(pathId);
    return getDataTable(list);
  }


  /**
   * 查询临床路径费用项目列表
   */
  @GetMapping("/listLclj")
  public TableDataInfo listLclj(LcljFyxmSh lcljFyxmSh) {
    List<LcljFyxmSh> list = lcljFyxmService.selectLcljList(lcljFyxmSh);
    return getDataTable(list);
  }

  /**
   * 查询临床路径费用项目列表
   */
  @GetMapping("/listLcljSh")
  public TableDataInfo listLcljSh(LcljFyxmSh lcljFyxmSh) {
    startPage();
    List<LcljFyxmSh> list = lcljFyxmService.selectLcljLShist(lcljFyxmSh);
    System.out.println(lcljFyxmSh.getDrgbh());
    return getDataTable(list);
  }

  /**
   * 根据临床路径查询临床路径费用项目列表
   */
  @GetMapping("/listLcljFyxmByLclj")
  public TableDataInfo listLcljFyxmByLclj(LcljFyxmSh lcljFyxmSh) {
    List<LcljFyxm> list = lcljFyxmService.selectLcljFyxmByLclj(lcljFyxmSh);
    return getDataTable(list);
  }

  @GetMapping("/selectLcljFyxmByzd")
  public TableDataInfo selectLcljFyxmByzd(LcljFyxmSh lcljFyxmSh) {
    String ls_zd = null;
    if (lcljFyxmSh.getBzbm() != null) {
      ls_zd = lcljFyxmSh.getBzbm().length() >= 5 ? lcljFyxmSh.getBzbm().substring(0, 5) : lcljFyxmSh.getBzbm();
      lcljFyxmSh.setBzbm(ls_zd);
    }
    List<LcljFyxm> list = lcljFyxmService.selectLcljFyxmByzd(lcljFyxmSh);
    return getDataTable(list);
  }

  @GetMapping("/selectLcljFyxmByzdtxt")
  public String selectLcljFyxmByzdtxt(LcljFyxmSh lcljFyxmSh) {
    String ls_zd = null;
    String ls_return = "";
    String ls_fyxm = "";
    
    if (lcljFyxmSh.getDrgbh() != null&&!"".equals(lcljFyxmSh.getDrgbh())) {
    	lcljFyxmSh.setDrgbh(lcljFyxmSh.getDrgbh());
    }else {
    	lcljFyxmSh.setDrgbh("%");
    }
    
    if (lcljFyxmSh.getBzbm() != null) {
      ls_zd = lcljFyxmSh.getBzbm().length() >= 5 ? lcljFyxmSh.getBzbm().substring(0, 5) : lcljFyxmSh.getBzbm();
      lcljFyxmSh.setBzbm(ls_zd);
    }
    List<LcljFyxm> list = lcljFyxmService.selectLcljFyxmByzd(lcljFyxmSh);
    for (int i = 0; i < list.size(); i++) {
      ls_fyxm = list.get(i).getXmmc() + "|" + list.get(i).getScsyts() + "|" + list.get(i).getSyzts() + "|" + list.get(i).getRpjyl() + "|" + list.get(i).getPjfy().toString() + "|" + list.get(i).getFykmname() + "|" + list.get(i).getXmtype();
      if (i == 0) {
        ls_return = ls_fyxm;
      } else {
        ls_return = ls_return + "&" + ls_fyxm;
      }
    }
    return ls_return;
  }

  /**
   * 修改临床路径费用项目审核状态
   */
  @PutMapping
  public AjaxResult edit(@RequestBody LcljFyxmSh lcljFyxmSh) {
    int i = lcljFyxmService.updateLcljFyxmStatus(lcljFyxmSh);
    if (i == 0) {
      return AjaxResult.error("当前治疗方案已被删除或内容已被修改！");
    }
    return toAjax(i);
  }

  /**
   * 删除临床路径费用项目
   */
  @RequestMapping("/delLcljById")
  public AjaxResult delLcljById(@RequestParam("pathId") String pathId) {
    return toAjax(lcljFyxmService.deleteLcljById(pathId));
  }


  /**
   * 根据ID获取治疗方案
   *
   * @param lcljFyxmSh
   * @return
   */
  @RequestMapping("/selectLcljFyxmShById")
  public LcljFyxmSh selectLcljFyxmShById(LcljFyxmSh lcljFyxmSh) {
    return lcljFyxmService.selectLcljFyxmShById(lcljFyxmSh);
  }

}
