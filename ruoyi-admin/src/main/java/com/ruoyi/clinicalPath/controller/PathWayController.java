package com.ruoyi.clinicalPath.controller;

import com.ruoyi.clinicalPath.domain.PathWay;
import com.ruoyi.clinicalPath.service.IPathWayService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.BaSyjl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 临床路径数据管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@RestController
@RequestMapping("/clinicalPath/pathways")
public class PathWayController extends BaseController {
  @Autowired
  private IPathWayService pathWayService;

  /**
   * 查询临床路径数据管理列表
   */

  @GetMapping("/list")
  public TableDataInfo list(PathWay pathWay) {
    startPage();
    List<PathWay> list = pathWayService.selectPathWayList(pathWay);
    return getDataTable(list);
  }

  @GetMapping("/clearEmpty")
  public void clearEmpty() {
    List<PathWay> pathWays = pathWayService.selectPathWayList(new PathWay());
    for (PathWay pathWay : pathWays) {
      pathWay.setName(pathWay.getName().trim());
      pathWayService.updatePathWay(pathWay);
    }
  }

  @GetMapping("/searchPath")
  public  TableDataInfo searchPath(@RequestParam("name") String pathName) {
    List<PathWay> list = pathWayService.searchPath(pathName);
    return getDataTable(list);
  }

  @PostMapping("/getPathWay")
  public TableDataInfo getPathWay(@RequestBody BaSyjl basy) {
    List<PathWay> list = pathWayService.getPathWay(basy);
    return getDataTable(list == null ? new ArrayList<>() : list);
  }

  public byte[] getBytesFromInputStream(InputStream inputStream) throws Exception {
    try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
      int nRead;
      byte[] data = new byte[16384];

      while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
        buffer.write(data, 0, nRead);
      }

      buffer.flush();
      return buffer.toByteArray();
    }
  }

  @GetMapping("getConById")
  public String selectContentById(@RequestParam("id") Long id) {
    return pathWayService.selectContentById(id);
  }

  @Anonymous
  @RequestMapping("/batchGetPath")
  public void batchGetPath() {
    pathWayService.batchGetPath();
  }

  @PostMapping("/resetPathData")
  public AjaxResult resetPathData() {
    return toAjax(pathWayService.resetPathData());
  }

  @GetMapping(value = "/pdf/{id}", produces = MediaType.APPLICATION_PDF_VALUE)
  public ResponseEntity<byte[]> getPdf(@PathVariable Long id) throws Exception {
    try (InputStream inputStream = pathWayService.selectPathPdfCon(id)) {
      if (inputStream == null) {
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
      }
      byte[] pdfBytes = getBytesFromInputStream(inputStream);
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_PDF);
      headers.setContentLength(pdfBytes.length);
      return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);
    }
  }

  /**
   * 导出临床路径数据管理列表
   */

  @Log(title = "临床路径数据管理", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, PathWay pathWay) {
    List<PathWay> list = pathWayService.selectPathWayList(pathWay);
    ExcelUtil<PathWay> util = new ExcelUtil<PathWay>(PathWay.class);
    util.exportExcel(response, list, "临床路径数据管理数据");
  }

  /**
   * 获取临床路径数据管理详细信息
   */
  @PreAuthorize("@ss.hasPermi('clinicalPath:way:query')")
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Long id) {
    return success(pathWayService.selectPathWayById(id));
  }

  /**
   * 新增临床路径数据管理
   */

  @Log(title = "临床路径数据管理", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody PathWay pathWay) {
    return toAjax(pathWayService.insertPathWay(pathWay));
  }

  /**
   * 修改临床路径数据管理
   */
 
  @Log(title = "临床路径数据管理", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody PathWay pathWay) {
    return toAjax(pathWayService.updatePathWay(pathWay));
  }

  /**
   * 删除临床路径数据管理
   */

  @Log(title = "临床路径数据管理", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Long[] ids) {
    return toAjax(pathWayService.deletePathWayByIds(ids));
  }
}
