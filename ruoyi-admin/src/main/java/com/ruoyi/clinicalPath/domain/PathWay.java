package com.ruoyi.clinicalPath.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.sql.Blob;

/**
 * 临床路径数据管理对象 path_way
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public class PathWay extends BaseEntity {
  private static final long serialVersionUID = 1L;

  /**
   * 路径ID
   */
  private Long id;

  /**
   * 路径名称
   */
  @Excel(name = "路径名称")
  private String name;

  /**
   * 路径版本
   */
  @Excel(name = "路径版本")
  private String version;

  /**
   * 主要诊断
   */
  @Excel(name = "主要诊断")
  private String icd10Main;

  /**
   * 第二诊断
   */
  @Excel(name = "第二诊断")
  private String icd10Second;

  /**
   * 次要诊断
   */
  @Excel(name = "次要诊断")
  private String icd10Other;

  /**
   * 排除诊断
   */
  @Excel(name = "排除诊断")
  private String icd10Pc;

  /**
   * 手术编码
   */
  @Excel(name = "手术编码")
  private String icd9;

  /**
   * 排除手术
   */
  @Excel(name = "排除手术")
  private String icd9Pc;

  /**
   * 路径内容
   */
  @Excel(name = "路径内容")
  private String content;

  /**
   * 最小年龄范围
   */
  @Excel(name = "最小年龄范围")
  private BigDecimal minAge;

  /**
   * 最大年龄范围
   */
  @Excel(name = "最大年龄范围")
  private BigDecimal maxAge;

  /**
   * 病理诊断
   */
  @Excel(name = "病理诊断")
  private String blzd;

  /**
   * 是否需要手术
   */
  @Excel(name = "是否需要手术")
  private String needIcd9;

  private Blob pdf;

  private String status;

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public Blob  getPdf() {
    return pdf;
  }

  public void setPdf(Blob pdf) {
    this.pdf = pdf;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Long getId() {
    return id;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getName() {
    return name;
  }

  public void setVersion(String version) {
    this.version = version;
  }

  public String getVersion() {
    return version;
  }

  public void setIcd10Main(String icd10Main) {
    this.icd10Main = icd10Main;
  }

  public String getIcd10Main() {
    return icd10Main;
  }

  public void setIcd10Second(String icd10Second) {
    this.icd10Second = icd10Second;
  }

  public String getIcd10Second() {
    return icd10Second;
  }

  public void setIcd10Other(String icd10Other) {
    this.icd10Other = icd10Other;
  }

  public String getIcd10Other() {
    return icd10Other;
  }

  public void setIcd10Pc(String icd10Pc) {
    this.icd10Pc = icd10Pc;
  }

  public String getIcd10Pc() {
    return icd10Pc;
  }

  public void setIcd9(String icd9) {
    this.icd9 = icd9;
  }

  public String getIcd9() {
    return icd9;
  }

  public void setIcd9Pc(String icd9Pc) {
    this.icd9Pc = icd9Pc;
  }

  public String getIcd9Pc() {
    return icd9Pc;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getContent() {
    return content;
  }

  public void setMinAge(BigDecimal minAge) {
    this.minAge = minAge;
  }

  public BigDecimal getMinAge() {
    return minAge;
  }

  public void setMaxAge(BigDecimal maxAge) {
    this.maxAge = maxAge;
  }

  public BigDecimal getMaxAge() {
    return maxAge;
  }

  public void setBlzd(String blzd) {
    this.blzd = blzd;
  }

  public String getBlzd() {
    return blzd;
  }

  public void setNeedIcd9(String needIcd9) {
    this.needIcd9 = needIcd9;
  }

  public String getNeedIcd9() {
    return needIcd9;
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
      .append("id", getId())
      .append("name", getName())
      .append("version", getVersion())
      .append("icd10Main", getIcd10Main())
      .append("icd10Second", getIcd10Second())
      .append("icd10Other", getIcd10Other())
      .append("icd10Pc", getIcd10Pc())
      .append("icd9", getIcd9())
      .append("icd9Pc", getIcd9Pc())
      .append("minAge", getMinAge())
      .append("maxAge", getMaxAge())
      .append("blzd", getBlzd())
      .append("needIcd9", getNeedIcd9())
      .toString();
  }
}
