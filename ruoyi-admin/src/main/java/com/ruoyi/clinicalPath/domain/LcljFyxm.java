package com.ruoyi.clinicalPath.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 临床路径费用项目对象 lclj_fyxm
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
public class LcljFyxm extends BaseEntity {
  private static final long serialVersionUID = 1L;

  private Integer id;
  private String pathId;

  /**
   * DRG编码
   */
  @Excel(name = "DRG编码")
  private String drgbh;

  /**
   * 主要诊断编码
   */
  @Excel(name = "主要诊断编码")
  private String bzbm;

  /**
   * 主要手术编码
   */
  @Excel(name = "主要手术编码")
  private String ssbm;

  /**
   * 不含手术编号
   */
  @Excel(name = "不含手术编号")
  private String pcssbm;

  /**
   * 使用病人数
   */
  @Excel(name = "使用病人数")
  private Long sybrs;

  /**
   * 总的病人数
   */
  @Excel(name = "总的病人数")
  private Long zbrs;

  /**
   * 使用该项目占比
   */
  @Excel(name = "使用该项目占比")
  private BigDecimal sygxmzb;

  /**
   * 项目编码
   */
  @Excel(name = "项目编码")
  private String xmbm;

  /**
   * 项目名称
   */
  @Excel(name = "项目名称")
  private String xmmc;

  /**
   * 费用类别
   */
  @Excel(name = "费用类别")
  private String fykmname;

  /**
   * 日平均用量
   */
  @Excel(name = "日平均用量")
  private BigDecimal rpjyl;

  /**
   * 平均费用
   */
  @Excel(name = "平均费用")
  private BigDecimal pjfy;

  /**
   * 使用总天数
   */
  @Excel(name = "使用总天数")
  private BigDecimal syzts;

  /**
   * 首次使用天数
   */
  @Excel(name = "首次使用天数")
  private BigDecimal scsyts;

  /**
   * 费用占比
   */
  @Excel(name = "费用占比")
  private BigDecimal fyzb;

  /**
   * 平均用量
   */
  @Excel(name = "平均用量")
  private BigDecimal pjyl;

  /**
   * 费用占所有项目的占比
   */
  @Excel(name = "费用占所有项目的占比")
  private BigDecimal fyzsyxmdzb;

  /**
   * 平均住院费
   */
  @Excel(name = "平均住院费")
  private BigDecimal pjzyf;

  /**
   * 总量
   */
  @Excel(name = "总量")
  private BigDecimal zl;

  /**
   * 平均药品费
   */
  @Excel(name = "平均药品费")
  private BigDecimal ypf;

  /**
   * 平均检验费
   */
  @Excel(name = "平均检验费")
  private BigDecimal jyf;

  /**
   * 平均检查费
   */
  @Excel(name = "平均检查费")
  private BigDecimal jcf;

  /**
   * 平均手术费
   */
  @Excel(name = "平均手术费")
  private BigDecimal ssf;

  /**
   * 平均治疗费
   */
  @Excel(name = "平均治疗费")
  private BigDecimal zlf;

  @Excel(name = "项目属性")
  private String xmtype;

  /**
   * 提交状态
   */
  private String submitStatus;

  /**
   * 平均耗材
   */
  @Excel(name = "平均耗材")
  private BigDecimal hcf;

  /**
   * 开始时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date adtFrom;

  /**
   * 结束时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date adtTo;

  private int selectStatus;

  private BigDecimal zfbz;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public int getSelectStatus() {
    return selectStatus;
  }

  public void setSelectStatus(int selectStatus) {
    this.selectStatus = selectStatus;
  }

  public BigDecimal getZfbz() {
    return zfbz;
  }

  public void setZfbz(BigDecimal zfbz) {
    this.zfbz = zfbz;
  }

  public String getSubmitStatus() {
    return submitStatus;
  }

  public void setSubmitStatus(String submitStatus) {
    this.submitStatus = submitStatus;
  }

  public String getXmtype() {
    return xmtype;
  }

  public void setXmtype(String xmtype) {
    this.xmtype = xmtype;
  }

  public String getPathId() {
    return pathId;
  }

  public void setPathId(String pathId) {
    this.pathId = pathId;
  }

  public void setSybrs(Long sybrs) {
    this.sybrs = sybrs;
  }

  public Long getSybrs() {
    return sybrs;
  }

  public void setZbrs(Long zbrs) {
    this.zbrs = zbrs;
  }

  public Long getZbrs() {
    return zbrs;
  }

  public void setSygxmzb(BigDecimal sygxmzb) {
    this.sygxmzb = sygxmzb;
  }

  public BigDecimal getSygxmzb() {
    return sygxmzb;
  }

  public void setXmbm(String xmbm) {
    this.xmbm = xmbm;
  }

  public String getXmbm() {
    return xmbm;
  }

  public void setXmmc(String xmmc) {
    this.xmmc = xmmc;
  }

  public String getXmmc() {
    return xmmc;
  }

  public void setFykmname(String fykmname) {
    this.fykmname = fykmname;
  }

  public String getFykmname() {
    return fykmname;
  }

  public void setRpjyl(BigDecimal rpjyl) {
    this.rpjyl = rpjyl;
  }

  public BigDecimal getRpjyl() {
    return rpjyl;
  }

  public void setPjfy(BigDecimal pjfy) {
    this.pjfy = pjfy;
  }

  public BigDecimal getPjfy() {
    return pjfy;
  }

  public void setSyzts(BigDecimal syzts) {
    this.syzts = syzts;
  }

  public BigDecimal getSyzts() {
    return syzts;
  }

  public void setScsyts(BigDecimal scsyts) {
    this.scsyts = scsyts;
  }

  public BigDecimal getScsyts() {
    return scsyts;
  }

  public void setFyzb(BigDecimal fyzb) {
    this.fyzb = fyzb;
  }

  public BigDecimal getFyzb() {
    return fyzb;
  }

  public void setPjyl(BigDecimal pjyl) {
    this.pjyl = pjyl;
  }

  public BigDecimal getPjyl() {
    return pjyl;
  }

  public void setFyzsyxmdzb(BigDecimal fyzsyxmdzb) {
    this.fyzsyxmdzb = fyzsyxmdzb;
  }

  public BigDecimal getFyzsyxmdzb() {
    return fyzsyxmdzb;
  }

  public void setPjzyf(BigDecimal pjzyf) {
    this.pjzyf = pjzyf;
  }

  public BigDecimal getPjzyf() {
    return pjzyf;
  }

  public void setZl(BigDecimal zl) {
    this.zl = zl;
  }

  public BigDecimal getZl() {
    return zl;
  }

  public void setYpf(BigDecimal ypf) {
    this.ypf = ypf;
  }

  public BigDecimal getYpf() {
    return ypf;
  }

  public void setJyf(BigDecimal jyf) {
    this.jyf = jyf;
  }

  public BigDecimal getJyf() {
    return jyf;
  }

  public void setJcf(BigDecimal jcf) {
    this.jcf = jcf;
  }

  public BigDecimal getJcf() {
    return jcf;
  }

  public void setSsf(BigDecimal ssf) {
    this.ssf = ssf;
  }

  public BigDecimal getSsf() {
    return ssf;
  }

  public void setZlf(BigDecimal zlf) {
    this.zlf = zlf;
  }

  public BigDecimal getZlf() {
    return zlf;
  }

  public void setHcf(BigDecimal hcf) {
    this.hcf = hcf;
  }

  public BigDecimal getHcf() {
    return hcf;
  }

  public void setAdtFrom(Date adtFrom) {
    this.adtFrom = adtFrom;
  }

  public Date getAdtFrom() {
    return adtFrom;
  }

  public void setAdtTo(Date adtTo) {
    this.adtTo = adtTo;
  }

  public Date getAdtTo() {
    return adtTo;
  }

  public void setPcssbm(String pcssbm) {
    this.pcssbm = pcssbm;
  }

  public String getPcssbm() {
    return pcssbm;
  }

  public void setBzbm(String bzbm) {
    this.bzbm = bzbm;
  }

  public String getBzbm() {
    return bzbm;
  }

  public void setSsbm(String ssbm) {
    this.ssbm = ssbm;
  }

  public String getSsbm() {
    return ssbm;
  }

  public void setDrgbh(String drgbh) {
    this.drgbh = drgbh;
  }

  public String getDrgbh() {
    return drgbh;
  }

}
