package com.ruoyi.clinicalPath.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class LcljFyxmSh {
  private String id;
  private String drgbh;
  private String bzbm;
  private String ssbm;
  private String pcssbm;
  private String status;
  private String submitStatus;
  private String remark;
  private String createDate;
  private String startCreateDate;
  private String endCreateDate;
  private String submitDate;
  private String startSubmitDate;
  private String endSubmitDate;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date cyStartDate;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date cyEndDate;
  private String drgmc;
  private String bzmc;
  private BigDecimal zfbz;
  private String flag;

  private String selectStatus;

  private List<LcljFyxm> fyxmList;


  public LcljFyxmSh() {
  }

  public LcljFyxmSh(String drgbh, String bzbm, String ssbm, String pcssbm, String submitStatus) {
    this.drgbh = drgbh;
    this.bzbm = bzbm;
    this.ssbm = ssbm;
    this.pcssbm = pcssbm;
    this.submitStatus = submitStatus;
  }

  public BigDecimal getZfbz() {
    return zfbz;
  }

  public void setZfbz(BigDecimal zfbz) {
    this.zfbz = zfbz;
  }

  public String getDrgmc() {
    return drgmc;
  }

  public void setDrgmc(String drgmc) {
    this.drgmc = drgmc;
  }

  public String getBzmc() {
    return bzmc;
  }

  public void setBzmc(String bzmc) {
    this.bzmc = bzmc;
  }

  public String getFlag() {
    return flag;
  }

  public void setFlag(String flag) {
    this.flag = flag;
  }

  public String getSelectStatus() {
    return selectStatus;
  }

  public void setSelectStatus(String selectStatus) {
    this.selectStatus = selectStatus;
  }

  public List<LcljFyxm> getFyxmList() {
    return fyxmList;
  }

  public void setFyxmList(List<LcljFyxm> fyxmList) {
    this.fyxmList = fyxmList;
  }

  public Date getCyStartDate() {
    return cyStartDate;
  }

  public void setCyStartDate(Date cyStartDate) {
    this.cyStartDate = cyStartDate;
  }

  public Date getCyEndDate() {
    return cyEndDate;
  }

  public void setCyEndDate(Date cyEndDate) {
    this.cyEndDate = cyEndDate;
  }

  public String getStartCreateDate() {
    return startCreateDate;
  }

  public void setStartCreateDate(String startCreateDate) {
    this.startCreateDate = startCreateDate;
  }

  public String getEndCreateDate() {
    return endCreateDate;
  }

  public void setEndCreateDate(String endCreateDate) {
    this.endCreateDate = endCreateDate;
  }

  public String getSubmitDate() {
    return submitDate;
  }

  public void setSubmitDate(String submitDate) {
    this.submitDate = submitDate;
  }

  public String getStartSubmitDate() {
    return startSubmitDate;
  }

  public void setStartSubmitDate(String startSubmitDate) {
    this.startSubmitDate = startSubmitDate;
  }

  public String getEndSubmitDate() {
    return endSubmitDate;
  }

  public void setEndSubmitDate(String endSubmitDate) {
    this.endSubmitDate = endSubmitDate;
  }

  public String getCreateDate() {
    return createDate;
  }

  public void setCreateDate(String createDate) {
    this.createDate = createDate;
  }

  public String getSubmitStatus() {
    return submitStatus;
  }

  public void setSubmitStatus(String submitStatus) {
    this.submitStatus = submitStatus;
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getDrgbh() {
    return drgbh;
  }

  public void setDrgbh(String drgbh) {
    this.drgbh = drgbh;
  }

  public String getBzbm() {
    return bzbm;
  }

  public void setBzbm(String bzbm) {
    this.bzbm = bzbm;
  }

  public String getSsbm() {
    return ssbm;
  }

  public void setSsbm(String ssbm) {
    this.ssbm = ssbm;
  }

  public String getPcssbm() {
    return pcssbm;
  }

  public void setPcssbm(String pcssbm) {
    this.pcssbm = pcssbm;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }
}
