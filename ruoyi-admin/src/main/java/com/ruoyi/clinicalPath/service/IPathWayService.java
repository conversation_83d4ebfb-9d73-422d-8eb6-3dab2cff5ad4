package com.ruoyi.clinicalPath.service;

import com.ruoyi.clinicalPath.domain.PathWay;
import com.ruoyi.system.domain.BaSyjl;

import java.io.InputStream;
import java.util.List;

/**
 * 临床路径数据管理Service接口
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IPathWayService {
  /**
   * 查询临床路径数据管理
   *
   * @param id 临床路径数据管理主键
   * @return 临床路径数据管理
   */
  public PathWay selectPathWayById(Long id);

  public void batchGetPath();

  public Integer resetPathData();

  public  List<PathWay> searchPath(String pathName);

  public String selectContentById(Long id);

  public List<PathWay> getPathWay(BaSyjl basy);

  public InputStream selectPathPdfCon(Long id);

  /**
   * 查询临床路径数据管理列表
   *
   * @param pathWay 临床路径数据管理
   * @return 临床路径数据管理集合
   */
  public List<PathWay> selectPathWayList(PathWay pathWay);

  /**
   * 新增临床路径数据管理
   *
   * @param pathWay 临床路径数据管理
   * @return 结果
   */
  public int insertPathWay(PathWay pathWay);

  /**
   * 修改临床路径数据管理
   *
   * @param pathWay 临床路径数据管理
   * @return 结果
   */
  public int updatePathWay(PathWay pathWay);

  /**
   * 批量删除临床路径数据管理
   *
   * @param ids 需要删除的临床路径数据管理主键集合
   * @return 结果
   */
  public int deletePathWayByIds(Long[] ids);

  /**
   * 删除临床路径数据管理信息
   *
   * @param id 临床路径数据管理主键
   * @return 结果
   */
  public int deletePathWayById(Long id);
}
