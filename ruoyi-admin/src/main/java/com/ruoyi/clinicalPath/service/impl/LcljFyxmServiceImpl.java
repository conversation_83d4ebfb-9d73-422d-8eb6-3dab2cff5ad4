package com.ruoyi.clinicalPath.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Random;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.clinicalPath.mapper.LcljFyxmMapper;
import com.ruoyi.clinicalPath.domain.LcljFyxm;
import com.ruoyi.clinicalPath.domain.LcljFyxmSh;
import com.ruoyi.clinicalPath.service.ILcljFyxmService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

/**
 * 临床路径费用项目Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Service
public class LcljFyxmServiceImpl implements ILcljFyxmService {
  @Autowired
  private LcljFyxmMapper lcljFyxmMapper;

  /**
   * 查询临床路径费用项目列表
   *
   * @param lcljFyxmSh 临床路径费用项目
   * @return 临床路径费用项目
   */
  @Override
  public List<LcljFyxm> selectLcljFyxmList(LcljFyxmSh lcljFyxmSh) {
    List<LcljFyxm> lcljFyxmList = lcljFyxmMapper.selectLcljFyxmList(lcljFyxmSh);
    return setId(lcljFyxmList);
  }

  /**
   * 查询单临床路径费用项目列表
   *
   * @param pathId 临床路径费用项目
   * @return 临床路径费用项目集合
   */
  public List<LcljFyxm> selectLcljFyxmByPathId(String pathId) {
    return setId(lcljFyxmMapper.selectLcljFyxmByPathId(pathId));
  }

  /**
   * 根据临床路径获取项目信息
   *
   * @param lcljFyxmSh
   * @return
   */
  public List<LcljFyxm> selectLcljFyxmByLclj(LcljFyxmSh lcljFyxmSh) {
    return lcljFyxmMapper.selectLcljFyxmByLclj(lcljFyxmSh);
  }

  /**
   * 查询临床路径列表
   *
   * @param lcljFyxmSh 临床路径费用项目
   * @return 临床路径费用项目集合
   */
  @Override
  public List<LcljFyxmSh> selectLcljList(LcljFyxmSh lcljFyxmSh) {
    List<LcljFyxmSh> lcljFyxmList = lcljFyxmMapper.selectLcljList(lcljFyxmSh);
    lcljFyxmList.forEach(item -> {item.setFyxmList(setId(item.getFyxmList()));});
    return lcljFyxmList;
  }

  @Override
  public List<LcljFyxmSh> selectLcljLShist(LcljFyxmSh lcljFyxmSh) {
    return lcljFyxmMapper.selectLcljLShist(lcljFyxmSh);
  }

  public List<LcljFyxm> setId(List<LcljFyxm> list) {
    for (int i = 0; i < list.size(); i++) {
      list.get(i).setId(i + i);
    }
    return list;
  }

  /**
   * 修改临床路径费用项目审核状态
   *
   * @param lcljFyxmSh
   * @return 结果
   */
  public int updateLcljFyxmStatus(LcljFyxmSh lcljFyxmSh) {
    return lcljFyxmMapper.updateLcljFyxmStatus(lcljFyxmSh);
  }

  /**
   * 删除临床路径费用项目
   *
   * @param lcljFyxmSh
   * @return 结果
   */
  public int deleteLclj(LcljFyxmSh lcljFyxmSh) {
    return lcljFyxmMapper.deleteLclj(lcljFyxmSh);
  }


  @Override
  public List<LcljFyxm> selectLcljFyxmByzd(LcljFyxmSh lcljFyxmSh) {
    // TODO Auto-generated method stub
    return lcljFyxmMapper.selectLcljFyxmByzd(lcljFyxmSh);
  }


  /**
   * 根据Id删除路径
   *
   * @param id
   * @return
   */
  public int deleteLcljById(String id) {
    return lcljFyxmMapper.deleteLcljById(id);
  }


  /**
   * 查询是否已经提交当前方案/路径
   *
   * @param lcljFyxmSh
   * @return
   */
  public LcljFyxmSh selectLcljItem(LcljFyxmSh lcljFyxmSh) {
    return lcljFyxmMapper.selectLcljItem(lcljFyxmSh);
  }

  /**
   * 根据ID和提交状态获取未通过审核的方案  --  用于编辑页面获取方案数据
   *
   * @param lcljFyxmSh
   * @return
   */
  public LcljFyxmSh selectLcljFyxmShById(LcljFyxmSh lcljFyxmSh) {
    return lcljFyxmMapper.selectLcljFyxmShById(lcljFyxmSh);
  }


  public String getId() {
    LocalDateTime currentDate = LocalDateTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddSSS");
    String formattedDate = currentDate.format(formatter);
    Random random = new Random();
    int randomNumber = random.nextInt(100) + 1;
    String id = formattedDate + "" + randomNumber;
    return id;
  }


  /**
   * 保存方案或草稿
   * @param lcljFyxmSh
   * @return
   */
  @Transactional
  public Integer insertLcljAndFyxms(LcljFyxmSh lcljFyxmSh) {
    String submitStatus = lcljFyxmSh.getSubmitStatus();
    if ("2".equals(submitStatus)) {
      lcljFyxmSh.setSubmitStatus("1");
    }
    String currentDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    String id = getId();
    lcljFyxmMapper.deleteLclj(lcljFyxmSh);
    if ("1".equals(lcljFyxmSh.getSubmitStatus())) {
      lcljFyxmSh.setSubmitDate(currentDate);
    }
    lcljFyxmSh.setCreateDate(currentDate);
    lcljFyxmSh.setId(id);
    List<LcljFyxm> fyxmList = lcljFyxmSh.getFyxmList();
    fyxmList.forEach(item -> item.setPathId(id));
    lcljFyxmMapper.saveLclj(lcljFyxmSh);
    lcljFyxmMapper.deleteLcljFyxmByPathId(lcljFyxmSh.getId());
    lcljFyxmMapper.saveLcljFyxmList(fyxmList);
    if ("2".equals(submitStatus)) {
      lcljFyxmMapper.setResourceConsumption(id);
    }
    return 1;
  }

}
