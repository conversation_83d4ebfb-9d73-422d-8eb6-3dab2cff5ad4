package com.ruoyi.clinicalPath.service.impl;

import com.ruoyi.clinicalPath.domain.PathWay;
import com.ruoyi.clinicalPath.mapper.PathWayMapper;
import com.ruoyi.clinicalPath.service.IPathWayService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.BaBrzdxx;
import com.ruoyi.system.domain.BaSsjl;
import com.ruoyi.system.domain.BaSyjl;
import com.ruoyi.system.domain.Brxx;
import com.ruoyi.system.mapper.BrxxMapper;
import com.ruoyi.system.service.IBaSyjlService;
import info.debatty.java.stringsimilarity.JaroWinkler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 临床路径数据管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Service
public class PathWayServiceImpl implements IPathWayService {
  @Autowired
  private PathWayMapper pathWayMapper;
  @Autowired
  private BrxxMapper brxxMapper;
  @Autowired
  private IBaSyjlService baSyjlService;

  List<PathWay> pathWayList;

  static JaroWinkler jaroWinkler = new JaroWinkler();
  /**
   * 查询临床路径数据管理
   *
   * @param id 临床路径数据管理主键
   * @return 临床路径数据管理
   */
  @Override
  public PathWay selectPathWayById(Long id) {
    return pathWayMapper.selectPathWayById(id);
  }

  @Override
  public InputStream selectPathPdfCon(Long id) {
    return pathWayMapper.selectPathPdfCon(id);
  }

  @Override
  public  List<PathWay> searchPath(String pathName) {
    return pathWayMapper.searchPath(pathName);
  }


  @Override
  public List<PathWay> getPathWay(BaSyjl basy) {

    if (pathWayList == null) {
      this.resetPathData();
      if (pathWayList == null || pathWayList.isEmpty()) {
        return null;
      }
    }

    List<BaBrzdxx> baBrzdxxList = basy.getBaBrzdxxList();
    if (baBrzdxxList == null | baBrzdxxList.isEmpty()) {
      return null;
    }
    baBrzdxxList.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getJbbm()) || StringUtils.isBlank(obj.getZdmc()));
    if (baBrzdxxList.size() == 0) {
      return null;
    }

    List<BaSsjl> baSsjlList = basy.getBaSsjlList();
    if (baSsjlList == null) {
      baSsjlList = new ArrayList<>();
    } else {
      baSsjlList.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getSsbm())  || StringUtils.isBlank(obj.getSsmc()));
    }

    System.out.println(baBrzdxxList.size());
    System.out.println(baSsjlList.size());

    Long nl = basy.getNl();
    String blzd_name = basy.getBlzd();
    String blzd_code = basy.getJbbm();

    List<PathWay> pathWays = new ArrayList<>();

    int pdFlag = 1;

    //根据主要诊断找
    for (PathWay pathWay : pathWayList) {
      if (handleExprDiag(pathWay.getIcd10Main(), baBrzdxxList,pathWay.getName(),pdFlag)) {
        pathWays.add(pathWay);
        System.out.println(pathWay.getName() + ":" + pathWay.getIcd10Main() + ":" + pathWay.getIcd9());
      }
    }

    //根据主要诊断无法找到路径，判断主要诊断和路径名称是否相同，若相同继续判断前五位编码
    if (pathWays.size() == 0) {
      pdFlag = 2;

      for (PathWay pathWay : pathWayList) {
        if (handleExprDiag(pathWay.getIcd10Main(), baBrzdxxList,pathWay.getName(),pdFlag)) {
          pathWays.add(pathWay);
          System.out.println(pathWay.getName() + ":" + pathWay.getIcd10Main() + ":" + pathWay.getIcd9());
        }
      }
    }

    System.out.println(pdFlag);

    //根据其他诊断排除
    if (pathWays.size() > 0) {
      Iterator<PathWay> iterator = pathWays.iterator();
      while (iterator.hasNext()) {
        PathWay pathWay = iterator.next();

        //不存在并发症的情况（只有主要诊断）
        if (baBrzdxxList.size() > 1 && "空".equals(pathWay.getIcd10Other())) {
          iterator.remove();
          continue;
        }

        //检查第二诊断（只针对第二诊断）-- 排除第二诊断不匹配的路径
        if (StringUtils.isNotBlank(pathWay.getIcd10Second()) && (baBrzdxxList.size() < 2 || !equalsCode(pathWay.getIcd10Second(), baBrzdxxList.get(1).getJbbm()))) {
          iterator.remove();
          continue;
        }

        // 检查排除诊断（只针对主要诊断）  -- 排除主要诊断不匹配的路径
        if (StringUtils.isNotBlank(pathWay.getIcd10Pc()) && handleExprDiag(pathWay.getIcd10Pc(), baBrzdxxList, pathWay.getName(),1)) {
          iterator.remove();
          continue;
        }

        // 检查其他诊断（只针对其他诊断）  -- 排除其他诊断不匹配的路径
        if (StringUtils.isNotBlank(pathWay.getIcd10Other()) && !handleOrExprOtherDiag(pathWay.getIcd10Other(), baBrzdxxList)) {
          iterator.remove();
          continue;
        }

        //检查年龄  -- 排除年龄不匹配的路径
        if (nl != null) {
          if (pathWay.getMinAge() != null && pathWay.getMinAge().compareTo(BigDecimal.ZERO) > 0 && pathWay.getMinAge().compareTo(BigDecimal.valueOf(nl)) < 0) {
            iterator.remove();
            continue;
          }
          if (pathWay.getMaxAge() != null && pathWay.getMaxAge().compareTo(BigDecimal.ZERO) > 0 && pathWay.getMaxAge().compareTo(BigDecimal.valueOf(nl)) < 0) {
            iterator.remove();
            continue;
          }
        }

        //检查病理诊断  -- 排除病理诊断不匹配的路径
        if (StringUtils.isNotBlank(pathWay.getBlzd()) && (!equalsName(pathWay.getBlzd(), blzd_name) && !equalsCode(pathWay.getBlzd(), blzd_code))) {
          iterator.remove();
          continue;
        }
      }

      //根据手术排除
      List<PathWay> tempPathWayList = new ArrayList<>();
      if (pathWays.size() > 0) {
        iterator = pathWays.iterator();
        if (baSsjlList.isEmpty()) {  //如果患者没有手术，则返回不需要匹配手术的路径
          while (iterator.hasNext()) {
            PathWay pathWay = iterator.next();
            if ("1".equals(pathWay.getNeedIcd9()) || StringUtils.isNotBlank(pathWay.getIcd9())) {
              tempPathWayList.add(pathWay);
              iterator.remove();
            }
          }
        } else { // 如果当前患者有手术，则依次进行排除
          while (iterator.hasNext()) {
            PathWay pathWay = iterator.next();
            if (StringUtils.isNotBlank(pathWay.getIcd9()) && !handleExprOper(pathWay.getIcd9(), baSsjlList)) {
              tempPathWayList.add(pathWay);
              iterator.remove();
              continue;
            }
            if (StringUtils.isNotBlank(pathWay.getIcd9Pc()) && handleExprOper(pathWay.getIcd9Pc(), baSsjlList)) {
              tempPathWayList.add(pathWay);
              iterator.remove();
              continue;
            }
          }
        }
      } else {
        return null;
      }

      List<PathWay> result = new ArrayList<>();

      // 全部被手术排除  返回根据主要诊断查询的所有临床路径
      if (pathWays.size() == 0 && tempPathWayList.size() > 0) {
        for (PathWay pathWay : tempPathWayList) {
          if (!"1".equals(pathWay.getNeedIcd9())) {
            result.add(setContent(pathWay));
          }
        }
        return result;
      }


      //获取最优路径
      if (pathWays.size() > 1) {
//        String mainName = baBrzdxxList.get(0).getZdmc();
//        PathWay longestAndPath = null;

        List<PathWay> tempPathList = new ArrayList<>();

        //排除年龄 -- 儿童
        if (nl != null && nl > 18) {
          iterator = pathWays.iterator();
          while (iterator.hasNext()) {
            PathWay pathWay = iterator.next();
            if (pathWay.getName().contains("儿童")) {
              tempPathList.add(pathWay);
              iterator.remove();
            }
          }
        }

        if (pathWays.isEmpty()) {
          pathWays = new ArrayList<>(tempPathList);
        }

        for (PathWay pathWay : pathWays) {
          setContent(pathWay);
        }

        return pathWays;


//        if (pathWays.size() == 1) {
//          return new ArrayList<>();
//        }
//
//
//        if (pdFlag == 2) {
//          for (PathWay pathWay : pathWays) {
//            setContent(pathWay);
//          }
//          return pathWays;
//        }
//
//        //需要同时存在两个诊断的路径
//        for (PathWay pathWay : pathWays) {
//          String icdCode = pathWay.getIcd10Main();
//          if (StringUtils.isBlank(icdCode)) {
//            continue;
//          }
//          if (icdCode.contains("&")) {
//            if (longestAndPath == null || icdCode.length() > longestAndPath.getIcd10Main().length()) {
//              longestAndPath = pathWay;
//            }
//          }
//        }
//
//        if (longestAndPath != null) {
//          result.add(setContent(longestAndPath));
//          return result;
//        }
//
//        //主要诊断和路径名称相似最高的路径
//        double maxScore = 0;
//        PathWay maxScorePath = null;
//        for (PathWay pathWay : pathWays) {
//          double similarityScore = jaroWinkler.similarity(mainName, pathWay.getName());
//          System.out.println(pathWay.getName() + ":" + pathWay.getIcd10Main() + ":" + pathWay.getIcd9() + ":" + similarityScore);
//          if (similarityScore > maxScore) {
//            maxScore = similarityScore;
//            maxScorePath = pathWay;
//          }
//        }
//
//        if (maxScorePath != null) {
//          result.add(setContent(maxScorePath));
//          return result;
//        } else {
//          result.add(setContent(pathWays.get(0)));
//          return result;
//        }
      } else {
        if (pathWays.size() == 1) {
          result.add(setContent(pathWays.get(0)));
          return result;
        } else  {
          return null;
        }
      }
    }
    return null;
  }


  private PathWay setContent(PathWay pathWay) {
    String content = pathWayMapper.selectContentById(pathWay.getId());
    pathWay.setContent(content);
    return pathWay;
  }


  private static boolean handleExprDiag(String expr, List<BaBrzdxx> baBrzdxxList, String pName, int flag) {
    // 分割 || 并处理每个子表达式
    if (StringUtils.isNotBlank(expr)) {
      String[] orOrParts = expr.trim().split("\\|\\|");
      for (String part : orOrParts) {
        if (part.indexOf("&") > -1) {
          if (handleAndExprDiag(part.trim(), baBrzdxxList, pName, flag)) {
            return true;
          }
        } else {
          if (handleOrExprDiag(part.trim(), baBrzdxxList, pName, flag)) {
            return true;
          }
        }
      }
    }
    return false;
  }


  //检查其他诊断是否包含指定诊断
  private static boolean handleOrExprOtherDiag(String expr, List<BaBrzdxx> baBrzdxxList) {
    for (int i = 1; i < baBrzdxxList.size(); i++) {
      if (equalsName(expr, baBrzdxxList.get(i).getZdmc()) || equalsCode(expr, baBrzdxxList.get(i).getJbbm())) {
        return true;
      }
    }
    return false;
  }


  private static boolean handleAndExprDiag(String expr, List<BaBrzdxx> baBrzdxxList, String pName, int flag) {
    // 分割 & 并处理每个子表达式
    if (StringUtils.isNotBlank(expr)) {
      String[] andParts = expr.split("&");
      if (andParts.length > 0) {
        String andPartFirst = andParts[0];   //诊断的&符号第一位判断主要诊断，后面的判断次要诊断，手术不区分主要手术和次要手术
        BaBrzdxx mainIcd10 = baBrzdxxList.get(0);
        if (!equalsDiagCode(andPartFirst, mainIcd10.getJbbm(), mainIcd10.getZdmc(), pName, flag) && !equalsName(andPartFirst, mainIcd10.getZdmc())) {
          return false;
        }
        for (int i = 1; i < andParts.length; i++) {
          for (int j = 1; j < baBrzdxxList.size(); j++) {
            if (equalsName(andParts[i], baBrzdxxList.get(j).getZdmc()) || equalsCode(andParts[i], baBrzdxxList.get(j).getJbbm())) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  private static boolean handleOrExprDiag(String expr, List<BaBrzdxx> baBrzdxxList, String pName, int flag) {
    // 分割 | 并处理每个子表达式
    if (StringUtils.isNotBlank(expr)) {
      String[] orParts = expr.split("\\|");
      for (String part : orParts) {
        if (handleDiag(part.trim(), baBrzdxxList, pName, flag)) {
          return true;
        }
      }
    }
    return false;
  }

  private static boolean handleDiag(String expr, List<BaBrzdxx> baBrzdxxList, String pName, int flag) {
    if (baBrzdxxList.size() > 0) {
      BaBrzdxx baBrzdxx = baBrzdxxList.get(0);
      if (equalsDiagCode(expr, baBrzdxx.getJbbm(), baBrzdxx.getZdmc(), pName, flag) || equalsName(expr, baBrzdxx.getZdmc())) {
        return true;
      }
    }
    return false;
  }


  private static boolean handleExprOper(String expr, List<BaSsjl> baSsjlList) {
    // 分割 || 并处理每个子表达式
    if (StringUtils.isNotBlank(expr)) {
      String[] orOrParts = expr.split("\\|\\|");
      for (String part : orOrParts) {
        if (handleAndExprOper(part.trim(), baSsjlList)) {
          return true;
        }
      }
    }
    return false;
  }

  private static boolean handleAndExprOper(String expr, List<BaSsjl> baSsjlList) {
    // 分割 & 并处理每个子表达式
    if (StringUtils.isNotBlank(expr)) {
      String[] andParts = expr.split("&");
      for (String part : andParts) {
        if (!handleOrExprOper(part.trim(), baSsjlList)) {
          return false;
        }
      }
    }
    return true;
  }

  private static boolean handleOrExprOper(String expr, List<BaSsjl> baSsjlList) {
    // 分割 | 并处理每个子表达式
    if (StringUtils.isNotBlank(expr)) {
      String[] orParts = expr.split("\\|");
      for (String part : orParts) {
        if (handleOper(part.trim(), baSsjlList)) {
          return true;
        }
      }
    }
    return false;
  }

  private static boolean handleOper(String expr, List<BaSsjl> baSsjlList) {
    if (StringUtils.isNotBlank(expr)) {
      for (BaSsjl baSsjl : baSsjlList) {
        if (equalsOperCode(expr, baSsjl.getSsbm()) || equalsName(expr, baSsjl.getSsmc())) {
          return true;
        }
      }
    }
    return false;
  }


  private static boolean equalsCode(String expr, String code) {
    if (StringUtils.isBlank(expr) || StringUtils.isBlank(code)) {
      return false;
    }
    if (expr.length() > code.length()) {
      return false;
    }
    if (expr.length() == code.length() && expr.equals(code)) {
      return true;
    }
    if (expr.length() < code.length() && code.substring(0, expr.length()).equals(expr)) {
      return true;
    }
    return false;
  }

  private static boolean equalsDiagCode(String expr, String code, String name, String pathName, int flag) {
    if (StringUtils.isBlank(expr) || StringUtils.isBlank(code)) {
      return false;
    }
    if (flag == 1) {
      if (expr.length() == code.length() && expr.equals(code)) {
        return true;
      }
      if (expr.length() < code.length() && code.substring(0, expr.length()).equals(expr)) {
        return true;
      }
    } else if (flag == 2) {
      if (name.contains(pathName.replace("临床路径","")) || pathName.contains(name)) {
        if (expr.length() >= 5 && code.length() >= 5) {
          return expr.substring(0,5).equals(code.substring(0,5));
        }
      }
    }
    return false;
  }

  private static boolean equalsOperCode(String expr, String code) {
    if (StringUtils.isBlank(expr) || StringUtils.isBlank(code)) {
      return false;
    }
    if (expr.length() == code.length() && expr.equals(code)) {
      return true;
    }
    if (expr.length() < code.length() && code.substring(0, expr.length()).equals(expr)) {
      return true;
    }
    if (expr.length() >= 5 && code.length() >= 5) {
      return expr.substring(0,5).equals(code.substring(0,5));
    }
    return false;
  }

  private static boolean equalsName(String expr, String name) {
    if (StringUtils.isBlank(expr) || StringUtils.isBlank(name)) {
      return false;
    }
    return isContainChinese(expr) && name.indexOf(expr) > -1;
  }

  private static Pattern p = Pattern.compile("[\\u4e00-\\u9fa5]");

  private static boolean isContainChinese(String str) {
    Matcher m = p.matcher(str);
    return m.find();
  }


  /**
   * 查询临床路径数据管理列表
   *
   * @param pathWay 临床路径数据管理
   * @return 临床路径数据管理
   */
  @Override
  public List<PathWay> selectPathWayList(PathWay pathWay) {
    return pathWayMapper.selectPathWayList(pathWay);
  }

  /**
   * 新增临床路径数据管理
   *
   * @param pathWay 临床路径数据管理
   * @return 结果
   */
  @Override
  public int insertPathWay(PathWay pathWay) {
    return pathWayMapper.insertPathWay(pathWay);
  }

  /**
   * 修改临床路径数据管理
   *
   * @param pathWay 临床路径数据管理
   * @return 结果
   */
  @Override
  public int updatePathWay(PathWay pathWay) {
    return pathWayMapper.updatePathWay(pathWay);
  }

  /**
   * 批量删除临床路径数据管理
   *
   * @param ids 需要删除的临床路径数据管理主键
   * @return 结果
   */
  @Override
  public int deletePathWayByIds(Long[] ids) {
    return pathWayMapper.deletePathWayByIds(ids);
  }

  /**
   * 删除临床路径数据管理信息
   *
   * @param id 临床路径数据管理主键
   * @return 结果
   */
  @Override
  public int deletePathWayById(Long id) {
    return pathWayMapper.deletePathWayById(id);
  }


  public void batchGetPath() {
    List<Brxx> brxxList = brxxMapper.selectBrxxList(new Brxx());
    for (Brxx brxx : brxxList) {
      List<BaSyjl> brInfo = baSyjlService.getBrInfo(new BaSyjl(brxx.getJzh()));
      if (brInfo.size() > 0 && brInfo.get(0) != null) {
        List<PathWay> pathWays = getPathWay(brInfo.get(0));
        String name = "";
        if (pathWays == null) {
          name = null;
        } else {
          for (PathWay pathWay : pathWays) {
            if (StringUtils.isNotBlank(name)) {
              name += "," + pathWay.getName();
            } else {
              name = pathWay.getName();
            }
          }
        }

        Brxx uBrxx = new Brxx(brxx.getJzh());
        if (name != null) {
          uBrxx.setPath(name);
        } else {
          uBrxx.setPath("未入径");
        }
        brxxMapper.updateBrxx(uBrxx);

      }
    }
  }


  public Integer resetPathData() {
    PathWay pathWay = new PathWay();
    pathWay.setStatus("1");
    pathWayList = pathWayMapper.selectPathWayList2(pathWay);
    return 1;
  }


  public String selectContentById(Long id) {
    return pathWayMapper.selectContentById(id);
  }
}
