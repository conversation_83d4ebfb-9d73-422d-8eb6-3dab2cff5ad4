package com.ruoyi.clinicalPath.service;

import java.util.List;

import com.ruoyi.clinicalPath.domain.LcljFyxm;
import com.ruoyi.clinicalPath.domain.LcljFyxmSh;
import org.springframework.transaction.annotation.Transactional;

/**
 * 临床路径费用项目Service接口
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
public interface ILcljFyxmService {
  /**
   * 查询临床路径费用项目列表
   *
   * @param lcljFyxmSh 临床路径项目
   * @return 临床路径费用项目集合
   */
  public List<LcljFyxm> selectLcljFyxmList(LcljFyxmSh lcljFyxmSh);

  /**
   * 查询单临床路径费用项目列表
   *
   * @param pathId
   * @return 临床路径费用项目集合
   */
  public List<LcljFyxm> selectLcljFyxmByPathId(String pathId);

  public List<LcljFyxm> selectLcljFyxmByzd(LcljFyxmSh lcljFyxmSh);

  /**
   * 查询是否已经提交当前方案/路径
   *
   * @param lcljFyxmSh
   * @return
   */
  public LcljFyxmSh selectLcljItem(LcljFyxmSh lcljFyxmSh);
  public LcljFyxmSh selectLcljFyxmShById(LcljFyxmSh lcljFyxmSh);

  /**
   * 查询临床路径列表
   *
   * @param lcljFyxmSh 临床路径费用项目
   * @return 临床路径费用项目集合
   */
  public List<LcljFyxmSh> selectLcljList(LcljFyxmSh lcljFyxmSh);
  public List<LcljFyxmSh> selectLcljLShist(LcljFyxmSh lcljFyxmSh);


  /**
   * 根据临床路径获取项目信息
   *
   * @param lcljFyxmSh
   * @return
   */
  public List<LcljFyxm> selectLcljFyxmByLclj(LcljFyxmSh lcljFyxmSh);

  /**
   * 修改临床路径费用项目审核状态
   *
   * @param lcljFyxmSh
   * @return 结果
   */
  public int updateLcljFyxmStatus(LcljFyxmSh lcljFyxmSh);

  /**
   * 删除临床路径费用项目
   *
   * @param lcljFyxmSh
   * @return 结果
   */
  public int deleteLclj(LcljFyxmSh lcljFyxmSh);

  /**
   * 根据Id删除路径
   *
   * @param id
   * @return
   */
  public int deleteLcljById(String id);


  /**
   * 保存方案信息及子费用项目记录
   *
   * @param lcljFyxmSh
   * @return
   */
  public Integer insertLcljAndFyxms(LcljFyxmSh lcljFyxmSh);

}
