package com.ruoyi.orderFood.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> {
  /**
   * 总记录数
   */
  private Long total;

  /**
   * 总页数
   */
  private Integer totalPages;

  /**
   * 当前页码
   */
  private Integer pageNum;

  /**
   * 每页条数
   */
  private Integer pageSize;

  /**
   * 数据列表
   */
  private List<T> list;

  /**
   * 构建分页结果
   * @param list 数据列表
   * @param total 总记录数
   * @param pageNum 当前页码
   * @param pageSize 每页条数
   * @return 分页结果对象
   */
  public static <T> PageResult<T> build(List<T> list, long total, int pageNum, int pageSize) {
    int totalPages = (int) (total + pageSize - 1) / pageSize;
    return new PageResult<>(total, totalPages, pageNum, pageSize, list);
  }
}

