package com.ruoyi.orderFood.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 包含规格信息的商品详情DTO
 */
@Data
public class FoodItemDetailDTO {
    private Long id;
    private Long categoryId;
    private String categoryName;
    private String foodName;
    private String shortDesc;
    private String fullDesc;
    private BigDecimal price;
    private String nutritionInfo;
    private String ingredients;
    private String suggestions;
    private String imageUrl;
    private List<String> tags;
    private Integer isEnabled;
    private LocalDateTime createTime;
    
    // 规格相关信息
    private List<SpecParamDTO> specParams; // 规格参数列表
}
