package com.ruoyi.orderFood.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ShoppingCart {
    private Long id;
    private Long userId;
    private Long goodsId;
    private Long combinationId;
    private Integer quantity;
    private String selected;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
