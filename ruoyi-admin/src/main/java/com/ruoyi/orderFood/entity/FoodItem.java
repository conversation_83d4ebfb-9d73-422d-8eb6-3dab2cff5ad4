package com.ruoyi.orderFood.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class FoodItem {
    private Long id;
    private Long categoryId;
    private String foodName;
    private String shortDesc;
    private String fullDesc;
    private BigDecimal price;
    private String nutritionInfo;  // 存储JSON字符串
    private String ingredients;
    private String suggestions;
    private String imageUrl;
    private String tags;
    private Integer isEnabled;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
