package com.ruoyi.orderFood.mapper;

import com.ruoyi.orderFood.dto.FoodItemDTO;
import com.ruoyi.orderFood.entity.FoodItem;
import com.ruoyi.orderFood.vo.GoodsVO;
import com.ruoyi.orderFood.vo.SpecCombinationVO;
import com.ruoyi.orderFood.vo.SpecParamVO;
import com.ruoyi.orderFood.vo.SpecValueVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface FoodItemMapper {
  FoodItemDTO selectFoodWithSpecData(@Param("id") Long id);

  /**
   * 查询商品列表
   * @param categoryId 分类ID（可为null）
   * @return 商品列表
   */
  List<FoodItemDTO> selectFoodItems(@Param("categoryId") Long categoryId);


  List<SpecParamVO> selectSpecParams(Long foodId);

  List<SpecValueVO> selectSpecValues(Long foodId);

  List<SpecCombinationVO> selectSpecCombinations(Long foodId);

  FoodItemDTO selectFoodBaseInfo(Long foodId);

  /**
   * 根据ID查询商品详情
   * @param id 商品ID
   * @return 商品详情DTO
   */
  FoodItemDTO selectByCategoriesId(@Param("id") Long id);
  // 查询所有商品
  List<FoodItem> selectAll();
  // 根据分类ID查询商品
  List<FoodItem> selectByCategoryId(@Param("categoryId") Long categoryId);
}
