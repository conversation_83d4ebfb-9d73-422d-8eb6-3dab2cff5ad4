package com.ruoyi.orderFood.mapper;

import com.ruoyi.orderFood.entity.ShoppingCart;
import com.ruoyi.orderFood.vo.CartItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface ShoppingCartMapper {
    // 根据用户ID查询购物车
    List<CartItemVO> selectByUserId(@Param("userId") Long userId);

    // 添加商品到购物车
    int insert(ShoppingCart cart);

    // 更新购物车商品数量和备注
    int updateQuantityAndRemark(@Param("id") Long id, @Param("quantity") Integer quantit);

    // 根据用户ID、商品ID和规格ID查询购物车项
    ShoppingCart selectByUserIdFoodIdAndSpecId(
            @Param("userId") Long userId,
            @Param("goodsId") Long goodsId,
            @Param("combinationId") Long combinationId);

    // 删除购物车项
    int deleteById(@Param("id") Long id);

    // 批量删除购物车项
    int deleteByIds(@Param("ids") List<Long> ids);
}
