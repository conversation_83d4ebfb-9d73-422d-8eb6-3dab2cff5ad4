package com.ruoyi.orderFood.mapper;

import com.ruoyi.orderFood.entity.Order;
import com.ruoyi.orderFood.vo.OrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface OrderMapper {
    // 创建订单
    int insert(Order order);

    // 根据用户ID查询订单列表
    List<OrderVO> selectByUserId(@Param("userId") Long userId);

    // 根据ID查询订单
    OrderVO selectById(@Param("id") Long id);

    // 更新订单支付状态
    int updatePayStatus(@Param("id") Long id, @Param("status") Integer status, @Param("payTime") String payTime);
}
