package com.ruoyi.orderFood.mapper;

import com.ruoyi.orderFood.vo.SpecCombinationVO;
import com.ruoyi.orderFood.vo.SpecValueVO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface SpecCombinationMapper {
  // 查询商品的所有规格组合
  List<SpecCombinationVO> selectCombinationsByGoodsId(@Param("goodsId") Long goodsId);

  // 查询商品的所有规格值
  List<SpecValueVO> selectSpecValuesByGoodsId(@Param("goodsId") Long goodsId);
}

