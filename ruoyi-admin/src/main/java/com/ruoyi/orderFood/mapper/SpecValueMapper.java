package com.ruoyi.orderFood.mapper;

import com.ruoyi.orderFood.dto.SpecValueDTO;
import com.ruoyi.orderFood.vo.SpecValueVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SpecValueMapper {

    /**
     * 根据参数ID列表查询规格值
     */
    List<SpecValueDTO> selectByParamIds(@Param("paramIds") List<Long> paramIds);

  List<SpecValueVO> selectValueVOByParamIds(@Param("paramIds") Long[] paramIds);
}
