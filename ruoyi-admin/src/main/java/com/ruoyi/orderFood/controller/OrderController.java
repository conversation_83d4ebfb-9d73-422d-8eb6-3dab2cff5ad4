package com.ruoyi.orderFood.controller;

import com.ruoyi.orderFood.service.OrderService;
import com.ruoyi.orderFood.util.Result;
import com.ruoyi.orderFood.vo.OrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/f/orders")
public class OrderController {

    @Autowired
    private OrderService orderService;

    @PostMapping
    public Result createOrder(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.parseLong(params.get("userId").toString());
            String deliveryAddress = (String) params.get("deliveryAddress");
            String contactInfo = (String) params.get("contactInfo");
            String remark = (String) params.get("remark");
            List<Long> cartItemIds = (List<Long>) params.get("cartItemIds");

            Map<String, Object> result = orderService.createOrder(
                    userId, deliveryAddress, contactInfo, remark, cartItemIds);

            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @GetMapping
    public Result getUserOrders(@RequestParam Long userId) {
        List<OrderVO> orders = orderService.getUserOrders(userId);
        return Result.success(orders);
    }

    @GetMapping("/{id}")
    public Result getOrderDetail(@PathVariable Long id) {
        OrderVO order = orderService.getOrderDetail(id);
        if (order == null) {
            return Result.error(404, "订单不存在");
        }
        return Result.success(order);
    }

    @PutMapping("/{id}/pay")
    public Result payOrder(@PathVariable Long id) {
        boolean success = orderService.payOrder(id);
        if (success) {
            return Result.success("支付成功");
        } else {
            return Result.error("支付失败");
        }
    }
}
