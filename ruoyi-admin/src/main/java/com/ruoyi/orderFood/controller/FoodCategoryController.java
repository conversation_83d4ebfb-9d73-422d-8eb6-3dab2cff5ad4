package com.ruoyi.orderFood.controller;


import com.ruoyi.orderFood.entity.FoodCategory;
import com.ruoyi.orderFood.service.FoodCategoryService;
import com.ruoyi.orderFood.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@RequestMapping("/f/categories")
public class FoodCategoryController {

    @Autowired
    private FoodCategoryService foodCategoryService;

    @GetMapping
    public Result getCategories() {
        List<FoodCategory> categories = foodCategoryService.getAllEnabledCategories();
        return Result.success(categories);
    }
}
