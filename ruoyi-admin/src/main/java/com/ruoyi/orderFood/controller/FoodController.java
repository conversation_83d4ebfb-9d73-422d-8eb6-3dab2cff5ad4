package com.ruoyi.orderFood.controller;

import com.ruoyi.orderFood.service.FoodService;
import com.ruoyi.orderFood.util.Result;
import com.ruoyi.orderFood.vo.GoodsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/f/goods")
public class FoodController {

    @Autowired
    private FoodService foodService;

    @GetMapping
    public Result getGoods(@RequestParam(required = false) Long categoryId) {
        List<GoodsVO> goods;
        if (categoryId != null) {
            goods = foodService.getGoodsByCategoryId(categoryId);
        } else {
            goods = foodService.getAllGoods();
        }
        return Result.success(goods);
    }

}
