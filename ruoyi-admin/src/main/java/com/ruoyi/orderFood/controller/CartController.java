package com.ruoyi.orderFood.controller;

import com.ruoyi.orderFood.entity.ShoppingCart;
import com.ruoyi.orderFood.service.CartService;
import com.ruoyi.orderFood.util.Result;
import com.ruoyi.orderFood.vo.CartItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/f/cart")
public class CartController {

    @Autowired
    private CartService cartService;

    @GetMapping
    public Result getCart(@RequestParam Long userId) {
        List<CartItemVO> cartItems = cartService.getUserCart(userId);
        return Result.success(cartItems);
    }

    @PostMapping
    public Result addToCart(@RequestBody ShoppingCart cart) {
        boolean success = cartService.addToCart(cart);
        if (success) {
            return Result.success("添加购物车成功");
        } else {
            return Result.error("添加购物车失败");
        }
    }

    @PutMapping("/{id}")
    public Result updateCartItem(
            @PathVariable Long id,
            @RequestBody Map<String, Object> params) {

        Integer quantity = (Integer) params.get("quantity");
        String remark = (String) params.get("remark");

        boolean success = cartService.updateCartItem(id, quantity, remark);
        if (success) {
            return Result.success("更新购物车成功");
        } else {
            return Result.error("更新购物车失败");
        }
    }

    @DeleteMapping("/{id}")
    public Result deleteCartItem(@PathVariable Long id) {
        boolean success = cartService.deleteCartItem(id);
        if (success) {
            return Result.success("删除购物车商品成功");
        } else {
            return Result.error("删除购物车商品失败");
        }
    }

    @DeleteMapping("/batch")
    public Result batchDeleteCartItems(@RequestBody List<Long> ids) {
        boolean success = cartService.batchDeleteCartItems(ids);
        if (success) {
            return Result.success("批量删除购物车商品成功");
        } else {
            return Result.error("批量删除购物车商品失败");
        }
    }
}
