package com.ruoyi.orderFood.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.orderFood.dto.FoodItemDTO;
import com.ruoyi.orderFood.dto.FoodItemDetailDTO;
import com.ruoyi.orderFood.dto.PageResult;
import com.ruoyi.orderFood.service.FoodItemService;
import com.ruoyi.orderFood.service.ISpecCombinationService;
import com.ruoyi.orderFood.vo.FoodSpecDataVO;
import com.ruoyi.orderFood.vo.SpecCombinationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/f/foodItems")
@Api(tags = "商品管理接口")
public class FoodItemController {

    @Autowired
    private FoodItemService foodItemService;

    @Autowired
    private ISpecCombinationService specCombinationService;
    @GetMapping
    @ApiImplicitParams({
        @ApiImplicitParam(name = "categoryId", value = "分类ID，可选", dataType = "long", paramType = "query"),
        @ApiImplicitParam(name = "pageNum", value = "页码，默认1", defaultValue = "1", dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "每页条数，默认10", defaultValue = "10", dataType = "int", paramType = "query")
    })
    public ResponseEntity<PageResult<FoodItemDTO>> getFoodItems(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<FoodItemDTO> result = foodItemService.getFoodItems(categoryId, pageNum, pageSize);
        return ResponseEntity.ok(result);
    }

  /**
   * 根据商品ID查询规格组合列表
   */
  @GetMapping("/combinations/{goodsId}")
  public AjaxResult getCombinations(@PathVariable Long goodsId) {
    FoodSpecDataVO foodSpecDataVO = specCombinationService.getCombinationsByGoodsId(goodsId);
    return AjaxResult.success(foodSpecDataVO);
  }




}
