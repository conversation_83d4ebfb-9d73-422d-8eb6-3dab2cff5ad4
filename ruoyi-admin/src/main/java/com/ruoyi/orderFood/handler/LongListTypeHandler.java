package com.ruoyi.orderFood.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用于处理 List<Long> 与数据库字符串的转换（如 "1,2,3" ←→ [1,2,3]）
 */
@MappedTypes(List.class)  // 映射Java类型
@MappedJdbcTypes(JdbcType.VARCHAR)  // 映射数据库类型
public class LongListTypeHandler extends BaseTypeHandler<List<Long>> {

  /**
   * 插入数据时：将List<Long>转换为逗号分隔的字符串
   */
  @Override
  public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType) throws SQLException {
    if (parameter == null || parameter.isEmpty()) {
      ps.setString(i, "");
      return;
    }
    // 转换为"1,2,3"格式
    String value = parameter.stream()
      .map(String::valueOf)
      .collect(Collectors.joining(","));
    ps.setString(i, value);
  }

  /**
   * 查询时：将数据库字符串转换为List<Long>
   */
  @Override
  public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
    return stringToList(rs.getString(columnName));
  }

  @Override
  public List<Long> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
    return stringToList(rs.getString(columnIndex));
  }

  @Override
  public List<Long> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
    return stringToList(cs.getString(columnIndex));
  }

  /**
   * 字符串转List<Long>（核心转换逻辑）
   */
  private List<Long> stringToList(String value) {
    if (value == null || value.trim().isEmpty()) {
      return new ArrayList<>();
    }
    // 分割字符串并转换为Long
    return Arrays.stream(value.split(","))
      .map(Long::valueOf)
      .collect(Collectors.toList());
  }
}
