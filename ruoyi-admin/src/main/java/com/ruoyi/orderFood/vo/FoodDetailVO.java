package com.ruoyi.orderFood.vo;

import com.ruoyi.orderFood.entity.FoodItem;
import com.ruoyi.orderFood.entity.FoodSpec;
import java.util.List;

/**
 * 商品详情视图对象（包含规格、分类等扩展信息）
 */
public class FoodDetailVO extends FoodItem {
  // 分类名称（冗余字段，用于前端直接展示）
  private String categoryName;

  // 商品规格列表（一对多关系）
  private List<FoodSpec> specs;

  // 标签数组（将数据库中逗号分隔的字符串转为数组）
  private String[] tagList;

  // getter和setter方法
  public String getCategoryName() {
    return categoryName;
  }

  public void setCategoryName(String categoryName) {
    this.categoryName = categoryName;
  }

  public List<FoodSpec> getSpecs() {
    return specs;
  }

  public void setSpecs(List<FoodSpec> specs) {
    this.specs = specs;
  }

  public String[] getTagList() {
    return tagList;
  }

  public void setTagList(String[] tagList) {
    this.tagList = tagList;
  }
}
