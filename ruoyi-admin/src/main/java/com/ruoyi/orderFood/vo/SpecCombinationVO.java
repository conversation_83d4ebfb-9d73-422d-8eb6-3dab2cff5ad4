package com.ruoyi.orderFood.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

// SpecCombinationVO.java（增加用于接收数据库字符串的临时字段）
@Data
public class SpecCombinationVO {
  // 数据库原始ID（临时使用）
  private Long dbId;
  // 前端展示的组合ID（如SC001）
  private String combinationId;
  // 规格项数组
  private List<SpecItemVO> specs = new ArrayList<>();
  // 价格对象
  private PriceVO price = new PriceVO();
  // 库存
  private Integer stock;
  // 临时字段：规格值ID字符串（如"1,4,7"）
  private String valueIdsStr;
  // 临时字段：接收数据库中的价格
  private BigDecimal originalPriceTemp;
  private BigDecimal sellPriceTemp;
}
