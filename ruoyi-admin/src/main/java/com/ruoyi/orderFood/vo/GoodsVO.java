package com.ruoyi.orderFood.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class GoodsVO {
    private Long id;
    private String name;
    private String desc;
    private String fullDesc;
    private BigDecimal price;
    private String image;
    private Long categoryId;
    private List<String> type;  // 标签列表
    private Map<String, Object> nutrition;  // 营养信息
    private List<SpecVO> specs;  // 规格列表
    private String ingredients;
    private String suggestions;
    private List<String> tags;
}
