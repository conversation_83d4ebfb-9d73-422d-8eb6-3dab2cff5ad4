package com.ruoyi.orderFood.service.impl;

import com.ruoyi.orderFood.entity.Order;
import com.ruoyi.orderFood.entity.OrderDetail;
import com.ruoyi.orderFood.mapper.OrderDetailMapper;
import com.ruoyi.orderFood.mapper.OrderMapper;
import com.ruoyi.orderFood.mapper.ShoppingCartMapper;
import com.ruoyi.orderFood.service.OrderService;
import com.ruoyi.orderFood.vo.CartItemVO;
import com.ruoyi.orderFood.vo.OrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderDetailMapper orderDetailMapper;

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    @Override
    @Transactional
    public Map<String, Object> createOrder(Long userId, String deliveryAddress,
                                         String contactInfo, String remark, List<Long> cartItemIds) {
        // 1. 获取购物车商品
        List<CartItemVO> cartItems = shoppingCartMapper.selectByUserId(userId).stream()
                .filter(item -> cartItemIds.contains(item.getId()))
                .collect(Collectors.toList());

        if (cartItems.isEmpty()) {
            throw new RuntimeException("购物车商品不存在");
        }

        // 2. 计算总金额
        BigDecimal totalAmount = cartItems.stream()
                .map(CartItemVO::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 3. 生成订单号
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String orderNo = "ORD" + sdf.format(new Date()) +
                        (int)(Math.random() * 1000);

        // 4. 创建订单
        Order order = new Order();
        order.setUserId(userId);
        order.setOrderNo(orderNo);
        order.setTotalAmount(totalAmount);
        order.setStatus(1); // 1-待支付
        order.setDeliveryAddress(deliveryAddress);
        order.setContactInfo(contactInfo);
        order.setRemark(remark);
        order.setCreateTime(new Date());

        orderMapper.insert(order);
        Long orderId = order.getId();

        // 5. 创建订单详情
        List<OrderDetail> orderDetails = cartItems.stream()
                .map(item -> {
                    OrderDetail detail = new OrderDetail();
                    detail.setOrderId(orderId);
                    detail.setFoodId(item.getFoodId());
                    detail.setSpecId(item.getSpecId());
                    detail.setQuantity(item.getQuantity());
                    detail.setUnitPrice(item.getSpecPrice());
                    detail.setRemark(item.getRemark());
                    return detail;
                })
                .collect(Collectors.toList());

        orderDetailMapper.batchInsert(orderDetails);

        // 6. 删除购物车中已下单的商品
        shoppingCartMapper.deleteByIds(cartItemIds);

        // 7. 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("orderId", orderId);
        result.put("orderNo", orderNo);
        result.put("totalAmount", totalAmount);

        return result;
    }

    @Override
    public List<OrderVO> getUserOrders(Long userId) {
        return orderMapper.selectByUserId(userId);
    }

    @Override
    public OrderVO getOrderDetail(Long orderId) {
        return orderMapper.selectById(orderId);
    }

    @Override
    @Transactional
    public boolean payOrder(Long orderId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return orderMapper.updatePayStatus(
                orderId,
                2, // 2-已支付
                sdf.format(new Date())
        ) > 0;
    }
}
