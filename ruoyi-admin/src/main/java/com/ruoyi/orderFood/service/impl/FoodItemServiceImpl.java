package com.ruoyi.orderFood.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.orderFood.dto.*;
import com.ruoyi.orderFood.entity.FoodSpec;
import com.ruoyi.orderFood.mapper.FoodItemMapper;
import com.ruoyi.orderFood.mapper.SpecParamMapper;
import com.ruoyi.orderFood.mapper.SpecValueMapper;
import com.ruoyi.orderFood.service.FoodItemService;
import com.ruoyi.orderFood.vo.FoodSpecDataVO;
import com.ruoyi.orderFood.vo.SpecCombinationVO;
import com.ruoyi.orderFood.vo.SpecParamVO;
import com.ruoyi.orderFood.vo.SpecValueVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FoodItemServiceImpl implements FoodItemService {

    @Autowired
    private FoodItemMapper foodItemMapper;

    @Autowired
    private SpecParamMapper specParamMapper;

    @Autowired
    private SpecValueMapper specValueMapper;

    @Override
    public PageResult<FoodItemDTO> getFoodItems(Long categoryId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<FoodItemDTO> list = foodItemMapper.selectFoodItems(categoryId);

        // 处理标签为列表
        list.forEach(item -> {
            if (StringUtils.hasText(item.getTags())) {
              item.setTags(String.valueOf(new ArrayList<>(Arrays.asList(item.getTags().split(",")))));
            }
        });

        PageInfo<FoodItemDTO> pageInfo = new PageInfo<>(list);
        return new PageResult<>(
            pageInfo.getTotal(),
            pageInfo.getPages(),
            pageInfo.getPageNum(),
            pageInfo.getPageSize(),
            pageInfo.getList()
        );
    }


}
