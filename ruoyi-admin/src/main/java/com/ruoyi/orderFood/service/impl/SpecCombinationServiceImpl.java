package com.ruoyi.orderFood.service.impl;

import com.ruoyi.orderFood.mapper.SpecCombinationMapper;
import com.ruoyi.orderFood.mapper.SpecParamMapper;
import com.ruoyi.orderFood.mapper.SpecValueMapper;
import com.ruoyi.orderFood.service.ISpecCombinationService;
import com.ruoyi.orderFood.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class SpecCombinationServiceImpl implements ISpecCombinationService {

  @Autowired
  private SpecCombinationMapper specCombinationMapper;
  @Autowired
  private SpecParamMapper specParamMapper;
  @Autowired
  private SpecValueMapper specValueMapper;


  @Override
  public FoodSpecDataVO getCombinationsByGoodsId(Long goodsId) {
    // 1. 查询所有规格组合
    List<SpecCombinationVO> combinations = specCombinationMapper.selectCombinationsByGoodsId(goodsId);
    if (combinations.isEmpty()) {
      return new FoodSpecDataVO();
    }

    // 2. 查询所有规格值（用于匹配规格项）
    List<SpecValueVO> allSpecValues = specCombinationMapper.selectSpecValuesByGoodsId(goodsId);

    // 3. 处理每个组合，转换为目标格式
    for (SpecCombinationVO comb : combinations) {
      // 3.1 格式化组合ID（数据库ID→SC001）
      comb.setCombinationId(String.valueOf(comb.getDbId()));

      // 3.2 组装价格对象
      PriceVO price = new PriceVO();
      price.setOriginalPrice(comb.getOriginalPriceTemp());
      price.setSellPrice(comb.getSellPriceTemp());
      comb.setPrice(price);

      // 3.3 组装specs数组
      List<SpecItemVO> specs = new ArrayList<>();
      String valueIdsStr = comb.getValueIdsStr();
      if (valueIdsStr != null && !valueIdsStr.trim().isEmpty()) {
        String[] valueIdArr = valueIdsStr.split(",");
        for (String valueIdStr : valueIdArr) {
          try {
            Long valueId = Long.valueOf(valueIdStr);
            // 匹配对应的规格值信息
            for (SpecValueVO specValue : allSpecValues) {
              if (valueId.equals(specValue.getValueId())) {
                SpecItemVO specItem = new SpecItemVO();
                specItem.setParamId(String.valueOf(specValue.getParamId()));
                specItem.setValueId(String.valueOf(specValue.getValueId()));
                specItem.setValueName(specValue.getValueName());
                specs.add(specItem);
                break;
              }
            }
          } catch (NumberFormatException e) {
            // 处理无效ID格式
            e.printStackTrace();
          }
        }
      }
      comb.setSpecs(specs);
    }
    FoodSpecDataVO foodSpecDataVO = new FoodSpecDataVO();
    List<SpecParamVO> specParamVOS= specParamMapper.selectParamVOByGoodsId(goodsId);//查询规格参数列表
    Long[] paramIds= new Long[specParamVOS.size()];
    for (int i = 0; i < specParamVOS.size(); i++) {
      paramIds[i]= specParamVOS.get(i).getParamId();
    }
    List<SpecValueVO> specValueVOS= specValueMapper.selectValueVOByParamIds(paramIds);//查询规格值列表
    foodSpecDataVO.setSpecParams(specParamVOS);//设置规格参数列表
    foodSpecDataVO.setSpecValues(specValueVOS);//设置规格值列表
    foodSpecDataVO.setSpecCombinationWithPrice(combinations);//设置规格组合列表
    return foodSpecDataVO;
  }
}
