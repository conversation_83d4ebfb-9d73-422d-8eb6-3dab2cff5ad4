package com.ruoyi.orderFood.service;

import com.ruoyi.orderFood.entity.ShoppingCart;
import com.ruoyi.orderFood.vo.CartItemVO;

import java.util.List;

public interface CartService {
    // 获取用户购物车
    List<CartItemVO> getUserCart(Long userId);

    // 添加商品到购物车
    boolean addToCart(ShoppingCart cart);

    // 更新购物车商品
    boolean updateCartItem(Long id, Integer quantity, String remark);

    // 删除购物车商品
    boolean deleteCartItem(Long id);

    // 批量删除购物车商品
    boolean batchDeleteCartItems(List<Long> ids);
}
