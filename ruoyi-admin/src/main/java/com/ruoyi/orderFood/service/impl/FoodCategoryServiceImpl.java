package com.ruoyi.orderFood.service.impl;

import com.ruoyi.orderFood.entity.FoodCategory;
import com.ruoyi.orderFood.mapper.FoodCategoryMapper;
import com.ruoyi.orderFood.service.FoodCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class FoodCategoryServiceImpl implements FoodCategoryService {

    @Autowired
    private FoodCategoryMapper foodCategoryMapper;

    @Override
    public List<FoodCategory> getAllEnabledCategories() {
        return foodCategoryMapper.selectAllEnabled();
    }
}
