package com.ruoyi.orderFood.service.impl;

import com.ruoyi.orderFood.entity.ShoppingCart;
import com.ruoyi.orderFood.mapper.ShoppingCartMapper;
import com.ruoyi.orderFood.service.CartService;
import com.ruoyi.orderFood.vo.CartItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Service
public class CartServiceImpl implements CartService {

    @Autowired
    private ShoppingCartMapper shoppingCartMapper;

    @Override
    public List<CartItemVO> getUserCart(Long userId) {
        return shoppingCartMapper.selectByUserId(userId);
    }

    @Override
    @Transactional
    public boolean addToCart(ShoppingCart cart) {
        // 检查商品是否已在购物车中
        ShoppingCart existingCart = shoppingCartMapper.selectByUserIdFoodIdAndSpecId(
                cart.getUserId(),
                cart.getGoodsId(),
                cart.getCombinationId()
        );

        if (existingCart != null) {
            // 已存在，更新数量
            int newQuantity = existingCart.getQuantity() + cart.getQuantity();
            return shoppingCartMapper.updateQuantityAndRemark(
                    existingCart.getId(),
                    newQuantity
            ) > 0;
        } else {
            // 不存在，新增
            return shoppingCartMapper.insert(cart) > 0;
        }
    }

    @Override
    public boolean updateCartItem(Long id, Integer quantity, String remark) {
        return shoppingCartMapper.updateQuantityAndRemark(id, quantity) > 0;
    }

    @Override
    public boolean deleteCartItem(Long id) {
        return shoppingCartMapper.deleteById(id) > 0;
    }

    @Override
    public boolean batchDeleteCartItems(List<Long> ids) {
        return shoppingCartMapper.deleteByIds(ids) > 0;
    }
}
