package com.ruoyi.orderFood.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.orderFood.entity.FoodItem;
import com.ruoyi.orderFood.entity.FoodSpec;
import com.ruoyi.orderFood.mapper.FoodItemMapper;
import com.ruoyi.orderFood.mapper.FoodSpecMapper;
import com.ruoyi.orderFood.service.FoodService;
import com.ruoyi.orderFood.vo.GoodsVO;
import com.ruoyi.orderFood.vo.SpecVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FoodServiceImpl implements FoodService {

    @Autowired
    private FoodItemMapper foodItemsMapper;

    @Autowired
    private FoodSpecMapper foodSpecMapper;

    @Autowired
    private ObjectMapper objectMapper;


  @Override
  public List<GoodsVO> getGoodsByCategoryId(Long categoryId) {
    List<FoodItem> foodItems = foodItemsMapper.selectByCategoryId(categoryId);
    return convertToGoodsVOList(foodItems);
  }

  @Override
  public List<GoodsVO> getAllGoods() {
    List<FoodItem> foodItems = foodItemsMapper.selectAll();
    return convertToGoodsVOList(foodItems);
  }
  // 转换为VO列表
  private List<GoodsVO> convertToGoodsVOList(List<FoodItem> foodItems) {
    return foodItems.stream()
      .map(this::convertToGoodsVO)
      .collect(Collectors.toList());
  }
  // 转换为单个VO
  private GoodsVO convertToGoodsVO(FoodItem foodItem) {
    GoodsVO goodsVO = new GoodsVO();
    goodsVO.setId(foodItem.getId());
    goodsVO.setName(foodItem.getFoodName());
    goodsVO.setDesc(foodItem.getShortDesc());
    goodsVO.setFullDesc(foodItem.getFullDesc());
    goodsVO.setPrice(foodItem.getPrice());
    goodsVO.setImage(foodItem.getImageUrl());
    goodsVO.setCategoryId(foodItem.getCategoryId());
    goodsVO.setIngredients(foodItem.getIngredients());
    goodsVO.setSuggestions(foodItem.getSuggestions());

    // 处理标签
    if (foodItem.getTags() != null && !foodItem.getTags().isEmpty()) {
      goodsVO.setType(Arrays.asList(foodItem.getTags().split("，")));
    } else {
      goodsVO.setType(new ArrayList<>());
    }

    // 处理营养信息JSON
    try {
      if (foodItem.getNutritionInfo() != null && !foodItem.getNutritionInfo().isEmpty()) {
        Map<String, Object> nutritionMap = objectMapper.readValue(
          foodItem.getNutritionInfo(),
          Map.class
        );
        goodsVO.setNutrition(nutritionMap);
      }
    } catch (JsonProcessingException e) {
      // 处理JSON解析错误
      e.printStackTrace();
    }

    // 处理规格
    List<FoodSpec> specs = foodSpecMapper.selectByFoodId(foodItem.getId());
    if (specs != null && !specs.isEmpty()) {
      List<SpecVO> specVOs = specs.stream()
        .map(spec -> {
          SpecVO specVO = new SpecVO();
          specVO.setId(spec.getId());
          specVO.setName(spec.getSpecName());
          specVO.setPrice(spec.getSpecPrice());
          specVO.setImageUrl(spec.getImageUrl());
          return specVO;
        })
        .collect(Collectors.toList());
      goodsVO.setSpecs(specVOs);
    } else {
      goodsVO.setSpecs(new ArrayList<>());
    }

    return goodsVO;
  }
}
