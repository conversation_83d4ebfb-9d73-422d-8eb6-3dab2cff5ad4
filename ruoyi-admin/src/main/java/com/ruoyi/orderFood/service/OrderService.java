package com.ruoyi.orderFood.service;

import com.ruoyi.orderFood.vo.OrderVO;

import java.util.List;
import java.util.Map;

public interface OrderService {
    // 创建订单
    Map<String, Object> createOrder(Long userId, String deliveryAddress,
                                    String contactInfo, String remark, List<Long> cartItemIds);

    // 获取用户订单列表
    List<OrderVO> getUserOrders(Long userId);

    // 获取订单详情
    OrderVO getOrderDetail(Long orderId);

    // 支付订单
    boolean payOrder(Long orderId);
}
