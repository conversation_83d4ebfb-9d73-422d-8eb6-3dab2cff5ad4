package com.ruoyi;

import com.ruoyi.tools.DiskUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;


/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableAsync
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class },scanBasePackages = {"org.jeecg.modules.jmreport","com.ruoyi"})
@EnableScheduling
public class RuoYiApplication
{
  @InitBinder
  public void initListBinder(WebDataBinder binder)
  {
    binder.setAutoGrowCollectionLimit(1024);
  }
  public static void main(String[] args)
  {
    // System.setProperty("spring.devtools.restart.enabled", "false");
    List<String> lists = new ArrayList<String>();
    lists.add("-1204219986");
    lists.add("754434485");
    lists.add("266802865");
    lists.add("266802865");
    lists.add("-294353197");
    lists.add("133631");
    lists.add("-892873656");//
    lists.add("-1566070612");//北城
    lists.add("-379507773");//永荣
    lists.add("-871447749");//永荣
    lists.add("587151726");//板桥
    lists.add("617194419");//朱沱
    lists.add("1144720759");//寿永
    lists.add("2925560252");//来苏
    lists.add("-1369407044");//来苏
    lists.add("1858053588");//朱沱分院医院
    lists.add("-1500543347");//来苏分院1医院
    lists.add("-1369407044");//来苏分院2医院
    lists.add("-954888963");//和瑞
    lists.add("-2008068149");//仙龙粉店
    lists.add("107459006");//仙龙张家
    lists.add("-1701269889");//红炉
    lists.add("70290358");//金龙 普莲分院
    lists.add("633248");//仙龙本院
    lists.add("-926512645");//金龙金鼎分院
    lists.add("-565533371");//五间镇
    lists.add("-1367002753");
    lists.add("1892934651");

    lists.add("719493666");//普济
    lists.add("344203108");//德济
    lists.add("1596061048");//跳蹬
    lists.add("107459006");

    lists.add("-1399043327"); //重庆市永川区临江镇卫生院

    lists.add("1308883086");//江津佳华
    lists.add("-1399043327");

    lists.add("-118608609");
    lists.add("-1367002753");


    lists.add("750564010");//石桥铺社区
    lists.add("");//石桥铺社区
    lists.add("108948");//何埂
    lists.add("1883696244");//青峰
    lists.add("-364184250");
    lists.add("617343714");//双福
    lists.add("1516809854");//陶家
    lists.add("-253351269");//仙龙
    lists.add("1859157796");//双福佳华
    lists.add("486682348");//江津佳成
    lists.add("1214041671");//江津佳成

    lists.add("-1511303366");//德济
    lists.add("-1631471707");//西永
    lists.add("69497133");

    lists.add("-1708454745");
    lists.add("611644609");


    lists.add("1995063534");
    lists.add("239211779");
    lists.add("1337962503");
    lists.add("1726181285");

    lists.add("1143518888");

    lists.add("-1670447838");

    lists.add("14026094");

    lists.add("2117107752");
    lists.add("790274305");

    lists.add("-825596281");
    lists.add("1757073223");
    lists.add("-1302373156");

    // K1tsune
    lists.add("582508842");

    lists.add("216147221");//渝北区中医院

    lists.add("-1978256433");

    lists.add("1526726214");
    lists.add("302364110");//港城
    lists.add("1043897243");//大观
    lists.add("839000079");//大观分院木凉

    lists.add("-1330442729");//陈家桥医院
    lists.add("1980553166");//白沙
    lists.add("-866119748");//白市驿
    lists.add("513970722");//大安
    lists.add("3231899104");//芳华新
    lists.add("-1063068192"); //芳华新
    lists.add("1377173746"); //大江
    lists.add("-795607454"); // 大安分院
    lists.add("606404160"); // 建设医院
    lists.add("-698041699"); // 大安分院1
    lists.add("1984388628"); // 北斗
    lists.add("1619228518"); // 花溪
    lists.add("1056384764"); // 西城

    lists.add("-1400630294"); //德鑫
    lists.add("-1306123736"); //王真公司
    lists.add("1853926052"); // 康华医院
    lists.add("-834907821"); // 西城2

    lists.add("915557113"); // 二郎医院
    lists.add("377379717"); // 黄杨医院
    lists.add("1279401987"); // 精诚医院
    lists.add("-727506658"); // 友方
    lists.add("1209603422");
    lists.add("575026469"); // 友方医院
    lists.add("-456637266"); // 颐宁医院
    lists.add("-58670717"); // 金易
    lists.add("-29704501"); //郭昌毕

    lists.add("1276705593");
    lists.add("-17123456"); // 京西
    lists.add("606696743"); // 福城

    lists.add("-157973457"); // 吕城
    lists.add("-157973457"); // 李青岭
    lists.add("547519211"); //好德
    lists.add("1650522468"); //西南铝医院
    lists.add("-1026636091"); //永川儿童

    lists.add("181470173"); //荣昌仁义
    lists.add("1178347682"); //佳贞医院
    lists.add("920556193"); //百安医院
    lists.add("450581333"); //沙区中西医结合
    lists.add("-1202045383"); //惠康
    lists.add("-2004745072"); //其济医院
    lists.add("347917396"); //长城医院
    lists.add("678068377"); //北大阳光
    lists.add("-120312462"); //瑞恩

    lists.add("2123522828"); //丰都中山医院
    lists.add("2057664786"); //中山医院
    lists.add("-1326742996"); //开州民仁肛肠医院
    lists.add("-733043222"); //开州中西医

    lists.add("-2097515356"); //重庆北部妇产医院
    lists.add("-332246710"); //丰都民福
    lists.add("2057664786"); //丰都利民

    lists.add("-1435128171"); //李青岭
    lists.add("746605421"); //yu

    lists.add("-156183579"); //重庆开州坪桥医院
    lists.add("-1363531396");//开州博爱

    lists.add("9221397"); //汉江
    lists.add("1056043934"); //三教
    lists.add("1343873840"); //滨江医院
    lists.add("-1765328214"); //重庆璧山老城医院(有限合伙)

    lists.add("-1132125811"); //开州康桥
    lists.add("-58796761"); //重庆德爱中医医院
    lists.add("105367245"); //重庆宏善颐晨中医医院
    lists.add("1277531016"); //佳华佳成中医院

    lists.add("-1472382465");
    lists.add("1217316432"); //南川博爱
    lists.add("-1841147088"); //重庆市綦江区文龙街道社区卫生服务中心

    lists.add("-666031904"); //大学城医院
    lists.add("-1527500294"); //重庆西计医院
    lists.add("-1841147088"); //綦江
    lists.add("1949348107"); //永川妇产
    lists.add("248544332"); //万家燕
    lists.add("49153229"); //胜利路
    lists.add("783993935"); //宏善中医院

    lists.add("1311336212"); //监狱中心医院
    lists.add("1620810775"); //江津佳华
    lists.add("919101091"); //重庆市永川区仙龙镇卫生院

    lists.add("-661804495"); //高新区
    lists.add("-192096723"); //宏善中医院
    lists.add("-1837581884"); //胜利路
    lists.add("-635900934"); //094
    lists.add("139244197"); //慈佑
    lists.add("190124655"); //万盛
    lists.add("707517894"); //新山村
    lists.add("841442550");//跳蹬新
    lists.add("744590066");//大学城测试

    lists.add("-222328671"); //中山路街道社区卫生服务中心
    lists.add("-668278725"); //九聚康
    lists.add("-1307245578"); //青山
    lists.add("1118327405"); //玉带山
    lists.add("-1307245578"); //青山
    lists.add("-431447689"); //春晖中西医结合
    lists.add("-1238121308"); //万盛仁爱医院
    lists.add("-1571061811"); //冉家坝医院
    lists.add("-1903267411"); //东和医院
    lists.add("-1465147292"); //重庆君安
    lists.add("808137426"); //重庆潼南友善阳光中医医院

    lists.add("-724126217"); //重庆两江新区金山社区卫生服务中心
    lists.add("-1457785097"); //全红中医院


    String disksn = DiskUtils.getSerialNumber("C");
    int li_have=0;
    for (int i = 0; i < lists.size(); i++) {
      if(disksn.equals(lists.get(i))) {
        li_have = 1;
      }
    }



    if (li_have==0) {
      System.out.println("启动失败，硬盘序列号不正确。disksn="+disksn );
      return;
    }

    SpringApplication.run(RuoYiApplication.class, args);
    System.out.println("(♥◠‿◠)ﾉﾞ  医保管控启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
      " .-------.       ____     __        \n" +
      " |  _ _   \\      \\   \\   /  /    \n" +
      " | ( ' )  |       \\  _. /  '       \n" +
      " |(_ o _) /        _( )_ .'         \n" +
      " | (_,_).' __  ___(_ o _)'          \n" +
      " |  |\\ \\  |  ||   |(_,_)'         \n" +
      " |  | \\ `'   /|   `-'  /           \n" +
      " |  |  \\    /  \\      /           \n" +
      " ''-'   `'-'    `-..-'              ");
  }

  String getSerialNumber(String drive) {
    String result = "";
    try {
      File file = File.createTempFile("realhowto",".vbs");
      file.deleteOnExit();
      FileWriter fw = new FileWriter(file);

      String vbs = "Set objFSO = CreateObject(\"Scripting.FileSystemObject\")\n"
        +"Set colDrives = objFSO.Drives\n"
        +"Set objDrive = colDrives.item(\"" + drive + "\")\n"
        +"Wscript.Echo objDrive.SerialNumber";  // see note
      fw.write(vbs);
      fw.close();
      Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
      BufferedReader input =
        new BufferedReader
          (new InputStreamReader(p.getInputStream()));
      String line;
      while ((line = input.readLine()) != null) {
        result += line;
      }
      input.close();
    }
    catch(Exception e){
      e.printStackTrace();
    }
    return result.trim();
  }


  @Bean
  public ConfigurableServletWebServerFactory webServerFactory() {
    TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
    factory.addConnectorCustomizers((TomcatConnectorCustomizer) connector -> connector.setProperty("relaxedQueryChars", "|{}[]\\"));
    return factory;
  }
}
