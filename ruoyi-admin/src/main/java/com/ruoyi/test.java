package com.ruoyi;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.db.Entity;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.YbgkDz;
import info.debatty.java.stringsimilarity.JaroWinkler;
import org.dom4j.*;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class test {
  public static void main(String[] args) throws ParseException {
//    setYbProInfo();

//    Entity entity = new Entity();
//    entity.put("hilistPricUplmtAmt",null);
//    System.out.println(entity.getBigDecimal("hilistPricUplmtAmt"));
//
    getEncryptedExpiryDate();
    String s = decryptExpiryDate("lloxA1oqQWlHaSYcUNs6vfTtC3cQz7Q5Si3kbWLHrVw=");
    System.out.println(s + " ::铭文");

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date date1 = sdf.parse("2025-08-31 17:23:35");
    Date date2 = sdf.parse("2025-08-31 23:39:29");
    boolean sameDay = DateUtil.isSameDay(date1, date2);
    System.out.println(sameDay);

//    JaroWinkler jaroWinkler = new JaroWinkler();
//    double similarityScore = jaroWinkler.similarity("慢性肾衰竭", "慢性肾衰竭拟行血液净化治疗的临床路径");
//    System.out.println("The Jaro-Winkler similarity score is: " + similarityScore);

//    System.out.println(parseZLBHResponse(xmlResponse, "DRG_ZDXX_DELETE"));
  }

  private final static String PROINFO_LINK = "https://ggfwpz.ylbzj.cq.gov.cn/hsa-pss-pw/web/pw/terms/queryHerbByPage";
  private final static String PROPRICE_LINK = "https://ggfwpz.ylbzj.cq.gov.cn/hsa-pss-pw/web/pw/terms/queryDetilInfo";
  private final static String HCPROINFO_LINK = "https://ggfwpz.ylbzj.cq.gov.cn/hsa-pss-pw/web/pw/terms/queryMedicalPage";
  private final static String CYPROINFO_LINK = "https://ggfwpz.ylbzj.cq.gov.cn/hsa-pss-pw/web/pw/terms/queryHerbByPage";

  private final static String xmlResponse = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
    "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n" +
    "    <soap:Body>\n" +
    "        <DRG_ZDXX_DELETEResponse xmlns=\"http://tempuri.org/\">\n" +
    "            <DRG_ZDXX_DELETEResult>1</DRG_ZDXX_DELETEResult>\n" +
    "            <Out_返回状态>1</Out_返回状态>\n" +
    "        </DRG_ZDXX_DELETEResponse>\n" +
    "    </soap:Body>\n" +
    "</soap:Envelope>";

  public static String parseZLBHResponse(String res, String serviceName) {
    try {
      Document document = DocumentHelper.parseText(res);

      // 获取soap命名空间
      Element envelope = document.getRootElement();
      Namespace soapNs = envelope.getNamespace();
      Element body = envelope.element("Body");

      // 获取tempuri命名空间的响应元素
      Element response = body.element(new QName(serviceName + "Response",
        Namespace.get("http://tempuri.org/")));

      if (response == null) {
        throw new RuntimeException("Response element not found");
      }

      return response.elementText("Out_返回状态");


    } catch (Exception e) {
      throw new RuntimeException("Failed to parse SOAP response", e);
    }
  }

  /**
   * 同步ybgk_dz项目信息
   * @return
   */
  public static Integer setYbProInfo() {

    RestTemplate restTemplate = new RestTemplate();
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);

    YbgkDz ybgkDz = new YbgkDz();


    setInfo(ybgkDz, restTemplate, headers);
    return 1;
  }

  private static void setInfo(YbgkDz ybgkDz, RestTemplate restTemplate, HttpHeaders headers) {
    Map<String, Object> map = new HashMap<>();
    map.put("medListCodg", "T000100030");
    map.put("pageNum", "1");
    map.put("pageSize", "10");


    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(map, headers);

    JSONObject jsonObject = getInfo(restTemplate, requestEntity, CYPROINFO_LINK);
    System.out.println(jsonObject);
    if ("0".equals(jsonObject.getStr("code"))) {
      JSONObject jsonObject1 = jsonObject.getJSONObject("data");
      JSONArray dataJSONArray = jsonObject1.getJSONArray("data");
      if (dataJSONArray.size() == 0) {
        return;
      }
      JSONObject data = dataJSONArray.getJSONObject(0);
      System.out.println(data);
      ybgkDz.setYbcd(data.getStr("prodentpName"));
      ybgkDz.setYbdw(data.getStr("minPacunt"));
      ybgkDz.setYbname(data.getStr("regName"));
      ybgkDz.setYbgg(data.getStr("regSpec"));
      ybgkDz.setYbjx(data.getStr("regDosform"));
      ybgkDz.setFydj(data.getStr("chrgitmLv"));
    } else {
      return;
    }

    JSONObject priceJsonObject = getInfo(restTemplate, requestEntity, PROPRICE_LINK);
    if ("0".equals(priceJsonObject.getStr("code"))) {
      JSONObject data = priceJsonObject.getJSONObject("data");
      System.out.println(data);
      ybgkDz.setYbprice(data.getBigDecimal("hilistPricUplmtAmt"));
    }

  }

  private static JSONObject getInfo(RestTemplate restTemplate, HttpEntity<?> requestEntity, String url) {
    ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, JSONObject.class);
    JSONObject jsonObject = responseEntity.getBody();
    return jsonObject;
  }

  public static void getEncryptedExpiryDate(){
    String encrypt = SecureUtil.aes("@wdnmdS*&1983ddP"
        .getBytes(StandardCharsets.UTF_8))
      .encryptBase64("2025-08-31 23:00:00");
    System.out.println(encrypt + "   :::密文");
  }

  public static String decryptExpiryDate(String str){
    String decryptStr = SecureUtil.aes("@wdnmdS*&1983ddP"
        .getBytes(StandardCharsets.UTF_8))
      .decryptStr(str);
//    System.out.println(decryptStr + "   :::明文");
    return decryptStr;
  }
}
