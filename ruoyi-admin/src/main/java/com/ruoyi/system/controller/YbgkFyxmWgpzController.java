package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbgkFyxmWgpz;
import com.ruoyi.system.service.IYbgkFyxmWgpzService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 费用项目违规配置Controller
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RestController
@RequestMapping("/gksz/wgpz")
public class YbgkFyxmWgpzController extends BaseController {
  @Autowired
  private IYbgkFyxmWgpzService ybgkFyxmWgpzService;

  /**
   * 查询费用项目违规配置列表
   */
  @PreAuthorize("@ss.hasPermi('gksz:wgpz:list')")
  @GetMapping("/list")
  public TableDataInfo list(YbgkFyxmWgpz ybgkFyxmWgpz) {
    startPage();
    List<YbgkFyxmWgpz> list = ybgkFyxmWgpzService.selectYbgkFyxmWgpzList(ybgkFyxmWgpz);
    return getDataTable(list);
  }

  /**
   * 导出费用项目违规配置列表
   */
  @PreAuthorize("@ss.hasPermi('gksz:wgpz:export')")
  @Log(title = "费用项目违规配置", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, YbgkFyxmWgpz ybgkFyxmWgpz) {
    List<YbgkFyxmWgpz> list = ybgkFyxmWgpzService.selectYbgkFyxmWgpzList(ybgkFyxmWgpz);
    ExcelUtil<YbgkFyxmWgpz> util = new ExcelUtil<YbgkFyxmWgpz>(YbgkFyxmWgpz.class);
    util.exportExcel(response, list, "费用项目违规配置数据");
  }

  /**
   * 获取费用项目违规配置详细信息
   */
  @PreAuthorize("@ss.hasPermi('gksz:wgpz:query')")
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Long id) {
    return success(ybgkFyxmWgpzService.selectYbgkFyxmWgpzById(id));
  }

  /**
   * 新增费用项目违规配置
   */
  @PreAuthorize("@ss.hasPermi('gksz:wgpz:add')")
  @Log(title = "费用项目违规配置", businessType = BusinessType.INSERT)
  @PostMapping
  public Map<String, Object> add(@RequestBody YbgkFyxmWgpz ybgkFyxmWgpz) {
    YbgkFyxmWgpz fyxmWgpz = new YbgkFyxmWgpz();
    fyxmWgpz.setXmbm(ybgkFyxmWgpz.getXmbm());
    fyxmWgpz.setBindXmbm(ybgkFyxmWgpz.getBindXmbm());
    List<YbgkFyxmWgpz> ybgkFyxmWgpzs = ybgkFyxmWgpzService.selectYbgkFyxmWgpzList(fyxmWgpz);
    if (ybgkFyxmWgpzs.size() > 0) {
      Map<String, Object> map = new HashMap<>();
      map.put("code",500);
      map.put("msg","当前项目已存在");
      return map;
    }
    return toAjax(ybgkFyxmWgpzService.insertYbgkFyxmWgpz(ybgkFyxmWgpz));
  }

  /**
   * 修改费用项目违规配置
   */
  @PreAuthorize("@ss.hasPermi('gksz:wgpz:edit')")
  @Log(title = "费用项目违规配置", businessType = BusinessType.UPDATE)
  @PutMapping
  public Map<String, Object> edit(@RequestBody YbgkFyxmWgpz ybgkFyxmWgpz) {
    YbgkFyxmWgpz fyxmWgpz = new YbgkFyxmWgpz();
    fyxmWgpz.setXmbm(ybgkFyxmWgpz.getXmbm());
    fyxmWgpz.setBindXmbm(ybgkFyxmWgpz.getBindXmbm());
    fyxmWgpz.setPercentage(ybgkFyxmWgpz.getPercentage());
    List<YbgkFyxmWgpz> ybgkFyxmWgpzs = ybgkFyxmWgpzService.selectYbgkFyxmWgpzList(fyxmWgpz);
    if (ybgkFyxmWgpzs.size() > 0) {
      Map<String, Object> map = new HashMap<>();
      map.put("code",500);
      map.put("msg","当前项目已存在");
      return map;
    }
    return toAjax(ybgkFyxmWgpzService.updateYbgkFyxmWgpz(ybgkFyxmWgpz));
  }

  /**
   * 删除费用项目违规配置
   */
  @PreAuthorize("@ss.hasPermi('gksz:wgpz:remove')")
  @Log(title = "费用项目违规配置", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Long[] ids) {
    return toAjax(ybgkFyxmWgpzService.deleteYbgkFyxmWgpzByIds(ids));
  }
}
