package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbgkKkmx;
import com.ruoyi.system.service.IYbgkKkmxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 扣款明细Controller
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@RestController
@RequestMapping("/gksz/kkmx")
public class YbgkKkmxController extends BaseController {
  @Autowired
  private IYbgkKkmxService ybgkKkmxService;

  /**
   * 查询扣款明细列表
   */
  @GetMapping("/list")
  public TableDataInfo list(YbgkKkmx ybgkKkmx) {
    startPage();
    List<YbgkKkmx> list = ybgkKkmxService.selectYbgkKkmxList(ybgkKkmx);
    return getDataTable(list);
  }


  @GetMapping("/deptList")
  public TableDataInfo selectDeptList() {
    return getDataTable(ybgkKkmxService.selectDeptList());
  }

  @GetMapping("/doctorList")
  public TableDataInfo selectDoctorList() {
    return getDataTable(ybgkKkmxService.selectDoctorList());
  }

  @GetMapping("/doctorBydept")
  public TableDataInfo selectDoctorListByDept(@RequestParam("deptName") String deptName) {
    return getDataTable(ybgkKkmxService.selectDoctorListByDept(deptName));
  }


  @PostMapping("/appeal")
  public AjaxResult appeal(@RequestBody YbgkKkmx ybgkKkmx) {
    return  toAjax(ybgkKkmxService.appeal(ybgkKkmx));
  }

  @PostMapping("/send")
  public AjaxResult send(@RequestBody YbgkKkmx ybgkKkmx) {
    return  toAjax(ybgkKkmxService.send(ybgkKkmx));
  }

  /**
   * 导出扣款明细列表
   */
  @Log(title = "扣款明细", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, YbgkKkmx ybgkKkmx) {
    List<YbgkKkmx> list = ybgkKkmxService.selectYbgkKkmxList(ybgkKkmx);
    ExcelUtil<YbgkKkmx> util = new ExcelUtil<YbgkKkmx>(YbgkKkmx.class);
    util.exportExcel(response, list, "扣款明细数据");
  }

  /**
   * 获取扣款明细详细信息
   */
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Integer id) {
    return success(ybgkKkmxService.selectYbgkKkmxById(id));
  }

  /**
   * 新增扣款明细
   */
  @Log(title = "扣款明细", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody YbgkKkmx ybgkKkmx) {
    return toAjax(ybgkKkmxService.insertYbgkKkmx(ybgkKkmx));
  }

  /**
   * 修改扣款明细
   */
  @PreAuthorize("@ss.hasPermi('gksz:kkmx:edit')")
  @Log(title = "扣款明细", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody YbgkKkmx ybgkKkmx) {
    return toAjax(ybgkKkmxService.updateYbgkKkmx(ybgkKkmx));
  }

  /**
   * 删除扣款明细
   */
  @PreAuthorize("@ss.hasPermi('gksz:kkmx:remove')")
  @Log(title = "扣款明细", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Integer[] ids) {
    return toAjax(ybgkKkmxService.deleteYbgkKkmxByIds(ids));
  }


  @Log(title = "扣款明细导入", businessType = BusinessType.IMPORT)
  @PostMapping("/importData")
  @ResponseBody
  public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
    ExcelUtil<YbgkKkmx> util = new ExcelUtil<YbgkKkmx>(YbgkKkmx.class);
    List<YbgkKkmx> kkmxList = util.importExcel(file.getInputStream());
    String message = ybgkKkmxService.importKkmx(kkmxList, updateSupport);
    return AjaxResult.success(message);
  }


  @GetMapping("/importTemplate")
  public AjaxResult importTemplate() {
    ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
    return util.importTemplateExcel("扣款明细");
  }


}
