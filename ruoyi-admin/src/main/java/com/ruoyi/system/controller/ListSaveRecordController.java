package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ListSaveRecord;
import com.ruoyi.system.service.IListSaveRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 清单保存记录Controller
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@RestController
@RequestMapping("/system/record")
public class ListSaveRecordController extends BaseController
{
    @Autowired
    private IListSaveRecordService listSaveRecordService;

    @GetMapping("/getLastSubmitByPatient")
    public AjaxResult getLastSubmitByPatient(@RequestParam("brid") String brid,
                                                @RequestParam("zyid") String zyid) {

      ListSaveRecord record = listSaveRecordService.selectLastSubmitByPatient(brid, zyid);

      return success().put("record", record);
    }

    /**
     * 查询清单保存记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(ListSaveRecord listSaveRecord)
    {
        startPage();
        List<ListSaveRecord> list = listSaveRecordService.selectListSaveRecordList(listSaveRecord);
        return getDataTable(list);
    }

    /**
     * 导出清单保存记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "清单保存记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ListSaveRecord listSaveRecord)
    {
        List<ListSaveRecord> list = listSaveRecordService.selectListSaveRecordList(listSaveRecord);
        ExcelUtil<ListSaveRecord> util = new ExcelUtil<ListSaveRecord>(ListSaveRecord.class);
        util.exportExcel(response, list, "清单保存记录数据");
    }

    /**
     * 获取清单保存记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(listSaveRecordService.selectListSaveRecordById(id));
    }

    /**
     * 新增清单保存记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "清单保存记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ListSaveRecord listSaveRecord)
    {
        return toAjax(listSaveRecordService.insertListSaveRecord(listSaveRecord));
    }

    /**
     * 修改清单保存记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "清单保存记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ListSaveRecord listSaveRecord)
    {
        return toAjax(listSaveRecordService.updateListSaveRecord(listSaveRecord));
    }

    /**
     * 删除清单保存记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "清单保存记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(listSaveRecordService.deleteListSaveRecordByIds(ids));
    }
}
