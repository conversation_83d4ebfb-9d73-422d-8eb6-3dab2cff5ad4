package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.IYbgkWgjlService;
import com.ruoyi.system.service.IYbjkJklogService;
import com.ruoyi.system.service.IYbjkOptionService;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 医保管控记录信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@RestController
@RequestMapping("/gksz/jklog")
public class YbjkJklogController extends BaseController {
  @Autowired
  private IYbjkJklogService ybjkJklogService;
  @Autowired
  private IYbgkWgjlService ybgkWgjlService;
  @Autowired
  private IYbjkOptionService ybjkOptionService;

  /**
   * 查询医保管控记录信息列表
   */
  @GetMapping("/list")
  public TableDataInfo list(YbjkJklog ybjkJklog) {
    startPage();
    List<YbjkJklog> list = ybjkJklogService.selectYbjkJklogList(ybjkJklog);
    return getDataTable(list);
  }

  @GetMapping("/list2")
  public TableDataInfo list2(YbgkWgjl ybjkJklog) {

    String fysh_ip = ybjkOptionService.selectYbjkOptionByCCode("fysh_ip").getcValue();
    String fysh_dk = ybjkOptionService.selectYbjkOptionByCCode("fysh_dk").getcValue();

    startPage();
    OkHttpClient okHttpClient = new OkHttpClient.Builder()
      .connectTimeout(40, TimeUnit.SECONDS)
      .readTimeout(40, TimeUnit.SECONDS)
      .writeTimeout(40, TimeUnit.SECONDS)
      .build();


    String transaction = "http://localhost:8096/cysh/cysh1&jzh=" + ybjkJklog.getJzh();
    Response response = null;
    String result = null;
    String errorData = null;
    try {
      Request request = new Request.Builder()
        .url(transaction)
        .build();

      System.out.println("交易地址:" + transaction);
      response = okHttpClient.newCall(request).execute();
      result = response.body().string();
    } catch (IOException e) {
      e.printStackTrace();
      errorData = String.valueOf(e);
      System.out.println(errorData);
      List<String> list = new ArrayList<>();
      list.add(errorData);
      return getDataTable(list);
    }

    System.out.println(result);


    List<String> resultList = new ArrayList<>();
    resultList.add(result);

//        List<YbjkJklog> jklogList = ybjkJklogService.selectYbjkJklogList(ybjkJklog);

    YbgkWgjl ybgkWgjl = new YbgkWgjl();
    ybgkWgjl.setJzh(ybjkJklog.getJzh());

    List<YbgkWgjl> jklogList = ybgkWgjlService.selectYbgkWgjlListByHospital(ybgkWgjl);


    List<List> list = new ArrayList<>();
    list.add(jklogList);
//        list.add(resultList);

    return getDataTable(list);
  }

  /**
   * 导出医保管控记录信息列表
   */
  @Log(title = "医保管控记录信息", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, YbjkJklog ybjkJklog) {
    List<YbjkJklog> list = ybjkJklogService.selectYbjkJklogList(ybjkJklog);
    ExcelUtil<YbjkJklog> util = new ExcelUtil<YbjkJklog>(YbjkJklog.class);
    util.exportExcel(response, list, "医保管控记录信息数据");
  }

  /**
   * 获取医保管控记录信息详细信息
   */
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") String id) {
    return success(ybjkJklogService.selectYbjkJklogById(id));
  }

  /**
   * 新增医保管控记录信息
   */
  @Log(title = "医保管控记录信息", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody YbjkJklog ybjkJklog) {
    return toAjax(ybjkJklogService.insertYbjkJklog(ybjkJklog));
  }

  /**
   * 修改医保管控记录信息
   */
  @Log(title = "医保管控记录信息", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody YbjkJklog ybjkJklog) {
    return toAjax(ybjkJklogService.updateYbjkJklog(ybjkJklog));
  }

  /**
   * 删除医保管控记录信息
   */
  @Log(title = "医保管控记录信息", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable String[] ids) {
    return toAjax(ybjkJklogService.deleteYbjkJklogByIds(ids));
  }

  /**
   * 查询个人诊断记录
   */
  @PostMapping("/zdjl")
  @ResponseBody
  public TableDataInfo zdjl(String jzh) {
//        startPage();
    List<Zdxx> zdxxes = ybjkJklogService.selectYbjkJklogjzjiById(jzh);
    return getDataTable(zdxxes);
  }

  /**
   * 查询个人病例记录
   */
  @PostMapping("/bljl")
  @ResponseBody
  public TableDataInfo bljl(String jzh) {
//        startPage();
    List<Blxx> bljl = ybjkJklogService.selectYbjkJklogbljlById(jzh);
    return getDataTable(bljl);
  }


  /**
   * 查询个人费用信息
   */
  @RequestMapping("/fyxx")
  public TableDataInfo fyxx(Fyxx fyxx) {
    startPage();
    if (fyxx.getXmmc() == null) {
      fyxx.setXmmc("");
    }
    List<Fyxx> fyxxList = ybjkJklogService.selectYbjkJklogfyxxById(fyxx);
    if (fyxxList.isEmpty()) {
      return fyxxCy(fyxx);
    }
    return getDataTable(fyxxList);
  }

  @RequestMapping("/fyxxCy")
  public TableDataInfo fyxxCy(Fyxx fyxx) {
    startPage();
    if (fyxx.getXmmc() == null) {
      fyxx.setXmmc("");
    }
    List<Fyxx> fyxxList = ybjkJklogService.selectYbjkJklogFyxxCy(fyxx);
    return getDataTable(fyxxList);
  }

  @RequestMapping("/fyxxByks")
  public TableDataInfo fyxxByks(Fyxx fyxx) {
    startPage();
    if (fyxx.getXmmc() == null) {
      fyxx.setXmmc("");
    }
    List<Fyxx> fyxxList = ybjkJklogService.selectYbjkJklogfyxxByKs(fyxx);
    return getDataTable(fyxxList);
  }


  /**
   * 查询个人病人基本信息
   */
  @PostMapping("/brxx")
  @ResponseBody
  public TableDataInfo brxx(String jzh) {
    List<Brxx> brxx = ybjkJklogService.selectYbjkJklogbrxxById(jzh);
    return getDataTable(brxx);
  }

}
