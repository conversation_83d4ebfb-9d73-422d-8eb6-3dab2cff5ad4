package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Icd9ybdy;
import com.ruoyi.system.service.IIcd9ybdyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * icd9ybdyController
 * 
 * <AUTHOR>
 * @date 2024-10-15
 */
@RestController
@RequestMapping("/system/icd9ybdy")
public class Icd9ybdyController extends BaseController
{
    @Autowired
    private IIcd9ybdyService icd9ybdyService;

    /**
     * 查询icd9ybdy列表
     */
    @PreAuthorize("@ss.hasPermi('system:icd9ybdy:list')")
    @GetMapping("/list")
    public TableDataInfo list(Icd9ybdy icd9ybdy)
    {
        startPage();
        List<Icd9ybdy> list = icd9ybdyService.selectIcd9ybdyList(icd9ybdy);
        return getDataTable(list);
    }

    /**
     * 导出icd9ybdy列表
     */
    @PreAuthorize("@ss.hasPermi('system:icd9ybdy:export')")
    @Log(title = "icd9ybdy", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Icd9ybdy icd9ybdy)
    {
        List<Icd9ybdy> list = icd9ybdyService.selectIcd9ybdyList(icd9ybdy);
        ExcelUtil<Icd9ybdy> util = new ExcelUtil<Icd9ybdy>(Icd9ybdy.class);
        util.exportExcel(response, list, "icd9ybdy数据");
    }

    /**
     * 获取icd9ybdy详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:icd9ybdy:query')")
    @GetMapping(value = "/{bm}")
    public AjaxResult getInfo(@PathVariable("bm") String bm)
    {
        return success(icd9ybdyService.selectIcd9ybdyByBm(bm));
    }

    /**
     * 新增icd9ybdy
     */
    @PreAuthorize("@ss.hasPermi('system:icd9ybdy:add')")
    @Log(title = "icd9ybdy", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Icd9ybdy icd9ybdy)
    {
        return toAjax(icd9ybdyService.insertIcd9ybdy(icd9ybdy));
    }

    /**
     * 修改icd9ybdy
     */
    @PreAuthorize("@ss.hasPermi('system:icd9ybdy:edit')")
    @Log(title = "icd9ybdy", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Icd9ybdy icd9ybdy)
    {
        return toAjax(icd9ybdyService.updateIcd9ybdy(icd9ybdy));
    }

    /**
     * 删除icd9ybdy
     */
    @PreAuthorize("@ss.hasPermi('system:icd9ybdy:remove')")
    @Log(title = "icd9ybdy", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bms}")
    public AjaxResult remove(@PathVariable String[] bms)
    {
        return toAjax(icd9ybdyService.deleteIcd9ybdyByBms(bms));
    }
}
