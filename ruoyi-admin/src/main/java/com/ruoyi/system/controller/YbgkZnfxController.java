package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbgkZnfx;
import com.ruoyi.system.service.IYbgkZnfxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 医保智能分析Controller
 *
 * <AUTHOR>
 * @date 2025-05-30
 */
@RestController
@RequestMapping("/system/znfx")
public class YbgkZnfxController extends BaseController
{
    @Autowired
    private IYbgkZnfxService ybgkZnfxService;

    /**
     * 查询医保智能分析列表
     */
    @PreAuthorize("@ss.hasPermi('system:znfx:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbgkZnfx ybgkZnfx)
    {
        startPage();
        List<YbgkZnfx> list = ybgkZnfxService.selectYbgkZnfxList(ybgkZnfx);
        return getDataTable(list);
    }

    @Anonymous
    @GetMapping("/fx")
    public AjaxResult znfx(@RequestParam String brid, @RequestParam String zyid) {

      ybgkZnfxService.znfx(brid, zyid);
      return success();
    }

    /**
     * 导出医保智能分析列表
     */
    @PreAuthorize("@ss.hasPermi('system:znfx:export')")
    @Log(title = "医保智能分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbgkZnfx ybgkZnfx)
    {
        List<YbgkZnfx> list = ybgkZnfxService.selectYbgkZnfxList(ybgkZnfx);
        ExcelUtil<YbgkZnfx> util = new ExcelUtil<YbgkZnfx>(YbgkZnfx.class);
        util.exportExcel(response, list, "医保智能分析数据");
    }

    /**
     * 获取医保智能分析详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:znfx:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ybgkZnfxService.selectYbgkZnfxById(id));
    }

    /**
     * 新增医保智能分析
     */
    @PreAuthorize("@ss.hasPermi('system:znfx:add')")
    @Log(title = "医保智能分析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbgkZnfx ybgkZnfx)
    {
        return toAjax(ybgkZnfxService.insertYbgkZnfx(ybgkZnfx));
    }

    /**
     * 修改医保智能分析
     */
    @PreAuthorize("@ss.hasPermi('system:znfx:edit')")
    @Log(title = "医保智能分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbgkZnfx ybgkZnfx)
    {
        return toAjax(ybgkZnfxService.updateYbgkZnfx(ybgkZnfx));
    }

    /**
     * 删除医保智能分析
     */
    @PreAuthorize("@ss.hasPermi('system:znfx:remove')")
    @Log(title = "医保智能分析", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ybgkZnfxService.deleteYbgkZnfxByIds(ids));
    }
}
