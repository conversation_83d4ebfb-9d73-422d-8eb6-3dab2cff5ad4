package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.BaSyjlExt;
import com.ruoyi.system.service.IBaSyjlExtService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 病案首页扩展Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/system/syjlext")
public class BaSyjlExtController extends BaseController
{
    @Autowired
    private IBaSyjlExtService baSyjlExtService;

    /**
     * 查询病案首页扩展列表
     */
    @PreAuthorize("@ss.hasPermi('system:syjlext:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaSyjlExt baSyjlExt)
    {
        startPage();
        List<BaSyjlExt> list = baSyjlExtService.selectBaSyjlExtList(baSyjlExt);
        return getDataTable(list);
    }

    /**
     * 导出病案首页扩展列表
     */
    @PreAuthorize("@ss.hasPermi('system:syjlext:export')")
    @Log(title = "病案首页扩展", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaSyjlExt baSyjlExt)
    {
        List<BaSyjlExt> list = baSyjlExtService.selectBaSyjlExtList(baSyjlExt);
        ExcelUtil<BaSyjlExt> util = new ExcelUtil<BaSyjlExt>(BaSyjlExt.class);
        util.exportExcel(response, list, "病案首页扩展数据");
    }

    /**
     * 获取病案首页扩展详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:syjlext:query')")
    @GetMapping(value = "/{brbs}")
    public AjaxResult getInfo(@PathVariable("brbs") String brbs)
    {
        return success(baSyjlExtService.selectBaSyjlExtByBrbs(brbs));
    }

    /**
     * 新增病案首页扩展
     */
    @PreAuthorize("@ss.hasPermi('system:syjlext:add')")
    @Log(title = "病案首页扩展", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaSyjlExt baSyjlExt)
    {
        return toAjax(baSyjlExtService.insertBaSyjlExt(baSyjlExt));
    }

    /**
     * 修改病案首页扩展
     */
    @PreAuthorize("@ss.hasPermi('system:syjlext:edit')")
    @Log(title = "病案首页扩展", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaSyjlExt baSyjlExt)
    {
        return toAjax(baSyjlExtService.updateBaSyjlExt(baSyjlExt));
    }

    /**
     * 删除病案首页扩展
     */
    @PreAuthorize("@ss.hasPermi('system:syjlext:remove')")
    @Log(title = "病案首页扩展", businessType = BusinessType.DELETE)
	@DeleteMapping("/{brbss}")
    public AjaxResult remove(@PathVariable String[] brbss)
    {
        return toAjax(baSyjlExtService.deleteBaSyjlExtByBrbss(brbss));
    }
}
