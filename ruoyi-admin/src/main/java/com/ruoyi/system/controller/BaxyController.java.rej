diff a/ruoyi-admin/src/main/java/com/ruoyi/system/controller/BaxyController.java b/ruoyi-admin/src/main/java/com/ruoyi/system/controller/BaxyController.java	(rejected hunks)
@@ -948,6 +948,37 @@
     }
 
   }
+  
+  
+  public String getMidString(String input,String startStr,String endStr) {
+//	  String input = "诊断有多个类目不同的S03和S13，应增加T03.0[累及头部伴有颈部的脱位、扭伤和劳损]";
+//      String startStr = "应增加";
+//      String endStr = "[";
+	  String subString = null;
+      // 找到开始字符串的位置
+	  
+	  if(input==null||startStr==null||endStr==null) {
+		  return "";
+	  }
+	  
+      int startIndex = input.indexOf(startStr) + startStr.length();
+      
+      // 确保开始字符串存在并且查找结束字符串的位置
+      if (startIndex != -1) {
+          int endIndex = input.indexOf(endStr, startIndex);
+          
+          // 确保结束字符串存在
+          if (endIndex != -1) {
+              subString = input.substring(startIndex, endIndex);
+              System.out.println("找到的内容: " + subString);
+          } else {
+              System.out.println("未找到结束字符串 '['");
+          }
+      } else {
+          System.out.println("未找到开始字符串 '应增加'");
+      }
+      return subString;
+  }
 
   public boolean equalStr(String str1, String str2) {
     if (str1 == null || str2 == null) {
