package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.DrgZxbxmx;
import com.ruoyi.system.domain.Drgfzmx;
import com.ruoyi.system.service.IDrgZxbxmxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * DRG月度信息明细Controller
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@RestController
@RequestMapping("/tjfx/zxbxmx")
public class DrgZxbxmxController extends BaseController {
  @Autowired
  private IDrgZxbxmxService drgZxbxmxService;

  /**
   * 查询DRG月度信息明细列表
   */
  @PreAuthorize("@ss.hasPermi('tjfx:zxbxmx:list')")
  @GetMapping("/list")
  public TableDataInfo list(DrgZxbxmx drgZxbxmx) {
    startPage();
    List<DrgZxbxmx> list = drgZxbxmxService.selectDrgZxbxmxList(drgZxbxmx);
    return getDataTable(list);
  }

  @Log(title = "DRG月度信息明细导入", businessType = BusinessType.IMPORT)
  @PostMapping("/importData")
  @ResponseBody
  public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
    ExcelUtil<DrgZxbxmx> util = new ExcelUtil<DrgZxbxmx>(DrgZxbxmx.class);
    List<DrgZxbxmx> drgZxbxmxList = util.importExcel(file.getInputStream());
    String message = drgZxbxmxService.importDrgFzData(drgZxbxmxList, updateSupport);
    return AjaxResult.success(message);
  }
}
