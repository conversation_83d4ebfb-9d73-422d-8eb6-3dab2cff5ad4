package com.ruoyi.system.controller;

import java.util.*;
import java.util.stream.Collectors;

public class DrgFzqCQ20xfzGroupMatcher {
    // 存储所有DRG规则
    private static final Map<String, List<DRGRule>> drgRules = new HashMap<>();

    static {
        // 初始化规则数据
        initializeRules();
    }

    public static void main(String[] args) {
        // 示例输入
        String drgPrefix = "JJ1"; // DRG前三位
        List<String> diagnoses = Arrays.asList("C43.500", "J35.200"); // 诊断列表（主诊断在前）
        List<String> procedures = Arrays.asList("86.4x01", "04.2x05"); // 手术列表（主手术在前）

        // 获取匹配的DRG完整代码
        String matchedDRG = classifyDRG(drgPrefix, diagnoses, procedures);
        System.out.println("Matched DRG: " + (matchedDRG != null ? matchedDRG : ""));

        String drgPrefix3 = "RN2"; // RN2组
        List<String> diagnoses3 = Arrays.asList("C18.700"); // 结肠癌
        List<String> procedures3 = Arrays.asList(
            "99.2800x006", // 分子靶向治疗（主手术）
            "99.2801",     // 抗肿瘤免疫治疗
            "45.2301"      // 结肠镜检查
        );

        String matchedDRG3 = classifyDRG(drgPrefix3, diagnoses3, procedures3);
        System.out.println("匹配的DRG: " + (matchedDRG3 != null ? matchedDRG3 : ""));
        System.out.println("预期结果: RN2A");
        System.out.println();

    }

    public static String classifyDRG(String drgPrefix, List<String> diagnoses, List<String> procedures) {
        List<DRGRule> candidateRules = drgRules.getOrDefault(drgPrefix, Collections.emptyList());
        if (candidateRules.isEmpty()) {
            return null;
        }

        String mainDiagnosis = diagnoses.isEmpty() ? null : diagnoses.get(0);
        String mainProcedure = procedures.isEmpty() ? null : procedures.get(0);

        for (DRGRule rule : candidateRules) {
            if (rule.matches(mainDiagnosis, mainProcedure, diagnoses, procedures)) {
                return rule.getFullCode();
            }
        }
        return null;
    }

    private static void initializeRules() {

// 规则4: BD1C 脊柱脊髓手术，小手术
        addRule(new DRGRule("BD1C", null, Arrays.asList("04.2x05"), null, null));

        // 规则5: BR2A 其他脑缺血性疾病，脑梗死、颈动脉夹层
        addRule(new DRGRule("BR2A", Arrays.asList("I63.900", "I72.005"), null, null, null));

        // 规则6: BT2D 细菌性脑、脊髓和脑膜炎，结核性脑、脊髓和脑膜炎
        List<String> bt2dDiagnoses = Arrays.asList(
            "A17.000+G01*", "A17.000x001+G05.0*", "A17.000x005+G01*", "A17.000x006+G01*",
            "A17.001+G01*", "A17.100+G07*", "A17.100x001+G07*", "A17.800x007+G05.0*",
            "A17.801+G07*", "A17.802+G07*", "A17.803+G05.0*", "A17.804+G05.0*",
            "A17.805+G07*", "A17.806+G07*", "A17.807+G94.0*", "A17.900+G99.8*",
            "A17.900x001+G99.8*"
        );
        addRule(new DRGRule("BT2D", bt2dDiagnoses, null, null, null));

        // 规则7: BT3E 神经系统的其他感染，脑炎、脊髓炎和脑脊髓炎
        List<String> bt3eDiagnoses = Arrays.asList(
            "B45.101+G02.1*", "B45.102+G05.2*", "B49.x01+G02.1*", "G04.800x003",
            "G04.800x007", "G04.800x008", "G04.800x009", "G04.800x010", "G04.802",
            "G04.803", "G04.804", "G04.900x001", "G04.900x005", "G04.900x010",
            "G04.900x011", "G04.900x019", "G04.900x021", "G04.900x024", "G04.900x025",
            "G04.900x027", "G04.900x031", "G04.903", "G04.904", "G04.905", "G04.906",
            "G04.907", "G04.908", "G04.911", "G04.912", "G04.913", "G04.914",
            "G04.916", "G04.917", "G04.918", "G04.919", "G04.920", "G04.921", "G04.922"
        );
        addRule(new DRGRule("BT3E", bt3eDiagnoses, null, null, null));

        // 规则8: BX2D 脑神经/周围神经疾病，吉兰-巴雷综合征
        List<String> bx2dDiagnoses = Arrays.asList(
            "G61.000", "G61.000x003", "G61.000x004", "G61.000x005", "G61.000x006",
            "G61.001", "G61.002", "G61.003"
        );
        addRule(new DRGRule("BX2D", bx2dDiagnoses, null, null, null));

        // 规则9: CB4C 玻璃体、视网膜、脉络膜手术，药物注射
        addRule(new DRGRule("CB4C", null, Arrays.asList("14.7903"), null, null));

        // 规则10: CB4A 玻璃体、视网膜、脉络膜手术，玻璃体手术
        List<String> cb4aProcedures = Arrays.asList(
            "14.0200x001", "14.0200x002", "14.0202", "14.4903", "14.7300x001",
            "14.7401", "14.7900x001", "14.2900x002", "14.2900x004", "14.3901"
        );
        addRule(new DRGRule("CB4A", null, cb4aProcedures, null, null));

        // 规则11: CB7A 角膜、巩膜、结膜手术，穿透伤或撕裂伤
        List<String> cb7aDiagnoses = Arrays.asList(
            "S05.201", "S05.202", "S05.203", "S05.204", "S05.300", "S05.300x004",
            "S05.300x010", "S05.301", "S05.302", "S05.304", "S05.500x002", "S05.500x003",
            "S05.600x002", "S05.601", "S05.604"
        );
        
        List<String> cb7aop = Arrays.asList("10.0x00x001","10.1x00x001","10.2900x001","10.3101","10.3102","10.3200","10.3201","10.3300x002","10.3301","10.3302","10.4100x001","10.4101","10.4102","10.4200x001","10.4201","10.4202","10.4300x002","10.4400x001","10.4401","10.4402","10.4403","10.4900x001","10.4900x003","10.4901","10.4903","10.4904","10.5x01","10.6x00x001","10.6x00x002","10.9900x001","10.9901","11.0x00","11.1x01","11.3201","11.3202","11.3203","11.3204","11.3900x001","11.3901","11.4100x001","11.4200","11.4300","11.4901","11.4903","11.5100","11.5101","11.5200","11.5300x001","11.5900x001","11.5900x002","11.5901","11.7100x001","11.7100x002","11.7100x005","11.7100x007","11.7101","11.7102","11.7103","11.7104","11.7400x001","11.7500","11.7600","11.7900x001","11.7901","11.7902","11.9100x001","11.9200x001","11.9900x002","12.0200x003","12.3400","12.5900x003","12.6100","12.6200","12.6500x003","12.6500x004","12.6501","12.6502","12.6503","12.6600","12.6901","12.8100","12.8200x001","12.8300x002","12.8302","12.8303","12.8304","12.8400x002","12.8400x004","12.8401","12.8402","12.8403","12.8404","12.8500x002","12.8600x001","12.8700x005","12.8701","12.8702","12.8802","12.8900x001","12.8900x007","12.8901","12.8902","12.8903","12.8904","12.9201","12.9202","12.9203","12.9301");
        
     
        addRule(new DRGRule("CB7A", cb7aDiagnoses, cb7aop, null, "zd_and_op",
                null, null));

        // 规则12: CD1A 眼眶手术，开框手术
        List<String> cd1aProcedures = Arrays.asList(
            "16.0101", "16.0900x004", "16.0900x005", "16.0901", "16.0903", "16.0904",
            "16.1x01", "16.1x02", "16.7200", "16.8100x002", "16.8900x001", "16.8900x002",
            "16.8904", "16.9201", "16.9300x003", "16.9801", "76.7802", "76.7902",
            "76.7903", "76.9100x009"
        );
        addRule(new DRGRule("CD1A", null, cd1aProcedures, null, null));

        // 规则13: CD36 除眼眶外的外眼手术，不伴并发症或合并症，且年龄≤18岁
        // 注意：需要额外检查年龄和并发症条件
        addRule(new DRGRule("CD36", null, null, null, "no_complication_age18"));

        // 规则14: EX2D 百日咳及急性支气管炎，白喉、百日咳
        List<String> ex2dDiagnoses = Arrays.asList(
            "A36.201", "A37.000", "A37.100", "A37.800x001", "A37.900", "A37.900x003",
            "A37.900x004", "A37.901+J17.0*"
        );
        addRule(new DRGRule("EX2D", ex2dDiagnoses, null, null, null));

        // 规则15: FL1A 经皮瓣膜植入或修复术，经导管主动脉瓣植入术
        addRule(new DRGRule("FL1A", null, Arrays.asList("35.0501"), null, null));

        // 规则16: GB1A 食管大手术，全食管切除术、三切口食管部分切除术
        List<String> gb1aProcedures = Arrays.asList(
            "42.4102", "42.4200x001", "42.4200x002", "42.4201", "42.4202", "42.4203"
        );
        addRule(new DRGRule("GB1A", null, gb1aProcedures, null, null));

        // 规则17: GB3A 小肠、大肠手术，联合手术
        // 注意：需要额外检查多个手术组合
        List<String> gb3aMainProcedures = Arrays.asList("17.3100","17.3101","17.3200","17.3200x001","17.3200x002","17.3300","17.3300x002","17.3400","17.3401","17.3500",
                "17.3500x001","17.3600","17.3600x001","17.3900x002","17.3900x003","17.3900x004","Soave","17.3901","40.5101","40.5908",
                "40.5909","40.5910","40.5911","40.5912","45.0001","45.0002","45.5000","45.5100x001","45.5101","45.5201","45.6100","45.6100x001",
                "45.6200x003","45.6200x004","45.6300","45.6300x001","45.7100x001","45.7200x002","45.7200x004","45.7201","45.7202","45.7300x003",
                "45.7300x006","45.7300x007","45.7301","45.7302","45.7304","45.7400x003","45.7401","45.7500","45.7500x001","45.7501","45.7600x008",
                "45.7601","45.7900x001","45.7900x002","45.7900x003","45.7901","45.7902","45.8100","45.8100x001","45.8200","45.8300","45.9000",
                "45.9100x009","45.9100x010","45.9200","45.9300x012","45.9300x013","45.9300x014","45.9300x015","45.9301","45.9302","45.9303",
                "45.9304","45.9305","45.9306","45.9307","45.9308","45.9309","45.9310","45.9400x004","45.9400x009","45.9400x012","45.9400x016",
                "45.9400x017","45.9400x018","45.9401","45.9402","45.9403","45.9404","45.9405","45.9406","45.9407","45.9408","45.9500x001",
                "45.9501","45.9502","45.9503","45.9504","46.0200","46.0400x002","46.0401","46.0402","46.1000","46.1000x007","46.1100","46.110");
        
        

        List<String> gb3aOtherProcedures = Arrays.asList(
        	    "40.5910", "40.5912", "45.6200x005", "45.6202", "50.2200", "50.2205",
        	    "52.5902", "55.5101", "55.5103", "56.4100", "56.4105", "57.6x00", "57.6x06",
        	    "68.4100", "68.4901", "68.8x01", "99.2505"
        	);

        	addRule(new DRGRule("GB3A", null, gb3aMainProcedures, null, "double_procedure", 
        	                   gb3aOtherProcedures, null));

        // 规则18: GF3A 腹股沟疝及股疝手术，双侧手术
        List<String> gf3aProcedures = Arrays.asList(
            "17.2100x001", "17.2200x001", "17.2300x001", "17.2400x001", "53.1000",
            "53.1101", "53.1200x001", "53.1201", "53.1202", "53.1203", "53.1301",
            "53.1401", "53.1501", "53.1601", "53.1701", "53.3100x001", "53.3101", "53.3901"
        );
        addRule(new DRGRule("GF3A", null, gf3aProcedures, null, null));

        // 规则19: GT1D 炎症性肠病，克罗恩病
        List<String> gt1dDiagnoses = Arrays.asList(
            "K50.000", "K50.000x005", "K50.001", "K50.002", "K50.102", "K50.103",
            "K50.800", "K50.800x001", "K50.801", "K50.900"
        );
        addRule(new DRGRule("GT1D", gt1dDiagnoses, null, null, null));

        // 规则20: HB1A 胰、肝切除和/或分流手术，胰腺手术
        List<String> hb1aProcedures = Arrays.asList(
            "51.3300", "51.3301", "52.5100x001", "52.5101", "52.5102", "52.5103",
            "52.5104", "52.5201", "52.5202", "52.5203", "52.5204", "52.5205", "52.5206",
            "52.5300", "52.5301", "52.5901", "52.5902", "52.5903", "52.5904", "52.5905",
            "52.5906", "52.6x00", "52.6x00x003", "52.6x00x004", "52.6x01", "52.6x02",
            "52.6x03", "52.7x00", "52.7x00x003", "52.7x00x004", "52.7x01", "52.9601",
            "52.9602", "52.9603", "52.9604", "52.9605"
        );
        addRule(new DRGRule("HB1A", null, hb1aProcedures, null, null));

        // 规则21: HC1A 胆总管手术，十二指肠镜胆总管手术伴胆囊切除术
        // 注意：需要两个手术组合
        
//        addRule(new DRGRule("HC1A", null, null, null, "procedure_combo"));
     // 规则21: HC1A 胆总管手术，十二指肠镜胆总管手术伴胆囊切除术
        List<String> hc1aMainProcedures = Arrays.asList("51.8802"); // 十二指肠镜下胆总管切开取石术
        List<String> hc1aSecondaryProcedures = Arrays.asList(
            "51.2100", "51.2101", "51.2200", "51.2200x004", 
            "51.2201", "51.2300", "51.2301", "51.2400", "51.2401"
        ); // 胆囊切除术相关手术
        addRule(new DRGRule("HC1A", null, hc1aMainProcedures, null, "double_procedure", 
                           hc1aSecondaryProcedures, null));

        // 规则22: HJ1A 与肝、胆或胰腺疾病有关的其他手术，人工肝治疗
        addRule(new DRGRule("HJ1A", null, Arrays.asList("50.9200x001", "50.9201"), null, null));

        // 规则23: IB3C 与脊柱有关的其他手术，小手术
        addRule(new DRGRule("IB3C", null, Arrays.asList("03.9000x001"), null, null));

        // 规则24: IB3D 与脊柱有关的其他手术，脊柱感染和肿瘤
        List<String> ib3dDiagnoses = Arrays.asList(
            "A18.000x018+M49.0*", "A18.000x047+M49.0*", "A18.000x048+M49.0*", "A18.000x049+M49.0*",
            "A18.000x057+M49.0*", "A18.000x063+M90.0*", "A18.000x064+M90.0*", "A18.000x065+M90.0*",
            "A18.000x066+M49.0*", "A18.005+M49.0*", "A18.007+M49.0*", "A18.009+M49.0*",
            "A18.010+M49.0*", "A18.011+M49.0*", "A18.012+M49.0*", "A18.013+M49.0*",
            "A18.032+M49.0*", "A18.800x027+M63.0*", "A18.800x028+M63.0*", "B37.800x090",
            "C41.200x005", "C41.201", "C41.202", "C41.203", "C41.403", "C41.405",
            "C41.800x001", "C79.500x006", "C79.500x025", "C79.500x026", "C79.500x030",
            "C79.500x031", "C79.500x032", "C79.509", "D16.600x002", "D16.600x003",
            "D16.600x004", "D16.802", "D16.804", "D48.012", "D48.013", "D48.016",
            "D48.017", "M46.300", "M46.300x021", "M46.300x041", "M46.300x061", "M46.301",
            "M46.302", "M46.500x091", "M46.500x092", "M46.501", "M46.502", "M46.503",
            "M46.504", "M60.008", "M79.800x081", "T84.600x003", "T84.603", "T84.604", "T84.605"
        );
        
        List<String> ib3dopss = Arrays.asList( "03.0100x001","03.0200","03.0900x003","03.0900x004","03.0900x005","03.0900x006","03.0900x009","03.0900x010","03.0900x014","03.0900x016","03.0900x019","03.0900x021","03.0900x022","03.0900x025","03.0900x026","03.0900x027","03.0900x028","03.0901","03.0902","03.0903","03.0904","03.0905","03.0906","03.0907","03.0908","03.0909","03.0910","03.0911","03.0912","03.0913","03.0914","03.0915","03.1x00x001","03.1x01","03.2100x001","03.2900x005","03.2901","03.2902","03.2903","03.3100x001","03.3201","03.3202","03.4x00x001","03.4x00x002","03.4x00x007","03.4x00x008","03.4x00x009","03.4x01","03.4x03","03.4x04","03.4x05","03.4x06","03.4x07","03.5100x003","03.5300x001","03.5301","03.5302","03.5304","03.5305","03.5900x005","03.5901","03.5902","03.5903","03.5905","03.6x00x008","03.6x01","03.6x02","03.6x03","03.7101","03.8x01","03.9000x001","03.9202","03.9600","03.9900x003","03.9900x004","54.4x00x047","77.0904","77.1900x004","77.1904","77.2900x004","77.2904","77.3908","77.6900x032","77.6900x039","77.6900x055","77.6900x056","77.6900x059","77.6900x060","77.6900x061","77.6900x062","77.6900x068","77.6900x069","77.6904","77.6905","77.6906","77.7900x005","77.8900x008","77.8900x013","77.8905","77.8906","77.8907","77.8908","77.8909","77.9900x");
        
        addRule(new DRGRule("IB3D", ib3dDiagnoses, ib3dopss, null, "zd_and_op",
                null, null));

        // 规则25: IC3A 关节修复、重建手术
        List<String> ic3aProcedures = Arrays.asList(
            "81.2300x002", "81.2300x004", "81.2300x005", "81.4501", "81.4502", "81.4503",
            "81.4504", "81.4505", "81.4700x012", "81.4700x014", "81.4700x016", "81.4700x018",
            "81.4700x019", "81.4900x004", "81.4900x005", "81.4900x006", "81.8300x003",
            "81.8500x006", "81.8500x007", "81.8500x008", "81.9300x009", "81.9400x007",
            "81.9600x003", "81.9600x020", "81.9600x021", "81.9600x022", "81.9600x023",
            "81.9600x028", "81.9600x029", "81.9600x032"
        );
        addRule(new DRGRule("IC3A", null, ic3aProcedures, null, null));

        // 规则26: IF1A 骨科固定装置去除/修正术，脊柱内固定装置去除术
        addRule(new DRGRule("IF1A", null, Arrays.asList("78.6907"), null, null));

        List<String> jj1aProcedures = Arrays.asList(
        "C43.500","C43.502","C43.503","C43.504","C43.505","C43.506","C43.600","C43.600x002","C43.601","C43.602","C43.603","C43.604","C43.605","C43.606","C43.700x001","C43.701","C43.702","C43.703","C43.704","C43.705","C43.706","C43.707","C43.800","C43.900","C43.900x003","C43.901","C44.500","C44.501","C44.502","C44.503","C44.504","C44.505","C44.506","C44.509","C44.600","C44.601","C44.602","C44.603","C44.604","C44.605","C44.606","C44.700","C44.701","C44.702","C44.703","C44.704","C44.705","C44.706","C44.707","C44.800","C44.900","C46.000","C79.200","C79.200x001","C79.200x005","C79.200x006","C79.204","C79.205","D03.500","D03.600","D03.600x002","D03.601","D03.602","D03.700x001","D03.700x002","D03.701","D03.800","D03.900","D03.900x002","D04.500","D04.600x001","D04.601","D04.700x001","D04.701","D04.800","D04.900x001","D48.500x002","D48.500x003","D48.500x004","D48.500x005","D48.500x006","D48.500x007","D48.500x008","D48.500x009","D48.500x010","D48.500x011","D48.500x012","L85.801"
        		);

        // 规则27: IT2D 慢性炎症性肌肉骨骼结缔组织疾病，川崎病
        addRule(new DRGRule("IT2D", Arrays.asList("M30.300", "M30.301"), null, null, null));

     // 规则27: IT2D 慢性炎症性肌肉骨骼结缔组织疾病，川崎病
        addRule(new DRGRule("JJ1A", Arrays.asList("C43.500","C43.502","C43.503","C43.504","C43.505","C43.506","C43.600","C43.600x002","C43.601",
        		"C43.602","C43.603","C43.604","C43.605","C43.606","C43.700x001","C43.701","C43.702","C43.703",
        		"C43.704","C43.705","C43.706","C43.707","C43.800","C43.900","C43.900x003","C43.901","C44.500",
        		"C44.501","C44.502","C44.503","C44.504","C44.505","C44.506","C44.509","C44.600","C44.601","C44.602",
        		"C44.603","C44.604","C44.605","C44.606","C44.700","C44.701","C44.702","C44.703","C44.704","C44.705",
        		"C44.706","C44.707","C44.800","C44.900","C46.000","C79.200","C79.200x001","C79.200x005","C79.200x006",
        		"C79.204","C79.205","D03.500","D03.600","D03.600x002","D03.601","D03.602","D03.700x001","D03.700x002",
        		"D03.701","D03.800","D03.900","D03.900x002","D04.500","D04.600x001","D04.601","D04.700x001","D04.701",
        		"D04.800","D04.900x001","D48.500x002","D48.500x003","D48.500x004","D48.500x005","D48.500x006","D48.500x007",
        		"D48.500x008","D48.500x009","D48.500x010","D48.500x011","D48.500x012","L85.801"),
        		Arrays.asList("86.4x01","86.4x02","86.4x03"), null, null));


        // 规则29: LA2C 膀胱肿瘤手术，经尿道手术
        addRule(new DRGRule("LA2C", null, Arrays.asList(
            "57.4900x001", "57.4901", "57.4902", "57.4903", "57.4904"
        ), null, null));

        // 规则30: LC1C 输尿管手术，经尿道输尿管支架置入术
        addRule(new DRGRule("LC1C", null, Arrays.asList("59.8x03"), null, null));

        // 规则31: LE1A 尿道手术，成形术
        List<String> le1aProcedures = Arrays.asList(
            "58.4600x001", "58.4600x002", "58.4600x003", "58.4600x004", "58.4600x005",
            "58.6x03", "59.5x01", "59.7903", "59.7904"
        );
        addRule(new DRGRule("LE1A", null, le1aProcedures, null, null));

        // 规则32: MA1A 男性生殖器官恶性肿瘤手术，前列腺癌根治术
        addRule(new DRGRule("MA1A", null, Arrays.asList("60.5x02"), null, null));

        // 规则33: NA1A 女性生殖器官恶性肿瘤的广泛切除手术，联合手术
        // 注意：需要多个手术组合
        List<String> na1aProcedures = Arrays.asList(
            "17.3200x002", "17.3300", "17.3400", "17.3500", "17.3600", "34.2700x001",
            "41.5x00", "41.5x01", "45.6201", "45.6208", "45.7200x002", "45.7300x007",
            "45.7400x003", "45.7500", "45.7600x008", "45.9000", "46.1000", "46.1000x007",
            "46.3904", "48.6301", "48.6302", "50.2200", "52.5201", "52.5204", "56.4100",
            "56.4105", "56.7400", "56.7402", "57.6x00", "57.6x06", "99.2505"
        );
        addRule(new DRGRule("NA1A", null, na1aProcedures, null, "multiple_procedures"));

        // 规则34: NS1G 女性生殖系统感染，轻症治疗
        List<String> ns1gDiagnoses = Arrays.asList(
            "A54.003", "A54.004", "A54.005", "A56.002", "A56.003", "A56.004", "A56.101+N74.4*",
            "A59.002+N77.1*", "A60.000x003+N77.1*", "B37.300+N77.1*", "B37.300x002+N77.1*",
            "B37.301+N77.1*", "B37.302+N77.1*", "N70.103", "N71.101", "N71.902", "N72.x00x003",
            "N73.001", "N73.101", "N73.902", "N75.802", "N76.000", "N76.000x001", "N76.000x003",
            "N76.000x004", "N76.100x001", "N76.100x002", "N76.101", "N76.200", "N76.300x001", "N76.301"
        );
        addRule(new DRGRule("NS1G", ns1gDiagnoses, null, null, null));

        // 规则35: RK2B 淋巴瘤、多发骨髓瘤化学治疗和/或其他治疗
        // 注意：需要治疗组合
        List<String> rk2bzop = Arrays.asList(
                "99.2500x017", "99.2500x036", "99.2500x037", "99.2502", "99.2503", "99.2504", "99.2505", "99.2506"
            );
        List<String> rk2bqtop = Arrays.asList(
                "99.2800x004", "99.2800x005", "99.2801", "99.2800x006"
            );
     
        addRule(new DRGRule("RK2B", null, rk2bzop, null, "double_treatment_or", rk2bqtop, null));
        
        // 规则36: RL1A 恶性及增生性疾病放射治疗（体外照射），特殊放射治疗
        List<String> rl1aProcedures = Arrays.asList(
            "92.2400x004", "92.2400x007", "92.3000", "92.3900"
        );

        addRule(new DRGRule("RL1A", null, rl1aProcedures, null, null));


        // 规则4-36（原有规则保持不变）
        addRule(new DRGRule("BR2A", Arrays.asList("I63.900", "I72.005"), null, null, null));

        // 规则37: RL1B - 放射治疗+免疫/靶向/化疗联合治疗
        List<String> rl1bMainProcedures = Arrays.asList(
            "92.2400x003", "92.2400x004", "92.2400x005", "92.2400x006", "92.2400x007"
        );
        List<String> rl1bSecondaryProcedures = Arrays.asList(
            "99.2800x004", "99.2800x005", "99.2801",  // 免疫治疗
            "99.2800x006",                           // 靶向治疗
            "54.9701", "54.9702", "54.9703",         // 化疗
            "99.2502", "99.2503", "99.2504", "99.2505", "99.2506",
            "03.8x01", "99.2500x036", "99.2500x037", "99.2500x038", "99.2500x039"
        );
        addRule(new DRGRule("RL1B", null, rl1bMainProcedures, null, "double_treatment_or", rl1bSecondaryProcedures, null));

        // 规则38: RN1A - 化疗+免疫+靶向联合治疗
        List<String> rn1aChemoProcedures = Arrays.asList(
            "03.8x01", "54.9701", "54.9702", "54.9703", "99.2500x036",
            "99.2500x037", "99.2500x039", "99.2502", "99.2503", "99.2504",
            "99.2505", "99.2506"
        );
        List<String> rn1aImmunoProcedures = Arrays.asList(
            "99.2800x004", "99.2800x005", "99.2801"
        );
        List<String> rn1aTargetedProcedures = Arrays.asList("99.2800x006");
        addRule(new DRGRule("RN1A", null, rn1aChemoProcedures, null, "triple_treatment",
                           rn1aImmunoProcedures, rn1aTargetedProcedures));

        // 规则39: RN1B - 化疗+免疫联合治疗
        addRule(new DRGRule("RN1B", null, rn1aChemoProcedures, null, "double_treatment",
                           rn1aImmunoProcedures, null));

        // 规则40: RN1C - 化疗+靶向联合治疗
        addRule(new DRGRule("RN1C", null, rn1aChemoProcedures, null, "double_treatment",
                           rn1aTargetedProcedures, null));

        // 规则41: RN2A - 免疫+靶向联合治疗
        List<String> rn2aImmunoProcedures = Arrays.asList(
            "99.2800x003", "99.2800x004", "99.2800x005", "99.2801"
        );
        List<String> rn2aTargetedProcedures = Arrays.asList("99.2800x006");
        addRule(new DRGRule("RN2A", null, rn2aImmunoProcedures, null, "double_treatment_swap",
                           rn2aTargetedProcedures, null));

       // 规则42: TR1A 精神分裂症，伴电休克治疗
        List<String> electroshockProcedures = Arrays.asList("94.2600", "94.2700", "94.2702");
        addRule(new DRGRule("TR1A", null, electroshockProcedures, Arrays.asList("F20"), null));

        // 规则43: TS1A 心境障碍，伴电休克治疗
        addRule(new DRGRule("TS1A", null, electroshockProcedures, null, null));

        // 规则44: TU1A 儿童期精神发育障碍，伴电休克治疗
        addRule(new DRGRule("TU1A", null, electroshockProcedures, null, null));

        // 规则45: XT3A 其他影响健康状态的因素，干细胞采集
        List<String> xt3aDiagnoses = Arrays.asList(
            "Z51.400x001", "Z51.400x003", "Z51.401", "Z52.000", "Z52.001", "Z52.300x002"
        );
        addRule(new DRGRule("XT3A", xt3aDiagnoses, Arrays.asList("99.7901"), null, null));

        // 规则46: YR1A HIV相关疾病，伴严重合并症或并发症，多发性感染
        addRule(new DRGRule("YR1A", Arrays.asList("B20.700x001"), null, null, "severe_complication"));
    }

    private static void addRule(DRGRule rule) {
        String prefix = rule.getPrefix();
        drgRules.computeIfAbsent(prefix, k -> new ArrayList<>()).add(rule);
    }

    static class DRGRule {
        private final String fullCode;
        private final Set<String> includedDiagnoses;
        private final Set<String> includedProcedures;
        private final Set<String> requiredDiagnosisPrefixes;
        private final String severity;
        private final Set<String> secondaryProcedures;
        private final Set<String> tertiaryProcedures;

        // 新构造函数支持多组操作
        public DRGRule(String fullCode, List<String> includedDiagnoses, List<String> includedProcedures,
                      List<String> requiredDiagnosisPrefixes, String severity,
                      List<String> secondaryProcedures, List<String> tertiaryProcedures) {
            this.fullCode = fullCode;
            this.includedDiagnoses = toSet(includedDiagnoses);
            this.includedProcedures = toSet(includedProcedures);
            this.requiredDiagnosisPrefixes = toSet(requiredDiagnosisPrefixes);
            this.severity = severity;
            this.secondaryProcedures = toSet(secondaryProcedures);
            this.tertiaryProcedures = toSet(tertiaryProcedures);
        }

        // 兼容旧规则的构造函数
        public DRGRule(String fullCode, List<String> includedDiagnoses, List<String> includedProcedures,
                      List<String> requiredDiagnosisPrefixes, String severity) {
            this(fullCode, includedDiagnoses, includedProcedures, requiredDiagnosisPrefixes, severity, null, null);
        }

        private Set<String> toSet(List<String> list) {
            return list != null ? new HashSet<>(list) : Collections.emptySet();
        }

        public String getFullCode() {
            return fullCode;
        }

        public String getPrefix() {
            return fullCode.substring(0, 3);
        }
        
     // 新增方法：检查双重手术匹配（用于HC1A）
        private boolean checkDoubleProcedure(String mainProcedure, List<String> allProcedures) {
        	  if (mainProcedure == null) return false;
              if (!includedProcedures.contains(mainProcedure)) return false;
              
           // 2. 创建排除主手术的副本列表
              List<String> otherProcedures = new ArrayList<>(allProcedures);
              if (mainProcedure != null) {
                  otherProcedures.remove(mainProcedure);
              }
              
              // 3. 检查其他手术中是否存在次要手术
              boolean hasSecondaryProcedure = otherProcedures.stream()
                  .anyMatch(proc -> secondaryProcedures.contains(proc));

              return hasSecondaryProcedure;
//              return allProcedures.stream().anyMatch(secondaryProcedures::contains);
        }

        public boolean matches(String mainDiagnosis, String mainProcedure,
                              List<String> allDiagnoses, List<String> allProcedures) {
            // 1. 检查诊断匹配
            boolean diagnosisMatch = checkDiagnosisMatch(mainDiagnosis);

            // 2. 根据严重程度类型执行特殊匹配逻辑
            if (severity != null) {
                switch (severity) {
                    case "zd_and_op":      // CB7A
                    return diagnosisMatch && checkopMatch(mainProcedure);
                    case "double_procedure":      // HC1A
                        return diagnosisMatch && checkDoubleProcedure(mainProcedure, allProcedures);
                    case "double_treatment_or":    // RL1B
                        return diagnosisMatch && checkDoubleTreatmentOr(mainProcedure, allProcedures);
                    case "triple_treatment":       // RN1A
                        return diagnosisMatch && checkTripleTreatment(allProcedures);
                    case "double_treatment":       // RN1B, RN1C
                        return diagnosisMatch && checkDoubleTreatment(allProcedures);
                    case "double_treatment_swap":  // RN2A
                        return diagnosisMatch && checkDoubleTreatmentSwap(mainProcedure, allProcedures);
                    case "severe_complication":    // 其他严重并发症规则
                        return diagnosisMatch && hasSevereComplication(allDiagnoses);
                    // 其他特殊条件可以在这里添加
                }
            }

            // 3. 默认匹配逻辑（原有逻辑）
            return diagnosisMatch && checkDefaultMatch(mainProcedure);
        }

        private boolean checkDiagnosisMatch(String mainDiagnosis) {
            if (mainDiagnosis == null) {
                return includedDiagnoses.isEmpty() && requiredDiagnosisPrefixes.isEmpty();
            }

            if (!includedDiagnoses.isEmpty() && includedDiagnoses.contains(mainDiagnosis)) {
                return true;
            }

            if (!requiredDiagnosisPrefixes.isEmpty()) {
                return requiredDiagnosisPrefixes.stream().anyMatch(prefix ->
                    mainDiagnosis.startsWith(prefix));
            }
            return includedDiagnoses.isEmpty() && requiredDiagnosisPrefixes.isEmpty();
        }
        
        private boolean checkopMatch(String mainDiagnosis) {
            if (mainDiagnosis == null) {
                return includedProcedures.isEmpty() && requiredDiagnosisPrefixes.isEmpty();
            }
            if (!includedProcedures.isEmpty() && includedProcedures.contains(mainDiagnosis)) {
                return true;
            }
            return false;
        }

        private boolean checkDoubleTreatmentOr(String mainProcedure, List<String> allProcedures) {
            // RL1B: 主手术在第一组 AND 存在第二组手术
            if (mainProcedure == null) return false;
            if (!includedProcedures.contains(mainProcedure)) return false;

            return allProcedures.stream().anyMatch(secondaryProcedures::contains);
        }

        private boolean checkTripleTreatment(List<String> allProcedures) {
            // RN1A: 同时存在三组手术
            boolean hasFirst = allProcedures.stream().anyMatch(includedProcedures::contains);
            boolean hasSecond = allProcedures.stream().anyMatch(secondaryProcedures::contains);
            boolean hasThird = allProcedures.stream().anyMatch(tertiaryProcedures::contains);
            return hasFirst && hasSecond && hasThird;
        }

        private boolean checkDoubleTreatment(List<String> allProcedures) {
            // RN1B/RN1C: 同时存在两组手术
            boolean hasFirst = allProcedures.stream().anyMatch(includedProcedures::contains);
            boolean hasSecond = allProcedures.stream().anyMatch(secondaryProcedures::contains);
            return hasFirst && hasSecond;
        }

        private boolean checkDoubleTreatmentSwap(String mainProcedure, List<String> allProcedures) {
            // RN2A: 主手术在任一操作组 AND 两组操作同时存在
            if (mainProcedure == null) return false;

            boolean mainInFirst = includedProcedures.contains(mainProcedure);
            boolean mainInSecond = secondaryProcedures.contains(mainProcedure);
            if (!(mainInFirst || mainInSecond)) return false;

            // 确保两组操作都存在（主手术已经在其中一组）
            boolean hasFirst = mainInFirst || allProcedures.stream().anyMatch(includedProcedures::contains);
            boolean hasSecond = mainInSecond || allProcedures.stream().anyMatch(secondaryProcedures::contains);

            return hasFirst && hasSecond;
        }

        private boolean checkDefaultMatch(String mainProcedure) {
            // 默认匹配逻辑：检查主手术是否匹配
            if (mainProcedure == null) {
                return includedProcedures.isEmpty();
            }
            return includedProcedures.isEmpty() || includedProcedures.contains(mainProcedure);
        }

        private boolean hasSevereComplication(List<String> diagnoses) {
            // 实现严重并发症检查逻辑
            // 示例：检查是否存在特定诊断
            return diagnoses.stream().anyMatch(d -> d.startsWith("B20.700x001"));
        }
    }
}
