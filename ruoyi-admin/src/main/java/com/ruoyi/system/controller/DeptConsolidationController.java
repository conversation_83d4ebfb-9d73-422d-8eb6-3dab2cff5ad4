package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DeptConsolidation;
import com.ruoyi.system.service.IDeptConsolidationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 科室合并映射Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/system/deptConsolidation")
public class DeptConsolidationController extends BaseController
{
    @Autowired
    private IDeptConsolidationService deptConsolidationService;

    /**
     * 查询科室合并映射列表
     */
    @PreAuthorize("@ss.hasPermi('system:deptConsolidation:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeptConsolidation deptConsolidation)
    {
        startPage();
        List<DeptConsolidation> list = deptConsolidationService.selectDeptConsolidationList(deptConsolidation);
        return getDataTable(list);
    }

    /**
     * 导出科室合并映射列表
     */
    @PreAuthorize("@ss.hasPermi('system:deptConsolidation:export')")
    @Log(title = "科室合并映射", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeptConsolidation deptConsolidation)
    {
        List<DeptConsolidation> list = deptConsolidationService.selectDeptConsolidationList(deptConsolidation);
        ExcelUtil<DeptConsolidation> util = new ExcelUtil<DeptConsolidation>(DeptConsolidation.class);
        util.exportExcel(response, list, "科室合并映射数据");
    }

    /**
     * 获取科室合并映射详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:deptConsolidation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deptConsolidationService.selectDeptConsolidationById(id));
    }

    /**
     * 新增科室合并映射
     */
    @PreAuthorize("@ss.hasPermi('system:deptConsolidation:add')")
    @Log(title = "科室合并映射", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody DeptConsolidation deptConsolidation)
    {
        return toAjax(deptConsolidationService.insertDeptConsolidation(deptConsolidation));
    }

    /**
     * 修改科室合并映射
     */
    @PreAuthorize("@ss.hasPermi('system:deptConsolidation:edit')")
    @Log(title = "科室合并映射", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody DeptConsolidation deptConsolidation)
    {
        return toAjax(deptConsolidationService.updateDeptConsolidation(deptConsolidation));
    }

    /**
     * 删除科室合并映射
     */
    @PreAuthorize("@ss.hasPermi('system:deptConsolidation:remove')")
    @Log(title = "科室合并映射", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deptConsolidationService.deleteDeptConsolidationByIds(ids));
    }
}
