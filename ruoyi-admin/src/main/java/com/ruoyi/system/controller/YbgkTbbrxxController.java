package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbgkTbbrxx;
import com.ruoyi.system.service.IYbgkTbbrxxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 特病人员Controller
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/system/tbbrxx")
public class YbgkTbbrxxController extends BaseController
{
    @Autowired
    private IYbgkTbbrxxService ybgkTbbrxxService;

    /**
     * 查询特病人员列表
     */
    @PreAuthorize("@ss.hasPermi('system:tbbrxx:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbgkTbbrxx ybgkTbbrxx)
    {
        startPage();
        List<YbgkTbbrxx> list = ybgkTbbrxxService.selectYbgkTbbrxxList(ybgkTbbrxx);
        return getDataTable(list);
    }

    /**
     * 导出特病人员列表
     */
    @PreAuthorize("@ss.hasPermi('system:tbbrxx:export')")
    @Log(title = "特病人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbgkTbbrxx ybgkTbbrxx)
    {
        List<YbgkTbbrxx> list = ybgkTbbrxxService.selectYbgkTbbrxxList(ybgkTbbrxx);
        ExcelUtil<YbgkTbbrxx> util = new ExcelUtil<YbgkTbbrxx>(YbgkTbbrxx.class);
        util.exportExcel(response, list, "特病人员数据");
    }

    /**
     * 获取特病人员详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tbbrxx:query')")
    @GetMapping(value = "/{sfz}")
    public AjaxResult getInfo(@PathVariable("sfz") String sfz)
    {
        return success(ybgkTbbrxxService.selectYbgkTbbrxxBySfz(sfz));
    }

    /**
     * 新增特病人员
     */
    @PreAuthorize("@ss.hasPermi('system:tbbrxx:add')")
    @Log(title = "特病人员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbgkTbbrxx ybgkTbbrxx)
    {
        return toAjax(ybgkTbbrxxService.insertYbgkTbbrxx(ybgkTbbrxx));
    }

    /**
     * 修改特病人员
     */
    @PreAuthorize("@ss.hasPermi('system:tbbrxx:edit')")
    @Log(title = "特病人员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbgkTbbrxx ybgkTbbrxx)
    {
        return toAjax(ybgkTbbrxxService.updateYbgkTbbrxx(ybgkTbbrxx));
    }

    /**
     * 删除特病人员
     */
    @PreAuthorize("@ss.hasPermi('system:tbbrxx:remove')")
    @Log(title = "特病人员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sfzs}")
    public AjaxResult remove(@PathVariable String[] sfzs)
    {
        return toAjax(ybgkTbbrxxService.deleteYbgkTbbrxxBySfzs(sfzs));
    }
}
