package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SettleZdxx;
import com.ruoyi.system.service.ISettleZdxxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 清单保存操作诊断Controller
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/system/zdxx")
public class SettleZdxxController extends BaseController
{
    @Autowired
    private ISettleZdxxService settleZdxxService;

    /**
     * 查询清单保存操作诊断列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SettleZdxx settleZdxx)
    {
        startPage();
        List<SettleZdxx> list = settleZdxxService.selectSettleZdxxList(settleZdxx);
        return getDataTable(list);
    }

    /**
     * 导出清单保存操作诊断列表
     */
    @PreAuthorize("@ss.hasPermi('system:zdxx:export')")
    @Log(title = "清单保存操作诊断", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SettleZdxx settleZdxx)
    {
        List<SettleZdxx> list = settleZdxxService.selectSettleZdxxList(settleZdxx);
        ExcelUtil<SettleZdxx> util = new ExcelUtil<SettleZdxx>(SettleZdxx.class);
        util.exportExcel(response, list, "清单保存操作诊断数据");
    }

    /**
     * 获取清单保存操作诊断详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:zdxx:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(settleZdxxService.selectSettleZdxxById(id));
    }

    /**
     * 新增清单保存操作诊断
     */
    @PreAuthorize("@ss.hasPermi('system:zdxx:add')")
    @Log(title = "清单保存操作诊断", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SettleZdxx settleZdxx)
    {
        return toAjax(settleZdxxService.insertSettleZdxx(settleZdxx));
    }

    /**
     * 修改清单保存操作诊断
     */
    @PreAuthorize("@ss.hasPermi('system:zdxx:edit')")
    @Log(title = "清单保存操作诊断", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SettleZdxx settleZdxx)
    {
        return toAjax(settleZdxxService.updateSettleZdxx(settleZdxx));
    }

    /**
     * 删除清单保存操作诊断
     */
    @PreAuthorize("@ss.hasPermi('system:zdxx:remove')")
    @Log(title = "清单保存操作诊断", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(settleZdxxService.deleteSettleZdxxByIds(ids));
    }
}
