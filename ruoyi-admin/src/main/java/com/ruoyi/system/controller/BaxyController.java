package com.ruoyi.system.controller;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import com.ruoyi.tools.ChongqingDistrict;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.tools.IDCardUtil;

import cn.hutool.core.util.StrUtil;

@Anonymous
@RestController
@RequestMapping("/drg/baxy")
public class BaxyController extends BaseController {
  @Autowired
  private IBaSyjlService baSyjlService;
  @Autowired
  private IBaSsjlService baSsjlService;
  @Autowired
  private IBaBrzdxxService baBrzdxxService;
  @Autowired
  private IBBasyCheckBzService basyCheckBzService;
  @Autowired
  private IBaSyjlErrorService baSyjlErrorService;
  @Autowired
  private IBBasyCheckBzPfService basyCheckBzPfService;
  @Autowired
  private IFyxxService fyxxService;
  @Autowired
  private IYbgkWgjlService wgjlService;
  @Autowired
  private DrgFzqController drgFzqController;
  @Autowired
  private IYbjkOptionService ybjkOptionService;
  @Autowired
  private IBrxxSyncService brxxSyncService;
  @Autowired
  private ISsmlService ssmlService;
  @Autowired
  private IBzmlService bzmlService;
  @Autowired
  private IIcd10ybdyService icd10ybdyService;
  @Autowired
  private IBaSyjlExtService baSyjlExtService;

  private boolean isInit = false;

  private Integer bmpdws = 5;
  //是否质控适应症  默认--质控
  private String zkSyz = "1";
  //是否质控病理诊断  默认--质控
  private String zkBlzd = "1";
  //是否质控受伤原因  默认--质控
  private String zkSsyy = "1";

  private String yyname = "";

  private String use_special_rule = "";

  private List<BBasyCheckBz> notqtzd_list;
  private List<BBasyCheckBz> must_list;
  private List<BBasyCheckBz> notzzd_list;
  private List<BBasyCheckBz> notzss_list;
  private List<BBasyCheckBz> hb_list;
  private List<BBasyCheckBz> tip_list;
  private List<BBasyCheckBz> sstip_list;
  private List<BBasyCheckBz> zsstip_list;
  private List<BBasyCheckBz> zzdtip_list;
  private List<BBasyCheckBz> zzdmctip_list;
  private List<BBasyCheckBz> zdmctip_list;
  private List<BBasyCheckBz> rybq_list;
  private List<BBasyCheckBz> man_list;
  private List<BBasyCheckBz> woman_list;
  private List<BBasyCheckBz> sshb_list;

  private List<BBasyCheckBz> together_list;
  private List<BBasyCheckBz> nottogether_list;
  private List<BBasyCheckBz> zzdnottogether_list;
  private List<BBasyCheckBz> sstogether_list;

  private List<IcdStopUse> stopUseIcd10List;
  private List<IcdStopUse> stopUseIcd9List;

  //科室诊断判断
  private List<BBasyCheckBz> dept_list;
  //编码优先判断
  private List<BBasyCheckBz> priority_list;

  //病种目录
  private List<Bzml> bzmlList;
  private List<Ssml> ssmlList;
  private List<Icd10ybdy> icd10ybdyList;

  //Z37相关的诊断
  private static final List<String> Z37_CODES = Arrays.asList("Z37.0", "Z37.2", "Z37.3", "Z37.5", "Z37.6");
  //不能为儿科的诊断
  private static final List<String> NOT_PEDIATRICS_DIAG_CODES = Arrays.asList("O00", "O01", "O02", "O03", "O04", "O05", "O06", "O07", "O08", "O30", "O31", "O32", "O33", "O34", "O35", "O36", "O37", "O38", "O39",
    "O40", "O41", "O42", "O43", "O44", "O45", "O46", "O47", "O48", "O60", "O61", "O62", "O63", "O64", "O65", "O66", "O67", "O68", "O69", "O70", "O71", "O72", "O73", "O74", "O75",
    "O80", "O81", "O82", "O83", "O84");
  //分娩编码
  private static final List<String> CHILDBIRTH_DIAG_CODES = Arrays.asList("O80", "O81", "O82", "O83", "O84");
  //流产编码
  private static final List<String> ABORTION_DIAG_CODES = Arrays.asList("O00", "O01", "O02", "O03", "O04", "O05", "O06", "O07", "O08");
  //新生儿编码
  private static final List<String> NEONATE_DIAG_CODES = Arrays.asList("P10", "P11", "P12", "P13", "P14", "P15");
  //低风险编码
  private static final List<String> LOWER_RISK_DIAG = Arrays.asList("A18", "B00", "B02", "B08", "B18", "C73", "D13", "D16", "D17", "D18", "D24", "D25", "D27", "D36", "D50", "D69", "E05", "E07", "E10", "E11", "G40", "G45", "H02", "H1l", "H25", "H26", "H33", "H40", "H81", "H91", "I10", "I20", "I47", "I70", "I80", "I83", "I84", "I86", "I87", "J04", "J06", "J20", "J21", "J32", "J33", "J34", "J35", "J38", "J40", "J45", "K11", "K12", "K21", "K22", "K25", "K29", "K31", "K35", "K40", "K52", "K60", "K61", "K63", "K80", "L08", "M06", "M13", "M17", "M47", "M48", "M51", "M79", "M87", "N02", "N04", "N10", "N13", "N20", "N40", "N43", "N45", "N80", "N83,O00,O02,O20,O26,O35,O36,O42,O47,O60,O69,O80,O82,O99", "P23", "P59", "P91", "Q35", "R42", "R56", "S00", "S22", "S42", "S52", "S62", "S82", "S83", "T14", "T18", "Z34", "Z47", "Z48", "Z51");
  //输血编码
  private static final List<String> BLOOD_CODES = Arrays.asList("99.00", "99.01", "99.02", "99.03", "99.04", "99.05", "99.06", "99.07");
  //病理诊断与主要诊断的对应
  private static final Map<String, List<String>> PATHOLOGYDIAG_MAINDIAG = new HashMap<>();
  //特殊的几个病理诊断与主要诊断的对应
  private static final Map<List<String>, List<String>> SPECIAL_PATHOLOGYDIAG_MAINDIAG = new HashMap<>();


  static {
    PATHOLOGYDIAG_MAINDIAG.put("/0", Arrays.asList(
      "D10", "D11", "D12", "D13", "D14", "D15", "D16", "D17", "D18", "D19", "D20", "D21", "D22", "D23",
      "D24", "D25", "D26", "D27", "D28", "D29", "D30", "D31", "D32", "D33", "D34", "D35", "D36"
    ));
    PATHOLOGYDIAG_MAINDIAG.put("/1", Arrays.asList("D37", "D38", "D39", "D40", "D41", "D42", "D43", "D44", "D45", "D46", "D47", "D48"));
    PATHOLOGYDIAG_MAINDIAG.put("/2", Arrays.asList("D00", "D01", "D02", "D03", "D04", "D05", "D06", "D07", "D08", "D09"));
    PATHOLOGYDIAG_MAINDIAG.put("/3", Arrays.asList(
      "C00", "C01", "C02", "C03", "C04", "C05", "C06", "C07", "C08", "C09", "C10", "C11", "C12", "C13", "C14", "C15"
      , "C16", "C17", "C18", "C19", "C20", "C21", "C22", "C23", "C24", "C25", "C26", "C27", "C28", "C29", "C30", "C31"
      , "C32", "C33", "C34", "C35", "C36", "C37", "C38", "C39", "C40", "C41", "C42", "C43", "C44", "C45", "C46", "C47"
      , "C48", "C49", "C50", "C51", "C52", "C53", "C54", "C55", "C56", "C57", "C58", "C59", "C60", "C61", "C62", "C63"
      , "C64", "C65", "C66", "C67", "C68", "C69", "C70", "C71", "C72", "C73", "C74", "C75"
    ));
    PATHOLOGYDIAG_MAINDIAG.put("/6", Arrays.asList("C78", "C79"));


    SPECIAL_PATHOLOGYDIAG_MAINDIAG.put(Arrays.asList("M801/3", "M802/3", "M803/3", "M804/3", "M805/3", "M806/3", "M807/3", "M808/3"), Arrays.asList("C44", "C46.0", "C51", "C52", "C60", "C63.2"));
    SPECIAL_PATHOLOGYDIAG_MAINDIAG.put(Arrays.asList("M8050/0", "M8051/0", "M8052/0", "M8053/0", "M8060/0"), Arrays.asList("D23", "D10.0", "D12.9", "D28.0", "D28.1", "D29.0", "D29.4"));
  }

  /**
   * 所有类型的日期格式化
   */
  public final static List<DateTimeFormatter> FORMATTERS = Arrays.asList(
    DateTimeFormatter.ofPattern("yyyy-MM-dd"),
    DateTimeFormatter.ofPattern("yyyy.MM.dd"),
    DateTimeFormatter.ofPattern("yyyy/MM/dd"),
    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
    DateTimeFormatter.ofPattern("yyyy年MM月dd日")
  );


  @RequestMapping("/getbajyData")
  public Map<String, Object> getBajyPage(
    @RequestParam(value = "brbs", required = false) String brbs,
    @RequestParam(value = "brid", required = false) String brid,
    @RequestParam(value = "zyid", required = false) String zyid) {

    Map<String, Object> map = new HashMap<>();

    if (StringUtils.isBlank(brbs)) {
      if (StringUtils.isBlank(zyid) || StringUtils.isBlank(brid)) {
        map.put("msg", "请输入完整参数：brbs或brid+zyid");
        return map;
      } else {
        brbs = brid + "_" + zyid;
      }
    }

    try {
      brxxSyncService.basySync(brbs,null);
    } catch (Exception e) {
      logger.error(e.getMessage());
      map.put("msg", "病案同步失败！");
      return map;
    }

    BaSyjl brxx = new BaSyjl();
    brxx.setBrbs(StringUtils.isNotBlank(brbs) ? brbs : null);
    brxx.setBrid(StringUtils.isNotBlank(brid) ? brid : null);
    brxx.setZyid(StringUtils.isNotBlank(zyid) ? zyid : null);

    List<BaSyjl> baSyjls = baSyjlService.selectBaSyjlList(brxx);
    baSyjls.removeIf(Objects::isNull);

    if (baSyjls.size() > 0) {
      brxx = baSyjls.get(0);
    } else {
      map.put("msg", "不存在当前病案！");
      return map;
    }

    brxx.setCydateStr(formatDate(brxx.getCydate()));
    brxx.setRydateStr(formatDate(brxx.getRydate()));

    TableDataInfo checkResult = baxy(brxx, "");

    List<BaSyjlError> checkRecords = checkResult.getRows() != null ? (List<BaSyjlError>) checkResult.getRows() : new ArrayList<>();
    checkRecords.removeIf(Objects::isNull);

    List<BaSyjlError> tipRecords = checkRecords.stream().filter(r -> r.getCode().contains("tip")).collect(Collectors.toList());
    checkRecords.removeIf(r -> r.getCode().contains("tip"));

    map.put("checkRecords", checkRecords);
    map.put("tipRecords", tipRecords);
    map.put("brxx", brxx);

    return map;
  }


  private String formatDate(Date date) {
    if (date != null) {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      return simpleDateFormat.format(date);
    }
    return "";
  }

  @RequestMapping("/gxgz")
  public void gxgz() {
	  initCheckRules();
  }

  @RequestMapping("/baxy")
  public TableDataInfo baxy(BaSyjl baSyjl, String as_ban) {
    String ban = as_ban;

    if (!isInit) {
      initCheckRules();
    }

    List<BaSyjlError> baxyList = new ArrayList<>();

    String brbs = baSyjl.getBrbs();

    if (StringUtils.isNotBlank(brbs)) {
      baSyjl.setZyzt(null);
      baSyjl.setCydate(null);
      baSyjl.setCydateStart(null);
      baSyjl.setCydateEnd(null);
      baSyjl.setCykb(null);
    }

    List<BaSyjl> list = baSyjlService.selectBaSyjlList(baSyjl);
    BaSyjlExt baSyjlExt = baSyjlExtService.selectBaSyjlExtByBrbs(brbs);

    if (baSyjlExt == null) {
      baSyjlExt = new BaSyjlExt();
      baSyjlExt.setBrbs(brbs);
    }

    if (list.size() == 0) {
      return getDataTable(baxyList);
    }

    // 病案首页
    BaSyjl basy = list.get(0);

    String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

    String brid = basy.getBrid();
    String zyid = basy.getZyid();
    String bah = basy.getBah();
    String jlly = basy.getJlly();

    if (StringUtils.isBlank(brid)) {
      brid = basy.getBrid();
    }
    if (StringUtils.isBlank(zyid)) {
      zyid = basy.getZyid();
    }

    List<BaBrzdxx> baBrzdxxList;
    List<BaSsjl> baSsjlList;
    List<BaBrzdxx> ybzdList= new ArrayList<>();
    List<BaSsjl> ybSsjlList= new ArrayList<>();

    //处理性别
    String gender = ifNull(basy.getXb());
    basy.setXb("1".equals(gender) ? "男" : "2".equals(gender) ? "女" : gender);

    //统一日期格式为yyyy-MM-dd的LocalDate类型
    basy = dateFormatter(basy);

    //如果是notsave，就去医保诊断手术 -- 前面调用时临时赋值给了医保诊断手术
    if ("notsave".equals(ban)) {
      baBrzdxxList = baBrzdxxService.selectBaBrzdxxList(new BaBrzdxx(brid, zyid));
      baSsjlList = baSsjlService.selectBaSsjlList(new BaSsjl(brid, zyid));
      ybzdList = baBrzdxxService.selectBaBrzdxxList(new BaBrzdxx(brid, zyid));
      ybSsjlList = baSsjlService.selectBaSsjlList(new BaSsjl(brid, zyid));
    } else {
      //如果不是notsave，就取临床诊断手术
      baBrzdxxList = baBrzdxxService.selectSyZdxx(new BaBrzdxx(brid, zyid));
      baSsjlList = baSsjlService.selectSySsxx(new BaSsjl(brid, zyid));

      //校验医保码是否重复
      ybzdList = baBrzdxxService.selectBaBrzdxxList(new BaBrzdxx(brid, zyid));
      baxyList = checkYbDiagInfo(baxyList, ybzdList);
      ybSsjlList = baSsjlService.selectBaSsjlList(new BaSsjl(brid, zyid));
    }

    //检查诊断和手术信息
    baxyList = checkDiagInfo(baxyList, ybzdList, basy);
//    baxyList = checkDiagInfo(baxyList, baBrzdxxList, basy);

    baxyList = checkOperInfo(baxyList, baSsjlList, basy);

    //提前去除可能为空的情况，后续不需要再进行重复的判断
    baBrzdxxList.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getJbbm()) || StringUtils.isBlank(obj.getZdmc()));
    baSsjlList.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getSsbm()) || StringUtils.isBlank(obj.getSsmc()));

    //标识是否为新生儿
    int ls_xseflag = 0;

    //检查日期
    LocalDate rysjDate = basy.getRysjDate();
    LocalDate csrqDate = basy.getCsrqDate();
    LocalDate cysjDate = basy.getCysjDate();
    LocalDate zkrqDate = basy.getZkrqDate();
    String idCard = ifNull(basy.getSfzh());

    if (basy.getNl() != null) {
      //检查年龄相关内容
      baxyList = checkAgeContent(baxyList, basy, baBrzdxxList);
    }

    if (!"".equals(ifNull(basy.getCykb()))) {
      //检查科室限制代码
      baxyList = checkDeptLimitCode(baxyList, baBrzdxxList, basy.getCykb());
    }

    // 出院时间不应大于质控日期
//    if (zkrqDate != null && cysjDate != null) {
//      if (cysjDate.compareTo(zkrqDate) > 0) {
//        addError(baxyList, "", "出院时间不应大于质控日期", "0", "0", "zkrq", 0, 0, 0.5);
//      }
//    }

    // 检查医保诊断列表是否都存在于诊断目录
    if (bzmlList.size() > 33300) {

      for (BaBrzdxx item : ybzdList) {
        if(bzmlList.stream().noneMatch(bz ->  bz.getBzbm().equalsIgnoreCase(item.getJbbm()) && bz.getBzmc().equalsIgnoreCase(item.getZdmc()) )) {
          addError(baxyList, "", "医保编码或名称" + item.getJbbm()  + "[" + item.getZdmc() + "]" + "不存在于医保病种目录", "0", "0", "jbdm", 0, 0, null);
        }
      }
    }

    if (ssmlList.size() > 13000) {
      for (BaSsjl item : ybSsjlList) {
        if (ssmlList.stream().noneMatch(ss -> ss.getSsbm().equalsIgnoreCase(item.getSsbm()) && ss.getSsbm().equalsIgnoreCase(item.getSsbm()))) {
          addError(baxyList, "", "手术医保编码或名称" + item.getSsbm()  + "[" + item.getSsmc() + "]" + "不存在于医保目录", "0", "0", "ssjczbm1", 0, 0, null);
        }
      }
    }

    // 检查icd10医保扩展码
    for (BaBrzdxx zd : ybzdList) {
      if (zd == null || StringUtils.isBlank(zd.getJbbm()) || zd.getJbbm().length() > 7) {
        continue;
      }
      List<Icd10ybdy> filterList =
        icd10ybdyList.stream().filter(item ->
            item.getYbbzbm() != null &&
            !Objects.equals(zd.getJbbm(), item.getYbbzbm()) &&
            item.getYbbzbm().contains(zd.getJbbm()))
          .collect(Collectors.toList());
      if(!filterList.isEmpty()) {
        String zdTipStr = filterList.stream().map(item -> item.getYbbzmc() + "[" + item.getYbbzbm() + "]").collect(Collectors.joining(", "));
        addError(baxyList, "tip", "诊断中存在医保诊断" + zd.getZdmc() + "[" + zd.getJbbm() + "], 可以使用其扩展编码" + zdTipStr , "0", "0", "tip", 0, 0, null, 0, "", zd.getJbbm());
      }
    }

    // 实际住院天数
    if (cysjDate != null && rysjDate != null) {
      long days = rysjDate.until(cysjDate, ChronoUnit.DAYS);
      if (basy.getSjzyts() != null) {
        if (basy.getSjzyts() == days || (basy.getSjzyts() == 1 && days == 0)) {
        } else {
          addError(baxyList, "", "实际住院天数应该等于出院时间-入院时间", "0", "0", "sjzyts", 0, 0, null);
        }
      }
    }

    if (csrqDate != null && rysjDate != null) {
      long days = csrqDate.until(rysjDate, ChronoUnit.DAYS);
      // 新生儿入院体重漏填
      if (days <= 28) {
        if (basy.getXserytz() == null) {
          addError(baxyList, "must", "新生儿入院体重漏填", "0", "0", "xserytz", 0, 0, 4.0);
        }
        ls_xseflag = 1;
      }
      // 年龄不足1周岁的体重漏填
      if (days < 365) {
        if (basy.getXserytz() == null) {
          addError(baxyList, "must", "年龄不足1周岁的体重漏填", "0", "0", "xserytz", 0, 0, null);
        }
      }
    }

    //入院时间不应早于出生日期
    if (rysjDate != null && csrqDate != null && rysjDate.compareTo(csrqDate) < 0) {
      addError(baxyList, "", "入院时间不应早于出生日期", "0", "0", "rysj", 0, 0, null);
    }


    //处理身份证
    if (!idCard.isEmpty()) {
      if (!IDCardUtil.isValidIdCard(idCard)) {
        addError(baxyList, "", "身份证号不符合标准", "0", "0", "sfzh", 0, 0, null);
      } else {
//        LocalDate birthDate = IDCardUtil.parseBirthDate(idCard);
//        if (rysjDate != null && nl != null && Period.between(birthDate, rysjDate).getYears() != nl.intValue()) {
//          addError(baxyList, "", "年龄与身份证不符", "0", "0", "nl", 0, 0, null);
//        }

        if (csrqDate != null) {
          String csrqDateStr = convertToDateStr(csrqDate, DateTimeFormatter.BASIC_ISO_DATE);
          if (csrqDateStr != null && !csrqDateStr.equals(idCard.substring(6, 14))) {
            addError(baxyList, "", "出生日期与身份证不符", "0", "0", "csrq", 0, 0, null);
          }
        }

        gender = ifNull(basy.getXb());
        if ( !"".equals(gender) && !gender.equals(IDCardUtil.getGenderByIdCard(idCard))) {
          addError(baxyList, "", "性别与身份证不符", "0", "0", "xb", 0, 0, null);
        }
      }
    }

    // 主要诊断信息
    boolean haveMainDiag = true;
    if (baBrzdxxList.size() == 0) {
      haveMainDiag = false;
    }

    //检查诊断合并
    if (haveMainDiag && baBrzdxxList.size() > 1) {
      baxyList = checkDiagMerge(baxyList, baBrzdxxList, ls_xseflag);
    }

    //检查血型
    baxyList = checkBloodType(baxyList, basy, baSsjlList);

    //前面判断内容为 --- 禁止
    if (!"".equals(ban) && ban != null) {
      if ("notsave".equals(ban)) {
        Drgfz drgfz = new Drgfz();
        drgfz.setBrbs(basy.getBrbs());
        DrgFzResult drgFzResult = drgFzqController.fzq(drgfz);
        if (drgFzResult.getDrgbh().indexOf("000") > -1 || drgFzResult.getDrgbh().indexOf("QY") > -1) {
          addError(baxyList, "", "首页预分组为" + drgFzResult.getDrgbh(), "0", "0", "drgbh", 0, 0, 0.0);
        }
      }
    }

    // 当主要诊断存在时，进行以下判断
    if (haveMainDiag) {

      String mainDiagCode = baBrzdxxList.get(0).getJbbm();
      String mainDiagName = baBrzdxxList.get(0).getZdmc();
      String mainDiagRybq = baBrzdxxList.get(0).getRybq();

      //主诊断小数点[.]后亚目为[9]，应核对病历，分类到更明确的诊断
      if (mainDiagCode.contains(".9")) {
        addError(baxyList, "tip", "主诊断小数点[.]后亚目为[9]，应核对病历，分类到更明确的诊断", "0", "0", "tip", 0, 0, null);
      }

      //主诊断入院病情一般不能为[院内发病]4
      if ("4".equals(mainDiagRybq) || "院内发病".equals(mainDiagRybq)) {
        addError(baxyList, "", "主诊断入院病情一般不能为[院内发病]4", "0", "0", "tip", 0, 0, null);
      }


      // 不宜作为主要诊断
      for (int i = 0; i < notzzd_list.size(); i++) {
        if (equalStr(notzzd_list.get(i).getComments(), baBrzdxxList.get(0).getJbbm())) {
          addError(baxyList, "notzzd", notzzd_list.get(i).getTip(), "0", "1", "notzzd", 0, 0,
            null);
        }
      }

      // 不能作为其他诊断
      if (baBrzdxxList.size() > 1) {
        for (int i = 1; i < baBrzdxxList.size(); i++) {
          for (int i1 = 0; i1 < notqtzd_list.size(); i1++) {
            if (equalStr(notqtzd_list.get(i1).getComments(), baBrzdxxList.get(i).getJbbm())) {
              addError(baxyList, "notqtzd", notqtzd_list.get(i1).getTip(), "0", "1", "notqtzd",
                0, 0, null);
            }
          }
        }
      }

      //检查入院病情
      baxyList = checkDiagRybq(baxyList, baBrzdxxList);
      //诊断提示
      baxyList = checkDiagCode(baxyList, baBrzdxxList, ls_xseflag);
      //同时存在的诊断
      baxyList = checkTogeterDiag(baxyList, together_list, baBrzdxxList, "together");
      //主诊断编码提示
      baxyList = checkMainDiagOrOper(baxyList, zzdtip_list, mainDiagCode, "zzdtip", ls_xseflag);
      //主要诊断不能为后遗症
      baxyList = checkSequelae(baxyList, mainDiagName);
      //主诊断名称提示
      baxyList = checkMainDiagOrOper(baxyList, zzdmctip_list, mainDiagName, "zzdmctip", ls_xseflag);
      //诊断名称提示
      baxyList = checkDiagName(baxyList, baBrzdxxList);
      //存在多个诊断时的判断
      if (baBrzdxxList.size() > 1) {
        //不能同时存在的诊断的判断
        baxyList = checkTogeterDiag(baxyList, nottogether_list, baBrzdxxList, "nottogether");
        //检查不能与主要诊断同时存在的诊断
        baxyList = checkTogeterDiag(baxyList, zzdnottogether_list, baBrzdxxList, "zzdnottogether");
      }
      //检查死亡病例
      if ("死亡".equals(ifNull(basy.getLyfs()))||"5".equals(ifNull(basy.getLyfs()))) {
        baxyList = checkLowRiskDiag(baxyList, mainDiagCode);
      }

      // 检查肿瘤
      baxyList = checkTumour(baxyList, baBrzdxxList);
      //检查病理诊断
      baxyList = checkPathologyDiag(baxyList, basy, mainDiagCode);
      //检查优先编码
      baxyList = checkCodePriority(baxyList, baBrzdxxList);

      //检查限制女性的诊断
      if ("男".equals(basy.getXb())) {
        baxyList = checkLimitGenderDiag(baxyList, baBrzdxxList, woman_list);
      }
      //检查限制男性的诊断
      if ("女".equals(basy.getXb())) {
        baxyList = checkLimitGenderDiag(baxyList, baBrzdxxList, man_list);
      }

      //诊断Z37.1[单一死产]，不应填写新生儿出生或入院体重
      boolean hasZ371Code = false;
      for (BaBrzdxx baBrzdxx : baBrzdxxList) {
        if (equalStr("Z37.1", baBrzdxx.getJbbm())) {
          hasZ371Code = true;
          break;
        }
      }
      if (hasZ371Code && (basy.getXsecstz() != null || basy.getXserytz() != null)) {
        addError(baxyList, "", "诊断Z37.1[单一死产]，不应填写新生儿出生或入院体重", "0", "0", "jbbm", 0, 0, null);
      }

      // 主要诊断或其他诊断编码为【分娩】O80--O84并且主要诊断和其他诊断的编码没有【流产】O00-O08的编码时，此时必须在其他诊断有（另编码）“Z37”的编码。
      boolean hasChildbirthDiag = false;
      boolean hasAbortionDiag = false;
      boolean hasZ37Diag = false;

      for (BaBrzdxx baBrzdxx : baBrzdxxList) {
        String diagStartThreeCode = getStartStr(baBrzdxx.getJbbm(), 3);
        if (CHILDBIRTH_DIAG_CODES.contains(diagStartThreeCode)) {
          hasChildbirthDiag = true;
        } else if (ABORTION_DIAG_CODES.contains(diagStartThreeCode)) {
          hasAbortionDiag = true;
        }
      }

      if (hasChildbirthDiag && !hasAbortionDiag) {
        for (BaBrzdxx baBrzdxx : baBrzdxxList) {
          if (equalStr("Z37", baBrzdxx.getJbbm())) {
            hasZ37Diag = true;
            break;
          }
        }
        if (!hasZ37Diag) {
          addError(baxyList, "", "主要诊断或其他诊断编码为080--O84并且主要诊断和其他诊断的编码没有O00-O08的编码时，此时必须在其他诊断有（另编码）“Z37”的编码。", "0", "1", "qtzd", 0, 0, null);
        }
      }

      // 当出院主要诊断或者出院其他诊断编码出现O80-O84编码，且无流产结局编码出现O00-O08编码时，
      // 出院其他诊断编码必须有分娩结局编码Z37
      if (hasChildbirthDiag && !hasAbortionDiag && !hasZ37Diag) {
        addError(baxyList, "must", "必须有分娩结局编码Z37", "0", "1", "qtzd", 0, 0, 0.5);
      }

      boolean hasZ37Code = false;
      for (BaBrzdxx baBrzdxx : baBrzdxxList) {
        String diagStartFiveCode = getStartStr(baBrzdxx.getJbbm(), 5);
        if ((Z37_CODES.contains(diagStartFiveCode))) {
          hasZ37Code = true;
        }
      }

      // 新生儿出生体重漏填
      if (hasZ37Code || ls_xseflag == 1) {
        if (basy.getXsecstz() == null) {
          addError(baxyList, "must", "新生儿出生体重漏填", "0", "0", "xsecstz", 0, 0, 4.0);
        }
      }

      String diagStartOneCode = getStartStr(mainDiagCode, 1);

      // 损伤（中毒）外部原因疾病编码漏填
      if ("1".equals(zkSsyy) && ("S".equals(diagStartOneCode) || "T".equals(diagStartOneCode))) {
        if (basy.getWbyy() == null || "".equals(basy.getWbyy())) {
          addError(baxyList, "must", "损伤（中毒）外部原因名称漏填", "0", "0", "wbyy", 0, 0, 0.5);
        }
        if (basy.getH23() == null || "".equals(basy.getH23())) {
          addError(baxyList, "must", "损伤（中毒）外部原因疾病编码漏填", "0", "0", "h23", 0, 1, 0.5);
        }
      }
    }

    //手术提示
    if (baSsjlList.size() > 0) {
      String mainOperCode = baSsjlList.get(0).getSsbm();

      //不能为主要手术
      baxyList = checkMainOperCode(baxyList, mainOperCode);
      baxyList = checkOperTip(baxyList, baSsjlList, ls_xseflag);
      baxyList = checkMainDiagOrOper(baxyList, zsstip_list, mainOperCode, "zsstip", ls_xseflag);
      baxyList = checkTogetherOper(baxyList, sstogether_list, baSsjlList, "sstogether");

      if (baSsjlList.size() > 1) {
        baxyList = checkOperMerge(baxyList, baSsjlList);
      }
    }

    // 入院科室与出院科室不同时，必须录入转科科别
    if (basy.getRykb() != null && basy.getCykb() != null && !basy.getRykb().equals(basy.getCykb()) && basy.getZkkb() == null) {
      if (!"重庆市沙坪坝区陈家桥医院".equals(yyname)) {
        addError(baxyList, "must", "入院科室与出院科室不同时，必须录入转科科别", "0", "1", "zkkb", 0, 0, 1.0);
      }
    }

    // 提取漏掉的手术
    baxyList = checkMissingOper(basy, baxyList, baSsjlList);
    // 提取漏掉的诊断  --  质控适应症
    if ("1".equals(zkSyz)) {
      baxyList = checkMissingDiag(basy, baxyList, baBrzdxxList);
    }

    if (basy.getZfy() != null && basy.getZfje() != null && basy.getZfy().compareTo(basy.getZfje()) == -1) {
      addError(baxyList, "", "住院总费用必须大于等于自付金额", "0", "0", "fy", 0, 0, null);
    }

    // 处理必填字段
    if (!"1".equals(basy.getZyzt()) && !"".equals(ifNull(basy.getJbdm()))) {
      for (BBasyCheckBz bBasyCheckBz : must_list) {
        if ("1".equals(bBasyCheckBz.getIsmust())) {
          try {
            String columnName = bBasyCheckBz.getColumnName();
            String firstChar = columnName.length() > 1 ? columnName.substring(0, 1).toUpperCase() : columnName.toUpperCase();
            String methodName = "get" + firstChar + columnName.substring(1, columnName.length());
            Method method = BaSyjl.class.getMethod(methodName);
            Object result = method.invoke(basy);
            if (result == null || "".equals(result)) {
              addError(baxyList, "must", bBasyCheckBz.getComments() + "漏填", "0", "0",
                columnName, 0, 0, bBasyCheckBz.getScore());
            }
          } catch (Exception e) {
            e.printStackTrace();
          }
        }
      }
    }

    if (basy.getXyf() != null && basy.getKjywf() != null && basy.getXyf().compareTo(basy.getKjywf()) == -1) {
      addError(baxyList, "", "西药费必须大于等于抗菌药物费用", "0", "0", "fy", 0, 0, null);
    }

    if (ls_xseflag == 1) {
      BigDecimal xsecstz = basy.getXsecstz();
      BigDecimal xserytz = basy.getXserytz();
      if (xsecstz != null) {
        if (xsecstz.compareTo(BigDecimal.valueOf(200)) < 0) {
          addError(baxyList, "", "新生儿出生体重（克）不能小于200g", "0", "0", "xsecstz", 0, 0, null);
        } else if (xsecstz.compareTo(BigDecimal.valueOf(10000)) > 0) {
          addError(baxyList, "", "新生儿出生体重（克）不能大于10000g", "0", "0", "xsecstz", 0, 0, null);
        }
      }
      if (xserytz != null) {
        if (xserytz.compareTo(BigDecimal.valueOf(200)) < 0) {
          addError(baxyList, "", "新生儿入院体重（克）不能小于200g", "0", "0", "xserytz", 0, 0, null);
        } else if (xserytz.compareTo(BigDecimal.valueOf(20000)) > 0) {
          addError(baxyList, "", "新生儿入院体重（克）不能大于20000g", "0", "0", "xserytz", 0, 0, null);
        }
      }
    }

    //计算天龄
    Long dayAge = getDayAge(csrqDate, basy.getBzyzsnl());


    //诊断编码出现P10～P15，入院日期减出生日期必须小于365天
    boolean hasXsrDiag = false;
    for (BaBrzdxx diag : baBrzdxxList) {
      String diagStartThreeCode = getStartStr(diag.getJbbm(), 3);
      if (NEONATE_DIAG_CODES.contains(diagStartThreeCode)) {
        hasXsrDiag = true;
        break;
      }
    }
    if (hasXsrDiag && csrqDate != null && rysjDate != null && csrqDate.until(rysjDate, ChronoUnit.DAYS) >= 365) {
      addError(baxyList, "", "诊断编码出现P10-P15，入院日期减出生日期必须小于365天", "0", "0", "rysj", 0, 0, null);
    }

    if (hasXsrDiag && dayAge != null && dayAge >= 365) {
      addError(baxyList, "", "诊断编码出现P10-P15，(年龄不足1周岁的)天龄必须小于365天", "0", "0", "bzyzsnl", 0, 0, null);
    }

    //add by lt 20240717
    if ((basy.getNl() == null || basy.getNl() == 0) && (basy.getBzyzsnl() == null || basy.getBzyzsnl() == 0)) {
      addError(baxyList, "", "当年龄为空或为0时，(年龄不足1周岁的)年龄(月或天)不能为空", "0", "0", "bzyzsnl", 0, 0, null);
    }

    if (dayAge != null && dayAge > 365) {
      addError(baxyList, "", "(年龄不足1周岁的)天龄不能大于365天", "0", "0", "bzyzsnl", 0, 0, null);
    }

//    if (ls_xseflag != 1 && basy.getBzyzsnl() != null) {
//      addError(baxyList, "", "不是新生儿，(年龄不足1周岁的)年龄(月)不能填写", "0", "0", "bzyzsnl", 0, 0, null);
//    }

    if (basy.getNl() == null && basy.getBzyzsnl() == null) {
      addError(baxyList, "", "年龄和(年龄不足1周岁的)年龄(月)不能同时为空", "0", "0", "bzyzsnl", 0, 0, null);
    }

    // 有手术费但无手术记录
    if (basy.getSsf() != null && basy.getSsf().compareTo(BigDecimal.valueOf(100)) >= 1 && baSsjlList.size() == 0) {
      addError(baxyList, "must", "有手术费但无手术记录", "0", "0", "ssjczbm1", 0, 0, 4.0);
    }

    //检查入院途径
    String rytj = ifNull(basy.getRytj());
    if ("9".equals(rytj) || "".equals(rytj) || "其他".equals(rytj)) {
      addError(baxyList, "", "入院途径漏填或为[其他]9", "0", "0", "rytj", 0, 0, null);
    }

    //新生儿体重大于4000g,应有诊断O33.501[巨大儿伴头盆不称]或O66.201[巨大儿难产]
    BigDecimal xsecstz = basy.getXsecstz() == null ? BigDecimal.ZERO : basy.getXsecstz();
    if (xsecstz.compareTo(BigDecimal.valueOf(4000)) > 0) {
      List<String> bmList = getDiagCodeList(baBrzdxxList);
      if (!bmList.contains("O33.501") && !bmList.contains("O66.201")) {
        addError(baxyList, "", "新生儿体重大于4000g，应有诊断O33.501[巨大儿伴头盆不称]或O66.201[巨大儿难产]", "0", "0", "qtzd", 0, 0, null);
      }
    }

    //实际住院天数一般应小于8000天且为整数
    Long sjzyts = basy.getSjzyts();
    if (sjzyts != null && sjzyts > 8000) {
      addError(baxyList, "", "实际住院天数一般应小于8000天且为整数", "0", "0", "sjzyts", 0, 0, null);
    }

    //诊断O00-O08、O30-O48、O60-O75、O80-O84，出院科别或转科科别不能为儿科
    String cykb = ifNull(basy.getCykb());
    String zkkb = ifNull(basy.getZkkb());
    if ("儿科".equals(cykb) || "儿科".equals(zkkb)) {
      for (BaBrzdxx baBrzdxx : baBrzdxxList) {
        String diagStartThreeCode = getStartStr(baBrzdxx.getJbbm(), 3);
        if (NOT_PEDIATRICS_DIAG_CODES.contains(diagStartThreeCode)) {
          addError(baxyList, "", "诊断O00-O08、O30-O48、O60-O75、O80-O84，出院科别或转科科别不能为儿科", "0", "0", "儿科".equals(cykb) ? "cykb" : "zkkb", 0, 0, null);
          break;
        }
      }
    }

    // 离院方式
    if ("医嘱转院".equals(basy.getLyfs()) && basy.getYzzyYljg() == null) {
      addError(baxyList, "", "当选择医嘱转院时，必须有拟转医疗机构", "0", "0", "yzzy_yljg", 0, 0, null);
    } else if ("医嘱转社区卫生服务机构/乡镇卫生院".equals(basy.getLyfs()) && basy.getWsyYljg() == null) {
      addError(baxyList, "", "当选择医嘱转社区卫生服务机构/乡镇卫生院时，必须有社区卫生服务卫生院名称", "0", "0", "wsy_yljg", 0, 0, null);
    }

    String checkQy = ybjkOptionService.getCacheOptionInfo("check_qy", "0");

    if ((StringUtils.isBlank(baSyjlExt.getQy()) || !ChongqingDistrict.containsDistrict(baSyjlExt.getQy())) && "1".equals(checkQy)) {
      addError(baxyList, "", "区域必须精确到区县", "0", "0", "qy", 0, 0, null);
    }

    // 3.2 住院标准规则校验
    if ("1".equals(use_special_rule)) {
      baxyList = checkLowStandardHospitalization(baxyList, basy, baBrzdxxList, baSsjlList);
    }

    baSyjlErrorService.deleteBaSyjlErrorByBrbsAndJlly(new BaSyjlError(brbs, jlly));

    //去重
    Set<BaSyjlError> baxySet = new TreeSet<>(Comparator.comparing(BaSyjlError::getErrordes));
    baxySet.addAll(baxyList);
    baxyList = new ArrayList<>(baxySet);

    List<BaSyjlError> tipList = new ArrayList<>();

    for (BaSyjlError baSyjlError : baxyList) {
      baSyjlError.setBah(bah);
      baSyjlError.setBrbs(brbs);
      baSyjlError.setJlly(jlly);
      baSyjlError.setCreatetime(currentTime);
      baSyjlError.setUpdatetime(currentTime);
      if (baSyjlError.getCode().indexOf("tip") == -1 && baSyjlError.getCode().indexOf("lzd") == -1) {
        baSyjlErrorService.insertBaSyjlError(baSyjlError);
      } else {
        tipList.add(baSyjlError);
      }
    }


    if ("重庆市沙坪坝区陈家桥医院".equals(yyname)) {
      List<BaSyjlError> errList = basyCheckBzPfService.getErrList(new BaSyjlError(brbs, jlly));
      errList.addAll(tipList);
      return getDataTable(errList);
    }


    List<BaSyjlError> errors = baSyjlErrorService.selectBaSyjlErrorList(new BaSyjlError(brbs, jlly, "1"));
    baxyList.addAll(errors);
    return getDataTable(baxyList);
  }

  // 校验信息返回文本
  @RequestMapping("/bajytxt")
  @ResponseBody
  public String bajytxt(BaSyjl baSyjl) throws Exception {
    String ls_return = "";
    String ls_errtype = "";
    transbasy(baSyjl.getBrbs());
    TableDataInfo rspData = new TableDataInfo();
    rspData = baxy(baSyjl, "");

    BaSyjlError basyjlerr = new BaSyjlError();
    if (rspData.getRows().size() > 0) {
      for (int i = 0; i < rspData.getRows().size(); i++) {


        basyjlerr = (BaSyjlError) rspData.getRows().get(i);
        ls_errtype = basyjlerr.getCode();

        //提示不返
//        if (ls_errtype != null) {
//          if (ls_errtype.indexOf("tip") > -1) {
//            continue;
//          }
//        }

        if ("".equals(ls_return)) {
          ls_return = basyjlerr.getCode() + "|||" + basyjlerr.getErrordes();
        } else {
          ls_return = ls_return + "&&&" + basyjlerr.getCode() + "|||" + basyjlerr.getErrordes();
        }

        if (basyjlerr.getScore() != null) {
          if (basyjlerr.getScore() > 0) {
            ls_return = ls_return + "  扣分：" + basyjlerr.getScore();
          }

        }
      }
    }


    return ls_return;
  }

  // 校验信息返回json
  @RequestMapping("/bajyjson")
  public Map<String, Object> bajyjson(BaSyjl baSyjl) throws Exception {
    String brbs = baSyjl.getBrbs();
    if (brbs.indexOf("-") > -1) {
      baSyjl.setBrbs(brbs.replace("-", "_"));
    }
    System.out.println("-------------------------------------------brbs:" + baSyjl.getBrbs());
    String ls_return = "";
    TableDataInfo rspData = new TableDataInfo();
    transbasy(baSyjl.getBrbs());
    rspData = baxy(baSyjl, "");
    BaSyjlError basyjlerr = new BaSyjlError();
    List<Errdesc> lists = new ArrayList<>();

    if (rspData.getRows().size() > 0) {
      for (int i = 0; i < rspData.getRows().size(); i++) {
        basyjlerr = (BaSyjlError) rspData.getRows().get(i);
        ls_return = basyjlerr.getErrordes();
        if (basyjlerr.getScore() != null) {
          if (basyjlerr.getScore() > 0) {
            ls_return = ls_return + "  扣分：" + basyjlerr.getScore();
          }
        }
        Errdesc obj1 = new Errdesc(ls_return);
        lists.add(obj1);
      }
    }

//		 List<Errdesc> list = new ArrayList<>();
//		 Errdesc obj1 = new Errdesc("aaa");
//	     list.add(obj1);


    Map<String, Object> map = new HashMap<>();
    map.put("info", lists);
    map.put("code", "0");
    map.put("message", "更新成功");
    return map;
  }

  public void transbasy(String ls_brbs) {
    try {
      brxxSyncService.basySync(ls_brbs,null);
    } catch (Exception e) {
      logger.error(e.getMessage());

    }
//    try {
//
//
//      OkHttpClient okHttpClient = new OkHttpClient();
//      String drgfzIp = baSyjlService.selectDrgFzIp();
////		        String drgfzIp = "*************";
//      String zkurl = "http://" + drgfzIp + ":9000/?" + "action=getbasy&brbs=" + ls_brbs + "&num="
//        + Math.random();
//      System.out.println(zkurl);
//      Request request = new Request.Builder().url(zkurl).build();
//      Response response = okHttpClient.newCall(request).execute();
//      String result = response.body().string();
//      System.out.println(result);
//    } catch (Exception e) {
//
//    }
  }

  public void initCheckRules() {
    notqtzd_list = getCheckRule("notqtzd");// 非其他诊断
    must_list = getCheckRule("must");// 必填项
    notzzd_list = getCheckRule("notzzd");// 非主要诊断
    notzss_list = getCheckRule("notzss");// 非主要手术
    hb_list = getCheckRule("hb");// 合并诊断
    tip_list = getCheckRule("tip"); //提示
    sstip_list = getCheckRule("sstip"); //手术提示
    zsstip_list = getCheckRule("zsstip"); // 主要手术提示
    zzdtip_list = getCheckRule("zzdtip"); // 主要诊断提示
    zzdmctip_list = getCheckRule("zzdmctip"); // 主要诊断提示
    zdmctip_list = getCheckRule("zdmctip");// 诊断名称提示
    rybq_list = getCheckRule("rybq"); //入院病情
    man_list = getCheckRule("man"); // 男
    woman_list = getCheckRule("woman"); // 女
    sshb_list = getCheckRule("sshb"); // 手术合并
    together_list = getCheckRule("together"); // 诊断合并
    nottogether_list = getCheckRule("nottogether"); // 诊断不合并
    zzdnottogether_list = getCheckRule("zzdnottogether"); // 主要诊断不合并
    sstogether_list = getCheckRule("sstogether");  // 手术合并
    dept_list = getCheckRule("dept"); // 科室
    priority_list = getCheckRule("priority"); // 优先级

    bzmlList = bzmlService.selectBzmlList(new Bzml());
    ssmlList = ssmlService.selectSsmlList(new Ssml());

    icd10ybdyList = icd10ybdyService.selectIcd10ybdyList(new Icd10ybdy());

    stopUseIcd10List = baBrzdxxService.selectStopUseIcd10();
    stopUseIcd10List.removeIf(icdStopUse -> icdStopUse == null || icdStopUse.getBm() == null || icdStopUse.getMc() == null);
    stopUseIcd9List = baSsjlService.selectStopUseIcd9();
    stopUseIcd9List.removeIf(icdStopUse -> icdStopUse == null || icdStopUse.getBm() == null || icdStopUse.getMc() == null);

    YbjkOption ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("bmpdws"); //编码判断位数
    bmpdws = (ybjkOption != null) ? Integer.parseInt(ybjkOption.getcValue()) : 5;

    ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("zk_syz"); //质控适应症
    zkSyz = (ybjkOption != null) ? ybjkOption.getcValue() : "1";

    ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("zk_blzd"); //是否质控病理诊断
    zkBlzd = (ybjkOption != null) ? ybjkOption.getcValue() : "1";

    ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("zk_ssyy");// 是否质控受伤原因
    zkSsyy = (ybjkOption != null) ? ybjkOption.getcValue() : "1";

    yyname = StringUtils.defaultString(ybjkOptionService.getCompanyName(),"");
    use_special_rule = ybjkOptionService.getOptionInfo("use_special_rule", "0");

    isInit = true;
  }

  public List<BBasyCheckBz> getCheckRule(String errType) {
    BBasyCheckBz bBasyCheckBz = new BBasyCheckBz();
    bBasyCheckBz.setErrtype(errType);
    List<BBasyCheckBz> checkBzList = basyCheckBzService.selectBBasyCheckBzList(bBasyCheckBz);
    checkBzList.removeIf(checkBz -> checkBz == null || (StringUtils.isBlank(checkBz.getTip()) && !"must".equals(checkBz.getErrtype())) || "".equals(checkBz.getComments()));
    return checkBzList;
  }

  @Scheduled(cron = "0 0 * * * ?")
  public void reset() {
    initCheckRules();
  }

  public String getMidString(String input, String startStr, String endStr) {
//    String input = "诊断有多个类目不同的S03和S13，应增加T03.0[累及头部伴有颈部的脱位、扭伤和劳损]";
//    String startStr = "应增加";
//    String endStr = "[";
    String subString = null;
    // 找到开始字符串的位置

    if (input == null || startStr == null || endStr == null) {
      return "";
    }

    int startIndex = input.indexOf(startStr) + startStr.length();

    // 确保开始字符串存在并且查找结束字符串的位置
    if (startIndex != -1) {
      int endIndex = input.indexOf(endStr, startIndex);

      // 确保结束字符串存在
      if (endIndex != -1) {
        subString = input.substring(startIndex, endIndex);
        System.out.println("找到的内容: " + subString);
      } else {
        System.out.println("未找到结束字符串 '['");
      }
    } else {
      System.out.println("未找到开始字符串 '应增加'");
    }
    return subString;
  }

  //处理合并
  @RequestMapping("/dealhb")
  @ResponseBody
  public String dealhb(@RequestParam(value = "zdxx") String ls_brbzbm) {

    if (ls_brbzbm == null) {
      return "";
    }
    // 初始化
    if (!isInit) {
      initCheckRules();
    }

    String ls_brbzbm_old = ls_brbzbm;
    // 病人病种编码
    List<String> brzdxxlist_bzbm = Arrays.asList(ls_brbzbm.split(","));
    // 需要合并的病种编码
    List<String> xyhbbzbm = new ArrayList<>();

//		   			xyhbbzbm.add(baBrzdxxList.get(ll_i).getJbbm());

    String ls_hbs = null;
    String[] ls_hbarr = null;
    String[] ls_hbtypearr = null;

    for (int i = 0; i < hb_list.size(); i++) {
      int ll_have = 0;
      ls_hbs = hb_list.get(i).getComments();

      // 为空时不比较
      if (ls_hbs == null || "".equals(ls_hbs)) {
        continue;
      }
      xyhbbzbm.clear();
      if (ls_hbs.length() > 1) {

        if (ls_hbs.indexOf("&") > 0) {
          ls_hbtypearr = ls_hbs.split("&");
          // 诊断I11.901[高血压性心脏病]、I12.900x003[高血压性肾病]、N18.900[慢性肾衰竭]，应合并为I13.100[高血压心脏和肾脏病伴有肾衰竭]---有三个&时
          if (ls_hbtypearr.length >= 3) {
            List<String> searchStrings = new ArrayList<>();
            searchStrings.add(ls_hbtypearr[0]);
            searchStrings.add(ls_hbtypearr[1]);
            searchStrings.add(ls_hbtypearr[2]);
            xyhbbzbm.clear();
            if (brzdxxlist_bzbm.stream().allMatch(s -> searchStrings.stream().anyMatch(s::contains))) {
              ll_have = 2;
              xyhbbzbm.add(ls_hbtypearr[0]);
              xyhbbzbm.add(ls_hbtypearr[1]);
              xyhbbzbm.add(ls_hbtypearr[2]);

            }

          } else {
            // 当同时存在E10%-E14%糖尿病和O80%-O84%流产或分娩时，应视情况分类到妊娠期糖尿病或妊娠合并糖尿病---类别不同
            for (int ll_i = 0; ll_i < ls_hbtypearr.length; ll_i++) {
              ls_hbarr = ls_hbtypearr[ll_i].split(",");
              for (int i1 = 0; i1 < ls_hbarr.length; i1++) {
//											boolean flag = false;
                // 长度小于2时返回
                if (ls_hbarr[i1].length() < 2) {
                  continue;
                }
                xyhbbzbm.clear();
                boolean flag = false;
                for (int i2 = 0; i2 < brzdxxlist_bzbm.size(); i2++) {
                  if (equalStr(ls_hbarr[i1], brzdxxlist_bzbm.get(i2))) {
                    flag = true;
                    xyhbbzbm.add(brzdxxlist_bzbm.get(i2));
                  }
                }

                if (flag) {

                  ll_have++;
                  break;// 在一个类别中找一个就退出，再在另一个类别中去找
                }
              }
            } // for (int ll_i = 0; ll_i < ls_hbtypearr.length; ll_i++) {
          }
        }

        // 诊断有多个S21.0-S21.2，应增加S21.700[胸壁多处开放性伤口],没有类别区分
        else {
          ls_hbarr = ls_hbs.split(",");
          for (int i1 = 0; i1 < ls_hbarr.length; i1++) {
            // 长度小于2时返回
            if (ls_hbarr[i1].length() < 2) {
              continue;
            }

            boolean flag = false;
            for (int i2 = 0; i2 < brzdxxlist_bzbm.size(); i2++) {

              if (equalStr(ls_hbarr[i1], brzdxxlist_bzbm.get(i2))) {
//
                flag = true;
                xyhbbzbm.add(brzdxxlist_bzbm.get(i2));// 添加需要合并
              }

            }
            if (flag) {
              ll_have++;

            }

          }
        }
      }

      if (ll_have > 1) {

        //当合并的病种编码不为空时，进行诊断合并
        if (hb_list.get(i).getHbbzbm() != null && !"".equals(hb_list.get(i).getHbbzbm())) {
          String[] hbarr = hb_list.get(i).getHbbzbm().split("\\|");

          if (hbarr.length == 2) {

            for (int i2 = 0; i2 < xyhbbzbm.size(); i2++) {
              //合并

              if ("h".equals(hbarr[0])) {
                ls_brbzbm = ls_brbzbm.replace(xyhbbzbm.get(i2), hbarr[1]);
              }
              //添加
              if ("+".equals(hbarr[0]) && !ls_brbzbm.contains(hbarr[1])) {
                ls_brbzbm = ls_brbzbm + "," + hbarr[1];
              }

            }

            //要合并或增加时，去除重复的诊断
            if (!ls_brbzbm.equals(ls_brbzbm_old)) {

              List<String> list = Arrays.asList(ls_brbzbm.split(","));
              List<String> uniqueList = list.stream()
                .distinct()
                .collect(Collectors.toList());

              ls_brbzbm = uniqueList.toString();
              ls_brbzbm = ls_brbzbm.replace("[", "");
              ls_brbzbm = ls_brbzbm.replace("]", "");

            }


          }


        }

      }
//						// 有两个或两个以上时，就应该合并
//						if (ll_have > 1) {
//							// 单独处理新生儿
//							if (hb_list.get(i).getTip().indexOf("新生儿") != -1||hb_list.get(i).getTip().indexOf("年龄≤28天") != -1) {
//								if (ls_xseflag == 1) {
//									addError(baxyList,brbs,bah,  hb_list.get(i).getTip(), "0", "1", "hb", 0, 0, null,
//											currentTime);
//								}
//							} else {
//								addError(baxyList,brbs,bah,  hb_list.get(i).getTip(), "0", "1", "hb", 0, 0, null,
//										currentTime);
//							}
//						}
    }
    return ls_brbzbm;
  }

  class Errdesc {
    String errinfo;

    public Errdesc(String errinfo_str) {
      // TODO Auto-generated constructor stub
      errinfo = errinfo_str;
    }

    public String getErrinfo() {
      return errinfo;
    }

    public void setErrinfo(String errinfo) {
      this.errinfo = errinfo;
    }

  }

  public boolean equalStr(String str1, String str2) {
    if (str1 == null || str2 == null) {
      return false;
    }
    if (str1.length() > str2.length()) {
      return false;
    }
    if (str1.length() == str2.length()) {
      if (str1.equals(str2)) {
        return true;
      } else
        return false;
    }
    if (str1.length() < str2.length()) {
      String newStr2 = "";
      if (str1.length() >= 7) {
        newStr2 = str2;
      } else {
        newStr2 = str2.substring(0, str1.length());
      }
      if (str1.equals(newStr2)) {
        return true;
      }
    }
    return false;
  }

  String bajyban = null;

  @RequestMapping("/bajyjsonban")
  public Map<String, Object> bajyjsonban(@RequestParam(value = "brbs") String brbs, @RequestParam(value = "zdxx") String zdxx, @RequestParam(value = "ssxx") String ssxx) {

    if (bajyban == null) {
      YbjkOption ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("bajy_ban");
      if (ybjkOption == null || "".equals(ybjkOption.getcValue())) {
        bajyban = "0";
      } else {
        bajyban = ybjkOption.getcValue();
      }
    }

    BaSyjl baSyjl = baSyjlService.selectBaSyjlByBrbs(brbs);
    if (baSyjl == null) {
      Map<String, Object> map = new HashMap<>();
      map.put("message", "病案不存在");
      return map;
    }
    if (brbs.indexOf("-") > -1) {
      baSyjl.setBrbs(brbs.replace("-", "_"));
    }
    String brid, zyid;
    brid = "";
    zyid = "";
    System.out.println("-------------------------------------------brbs:" + baSyjl.getBrbs());
    String ls_return = "";
    TableDataInfo rspData = new TableDataInfo();

    String[] brbsarr = brbs.split("_");
    if (brbsarr.length == 2) {
      brid = brbsarr[0];
      zyid = brbsarr[1];
    } else {
      Map<String, Object> map = new HashMap<>();
      map.put("message", "病案不存在");
      return map;
    }


    String[] zdxxarr = zdxx.split(",");
    BaBrzdxx baBrzdxx = new BaBrzdxx();
    baBrzdxx.setBrid(brid);
    baBrzdxx.setZyid(zyid);
    baBrzdxxService.deleteBaBrzdxxById(baBrzdxx);
    int li_zdcx = 0;
    for (String s : zdxxarr) {
      BaBrzdxx baBrzdxx1 = new BaBrzdxx();
      baBrzdxx1.setJbbm(s);
      li_zdcx = li_zdcx + 1;
      baBrzdxx1.setBrid(brid);
      baBrzdxx1.setZyid(zyid);
      baBrzdxx1.setBrbs(brbs);
      baBrzdxx1.setZdcx(li_zdcx);
      baBrzdxx1.setJbbm(s);
      baBrzdxx1.setZdmc("");
      baBrzdxxService.insertBaBrzdxx(baBrzdxx1);
    }

    int li_sscx = 0;
    String[] ssxxarr = ssxx.split(",");

    BaSsjl baSsjl = new BaSsjl();
    baSsjl.setBrbs(brbs);
    baSsjl.setBrid(brid);
    baSsjl.setZyid(zyid);

    baSsjlService.deleteBaSsjlById(baSsjl);

    for (String s : ssxxarr) {
      BaSsjl baSsjl1 = new BaSsjl();
      baSsjl1.setBrbs(brbs);
      baSsjl1.setBrid(brid);
      baSsjl1.setZyid(zyid);
      baSsjl1.setSsbm(s);
      li_sscx = li_sscx + 1;
      baSsjlService.insertBaSsjl(baSsjl1);
      baSsjl1.setSsmc("");
    }

//    String ban = "ban|" + zdxx + "|" + ssxx;
    rspData = baxy(baSyjl, "notsave");

    BaSyjlError basyjlerr = new BaSyjlError();
    List<Errdesc> lists = new ArrayList<>();

    //还原记录

    baBrzdxxService.deleteBaBrzdxxById(baBrzdxx);
    baSsjlService.deleteBaSsjlById(baSsjl);

    baBrzdxxService.insertBaBrzdxxBySyzd(baBrzdxx);
    baSsjlService.insertBaSsjlxxBySyss(baSsjl);

    String flag = "询问";
    BigDecimal je = BigDecimal.valueOf(0);
    if (rspData.getRows().size() > 0) {
      for (int i = 0; i < rspData.getRows().size(); i++) {
        basyjlerr = (BaSyjlError) rspData.getRows().get(i);
        ls_return = basyjlerr.getErrordes();
        if (basyjlerr.getCode() == null || "".equals(basyjlerr.getCode())) {
          continue;
        }

        if ("yfz".equals(basyjlerr.getCode()) || "hb".equals(basyjlerr.getCode()) || "lss".equals(basyjlerr.getCode())) {
          if (basyjlerr.getScore() != null) {
            if (basyjlerr.getScore() > 0) {
              ls_return = ls_return + "  扣分：" + basyjlerr.getScore();
            }
          }
          Errdesc obj1 = new Errdesc(ls_return);
          lists.add(obj1);
        }

        if ("1".equals(bajyban)) {
          //查询是否存在漏手术，且金额大于500   返回禁止  否则返回询问
          if ("lss".equals(basyjlerr.getCode())) {
            String errordes = basyjlerr.getErrordes();
            int startIndex = errordes.indexOf("[") + 1;
            if (startIndex > -1) {
              int endIndex = errordes.indexOf("]", startIndex);
              if (endIndex > -1) {
                String fyxmmc = errordes.substring(startIndex, endIndex);
                Fyxx fyxx = new Fyxx();
                fyxx.setXmmc(fyxmmc);
                Integer dyssNum = fyxxService.selectFyDySsNum(fyxx);

                //没有对应手术的项目存在sstype为“手术”的记录
                if (dyssNum > 0) {
                  je = je.add(basyjlerr.getJe());
                }

              }
            }
          }
        }
      }

      if ("1".equals(bajyban)) {
        //且总金额大于500 返回禁止
        if (je.compareTo(BigDecimal.valueOf(500)) >= 0) {
          flag = "禁止";
        }
      }
    }

    Map<String, Object> map = new HashMap<>();
    map.put("info", lists);
    map.put("code", "0");
    map.put("message", "更新成功");
    map.put("flag", flag);
    return map;
  }

  /**
   * 处理空字符
   */
  public String ifNull(String inputStr) {
    return StringUtils.defaultIfBlank(inputStr, "");
  }

  /**
   * 检查主要诊断和主要手术相关的操作
   */
  public List<BaSyjlError> checkMainDiagOrOper(List<BaSyjlError> baxyList, List<BBasyCheckBz> checkBzList, String checkObject, String checkType, int ls_xseflag) {
    for (BBasyCheckBz bBasyCheckBz : checkBzList) {
      String[] tipCodes = bBasyCheckBz.getComments().split(",");
      String tipContent = bBasyCheckBz.getTip();
      boolean isMatch;
      for (String tipCode : tipCodes) {
        isMatch = checkType.contains("bm") ? equalStr(tipCode, checkObject) : checkObject.contains(tipCode);
        if (isMatch && !(tipContent.contains("新生儿") && ls_xseflag != 1)) {
          addError(baxyList, checkType, tipContent, "0", "1", checkType, 0, 0, null, bBasyCheckBz.getIslink(), bBasyCheckBz.getLinkData(), checkObject);
          break;
        }
      }
    }
    return baxyList;
  }

  /**
   * 检查入院病情
   */
  public List<BaSyjlError> checkDiagRybq(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList) {

    BaBrzdxx mainDiag = baBrzdxxList.get(0);
    String mainDiagCode = mainDiag.getJbbm();
    String mainDiagRybq = ifNull(mainDiag.getRybq());

    for (BBasyCheckBz bBasyCheckBz : rybq_list) {
      String rybqValue = bBasyCheckBz.getRybq();
      String rybqFirst = rybqValue.contains("|") ? rybqValue.split("\\|")[0] : rybqValue;
      String rybqSecond = rybqValue.contains("|") && rybqValue.split("\\|").length > 1 ? rybqValue.split("\\|")[1] : "###";
      String rybqTip = bBasyCheckBz.getTip();
      String rybqTrigger = bBasyCheckBz.getRybqpdbz();
      String rybqObject = bBasyCheckBz.getRybqobject();
      String rybqCodes = bBasyCheckBz.getComments();

      String[] rybqCodeArr = rybqCodes.split(",");

      for (int i = 0; i < rybqCodeArr.length; i++) {
        String rybqCode = rybqCodeArr[i];
        if (equalStr(rybqCode, mainDiagCode)) {

          //入院病情判断对象为主要诊断
          if ("zzd".equals(rybqObject)) {
            //触发时提示 并且 当前主要诊断入院病情 等于 设置的入院病情
            //不触发时提示 并且 当前主要诊断入院病情 不等于 设置的入院病情
            if (("1".equals(rybqTrigger) && (mainDiagRybq.equals(rybqFirst) || mainDiagRybq.equals(rybqSecond))) || ("0".equals(rybqTrigger) && !mainDiagRybq.equals(rybqFirst) && !mainDiagRybq.equals(rybqSecond))) {
              addError(baxyList, "rybq", rybqTip, "0", "1", "rybq", 0, 0, null);
              break;
            }
          }

          //入院病情判断对象为其他诊断
          if ("qtzd".equals(rybqObject)) {
            //判断标志为1：触发时提示
            if ("1".equals(rybqTrigger) && baBrzdxxList.size() > 1) {
              //其他诊断中存在入院病情与设置的入院病情相同  触发校验  添加错误
              for (BaBrzdxx baBrzdxx : baBrzdxxList) {
                if (rybqFirst.equals(baBrzdxx.getRybq()) || rybqSecond.equals(baBrzdxx.getRybq())) {
                  addError(baxyList, "rybq", rybqTip, "0", "1", "rybq", 0, 0, null);
                  break;
                }
              }
            }
            //判单标志为0：不触发时提示
            if ("0".equals(rybqTrigger)) {
              //不存在其他诊断或者其他诊断的入院病情都不是设置的入院病情时触发
              if (baBrzdxxList.size() <= 1 || baBrzdxxList.subList(1, baBrzdxxList.size()).stream().noneMatch(item -> rybqFirst.equals(item.getRybq()) || rybqSecond.equals(item.getRybq()))) {
                addError(baxyList, "rybq", rybqTip, "0", "1", "rybq", 0, 0, null);
              }
            }
          }
        }
      }
    }
    return baxyList;
  }

  /**
   * 检查死亡病历
   */
  public List<BaSyjlError> checkLowRiskDiag(List<BaSyjlError> baxyList, String mainDiagCode) {
    if (mainDiagCode.length() >= 3) {
      String diagStartThreeCode = mainDiagCode.substring(0, 3);
      if (LOWER_RISK_DIAG.contains(diagStartThreeCode)) {
        addError(baxyList, "", "死亡病历中主要诊断不能为低风险诊断", "0", "1", "zyzd", 0, 0, null);
      }
    }
    return baxyList;
  }

  /**
   * 检查提示信息
   */
  public List<BaSyjlError> checkDiagCode(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList, int ls_xseflag) {
    for (BaBrzdxx baBrzdxx : baBrzdxxList) {
      for (BBasyCheckBz bBasyCheckBz : tip_list) {
        String[] tipCodes = bBasyCheckBz.getComments().split(",");
        for (String tipCode : tipCodes) {
          if (equalStr(tipCode, baBrzdxx.getJbbm())) {
            String tipContent = bBasyCheckBz.getTip();
            if (tipContent.contains("新生儿") && ls_xseflag != 1) {
              continue;
            }
            addError(baxyList, "tip", tipContent, "0", "1", "tip", 0, 0, null, bBasyCheckBz.getIslink(), bBasyCheckBz.getLinkData(), baBrzdxx.getJbbm());
            break;
          }
        }
      }
    }
    return baxyList;
  }

  /**
   * 检查手术提示
   */
  public List<BaSyjlError> checkOperTip(List<BaSyjlError> baxyList, List<BaSsjl> baSsjlList, int ls_xseflag) {
    for (BaSsjl operate : baSsjlList) {
      for (BBasyCheckBz operateTip : sstip_list) {
        String[] operateTipCodes = operateTip.getComments().split(",");
        for (String operateTipCode : operateTipCodes) {
          if (equalStr(operateTipCode, operate.getSsbm())) {
            String operateTipContent = operateTip.getTip();
            if (operateTipContent.contains("新生儿") && ls_xseflag != 1) {
              continue;
            }
            addError(baxyList, "sstip", operateTipContent, "0", "1", "sstip", 0, 0, null, operateTip.getIslink(), operateTip.getLinkData(), operate.getSsbm());
            break;
          }
        }
      }
    }

    return baxyList;
  }

  /**
   * 检查诊断名称的提示
   */
  public List<BaSyjlError> checkDiagName(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList) {
    for (BBasyCheckBz bBasyCheckBz : zdmctip_list) {
      String[] tipNames = bBasyCheckBz.getComments().split(",");
      String tipContent = bBasyCheckBz.getTip();
      for (String tipName : tipNames) {
        if (baBrzdxxList.stream().anyMatch(diag -> diag.getZdmc().contains(tipName))) {
          addError(baxyList, "zdmctip", tipContent, "0", "1", "zdmctip", 0, 0, null, bBasyCheckBz.getIslink(), bBasyCheckBz.getLinkData(), "");
          break;
        }
      }
    }
    return baxyList;
  }

  /**
   * 检查漏掉的手术
   */
  public List<BaSyjlError> checkMissingOper(BaSyjl basy, List<BaSyjlError> baxyList, List<BaSsjl> baSsjlList) {
    Fyxx fyxx = new Fyxx();
    fyxx.setBrid(basy.getBrid());
    fyxx.setZyid(basy.getZyid());
    fyxx.setXmbm("1");
    List<Fyxx> opersWithFee = fyxxService.selectssfyxx(fyxx);   //获取该用户费用项目对应手术
    opersWithFee.removeIf(Objects::isNull);
    List<BaSsjl> recommendOpers = baSsjlService.selectTjss(basy);   //获取该用户推荐手术
    recommendOpers.removeIf(Objects::isNull);
    for (Fyxx fee : opersWithFee) {
      boolean hasOperate = false;  //是否存在当前费用对应手术
      //这里的fykmname代表项目的对应手术名称
      //如果项目名称不为空但没有对应的手术，就判断当前手术中是否存在缺失手术的同类型推荐手术，如果存在同类型手术，就不算漏填
      if (fee.getFykmname() == null) {
        if (fee.getXmmc() != null) {
          for (BaSsjl recommendOper : recommendOpers) {
            if (!fee.getXmmc().equals(recommendOper.getXmmc()) || recommendOper.getSsbm().length() < bmpdws) continue;
            for (BaSsjl operate : baSsjlList) {
              if (operate.getSsbm().length() < bmpdws) continue;
              if (recommendOper.getSsbm().substring(0, bmpdws).equals(operate.getSsbm().substring(0, bmpdws))) {
                hasOperate = true;
                break;
              }
            }
            if (hasOperate) break;
          }
        }
        if (!hasOperate) {
          //当前项目没有对应手术
          addError(baxyList, "lss", "[" + fee.getXmmc() + "]" + "没有添加对应的手术", "0", "0", "ylss", 0, 0, 0.0, fee.getJe());
        }
      }
    }
    return baxyList;
  }

  /**
   * 检查漏掉的诊断
   */
  public List<BaSyjlError> checkMissingDiag(BaSyjl basy, List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList) {
    // 提取漏掉的诊断
    YbgkWgjl ybgkWgjl = new YbgkWgjl();
    ybgkWgjl.setZyh(basy.getBah());
    ybgkWgjl.setJzh(basy.getJzh());
    ybgkWgjl.setJktype("xzbz");
    String missingDiag;   //缺失的诊断
    List<YbgkWgjl> violationRecords = wgjlService.selectYbgkWgjlListByHospital(ybgkWgjl);  //当前病人的限制病种类型的违规记录
    violationRecords.removeIf(Objects::isNull);

    for (YbgkWgjl violationRecord : violationRecords) {
      if (violationRecord.getJklog() != null) {
        missingDiag = violationRecord.getJklog()
          .replace("扣分:0", "")
          .replace("限[", "适应症为[")
          .replace("[出院]", "");
        boolean hasMissingDiag = false;
        for (BaBrzdxx patientDiag : baBrzdxxList) {
          if (missingDiag.contains(patientDiag.getZdmc())) {
            hasMissingDiag = true;   //如果违规记录直接包含当前诊断
            break;
          }
          if (!hasMissingDiag && missingDiag.contains("适应症为[")) {
            if (missingDiag.indexOf("]", missingDiag.indexOf("适应症为[")) > -1) {
              //违规记录中限制的诊断
              String limitDiags = missingDiag.substring(missingDiag.indexOf("适应症为[") + 5, missingDiag.indexOf("]", missingDiag.indexOf("适应症为[")));
              if (limitDiags.indexOf("、") > -1) {
                String[] limitDiagArr = limitDiags.split("、");
                for (String limitDiag : limitDiagArr) {
                  if (patientDiag.getZdmc().contains(limitDiag)) {
                    hasMissingDiag = true;
                    break;
                  }
                }
              } else {
                if (patientDiag.getZdmc().contains(limitDiags)) {
                  hasMissingDiag = true;
                }
              }
            }
          }
        }

        if (!hasMissingDiag) {
          addError(baxyList, "lzd", missingDiag, "0", "0", "ylzd", 0, 0, 0.0);
        }
      }
    }
    return baxyList;
  }

  /**
   * 检查肿瘤
   */
  public List<BaSyjlError> checkTumour(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList) {
    int li_num = 0;
    for (BaBrzdxx baBrzdxx : baBrzdxxList) {
      String diagName = baBrzdxx.getZdmc();
      if (diagName.endsWith("恶性肿瘤") && !diagName.endsWith("继发恶性肿瘤")) {
        li_num++;
        if (li_num > 1) {
          addError(baxyList, "tip", "一次住院一般不用录两个或以上的恶性肿瘤", "0", "0", "tip", 0, 0, 0.0);
          break;
        }
      }
    }

    return baxyList;
  }

  /**
   * 检查限制性别的诊断
   */
  public List<BaSyjlError> checkLimitGenderDiag(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList, List<BBasyCheckBz> genderCheckList) {
    for (BaBrzdxx baBrzdxx : baBrzdxxList) {
      for (BBasyCheckBz genderCheckBz : genderCheckList) {
        if (genderCheckBz.getComments().equals(baBrzdxx.getJbbm())) {
          addError(baxyList, "", genderCheckBz.getTip(), "0", "1", "xb", 0, 0, 0.0);
          break;
        }
      }
    }
    return baxyList;
  }

  /**
   * 检查手术合并情况
   */
  public List<BaSyjlError> checkOperMerge(List<BaSyjlError> baxyList, List<BaSsjl> baSsjlList) {
    for (BBasyCheckBz bBasyCheckBz : sshb_list) {
      String tipContent = bBasyCheckBz.getTip();
      String tipCodes = bBasyCheckBz.getComments();
      String mergeSsCode = bBasyCheckBz.getHbbzbm();
      long matchingCount = 0;
      if (tipCodes.contains("&")) {
        String[] tipCodeArrs = tipCodes.split("&");

        for (String tipCodeArrStr : tipCodeArrs) {
          String[] tipCodeArr = tipCodeArrStr.split(",");
          boolean hasCommonCode = Arrays.stream(tipCodeArr).anyMatch(tipCode -> baSsjlList.stream().anyMatch(operate -> equalStr(tipCode, operate.getSsbm())));
          if (hasCommonCode) matchingCount++;
        }
      } else {
        String[] tipCodeArr = bBasyCheckBz.getComments().split(",");
        matchingCount = Arrays.asList(tipCodeArr).stream().filter(tipCode -> baSsjlList.stream()
          .anyMatch(ssjl -> equalStr(tipCode, ssjl.getSsbm()))).count();
      }

      if (matchingCount >= 2) {
        int flag = 0;
        if (StringUtils.isNotBlank(mergeSsCode)) {
          if (mergeSsCode.indexOf(",") > -1) {
            String[] mergeSsCodes = mergeSsCode.split(",");
            for (BaSsjl baSsjl : baSsjlList) {
              for (int i = 0; i < mergeSsCodes.length; i++) {
                if (equalStr(mergeSsCodes[i], baSsjl.getSsbm())) {
                  flag = 1;
                  break;
                }
              }
              if (flag == 1) {
                break;
              }
            }
          } else {
            for (BaSsjl baSsjl : baSsjlList) {
              if (equalStr(mergeSsCode, baSsjl.getSsbm())) {
                flag = 1;
                break;
              }
            }
          }
        }
        if (flag == 0) {
          addError(baxyList, "sshb", tipContent, "0", "1", "sshb", 0, 0, 0.0);
        }
      }

    }

    return baxyList;
  }

  /**
   * 获取诊断编码列表
   */
  public List<String> getDiagCodeList(List<BaBrzdxx> baBrzdxxList) {
    List<String> diagCodeList = new ArrayList<>();
    for (BaBrzdxx baBrzdxx : baBrzdxxList) {
      diagCodeList.add(baBrzdxx.getJbbm());
    }
    return diagCodeList;
  }


  /**
   * 处理诊断合并
   */
  public List<BaSyjlError> checkDiagMerge(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList, int ls_xseflag) {
    List<String> diagCodes = getDiagCodeList(baBrzdxxList);

    for (BBasyCheckBz bBasyCheckBz : hb_list) {
      String mergeTip = bBasyCheckBz.getTip();
      String mergeCodes = bBasyCheckBz.getComments();
      String mergeBzCode = bBasyCheckBz.getHbbzbm();

      int mergeCodeCount = 0;

      if (mergeCodes.length() > 1) {
        if (mergeCodes.indexOf("&") > -1) {
          String[] mergeCodeArr = mergeCodes.split("&");
          if (mergeCodeArr.length >= 3) {
            //如果存在三类编码，一般都是各有一个诊断编码，就判断当前诊断中是否全部存在
            List<String> searchStrings = new ArrayList<>();
            searchStrings.add(mergeCodeArr[0]);
            searchStrings.add(mergeCodeArr[1]);
            searchStrings.add(mergeCodeArr[2]);
            if (mergeCodeArr.length >= 4) {
              searchStrings.add(mergeCodeArr[3]);
            }
            if (searchStrings.stream().allMatch(diagCode -> diagCodes.stream().anyMatch(diagCode::contains))) {
              mergeCodeCount = 2;
            }
          } else {
            //如果有两类编码，就判断当前诊断是否同时存在两类诊断
            for (String mergeCode : mergeCodeArr) {
              String[] mergeCodeItems = mergeCode.split(",");
              for (String mergeCodeItem : mergeCodeItems) {
                boolean flag = false;
                for (BaBrzdxx baBrzdxx : baBrzdxxList) {
                  if (equalStr(mergeCodeItem, baBrzdxx.getJbbm())) {
                    flag = true;
                  }
                }
                if (flag) {
                  mergeCodeCount++;
                  break;
                }
              }
            }
          }
        } else {
          //如果只有一类诊断
          String[] mergeCodeArr = mergeCodes.split(",");
          //如果只有一个诊断编码，一般为编码前缀，就判断是否存在多个编码前缀与之相同的编码
          if (mergeCodeArr.length == 1 && (mergeTip.contains("年龄≤28天") || mergeTip.contains("有多个"))) {
            String mergeCode = mergeCodeArr[0];
            for (int j = 0; j < baBrzdxxList.size(); j++) {
              if (equalStr(mergeCode, baBrzdxxList.get(j).getJbbm())) {
                mergeCodeCount++;
              }
            }
          } else {
            //如果是多个编码，就判断当前诊断是否同时存在其中的多个编码
            for (String mergeCode : mergeCodeArr) {
              if (mergeCode.length() < 2) continue;
              boolean flag = false;
              for (BaBrzdxx baBrzdxx : baBrzdxxList) {
                if (equalStr(mergeCode, baBrzdxx.getJbbm())) {
                  flag = true;
                }
              }
              if (flag) {
                mergeCodeCount++;
              }
            }
          }
        }
      }

      // 有两个或两个以上时，就应该合并
      if (mergeCodeCount > 1) {
        // 单独处理新生儿
        if ((mergeTip.contains("新生儿") || mergeTip.contains("年龄≤28天"))) {
          if (ls_xseflag == 1) {
            addError(baxyList, "hb", mergeTip, "0", "1", "hb", 0, 0, null);
          }
        } else {
          int flag = 0;
//          if (mergeTip.contains("应增加")) {
//            String shouldAddCode = getMidString(mergeTip, "应增加", "[");
//            // 检查是否已经有增加的诊断
//            for (BaBrzdxx baBrzdxx : baBrzdxxList) {
//              if (equalStr(shouldAddCode, baBrzdxx.getJbbm())) {
//                flag = 1;
//              }
//            }
//          }
          //如果提示中包含"且不存在"，就判断当前诊断中是否存在这个诊断
          if (mergeTip.contains("且不存在")) {
            String notExistCode = getMidString(mergeTip, "且不存在", "[");
            for (BaBrzdxx baBrzdxx : baBrzdxxList) {
              if (equalStr(notExistCode, baBrzdxx.getJbbm())) {
                flag = 1;
              }
            }
          }

          //如果当前已经存在合并后的编码，则不质控
          if (StringUtils.isNotBlank(mergeBzCode) && flag == 0) {
            if (mergeBzCode.indexOf(",") > -1) {
              String[] mergeBzCodes = mergeBzCode.split(",");
              for (BaBrzdxx baBrzdxx : baBrzdxxList) {
                for (int i = 0; i < mergeBzCodes.length; i++) {
                  if (equalStr(mergeBzCodes[i], baBrzdxx.getJbbm())) {
                    flag = 1;
                    break;
                  }
                }
                if (flag == 1) {
                  break;
                }
              }
            } else {
              for (BaBrzdxx baBrzdxx : baBrzdxxList) {
                if (equalStr(mergeBzCode, baBrzdxx.getJbbm())) {
                  flag = 1;
                  break;
                }
              }
            }
          }

          if (flag == 0) {
            addError(baxyList, "hb", mergeTip, "0", "1", "hb", 0, 0, null);
          }
        }
      }
    }

    return baxyList;
  }

  /**
   * 格式化日期到字符串
   */
  public String convertToDateStr(LocalDate inputDate, DateTimeFormatter dateTimeFormatter) {
    String formatDateStr = dateTimeFormatter.format(inputDate);
    return formatDateStr;
  }

  /**
   * 将日期格式化为指定格式字符串
   */
  public LocalDate convertToLocalDate(String inputDate) {
    if (StringUtils.isNotBlank(inputDate)) {
      for (DateTimeFormatter formatter : FORMATTERS) {
        try {
          LocalDate date = LocalDate.parse(inputDate, formatter);
          return date;
        } catch (Exception e) {
          continue;
        }
      }
    }
    return null;  //当前日期不能被格式化或为空
  }

  /**
   * 格式化日期   将可以格式化的日期进行格式化  不能被格式化的日期赋值为null
   */
  public BaSyjl dateFormatter(BaSyjl basy) {
    basy.setZkrqDate(convertToLocalDate(basy.getZkrq()));
    basy.setCsrqDate(convertToLocalDate(basy.getCsrq()));
    basy.setRysjDate(convertToLocalDate(basy.getRysj()));
    basy.setCysjDate(convertToLocalDate(basy.getCysj()));
    return basy;
  }

  /**
   * 测试质控所有病案首页
   */
  @RequestMapping("/batchCheck")
  public void batchCheck() {
    long l1 = System.currentTimeMillis();
    List<BaSyjl> baSyjls = baSyjlService.selectBaSyjlList(new BaSyjl());
    for (int i = 0; i < baSyjls.size(); i++) {
      baxy(baSyjls.get(i), "");
    }
    long l2 = System.currentTimeMillis();
    System.out.println("------------------------");
    System.out.println(l2 - l1);
    System.out.println("------------------------");
  }

  /**
   * 检查同时存在的手术
   */
  public List<BaSyjlError> checkTogetherOper(List<BaSyjlError> baxyList, List<BBasyCheckBz> bBasyCheckBzList, List<BaSsjl> baSsjlList, String checkCondition) {
    for (BBasyCheckBz basyCheckBz : bBasyCheckBzList) {

      boolean hasFirstOper = false;
      boolean hasSecondOper = false;

      String[] operCodes = basyCheckBz.getComments().split("&");
      if (operCodes.length < 2) {
        continue;
      }

      Set<String> firstOperCodes = new HashSet<>(Arrays.asList(operCodes[0].split(",")));
      for (String operCode : firstOperCodes) {
        if ((!checkCondition.contains("zss") && baSsjlList.stream().anyMatch(oper -> equalStr(operCode, oper.getSsbm())))
          || (checkCondition.contains("zss") && equalStr(operCode, baSsjlList.get(0).getSsbm()))) {
          hasFirstOper = true;
          break;
        }
      }

      if (hasFirstOper) {
        Set<String> secondOperCodes = new HashSet<>(Arrays.asList(operCodes[1].split(",")));
        for (String operCode : secondOperCodes) {
          if (baSsjlList.stream().anyMatch(oper -> equalStr(operCode, oper.getSsbm()))) {
            hasSecondOper = true;
            break;
          }
        }

        if (("sstogether".equals(checkCondition) && !hasSecondOper) ||
          ((("zssnottogether".equals(checkCondition) || "ssnottogether".equals(checkCondition)) && hasSecondOper))) {
          addError(baxyList, checkCondition, basyCheckBz.getTip(), "0", "1", checkCondition, 0, 0, null);
        }
      }

    }
    return baxyList;
  }

  /**
   * 检查同时存在的诊断
   * 判断together时，若包含第一个诊断而未包含第二个诊断，则进行记录
   * 判断zzdnottogether时，若主要诊断为第一个诊断且存在第二个诊断，则进行记录
   * 判断nottogether时，若同时包含两个诊断，则进行记录
   */
  public List<BaSyjlError> checkTogeterDiag(List<BaSyjlError> baxyList, List<BBasyCheckBz> bBasyCheckBzList, List<BaBrzdxx> baBrzdxxList, String checkCondition) {
    for (BBasyCheckBz basyCheckBz : bBasyCheckBzList) {

      boolean hasFirstDiag = false;
      boolean hasSecondDiag = false;

      String[] diagCodes = basyCheckBz.getComments().split("&");
      if (diagCodes.length < 2) {
        continue;
      }

      Set<String> firstDiagCodes = new HashSet<>(Arrays.asList(diagCodes[0].split(",")));
      for (String diagCode : firstDiagCodes) {
        if ((!checkCondition.contains("zzd") && baBrzdxxList.stream().anyMatch(diag -> equalStr(diagCode, diag.getJbbm())))
          || (checkCondition.contains("zzd") && equalStr(diagCode, baBrzdxxList.get(0).getJbbm()))) {
          hasFirstDiag = true;
          break;
        }
      }

      if (hasFirstDiag) {
        Set<String> secondDiagCodes = new HashSet<>(Arrays.asList(diagCodes[1].split(",")));
        for (String diagCode : secondDiagCodes) {
          if (baBrzdxxList.stream().anyMatch(diag -> equalStr(diagCode, diag.getJbbm()))) {
            hasSecondDiag = true;
            break;
          }
        }

        if (("together".equals(checkCondition) && !hasSecondDiag) ||
          ((("zzdnottogether".equals(checkCondition) || "nottogether".equals(checkCondition)) && hasSecondDiag))) {
          addError(baxyList, checkCondition, basyCheckBz.getTip(), "0", "1", checkCondition, 0, 0, null);
        }
      }

    }
    return baxyList;
  }

  /**
   * 检查诊断编码问题
   */
//  public List<BaSyjlError> checkDiagInfo(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList, BaSyjl basy) {
public List<BaSyjlError> checkDiagInfo(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList, BaSyjl basy) {

    HashSet<String> uniqueDiagCodes = new HashSet<>();
    for (BaBrzdxx diag : baBrzdxxList) {

      if (diag == null) {
        continue;
      }

      String diagCode = ifNull(diag.getJbbm());
      String diagName = ifNull(diag.getZdmc());

      if (diagCode != null && !uniqueDiagCodes.add(diagCode)) {
        addError(baxyList, "", "诊断[" + diagCode + "]存在重复记录", "0", "1", "jbbm", 0, 0, null);
      }

      if (StringUtils.isBlank(diagCode) || StringUtils.isBlank(diagName)) {
        addError(baxyList, "", "诊断次序为" + diag.getZdcx() + "的诊断编码或名称漏填", "0", "1", "jbbm", 0, 0, null);
      }

      if (!"".equals(diagCode)) {
        Integer hmbz = baBrzdxxService.selectIcd10CodeHm(diag);
        if (hmbz != null && hmbz >= 1) {
          addError(baxyList, "", "诊断" + diagCode + "[" + diagName  + "]为灰码", "0", "1", "hm", 0, 0, null);
        }
      }

      for (IcdStopUse icdStopUse : stopUseIcd10List) {
        if (icdStopUse.getBm().equals(diagCode) ) {
          addError(baxyList, "", "诊断" + diagCode + "[" + diagName  + "]已停用", "0", "1", "stop_use_icd", 0, 0, null);
          break;
        }
      }

    }

    return baxyList;
  }

  /**
   * 检查手术编码问题
   */
  public List<BaSyjlError> checkOperInfo(List<BaSyjlError> baxyList, List<BaSsjl> baSsjlList, BaSyjl basy) {
    for (BaSsjl operation : baSsjlList) {
      if (operation == null) {
        continue;
      }

      String operCode = ifNull(operation.getSsbm());
      String operName = ifNull(operation.getSsmc());

      if ("".equals(operCode) || "".equals(operName)) {
        addError(baxyList, "", "手术次序为" + operation.getSscx() + "的手术编码或名称漏填", "0", "1", "ssbm", 0, 0, null);
      }

      if (!"".equals(operCode)) {
        Integer hmbz = baSsjlService.selectIcd9CodeHm(operation);
        if (hmbz != null && hmbz >= 1) {
          addError(baxyList, "", "手术" + operCode + "[" + operName  + "]为灰码", "0", "1", "hm", 0, 0, null);
        }
      }

      for (IcdStopUse icdStopUse : stopUseIcd9List) {
        if (operCode.equals(icdStopUse.getBm()) && operName.equals(icdStopUse.getMc())) {
          addError(baxyList, "", "手术" + operCode + "[" + operName  + "]已停用", "0", "1", "stop_use_icd", 0, 0, null);
          break;
        }
      }
    }
    return baxyList;
  }


  public void addError(List<BaSyjlError> baxyList, String code, String errordes, String trust, String errortype, String field, Integer effectdrg, Integer displayorder, Double score) {
    BaSyjlError baSyjlError = new BaSyjlError(code, errordes, trust, errortype, field, effectdrg, displayorder, score);
    baxyList.add(baSyjlError);
  }

  public void addError(List<BaSyjlError> baxyList, String code, String errordes, String trust, String errortype, String field, Integer effectdrg, Integer displayorder, Double score, BigDecimal je) {
    BaSyjlError baSyjlError = new BaSyjlError(code, errordes, trust, errortype, field, effectdrg, displayorder, score, je);
    baxyList.add(baSyjlError);
  }

  public void addError(List<BaSyjlError> baxyList, String code, String errordes, String trust, String errortype, String field, Integer effectdrg, Integer displayorder, Double score, Integer islink, String likeDate, String tipObject) {
    BaSyjlError baSyjlError = new BaSyjlError(code, errordes, trust, errortype, field, effectdrg, displayorder, score, islink, likeDate, tipObject);
    baxyList.add(baSyjlError);
  }

  public LocalDate convertToLocalDate(Date date) {
    return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
  }

  /**
   * 配合一键编码对诊断进行性别限定检查
   */
  public List<String> check_sex_code(List<BaBrzdxx> zdxxList, String sex) {
    if (!isInit) {
      initCheckRules();
    }

    List<String> errCodeList = new ArrayList<>();

    for (BaBrzdxx zd : zdxxList) {
      if (StrUtil.isBlank(zd.getJbbm())) {
        continue;
      }
      if ("男".equals(sex)) {
        for (BBasyCheckBz bz : woman_list) {
          if (StrUtil.isBlank(bz.getComments())) {
            continue;
          }
          if (zd.getJbbm().equals(bz.getComments())) {
            errCodeList.add(zd.getJbbm());
          }
        }
      } else if ("女".equals(sex)) {
        for (BBasyCheckBz bz : man_list) {
          if (StrUtil.isBlank(bz.getComments())) {
            continue;
          }
          if (zd.getJbbm().equals(bz.getComments())) {
            errCodeList.add(zd.getJbbm());
          }
        }
      }

    }

    return errCodeList;
  }


  /**
   * 主要诊断不能为后遗症
   */
  public List<BaSyjlError> checkSequelae(List<BaSyjlError> baxyList, String mainDiagName) {
    if (mainDiagName.contains("后遗症")) {
      addError(baxyList, "", "后遗症一般不作为主要诊断", "0", "1", "zyzd", 0, 0, 0.0);
    }
    return baxyList;
  }

  /**
   * 检查主要手术
   */
  public List<BaSyjlError> checkMainOperCode(List<BaSyjlError> baxyList, String mainOperCode) {
    for (BBasyCheckBz bBasyCheckBz : notzss_list) {
      String[] operCodes = bBasyCheckBz.getComments().split(",");
      for (String operCode : operCodes) {
        if (equalStr(operCode, mainOperCode)) {
          addError(baxyList, "notzss", bBasyCheckBz.getTip(), "0", "1", "notzss", 0, 0, null);
          break;
        }
      }
    }
    return baxyList;

  }

  /**
   * 检查年龄相关内容判断
   */
  public List<BaSyjlError> checkAgeContent(List<BaSyjlError> baxyList, BaSyjl basy, List<BaBrzdxx> baBrzdxxList) {
    Long nl = basy.getNl();

    if (nl > 150) {
      addError(baxyList, "", "年龄不能大于150岁", "0", "0", "nl", 0, 0, null);
    }

    //当患者年龄小于30岁时，不能出现H25老年性白内障
    //当患者年龄小于15岁时，应更改编码J40为J20
    if (nl < 35) {
      for (BaBrzdxx diag : baBrzdxxList) {
        String diagStartThreeCode = getStartStr(diag.getJbbm(), 3);
        String diagStartFirstCode = getStartStr(diag.getJbbm(), 1);
        if ("H25".equals(diagStartThreeCode)) {
          addError(baxyList, "", "当患者年龄小于30岁时，不能出现H25老年性白内障", "0", "1", "jbbm", 0, 0, null);
        }
        if (nl < 15 && "J40".equals(diagStartThreeCode)) {
          addError(baxyList, "", "当患者年龄小于15岁时，应更改编码J40为J20", "0", "1", "jbbm", 0, 0, null);
        }
        if (nl > 5 && "P".equals(diagStartFirstCode)) {
          addError(baxyList, "", "5岁以下儿童才可编码P00-P96", "0", "1", "jbbm", 0, 0, null);
        }
      }
    }

//    if (nl >= 3 && nl <= 15 && !"学生".equals(basy.getZy())) {
//      addError(baxyList, "", "患者年龄在3~15岁时，职业必须为学生", "0", "0", "zy", 0, 0, null);
//    }

    //年龄与出生日期不符
    if (basy.getCsrqDate() != null && nl != null && basy.getRysjDate() != null) {
      int age = Period.between(basy.getCsrqDate(), basy.getRysjDate()).getYears();
      if (age != nl && age != nl - 1 && age != nl + 1) {
        addError(baxyList, "", "年龄与出生日期不符", "0", "0", "nl", 0, 0, null);
      }
    }

    //检查婚姻状况
    if ("已婚".equals(basy.getHy())) {
      if ("男".equals(basy.getXb()) && nl < 22) {
        addError(baxyList, "", "男性不满22周岁，婚姻状况不能为已婚", "0", "0", "nl", 0, 0, null);
      } else if ("女".equals(basy.getXb()) && nl < 20) {
        addError(baxyList, "", "女性不满20周岁，婚姻状况不能为已婚", "0", "0", "nl", 0, 0, null);
      }
    }

    if ("未婚".equals(basy.getHy()) && "配偶".equals(basy.getGx())) {
      addError(baxyList, "", "当婚姻状态为未婚时，联系人与患者关系不为配偶", "0", "0", "gx", 0, 0, null);
    }

    return baxyList;
  }


  /**
   * 检查血型填写是否合理
   */
  public List<BaSyjlError> checkBloodType(List<BaSyjlError> baxyList, BaSyjl basy, List<BaSsjl> baSsjlList) {
    String xx = ifNull(basy.getXx());
    String rh = ifNull(basy.getRh());
    BigDecimal xf = basy.getXf() == null ? BigDecimal.ZERO : basy.getXf();

    //根据血型号检查RH值
    if (("1".equals(xx) || "2".equals(xx) || "3".equals(xx) || "4".equals(xx)) && (!"1".equals(rh) && !"2".equals(rh))) {
      addError(baxyList, "", "血型为1[A型]、2[B型]、3[O型]、4[AB型]时，RH应为1[阴]或2[阳]", "0", "0", "rh", 0, 0, null);
    }

    //血费大于0，血型不能为空
    if (xf.compareTo(BigDecimal.ZERO) > 0 && "".equals(xx)) {
      addError(baxyList, "", "血费大于0，血型不能为空", "0", "0", "xx", 0, 0, null);
    }

    //血型5[不详]，RH应为3[不详]
//    if ("5".equals(xx) && !"3".equals(rh)) {
//      addError(baxyList, "", "血型5[不详]，RH应为3[不详]", "0", "0", "xx", 0, 0, null);
//    }

    //99.00-99.07[输血和血液成分]，需填写血型和RH
    for (BaSsjl baSsjl : baSsjlList) {
      String operStartFiveCode = getStartStr(baSsjl.getSsbm(), 5);
      if (BLOOD_CODES.contains(operStartFiveCode) && ("".equals(xx) || "".equals(rh))) {
        addError(baxyList, "", "99.00-99.07[输血和血液成分]，需填写血型和RH", "0", "0", "".equals(xx) ? "xx" : "rh", 0, 0, null);
        break;
      }
    }

    //血型6[未查]，RH应为4[未查]
//    if ("6".equals(xx) && !"4".equals(rh)) {
//      addError(baxyList, "", "血型6[未查]，RH应为4[未查]", "0", "0", "xx", 0, 0, null);
//    }

    return baxyList;
  }


  /**
   * 检查出院科室限制的编码
   */
  public List<BaSyjlError> checkDeptLimitCode(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList, String dept) {
    for (BBasyCheckBz bBasyCheckBz : dept_list) {
      if (bBasyCheckBz.getDept() != null && dept.equals(bBasyCheckBz.getDept())) {
        String[] limitCodes = bBasyCheckBz.getComments().split(",");
        for (String limitCode : limitCodes) {
          if (baBrzdxxList.stream().anyMatch(diag -> equalStr(limitCode, diag.getJbbm()))) {
            addError(baxyList, "dept", bBasyCheckBz.getTip(), "0", "1", "dept", 0, 0, null);
            break;
          }
        }
      }
    }
    return baxyList;
  }


  /**
   * 检查病理相关内容填写是否合理
   *
   * @return
   */
  public List<BaSyjlError> checkPathologyDiag(List<BaSyjlError> baxyList, BaSyjl basy, String mainDiagCode) {

    // 病理号在病理费＞100元时未填写（D）
    if (basy.getBlzdf() != null && basy.getBlh() == null && basy.getBlzdf().compareTo(BigDecimal.valueOf(100)) >= 1) {
      addError(baxyList, "must", "病理号在病理费＞100元时未填写（D）", "0", "0", "blh", 0, 0, 0.5);
    }

    if ("1".equals(zkBlzd)) {
      String diagStartOneCode = getStartStr(mainDiagCode, 1);
      String diagStartThreeCode = getStartStr(mainDiagCode, 3);
      //病理诊断编码
      String pathologyDiagCode = ifNull(basy.getJbmm());

      // 病理不能为空
      if ("C".equals(diagStartOneCode) || ("D".equals(diagStartOneCode) && Integer.parseInt(mainDiagCode.substring(1, 3)) >= 00 && Integer.parseInt(mainDiagCode.substring(1, 3)) <= 48)) {
        if (basy.getBlzd() == null || "".equals(basy.getBlzd())) {
          addError(baxyList, "must", "主要诊断为肿瘤时，病理诊断漏填", "0", "0", "blzd", 0, 2, 0.5);
        }
        if ("".equals(pathologyDiagCode)) {
          addError(baxyList, "must", "主要诊断为肿瘤时，病理诊断疾病编码漏填", "0", "0", "jbmm", 0, 2, 0.5);
        }
        if (basy.getBlh() == null || "".equals(basy.getBlh())) {
          addError(baxyList, "must", "主要诊断为肿瘤时，病理号漏填", "0", "0", "blh", 0, 2, 0.5);
        }

        if (("C76".equals(diagStartThreeCode) || "C77".equals(diagStartThreeCode) || "C80".equals(diagStartThreeCode)) && !pathologyDiagCode.endsWith("/6") && !pathologyDiagCode.endsWith("/3")) {
          addError(baxyList, "", "主要诊断是" + diagStartThreeCode + "时，其病理肿瘤诊断的动态代码必须是/3或/6", "0", "0", "jbmm", 0, 0, null);
        } else {
          PATHOLOGYDIAG_MAINDIAG.forEach((key, value) -> {
            if (value.contains(diagStartThreeCode) && !pathologyDiagCode.endsWith(key)) {
              addError(baxyList, "", "主要诊断是" + diagStartThreeCode + "时，其病理肿瘤诊断的动态代码必须是" + key, "0", "0", "jbmm", 0, 0, null);
            }
          });
        }

      } else {
        //判断是否为肿瘤病理编码（M/)
        if (pathologyDiagCode.length() > 2 && pathologyDiagCode.startsWith("M") && pathologyDiagCode.charAt(pathologyDiagCode.length() - 2) == '/') {

          addError(baxyList, "", "当存在肿瘤病理编码（M/)时，主要诊断编码范围为C00~D48", "0", "0", "jbdm", 0, 0, null);


          SPECIAL_PATHOLOGYDIAG_MAINDIAG.forEach((key, value) -> {
            for (String s : key) {
              if (isPrefixAndSuffixMatch(pathologyDiagCode, s.split("/")[0], "/" + s.split("/")[1]) && !value.stream().anyMatch(item -> equalStr(item,mainDiagCode))) {
                addError(baxyList, "", "病理诊断为" + key + "时，其主要诊断应为" + value.toString(), "0", "0", "jbdm", 0, 0, null);
              }
            }
          });

          PATHOLOGYDIAG_MAINDIAG.forEach((key, value) -> {
            if (pathologyDiagCode.endsWith(key) && !value.contains(diagStartThreeCode)) {
              addError(baxyList, "", "病理肿瘤诊断的动态代码是" + key + "时，主要诊断必须是" + value.get(0) + "-" + value.get(value.size() - 1) +
                (((key.equals("/3") || key.equals("/6"))) ? ",C76,C77,C80" : ""), "0", "0", "jbdm", 0, 0, null);
            }
          });
        }

      }
    }

    return baxyList;
  }


  /**
   * 检查编码优先
   */
  public List<BaSyjlError> checkCodePriority(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList) {
    for (BBasyCheckBz bBasyCheckBz : priority_list) {
      String[] checkCodes = bBasyCheckBz.getComments().split("&");
      if (checkCodes.length < 3) {
        continue;
      }

      List<String> fistCodes = Arrays.asList(checkCodes[0].split(","));
      List<String> secondCodes = Arrays.asList(checkCodes[1].split(","));
      String checkTarget = checkCodes[2];

      boolean hasFistCode = false;
      boolean hasSecondCode = false;

      for (BaBrzdxx baBrzdxx : baBrzdxxList) {
        String diagCode = baBrzdxx.getJbbm();
        if (fistCodes.stream().anyMatch(code -> equalStr(code,diagCode))) {
          hasFistCode = true;
          break;
        }
      }

      if (hasFistCode) {
        for (BaBrzdxx baBrzdxx : baBrzdxxList) {
          String diagCode = baBrzdxx.getJbbm();
          if (secondCodes.stream().anyMatch(code -> equalStr(code,diagCode))) {
            hasSecondCode = true;
            break;
          }
        }

        if (hasSecondCode) {
          String mainDiagCode = baBrzdxxList.get(0).getJbbm();
          if ("1".equals(checkTarget)) {
            if (secondCodes.stream().anyMatch(code -> equalStr(code,mainDiagCode))) {
              addError(baxyList, "", bBasyCheckBz.getTip(), "0", "1", "jbbm", 0, 0, null);
            }

          } else if ("2".equals(checkTarget)) {
            if (fistCodes.stream().anyMatch(code -> equalStr(code,mainDiagCode))) {
              addError(baxyList, "", bBasyCheckBz.getTip(), "0", "1", "jbbm", 0, 0, null);
            }
          }
        }
      }

    }


    return baxyList;
  }


  /**
   * 获取字符串str前strLen位
   *
   * @param str
   * @param len
   * @return
   */
  public String getStartStr(String str, int len) {
    return str.length() > len ? str.substring(0, len) : str;
  }

  /**
   * 判断字符串前缀与后缀与条件是否相同
   *
   * @param str
   * @param prefix
   * @param suffix
   * @return
   */
  public boolean isPrefixAndSuffixMatch(String str, String prefix, String suffix) {
    return str.startsWith(prefix) && str.endsWith(suffix);
  }


  /**
   * 计算天龄
   */
  public Long getDayAge(LocalDate date,Long month) {
    if (month == null || date == null) {
      return null;
    }
    try {
      LocalDate endDate = date.plusMonths(month);
      Long daysBetween = ChronoUnit.DAYS.between(date, endDate);
      return daysBetween;
    } catch (Exception e) {
      return null;
    }
  }


  /**
   * 检查诊断编码问题
   */
  public List<BaSyjlError> checkYbDiagInfo(List<BaSyjlError> baxyList, List<BaBrzdxx> baBrzdxxList) {
    HashSet<String> uniqueDiagCodes = new HashSet<>();
    for (BaBrzdxx diag : baBrzdxxList) {
      if (diag == null) {
        continue;
      }
      String diagCode = ifNull(diag.getJbbm());
      if (diagCode != null && !uniqueDiagCodes.add(diagCode)) {
        addError(baxyList, "", "医保码[" + diagCode + "]存在重复记录", "0", "1", "jbbm", 0, 0, null);
      }
    }
    return baxyList;
  }

  /**
   * 3.2 住院标准规则校验 - 低标准住院检测
   * 根据开发文档3.2节实现各类疾病的低标准住院检测规则
   */
  public List<BaSyjlError> checkLowStandardHospitalization(List<BaSyjlError> baxyList, BaSyjl basy,
                                                          List<BaBrzdxx> baBrzdxxList, List<BaSsjl> baSsjlList) {
    if (baBrzdxxList.isEmpty()) {
      return baxyList;
    }

    String mainDiagCode = baBrzdxxList.get(0).getJbbm();
    String lyfs = ifNull(basy.getLyfs());
    Long sjzyts = basy.getSjzyts();
    Long nl = basy.getNl();
    String zyzt = ifNull(basy.getZyzt());

    // 总费用
    BigDecimal totalFee = basy.getZfy() == null ? BigDecimal.ZERO : basy.getZfy();
    // 总药费
    BigDecimal drugFee = (basy.getXyf() == null ? BigDecimal.ZERO : basy.getXyf())
        .add(basy.getZcyf() == null ? BigDecimal.ZERO : basy.getZcyf())
        .add(basy.getZcyf1() == null ? BigDecimal.ZERO : basy.getZcyf1());
    // 检验 ( 病理诊断费 + 实验室诊断费
    BigDecimal jyf = (basy.getSyszdf() == null ? BigDecimal.ZERO : basy.getSyszdf())
        .add(basy.getBlzdf() == null ? BigDecimal.ZERO : basy.getBlzdf());
    // 检查 ( 影像学诊断费 临床诊断项目费
    BigDecimal jcf = (basy.getYxxzdf() == null ? BigDecimal.ZERO : basy.getYxxzdf())
        .add(basy.getLczdxmf() == null ? BigDecimal.ZERO : basy.getLczdxmf());

    double drugRatio = totalFee.compareTo(BigDecimal.ZERO) > 0 ?
        drugFee.divide(totalFee, 4, RoundingMode.HALF_UP).doubleValue() * 100 : 0;
    // 计算检查占比
    double jczb = totalFee.compareTo(BigDecimal.ZERO) > 0 ?
      jcf.divide(totalFee, 4, RoundingMode.HALF_UP).doubleValue() * 100 : 0;
    // 计算检验占比
    double jyzb = totalFee.compareTo(BigDecimal.ZERO) > 0 ?
      jyf.divide(totalFee, 4, RoundingMode.HALF_UP).doubleValue() * 100 : 0;
    // 检验检查共占比
    double jyjczb = totalFee.compareTo(BigDecimal.ZERO) > 0 ?
      jcf.add(jyf).divide(totalFee, 4, RoundingMode.HALF_UP).doubleValue() * 100 : 0;

    // 检查是否有大手术（手术级别≥3级）
    boolean hasMajorSurgery = baSsjlList.stream()
        .anyMatch(surgery -> surgery.getSsjb() != null &&
                  (surgery.getSsjb().equals("3") || surgery.getSsjb().equals("4")));

    // 规则3：椎间盘突出类疾病低标准住院检测
    if (mainDiagCode.startsWith("M50") || mainDiagCode.startsWith("M51")) {
      if (("医嘱离院".equals(lyfs) || "0".equals(zyzt)) && drugRatio <= 5 && sjzyts != null && sjzyts <= 7 && !hasMajorSurgery) {
        addError(baxyList, "low_standard", "疑似椎间盘突出类疾病低标准住院：药占比≤5%，住院天数≤7天",
                "0", "0", "special_rule", 0, 0, 2.0);
      }
    }

    // 规则4：耳鼻喉科类疾病低标准住院检测
    if (mainDiagCode.startsWith("H6") || mainDiagCode.startsWith("H7") ||
        mainDiagCode.startsWith("H8") || mainDiagCode.startsWith("H9")) {
      // 排除急症病例
      boolean isEmergency = mainDiagCode.contains("J38.5") || // 喉痉挛
                           mainDiagCode.contains("J05.1") || // 急性会厌炎
                           mainDiagCode.contains("J38.6");   // 喉水肿
      if (("医嘱离院".equals(lyfs) || "0".equals(zyzt)) && drugRatio <= 5 && sjzyts != null && sjzyts <= 3 && !isEmergency) {
        addError(baxyList, "low_standard", "疑似耳鼻喉科类疾病低标准住院：药占比≤5%，住院天数≤3天",
                "0", "0", "special_rule", 0, 0, 2.0);
      }
    }

    // 规则5：泌尿外科类疾病低标准住院检测
    if (mainDiagCode.startsWith("N")) {
      if ("0".equals(zyzt)  && drugRatio <= 5 && !hasMajorSurgery) {
        addError(baxyList, "low_standard", "疑似泌尿外科类疾病低标准住院：药占比≤5%，无大手术",
                "0", "0", "special_rule", 0, 0, 2.0);
      }
    }

    // 规则6：呼吸道感染类疾病低标准住院检测
    if (mainDiagCode.startsWith("J0") || mainDiagCode.startsWith("J1") || mainDiagCode.startsWith("J2")) {
      // 排除重症肺炎
      boolean isSeverePneumonia = mainDiagCode.contains("J44.0") || // 急性加重的慢性阻塞性肺疾病
                                 mainDiagCode.contains("J15") ||   // 细菌性肺炎
                                 mainDiagCode.contains("J18.9");   // 肺炎，未特指
      if (("医嘱离院".equals(lyfs) || "0".equals(zyzt)) && nl != null && nl >= 16 && drugRatio <= 30 &&
          sjzyts != null && sjzyts <= 5 && !isSeverePneumonia && !hasMajorSurgery) {
        addError(baxyList, "low_standard", "疑似呼吸道感染类疾病低标准住院：年龄≥16岁，药占比≤30%，住院天数≤5天",
                "0", "0", "special_rule", 0, 0, 2.0);
      }
    }

    // 规则7：胃肠消化科类疾病低标准住院检测
    if (mainDiagCode.startsWith("K")) {
      if (("医嘱离院".equals(lyfs) || "0".equals(zyzt)) && sjzyts != null && sjzyts <= 3) {
        // 基于DRG分组的动态费用标准比较（简化实现）
        if (drugRatio <= 15) { // 简化的药占比标准
          addError(baxyList, "low_standard", "疑似胃肠消化科类疾病低标准住院：药占比过低，住院天数≤3天",
                  "0", "0", "special_rule", 0, 0, 2.0);
        }
      }
    }

    // 规则8：心内科疾病低标准住院检测
    if (mainDiagCode.startsWith("I")) {
      // 排除急危重症
      boolean isCardiacEmergency = mainDiagCode.contains("I50") ||  // 心力衰竭
                                  mainDiagCode.contains("I21") ||  // 急性心肌梗死
                                  mainDiagCode.contains("I46") ||  // 心脏停搏
                                  mainDiagCode.contains("I47") ||  // 阵发性心动过速
                                  mainDiagCode.contains("I48");    // 心房颤动和心房扑动
      if (("医嘱离院".equals(lyfs) || "0".equals(zyzt)) && drugRatio <= 20 && sjzyts != null && sjzyts <= 3 && !isCardiacEmergency) {
        addError(baxyList, "low_standard", "疑似心内科疾病低标准住院：药占比≤20%，住院天数≤3天",
                "0", "0", "special_rule", 0, 0, 2.0);
      }
    }

    // 规则9：肿瘤类疾病低标准住院检测
    // todo 医疗费总额小于该病种同级别标杆参考值
//    if (mainDiagCode.startsWith("C") || mainDiagCode.startsWith("D")) {
//      if ("医嘱离院".equals(lyfs) || "0".equals(zyzt)) {
//        // 检查是否有化疗、放疗相关费用
//        BigDecimal specialTreatmentFee = (basy.getFsszlxmf() == null ? BigDecimal.ZERO : basy.getFsszlxmf())
//            .add(basy.getZyzlf() == null ? BigDecimal.ZERO : basy.getZyzlf());
//        if (drugFee.compareTo(BigDecimal.valueOf(1000)) <= 0 &&
//            specialTreatmentFee.compareTo(BigDecimal.valueOf(500)) <= 0) {
//          addError(baxyList, "low_standard", "疑似肿瘤类疾病低标准住院：药费和特殊治疗费用过低",
//                  "0", "0", "special_rule", 0, 0, 2.0);
//        }
//      }
//    }


    // 规则10：住院药费占比过高检测
    if (drugRatio >= 70 && sjzyts != null && sjzyts <= 5) {
      // 检查是否缺少检查和其他治疗项目
      if (jyf.add(jcf).compareTo(BigDecimal.valueOf(200)) <= 0) {
        addError(baxyList, "high_drug_ratio", "住院药费占比过高：药占比≥70%，住院天数≤5天，缺少检查项目",
                "0", "0", "special_rule", 0, 0, 3.0);
      }
    }

    // 规则11：住院检查、检验费用占比过高检测
    if (jyjczb >= 70 && sjzyts != null && sjzyts <= 5) {
      // 检查费用是否明显高于标准
      if (totalFee.compareTo(BigDecimal.valueOf(3000)) > 0) { // 简化的费用标准

        addError(baxyList, "high_exam_ratio", "住院检查检验费用占比过高：检查占比≥70%，住院天数≤5天",
                "0", "0", "special_rule", 0, 0, 3.0);
      }
    }

    // 规则12：可疑分解入院检测（简化实现）
    if (sjzyts != null && sjzyts <= 7) {
      // 这里可以扩展为更复杂的分解入院检测逻辑
      // 目前简化为短期住院的提示
      if (drugRatio <= 10 && jyjczb <= 10) {
        addError(baxyList, "suspicious_split", "可疑分解入院：住院天数≤7天，药占比和检查占比均过低",
                "0", "0", "special_rule", 0, 0, 1.5);
      }
    }

    return baxyList;
  }

}
