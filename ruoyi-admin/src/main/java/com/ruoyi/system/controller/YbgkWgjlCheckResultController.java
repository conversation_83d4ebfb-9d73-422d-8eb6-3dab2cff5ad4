package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbgkWgjlCheckResult;
import com.ruoyi.system.service.IYbgkWgjlCheckResultService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 违规检查结果Controller
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/gksz/wgjlcheck/result")
public class YbgkWgjlCheckResultController extends BaseController {
  @Autowired
  private IYbgkWgjlCheckResultService ybgkWgjlCheckResultService;

  /**
   * 查询违规检查结果列表
   */
  @GetMapping("/list")
  public TableDataInfo list(YbgkWgjlCheckResult ybgkWgjlCheckResult) {
    startPage();
    List<YbgkWgjlCheckResult> list = ybgkWgjlCheckResultService.selectYbgkWgjlCheckResultList(ybgkWgjlCheckResult);
    return getDataTable(list);
  }

  /**
   * 导出违规检查结果列表
   */
  @Log(title = "违规检查结果", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, YbgkWgjlCheckResult ybgkWgjlCheckResult) {
    List<YbgkWgjlCheckResult> list = ybgkWgjlCheckResultService.selectYbgkWgjlCheckResultList(ybgkWgjlCheckResult);
    ExcelUtil<YbgkWgjlCheckResult> util = new ExcelUtil<YbgkWgjlCheckResult>(YbgkWgjlCheckResult.class);
    util.exportExcel(response, list, "违规检查结果数据");
  }

  /**
   * 获取违规检查结果详细信息
   */
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Long id) {
    return success(ybgkWgjlCheckResultService.selectYbgkWgjlCheckResultById(id));
  }

  /**
   * 新增违规检查结果
   */
  @Log(title = "违规检查结果", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody YbgkWgjlCheckResult ybgkWgjlCheckResult) {
    return toAjax(ybgkWgjlCheckResultService.insertYbgkWgjlCheckResult(ybgkWgjlCheckResult));
  }

  /**
   * 修改违规检查结果
   */
  @Log(title = "违规检查结果", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody YbgkWgjlCheckResult ybgkWgjlCheckResult) {
    return toAjax(ybgkWgjlCheckResultService.updateYbgkWgjlCheckResult(ybgkWgjlCheckResult));
  }

  /**
   * 删除违规检查结果
   */
  @Log(title = "违规检查结果", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Long[] ids) {
    return toAjax(ybgkWgjlCheckResultService.deleteYbgkWgjlCheckResultByIds(ids));
  }
}
