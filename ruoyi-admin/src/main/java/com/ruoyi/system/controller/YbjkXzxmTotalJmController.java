package com.ruoyi.system.controller;

import java.io.IOException;
import java.util.*;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbjkXzxmTotalJm;
import com.ruoyi.system.service.IYbjkXzxmTotalJmService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 所有限制信息-加密Controller
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@RestController
@RequestMapping("/system/xzxmEncode")
public class YbjkXzxmTotalJmController extends BaseController
{
    @Autowired
    private IYbjkXzxmTotalJmService ybjkXzxmTotalJmService;


    /**
     * 查询所有限制信息-加密列表
     */
//    @PreAuthorize("@ss.hasPermi('system:xzxmEncode:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbjkXzxmTotalJm ybjkXzxmTotalJm)
    {
        List<YbjkXzxmTotalJm> list = ybjkXzxmTotalJmService.selectYbjkXzxmTotalJmList(ybjkXzxmTotalJm);
        return getDataTable(list);
    }

    //获取加密限制信息的测试接口，暂弃用
//    @GetMapping("/decode/xzxm")
//    public AjaxResult decodeTest() throws IOException {
//      OkHttpClient client = new OkHttpClient();
//      Response res = client.newCall(new Request.Builder()
//        .url(encodeXzxmHost)
//        .build()).execute();
//
//      String string = res.body().string();
//      Gson gson = new Gson();
//      Map objectStringMap = new HashMap();
//      objectStringMap = gson.fromJson(string, objectStringMap.getClass());
//
//      ybjkXzxmTotalJmService.decodeXzxmAndSave(gson.fromJson(gson.toJson(objectStringMap.get("rows")), List.class));
//      return success();
//    }

    /**
     * 导出所有限制信息-加密列表
     */
//    @PreAuthorize("@ss.hasPermi('system:xzxmEncode:export')")
    @Log(title = "所有限制信息-加密", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbjkXzxmTotalJm ybjkXzxmTotalJm)
    {
        List<YbjkXzxmTotalJm> list = ybjkXzxmTotalJmService.selectYbjkXzxmTotalJmList(ybjkXzxmTotalJm);
        ExcelUtil<YbjkXzxmTotalJm> util = new ExcelUtil<YbjkXzxmTotalJm>(YbjkXzxmTotalJm.class);
        util.exportExcel(response, list, "所有限制信息-加密数据");

    }

    /**
     * 获取所有限制信息-加密详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:xzxmEncode:query')")
    @GetMapping(value = "/{sno}")
    public AjaxResult getInfo(@PathVariable("sno") String sno)
    {
        return success(ybjkXzxmTotalJmService.selectYbjkXzxmTotalJmBySno(sno));
    }

    /**
     * 新增所有限制信息-加密
     */
    @PreAuthorize("@ss.hasPermi('system:xzxmEncode:add')")
    @Log(title = "所有限制信息-加密", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbjkXzxmTotalJm ybjkXzxmTotalJm)
    {
        return toAjax(ybjkXzxmTotalJmService.insertYbjkXzxmTotalJm(ybjkXzxmTotalJm));
    }

    /**
     * 修改所有限制信息-加密
     */
    @PreAuthorize("@ss.hasPermi('system:xzxmEncode:edit')")
    @Log(title = "所有限制信息-加密", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbjkXzxmTotalJm ybjkXzxmTotalJm)
    {
        return toAjax(ybjkXzxmTotalJmService.updateYbjkXzxmTotalJm(ybjkXzxmTotalJm));
    }

    /**
     * 删除所有限制信息-加密
     */
    @PreAuthorize("@ss.hasPermi('system:xzxmEncode:remove')")
    @Log(title = "所有限制信息-加密", businessType = BusinessType.DELETE)
	@DeleteMapping("/{snos}")
    public AjaxResult remove(@PathVariable String[] snos)
    {
        return toAjax(ybjkXzxmTotalJmService.deleteYbjkXzxmTotalJmBySnos(snos));
    }
}
