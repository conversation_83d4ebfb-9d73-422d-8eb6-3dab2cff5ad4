package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.JsxxHisZy;
import com.ruoyi.system.service.IJsxxHisZyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 住院结算信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@RestController
@RequestMapping("/jsxx/jsxxzy")
public class JsxxHisZyController extends BaseController
{
    @Autowired
    private IJsxxHisZyService jsxxHisZyService;

    /**
     * 查询住院结算信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(JsxxHisZy jsxxHisZy)
    {
        startPage();
        List<JsxxHisZy> list = jsxxHisZyService.selectJsxxHisZyList(jsxxHisZy);
        return getDataTable(list);
    }

    /**
     * 导出住院结算信息列表
     */
    @PreAuthorize("@ss.hasPermi('jsxx:jsxxzy:export')")
    @Log(title = "住院结算信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JsxxHisZy jsxxHisZy)
    {
        List<JsxxHisZy> list = jsxxHisZyService.selectJsxxHisZyList(jsxxHisZy);
        ExcelUtil<JsxxHisZy> util = new ExcelUtil<JsxxHisZy>(JsxxHisZy.class);
        util.exportExcel(response, list, "住院结算信息数据");
    }

    /**
     * 获取住院结算信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('jsxx:jsxxzy:query')")
    @GetMapping(value = "/{setlId}")
    public AjaxResult getInfo(@PathVariable("setlId") String setlId)
    {
        return success(jsxxHisZyService.selectJsxxHisZyBySetlId(setlId));
    }

    /**
     * 新增住院结算信息
     */
    @PreAuthorize("@ss.hasPermi('jsxx:jsxxzy:add')")
    @Log(title = "住院结算信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JsxxHisZy jsxxHisZy)
    {
        return toAjax(jsxxHisZyService.insertJsxxHisZy(jsxxHisZy));
    }

    /**
     * 修改住院结算信息
     */
    @PreAuthorize("@ss.hasPermi('jsxx:jsxxzy:edit')")
    @Log(title = "住院结算信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JsxxHisZy jsxxHisZy)
    {
        return toAjax(jsxxHisZyService.updateJsxxHisZy(jsxxHisZy));
    }

    /**
     * 删除住院结算信息
     */
    @PreAuthorize("@ss.hasPermi('jsxx:jsxxzy:remove')")
    @Log(title = "住院结算信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{setlIds}")
    public AjaxResult remove(@PathVariable String[] setlIds)
    {
        return toAjax(jsxxHisZyService.deleteJsxxHisZyBySetlIds(setlIds));
    }
}
