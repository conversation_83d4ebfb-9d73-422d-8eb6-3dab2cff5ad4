package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.IBrxxSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;

@RestController
@Anonymous
@RequestMapping("/system/sync")
public class BrxxSyncController extends BaseController {

  @Autowired
  private IBrxxSyncService syncService;

  @RequestMapping("/syncFzErrBasy")
  public void syncFzErrBasy() {
    syncService.syncFzErrBasy();
  }

  @RequestMapping("/syncAllBasy")
  public void syncAllBasy() {
    syncService.syncAllBasy();
  }


  @GetMapping("/refreshOption")
  public void refreshOption() {
    syncService.refreshOption();
  }

  @RequestMapping("/basySync")
  public AjaxResult basySync(@RequestParam(value = "brbs") String brbs) throws ParseException {
    return toAjax(syncService.basySync(brbs,null));
  }

  @RequestMapping("/brxxSync")
  public AjaxResult brxxSync() throws ParseException {
    return toAjax(syncService.brxxSync());
  }

  @RequestMapping("/fyxxSync")
  public AjaxResult fyxxSync(@RequestParam("brid")String brid,@RequestParam("zyid")String zyid) {
    return toAjax(syncService.fyxxSync(brid,zyid));
  }

  @RequestMapping("/jsxxSync")
  public AjaxResult jsxxSync() {
    return toAjax(syncService.jsxxSync());
  }

  @RequestMapping("/jcxxSync")
  public AjaxResult jcxxSync() {
    syncService.jcxxSync();
    return toAjax(1);
  }

  @RequestMapping("/jyxxSync")
  public AjaxResult jyxxSync() {
    return toAjax(syncService.jyxxSync());
  }


  @RequestMapping("/blxxSync")
  public AjaxResult blxxSync(@RequestParam(value = "brid") String brid,@RequestParam(value = "zyid") String zyid) {
    return toAjax(syncService.blxxSync(brid,zyid));
  }

  @RequestMapping("icdHisSync")
  public AjaxResult icdHisSync() {
    return toAjax(syncService.syncIcdHis());
  }


}
