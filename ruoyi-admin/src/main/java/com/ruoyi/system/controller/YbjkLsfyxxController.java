package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbjkLsfyxx;
import com.ruoyi.system.service.IYbjkLsfyxxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 临时费用信息Controller
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@RestController
@RequestMapping("/system/lsfyxx")
public class YbjkLsfyxxController extends BaseController
{
    @Autowired
    private IYbjkLsfyxxService ybjkLsfyxxService;

    /**
     * 查询临时费用信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:lsfyxx:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbjkLsfyxx ybjkLsfyxx)
    {
        startPage();
        List<YbjkLsfyxx> list = ybjkLsfyxxService.selectYbjkLsfyxxList(ybjkLsfyxx);
        return getDataTable(list);
    }

    /**
     * 导出临时费用信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:lsfyxx:export')")
    @Log(title = "临时费用信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbjkLsfyxx ybjkLsfyxx)
    {
        List<YbjkLsfyxx> list = ybjkLsfyxxService.selectYbjkLsfyxxList(ybjkLsfyxx);
        ExcelUtil<YbjkLsfyxx> util = new ExcelUtil<YbjkLsfyxx>(YbjkLsfyxx.class);
        util.exportExcel(response, list, "临时费用信息数据");
    }

    /**
     * 获取临时费用信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:lsfyxx:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ybjkLsfyxxService.selectYbjkLsfyxxById(id));
    }

    /**
     * 新增临时费用信息
     */
    @PreAuthorize("@ss.hasPermi('system:lsfyxx:add')")
    @Log(title = "临时费用信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbjkLsfyxx ybjkLsfyxx)
    {
        return toAjax(ybjkLsfyxxService.insertYbjkLsfyxx(ybjkLsfyxx));
    }

    /**
     * 修改临时费用信息
     */
    @PreAuthorize("@ss.hasPermi('system:lsfyxx:edit')")
    @Log(title = "临时费用信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbjkLsfyxx ybjkLsfyxx)
    {
        return toAjax(ybjkLsfyxxService.updateYbjkLsfyxx(ybjkLsfyxx));
    }

    /**
     * 删除临时费用信息
     */
    @PreAuthorize("@ss.hasPermi('system:lsfyxx:remove')")
    @Log(title = "临时费用信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ybjkLsfyxxService.deleteYbjkLsfyxxByIds(ids));
    }
}
