package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbgkWgjl;
import com.ruoyi.system.domain.YbgkWgjlCheckRecord;
import com.ruoyi.system.domain.YbgkWgjlGdjc;
import com.ruoyi.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 违规查询Controller
 *
 * <AUTHOR>
 * @date 2023-07-05
 */
@RestController
@RequestMapping("/gksz/wgjl")
public class YbgkWgjlController extends BaseController {
  @Autowired
  private IYbgkWgjlService ybgkWgjlService;

  /**
   * 查询住院违规查询列表
   */
  @GetMapping("/list")
  public TableDataInfo list(YbgkWgjl ybgkWgjl) {
    startPage();
    List<YbgkWgjl> list = ybgkWgjlService.selectYbgkWgjlListByHospital(ybgkWgjl);
    return getDataTable(list);
  }

  @GetMapping("/listys")
  public TableDataInfo listys(YbgkWgjl ybgkWgjl) {
    List<YbgkWgjl> list = ybgkWgjlService.selectYbgkWgjlys(ybgkWgjl);
    return getDataTable(list);
  }

  @GetMapping("/listks")
  public TableDataInfo listks(YbgkWgjl ybgkWgjl) {
    List<YbgkWgjl> list = ybgkWgjlService.selectYbgkWgjlks(ybgkWgjl);
    return getDataTable(list);
  }

  @RequestMapping("/sendWgklCheck")
  public AjaxResult sendWgklCheck(@RequestBody YbgkWgjlCheckRecord ybgkWgjlCheckRecord) {
    return toAjax(ybgkWgjlService.sendWgklCheck(ybgkWgjlCheckRecord));
  }


  @GetMapping("/gdjclist")
  public TableDataInfo selectYbgkWgjlGdjc(YbgkWgjlGdjc ybgkWgjl) {
    startPage();
    List<YbgkWgjlGdjc> list = ybgkWgjlService.selectYbgkWgjlGdjc(ybgkWgjl);
    return getDataTable(list);
  }

  @Log(title = "过度检查违规记录导出", businessType = BusinessType.EXPORT)
  @PostMapping("/exportGdjc")
  public void exportGdjc(HttpServletResponse response, YbgkWgjlGdjc ybgkWgjl) {
    List<YbgkWgjlGdjc> list = ybgkWgjlService.selectYbgkWgjlGdjc(ybgkWgjl);
    ExcelUtil<YbgkWgjlGdjc> util = new ExcelUtil<>(YbgkWgjlGdjc.class);
    util.exportExcel(response, list, "过度检查违规记录");
  }


  /**
   * 查询门诊违规查询列表
   */
  @GetMapping("/list2")
  public TableDataInfo list2(YbgkWgjl ybgkWgjl) {
    startPage();
    List<YbgkWgjl> list = ybgkWgjlService.selectYbgkWgjlListByOutpatient(ybgkWgjl);
    return getDataTable(list);
  }

  @GetMapping("/selectCheckWgjl")
  public TableDataInfo selectCheckWgjl(YbgkWgjl ybgkWgjl) {
    startPage();
    List<YbgkWgjl> list = ybgkWgjlService.selectCheckWgjl(ybgkWgjl);
    return getDataTable(list);
  }


  @Log(title = "违规查询", businessType = BusinessType.EXPORT)
  @PostMapping("/checkWgjlExport")
  public void checkWgjlExport(HttpServletResponse response, YbgkWgjl ybgkWgjl) {
    List<YbgkWgjl> list = ybgkWgjlService.selectCheckWgjl(ybgkWgjl);
    ExcelUtil<YbgkWgjl> util = new ExcelUtil<YbgkWgjl>(YbgkWgjl.class);
    util.exportExcel(response, list, "违规查询数据");
  }

  /**
   * 导出住院违规查询列表
   */
  @Log(title = "违规查询", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, YbgkWgjl ybgkWgjl) {
    List<YbgkWgjl> list = ybgkWgjlService.selectYbgkWgjlListByHospital(ybgkWgjl);
    ExcelUtil<YbgkWgjl> util = new ExcelUtil<YbgkWgjl>(YbgkWgjl.class);
    util.exportExcel(response, list, "违规查询数据");
  }

  /**
   * 导出门诊违规查询列表
   */
  @Log(title = "违规查询", businessType = BusinessType.EXPORT)
  @PostMapping("/export2")
  public void export2(HttpServletResponse response, YbgkWgjl ybgkWgjl) {
    List<YbgkWgjl> list = ybgkWgjlService.selectYbgkWgjlListByOutpatient(ybgkWgjl);
    ExcelUtil<YbgkWgjl> util = new ExcelUtil<YbgkWgjl>(YbgkWgjl.class);
    util.exportExcel(response, list, "违规查询数据");
  }

  /**
   * 获取违规查询详细信息
   */
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") String id) {
    return success(ybgkWgjlService.selectYbgkWgjlById(id));
  }

  /**
   * 新增违规查询
   */
  @Log(title = "违规查询", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody YbgkWgjl ybgkWgjl) {
    return toAjax(ybgkWgjlService.insertYbgkWgjl(ybgkWgjl));
  }

  /**
   * 修改违规查询
   */
  @Log(title = "违规查询", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody YbgkWgjl ybgkWgjl) {
    return toAjax(ybgkWgjlService.updateYbgkWgjl(ybgkWgjl));
  }

  /**
   * 删除违规查询
   */
  @Log(title = "违规查询", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable String[] ids) {
    return toAjax(ybgkWgjlService.deleteYbgkWgjlByIds(ids));
  }

}
