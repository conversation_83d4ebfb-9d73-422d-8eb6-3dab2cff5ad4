package com.ruoyi.system.controller;

import cn.hutool.poi.excel.sax.SheetDataSaxHandler;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.components.annotation.DeptCheck;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.domain.BaSyjl;
import com.ruoyi.system.domain.SysUserOnline;
import com.ruoyi.system.domain.vo.BaZbfx;
import com.ruoyi.system.domain.vo.BlQzYkxx;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.service.*;
import com.ruoyi.tools.ExcelExp;
import com.ruoyi.tools.ExcelUtilManySheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 统计分析Controller
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@RestController
@RequestMapping("/tjfx/tjfx")
public class TjfxController extends BaseController {
  @Autowired
  private ITjfxService tjfxService;

  @Autowired
  private TokenService tokenService;

  @Autowired
  private IJsxxHisService jsxxHisService;

  @Autowired
  private ISysUserService sysUserService;

  @Autowired
  private IBaSyjlService  baSyjlService;

  @Autowired
  private IYbgkWgjlService ybgkWgjlService;

  @Autowired
  private IHUserDeptService hUserDeptService;

  @Autowired
  private RedisCache redisCache;
  /**
   * 查询病例权重-盈亏图列表
   */
  @GetMapping("/getBlqzYkxx")
  public TableDataInfo list(BlQzYkxx blqzYkxx)
  {
    List<BlQzYkxx> list = tjfxService.selectBlqzYkxx(blqzYkxx);
    return getDataTable(list);
  }

  @PostMapping("/export/getBlqzYkxx")
  public void exportBlqzYkxx(HttpServletResponse response, BlQzYkxx blqzYkxx) throws IOException {
    List<BlQzYkxx> list = tjfxService.selectBlqzYkxx(blqzYkxx);

    Double cmiDouble = tjfxService.selectAvgCMI();
    BigDecimal avgCMI = new BigDecimal(cmiDouble);

    // 战略病组
    List<BlQzYkxx> zlbz = list.stream().filter(item -> item.getZyk().compareTo(new BigDecimal("0.00")) > 0 && item.getZfqz().compareTo(avgCMI) > 0).collect(Collectors.toList());
    // 关注病组
    List<BlQzYkxx> gzbz = list.stream().filter(item -> item.getZyk().compareTo(new BigDecimal("0.00")) < 0 && item.getZfqz().compareTo(avgCMI) > 0).collect(Collectors.toList());
    // 优势病组
    List<BlQzYkxx> ysbz = list.stream().filter(item -> item.getZyk().compareTo(new BigDecimal("0.00")) > 0 && item.getZfqz().compareTo(avgCMI) < 0).collect(Collectors.toList());
    // 劣势病组
    List<BlQzYkxx> lsbz = list.stream().filter(item -> item.getZyk().compareTo(new BigDecimal("0.00")) < 0 && item.getZfqz().compareTo(avgCMI) < 0).collect(Collectors.toList());

    ExcelExp zlbzExp = new ExcelExp("战略病组", zlbz, BlQzYkxx.class);
    ExcelExp gzbzExp = new ExcelExp("关注病组", gzbz, BlQzYkxx.class);
    ExcelExp ysbzExp = new ExcelExp("优势病组", ysbz, BlQzYkxx.class);
    ExcelExp lsbzExp = new ExcelExp("劣势病组", lsbz, BlQzYkxx.class);

    List<ExcelExp> sheets = new ArrayList<>();
    sheets.add(zlbzExp);
    sheets.add(gzbzExp);
    sheets.add(ysbzExp);
    sheets.add(lsbzExp);

//    ExcelUtil<BlQzYkxx> util = new ExcelUtil<BlQzYkxx>(BlQzYkxx.class);
    ExcelUtilManySheet<List<ExcelExp>> util = new ExcelUtilManySheet<>(sheets);

    util.exportExcelManySheet(response, sheets);
  }

  @PostMapping("/getKsqzYkxx")
  public TableDataInfo ksList(BlQzYkxx blqzYkxx) {
    List<BlQzYkxx> list = tjfxService.selectKsqzYkxx(blqzYkxx);
    return getDataTable(list);
  }

  /**
   * 病例盈亏分析
   * @param vo
   * @return
   */
  @PostMapping("/blykfx")
  public TableDataInfo blykfxList(@RequestBody BlykfxQueryVo vo) {
    startPage();
    List<BlykfxVo> list = tjfxService.selectBlykfx(vo);

    return getDataTable(list);
  }

  @PostMapping("/export/blykfx")
  public void exportBlykfx(HttpServletResponse response, BlykfxQueryVo vo) {
    List<BlykfxVo> list = tjfxService.selectBlykfx(vo);
    ExcelUtil<BlykfxVo> util = new ExcelUtil<BlykfxVo>(BlykfxVo.class);
    util.exportExcel(response, list, "病组分析数据");
  }

  /**
   * 查询病例权重-盈亏图列表
   */
  @GetMapping("/getAvgCMI")
  public Double getAvgCMI()
  {
    Double cmi = tjfxService.selectAvgCMI();
    return cmi;
  }

  @PostMapping("/getDeptList")
  public AjaxResult getDeptList(HttpServletRequest req) {

    LoginUser loginUser = tokenService.getLoginUser(req);
    SysUser user = loginUser.getUser();
    List<SysRole> roles = user.getRoles();

    if (roles.stream().anyMatch(role -> "ys".equals(role.getRoleKey())) && roles.size() == 1) {
      List<String> list = new ArrayList<>();
      list.add(user.getDept().getDeptName());
      return success().put("list", list).put("is_ys", true);
    }


    return success().put("list", jsxxHisService.getDeptnameListWithTwoYears());

  }

  @Anonymous
  @GetMapping("/getDeptConsolidation")
  public List<Map<String, String>> getDeptConsolidation(){
    return baSyjlService.getDeptHbList();
  }

  @GetMapping("/getDeptListFromSy")
  public AjaxResult getDeptListFromSy(HttpServletRequest req) {
    LoginUser loginUser = tokenService.getLoginUser(req);
    SysUser user = loginUser.getUser();
    List<SysRole> roles = user.getRoles();

    if (roles.stream().anyMatch(role -> "ys".equals(role.getRoleKey())  || "kzrys".equals(role.getRoleKey())) && !user.isAdmin()) {
      List<String> list = new ArrayList<>();
      list.add(user.getDept().getDeptName());
      return success().put("list", list).put("is_ys", true);
    }

    List<BaSyjl> syjlList = baSyjlService.selectCykbList();
    List<String> list = new ArrayList<>();

    syjlList.forEach(item -> list.add(item.getCykb()));
    return success().put("list", list).put("is_ys", false);
  }

  @GetMapping("/getDoctorListByDept")
  public AjaxResult getDoctorListByDept(@RequestParam(name = "deptName", required = false) String deptName) {
    BaSyjl syjl = new BaSyjl();
    syjl.setCykb(deptName);
    List<BaSyjl> syList = baSyjlService.selectDoctorByDept(syjl);

    List<String> list = new ArrayList<>();
    syList.forEach(item -> list.add(item.getZyys()));
    return success().put("list", list);
  }

  @Anonymous
  @GetMapping("/getDeptDict")
  public List<Map<String, String>> getDeptDict(@RequestParam(name = "sysUserCode") String sysUserCode,
                                               @RequestParam(name = "jgid", value = "jgid", required = false) String jgid) {

    List<Map<String, String>> cacheList = redisCache.getCacheObject(sysUserCode + ":DeptDict");
    if(cacheList != null && !cacheList.isEmpty()) {
      return cacheList;
    }

    List<Map<String, String>> list = new ArrayList<>();

    String s = sysUserService.selectUserRoleGroup(sysUserCode);
    if (s != null && (s.contains("医生角色") || s.contains("科主任角色")) && !"admin".equals(sysUserCode) && !"yyAdmin".equals(sysUserCode)
      && (!s.contains("admin") && (!s.contains("yyAdmin")))) {
      SysUser sysUser = new SysUser();
      sysUser.setUserName(sysUserCode);
      List<SysUserOnline> sysUserOnlines = sysUserService.selectDeptByUser(sysUser);

      for (SysUserOnline user : sysUserOnlines) {
        Map<String, String> map = new HashMap<>();
        map.put("value", user.getDeptName());
        map.put("text", user.getDeptName());
        list.add(map);
      }
      redisCache.setCacheObject(sysUserCode + ":DeptDict", list, 10, TimeUnit.MINUTES);
      return list;
    }

    Map<String, String> defaultMap = new HashMap<>();
    defaultMap.put("value", "所有");
    defaultMap.put("text", "所有");
    list.add(defaultMap);

    List<BaSyjl> syjls = baSyjlService.selectDeptList();
    syjls.forEach(item -> {
      Map<String, String> map = new HashMap<>();
      map.put("value", item.getCykb());
      map.put("text", item.getCykb());

      list.add(map);
    });

    redisCache.setCacheObject(sysUserCode + ":DeptDict", list, 10, TimeUnit.MINUTES);
    return list;

  }

  @Anonymous
  @GetMapping("/getHDeptDict")
  public List<Map<String, String>> getHDeptDict(@RequestParam(name = "sysUserCode") String sysUserCode) {

    List<Map<String, String>> cacheList = redisCache.getCacheObject(sysUserCode + ":HDeptDict");
    if(cacheList != null && !cacheList.isEmpty()) {
      return cacheList;
    }

    List<Map<String, String>> list = new ArrayList<>();

    String s = sysUserService.selectUserRoleGroup(sysUserCode);
    if (s != null && (s.contains("医生角色") || s.contains("科主任角色")) && !"admin".equals(sysUserCode) && !"yyAdmin".equals(sysUserCode)
        && (!s.contains("admin") && (!s.contains("yyAdmin")) && (!s.contains("医院管理员")))) {
    List<String> deptListSource = hUserDeptService.selectHUserDeptDict(sysUserCode);
    List<String> deptListConsolidation = hUserDeptService.selectHConsolidation(deptListSource);
    deptListConsolidation.addAll(deptListSource);

      List<String> deptList = deptListConsolidation.stream().distinct().collect(Collectors.toList());

      for (String deptname : deptList) {
        Map<String, String> map = new HashMap<>();
        map.put("value", deptname);
        map.put("text", deptname);
        list.add(map);
      }
      redisCache.setCacheObject(sysUserCode + ":HDeptDict", list, 10, TimeUnit.MINUTES);
      return list;
    }

    Map<String, String> defaultMap = new HashMap<>();
    defaultMap.put("value", "所有");
    defaultMap.put("text", "所有");
    list.add(defaultMap);

    List<BaSyjl> syjls = new ArrayList<>();

    SysUser sysUser = sysUserService.selectUserByUserName(sysUserCode);
    String jgid = sysUser.getJgid();
    if (!"admin".equals(jgid) && jgid != null && !jgid.isEmpty()) {
      syjls.addAll( baSyjlService.selectDeptListByJgid(jgid));
    }else {
      syjls.addAll(baSyjlService.selectDeptList());
    }

    syjls.forEach(item -> {
      Map<String, String> map = new HashMap<>();
      map.put("value", item.getCykb());
      map.put("text", item.getCykb());

      list.add(map);
    });

    redisCache.setCacheObject(sysUserCode + ":HDeptDict", list, 10, TimeUnit.MINUTES);
    return list;

  }

  @Anonymous
  @PostMapping("/zbfx")
  public TableDataInfo getzbfx(@RequestBody ZbfxQueryVo vo) {
    startPage();
    List<Zbfx> list = tjfxService.getZbfx(vo);
    return getDataTable(list);
  }

  @PostMapping("/zbfx/export")
  public void export(HttpServletResponse response, ZbfxQueryVo vo) {
    List<Zbfx> list = tjfxService.getZbfx(vo);
    ExcelUtil<Zbfx> util = new ExcelUtil<Zbfx>(Zbfx.class);
    if (vo.getDataType() == 1) {
      util.hideColumn("cykb");
    } else if (vo.getDataType() == 2) {
      util.hideColumn("drgmc", "drgbh", "jcfl", "zfbz");
    }
    util.exportExcel(response, list, "指标分析数据");
  }

  /**
   * 质控分析-医生排行
   * @param vo
   * @return
   */
  @GetMapping("/zkfx/ysph")
  public AjaxResult ysph(ZkfxVo vo) {
      List<YsphRes> list = ybgkWgjlService.ysphTj(vo);
      return success().put("list", list);
  }

  @GetMapping("/zkfx/ksph")
  public AjaxResult ksph(ZkfxVo vo) {
    List<KsphRes> list = ybgkWgjlService.ksphTj(vo);
    return success().put("list", list);
  }

  @GetMapping("/zkfx/lxzb")
  public AjaxResult lxzb(ZkfxVo vo) {
    List<EchartsResVo> list = ybgkWgjlService.lxzbTj(vo);
    return success().put("list", list);
  }

  @GetMapping("/zkfx/xm/wordcloud")
  public AjaxResult xmcy(ZkfxVo vo) {
    List<EchartsResVo> list = ybgkWgjlService.xmcy(vo);
    return success().put("list", list);
  }

  @GetMapping("/zkfx/mrwg")
  public AjaxResult mrwg(ZkfxVo vo) {
    List<EchartsResVo> list = ybgkWgjlService.mrwg(vo);
    return success().put("list", list);
  }

  @GetMapping("/zkfx/xzxmsl")
  public AjaxResult xzxmSl(ZkfxVo vo) {
    List<EchartsResVo> list = ybgkWgjlService.xzxmSl(vo);
    return success().put("list", list);
  }

  @GetMapping("/report/drgfztj")
  public TableDataInfo drgfztj(FztjQueryVo vo) {
    startPage();
    List<Map<String, Object>> fztj = tjfxService.fztj(vo);
    return getDataTable(fztj);
  }


  @GetMapping("/report/bazbfx")
  public TableDataInfo bazbfx(BaZbfx vo) {
    List<BaZbfx> list = tjfxService.selectBaZbfx(vo);
    return getDataTable(list);
  }

  @GetMapping("/diagDiffDetails")
  public TableDataInfo diagDiffDetails(BaZbfx vo) {
    if (StringUtils.isBlank(vo.getDatetype())) {
      vo.setDatetype("jsdate");
    }
    startPage();
    List<diagDiffDetailsRes> list = tjfxService.diagDiffDetails(vo);
    return getDataTable(list);
  }

  @PostMapping("/ksdrgfx")
  public TableDataInfo ksdrgfx(@RequestBody KsdrgfxQueryVo vo){
    startPage();
    List<KsdrgfxVo> list = tjfxService.selectKsdrgfx(vo);
    return getDataTable(list);
  }

  @PostMapping("/ksdrgfx/export")
  public void exportKsdrgfx(HttpServletResponse response, KsdrgfxQueryVo vo) {
    List<KsdrgfxVo> list = tjfxService.selectKsdrgfx(vo);
    ExcelUtil<KsdrgfxVo> util = new ExcelUtil<KsdrgfxVo>(KsdrgfxVo.class);
    util.exportExcel(response, list, "科室DRG分析");
  }

  @DeptCheck
  @PostMapping("/ysdrgtj")
    public TableDataInfo ysdrgtj(@RequestBody YsdrgtjQueryVo vo) {
    startPage();
    List<YsdrgtjVo> list = tjfxService.selectYsdrgtj(vo);
    return getDataTable(list);
  }

  @PostMapping("/ykjlByMonth")
  public TableDataInfo ykjlByMonth(@RequestParam String bah) {
    List<YkjlVo> list = tjfxService.ykjlByMonth(bah);
    return getDataTable(list);
  }

  @PostMapping("/bafxYs")
  public TableDataInfo bafxYs(@RequestBody BafxYsQueryVo vo) {
    List<BafxYsVo> list = tjfxService.bafxYs(vo);
    return null;
  }

  @PostMapping("/ksdrgtj")
  public TableDataInfo ksdrgTj(@RequestBody KsdrgtjQeuryVo vo) {
    startPage();
    List<KsdrgtjVo> list = tjfxService.ksdrgtj(vo);
    return getDataTable(list);
  }

  @PostMapping("/export/ksdrgtj")
  public void exportKsdrgtj(HttpServletResponse response, KsdrgtjQeuryVo vo) {
    List<KsdrgtjVo> list = tjfxService.ksdrgtj(vo);
    ExcelUtil<KsdrgtjVo> util = new ExcelUtil<>(KsdrgtjVo.class);
    util.exportExcel(response, list, "科室DRG统计");
  }


  @RequestMapping("/getDeptYxzb")
  public TableDataInfo getDeptYxzb(DeptYxzbQueryVo deptYxzbQueryVo) {
    List<DeptYxzbQueryVo> list = tjfxService.selectDeptYxzb(deptYxzbQueryVo);
    return getDataTable(list);
  }
}
