package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.MztbFyxx;
import com.ruoyi.system.service.IMztbFyxxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 特病费用信息Controller
 * 
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/system/mztbfyxx")
public class MztbFyxxController extends BaseController
{
    @Autowired
    private IMztbFyxxService mztbFyxxService;

    /**
     * 查询特病费用信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:mztbfyxx:list')")
    @GetMapping("/list")
    public TableDataInfo list(MztbFyxx mztbFyxx)
    {
        startPage();
        List<MztbFyxx> list = mztbFyxxService.selectMztbFyxxList(mztbFyxx);
        return getDataTable(list);
    }

    /**
     * 导出特病费用信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:mztbfyxx:export')")
    @Log(title = "特病费用信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MztbFyxx mztbFyxx)
    {
        List<MztbFyxx> list = mztbFyxxService.selectMztbFyxxList(mztbFyxx);
        ExcelUtil<MztbFyxx> util = new ExcelUtil<MztbFyxx>(MztbFyxx.class);
        util.exportExcel(response, list, "特病费用信息数据");
    }

    /**
     * 获取特病费用信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:mztbfyxx:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(mztbFyxxService.selectMztbFyxxById(id));
    }

    /**
     * 新增特病费用信息
     */
    @PreAuthorize("@ss.hasPermi('system:mztbfyxx:add')")
    @Log(title = "特病费用信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MztbFyxx mztbFyxx)
    {
        return toAjax(mztbFyxxService.insertMztbFyxx(mztbFyxx));
    }

    /**
     * 修改特病费用信息
     */
    @PreAuthorize("@ss.hasPermi('system:mztbfyxx:edit')")
    @Log(title = "特病费用信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MztbFyxx mztbFyxx)
    {
        return toAjax(mztbFyxxService.updateMztbFyxx(mztbFyxx));
    }

    /**
     * 删除特病费用信息
     */
    @PreAuthorize("@ss.hasPermi('system:mztbfyxx:remove')")
    @Log(title = "特病费用信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(mztbFyxxService.deleteMztbFyxxByIds(ids));
    }
}
