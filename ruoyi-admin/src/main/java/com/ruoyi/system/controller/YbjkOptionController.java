package com.ruoyi.system.controller;

import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbjkOption;
import com.ruoyi.system.service.IYbjkOptionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 参数Controller
 *
 * <AUTHOR>
 * @date 2023-10-14
 */
@RestController
@Anonymous
@RequestMapping("/drg/option")
public class YbjkOptionController extends BaseController
{
    @Autowired
    private IYbjkOptionService ybjkOptionService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询参数列表
     */
    @GetMapping("/list")
    public TableDataInfo list(YbjkOption ybjkOption)
    {
        startPage();
        List<YbjkOption> list = ybjkOptionService.selectYbjkOptionList(ybjkOption);
        return getDataTable(list);
    }

    @GetMapping("/yyname")
    public String getYyName() {
      return StringUtils.defaultString(ybjkOptionService.getCompanyName(),"");
    }

    /**
     * 清空所有参数
     */
    @GetMapping("/clear")
    public void clear() {
      redisCache.deleteObject("option_*");
    }

    /**
     * 导出参数列表
     */
    @Log(title = "参数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbjkOption ybjkOption)
    {
        List<YbjkOption> list = ybjkOptionService.selectYbjkOptionList(ybjkOption);
        ExcelUtil<YbjkOption> util = new ExcelUtil<YbjkOption>(YbjkOption.class);
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 获取参数详细信息
     */
    @GetMapping(value = "/{cCode}")
    public AjaxResult getInfo(@PathVariable("cCode") String cCode)
    {

      YbjkOption yo = redisCache.getCacheObject("option_" + cCode);
      if (yo != null) {
        return success(yo);
      }
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode(cCode);
      redisCache.setCacheObject("option_" + cCode, option, 2, TimeUnit.MINUTES);
      return success(option);
    }

    /**
     * 新增参数
     */
    @Log(title = "参数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbjkOption ybjkOption)
    {
        return toAjax(ybjkOptionService.insertYbjkOption(ybjkOption));
    }

    /**
     * 修改参数
     */
    @Log(title = "参数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbjkOption ybjkOption)
    {
        return toAjax(ybjkOptionService.updateYbjkOption(ybjkOption));
    }

    /**
     * 删除参数
     */
    @Log(title = "参数", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cCodes}")
    public AjaxResult remove(@PathVariable String[] cCodes)
    {
        return toAjax(ybjkOptionService.deleteYbjkOptionByCCodes(cCodes));
    }
}
