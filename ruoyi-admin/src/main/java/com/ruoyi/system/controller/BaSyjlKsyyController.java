package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.BaSyjlKsyy;
import com.ruoyi.system.service.IBaSyjlKsyyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 病案亏损原因Controller
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/drg/ksyy")
public class BaSyjlKsyyController extends BaseController {
  @Autowired
  private IBaSyjlKsyyService baSyjlKsyyService;

  /**
   * 查询病案亏损原因列表
   */
  @PreAuthorize("@ss.hasPermi('drg:ksyy:list')")
  @GetMapping("/list")
  public TableDataInfo list(BaSyjlKsyy baSyjlKsyy) {
    startPage();
    List<BaSyjlKsyy> list = baSyjlKsyyService.selectBaSyjlKsyyList(baSyjlKsyy);
    return getDataTable(list);
  }

  @GetMapping("/ksyymx")
  public TableDataInfo ksyymx(BaSyjlKsyy baSyjlKsyy) {
    startPage();
    List<BaSyjlKsyy> list = baSyjlKsyyService.selectBaSyjlKsyymx(baSyjlKsyy);
    return getDataTable(list);
  }


  @GetMapping("/getBaKsyy")
  public TableDataInfo list(@RequestParam("brbs")String brbs) {
    List<BaSyjlKsyy> list = baSyjlKsyyService.getBaKsyy(brbs);
    return getDataTable(list);
  }

  /**
   * 导出病案亏损原因列表
   */
  @Log(title = "亏损明细", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, BaSyjlKsyy baSyjlKsyy) {
    List<BaSyjlKsyy> list = baSyjlKsyyService.selectBaSyjlKsyymx(baSyjlKsyy);
    ExcelUtil<BaSyjlKsyy> util = new ExcelUtil<BaSyjlKsyy>(BaSyjlKsyy.class);
    util.exportExcel(response, list, "亏损明细");
  }

  /**
   * 获取病案亏损原因详细信息
   */
  @PreAuthorize("@ss.hasPermi('drg:ksyy:query')")
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Long id) {
    return success(baSyjlKsyyService.selectBaSyjlKsyyById(id));
  }

  /**
   * 新增病案亏损原因
   */
  @PreAuthorize("@ss.hasPermi('drg:ksyy:add')")
  @Log(title = "病案亏损原因", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody BaSyjlKsyy baSyjlKsyy) {
    return toAjax(baSyjlKsyyService.insertBaSyjlKsyy(baSyjlKsyy));
  }

  /**
   * 修改病案亏损原因
   */
  @PreAuthorize("@ss.hasPermi('drg:ksyy:edit')")
  @Log(title = "病案亏损原因", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody BaSyjlKsyy baSyjlKsyy) {
    return toAjax(baSyjlKsyyService.updateBaSyjlKsyy(baSyjlKsyy));
  }

  /**
   * 删除病案亏损原因
   */
  @PreAuthorize("@ss.hasPermi('drg:ksyy:remove')")
  @Log(title = "病案亏损原因", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Long[] ids) {
    return toAjax(baSyjlKsyyService.deleteBaSyjlKsyyByIds(ids));
  }
}
