package com.ruoyi.system.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.vo.FyshVo;
import com.ruoyi.system.manager.CheckPatBatchManager;
import com.ruoyi.system.service.IAPIDataSyncService;
import com.ruoyi.system.service.IBrxxService;
import com.ruoyi.system.service.IBrxxSyncService;
import com.ruoyi.system.service.IFyxxService;
import com.ruoyi.system.service.IJsxxHisService;
import com.ruoyi.system.service.IJsxxHisZyService;
import com.ruoyi.system.service.IJyxxService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.IYbgkDzService;
import com.ruoyi.system.service.IYbgkWgjlService;
import com.ruoyi.system.service.IYbgkXzxmService;
import com.ruoyi.system.service.IYbjkLsfyxxService;
import com.ruoyi.system.service.IYbjkOptionService;
import com.ruoyi.system.service.IYbjkShxmService;
import com.ruoyi.system.service.IZdxxService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

@Anonymous
@RestController
@RequestMapping("/gksz/cysh")
public class CyshController extends BaseController {
  @Autowired
  private IYbgkWgjlService ybgkWgjlService;
  @Autowired
  private IYbjkOptionService ybjkOptionService;
  @Autowired
  private IBrxxService brxxService;
  @Autowired
  private IFyxxService fyxxService;
  @Autowired
  private IJyxxService jyxxService;
  @Autowired
  private IYbgkXzxmService ybgkXzxmService;
  @Autowired
  private IYbjkShxmService ybjkShxmService;
  @Autowired
  private IYbgkDzService ybgkDzService;
  @Autowired
  private IZdxxService zdxxService;
  @Autowired
  private IAPIDataSyncService iapiDataSyncService;
  @Autowired
  private IBrxxSyncService brxxSyncService;
  @Autowired
  private IYbgkTbbrxxService ybgkTbbrService;
  @Autowired
  private IYbgkTbyylService ybgkTbyylService;

  @Autowired
  private IYbjkLsfyxxService lsfyxxService;
  @Autowired
  private DataSource nativeDataSource;
  @Autowired
  private IJsxxHisService jsxxHisService;
  @Autowired
  private IJsxxHisZyService JsxxHisZyService;
  @Autowired
  private ISysUserService userService;
  @Autowired
  private IYbgkWgjlHistoryService ybgkWgjlHistoryService;

  @Autowired
  private IYbgkBrxxCheckjlService ybgkBrxxCheckjlService;
  
 
  

  @Autowired
  private IYbgkDzService iYbgkDzService;

  private static final Set<String> todayCallRecords = ConcurrentHashMap.newKeySet();
  private static volatile LocalDate currentDate = LocalDate.now();

  static final Map<String, List<YbgkWgjl>> zdsytsWgjlMap = new HashMap<>();

  private final CheckPatBatchManager checkPatBatchManager;

  public CyshController(CheckPatBatchManager checkPatBatchManager) {
    this.checkPatBatchManager = checkPatBatchManager;
  }


  String is_cysh_flag = "出院";
  String is_allow_next = "0";
  String is_gklog_xb = "";
  List<YbgkXzxm> ybgkXzxmListAll;
  List<SysUser> sysUserListAll;
  List<YbgkXzxm> ybgkXzxmListHistoryAll;
  List<YbgkTbbrxx> ybgkTbbrxxAll;
  List<YbgkTbyyl> ybgkTbbzAll;
  List<YbgkTbyyl> ybgkTbyylAll;
  String is_fysh_day_allmx;  //费用审核每日审核所有明细
  String is_tip_type = "2";
  String idt_today;
  String is_yydj;
  String is_fy_fromzl = "1";
  String is_bl_date_zk;
  String is_xzbz_frombl = "";

  String is_cysh_xsybbz = "";

  String is_check_times_for_day;
  String is_fysh_shcfzy;   //是否检查重复住院
  String jkdy_fhmr; //审核接口调用时直接返回空数组

  int plcysh_brwgjlts = -1;

  String is_hissoftname="";
  String is_pre_date = "";
  SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
  SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
  SimpleDateFormat sdfDateHour = new SimpleDateFormat("yyyy-MM-dd HH");

  //在院门诊分别需要审核的科室
  String zyshDeptStr;
  String mzshDeptStr;
  String is_check_dept;
  String is_check_zc;
  String is_yymc;
  String is_cysh_tsqjszd;//出院审核同时取出院诊断
  String is_cysh_yszyzz;//出院审核审核医生中医资质

  String is_cyshshjyx;//出院审核审核假阳性
  String is_cysh_bjfy;//出院审核比较费用
  String is_shgdjc;//审核过度检查
  String is_wgjl_zyys;//违规药品显示住院医生

  String is_mz_rqslxz;//门诊使用日期数量限制

  String is_tb_usesfzjzh;//检查特病用身份证或就就诊号或医保号1-身份证 2-就诊号 3-医保号

  String is_wg_writelog; //有违规时写日志

  String is_write_lsfyxx; //调接口时写lsfyxx
  
  String is_ybdjh_get_jzh;//住院根据医保登记号重取就诊号

  //中药饮片
  String is_zyyp="、阿胶、白糖参、朝鲜红参、穿山甲、醋山甲、炮山甲、玳瑁、冬虫夏草、蜂蜜、狗宝、龟鹿二仙胶、哈蟆油、海龙、海马、猴枣、蜂胶、羚羊角尖粉、羚羊角镑片、羚羊角粉 、鹿茸、鹿茸粉、鹿茸片、马宝、玛瑙、牛黄、珊瑚、麝香、天山雪莲、鲜石斛、铁皮石斛、西红花、番红花、西洋参、血竭、燕窝、野山参、移山参、珍珠、紫河车";

  String[] exemptKeywords = {"静脉采血", "真空采血管", "皮下注射", "氯化钠注射液",
    "静脉注射", "静脉输液", "肌肉注射", "葡萄糖注射液","图文报告","真空采血管"};
  
  String[] xmjsKeywords = {"血清","肝炎","钠测定","钙测定","钾测定","血细胞","肌酐","尿素","尿液分析","四肢血管彩色多普勒超声","凝血酶","乳酸","交叉配血","纤维蛋白","糖化血红","红细胞沉降率","葡萄糖测定"};

  //赋值为空列表
  List<Fyxx> emptyFyxxList = new ArrayList<>();
  Date   idt_predate;

  //赋值为空列表
  List<Zdxx> emptyZdxxList = new ArrayList<>();
  
  //临时保存门诊费用
  List<Fyxx> fyxxList_mz = new ArrayList<>();

  //历史记录版本 以历史数据记录违规是否已解决
  String wgjl_history;

  //询问操作版本 记录医生护士点击不再提示的违规记录
  String wgjl_exclude;
  String yb_wgjl_exclude;

  //退费检查，是否存在误退费操作，存在则对应项目不算作违规
  String wgjl_wtfjc;

  //审核时使用单量或总量 1使用更小者 0默认使用更大者
  String check_sum_dose;

  @RequestMapping("/getFyshData2")
  public String getWgjlData2(HttpServletRequest request) {
    String in1 = request.getParameter("in1");
    String in2 = request.getParameter("in2");
    String in3 = request.getParameter("in3");
    List<YbgkWgjl> ybgkWgjls = getWgjlByDoctor(in1, in2, in3);
    return JSON.toJSONString(ybgkWgjls);
  }

  private static BigDecimal parseToBigDecimal(String str) {

    if (str==null ) {
      return null;
    }

    try {
      return new BigDecimal(str);
    } catch (NumberFormatException e) {
      return null;
    }
  }


  @RequestMapping("/hisgetFyshData")
  public String hisgetFyshData(Fyshin fyshin) {
    String in1 = fyshin.getIn1();
    String in2 = fyshin.getIn2();
    String in3 = fyshin.getIn3();
    List<YbgkWgjl> ybgkWgjls = getWgjlByDoctor(in1, in2, in3);
    return JSON.toJSONString(ybgkWgjls);
  }

  @RequestMapping("/getFyshData")
  public String getWgjlData(@RequestBody String data) {
    JSONObject jsonObject = JSON.parseObject(data);
    String in1 = jsonObject.getString("in1");
    String in2 = jsonObject.getString("in2");
    String in3 = jsonObject.getString("in3");
    return JSON.toJSONString(getWgjlByDoctor(in1, in2, in3));
  }

  //更新规则、HISXXSQL、baxy的规则
  @RequestMapping("/gxgz")
  public void gxgz() {
    initYbgkXzxmList();
    brxxSyncService.clearhisxxsqlMap();
    is_hissoftname="";
    is_check_dept="";
    init();
  }


  public List<YbgkWgjl> getWgjlByDoctor(String in1, String in2, String in3) {
    if ("".equals(in1) || "".equals(in2) || in1 == null || in2 == null) {
      throw new ServiceException("请输入完整数据");
    }

    if (in2.indexOf("|") == -1) {
      throw new ServiceException("请输入完整数据");
    }

    String[] split = in2.split("\\|");

    if (split.length < 3) {
      throw new ServiceException("请输入完整数据");
    }

    YbgkWgjl ybgkWgjl = new YbgkWgjl();
    ybgkWgjl.setDoctor(split[0]);
    ybgkWgjl.setDoctorname(split[1]);


    return ybgkWgjlService.selectYbgkWgjlList1(ybgkWgjl);
  }




  @RequestMapping("/hischeckRule")
  public String hischeckRule(@RequestParam(name = "in1") String in1, @RequestParam(name = "in3") String in3,
                             @RequestBody(required = false) List<FyshVo> fyshVo) {
    if (fyshVo == null) {
      // 处理请求体为空的情况
      return "[]";
    }
    if ("".equals(is_hissoftname)) {
      init();
    }
    if ("1".equals(jkdy_fhmr)) {
      return "[]";
    }
    List<YbgkWgjl> ybgkWgjls = new ArrayList<>();

//    return JSON.toJSONString(ybgkWgjls);

    String strData = "";
    for (FyshVo vo : fyshVo) {
      strData += vo.toParam() + "&";
    }
    if (!"".equals(strData)) {
      strData = strData.substring(0, strData.length() - 1);
    }



    ybgkWgjls = checkRule(in1, strData, in3);

//    if ("zysh".equals(in1)) {
//      lsfyxxService.saveToLsFy(fyshVo);
//    }

    return JSON.toJSONString(ybgkWgjls);

  }

  @RequestMapping("/hischeckstrRule")
  public String hischeckstrRule(@RequestParam(name = "in1") String in1, @RequestParam(name = "in3") String in3,
                                @RequestBody String strData) {
    if ("mzsf".equals(in1)) {
      in1 = "mzsh";
    }
    List<YbgkWgjl> ybgkWgjls = new ArrayList<>();
    if ("".equals(is_hissoftname)) {
      init();
    }
    if ("1".equals(jkdy_fhmr)) {
      return "";
    }
    //如果是医嘱执行，则不审核  add by lt 2024-12-13
    if ("yzzx".equals(in3)) {
      return "";
    }

    

//    return JSON.toJSONString(ybgkWgjls);
    if(strData==null) {
      strData="";
    }

    //add by lt 2024-09-07
    if (!"".equals(strData)) {
      char lastChar = strData.charAt(strData.length() - 1);
      if  (Character.isDigit(lastChar)==false){
        strData = strData.substring(0, strData.length() - 1);
      }

    }

    if ("brcy".equals(in3)&&strData!=null&&strData.contains("出院")) {

      // 方法1：使用split分割字符串
      String[] parts = strData.split("\\|");
      if (parts.length==0) {
        return "";
      }

      YbgkWgjl ybjkJklog = new YbgkWgjl();
      String ls_jzh=null;
      ls_jzh = parts[0];
      if(ls_jzh!=null&&!"".equals(ls_jzh)&&!ls_jzh.contains("_")) {
        String ls_ybdjlsh = parts[0];
        Brxx brxx1= new Brxx();
        brxx1.setYbdjlsh(ls_ybdjlsh);
        Brxx brxx2 = brxxService.selectBrxxByybdjlsh(brxx1);
        if (brxx2!=null&&brxx2.getJzh()!=null) {
          ls_jzh = brxx2.getJzh();
        }
      }

      if ( ls_jzh!=null&&ls_jzh.length()>5) {
        ybjkJklog.setJzh(ls_jzh);
        ybjkJklog.setBrtype("2");
        cysh(ybjkJklog);
        ybgkWgjls = ybgkWgjlService.selectYbgkWgjlListByHospital(ybjkJklog);
      }
      else {
        return "";
      }

    }else {
      ybgkWgjls = checkRule(in1, strData, in3);
    }


    String ls_return="";
    for (int i = 0; i < ybgkWgjls.size(); i++) {
      if (ybgkWgjls.get(i).getYbbz()==null) {
        ybgkWgjls.get(i).setYbbz("无");
      }
      if (ybgkWgjls.get(i).getJklog()==null) {
        ybgkWgjls.get(i).setJklog("无");
      }
      if (ybgkWgjls.get(i).getBrname()==null) {
        ybgkWgjls.get(i).setBrname("无");
      }
      if (i==0) {
        ls_return = ybgkWgjls.get(i).getBrname()+"$$"+ybgkWgjls.get(i).getJklog()+"$$"+ybgkWgjls.get(i).getYbbz()+"$$0";
      }else
      {
        ls_return =ls_return+"||"+ ybgkWgjls.get(i).getBrname()+"$$"+ybgkWgjls.get(i).getJklog()+"$$"+ybgkWgjls.get(i).getYbbz()+"$$0";
      }
    }
    return ls_return;
  }

//  @RequestMapping("/hischeckRule")
//  public String hischeckRule(Fyshin fyshin) {
//    String in1 =  fyshin.getIn1();
//    String in2 =  fyshin.getIn2();
//    String in3 =  fyshin.getIn3();
//    return JSON.toJSONString(checkRule(in1, in2, in3));
//  }

  public boolean isNotBlankAndNotEmpty(String str) {
    return str != null && !str.trim().isEmpty();
  }

  public String getjzh(String as_strData) {
    //以下代码替换zlhis就诊号
    if (("zlhis".equals(is_hissoftname)||"1".equals(is_ybdjh_get_jzh))&&as_strData!=null) {
      if(as_strData.contains("_")==false){
        int index = as_strData.indexOf("|");
        if (index != -1) {
          String ls_ybdjlsh = as_strData.substring(0, index);
          String ls_ybdjlsh_old = ls_ybdjlsh;
          if (ls_ybdjlsh==null) {
            ls_ybdjlsh = "";
          }
          
          char firstChar = ls_ybdjlsh.charAt(0);
          if(firstChar!='3') {
            ls_ybdjlsh = "3"+ls_ybdjlsh;//补全医保号，传进来的少了第一位
          }
 
//	                System.out.println(ls_ybdjlsh); // 输出: 医保登记号
          if (ls_ybdjlsh!=null&&!"".equals(ls_ybdjlsh)) {
            Brxx brxx1= new Brxx();
            brxx1.setYbdjlsh(ls_ybdjlsh);
            Brxx brxx2 = brxxService.selectBrxxByybdjlsh(brxx1);
            if (brxx2==null) {
              return as_strData;
            }
            String ls_jzh = brxx2.getJzh();
            if (ls_jzh==null) {
              ls_jzh="";
            }
            if(ls_jzh.contains("_")==true){
              as_strData = as_strData.replaceAll(ls_ybdjlsh_old, ls_jzh);//替换就诊断号
            }
          }
        }
      }else
      //有下划线的情况，但时前面差一位
      {
    	  
        int index = as_strData.indexOf("|");
        String ls_jzh="";
        if (index == -1) {
          return as_strData;
        }

        ls_jzh = as_strData.substring(0, index);
        String ls_jzh_old = ls_jzh;
        if (ls_jzh==null||"".equals(ls_jzh)) {
          return as_strData;
        }


        Brxx brxx1 = brxxService.selectBrxxByBrbs(ls_jzh);

        if (brxx1!=null) {
          //通过就诊号找到病人
          return as_strData;
        }
        //没有找到病人，通过住院号找病人
        int index1 = as_strData.lastIndexOf('|');
        if (index1 == -1) {
          return as_strData;
        }

        //取最后的住院号
        String ls_zyh = as_strData.substring(index1 + 1);
        if (ls_zyh==null||"".equals(ls_zyh)) {
          return as_strData;
        }
        
        //传入医保登记号，取jzh
        if(as_strData!=null&&as_strData.length() >=2&&"31".equals(as_strData.substring(0, 2))&&ls_jzh_old!=null&&!ls_jzh_old.contains("_")) {
        	String ls_ybdjlsh = "";
        	Brxx queryBrxx = new Brxx();
        	queryBrxx .setYbdjlsh(ls_jzh);
            Brxx brxx2 = brxxService.selectBrxxByybdjlsh(queryBrxx );
            if (brxx2!=null&&brxx2.getJzh()!=null&&brxx2.getJzh().contains("_")) {
            	as_strData = as_strData.replaceAll(ls_jzh_old, ls_jzh);//替换就诊断号
            	return as_strData;
            }
        }

        Brxx brxx2 = brxxService.selectBrxxByzyh(ls_zyh);
        if (brxx2==null) {
          return as_strData;
        }
        ls_jzh = brxx2.getJzh();

        if(ls_jzh.contains("_")==true){
          as_strData = as_strData.replaceAll(ls_jzh_old, ls_jzh);//替换就诊断号
        }

      }//有下划线的情况，但时前面差一位
    }
    
    
    
    //以上代码替换zlhis就诊号
    return as_strData;
  }

  @RequestMapping("/hischeckRulestr")
  public List<YbgkWgjl> checkRule(String strKey, String strData, String in3) {
    String as_strKey = strKey;
    String as_strData = strData;
    String as_in3 = in3;



    if (!(isNotBlankAndNotEmpty(strKey)) || !(isNotBlankAndNotEmpty(strData)) || !(isNotBlankAndNotEmpty(in3))) {
      System.out.println(strKey+"---"+strData+"---"+in3);
      throw new ServiceException("请输入完整参数或费用信息");
    }

    List<YbgkWgjl> ids_log = new ArrayList<>();
    if ("".equals(is_hissoftname)) {
      init();
    }

    if ("1".equals(jkdy_fhmr)) {
      System.out.println("当前是忽略所有审核");
      return ids_log;
    }

  
   if(fyxxList_mz!=null&&fyxxList_mz.size()>500) {
	   fyxxList_mz.clear();
   }


    Brxx brxx = null;
    int li_flag = 0;
    

    List<Fyxx> fyxxList = new ArrayList<>();

    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    String brtype = "mzsh".equals(strKey) ? "1" : "2";
    if(!"1".equals(brtype)) {
      as_strData = getjzh(as_strData );
    }
    
    String[] fyxxArr = as_strData.split("&");
    
    for (String fyxxStr : fyxxArr) {
      if ("".equals(ifNull(fyxxStr))) {
        continue;
      }
      li_flag++;
      System.out.println("-------------------------费用单项:" +strKey+"--"+ fyxxStr);
      String[] attributeArr = new String[22];
      String[] split = fyxxStr.split("\\|");

      if (split.length > 22) {
        split = Arrays.copyOfRange(split, 0, 22);
      }

      for (int i = 0; i < split.length; i++) {
        if (split[i] != null && !"".equals(split)) {
          attributeArr[i] = split[i];
        }
      }

      Fyxx fyxx = new Fyxx();


      Pattern p = Pattern.compile("[\u4e00-\u9fa5]");

      if (attributeArr[20] != null) {
        Matcher billDoctorM = p.matcher(attributeArr[20]);
        if (billDoctorM.find()) {
          fyxx.setYsname(attributeArr[20]);
          fyxx.setYsid(attributeArr[8]);
        }
      }

      if (attributeArr[8] != null && fyxx.getYsname() == null) {
        Matcher advDoctorM = p.matcher(attributeArr[8]);
        if (advDoctorM.find()) {
          fyxx.setYsname(attributeArr[8]);
          fyxx.setYsid(attributeArr[20]);
        }
      }

      if (attributeArr[19] != null && p.matcher(attributeArr[19]).find()) {
        fyxx.setKsname(attributeArr[19]);
      } else {
        fyxx.setKsname(attributeArr[4]);
      }

//      Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
//      Matcher billDoctorM = p.matcher(attributeArr[20]);
//      Matcher advDoctorM = p.matcher(attributeArr[8]);
//      if (billDoctorM.find()) {
//        fyxx.setYsname(attributeArr[20]);
//        fyxx.setYsid(attributeArr[8]);
//      } else if (advDoctorM.find()) {
//        fyxx.setYsname(attributeArr[8]);
//        fyxx.setYsid(attributeArr[20]);
//      }
//
//      Matcher execDeptM = p.matcher(attributeArr[19]);
//      if (execDeptM.find()) {
//        fyxx.setKsname(attributeArr[19]);
//      } else {
//        fyxx.setKsname(attributeArr[4]);
//      }


//      if("zlhis".equals(is_hissoftname)) {
//        fyxx.setKsname(attributeArr[19]);
//        fyxx.setYsid(attributeArr[20]);
//        fyxx.setYsname(attributeArr[20]);
//      }else {
//        fyxx.setKsname(attributeArr[4]);
//        fyxx.setYsid(attributeArr[8]);
//      }

      //病人信息
      if (brxx == null) {
        brxx = new Brxx();
        brxx.setTbflag("0");
        brxx.setJzh(attributeArr[0]);
        brxx.setBrtype(brtype);

        //住院才执行
        if(!"1".equals(brtype)) {
          List<Brxx> brxxList = brxxService.selectBrxxList(brxx);
          if (brxxList.size() > 0 && brxxList.get(0) != null) {
            brxx = brxxList.get(0);
          }
          else {
            //传医保登记号的情况 add by lt 2025-07-06
            if(attributeArr[0]!=null&&!"".equals(attributeArr[0])&&!attributeArr[0].contains("_")) {
              brxx.setJzh(null);
              brxx.setYbdjlsh(attributeArr[0]);
              Brxx brxx2 = brxxService.selectBrxxByybdjlsh(brxx);
              if (brxx2!=null&&brxx2.getJzh()!=null) {
                brxx = brxx2;
              }
            }
          }
        }


        brxx.setZyh(attributeArr[21]);
        brxx.setName(attributeArr[17]);
        brxx.setBed(attributeArr[18]);
//        brxx.setDoctorname(attributeArr[20]);
        brxx.setDoctorname(fyxx.getYsname());
//        brxx.setDeptname(attributeArr[19]);
        brxx.setDeptname(fyxx.getKsname());
        brxx = getXzFlagByRydate(brxx);
        brxx.setSfz(attributeArr[15]);
      }

      if (brxx.getAge() == null) {
        if (StrUtil.isNotEmpty(attributeArr[15]) && attributeArr[15].length() == 18) {
          try {
            char code = attributeArr[15].charAt(16);
            String birthDateStr = attributeArr[15].substring(6, 14);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
            LocalDate currentDate = LocalDate.now();
            // 计算年龄
            brxx.setAge(String.valueOf(Period.between(birthDate, currentDate).getYears()));

          } catch (Exception e) {
            // TODO: handle exception
          }
        }
      }



      if (brxx.getSex() == null) {
        if (StrUtil.isNotEmpty(attributeArr[15]) && attributeArr[15].length() == 18) {
          char code = attributeArr[15].charAt(16);
          int codeNumber = code - '0';
          if (codeNumber % 2 == 0) {
            brxx.setSex("女");
          } else {
            brxx.setSex("男");
          }
        }
      }


      if ("1".equals(is_check_dept)) {
        if ("1".equals(brtype)) { //门诊
          if (!mzshDeptStr.contains(fyxx.getKsname())) {
            continue;
          }
        }
      }

      if ("2".equals(is_check_dept)) {
        if ("2".equals(brtype)) { //住院
          if (!zyshDeptStr.contains(fyxx.getKsname())) {
            continue;
          }
        }
      }

      if ("3".equals(is_check_dept)) {
        if ("1".equals(brtype)) { //门诊
          if (!mzshDeptStr.contains(fyxx.getKsname())) {
            continue;
          }
        }
        if ("2".equals(brtype)) { //住院
          if (!zyshDeptStr.contains(fyxx.getKsname())) {
            continue;
          }
        }
      }
      //费用信息
      fyxx.setJzh(attributeArr[0]);

      fyxx.setYyxmbm(attributeArr[1]);
      fyxx.setXmmc(attributeArr[2]);
      fyxx.setXmbm(attributeArr[1]);

      if("1".equals(brxx.getBrtype())&&attributeArr[10]!=null&&attributeArr[10].matches("^-?\\d+$")) {
        try {
          brxx.setNum(Integer.parseInt(attributeArr[10]));
        } catch (Exception e) {

        }

      }
      
      if(fyxx.getFydate()==null){
    	  fyxx.setFydate(new Date());
      }


      if ("".equals(attributeArr[12])) {
        attributeArr[12] = attributeArr[5];
      }
      if ("".equals(attributeArr[5])) {
        attributeArr[5] = attributeArr[12];
      }
      //比较总量和单量
      BigDecimal sl = parseToBigDecimal(attributeArr[5]);
      BigDecimal zl = parseToBigDecimal(attributeArr[12]);

      if (sl != null && zl != null) {
        int comparisonResult = zl.compareTo(sl);
        //总量与单量默认取其更大者，如参数为1则取其更小者
        //如果总量小于单量（5比12小），单量给总量
        if (comparisonResult < 0 && "0".equals(check_sum_dose)) {
          attributeArr[12] = attributeArr[5];
        } else if ( comparisonResult > 0 && "1".equals(check_sum_dose)) {
          attributeArr[12] = attributeArr[5];
        }
        //药品取最小的
        if ( comparisonResult > 0 && attributeArr[1]!=null&&attributeArr[1].length()>2&&attributeArr[1].startsWith("X")||attributeArr[1].startsWith("Z")) {
          attributeArr[12] = attributeArr[5];
        }
      }



      if (!"".equals(attributeArr[12]) && attributeArr[12] != null) {
        fyxx.setSl(new BigDecimal(attributeArr[12]));
      }

      if (!"".equals(attributeArr[6]) && attributeArr[6] != null) {
        fyxx.setPrice(new BigDecimal(attributeArr[6]));
      }
      fyxx.setDw(attributeArr[11]);
      fyxx.setKsid(attributeArr[3]);

      if ("".equals(attributeArr[7]) || attributeArr[7] == null) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        attributeArr[7] = formatter.format(calendar.getTime());

      }

      if (!"".equals(attributeArr[7]) && attributeArr[7] != null) {

        //处理日期有问题的
        String regex = "^\\d{4}-\\d{2}-\\d{2}\\d{2}:\\d{2}$"; // 注意这里没有空格
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(attributeArr[7]);
        if (matcher.matches()) {
          attributeArr[7] = attributeArr[7].substring(0, 10) + " " + attributeArr[7].substring(10)+":01";
        }

        try {

          if (attributeArr[7]!=null) {
            attributeArr[7] = attributeArr[7].replace("/", "-");
            attributeArr[7] = attributeArr[7].replace("\\", "-");
            attributeArr[7] = attributeArr[7].replace("T", " ");
          }


          fyxx.setFydate(simpleDateFormat.parse(attributeArr[7]));
          fyxx.setOpdate(simpleDateFormat.parse(attributeArr[7]));

        } catch (ParseException e) {

          Calendar calendar = Calendar.getInstance();
          SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

          attributeArr[7] = formatter.format(calendar.getTime());
//          throw new RuntimeException(e);

        }
      }





      fyxx.setZyh(attributeArr[21]);
      fyxx.setBed(attributeArr[18]);
      fyxx.setBz(attributeArr[9]);
      fyxx.setZdxx(attributeArr[9]);

      fyxxList.add(fyxx);


//      特病检查
      if(ybgkTbbzAll!=null&&ybgkTbbzAll.size()>5&&"1".equals(brxx.getBrtype())&&ybgkTbyylAll != null&&ybgkTbyylAll.size()>1000&&attributeArr[9]!=null) {
        if( attributeArr[9].contains("特病")||checkForPattern(attributeArr[9])||!"0".equals(is_tb_usesfzjzh)) {
          String ls_oldxmbm = fyxx.getXmbm();
          try {
            of_check_tbyyl(brxx, fyxx, ids_log,"hischeckRule",emptyFyxxList,li_flag,strData);
          } catch (Exception e) {
            // TODO: handle exception
          }

          fyxx.setXmbm(ls_oldxmbm);
        }
      }



      // 门诊不传病种不提示
      if(("1".equals(brxx.getBrtype())&&fyxx.getBz()!=null&&fyxx.getBz().length()>1)||!"1".equals(brxx.getBrtype())) {
        of_check_xzbz(brxx, fyxx, ids_log,strKey,"hischeckRule",emptyZdxxList,emptyFyxxList,strData);
      }

      of_check_jjz(brxx, fyxx, ids_log,strKey,"hischeckRule",emptyZdxxList);

//      if (brxx.getSex() != null) {
//          of_check_xzlx(brxx, fyxx, ids_log);
//          of_check_xznx(brxx, fyxx, ids_log);
//        }
//      if (brxx.getAge() != null) {
//          of_check_xzet(brxx, fyxx, ids_log);
//          of_check_xzage(brxx, fyxx, ids_log);
//        }
      of_check_xzyydj(brxx, fyxx, ids_log);


      of_check_xzage(brxx, fyxx, ids_log);
      //门诊提示过度检查
      if("1".equals(is_shgdjc)) {
        if(("1".equals(brxx.getBrtype())&&fyxx.getBz()!=null&&fyxx.getBz().length()>1)||!"1".equals(brxx.getBrtype())) {
          of_check_gdjc(brxx, fyxx, ids_log,emptyFyxxList,strData);
        }

      }


      if (brxx.getSex() != null) {
        of_check_xzlx(brxx, fyxx, ids_log);//限男性
        of_check_xznx(brxx, fyxx, ids_log);//限女性
      }

      if (brxx.getAge() != null) {
        of_check_xzet(brxx, fyxx, ids_log);

      }

      of_check_blgyxm(brxx, fyxx, ids_log,as_strData);


      if("1".equals(is_mz_rqslxz)||!strKey.equals("mzsh")||(fyxx.getXmmc()!=null&&fyxx.getXmmc().contains("糖化血红蛋白"))) {
        of_check_rqslxz(brxx, fyxx, ids_log, "hischeckRule");
      }

      //审核中药饮片
      if("1".equals(is_cysh_xsybbz)) {
        //中药饮片
        if(fyxx.getXmmc()!=null&&is_zyyp.indexOf(fyxx.getXmmc()) != -1) {
          setWgjlItem("项目[" + fyxx.getXmmc() + "]中药饮片不得纳入基金支付", "ybbz", fyxx, ids_log, brxx, "药品", "0");
        }
      }

      if (!strKey.equals("mzsh")) {

        of_check_lcbx(brxx, fyxx, ids_log);
        of_check_xzks(brxx, fyxx, ids_log);

        of_check_zysyzdts(brxx, fyxx, ids_log,"hischeckRule",emptyFyxxList);

        of_check_xxmjssy(brxx, fyxx, ids_log,"hischeckRule",emptyFyxxList,as_strData);


        //显示医保备注
        if("1".equals(is_cysh_xsybbz)||"1".equals(is_cysh_yszyzz)||(fyxx.getXmbm()!=null&&fyxx.getXmbm().startsWith("0046"))) {
          of_check_ybbz(brxx, fyxx, ids_log);
        }
      }

      //去除限制病种与当前费用病种冲突的违规记录
      if (fyxx.getBz() != null && !fyxx.getBz().isEmpty() && ids_log.stream().anyMatch(obj -> "xzbz".equals(obj.getJktype()))) {
        Iterator<YbgkWgjl> wgjlIterator = ids_log.iterator();
        while (wgjlIterator.hasNext()) {
          YbgkWgjl ybgkWgjl = wgjlIterator.next();
          if (ybgkWgjl != null) {
            if ("xzbz".equals(ybgkWgjl.getJktype()) && checkExpenseLimitDisease(ybgkWgjl, fyxx.getBz())) {
              wgjlIterator.remove();
            }
          }
        }
      }

      //门诊数据将诊断写进违规记录
      if ("mzsh".equals(strKey)) {
        for (YbgkWgjl ybgkWgjl : ids_log) {
          ybgkWgjl.setBzmc(fyxx.getBz());
        }
      }

    }

    if (!"mzsh".equals(strKey)) {
      //检查一次住院最大使用次数
      for (Fyxx fyxx : fyxxList) {
        fyxx.setZl(getFeeTotalNum(fyxxList,fyxx.getXmbm()));
        of_check_zyslxz(brxx, fyxx, ids_log, "hischeckRule",emptyFyxxList);
      }

      // 添加最大使用天数每日提示
      if (zdsytsWgjlMap.containsKey(brxx.getJzh())) {
        List<YbgkWgjl> list = zdsytsWgjlMap.get(brxx.getJzh());
        ids_log.addAll(list);
        zdsytsWgjlMap.remove(brxx.getJzh());
      }
    }

    //检查传入的多个费用项目能否共用 门诊与住院都检查
    if (fyxxList.size() > 1) {
      canUseExpenseItemsTogether(brxx, fyxxList, ids_log);
    }

    //根据违规信息去重
    Set<YbgkWgjl> ybgkWgjlSet = new TreeSet<>(Comparator.comparing(YbgkWgjl::getJklog));
    ybgkWgjlSet.addAll(ids_log);
    ids_log = new ArrayList<>(ybgkWgjlSet);

    //取医保备注及说明书
    try {
      YbgkDz ybgkdz=new YbgkDz();
      String ls_ybxz="";
      for (int i = 0; i < ids_log.size(); i++) {
        if("xzbz".equals(ids_log.get(i).getJktype())||"xzlcbx".equals(ids_log.get(i).getJktype())){
          if (ids_log.get(i).getFymId()!=null&&!"".equals(ids_log.get(i).getFymId())) {
            ybgkdz.setXmbm(ids_log.get(i).getFymId());
            List<YbgkDz> list =ybgkDzService.selectYbgkDzList(ybgkdz);
            if (list.size()>0 ) {
              ls_ybxz = list.get(0).getYbxz();
              if (ls_ybxz!=null&&!"".equals(ls_ybxz)) {
                ids_log.get(i).setYbbz(ls_ybxz);
                if (ls_ybxz.contains("适应症")&&ids_log.get(i).getJklog()!=null&&!"".equals(ids_log.get(i).getJklog())) {
                  ids_log.get(i).getJklog().replace("]限","]适应症");
                }
              }
            }
          }
        }
      }
    }
    catch (Exception e) {
      // TODO: handle exception
    }


    synchronized (this) {
      for (int i = 0; i < ids_log.size(); i++) {
        //根据病人类型、就诊号、违规内容搜索是否已经存在违规记录
        List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectAllYbglWgjl(new YbgkWgjl(ids_log.get(i).getJzh(), ids_log.get(i).getJklog(), brxx.getBrtype()));
        if (ybgkWgjlList.size() == 0) {
          ybgkWgjlService.insertYbgkWgjl(ids_log.get(i));
        }
      }
    }

    //编辑显示颜色
    for (YbgkWgjl ybgkWgjl : ids_log) {
      YbgkXzxm ybgkXzxm = new YbgkXzxm();
      if (ybgkWgjl.getFymId() != null && ybgkWgjl.getJktype() != null) {
        ybgkXzxm.setXmbm(ybgkWgjl.getFymId());
        ybgkXzxm.setXzcode(ybgkWgjl.getJktype());
        String showColor = ybgkXzxmService.selectShowColor(ybgkXzxm);
        ybgkWgjl.setXzlb(showColor);
      }
    }


    //大学城门诊只有几个科室返回提示
    if ("mzsh".equals(strKey)&&is_yymc!=null&&is_yymc.contains("大学城医院")&&as_strData!=null) {
      if(!as_strData.contains("肿瘤科")&&!as_strData.contains("肾脏")&&!as_strData.contains("全科")) {
        ids_log.clear();
        return ids_log;
      }
    }

    LocalDateTime now = LocalDateTime.now();
    LocalTime currentTime = now.toLocalTime();
    LocalDate today = now.toLocalDate();
    //如果是住院医嘱，并且时间在12：00之前，则进入逻辑
    if ("2".equals(brtype) && currentTime.isBefore(LocalTime.of(12, 0))) {
      // 检查是否跨天，如果跨天则清空记录
      if (!today.equals(currentDate)) {
        synchronized (CyshController.class) {
          if (!today.equals(currentDate)) {
            todayCallRecords.clear();
            currentDate = today;
            if(fyxxList_mz!=null&&!fyxxList_mz.isEmpty()) {
            	fyxxList_mz.clear();//跨天清门诊的费用list
            }
          }
        }
      }

      String callKey =  brxx.getDeptname() + "|" + brxx.getDoctorname();

      // 判断是否是当日第一次调用
      boolean isFirstCall = todayCallRecords.add(callKey);

      if (isFirstCall) {
        // 当日第一次调用的特殊处理
        YbgkWgjl wgjlQuery = new YbgkWgjl();
        wgjlQuery.setKdksname(brxx.getDeptname());
        wgjlQuery.setDoctorname(brxx.getDoctorname());
        LocalDateTime startDate = now.withHour(0).withMinute(0).withSecond(0).withNano(0).minusDays(1);
        LocalDateTime endDate = now.withHour(23).withMinute(59).withSecond(59).withNano(59);
        wgjlQuery.setCreateDateStart(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        wgjlQuery.setCreateDateEnd(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        String[] jktypes = {"syzdts", "cfzy"};
        wgjlQuery.setJktypes(jktypes);
        List<YbgkWgjl> wjgls = ybgkWgjlService.selectYbgkWgjlListByHospital(wgjlQuery);
        ids_log.addAll(wjgls);
      }

    }

    //写临时费用表
    if ("zysh".equals(strKey)&&"1".equals(is_write_lsfyxx)) {
        try {
			lsfyxxService.plfyxxinsertLsfyxx(fyxxList);
		} catch (Exception e) {
			// TODO: handle exception
		}
    }
    if ("mzsh".equals(strKey)&&"1".equals(is_write_lsfyxx)) {
    	fyxxList_mz.addAll(fyxxList);
    	
    }

    return ids_log;
  }

  
  /**
   * 检查病种名称是否包含在ls_ybxz中
   * @param fymId 传入的ids_log.get(i).getFymId()字符串
   * @param as_bzmc 病种名称
   * @return 如果as_bzmc包含在ls_ybxz中返回true，否则返回false
   */
  private boolean checkBzmcInSyz(String fymId, String as_bzmc) {
      YbgkDz ybgkdz = new YbgkDz();
      ybgkdz.setXmbm(fymId);
      if (as_bzmc==null||"".equals(as_bzmc)) {
    	  return false;
      }
      
      if (fymId==null||"".equals(fymId)) {
    	  return false;
      }
      
      List<YbgkDz> list = ybgkDzService.selectYbgkDzList(ybgkdz);  
      if (list.size() > 0) {
          String ls_ybxz = list.get(0).getYbxz();
          if (ls_ybxz != null && !"".equals(ls_ybxz)) {
              // 检查病种名称是否包含在ls_ybxz中
              return ls_ybxz.contains(as_bzmc);
          }
      }
      // 如果没有找到对应的记录或ls_ybxz为空，返回false
      return false;
  }

  public  boolean checkForPattern(String input) {
    // 定义正则表达式：M后面跟着3个数字
    String regex = "M\\d{3}";
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(input);
    return matcher.find();
  }

  @Scheduled(cron = "0 30 4 * * *")
  @RequestMapping("/autoSetWgjlMap")
  public void setWgjlMap() {
    zdsytsWgjlMap.clear();

    Brxx brxx = new Brxx();
    brxx.setZyzt("1");
    List<Brxx> brxxList = brxxService.selectBrxxList(brxx);

    for (Brxx br : brxxList) {
      YbgkWgjl wgjl = new YbgkWgjl();
      wgjl.setJktype("syzdts");
      wgjl.setJzh(br.getJzh());
      List<YbgkWgjl> list = ybgkWgjlService.selectAllYbglWgjl(wgjl);

      zdsytsWgjlMap.put(br.getJzh(), list);
    }
  }

  public BigDecimal getFeeTotalNum(List<Fyxx> fyxxList, String xmbm) {
    if (StringUtils.isEmpty(xmbm)) {
      return BigDecimal.ZERO;
    }
    BigDecimal feeTotalNum = BigDecimal.ZERO;
    for (Fyxx fyxx : fyxxList) {
      if (xmbm.equals(fyxx.getXmbm())) {
        feeTotalNum = feeTotalNum.add(fyxx.getSl() == null ? BigDecimal.ZERO : fyxx.getSl());
      }
    }
    return feeTotalNum;
  }

  //根据入院时间选择对应的审核限制规则
  public Brxx getXzFlagByRydate(Brxx brxx) {
    Date rydate = brxx.getRydate();
    if (rydate == null || DateUtil.year(rydate) == DateUtil.thisYear()) {
      brxx.setFlag(0);
    } else {
      int year = DateUtil.year(rydate);
      if (ybgkXzxmListHistoryAll.stream().anyMatch(item -> year == item.getYear())) {
        brxx.setYear(year);
        brxx.setFlag(1);
      } else {
        brxx.setFlag(0);
      }
    }
    return brxx;
  }


  /**
   * 检查传入的费用项目之间能否共用
   *
   * @param brxx
   * @param fyxxList
   * @param ids_log
   */
  public void canUseExpenseItemsTogether(Brxx brxx, List<Fyxx> fyxxList, List<YbgkWgjl> ids_log) {
    //获取所有费用项目名称
    List<String> xmmcList = fyxxList.stream().map(Fyxx::getXmmc).collect(Collectors.toList());

    for (int j = 0; j < fyxxList.size(); j++) {
      Fyxx fyxx = fyxxList.get(j);

      List<YbgkXzxm> ybgkXzxmList = ybgkXzxmListAll.stream().filter(ybgkXzxmListAll -> ybgkXzxmListAll.getXmbm().equals(fyxx.getXmbm())
        && ybgkXzxmListAll.getXzcode().equals("blgyxm")).collect(Collectors.toList());

      if (ybgkXzxmList.size() == 0) {
        continue;
      }

      for (YbgkXzxm ybgkXzxm : ybgkXzxmList) {
        String xzvalue = ifNull(ybgkXzxm.getXzvalue());
        String xmtype = ifNull(ybgkXzxm.getXmtype());

        if (xzvalue.indexOf("、") > -1 && !"".equals(xzvalue)) {
          String[] blgyxm = xzvalue.split("、");
          for (int i = 0; i < blgyxm.length; i++) {
            if (xmmcList.contains(blgyxm[i])) {
              setWgjlItem("项目[" + ifNull(fyxx.getXmmc()) + "]不能与[" + blgyxm[i] + "]同时开", "blgyxm", fyxx, ids_log, brxx, xmtype, "0", ybgkXzxmList.get(0).getShowcolor());
            }
          }
        } else if (xmmcList.contains(xzvalue) && !"".equals(xzvalue)) {
          setWgjlItem("项目[" + ifNull(fyxx.getXmmc()) + "]不能与[" + xzvalue + "]同时开", "blgyxm", fyxx, ids_log, brxx, xmtype, "0", ybgkXzxmList.get(0).getShowcolor());
        }
      }
    }

  }

  public String ifNull(String inputStr) {
    if (inputStr == null || "".equals(inputStr)) {
      return "";
    }
    return inputStr.trim();
  }


  /**
   * 根据费用限制病种检查当前费用的病种
   *
   * @param ybgkWgjl
   * @param bz
   * @return
   */
  public boolean checkExpenseLimitDisease(YbgkWgjl ybgkWgjl, String bz) {
    String jklog = ifNull(ybgkWgjl.getJklog());
    if (jklog != null) {
      int startIndex = jklog.indexOf("限[") + 2;
      if (startIndex > -1) {
        int endIndex = jklog.indexOf("]", startIndex);
        if (endIndex > -1) {
          String xzbz = jklog.substring(startIndex, endIndex).trim();
          if (xzbz.indexOf("、") > -1) {
            String[] xzbzArr = xzbz.split("、");
            for (int i = 0; i < xzbzArr.length; i++) {
              if (bz.indexOf(xzbzArr[i]) > -1) {
                return true;
              }
            }
          } else {
            if (bz.indexOf(xzbz) > -1) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }


  @RequestMapping("/okflag")
  @ResponseBody
  public String okflag() {
    Date currentTime = new Date(System.currentTimeMillis());
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String formattedTime = sdf.format(currentTime);
    return "shok"+formattedTime;
  }

  @RequestMapping("/htokflag")
  @ResponseBody
  public String htokflag() {
    Date currentTime = ybjkOptionService.getSystemServerTime();
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String formattedTime = sdf.format(currentTime);
    return "shok"+formattedTime;
  }

  @RequestMapping("/transfyblzd")
  @ResponseBody
  public String transfyblzd(YbgkWgjl ybjkJklog) {
    System.out.println(ybjkJklog.toString());

    OkHttpClient okHttpClient = new OkHttpClient.Builder()
      .connectTimeout(40, TimeUnit.SECONDS)
      .readTimeout(40, TimeUnit.SECONDS)
      .writeTimeout(40, TimeUnit.SECONDS)
      .build();
    if (ybjkJklog.getJzh() == null || "".equals(ybjkJklog.getJzh())) {
      return "error";
    }
    //陈家桥
    YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("patient_data_sources");
    if (option != null && option.getcValue().equals("1")) {
      boolean syncRes = iapiDataSyncService.syncFeeByJzh(ybjkJklog.getJzh());
      if (!syncRes) {
        return "error";
      }
      return "ok";
    }

    option = ybjkOptionService.selectYbjkOptionByCCode("sync_version");
    if (option != null && "java".equals(option.getcValue())) {
      List<Brxx> brxx = brxxService.selectBrxxByJzh(ybjkJklog.getJzh());
      if (brxx.size() > 0) {
        brxxSyncService.zdxxSync(brxx.get(0).getBrid(), brxx.get(0).getZyid());
        brxxSyncService.fyxxSync(brxx.get(0).getBrid(), brxx.get(0).getZyid());
        brxxSyncService.blxxSync(brxx.get(0).getBrid(), brxx.get(0).getZyid());
      }
      return "ok";
    }


    String transaction = "http://localhost:9000/?action=transfy&ybjzh=" + ybjkJklog.getJzh() + "&type=fyblzd&num=" + Math.random();
    System.out.println("111" + transaction);
    Response response = null;
    String result = null;
    String errorData = null;
    try {
      Request request = new Request.Builder()
        .url(transaction)
        .build();

      System.out.println("交易地址:" + transaction);
      response = okHttpClient.newCall(request).execute();
      result = response.body().string();
    } catch (IOException e) {
      e.printStackTrace();
      errorData = String.valueOf(e);
      System.out.println(errorData);
      return "error";
    }
    return "ok";
  }


  @RequestMapping("/cysh1")
  public TableDataInfo cysh(YbgkWgjl ybjkJklog) {
    boolean isSaveCheckRecord = true;
    try {
      LoginUser loginUser = getLoginUser();
      if("admin".equals(loginUser.getUsername())) {
        isSaveCheckRecord = false;
      }
    } catch (Exception e) {
      isSaveCheckRecord = true;
    }
//    System.out.println(new Date());

    List<Object> list = null;
    String jzh = ybjkJklog.getJzh();
    String is_reg_id = jzh;
    String ls_plshflag = ybjkJklog.getPlshflag();

    if(jzh==null) {
      return getDataTable(new ArrayList<>());
    }


    init();
    String ls_zdqk="";
    List<Brxx> brxxList = new ArrayList<>();
    String ls_jgid=null;
    //门诊病人
    if (ybjkJklog.getBrtype() != null && "1".equals(ybjkJklog.getBrtype())) {
      JsxxHis jsxxHis = new JsxxHis();
      jsxxHis.setSetlId(ybjkJklog.getJzh());
      List<JsxxHis> jsxxList = jsxxHisService.selectJsxxHisList(jsxxHis);
      for (JsxxHis js : jsxxList) {
        Brxx brxx = new Brxx();
        brxx.setAge(js.getAge() == null ? null : js.getAge().toString());
        brxx.setName(js.getPsnName());
        brxx.setJgid(js.getFixmedinsCode());
        brxx.setSex(js.getGend());
        brxx.setJzh(js.getBrid()+"_"+js.getZyid());//门诊固定用brbs
        brxx.setZyh(js.getMdtrtId());
        brxx.setBrid(js.getBrid());
        brxx.setZyid(js.getZyid());
        brxx.setZyh(js.getSetlId());

        if(js.getDiseCodg()==null) {
          js.setDiseCodg("");
        }
        if(js.getDiseName()==null) {
          js.setDiseName("");
        }


        brxx.setBzname(js.getDiseCodg()+","+js.getDiseName());
        ls_zdqk=js.getDiseCodg()+","+js.getDiseName();//设置用于审核的诊断
        brxx.setBrtype("1");
        if (js.getInsutype()!=null&&js.getInsutype().equals("310"))
        {
          brxx.setCblb("职工");
        }
        else if (js.getInsutype()!=null&&js.getInsutype().equals("3101")) {
          brxx.setCblb("异地职工");
        }
        else if (js.getInsutype()!=null&&js.getInsutype().equals("3901")) {
          brxx.setCblb("异地居民");
        }
        else
        {
          brxx.setCblb("居民");
        }
        brxx.setSfz(js.getCertno());

        brxx.setRydate(js.getBegndate());
        brxx.setCydate(js.getHisJsdate() == null ? js.getEnddate() : js.getHisJsdate());
        brxx.setSetl_id(js.getSetlId());
        brxx.setSfz(js.getCertno());
        BigDecimal lbd_ybbx;

        lbd_ybbx = js.getHifmiPay().add(js.getHifobPay()).add(js.getHifpPay()).add(js.getCvlservPay()).add(js.getMafPay());
        if(lbd_ybbx!=null) {
          brxx.setYbbx(lbd_ybbx.doubleValue());
        }
        if(js.getHifpPay()!=null) {
          brxx.setTc(js.getHifpPay().doubleValue());
        }

        if(js.getMafPay()!=null) {
          brxx.setYljz(js.getMafPay().doubleValue());
        }

        if(js.getMafPay()!=null&&js.getHifobPay()!=null) {
          brxx.setDe(js.getHifmiPay().add(js.getHifobPay()).doubleValue());
        }

        if(js.getMedfeeSumamt()!=null) {
          brxx.setZje(js.getMedfeeSumamt().doubleValue());
        }

        brxx.setDeptname(js.getDeptname());
        brxx.setDoctorname(js.getDoctorname());
        brxx.setDeptid(js.getDeptid());
        brxx.setDoctorid(js.getDoctorid());
        brxxList.add(brxx);
      }
    } else {
      brxxList = brxxService.selectBrxxList(new Brxx(jzh));
      if (brxxList.size()>0) {

        JsxxHisZy jsxxHis = new JsxxHisZy();
        jsxxHis.setBrid(brxxList.get(0).getBrid());
        jsxxHis.setZyid(brxxList.get(0).getZyid());

        List<JsxxHisZy> jsxxList = JsxxHisZyService.selectJsxxHisZyList(jsxxHis);
        if (jsxxList.size()>0) {
          JsxxHisZy js = jsxxList.get(0);

          if (brxxList.get(0).getCydate()==null) {
            brxxList.get(0).setCydate(js.getHisJsdate() == null ? js.getEnddate() : js.getHisJsdate());
          }



//          if (brxxList.get(0).getRydate()==null) {
//            brxxList.get(0).setRydate(js.getHisJsdate());
//          }

          if (js.getInsutype()!=null&&js.getInsutype().equals("310"))
          {
            brxxList.get(0).setCblb("职工");
          }
          if (js.getInsutype()!=null&&js.getInsutype().equals("3101")) {
            brxxList.get(0).setCblb("异地职工");
          }
          if (js.getInsutype()!=null&&js.getInsutype().equals("3901")) {
            brxxList.get(0).setCblb("异地居民");
          }
          if (js.getInsutype()!=null&&js.getInsutype().equals("390")) {
            brxxList.get(0).setCblb("居民");
          }

          if(js.getMedfeeSumamt()!=null) {
            brxxList.get(0).setZje(js.getMedfeeSumamt().doubleValue());
          }

          brxxList.get(0).setBrtype("2");

          brxxList.get(0).setSetl_id(js.getSetlId());
          brxxList.get(0).setSfz(js.getCertno());
          BigDecimal lbd_ybbx;

          lbd_ybbx = js.getHifmiPay().add(js.getHifobPay()).add(js.getHifpPay()).add(js.getCvlservPay()).add(js.getMafPay());
          if(lbd_ybbx!=null) {
            brxxList.get(0).setYbbx(lbd_ybbx.doubleValue());
          }
          if(js.getHifpPay()!=null) {
            brxxList.get(0).setTc(js.getHifpPay().doubleValue());
          }

          if(js.getMafPay()!=null) {
            brxxList.get(0).setYljz(js.getMafPay().doubleValue());
          }

          if(js.getMafPay()!=null&&js.getHifobPay()!=null) {
            brxxList.get(0).setDe(js.getHifmiPay().add(js.getHifobPay()).doubleValue());
          }


//        	  brxxList.get(0).set(js.getCertno());
        }

      }
    }

    List<Brxx> brxxList2 = null;
    if (brxxList.size() == 0) {
      if(jzh.contains("_"))	{
        String brid = jzh.split("_")[0];
        String zyid = jzh.split("_")[1];
        Brxx brxx2 =  new Brxx();
        brxx2.setBrid(jzh.split("_")[0]);
        brxx2.setZyid(jzh.split("_")[1]);
        brxxList2 = brxxService.selectBrxxList(brxx2);
        if (brxxList.size() == 0) {
          return getDataTable(new ArrayList<>());
        }
      }else
      {
        return getDataTable(new ArrayList<>());
      }
    }
    Brxx brxx = brxxList.get(0);
    is_reg_id = brxx.getJzh();


    //这几家没有控费
    if(brxx!=null) {
      ls_jgid = brxx.getJgid();
      if("c182cc62-ee76-4b61-ab6d-75c0b03bfce0".equals(ls_jgid)||"9ae2a358-50c1-4602-a547-9b65bd7a1b18".equals(ls_jgid)||"7fd48753-fb44-47e2-9dfc-cdacc6469966".equals(ls_jgid)||"3be60727-c314-442d-a5ab-f2a77a8a0c8a".equals(ls_jgid)||"eea34cba-29de-44c7-bafc-d7a362557441".equals(ls_jgid))
      {
        return getDataTable(new ArrayList<>());
      }
    }



    brxx.setAge(String.valueOf(convertToDecimalAge(brxx.getAge())));


    List<YbgkWgjl> ids_log = new ArrayList<>();
    //10日内重复住院

    if (brxx.getRydate() != null) {
      //根据入院时间选择对应的审核限制规则
      brxx = getXzFlagByRydate(brxx);
      if ("1".equals(is_fysh_shcfzy)) {
        long betweenDay = DateUtil.between(brxx.getRydate(), DateUtil.date(), DateUnit.DAY);
        System.out.println(betweenDay);

        if (betweenDay <= 2) {
          List<Brxx> brxxList1 = brxxService.select10dayzy(brxx);
          if (brxxList1.size() > 0) {
            Brxx brxx1 = brxxList1.get(0);
            YbgkWgjl ybgkWgjl = new YbgkWgjl();
            ybgkWgjl.setId(getDateId());
            ybgkWgjl.setBrname(brxx.getName());
            ybgkWgjl.setBrtype("2");
            ybgkWgjl.setKdksname(brxx.getDeptname());
            ybgkWgjl.setDoctorname(brxx.getDoctorname());
            ybgkWgjl.setBed(brxx.getBed());
            ybgkWgjl.setKdks(brxx.getDeptid());
            ybgkWgjl.setDoctor(brxx.getDoctorid());
            ybgkWgjl.setFymName("");
            ybgkWgjl.setSl(new BigDecimal(0));
            ybgkWgjl.setFymId("");
            ybgkWgjl.setJe(new BigDecimal(0));
            ybgkWgjl.setZyh(brxx.getZyh());
            ybgkWgjl.setJzh(brxx.getJzh());
            ybgkWgjl.setCreateDate(DateUtil.formatDateTime(DateUtil.date()));
            ybgkWgjl.setClFlag("0");
            ybgkWgjl.setTshOper(is_tip_type);
            ybgkWgjl.setJklog("病人：" + brxx1.getName() + ",住院号为：" + brxx1.getZyh() + "," + "上次出院时间为：" + DateUtil.format(brxx1.getCydate(), "yyyy-MM-dd") + ",该病人" + brxx1.getShzt() + "日内重复住院，");
            ybgkWgjl.setJktype("cfzy");
            ybgkWgjl.setDelFlag("0");
            ybgkWgjl.setUpdateBy("localhost");
            ybgkWgjl.setTshOper("每日自动");
            ybgkWgjl.setFyType("");
            ybgkWgjl.setJkCode("6");
            ybgkWgjl.setXzlb("");
            ybgkWgjl.setCreateBy("每日自动审核");
            ids_log.add(ybgkWgjl);
          }
        }
      }
    }


//    List<Fyxx> fyxxes = fyxxService.selectFyxxByJzh(is_reg_id);
    List<Fyxx> fyxxes = new ArrayList<>();
    Date brdate = brxx.getCydate() == null ? brxx.getRydate() : brxx.getCydate();
    // 出入院时间都没有的话，就不审核了
    if (brdate == null) {
      return getDataTable(new ArrayList<>());
    }
    if (ybjkJklog.getBrtype() != null && "1".equals(ybjkJklog.getBrtype())) {
      // 门诊费用
      fyxxes.addAll(fyxxService.selectMzFyxxByJzh(is_reg_id));
    }else if (brdate != null || brdate.after(Date.from(LocalDate.now().minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant()))) {
      // 最近一年
//    	System.out.println("aa");
      fyxxes.addAll(fyxxService.selectPreCheckFyxxByJzh(is_reg_id));
      if (fyxxes.size()==0) {
        // 从历史费用表取费用明细
        fyxxes.addAll(fyxxService.selectHistoryCheckFyxx(is_reg_id, brdate));
      }
    } else {
      // 从历史费用表取费用明细
      fyxxes.addAll(fyxxService.selectHistoryCheckFyxx(is_reg_id, brdate));
      //历史库没得，从当前库找
      if (fyxxes.size()==0) {
        fyxxes.addAll(fyxxService.selectPreCheckFyxxByJzh(is_reg_id));
      }
    }
    Double ld_jszje = 0D;
    ld_jszje = brxx.getZje();

    //add by lt 出院病人比较费用  2025-04-25
    if ("1".equals(is_cysh_bjfy)&&"2".equals(brxx.getBrtype())&&ld_jszje!=null&&ld_jszje>0) {
      BigDecimal total = BigDecimal.ZERO;
      //计算费用总金额
      for (Fyxx item : fyxxes) {
        total = total.add(item.getJe());
      }
      Double ld_total = 0D;
      if (total!=null) {
        ld_total = total.doubleValue();
        double ld_cebl = Math.abs(ld_total - ld_jszje) / ld_jszje;

        if (ld_jszje>0&&ld_cebl>0.02&&Math.abs(ld_total - ld_jszje)>100){
          System.out.println(is_reg_id+"---"+brxx.getZyh()+"---------------------------------费用不同，重转费用："+Math.abs(ld_total - ld_jszje));
          try {
            refreshCost(brxx.getJzh());
            fyxxes = new ArrayList<>();
            if (brdate != null || brdate.after(Date.from(LocalDate.now().minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant()))) {
              // 最近一年
//    				    	System.out.println("aa");
              fyxxes.addAll(fyxxService.selectPreCheckFyxxByJzh(is_reg_id));
              if (fyxxes.size()==0) {
                // 从历史费用表取费用明细
                fyxxes.addAll(fyxxService.selectHistoryCheckFyxx(is_reg_id, brdate));
              }
            } else {
              // 从历史费用表取费用明细
              fyxxes.addAll(fyxxService.selectHistoryCheckFyxx(is_reg_id, brdate));
              //历史库没得，从当前库找
              if (fyxxes.size()==0) {
                fyxxes.addAll(fyxxService.selectPreCheckFyxxByJzh(is_reg_id));
              }
            }
          } catch (Exception e) {
            // TODO: handle exception
          }

        }
      }
    }


    // 获取当前日期
    LocalDate today = LocalDate.now();
    // 获取 10 天前的日期
    LocalDate tenDaysAgo = today.minusDays(10);
    // 将日期转换为当天的起始时间（00:00:00）
    LocalDateTime ldt_fyfrom = tenDaysAgo.atStartOfDay();
    long diffInSeconds=0;
    if (idt_predate!=null) {
      long diffInMillis = Math.abs(new Date().getTime() - idt_predate.getTime());
      diffInSeconds = diffInMillis / 1000;

    }
    idt_predate = new Date();

    int li_90dayflag = 0;

    if("0".equals(brxx.getZyzt())&&brxx.getCydate()!=null) {
      LocalDate cyLocalDate = brxx.getCydate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
      // 计算两个日期之间的天数差
      if (ChronoUnit.DAYS.between(cyLocalDate, today)>90) {
        li_90dayflag = 1;
      }
    }



    //费用超过2000条记录，只删除近9天的
    if (fyxxes.size()>2000&"1".equals(ls_plshflag)) {
      YbgkWgjl ybgkWgjl = new YbgkWgjl();
      ybgkWgjl.setJzh(is_reg_id);
      // 定义日期时间格式化器
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00");
      // 将 10 天前的时间格式化为字符串
      String formattedDateTime = ldt_fyfrom.format(formatter);
      ybgkWgjl.setCreateDateStart(formattedDateTime);
      ybgkWgjlService.deleteYbgkWgjlByJzhandDate(ybgkWgjl);
    }else {
      ybgkWgjlService.deleteYbgkWgjlByJzh(is_reg_id);
    }
    ybgkWgjlService.deleteYbgkWgjlGdjcByJzh(is_reg_id);

    Zdxx zdxx1 = new Zdxx();
    zdxx1.setJzh(is_reg_id);



    List<Zdxx> ids_zlhis_zdxx = zdxxService.selectZdxxList(zdxx1);
    if (ids_zlhis_zdxx.size() > 0) {
      for (int i = 0; i < ids_zlhis_zdxx.size(); i++) {
        ls_zdqk = ls_zdqk + "," + "("+ids_zlhis_zdxx.get(i).getZdcode()+")"+ids_zlhis_zdxx.get(i).getZdname();
      }
    }

    //同时加上结算表里的诊断-陈家桥医院保存得不全
    if ("1".equals(is_cysh_tsqjszd)) {
      List<Zdxx> ids_zlhis_zdxx1 = zdxxService.selectjsxxzdxx(zdxx1);
      if (ids_zlhis_zdxx.size() > 0&&ids_zlhis_zdxx.get(0)!=null&&ids_zlhis_zdxx.get(0).getZdname()!=null) {
        ls_zdqk =  ls_zdqk+ "," +ids_zlhis_zdxx.get(0).getZdname();
      }
    }

    Date ldt_minfydate,ldt_maxfydate;
    if (fyxxes.size()>2) {
      //是按时间倒序的
      ldt_maxfydate=fyxxes.get(0).getFydate();
      ldt_minfydate = fyxxes.get(fyxxes.size() - 1).getFydate();
    }else
    {
      ldt_minfydate = brxx.getRydate();
      ldt_maxfydate=brxx.getCydate();
    }
    if(brxx.getRydate()==null&&ldt_minfydate!=null) {
      brxx.setRydate(ldt_minfydate);
    }

    if(brxx.getCydate()==null&&ldt_maxfydate!=null) {
      brxx.setCydate(ldt_maxfydate);
    }

    if(ldt_maxfydate!=null&&brxx.getCydate()!=null&&ldt_maxfydate.compareTo(brxx.getCydate())>0) {
      brxx.setCydate(ldt_maxfydate);
    }


    if(brxx.getRydate()==null) {
      brxx.setRydate(new Date(123, 0, 1));
    }
    if(brxx.getCydate()==null) {
      brxx.setCydate(new Date());
    }

    if(ldt_minfydate==null) {
      ldt_minfydate = new Date(123, 0, 1);
    }
    if(ldt_maxfydate==null) {
      ldt_maxfydate = new Date();
    }

    if(brxx.getRydate().after(ldt_minfydate)) {
      brxx.setRydate(ldt_minfydate);
    }
    if(brxx.getCydate().before(ldt_maxfydate)) {
      brxx.setCydate(ldt_maxfydate);
    }


    LocalDate localDate1 = brxx.getRydate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    LocalDate localDate2 = brxx.getCydate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

    // 计算两个 LocalDate 对象相差的天数
    Double  ld_sjzyts = (double) java.time.temporal.ChronoUnit.DAYS.between(localDate1, localDate2);
    if (ld_sjzyts==null) {
      ld_sjzyts = 1D;
    }
    brxx.setSjzyts(ld_sjzyts);
    int hour = DateUtil.hour(brxx.getCydate(), true);
    if(hour >= 12) {
      brxx.setSjzyts(ld_sjzyts+0.5);
    }
    if(hour >= 18||"1".equals(brxx.getZyzt())) {
      brxx.setSjzyts(ld_sjzyts+1);
    }



    System.out.println(is_reg_id+"---"+brxx.getZyh()+"--"+hour+"-记录数量：" + fyxxes.size()+" 时间："+String.valueOf(diffInSeconds)+"--"+brxx.getRydate().toLocaleString()+brxx.getCydate().toLocaleString()+"zyts="+brxx.getSjzyts());

//    System.out.println(brxx.getRydate().toLocaleString()+brxx.getCydate().toLocaleString()+"ld_sjzyts="+ld_sjzyts);

    for (int i = 0; i < fyxxes.size(); i++) {
      Fyxx fy = fyxxes.get(i);
      Fyxx fyxx = new Fyxx();
      BeanUtils.copyProperties(fy, fyxx);

      //处理单价为0的情况
      if ((fyxx.getPrice()==null||new BigDecimal("0").compareTo(fyxx.getPrice())==0)&&fyxx.getSl()!=null&&new BigDecimal("0").compareTo(fyxx.getSl())!=0&&fyxx.getJe()!=null) {
        fyxx.setPrice(fyxx.getJe().divide(fyxx.getSl(), 2, RoundingMode.HALF_UP));
      }



      fyxx.setZdxx(ls_zdqk);


      fyxx.setZdxx(ls_zdqk);
      if (fyxxes.size()>2000&"1".equals(ls_plshflag)) {
        Date ldt_fydate = fyxx.getFydate();
        if (ldt_fydate==null) {
          continue;
        }
        LocalDateTime localDateTime= ldt_fydate.toInstant()
          .atZone(ZoneId.systemDefault())
          .toLocalDateTime();
        if(localDateTime.compareTo(ldt_fyfrom)<0) {
          continue;
        }
      }


      //add by lt 退费不处理
      if (fyxx.getJe()!=null) {
        if (fyxx.getJe().doubleValue()<0) {
          continue ;
        }
      }

      // 限制病种
      of_check_xzbz(brxx, fyxx, ids_log,"zysh","",ids_zlhis_zdxx,fyxxes,"");
      //超过90天的不审核病历,门诊不管
      if (li_90dayflag==0&&"2".equals(brxx.getBrtype())) {
        of_check_lcbx(brxx, fyxx, ids_log);
      }

//      of_check_blgyxm(brxx, fyxx, ids_log);
      String ls_fymname = fyxx.getXmmc();
      if (ls_fymname==null) {
        ls_fymname = "";
      }

//      if (ls_fymname.contains("静脉输液")||ls_fymname.contains("麻醉")) {
//    	  of_check_blgyxm(brxx, fyxx, ids_log);
//	  }else
//	  {
//         mem_check_blgyxm(brxx, fyxx, ids_log, fyxxes);
//      }
//      of_check_rqslxz(brxx, fyxx, ids_log, "");

      of_check_xzyydj(brxx, fyxx, ids_log);

      of_check_zyslxz(brxx, fyxx, ids_log, "",fyxxes);
      of_check_zysyzdts(brxx, fyxx, ids_log,"",fyxxes);

      //检查男性
      of_check_xzlx(brxx, fyxx, ids_log);
      //检查女性
      of_check_xznx(brxx, fyxx, ids_log);
      of_check_xzet(brxx, fyxx, ids_log);
      //限制年龄
      of_check_xzage(brxx, fyxx, ids_log);
      of_check_xxmjssy(brxx, fyxx, ids_log,"",fyxxes,"");
      of_check_xzks(brxx, fyxx, ids_log);
      of_check_jjz(brxx, fyxx, ids_log,"zysh","",ids_zlhis_zdxx);

      of_check_gdjc(brxx, fyxx, ids_log,fyxxes,"");
      //限制职称
      if ("1".equals(is_check_zc)) {
//        of_check_xzzc(brxx, fyxx, ids_log);
      }

      try {
        mem_check_rqslxz(brxx, fyxx, ids_log,fyxxes);
        mem_check_blgyxm(brxx, fyxx, ids_log, fyxxes);
      } catch (Exception e) {
        // TODO: handle exception
      }
    }

    ids_log = ids_log.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getJktype() + ";" + o.getJklog()+ ";" + o.getJzh()))), ArrayList::new));

    if ("1".equals(wgjl_wtfjc)) {
      Date wrongRefundCheckTime = brxx.getCydate() == null || "1".equals(brxx.getZyzt()) ? new Date() : brxx.getCydate();

      Fyxx fyQuery = new Fyxx();
      fyQuery.setJzh(is_reg_id);
      fyQuery.setOpdate(wrongRefundCheckTime);
      List<Fyxx> refundList = fyxxService.selectRefundByOpdateAndXmbm(fyQuery);

      for (int i = ids_log.size() -1; i >= 0; i--) {
        YbgkWgjl wgjl = ids_log.get(i);
        if (refundList.stream().anyMatch(fy -> wgjl.getFymId().equals(fy.getXmbm())) ||
          refundList.stream().anyMatch(fy -> "blgyxm".equals(wgjl.getJktype()) && wgjl.getJklog().contains(fy.getXmmc())) ) {
          ids_log.remove(i);
        }
      }

//      for (int i = ids_log.size() - 1; i >= 0; i--) {
//        if(!"rqzdyl".equals(ids_log.get(i).getJktype()) && !"blgyxm".equals(ids_log.get(i).getJktype())) {
//          continue;
//        }
//        YbgkWgjl wgjl = ids_log.get(i);
//        Fyxx fyQuery = new Fyxx();
//        fyQuery.setJzh(wgjl.getJzh());
//        fyQuery.setOpdate(wrongRefundCheckTime);
//        fyQuery.setXmbm(wgjl.getFymId());
//
//        List<Fyxx> fyxxList = fyxxService.selectRefundByOpdateAndXmbm(fyQuery);
//        if(!fyxxList.isEmpty()) {
//          ids_log.remove(i);
//        }
//      }
    }

    if ("1".equals(yb_wgjl_exclude)) {
      List<YbgkWgjlHistory> ybExcludeWgjl = ybgkWgjlHistoryService.selectYbgkWgjlHistoryList(new YbgkWgjlHistory().setBrid(brxx.getBrid()).setZyid(brxx.getZyid()).setJltype("3"));
      for (int i = ybExcludeWgjl.size() - 1; i >= 0; i--) {
        YbgkWgjlHistory history = ybExcludeWgjl.get(i);
        if(ids_log.stream().anyMatch(item -> item.getFymId().equals(history.getFymId()) && item.getJktype().equals(history.getJktype())) ) {
          ids_log.remove(i);
        }
      }
    }

    try {
      if("1".equals(wgjl_exclude)) {
        List<YbgkWgjlHistory> excludeWgjl = ybgkWgjlHistoryService.selectYbgkWgjlHistoryList(new YbgkWgjlHistory().setBrid(brxx.getBrid()).setZyid(brxx.getZyid()).setJltype("2"));
        for (int i = ids_log.size() - 1 ; i >= 0 ; i--) {
          YbgkWgjl wgjl = ids_log.get(i);
          String jklog = wgjl.getJklog();
          if (excludeWgjl.stream().anyMatch(item -> item.getFingerPrint().equals(Md5Utils.hash(
            item.getJklog().contains("]限") ? wgjl.getJklog() : wgjl.getJklog().replace("限", "适应症")
          )))) {
            ids_log.remove(i);
          }
        }
      }
    } catch (Exception e) {
      logger.error("判断排除记录失败: {}, {}, {}", e.getMessage(), brxx.getBrid(), brxx.getZyid());
    }

    for (int i = 0; i < ids_log.size(); i++) {
      if ("gdjc".equals(ids_log.get(i).getJktype())&&"0".equals(is_shgdjc) ) {
        ybgkWgjlService.insertYbgkWgjlGdjc(ids_log.get(i));
      } else {
        ybgkWgjlService.insertYbgkWgjl(ids_log.get(i));
      }
    }

    // 批量更新违规历史记录的处理状态
    try {
      if ("1".equals(wgjl_history)) {
        ybgkWgjlHistoryService.batchUpdateStatus(brxx.getBrid(), brxx.getZyid(), ids_log);
      }
    } catch (Exception e) {
      logger.error("写入历史记录失败：{}, {}, {}", e.getMessage(), brxx.getBrid(), brxx.getZyid());
    }

    //启用病历质控
    if ("1".equals(is_bl_date_zk)&&"2".equals(brxx.getBrtype())) {
      ybgkWgjlService.insertblzk(is_reg_id);
    }

    YbgkWgjl ybgkWgjl1 = new YbgkWgjl();
    ybgkWgjl1.setJzh(ybjkJklog.getJzh());

    List<YbgkWgjl> jklogList = ybgkWgjlService.selectYbgkWgjlListByHospital(ybgkWgjl1);

    if (jklogList.size() > 1) {
      jklogList = jklogList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getJktype() + ";" + o.getJklog()))), ArrayList::new));
      jklogList.sort(Comparator.comparing(YbgkWgjl::getJklog));
    }



    //先暂时不执行 add by lt 2025-06-08
//    if (isSaveCheckRecord&&"2".equals(brxx.getBrtype())) {
//      ybgkBrxxCheckjlService.updateBrxxCheckjl(brxx, jklogList);
//    }

    list = new ArrayList<>();
    list.add(jklogList);
//    System.out.println(new Date());
    return getDataTable(list);
  }

  private void mem_check_rqslxz(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log, List<Fyxx> fyxxList) {
    Map<String, Object> rqzdyl = of_get_tiptype("rqzdyl", fyxx, ids_log, brxx);
    String tiptype = (String) rqzdyl.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }
    SimpleDateFormat sdfDate1 = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat sdfDateHour1 = new SimpleDateFormat("yyyy-MM-dd HH");
    BigDecimal lb_oldsl = fyxx.getSl();
    BigDecimal lb_oldje = fyxx.getJe();
    BigDecimal lb_oldprice = fyxx.getPrice();

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) rqzdyl.get("xzxm_list");
    if (ybgkXzxmList.size() <= 0) {
      return;
    }
    String is_xmtype = (String) rqzdyl.get("xmtype");

    for (YbgkXzxm xzxm : ybgkXzxmList) {
      String xzcode = xzxm.getXzcode();
      String xzvalue = xzxm.getXzvalue();
      String[] xzvalueSplit = xzvalue.split("\\|");
      int dayNum = Integer.parseInt(xzvalueSplit[0]);
      int xzsl = Integer.parseInt(xzvalueSplit[1]);
      String ls_bz="";
      if (xzxm.getYbxz()!=null) {
        ls_bz = xzxm.getYbxz();
      }

//      String ldt_from = of_get_fromdate(xzcode, fyxx.getFydate(), xzvalueSplit[0]);

      YbgkWgjl ybgkWgjl = new YbgkWgjl();
      ybgkWgjl.setJzh(fyxx.getJzh());
      ybgkWgjl.setFymId(xzxm.getXmbm());
      ybgkWgjl.setCreateDate(sdfDate1.format(fyxx.getFydate()));
      ybgkWgjl.setSl(BigDecimal.ZERO);

//      if  ("001203000010000-ABJB0001".equals(fyxx.getXmbm())||"001203000010000-ABJ.01".equals(fyxx.getXmbm())) {
//    	  String ls_temp1="1";
//    	  System.out.print(ls_temp1);
//      }

      boolean is_dlcx = false;
      if ("001203000010000-ABJB0001".equals(fyxx.getXmbm()) || "001203000010000-ABJA0001".equals(fyxx.getXmbm()) ||
        "003106030010000-310603001".equals(fyxx.getXmbm()) || "003106030020000-310603002".equals(fyxx.getXmbm()) ||
        "001203000010000-120300001".equals(fyxx.getXmbm()) || "003106070060000-310607005".equals(fyxx.getXmbm())) {
//        BigDecimal zsl = fyxxService.selectZslByXmbmArray(brxx.getJzh(), fyxx.getFydate(),
//          new String[]{"001203000010000-ABJB0001", "001203000010000-ABJA0001","003106030010000-310603001", "003106030020000-310603002",
//            "003106070060000-310607005", "001203000010000-120300001"});
//        System.out.println(zsl);
//
//        if (zsl.compareTo(new BigDecimal("24")) > 0) {
//          String log = String.format("项目【氧气吸入、辅助呼吸、通气】共同一天内不能超24小时，当前已开[%s]次，日期为[%s]",
//            zsl, sdfDate.format(fyxx.getFydate()));
//          setWgjlItem(log, "rqzdyl", fyxx, ids_log, brxx, is_xmtype, "0");
//        }
        if ("1".equals(xzvalueSplit[0]) && fyxx.getSl().compareTo(BigDecimal.valueOf(xzsl)) > 0) {
          String LOG = String.format("项目【氧气吸入、辅助呼吸、通气】共同一天内不能超24小时，当前已开[%s]次，日期为[%s]",
            gf_null_dec(fyxx.getSl()), sdfDate.format(fyxx.getFydate()));
          BigDecimal ldec_ce = (fyxx.getSl().subtract(BigDecimal.valueOf(xzsl)));
          //超出的金额
          BigDecimal amountDifference = fyxx.getPrice().multiply(ldec_ce);

          fyxx.setJe(amountDifference);
          fyxx.setSl(ldec_ce);
          setWgjlItem(LOG, "rqzdyl", fyxx, ids_log, brxx, is_xmtype, "0");
          fyxx.setJe(lb_oldprice);
          fyxx.setSl(lb_oldsl);
        }
      }else if ("1".equals(xzvalueSplit[0]) && fyxx.getSl().compareTo(BigDecimal.valueOf(xzsl)) > 0) {


        is_dlcx = true;
        //只检查出院
//        if("0".equals(brxx.getZyzt())) {
//          if (fyxx.getXmmc().contains("床位费")) {
//            Double ld_cwf_sum = 0D;
//            ld_cwf_sum = of_get_cwfsum(fyxxList);
//            if(ld_cwf_sum<=brxx.getSjzyts()) {
//              continue;
//            }
//          }
//          if (fyxx.getXmmc().contains("诊查费")) {
//            Double ld_cwf_sum = 0D;
//            ld_cwf_sum = of_get_cwfsum(fyxxList);
//            if(ld_cwf_sum<=brxx.getSjzyts()) {
//              continue;
//            }
//          }
//        }


        fyxx.setSl(lb_oldsl);
        fyxx.setJe(lb_oldje);
//        if (is_dlcx) {
        //出院病人根据设置只写总量
        if (brxx.getSjzyts()!=null&&brxx.getSjzyts()>0&&xzsl>0&&ls_bz!=null&&(ls_bz.contains("住院天数")||ls_bz.contains("不大于24小时")||"床/日".equals(fyxx.getDw()))) {
          Double ld_yxzl = brxx.getSjzyts()*xzsl;//允许的总量
          Double ld_sjzl = 0D;
          for (Fyxx fyxx2 : fyxxList) {
            if (fyxx2.getXmbm()!=null&&fyxx2.getXmbm().equals(fyxx.getXmbm())) {
              ld_sjzl += fyxx2.getSl().doubleValue();
            }
          }
          if (ld_sjzl!=null&&ld_sjzl>ld_yxzl) {
//      	    	"项目按日或按时[",ls_bcfym,"]上限为：[",li_fycount,"]患者实际：","[",li_zsl,"]
            String ls_jklog1 = "项目[" + fyxx.getXmmc() + "]上限为" + ld_yxzl + "患者实际[" + ld_sjzl +"]";
            fyxx.setSl(new BigDecimal(ld_sjzl - ld_yxzl));
            fyxx.setJe(new BigDecimal(ld_sjzl - ld_yxzl).multiply(fyxx.getPrice()));
            setWgjlItem(ls_jklog1, xzcode, fyxx, ids_log, brxx, is_xmtype, "0", ybgkXzxmList.get(0).getShowcolor());
            fyxx.setSl(lb_oldsl);
            fyxx.setJe(lb_oldje);
            //检查了总量，不再检查最后一天的床位费
            continue;
          }

        }else {
          BigDecimal ldec_ce = (fyxx.getSl().subtract(BigDecimal.valueOf(xzsl)));
          String ls_jklog = "项目[" + fyxx.getXmmc() + "]" + dayNum + "天开的数量[" + gf_null_dec(fyxx.getSl()) +
            "]已经超出设定的数量[" + xzsl + "],日期为:" + sdfDate1.format(fyxx.getFydate());
          //修改数量和单价
          fyxx.setSl(ldec_ce);
          fyxx.setJe(ldec_ce.multiply(fyxx.getPrice()));
          setWgjlItem(ls_jklog, xzcode, fyxx, ids_log, brxx, is_xmtype, "0", ybgkXzxmList.get(0).getShowcolor());
        }
      } else if (!"1".equals(xzvalueSplit[0])) {
        BigDecimal xmsl = ybgkWgjlService.selectRqslxz(ybgkWgjl);
        if (xmsl.compareTo(BigDecimal.valueOf(xzsl)) > 0) {
          String ls_jklog = "项目[" + fyxx.getXmmc() + "]" + dayNum + "天开的数量[" + gf_null_dec(fyxx.getSl()) +
            "]已经超出设定的数量[" + xzsl + "],日期为:" + sdfDate.format(fyxx.getFydate());
          is_dlcx = true;
          BigDecimal ldec_ce = (fyxx.getSl().subtract(BigDecimal.valueOf(xzsl)));
          fyxx.setSl(ldec_ce);
          setWgjlItem(ls_jklog, xzcode, fyxx, ids_log, brxx, is_xmtype, "0", ybgkXzxmList.get(0).getShowcolor());


        }
      }

      //床位费才检查
      if (brxx.getCydate() != null && sdfDate1.format(fyxx.getFydate()).equals(sdfDate1.format(brxx.getCydate()))&&(fyxx.getXmmc().contains("床位费")||fyxx.getXmmc().contains("双人间")||fyxx.getXmmc().contains("三人间")||fyxx.getXmmc().contains("单人间")||fyxx.getXmmc().contains("四人间"))) {
        ybgkWgjlService.selectRqslxz(ybgkWgjl);
      }

      //  if (is_dlcx) {
//    String ls_log = ybgkWgjlService.selectRqslxzZl(ybgkWgjl);
//    if (StrUtil.isNotEmpty(ls_log)) {
//      setWgjlItem(ls_log, xzcode, fyxx, ids_log, brxx, is_xmtype, tiptype);
//    }
//   }


    }


    //如果是床位费，执行特殊审核逻辑
    if (fyxx.getXmmc().contains("床位费") || fyxx.getXmmc().contains("级护理") || fyxx.getXmmc().contains("诊察费") || fyxx.getXmmc().contains("诊查费")) {
      if (brxx.getZyzt() == null || "1".equals(brxx.getZyzt())) {
        return;
      }
      //只检查出院

      Date fydate = fyxx.getFydate();
      Date cydate = brxx.getCydate();
      Date rydate = brxx.getRydate();

      if (cydate != null && rydate != null && DateUtil.isSameDay(cydate, rydate)) {
        return;
      }
      if (fydate == null || cydate == null) {
        logger.error("审核出院日床位费失败，费用时间或出院时间为空: {}, {}", brxx.getName(), brxx.getJzh());
        return;
      }
      if (!DateUtil.isSameDay(cydate, fydate)) {
        return;
      }

      Double ld_cwf_sum = 0D;
      if (fyxx.getXmmc().contains("床位费")) {
        ld_cwf_sum = of_get_cwfsum(fyxxList);
      } else if (fyxx.getXmmc().contains("级护理")) {
        ld_cwf_sum = of_get_hlfsum(fyxxList);
      } else if (fyxx.getXmmc().contains("诊察费") || fyxx.getXmmc().contains("诊查费")) {
        ld_cwf_sum = of_get_zcfsum(fyxxList);
      }
      if(ld_cwf_sum<=brxx.getSjzyts()) {
        return;
      }

      int hour = DateUtil.hour(cydate, true);
      if (hour >= 12 &&hour < 18&& fyxx.getSl().compareTo(new BigDecimal("0.5")) > 0&&fyxx.getSl().compareTo(new BigDecimal("1.5"))<0) {
//        ld_cwf_sum = of_get_cwfsum(fyxxList);
//        if(ld_cwf_sum<=brxx.getSjzyts()) {
//          return;
//        }
        String ls_log = "项目【" + fyxx.getXmmc() + "】用量为【" + fyxx.getSl() + "】，出院时间为: " + sdfDateHour1.format(brxx.getCydate()) + ", 当日床位费只应计半天";
        setWgjlItem(ls_log, "rqzdyl", fyxx, ids_log, brxx, is_xmtype, tiptype);
      } else if(hour < 12 && fyxx.getSl().compareTo(BigDecimal.ZERO) > 0&&fyxx.getSl().compareTo(new BigDecimal("1.5"))<0) {
//        ld_cwf_sum = of_get_cwfsum(fyxxList);
//        if(ld_cwf_sum<=brxx.getSjzyts()) {
//          return;
//        }
        String ls_log = "项目【" + fyxx.getXmmc() + "】用量为【" + fyxx.getSl() + "】，出院时间为: " + sdfDateHour1.format(brxx.getCydate()) + ", 当日不应计床位费";
        setWgjlItem(ls_log, "rqzdyl", fyxx, ids_log, brxx, is_xmtype, tiptype);
      }
    }

    fyxx.setSl(lb_oldsl);
    fyxx.setJe(lb_oldje);

  }

  public Double of_get_cwfsum(List<Fyxx> fyxxList) {
    Double ld_cwf_sum = 0D;
    for (Fyxx fyxx2 : fyxxList) {
      if ((fyxx2.getXmmc().contains("床位费")||fyxx2.getXmmc().contains("双人间")||fyxx2.getXmmc().contains("三人间")||fyxx2.getXmmc().contains("单人间")||fyxx2.getXmmc().contains("四人间"))&&fyxx2.getSl()!=null&&!fyxx2.getXmmc().contains("陪伴")) {
        ld_cwf_sum += fyxx2.getSl().doubleValue();
      }
    }
    return ld_cwf_sum;
  }

  public Double of_get_hlfsum(List<Fyxx> fyxxList) {
    Double ld_hlfsum = 0D;
    for (Fyxx fy : fyxxList) {
      if (fy.getXmmc() != null && fy.getXmmc().contains("级护理")) {
        ld_hlfsum += fy.getSl().doubleValue();
      }
    }
    return ld_hlfsum;
  }

  //获取诊查费
  public Double of_get_zcfsum(List<Fyxx> fyxxList) {
    Double ld_hlfsum = 0D;
    for (Fyxx fy : fyxxList) {
      if (fy.getXmmc() != null && (fy.getXmmc().contains("诊察费") || fy.getXmmc().contains("诊查费"))) {
        ld_hlfsum += fy.getSl().doubleValue();
      }
    }
    return ld_hlfsum;
  }


  public boolean of_check_strisnum(String str) {
    if (str == null || "".equals(str)) {
      return false;
    }
    if (str.chars().allMatch(Character::isDigit)) {
      return true;
    } else {
      return false;
    }

  }


  /**
   * 审核特定日期数量
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   * @param caller  调用方
   */
  @Async
  public void of_check_rqslxz(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log, String caller) {
    Map<String, Object> rqzdyl = of_get_tiptype("rqzdyl", fyxx, ids_log, brxx);
    String tiptype = (String) rqzdyl.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

//    if (!ids_log.isEmpty() && !ids_log.stream().anyMatch(item ->
//      { try { return
//          ("rqzdyl".equals(item.getJktype()) && item.getFymId().equals(fyxx.getXmbm()) && sdfDateTime.parse(item.getCreateDate()).compareTo(fyxx.getFydate()) > 0);  }
//        catch (ParseException e) { throw new RuntimeException(e); } }
//    )) { return; }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) rqzdyl.get("xzxm_list");
    if (ybgkXzxmList.size() <= 0) {
      return;
    }

    BigDecimal ldec_sl_old = fyxx.getSl();

    //门诊计算每天的用量
    if ("1".equals(brxx.getBrtype())&&brxx.getNum()>0&&fyxx.getSl()!=null&&"hischeckRule".equals(caller)) {
      try {
        double  ldec_mrsl = (fyxx.getSl().doubleValue())/brxx.getNum();
        fyxx.setSl(new BigDecimal(ldec_mrsl));
      } catch (Exception e) {
        // TODO: handle exception
      }

    }
    SimpleDateFormat sdfDate1 = new SimpleDateFormat("yyyy-MM-dd");

    String ls_ybxmlsh, ls_xzvalue, is_jktype;
    for (int i = 0; i < ybgkXzxmList.size(); i++) {

      ls_ybxmlsh = ybgkXzxmList.get(i).getXmbm();// 医保项目流水号
      ls_xzvalue = ybgkXzxmList.get(i).getXzvalue();// 限制的数量
      is_jktype = ybgkXzxmList.get(i).getXzcode();// 限制类别

      int index = ls_xzvalue.indexOf("|");
      if (index != -1) {
        String leftStr = ls_xzvalue.substring(0, index);
        String rightStr = ls_xzvalue.substring(index + 1);

        if (!of_check_strisnum(leftStr)) {
          continue;
        }

        BigDecimal ldec_xzzsl = new BigDecimal(rightStr);
        String is_xmtype = (String) rqzdyl.get("xmtype");

        if( "1".equals(brxx.getBrtype())&&"1".equals(leftStr)) {
          if(fyxx.getSl()!=null&&fyxx.getSl().compareTo(ldec_xzzsl)>0) {
            setWgjlItem("项目[" + fyxx.getXmmc() + "]" + leftStr + "天开的数量[" + gf_null_dec(fyxx.getSl()) +
              "]已经超出设定的数量[" + gf_null_dec(ldec_xzzsl) + "],日期为:" + sdfDate.format(fyxx.getFydate()), "rqzdyl", fyxx, ids_log, brxx, is_xmtype, "0");
          }
          continue;
        }

//        String ldt_from = of_get_fromdate(is_jktype, fyxx.getFydate(), leftStr);// 取得

        BigDecimal sl = BigDecimal.ZERO;
        if ("hischeckRule".equals(caller)) {
          sl = fyxx.getSl();
        }
        

        YbgkWgjl ybgkWgjl = new YbgkWgjl();
        ybgkWgjl.setJzh(fyxx.getJzh());
        ybgkWgjl.setFymId(ls_ybxmlsh);
        ybgkWgjl.setCreateDate(sdfDate1.format(fyxx.getFydate()));
        ybgkWgjl.setSl(sl);

        String ls_log = ybgkWgjlService.selectRqslxzZl(ybgkWgjl);



        //如果总量超出限制，计入违规记录
        if (ls_log != null && !"".equals(ls_log)) {
          setWgjlItem(ls_log, is_jktype, fyxx, ids_log, brxx, is_xmtype, tiptype, ybgkXzxmList.get(0).getShowcolor());


        }

//        BigDecimal idec_bcsl = fyxx.getSl();
//        BigDecimal ldec_zsl;
//        ldec_zsl = ybgkWgjlService.selectRqslxz(ybgkWgjl);
//        if (ldec_zsl==null) {
//          ldec_zsl = BigDecimal.valueOf(0);
//        }
//        if (sl==null) {
//          sl = BigDecimal.valueOf(0);
//        }
        if ("001203000010000-ABJB0001".equals(fyxx.getXmbm()) || "001203000010000-ABJA0001".equals(fyxx.getXmbm()) ||
          "003106030010000-310603001".equals(fyxx.getXmbm()) || "003106030020000-310603002".equals(fyxx.getXmbm()) ||
          "001203000010000-120300001".equals(fyxx.getXmbm()) || "003106070060000-310607005".equals(fyxx.getXmbm())) {
          BigDecimal zsl = fyxxService.selectZslByXmbmArray(brxx.getJzh(), fyxx.getFydate(),
            new String[]{"001203000010000-ABJB0001", "001203000010000-ABJA0001","003106030010000-310603001", "003106030020000-310603002",
              "003106070060000-310607005", "001203000010000-120300001"});
          System.out.println(zsl);

          if (zsl.compareTo(new BigDecimal("24")) > 0) {
            String log = String.format("项目【氧气吸入、辅助呼吸、通气】共同一天内不能超24小时，当前已开[%s]次，日期为[%s]",
              zsl, sdfDate.format(fyxx.getFydate()));
            setWgjlItem(log, "rqzdyl", fyxx, ids_log, brxx, is_xmtype, "0");
          }
        } else {
          BigDecimal idec_bcsl = fyxx.getSl();
          BigDecimal ldec_zsl;
          ldec_zsl = ybgkWgjlService.selectRqslxz(ybgkWgjl);
          if (ldec_zsl==null) {
            ldec_zsl = BigDecimal.valueOf(0);
          }
          if (sl==null) {
            sl = BigDecimal.valueOf(0);
          }

          ldec_zsl = ldec_zsl.add(sl);
          

          if (ldec_zsl.compareTo(ldec_xzzsl) > 0) {
            String ls_jklog = "项目[" + fyxx.getXmmc() + "]" + leftStr + "天开的数量[" + gf_null_dec(ldec_zsl) +
              "]已经超出设定的数量[" + gf_null_dec(ldec_xzzsl) + "],日期为:" + sdfDate1.format(fyxx.getFydate());
            setWgjlItem(ls_jklog, is_jktype, fyxx, ids_log, brxx, is_xmtype, tiptype, ybgkXzxmList.get(0).getShowcolor());
          }
        }



//        // 是否要检查每日使用次数
//        if ("0".equals(is_check_times_for_day)) {
//          continue;
//        }
//
//        Fyxx fyQuery = new Fyxx();
//        fyQuery.setJzh(fyxx.getJzh());
//        fyQuery.setXmbm(ls_ybxmlsh);
//        // 获取到该病人的该项目的记录
//        List<Fyxx> fyxxes = fyxxService.selectFyxxList(fyQuery);
//        Date fydate = fyxx.getFydate();
//
//        // 准备时间格式化对象
//        String dateStr = sdfDate.format(fydate);
//
//        BigDecimal projectSum = new BigDecimal("0");
//        // 遍历项目记录，计算出本项目当日使用的数量
//        for (Fyxx fy : fyxxes) {
//          Date date = fy.getFydate();
//          String format = sdfDate.format(date);
//          if(dateStr.equals(format)){
//            projectSum = projectSum.add(fy.getSl());
//          }
//        }
//
//        // 如果当日使用数量大于设置的单日限量，则计入违规记录
//        if(projectSum.compareTo(ldec_xzzsl) > 0) {
//          String ls_jklog = "项目[" + fyxx.getXmmc() + "]" + leftStr + "天开的数量[" + projectSum +
//                  "]已经超出设定的数量[" + gf_null_dec(ldec_xzzsl) + "],日期为:" + dateStr;
//          setWgjlItem(ls_jklog, is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
//        }


      }

    }
    //恢复单价
    fyxx.setSl(ldec_sl_old);

  }

  /**
   * 审核项目数量
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_zyslxz(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log, String caller,List<Fyxx> fyxxList) {
    Map<String, Object> zyzdyl = of_get_tiptype("zyzdyl", fyxx, ids_log, brxx);
    String tiptype = (String) zyzdyl.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) zyzdyl.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {
      for (YbgkXzxm ybgkXzxm : ybgkXzxmList) {
        String xmbm = ybgkXzxm.getXmbm();
        String xztype = ybgkXzxm.getXzcode();
        String ls_ybxz = ybgkXzxm.getYbxz();
        if (!of_check_strisnum(ybgkXzxm.getXzvalue())) {
          continue;
        }
        BigDecimal xzvalue = BigDecimal.valueOf(Double.parseDouble(ybgkXzxm.getXzvalue()));
        BigDecimal allUseNum =  BigDecimal.ZERO;
        BigDecimal sl = fyxx.getSl() == null ? BigDecimal.ZERO : fyxx.getSl();
        if ("hischeckRule".equals(caller)) {
          Fyxx fee = new Fyxx();
          fee.setJzh(fyxx.getJzh());
          fee.setXmbm(xmbm);
          allUseNum = BigDecimal.valueOf(fyxxService.selectXmNumByJzh(fee));
          BigDecimal zl = fyxx.getZl() == null ? BigDecimal.ZERO : fyxx.getZl();
          allUseNum = allUseNum.add(zl);
        }
        else {
          //出院审核
          int li_num=0;
          String ls_fymx="";
          if (fyxx.getXmbm()!=null&&fyxxList!=null){
            for (Fyxx fyxx2 : fyxxList) {
              if (fyxx2.getXmbm()!=null&&fyxx2.getXmbm().equals(fyxx.getXmbm())) {
                li_num++;
                allUseNum = allUseNum.add(fyxx2.getSl());
                if(li_num<10) {
                  ls_fymx = ls_fymx+fyxx2.getXmmc()+" "+fyxx2.getSl()+" "+fyxx2.getFydate().getDay();
                }
//        	    	 System.out.println(fyxxList.size()+"--"+fyxx.toString());
//        	    	  allUseNum += fyxx2.getSl().doubleValue();
              }
            }
            fyxx.setFymx(ls_fymx);
//            allUseNum = fyxxList.stream()
//              .filter(fyxx1 -> fyxx.getXmbm().equals(fyxx1.getXmbm()))
//              .map(fyxx1 -> fyxx1.getSl() != null ? fyxx1.getSl() : BigDecimal.ZERO)
//              .reduce(BigDecimal.ZERO, BigDecimal::add);
          }

        }


        if (allUseNum!=null&&allUseNum.compareTo(xzvalue) > 0) {
          String is_xmtype = (String) zyzdyl.get("xmtype");
          SimpleDateFormat sdfDate1 = new SimpleDateFormat("yyyy-MM-dd");

          BigDecimal ldec_ce = allUseNum.subtract(xzvalue);
          fyxx.setSl(ldec_ce);
//          fyxx.setSl(ldec_ce);
          sl = fyxx.getSl() == null ? BigDecimal.ZERO : fyxx.getSl();

          //重新设置金额
          if(fyxx.getPrice()!=null&&fyxx.getSl()!=null) {
            fyxx.setJe(fyxx.getPrice().multiply(fyxx.getSl()));
          }

          if (ls_ybxz!=null&&(ls_ybxz.contains("同一切口")||ls_ybxz.contains("收费标准的50%")||ls_ybxz.contains("收费标准的20%")||ls_ybxz.contains("收费标准的70%"))) {
        	  setWgjlItem("项目[" + fyxx.getXmmc() + "]开的数量[" + allUseNum
        	            + "]请注意数量是否超量[" + xzvalue + "]", xztype, fyxx, ids_log, brxx, is_xmtype, tiptype);
          }else {
        	  setWgjlItem("项目[" + fyxx.getXmmc() + "]开的数量[" + allUseNum
        	            + "]已经超出一次住院设定的最大数量[" + xzvalue + "]", xztype, fyxx, ids_log, brxx, is_xmtype, tiptype);
          }


        }
      }


//      String ls_ybxmlsh, ls_xzvalue, is_jktype;
//      for (int i = 0; i < ybgkXzxmList.size(); i++) {
//        ls_ybxmlsh = ybgkXzxmList.get(i).getXmbm();// 医保项目流水号
//        ls_xzvalue = ybgkXzxmList.get(i).getXzvalue();// 限制的数量
//        is_jktype = ybgkXzxmList.get(i).getXzcode();// 限制类别
//        if (!of_check_strisnum(ls_xzvalue)) {
//          return;
//        }
//
//        Double ldec_xzzsl = Double.parseDouble(ls_xzvalue);
//        YbgkWgjl ybgkWgjl = new YbgkWgjl();
//        ybgkWgjl.setJzh(fyxx.getJzh());
//        ybgkWgjl.setBzmc(ls_ybxmlsh);
//        List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectZyslxz(ybgkWgjl);
//        int ldec_zsl = ybgkWgjlList.size() > 0 ? ybgkWgjlList.get(0).getLdecZsl() : 0;
//        String is_xmtype = (String) zyzdyl.get("xmtype");
//        if ("每日".equals(is_cysh_flag) || "出院".equals(is_cysh_flag)) {
//          if (ldec_zsl > ldec_xzzsl) {
//            setWgjlItem("项目[" + fyxx.getXmmc() + "]开的数量[" + ldec_zsl + "]已经超出一次住院设定的最大数量["
//              + ldec_xzzsl + "],日期为:" + sdfDate.format(fyxx.getFydate()), is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
//          }
//        } else {
//          int idec_bcsl = 0;
//          if (fyxx.getSl() != null) {
//            idec_bcsl = fyxx.getSl().intValue();
//          }
//          if (ldec_zsl + idec_bcsl > ldec_xzzsl) {
//            ldec_zsl = ldec_zsl + idec_bcsl;
//            setWgjlItem("项目[" + fyxx.getXmmc() + "]开的数量[" + ldec_zsl
//              + "]已经超出一次住院设定的最大数量[" + ldec_xzzsl + "],本次数量[" + idec_bcsl+"]日期为:" + sdfDate.format(fyxx.getFydate()), is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
//
//          }
//        }
//      }
    }
  }

  /**
   * 审核使用天数
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_zysyzdts(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log,String caller,List<Fyxx> fyxxList) {
    Map<String, Object> syzdts = of_get_tiptype("syzdts", fyxx, ids_log, brxx);
    String tiptype = (String) syzdts.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }


    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) syzdts.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {
      String ls_syzdts = ybgkXzxmList.get(0).getXzvalue();
      String is_jktype = ybgkXzxmList.get(0).getXzcode();

      if (!of_check_strisnum(ls_syzdts)) {
        return;
      }
      long ll_num=0;
      Date ldt_kzdate = null;
      if("hischeckRule".equals(caller)) {
        YbgkWgjl ybgkWgjl = new YbgkWgjl();
        ybgkWgjl.setFymId(fyxx.getXmbm());
        ybgkWgjl.setJzh(fyxx.getJzh());
        List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectZyzdsyts(ybgkWgjl);
        if (ybgkWgjlList.size() == 0) {
          ll_num = 0;
        } else {
          ll_num = ybgkWgjlList.get(0).getLlNum();
          ldt_kzdate = ybgkWgjlList.get(0).getKzdate();
        }
      }
      else {
        //add by lt cysh修改为从集合取数
        if (fyxx.getXmbm()!=null&&fyxxList!=null){
          ll_num = fyxxList.stream()
            .filter(fyxx1 -> fyxx.getXmbm().equals(fyxx1.getXmbm()))
            .count();
          Optional<Date> minFydate = fyxxList.stream()
            .filter(fyxx1 -> fyxx1.getXmbm().equals(fyxx.getXmbm())) // 替换为实际 xmbm
            .map(Fyxx::getFydate)
            .filter(Objects::nonNull) // 过滤掉 fydate 为 null 的项
            .min(Comparator.naturalOrder());
          ldt_kzdate = minFydate.orElse(new Date(0L));

        }
      }




//			System.out.println(ll_num+"---"+ll_num);
//      if ("每日".equals(is_cysh_flag)) {
      if (ll_num + 1 >= Integer.parseInt(ls_syzdts)) {
        String is_xmtype = (String) syzdts.get("xmtype");
        if (ll_num > Integer.parseInt(ls_syzdts)) {

          // 费用时间小的不写违规
          if (fyxx.getFydate()!=null&&ldt_kzdate!=null) {
            LocalDate date2 = fyxx.getFydate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate date1 = ldt_kzdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            // 计算天数差
            long daysBetween = ChronoUnit.DAYS.between(date1, date2);
            if (daysBetween+1<Integer.parseInt(ls_syzdts)) {
              return;
            }
          }

          setWgjlItem("项目[" + fyxx.getXmmc() + "]使用天数[" + ll_num + "]超设定的天数[" + ls_syzdts + "]" , is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
        } else {
//          setWgjlItem("项目[" + fyxx.getXmmc() + "]使用天数[" + ll_num + "]即将超设定的天数[" + ls_syzdts + "]日期为:" + sdfDate.format(fyxx.getFydate()), is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
        }
      }
    }
  }


  //检查并返回匹配的疾病名称，如果没有匹配则返回 null
  private String getMatchedDisease(List<String> DISEASE_KEYWORDS,String text) {
    if (text == null) return null;
    if (DISEASE_KEYWORDS==null) {
      return null;
    }
    return DISEASE_KEYWORDS.stream()
      .filter(text::contains)
      .findFirst()
      .orElse(null);
  }


  /**
   * 提取字符串中所有符合"M"后跟5位数字的代码（如：M01600, M03900）
   * @param input 待处理的字符串
   * @return 包含所有匹配代码的列表，如果没有匹配项则返回空列表
   */
  public  List<String> extractMCode(String input) {
    List<String> result = new ArrayList<>();
    if (input == null || input.isEmpty()) {
      return result;
    }

    // 定义正则表达式：M后跟5位数字
    String regex = "M\\d{5}";
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(input);

    // 查找并收集所有匹配结果
    while (matcher.find()) {
      result.add(matcher.group());
    }

    return result;
  }
  /**
   * 特病用药范围
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_tbyyl(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log, String caller, List<Fyxx> fyxxList, int li_first, String ls_jkdata) {
    // 参数校验 - 统一前置检查
    if (fyxx == null || fyxx.getXmbm() == null || fyxx.getXmmc() == null||fyxx.getZdxx()==null) {
      return;
    }

    // 中药不检查 & 项目编码长度校验
    String xmbm = fyxx.getXmbm();
    if (xmbm.length() >= 1 && "T".equals(xmbm.substring(0, 1))) {
      return;
    }
    if (xmbm.length() <= 2) {
      return;
    }
    
    //耗材暂时不检查
    if (xmbm.length() >= 1 && "C".equals(xmbm.substring(0, 1))) {
    	return;
    }

    // 豁免关键词检查
    for (String keyword : exemptKeywords) {
      if (fyxx.getXmmc().contains(keyword)) {
        return;
      }
    }

    // 诊断信息检查
    String ls_zdxx = fyxx.getZdxx().toUpperCase();
    if (ls_zdxx == null || ls_zdxx.length() < 5) {
      return;
    }


    // 提取特病病种
    List<String> tbbzarr = extractMCode(ls_zdxx);
    String ls_tbbz = "";
    int li_tbflag = 0;
    
    String ls_bzmc="";

    // 处理特病病种（方式一：从诊断中提取）
    if (tbbzarr != null && !tbbzarr.isEmpty()) {
      for (String value : tbbzarr) {
        if (value == null || value.trim().isEmpty()) {
          continue;
        }

        boolean foundInDiagnosis = false;
        for (YbgkTbyyl item : ybgkTbbzAll) {
          if (value.equals(item.getTbbm()) && ls_zdxx.contains(item.getBzbm().toUpperCase())) {
            ls_tbbz = ls_tbbz.isEmpty() ? item.getBzbm() : ls_tbbz + "," + item.getBzbm();
            ls_bzmc = item.getTbbz();
            li_tbflag = 1;
            foundInDiagnosis = true;
            break;
          }
        }

        // 未在诊断中找到明细，但有特病编码
        if (!foundInDiagnosis) {
          for (YbgkTbyyl item : ybgkTbbzAll) {
            if (value.equals(item.getTbbm())) {
              ls_tbbz = ls_tbbz.isEmpty() ? item.getBzbm() : ls_tbbz + "," + item.getBzbm();
              ls_bzmc = item.getTbbz();
              li_tbflag = 1;
              break;
            }
          }
        }
      }
    }
    // 处理特病病种（方式二：通过患者标识提取）
    else if (!"0".equals(is_tb_usesfzjzh)) {

      String ls_bsh = null;
      if ("1".equals(is_tb_usesfzjzh) || "3".equals(is_tb_usesfzjzh)) {
        ls_bsh = brxx.getSfz();
      } else if ("2".equals(is_tb_usesfzjzh) && brxx.getJzh() != null) {
        ls_bsh = brxx.getJzh();
        String[] parts = brxx.getJzh().split("_");
        if (parts.length > 0) {
          ls_bsh = parts[0];
        }
      }

      if (ls_bsh == null || ls_bsh.isEmpty()) {
        return;
      }

      String[] ls_zfzarr = {ls_bsh};
      Optional<YbgkTbbrxx> result = ybgkTbbrxxAll.stream()
        .filter(item -> item.getSfz().equals(ls_zfzarr[0]))
        .findFirst();

      if (!result.isPresent()) {
        return;
      }

      // 检查诊断匹配
      for (YbgkTbyyl item : ybgkTbbzAll) {
        if (ls_zdxx.contains(item.getBzbm().toUpperCase())) {
          ls_tbbz = ls_tbbz.isEmpty() ? item.getBzbm() : ls_tbbz + "," + item.getBzbm();
          li_tbflag = 1;
        }
      }
    }

    // 特病用药范围检查
    if (li_tbflag == 1 && StrUtil.isNotEmpty(ls_tbbz) && brxx != null) {
      // 保存并处理项目编码
      String originalXmbm = fyxx.getXmbm();
      String processedXmbm = Optional.ofNullable(originalXmbm)
        .filter(s -> !s.isEmpty())
        .orElse("0");

      // 处理非数字开头的药品编码
      if (!processedXmbm.isEmpty() && !Character.isDigit(processedXmbm.charAt(0))) {
        processedXmbm = processedXmbm.length() >= 6 ? processedXmbm.substring(0, 6) : processedXmbm;
      }
      fyxx.setXmbm(processedXmbm);

      // 检查用药范围
      String tbbz = Optional.ofNullable(ls_tbbz).orElse("");
      String[] tbbzArray = tbbz.split(",");
      boolean inRange = false;

      for (String bz : tbbzArray) {
        List<YbgkTbyyl> filteredList = ybgkTbyylAll.stream()
          .filter(p -> p != null
            && Objects.equals(p.getBzbm(), bz)
            && Objects.equals(p.getXmbm(), fyxx.getXmbm()))
          .collect(Collectors.toList());

        if (!filteredList.isEmpty()) {
          inRange = true;
          break;
        }
      }

      if (inRange) {
        return;
      }

      // 检查是否为自费项目
      YbgkDz ybgkdz = new YbgkDz();
      ybgkdz.setXmbm(originalXmbm);
      List<YbgkDz> resultList = iYbgkDzService.selectYbgkDzList(ybgkdz);

      if (!resultList.isEmpty()) {
        YbgkDz firstRecord = resultList.get(0);
        if (firstRecord.getFydj() != null && "3".equals(firstRecord.getFydj())) {
          return;
        }
      }
     String ls_tbtip;
      if(is_yymc!=null&&(is_yymc.contains("大学城医院")||is_yymc.contains("陈家桥医院"))) {
    	  ls_tbtip = "";
      }else {
    	  ls_tbtip = "如是特病医嘱，";
      }
      // 构建警告信息
      String msg;
      if (fyxx.getXmbm() != null && ("X".equals(fyxx.getXmbm().substring(0, 1))||"Z".equals(fyxx.getXmbm().substring(0, 1)))) {
    	  
    	  if (checkBzmcInSyz(originalXmbm,ls_bzmc)) {
    		  return ;
    	  }
        msg = String.format("项目[%s%s]超范围使用,适应症满足可用、项目是自费或已转自费可以使用，诊断为%s;%s",
          fyxx.getXmmc(), originalXmbm, ls_zdxx,ls_tbbz+ls_bzmc);
      } else {
        msg = String.format("项目[%s%s]超范围使用,项目是自费或已转自费可以使用，诊断为%s;%s",
          fyxx.getXmmc(), originalXmbm, ls_zdxx,ls_tbbz+ls_bzmc);
      }
      
      msg = ls_tbtip +msg;
     

      // 记录违规日志
      brxx.setBlxbs(ls_jkdata);
      fyxx.setXmbm(originalXmbm);
      setWgjlItem(msg, "tbyyfw", fyxx, ids_log, brxx, "", "2", "");
    }
  }
  /**
   * 审核过度检查
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_gdjc(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log,List<Fyxx> fyxxList,String ls_jkdata) {
    Map<String, Object> xzbz = of_get_tiptype("gdjc", fyxx, ids_log, brxx);
    String tiptype = (String) xzbz.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }
    String ls_xzvalue = null;
    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xzbz.get("xzxm_list");

    if (ybgkXzxmList.size() > 0) {

//      Zdxx zdxx = new Zdxx(fyxx.getJzh());
//      List<Zdxx> zdxxList = zdxxService.selectZdxxList(zdxx);

      boolean flag = false;

//      for (YbgkXzxm xzxm : ybgkXzxmList) {
//    	ls_xzvalue = xzxm.getXzvalue();
//        List<String> list = Arrays.asList(xzxm.getXzvalue().split("、"));
////        flag = zdxxList.stream().anyMatch(item ->
////          list.stream().anyMatch(string ->
////          (item.getZdname() != null && item.getZdname().contains(string)) ||
////          (item.getZdcode() != null && item.getZdcode().contains(string))
////          )
////        );
////        if (fyxx.getZdxx()!=null) {
////        	 flag = list.stream().allMatch(fyxx.getZdxx()::contains);
////        }
//
//        if (flag) {
//          break;
//        }
//      }
      if (fyxx.getZdxx()==null||"".equals(fyxx.getZdxx())) {
        return;
      }



      for (YbgkXzxm xzxm : ybgkXzxmList) {
        ls_xzvalue = xzxm.getXzvalue();
        List<String> list = Arrays.asList(xzxm.getXzvalue().split("、"));
        // 遍历列表中的每个字符串
        for (String str : list) {
          flag = fyxx.getZdxx().contains(str);
          if (flag) {
            break;
          }
        }



        if (flag) {
          break;
        }
      }

      //检查手术
      if (ls_xzvalue!=null&&ls_xzvalue.contains("、手术")) {
        for (Fyxx fyxx2 : fyxxList) {
          String fykmname = fyxx2.getFykmname();
          if (fykmname == null || fykmname.isEmpty()) {
          } else if (fykmname.contains("手术")||(!"".equals(ls_jkdata)&&ls_jkdata!=null&&(ls_jkdata.contains("-31")||ls_jkdata.contains("-32")|ls_jkdata.contains("-33")||ls_jkdata.contains("-34")))) {
            flag=true;
            break;
          }
        }
      }

      if (ls_xzvalue!=null&&ls_xzvalue.contains("大于60岁")) {
        //只取含小数点的数字
        String brage = String.valueOf(convertToDecimalAge(brxx.getAge()));
        if (NumberUtils.isParsable(brage) == true) {
          if(Double.parseDouble(brage) >= 60 ) {
            flag=true;
          }
        }
      }


      if (!flag) {
        String ls_bzall = "";
        for (int i = 0; i < ybgkXzxmList.size(); i++) {
          ls_xzvalue = ybgkXzxmList.get(i).getXzvalue();
          if (i == 0) {
            ls_bzall = ls_xzvalue;
          } else {
            ls_bzall = ls_bzall + '、' + ls_xzvalue;
          }
        }

        //只有一条，并且含有++2的不算
        if (ls_bzall!=null&&fyxx.getXmbm()!=null&&ls_bzall.contains("++2")){
          long count = fyxxList.stream()
            .filter(fyxx1 -> fyxx.getXmbm().equals(fyxx1.getXmbm()))
            .count();
          if (count<2) {
            return;
          }
        }





        String ls_xzbzname = ls_bzall;
        String is_xmtype = (String) xzbz.get("xmtype");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]过度检查或治疗，在诊断中未找到[" + ls_xzbzname + "]病种，日期为:" + sdfDate.format(fyxx.getFydate()), "gdjc", fyxx, ids_log, brxx, is_xmtype, "0");
      }
    }
  }



  /**
   * 审核限制病种
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xzbz(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log,String shlb, String caller,List<Zdxx> zdxxList,List<Fyxx> fyxxList,String ls_jkdata) {
//    System.out.println("----------费用单项信息");
//    System.out.println(fyxx.toString());
//    System.out.println("----------");
    Map<String, Object> xzbz = of_get_tiptype("xzbz", fyxx, ids_log, brxx);
    String tiptype = (String) xzbz.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    if (shlb==null) {
      shlb="";
    }

//		ls_zdqk = "";

//		if (!fyxx.getXmmc().equals("【甲】强力枇杷露(无糖型）")) {
//			return;
//		}

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xzbz.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {

      // 第一条费用才执行
//			if (index == 1) {
      Zdxx brsyzd = new Zdxx();
      brsyzd.setJzh(fyxx.getJzh());
      String ls_zdqk = "";
      String is_brzdxx = "";

      if (is_brzdxx == null) {
        is_brzdxx = "";
      }
      //还是原来的zdxx,就不重新提取
      if (is_brzdxx.indexOf(fyxx.getJzh()) > -1) {
        ls_zdqk = is_brzdxx;
      } else {
        if (fyxx.getJzh() != null && !"".equals(fyxx.getJzh())) {
          ls_zdqk = fyxx.getJzh() + ",";
        }
        List<Zdxx> ids_zlhis_zdxx;

        if(!"".equals(caller)&&"2".equals(brxx.getBrtype())) {
          ids_zlhis_zdxx = zdxxService.selectZdxxList(brsyzd);
        }else
        {
          ids_zlhis_zdxx = zdxxList;
        }

        if (ids_zlhis_zdxx!=null&&ids_zlhis_zdxx.size() > 0) {
          for (int i = 0; i < ids_zlhis_zdxx.size(); i++) {
            ls_zdqk = ls_zdqk + ',' + ids_zlhis_zdxx.get(i).getZdname();
          }
        }
        is_brzdxx = ls_zdqk;
      }

      if("mzsh".equals(shlb)&&fyxx.getBz()!=null) {
        ls_zdqk = fyxx.getBz();
      }


      String ls_xzvalue;
      String ls_ybxz="";
      String ls_bzall = "";
      for (int i = 0; i < ybgkXzxmList.size(); i++) {
        ls_xzvalue = ybgkXzxmList.get(i).getXzvalue();
        ls_ybxz = ybgkXzxmList.get(i).getYbxz();
        if (i == 0) {
          ls_bzall = ls_xzvalue;
        } else {
          ls_bzall = ls_bzall + '、' + ls_xzvalue;
        }
      }



      //门诊如果没有传诊断，就不提示适应症
      if(ls_ybxz!=null&&ls_ybxz.contains("适应症")&&"mzsh".equals(shlb)&&(fyxx.getBz()==null||"".equals(fyxx.getBz()))) {
        return;
      }


      String ls_xzbzname = ls_bzall;
//			System.out.println(fyxx.getJzh()+"---------"+ls_zdqk+"---"+ls_xzbzname);
      String[] ls_bzarr = ls_bzall.split("、");
      int li_haveflag = 0;
      for (int i = 0; i < ls_bzarr.length; i++) {
//            	System.out.println(ls_zdqk+"--"+ls_bzarr[i]);
        if (ls_zdqk.indexOf(ls_bzarr[i]) > -1) {
          li_haveflag = 1;
          break;
        }
      }

      //检查手术
      if (ls_bzall!=null&&ls_bzall.contains("、手术")) {
        for (Fyxx fyxx2 : fyxxList) {
          String fykmname = fyxx2.getFykmname();
          if (fykmname == null || fykmname.isEmpty()) {
          } else if (fykmname.contains("手术")||("hischeckRule".equals(shlb)&&ls_jkdata!=null&&(ls_jkdata.contains("-31")||ls_jkdata.contains("-32")|ls_jkdata.contains("-33")||ls_jkdata.contains("-34")))) {
            li_haveflag = 1;
            break;
          }
        }
      }


      //没有找到，从病历里面去找,住院才执行
      if (li_haveflag == 0&&!"mzsh".equals(shlb) && "1".equals(is_xzbz_frombl)) {
        for (int i = 0; i < ls_bzarr.length; i++) {
          YbgkWgjl ybgkWgjl = new YbgkWgjl();
          ybgkWgjl.setJzh(fyxx.getJzh());

          ybgkWgjl.setBlnr('%' + String.valueOf(ls_bzarr[i]) + '%');

          List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectBlsl(ybgkWgjl);
          li_haveflag = ybgkWgjlList.get(0).getLiHaveflag();
          if (li_haveflag > 0) {
            //  					new_ls_lcbxall = ls_lcbxarr[i];
            break;
          }
        }
      }



      //没有找到，从费里面去找,住院才执行
      if (li_haveflag == 0&&!"mzsh".equals(shlb)&&fyxx.getXmmc()!=null ) {
        //检查费用
        if (fyxx.getXmmc().contains("布比卡因")|| fyxx.getXmmc().contains("aaaaaaaa")) {
          YbgkWgjl ybgkWgjl = new YbgkWgjl();
          ybgkWgjl.setJzh(fyxx.getJzh());
          ybgkWgjl.setBzmc("%麻醉%");
          ybgkWgjl.setFymId(fyxx.getXmbm());
          if (is_fy_fromzl == "1") {
            List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectFyxxSl1(ybgkWgjl);
            li_haveflag = ybgkWgjlList.get(0).getLiHaveflag();
          } else {
            List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectKcSl(ybgkWgjl);
            li_haveflag = ybgkWgjlList.get(0).getLiHaveflag();
          }
          if (li_haveflag > 0) {
            return;
          }
        }
      }



//            System.out.println(ls_zdqk+"--"+li_haveflag);
      if (li_haveflag == 0) {
        String is_xmtype = (String) xzbz.get("xmtype");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]限[" + ls_xzbzname + "]病种,但在诊断中未找到相应的病种，如诊断已加，可能是诊断未同步，可忽略,日期为:" + sdfDate.format(fyxx.getFydate()), "xzbz", fyxx, ids_log, brxx, is_xmtype, tiptype, ybgkXzxmList.get(0).getShowcolor());
      }

    }

  }

  /**
   * 审核限制病种
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_jjz(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log,String shlb, String caller,List<Zdxx> zdxxList) {
//    System.out.println("----------费用单项信息");
//    System.out.println(fyxx.toString());
//    System.out.println("----------");
    Map<String, Object> jjz = of_get_tiptype("jjz", fyxx, ids_log, brxx);
    String tiptype = (String) jjz.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    if (shlb==null) {
      shlb="";
    }



    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) jjz.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {

      // 第一条费用才执行
//			if (index == 1) {
      Zdxx brsyzd = new Zdxx();
      brsyzd.setJzh(fyxx.getJzh());
      String ls_zdqk = "";
      String is_brzdxx = "";

      if (is_brzdxx == null) {
        is_brzdxx = "";
      }
      //还是原来的zdxx,就不重新提取
      if (is_brzdxx.indexOf(fyxx.getJzh()) > -1) {
        ls_zdqk = is_brzdxx;
      } else {
        if (fyxx.getJzh() != null && !"".equals(fyxx.getJzh())) {
          ls_zdqk = fyxx.getJzh() + ",";
        }

        List<Zdxx> ids_zlhis_zdxx;

        if(!"".equals(caller)&&"2".equals(brxx.getBrtype())) {
          ids_zlhis_zdxx = zdxxService.selectZdxxList(brsyzd);
        }else
        {
          ids_zlhis_zdxx = zdxxList;
        }

        if (ids_zlhis_zdxx!=null&&ids_zlhis_zdxx.size() > 0) {
          for (int i = 0; i < ids_zlhis_zdxx.size(); i++) {
            ls_zdqk = ls_zdqk + ',' + ids_zlhis_zdxx.get(i).getZdname();
          }
        }
        is_brzdxx = ls_zdqk;
      }




      String ls_xzvalue;
      String ls_bzall = "";
      for (int i = 0; i < ybgkXzxmList.size(); i++) {
        ls_xzvalue = ybgkXzxmList.get(i).getXzvalue();
        if (i == 0) {
          ls_bzall = ls_xzvalue;
        } else {
          ls_bzall = ls_bzall + '、' + ls_xzvalue;
        }
      }

      if("mzsh".equals(shlb)&&fyxx.getBz()!=null) {
        ls_zdqk = fyxx.getBz();
      }


      String ls_xzbzname = ls_bzall;
//			System.out.println(fyxx.getJzh()+"---------"+ls_zdqk+"---"+ls_xzbzname);
      String[] ls_bzarr = ls_bzall.split("、");
      int li_haveflag = 0;
      for (int i = 0; i < ls_bzarr.length; i++) {
//            	System.out.println(ls_zdqk+"--"+ls_bzarr[i]);
        if (ls_zdqk.indexOf(ls_bzarr[i]) > -1) {
          li_haveflag = 1;
          break;
        }
      }

      if (li_haveflag > 0) {
        String is_xmtype = (String) jjz.get("xmtype");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]不允许[" + ls_xzbzname + "]病种使用,在诊断中找到相应的病种，日期为:" + sdfDate.format(fyxx.getFydate()), "jjz", fyxx, ids_log, brxx, is_xmtype, "0");
      }

    }

  }

  /**
   * 审核临床表现
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_lcbx(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> xzlcbx = of_get_tiptype("xzlcbx", fyxx, ids_log, brxx);
    String tiptype = (String) xzlcbx.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    String isXzlcbx = ybjkOptionService.getCacheOptionInfo("xzlcbx", "1");
    if ("0".equals(isXzlcbx)) {
      return;
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xzlcbx.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {
      String ls_xzvalue = "";
      String is_jktype = ybgkXzxmList.get(0).getXzcode();// 限制类别
      String ls_lcbxall = "";
      for (int i = 0; i < ybgkXzxmList.size(); i++) {
        ls_xzvalue = ybgkXzxmList.get(i).getXzvalue();
        if (i == 0) {
          ls_lcbxall = ls_xzvalue;
        } else {
          ls_lcbxall = ls_lcbxall + '、' + ls_xzvalue;
        }
      }
      int li_haveflag = 0;
//			String new_ls_lcbxall = "";
      String ls_lcbxarr[] = ls_lcbxall.split("、");
      for (int i = 0; i < ls_lcbxarr.length; i++) {
        YbgkWgjl ybgkWgjl = new YbgkWgjl();
        ybgkWgjl.setJzh(fyxx.getJzh());
        ybgkWgjl.setBlnr('%' + String.valueOf(ls_lcbxarr[i]) + '%');
        List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectBlsl(ybgkWgjl);
        li_haveflag = ybgkWgjlList.get(0).getLiHaveflag();
        if (li_haveflag > 0) {
//					new_ls_lcbxall = ls_lcbxarr[i];
          break;
        }
      }







      if (li_haveflag == 0) {

        //检查检验
        if (ls_lcbxall!=null)  {
          if (ls_lcbxall.contains("AST大于120") ) {
            Jyxx jyxx = new Jyxx();
            jyxx.setJzh(brxx.getJzh());
            if (jyxxService.selectJyxxast(jyxx).size()>0) {
              return;
            }
          }

          //检查费用
          if (ls_lcbxall.contains("复合麻醉")|| ls_lcbxall.contains("全身麻醉")) {
            YbgkWgjl ybgkWgjl = new YbgkWgjl();
            ybgkWgjl.setJzh(fyxx.getJzh());
            ybgkWgjl.setBzmc("%全身麻醉%");
            ybgkWgjl.setFymId(fyxx.getXmbm());
            if (is_fy_fromzl == "1") {
              List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectFyxxSl1(ybgkWgjl);
              li_haveflag = ybgkWgjlList.get(0).getLiHaveflag();
            } else {
              List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectKcSl(ybgkWgjl);
              li_haveflag = ybgkWgjlList.get(0).getLiHaveflag();
            }
            if (li_haveflag > 0) {
              return;
            }
          }

          //检查费用
          if (ls_lcbxall.contains("限重症医学科")) {
            if (fyxx.getKsname()!=null){
              if (fyxx.getKsname().contains("重症")) {
                return;
              }
            }
          }

          //在诊断里面找,如果包含，则返回
          if (fyxx.getZdxx()!=null) {
            String ls_zdxxarr[] = ls_lcbxall.split("、");
            for (int i = 0; i < ls_lcbxarr.length; i++) {
              if (fyxx.getZdxx().contains(ls_lcbxarr[i])) {
                return;
              }
            }
          }


        }

        Map<String, String> ls_return = of_get_spxx(brxx, fyxx, ls_xzvalue);
        String spxx = ls_return.get("spxx");
        if (spxx.equals("ok")) {

        } else {

          String is_cl_flag = ls_return.get("cl_flag");
          String is_xmtype = (String) xzlcbx.get("xmtype");
          setWgjlItem(spxx + "项目[" + fyxx.getXmmc() + "]应在病历中书写[" + ls_lcbxall + "]等临床表现,日期为:" + sdfDate.format(fyxx.getFydate()), is_jktype, fyxx, ids_log, brxx, is_xmtype, is_cl_flag, ybgkXzxmList.get(0).getShowcolor());
        }
      }
    }
  }

  /**
   * 审核科室
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xzks(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> xzks = of_get_tiptype("xzks", fyxx, ids_log, brxx);
    String tiptype = (String) xzks.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }


    String ls_xzbzname = "";


    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xzks.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {
      String is_jktype = ybgkXzxmList.get(0).getXzcode();// 限制类别
      for (int i = 0; i < ybgkXzxmList.size(); i++) {

//				if ("null".equals(ybgkXzxmList.get(i).getXzvalue())||ybgkXzxmList.get(i).getXzvalue()==null||"".equals(ybgkXzxmList.get(i).getXzvalue())) {
//					continue;
//				}

        if (ls_xzbzname != "") {
          ls_xzbzname = ls_xzbzname + ',' + ybgkXzxmList.get(i).getXzvalue();
        } else {

          ls_xzbzname = ybgkXzxmList.get(i).getXzvalue();

        }
      }
      if (ls_xzbzname.indexOf(fyxx.getKsname()) == -1) {
        String is_xmtype = (String) xzks.get("xmtype");
        ls_xzbzname = ls_xzbzname.replaceAll("null,", "");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]限[" + ls_xzbzname + "]使用,病人科室不符合", is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
      }
    }
  }

  /**
   * 审核使用后加收
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xxmjssy(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log,String caller,List<Fyxx> fyxxList,String ls_jkdata) {
    Map<String, Object> xxmsyhjs = of_get_tiptype("xxmsyhjs", fyxx, ids_log, brxx);
    String tiptype = (String) xxmsyhjs.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }
    
    //为了避免提示太多
    if("hischeckRule".equals(caller)&&fyxx.getXmmc()!=null) {
    	  // 豁免关键词检查
        for (String keyword : xmjsKeywords) {
          if (fyxx.getXmmc().contains(keyword)) {
            return;
          }
        }
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xxmsyhjs.get("xzxm_list");
    long li_haveflag = 0;
    if (ybgkXzxmList.size() > 0) {
      String is_jktype = ybgkXzxmList.get(0).getXzcode();// 限制类别
      String ls_xzzlxm = "";

      String ls_xzvalue,ls_lcbxall;
      ls_xzvalue = "";
      ls_lcbxall="";
      for (int i = 0; i < ybgkXzxmList.size(); i++) {
        ls_xzvalue = ybgkXzxmList.get(i).getXzvalue();
        if (i == 0) {
          ls_lcbxall = ls_xzvalue;
        } else {
          ls_lcbxall = ls_lcbxall + '、' + ls_xzvalue;
        }
      }

      String ls_lcbxarr[] = ls_lcbxall.split("、");

      for (int i = 0; i < ls_lcbxarr.length; i++) {
        String ls_fym;
        ls_xzzlxm = ls_lcbxarr[i];
        if (ls_xzzlxm==null||"".equals(ls_xzzlxm)) {
        	continue;
        }
        if(ls_xzzlxm.contains("*")) {
        	ls_fym = ls_xzzlxm.replace("*", ".*");
        }else {
        	ls_fym = '%' + ls_xzzlxm + '%';
        }
        
        YbgkWgjl ybgkWgjl = new YbgkWgjl();
        ybgkWgjl.setJzh(fyxx.getJzh());
        ybgkWgjl.setBzmc(ls_fym);
        ybgkWgjl.setFymId(fyxx.getXmbm());
//          if (is_fy_fromzl == "1") {
        // add by lt 2025-08-08添加通配符
        if("hischeckRule".equals(caller)) {
        	List<YbgkWgjl> ybgkWgjlList=null;
        	  if(ls_fym.contains("*")) {
        		  ybgkWgjlList = ybgkWgjlService.selectFyxxSl2(ybgkWgjl);
        	  }else {
        		  ybgkWgjlList = ybgkWgjlService.selectFyxxSl1(ybgkWgjl);
        	  }
        	  
          li_haveflag = ybgkWgjlList.get(0).getLiHaveflag();
          if (li_haveflag==0) {
        	  String ls_jkdata1 = ls_jkdata.replace(fyxx.getXmmc(), "");
              if (ls_jkdata1.contains(ls_xzzlxm)) {
            	  li_haveflag = 1;
              }
          }
        }else {
          //出院审核
          String[] ls_xzzlxmWrapper = new String[]{ls_xzzlxm,fyxx.getXmbm()};
          if (ls_xzzlxmWrapper[0]!=null) {
        	  if(ls_fym.contains("*")) {
        		  ls_xzzlxmWrapper[0] = ls_fym;
        		  li_haveflag = fyxxList.stream()
        				  .filter(fyxx1 -> fyxx1.getXmbm() != null && !fyxx1.getXmbm().equals(ls_xzzlxmWrapper[1]))
        	              .filter(fyxx1 -> fyxx1.getXmmc() != null && 
        	                            Pattern.matches(ls_xzzlxmWrapper[0], fyxx1.getXmmc()))
        	              .count();  
        	  }else {
        		  li_haveflag = fyxxList.stream()
        				  .filter(fyxx1 -> fyxx1.getXmbm() != null && !fyxx1.getXmbm().equals(ls_xzzlxmWrapper[1]))
        	              .filter(fyxx1 -> fyxx1.getXmmc().contains(ls_xzzlxmWrapper[0]))
        	              .count();
        	  }
          }
        }


        if (li_haveflag > 0) {
          break;
        }
      }

      if (li_haveflag <= 0) {
        String is_xmtype = (String) xxmsyhjs.get("xmtype");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]限[" + ls_lcbxall + "]使用后收取,在之前的项目中没有找到此项目,如该项目已收取，可能是费用未同步，可忽略，日期为:" + sdfDate.format(fyxx.getFydate()),
          is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");

      }
    }
  }

  /**
   * 审核限制医院等级项目
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xzyydj(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> xyydj = of_get_tiptype("xyydj", fyxx, ids_log, brxx);
    String tiptype = (String) xyydj.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }



    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xyydj.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {
      String ls_yydj=is_yydj;
      // add by lt 铜无局是2级
      if("f636ce3fc3be52db".equals(brxx.getJgid())) {
        ls_yydj = "2";
      }
      String is_jktype = ybgkXzxmList.get(0).getXzcode();// 限制类别
      String ls_xzyydj = ybgkXzxmList.get(0).getXzvalue();
      if (ls_yydj == null) {
        return;
      }

      if (!of_check_strisnum(ls_yydj) || !of_check_strisnum(ls_xzyydj)) {
        return;
      }

      if (Double.parseDouble(ls_yydj) < Double.parseDouble(ls_xzyydj)) {
        String is_xmtype = (String) xyydj.get("xmtype");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]限[" + ls_xzyydj + "]级医院使用,医院等级不符合", is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
      }

    }
  }

  public void mem_check_blgyxm(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log, List<Fyxx> fyxxList) {
    Map<String, Object> blgyxm = of_get_tiptype("blgyxm", fyxx, ids_log, brxx);
    String tiptype = (String) blgyxm.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) blgyxm.get("xzxm_list");
    if (ybgkXzxmList == null || ybgkXzxmList.isEmpty()) {
      return ;
    }

    String ls_wjxzflag = null;
    String ls_ybxz = null;


    String ls_blgyall = "";
    for (int j = 0; j < ybgkXzxmList.size(); j++) {
      String ls_xzvalue = ybgkXzxmList.get(j).getXzvalue();
      ls_wjxzflag = ybgkXzxmList.get(j).getWjxzflag();
      ls_ybxz = ybgkXzxmList.get(j).getYbxz();

      if (j == 0) {
        ls_blgyall = ls_xzvalue;
      } else {
        ls_blgyall = ls_blgyall + '、' + ls_xzvalue;
      }
    }

    fyxx.setYbbz(ls_ybxz);
    fyxx.setWjxzflag(ls_wjxzflag);


    String[] ls_blgyarr = ls_blgyall.split("、");

    String ls_oldxmmc=fyxx.getXmmc();
    String ls_oldxmbm=fyxx.getXmbm();
    BigDecimal lb_oldsl = fyxx.getSl();
    BigDecimal lb_oldje = fyxx.getJe();
    BigDecimal lb_oldprice = fyxx.getPrice();
    Date ldt_oldfydate = fyxx.getFydate();


    String ls_xmmc=null;
    ls_xmmc = fyxx.getXmmc();

    for (String ls_xzvalue : ls_blgyarr) {
      if ("".equals(ls_xzvalue)) {
        continue;
      }

      if(ls_xzvalue!=null&&ls_xzvalue.equals(fyxx.getXmmc())) {
        break;
      }

      fyxx.setXmmc(ls_oldxmmc);
      fyxx.setXmbm(ls_oldxmbm);
      fyxx.setSl(lb_oldsl);
      fyxx.setJe(lb_oldje);
      fyxx.setPrice(lb_oldprice);

      if (ls_xzvalue!=null&&ls_xzvalue.contains("剔除[")) {
        List<Zdxx> zdxxList = zdxxService.selectZdxxList(new Zdxx(brxx.getJzh()));
        zdxxList.removeIf(zdxx -> zdxx == null || StringUtils.isBlank(zdxx.getZdcode()) || StringUtils.isBlank(zdxx.getZdname()));
        if (of_check_blgyxm_tcnr(ls_xzvalue, zdxxList)) {
          continue;
        }
      }

//      YbgkWgjl ybgkWgjl = new YbgkWgjl();
//      ybgkWgjl.setJzh(fyxx.getJzh());
//      ybgkWgjl.setBzmc(ls_xzvalue);
//      ybgkWgjl.setFymId(fyxx.getXmbm());
//      ybgkWgjl.setCreateDate(sdfDateTime.format(fyxx.getFydate()));
      SimpleDateFormat sdfDate1 = new SimpleDateFormat("yyyy-MM-dd");
      List<Fyxx> fyxxForDay =
        fyxxList.stream()
          .filter(fy ->
            sdfDate1.format(fy.getFydate()).equals(sdfDate1.format(fyxx.getFydate()))&&!Objects.equals(fy.getXmbm(), fyxx.getXmbm()))
          .collect(Collectors.toList());



      List<Fyxx> filteredList = fyxxForDay.stream()
        .filter(fy -> {
          if (ls_xzvalue.contains("=")) {
            String xzvalue = ls_xzvalue.replaceAll("=", "");
            return xzvalue.equals(fy.getXmmc());
          } else {
            if (ls_xzvalue.contains("剔除[")) {
              return fy.getXmmc().contains(ls_xzvalue.substring(0, ls_xzvalue.indexOf("剔除[")));
            } else {
              return fy.getXmmc().contains(ls_xzvalue);
            }
          }
        }).collect(Collectors.toList());



      for (Fyxx fyxx2 : filteredList) {


        BigDecimal ldec_sl1 = null;
        String ls_dw=null;
        String ls_xmmc1=null;
        String ls_yzid=null;
        String ls_deptname="";
        Date ldt_fydate1=null;
        String ls_fymx1=null;
        String ls_xmbm1 = null;
        BigDecimal ls_je = null;
        BigDecimal ls_price1 = null;
        BigDecimal ldec_slce = null;
        String ls_houre_tip="";

        ldec_sl1 = fyxx2.getSl();
        ls_dw = fyxx2.getDw();
        ls_xmmc1 = fyxx2.getXmmc();
        ldt_fydate1 = fyxx2.getFydate();
        ls_yzid = fyxx2.getYsid();
        ls_deptname = fyxx2.getKsname();
        ls_je = fyxx2.getJe();
        ls_xmbm1 = fyxx2.getXmbm();
        ls_price1 = fyxx2.getPrice();
        ls_fymx1 = fyxx2.getFymx();
        fyxx.setFymx1(fyxx2.getFymx());



        if (ls_xmmc1==null) {
          continue;
        }

        ls_houre_tip="";

        if(fyxx.getXmmc()!=null&&fyxx.getXmmc().equals(ls_xmmc1)) {
          continue;
        }

        SimpleDateFormat sdfDateTime1 = new SimpleDateFormat("MM-dd HH:mm");
        String is_xmtype = (String) blgyxm.get("xmtype");






        //当前项目和限制值单位均含有小时，只比较数量

        Integer li_flag = 0;
        //同一分钟
//        if (fyxx.getFydate()!=null&&ldt_fydate1!=null) {
//          LocalDateTime dateTime1 = fyxx.getFydate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
//          LocalDateTime dateTime2 = ldt_fydate1.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
//          if(dateTime1.getYear() == dateTime2.getYear() &&
//            dateTime1.getMonth() == dateTime2.getMonth() &&
//            dateTime1.getDayOfMonth() == dateTime2.getDayOfMonth() &&
//            dateTime1.getHour() == dateTime2.getHour() &&
//            dateTime1.getMinute() == dateTime2.getMinute()) {
//            li_flag = 1;
//          }
//        }




        //相同的医嘱
//        if((fyxx.getYzid()!=null&&fyxx.getYzid().equals(ls_yzid))&&ls_xmmc1.contains("血氧饱和度监测")) {
//          li_flag = 2;
//        }
//


        if(is_yymc!=null&&is_yymc.contains("大学城医院")&&fyxx.getXmmc()!=null&&fyxx.getXmmc().contains("心电监测")&&ls_xmmc1.contains("血氧饱和度监测")) {
//          li_flag = 2;
          continue;
        }

        if(is_yymc!=null&&is_yymc.contains("万家燕")&&fyxx.getXmmc()!=null&&fyxx.getXmmc().contains("心电监测")&&ls_xmmc1.contains("血氧饱和度监测")&&fyxx.getYzid()!=null&&!fyxx.getYzid().equals(ls_yzid)) {
//          li_flag = 2;
          continue;
        }

        if(is_yymc!=null&&is_yymc.contains("陈家桥医院")&&fyxx.getXmmc()!=null&&fyxx.getXmmc().contains("心电监测")&&ls_xmmc1.contains("血氧饱和度监测")) {
          fyxx.setDw("");
          ls_dw = "";
          li_flag = 2;
        }

//        if("心电监测".equals(fyxx.getXmmc())&&ls_xmmc1!=null&&ls_xmmc1.contains("血氧饱和度监测")&&li_flag==0){
//          continue;
//        }

        if(ldt_fydate1!=null&&fyxx.getFydate()!=null) {
          long differenceInMillis = fyxx.getFydate().getTime() - ldt_fydate1.getTime();
          long differenceInMinutes = Math.abs(differenceInMillis / (60 * 1000));
          if (fyxx.getXmmc()!=null&&fyxx.getXmmc().contains("输液'")||fyxx.getXmmc().contains("报告'")) {
            if (differenceInMinutes>10) {
              continue;
            }
          }

//          if ("局部浸润麻醉".equals(ls_xmmc1)&&"血气分析".equals(ls_oldxmmc)) {
//        	 System.out.println(ls_xmmc1+fyxx.getXmmc()+fyxx.getXmbm());
//          }
//


          if ("局部浸润麻醉".equals(ls_xmmc1)&&(fyxx.getXmbm().contains("-32")||fyxx.getXmbm().contains("-31"))) {
            for (Fyxx fyxx3 : fyxxForDay) {
              String xmbm = fyxx3.getXmbm();
              String xmmc = fyxx3.getXmmc();
              if (xmbm != null && xmbm.length() >= 2 && xmbm.contains("-33")&&xmmc!=null&&!xmmc.contains("麻醉")) {
                return ;
              }
              if (xmmc!=null&&xmmc.contains("人工晶体植入术")) {
                return ;
              }
            }

            //如果当天有局部浸润麻醉不能共用的，就不再写
            if(checkLocalInfiltrationAnesthesia(ids_log)==true) {
              return;

            }

            //大于180分钟，则不检查 局部浸润麻醉
            if (differenceInMinutes>180) {
              YbgkWgjl ybgkWgjl = new YbgkWgjl();
              ybgkWgjl.setJzh(fyxx.getJzh());
              ybgkWgjl.setBzmc(ls_xmmc1);
              ybgkWgjl.setFymId(ls_oldxmbm);
//     					System.out.println("idt_fydate="+idt_fydate);
              ybgkWgjl.setCreateDate(sdfDateTime1.format(ldt_oldfydate));
              //住院才检查保存的费用信息
              if("2".equals(brxx.getBrtype())){
                List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectBlgyxm(ybgkWgjl);
                if (ybgkWgjlList != null && ybgkWgjlList.size() > 0) {
                  int ldec_zsl = ybgkWgjlList.get(0).getLdecZsl();
                  if (ldec_zsl > 0) {
                    differenceInMinutes = 0;
                  }
                }
              }

              if (differenceInMinutes>180){
                return;
              }
            }

            setWgjlItem("项目[" + fyxx.getXmmc() + "]不能与[" + ls_xmmc1 + "]同时开,日期分别为:" + sdfDateTime1.format(ldt_fydate1)+"--"+sdfDateTime1.format(fyxx.getFydate())+" 科室分别为："+ls_deptname+"--"+fyxx.getKsname(), "blgyxm", fyxx, ids_log, brxx, is_xmtype, tiptype, ybgkXzxmList.get(0).getShowcolor());
            return ;
          }

          //相同时间
          if (ls_blgyall!=null&&(ls_blgyall.contains("相同时间"))&&fyxx.getFydate()!=null&&ldt_fydate1!=null) {
            if (differenceInMinutes>2) {
              continue;
            }
          }

          if (li_flag==0) {


            //如果两个都是日，如果总量没有超1，不算违规
            if (fyxx.getDw()!=null&&"日".equals(fyxx.getDw())&&ls_dw!=null&&"日".equals(ls_dw)&&ldec_sl1!=null&&fyxx.getSl()!=null) {
              BigDecimal ldec_sum1=null;
              if (ldec_sl1!=null&&fyxx.getSl()!=null) {
                ldec_sum1 = ldec_sl1.add(fyxx.getSl());
                if(ldec_sum1.compareTo(new BigDecimal("1")) <= 0) {
                  continue;
                }
              }
            }

            //含有小时，
            if (((fyxx.getDw()!=null&&fyxx.getDw().contains("小时"))||(ls_dw!=null&&ls_dw.contains("小时")))&&ldec_sl1!=null&&fyxx.getSl()!=null) {
              //如果两个都是小时
              BigDecimal ldec_sum=null;

              //只要有一个为小时，总量就不能超24
              if (((fyxx.getDw()!=null&&fyxx.getDw().contains("小时"))||(ls_dw!=null&&ls_dw.contains("小时")))&&ldec_sl1!=null) {
                ldec_sum = ldec_sl1.add(fyxx.getSl());
              }

              if (ldec_sum!=null && ldec_sum.compareTo(new BigDecimal("24"))>0) {
                li_flag=1;
              }

              Long ll_xcfz=0L;

              //合计没有超24小时
              if (li_flag!=1) {
//            	  ll_xcfz = 1L;
                if  ("1".equals(is_cyshshjyx)){
                  ll_xcfz = 1L;
                }  else
                {
                  ll_xcfz = 60L;
                }

                if (differenceInMinutes>60) {
                  continue;
                }

                //一个是小时，一个次，则检查一分钟
                if(ldec_sl1.equals(BigDecimal.ONE)&&fyxx.getSl().compareTo(BigDecimal.ONE)>0&&ls_dw!=null&&"小时".equals(ls_dw)) {

                  if (differenceInMinutes>ll_xcfz) {
                    continue;
                  }
                }

                //一个是小时，一个次，则检查一分钟
                if(ldec_sl1.compareTo(BigDecimal.ONE)>0&&fyxx.getDw()!=null&&"小时".equals(fyxx.getDw())&&fyxx.getSl().compareTo(BigDecimal.ONE)==0) {
                  if (differenceInMinutes>ll_xcfz) {
                    continue;
                  }
                }

                if(is_yymc!=null&&(is_yymc.contains("大学城医院")||is_yymc.contains("陈家桥医院"))&&"小时".equals(ls_dw)) {
                  if(ldec_sl1.doubleValue()<24) {
                    continue;
                  }
                }


              }else {
                //两个的合计超过24
                if(fyxx.getDw()==null) {
                  fyxx.setDw("");
                }

                if(ls_dw==null) {
                  ls_dw = "";
                }
                if(fyxx.getDw().contains("小时")&&ls_dw.contains("小时")) {
                  ldec_slce = (ldec_sum.subtract(new BigDecimal("24")));
                  ls_houre_tip=",两项目合计超24";
                }
                if(fyxx.getSl()!=null&&fyxx.getDw().contains("小时")&&!ls_dw.contains("小时")) {
                  if(fyxx.getSl().compareTo(new BigDecimal("23.5"))>0) {
                    ls_houre_tip=","+fyxx.getXmmc()+"数量为:"+fyxx.getSl().toString();
                  }else {
                    return;
                  }
                }

                if(ldec_sl1!=null&&!fyxx.getDw().contains("小时")&&ls_dw.contains("小时")) {
                  if(ldec_sl1.compareTo(new BigDecimal("23.5"))>0) {
                    ls_houre_tip=","+ls_xmmc1+"数量为:"+ldec_sl1.toString();
                  }else {
                    return;
                  }
                }

              }


            }
          }//if (li_flag==0) {
        }


        // 取两条费用之间金额小的作为违规金额
        if (ls_je != null && fyxx.getJe() != null&&!"小时".equals(ls_dw)&&!"小时".equals(fyxx.getDw())&&ls_xmbm1!=null&&ls_xmmc1!=null) {
          if (ls_je.compareTo(fyxx.getJe()) < 0) {
            fyxx.setXmbm(ls_xmbm1);
            fyxx.setXmmc(ls_xmmc1);
            fyxx.setJe(ls_je);
            fyxx.setSl(ldec_sl1);
          }
        }

        //如果有小时，且总量超24取单价小那个来算金额
        if (ldec_slce != null && fyxx.getPrice() != null&&ls_price1!=null&&ls_xmbm1!=null&&ls_xmmc1!=null&&("小时".equals(ls_dw)||"小时".equals(fyxx.getDw()))) {
          if (fyxx.getPrice().compareTo(ls_price1) < 0) {
            fyxx.setSl(ldec_slce);
            fyxx.setJe(fyxx.getPrice().multiply(ldec_slce));
          }else {
            fyxx.setXmbm(ls_xmbm1);
            fyxx.setXmmc(ls_xmmc1);
            fyxx.setSl(ldec_slce);
            fyxx.setPrice(ls_price1);
            fyxx.setJe(ls_price1.multiply(ldec_slce));
          }
        }
        if (ldt_fydate1==null) {
          ldt_fydate1 = fyxx.getFydate();
        }

        if (ls_xmmc==null||ls_xmmc1==null) {
          continue;
        }


        if(fyxx.getFydate()!=null&&ldt_fydate1!=null&&fyxx.getFydate().before(ldt_fydate1)) {
          setWgjlItem("项目[" + ls_oldxmmc + "]不能与[" + ls_xmmc1 + "]同时开"+ls_houre_tip+",日期分别为:" + sdfDateTime1.format(fyxx.getFydate())+"--"+sdfDateTime1.format(ldt_fydate1)+" 科室分别为："+fyxx.getKsname()+"--"+ls_deptname, "blgyxm", fyxx, ids_log, brxx, is_xmtype, tiptype, ybgkXzxmList.get(0).getShowcolor());
        } else {
          setWgjlItem("项目[" + ls_xmmc1 + "]不能与[" + ls_oldxmmc + "]同时开"+ls_houre_tip+",日期分别为:" + sdfDateTime1.format(ldt_fydate1)+"--"+sdfDateTime1.format(fyxx.getFydate())+" 科室分别为："+ls_deptname+"--"+fyxx.getKsname(), "blgyxm", fyxx, ids_log, brxx, is_xmtype, tiptype, ybgkXzxmList.get(0).getShowcolor());
        }

        //恢复原来的名称
        fyxx.setXmmc(ls_oldxmmc);
        fyxx.setXmbm(ls_oldxmbm);
        fyxx.setSl(lb_oldsl);
        fyxx.setJe(lb_oldje);
        fyxx.setPrice(lb_oldprice);

      }
    }

//    //恢复原来的名称
//    fyxx.setXmmc(ls_oldxmmc);
//    fyxx.setXmbm(ls_oldxmbm);
//    fyxx.setSl(lb_oldsl);
//    fyxx.setJe(lb_oldje);
//    fyxx.setPrice(lb_oldprice);

  }

  public  boolean checkLocalInfiltrationAnesthesia(List<YbgkWgjl> idsLog) {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.CHINA);
    for (YbgkWgjl record : idsLog) {
      if (record == null || record.getCreateDate() == null || record.getJklog() == null) {
        continue;
      }

      try {
        LocalDate createDate = LocalDate.parse(record.getCreateDate().substring(0, 10), formatter);
        if (createDate.isEqual(currentDate)) {
          if (record.getJklog().contains("局部浸润麻醉")) {
            return true;
          }
        }
      } catch (Exception e) {
        System.err.println("日期解析错误: " + record.getCreateDate());
        e.printStackTrace();
      }
    }
    return false;
  }

  /**
   * 检查不能共用项目的剔除内容
   * @param ls_xzvalue
   * @param zdxxList
   * @return
   */
  public boolean of_check_blgyxm_tcnr(String ls_xzvalue,List<Zdxx> zdxxList) {
    if (ls_xzvalue.contains("剔除[")) {
      int startIndex = ls_xzvalue.indexOf("剔除[");
      int endIndex = ls_xzvalue.indexOf(']', startIndex);

      if (startIndex != -1 && endIndex != -1) {
        String tcnr = ls_xzvalue.substring(startIndex + 3, endIndex).trim();  //剔除内容
        List<String> list = Arrays.asList(tcnr.split(","));

        boolean match = zdxxList.stream().anyMatch(zdxx ->
          list.stream().anyMatch(item -> zdxx.getZdname().contains(item) || zdxx.getZdcode().contains(item))
        );

        if (match) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * 审核不能同时共享的项目
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_blgyxm(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log,String as_jkdata) {
    Map<String, Object> blgyxm = of_get_tiptype("blgyxm", fyxx, ids_log, brxx);
    String tiptype = (String) blgyxm.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) blgyxm.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {
      String ls_ybxmlsh, ls_xzvalue, is_jktype;
      for (int i = 0; i < ybgkXzxmList.size(); i++) {
        ls_ybxmlsh = ybgkXzxmList.get(i).getXmbm();
        is_jktype = ybgkXzxmList.get(i).getXzcode();// 限制类别
        ls_xzvalue = ybgkXzxmList.get(i).getXzvalue();
//        String ls_day = "0";

        int ldec_zsl = 0;
        String ls_blgyall = "";
        for (int j = 0; j < ybgkXzxmList.size(); j++) {
          ls_xzvalue = ybgkXzxmList.get(j).getXzvalue();
          if (j == 0) {
            ls_blgyall = ls_xzvalue;
          } else {
            ls_blgyall = ls_blgyall + '、' + ls_xzvalue;
          }
        }
        String ls_blgyarr[] = ls_blgyall.split("、");


        for (int ll_i = 0; ll_i < ls_blgyarr.length; ll_i++) {
          ls_xzvalue = ls_blgyarr[ll_i];

          if ( "".equals(ls_xzvalue)) {
            continue;
          }

          if(ls_xzvalue!=null&&ls_xzvalue.equals(fyxx.getXmmc())) {
            break;
          }

          if (ls_xzvalue!=null&&ls_xzvalue.contains("剔除[")) {
            List<Zdxx> zdxxList = zdxxService.selectZdxxList(new Zdxx(brxx.getJzh()));
            zdxxList.removeIf(zdxx -> zdxx == null || StringUtils.isBlank(zdxx.getZdcode()) || StringUtils.isBlank(zdxx.getZdname()));
            if (of_check_blgyxm_tcnr(ls_xzvalue, zdxxList)) {
              continue;
            }
          }

//					if (ids_icd10 == null) {
//						ids_icd10 = baBrzdxxService.selectIcd10List();
//					}
//
//					for (int j = 0; j < ids_icd10.size(); j++) {
//						if (ids_icd10.get(j).getJbbm() == is_bzcode) {
//							ll_finded = j;
//						}
//					}
//
//					if (ll_finded > 0) {
//						ldec_zsl = 100;
//						break;
//					}
          SimpleDateFormat sdfDateTime1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

          YbgkWgjl ybgkWgjl = new YbgkWgjl();
          ybgkWgjl.setJzh(fyxx.getJzh());
          ybgkWgjl.setBzmc(ls_blgyarr[ll_i]);
          ybgkWgjl.setFymId(ls_ybxmlsh);
//					System.out.println("idt_fydate="+idt_fydate);
          ybgkWgjl.setCreateDate(sdfDateTime1.format(fyxx.getFydate()));
          //住院才检查保存的费用信息
          if("2".equals(brxx.getBrtype())){
            List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectBlgyxm(ybgkWgjl);
            if (ybgkWgjlList != null && ybgkWgjlList.size() > 0) {
              ldec_zsl = ybgkWgjlList.get(0).getLdecZsl();
              if (ldec_zsl > 0) {
                break;
              }
            }
          }
          
          
          if("1".equals(brxx.getBrtype())&&fyxxList_mz != null&&!fyxxList_mz.isEmpty()&&fyxx.getJzh()!=null){
              for (Fyxx fyxx2 : fyxxList_mz) {
                  boolean jzhMatch = fyxx.getJzh().equals(fyxx2.getJzh()); 
                  // 检查xmmc是否包含"abc"（注意处理null值）
                  boolean xmmcMatch = fyxx2.getXmmc() != null && fyxx2.getXmmc().contains(ls_blgyarr[ll_i]);
                  // 两个条件都满足则返回true
                  if (jzhMatch && xmmcMatch) {
                	  ldec_zsl = 1;
                  }
              }
            }
          
          

          if((ldec_zsl==0)&&as_jkdata!=null){
            if(ls_xzvalue.contains("=")) {
              ls_xzvalue = ls_xzvalue.replace("=", "");
              if(as_jkdata.contains(ls_xzvalue+"|")) {
                ldec_zsl = 1;
              }
            }else {
              if(as_jkdata.contains(ls_xzvalue)) {
                ldec_zsl = 1;
              }
            }
          }
        }

        if("相同时间".equals(ls_xzvalue)) {
          return;
        }


//        ldec_zsl = ldec_zsl + ldec_zsl1;
        if (ldec_zsl > 0) {
          Map<String, String> ls_return = of_get_spxx(brxx, fyxx, ls_xzvalue);
          String spxx = ls_return.get("spxx");
          if (spxx.equals("ok")) {

          } else {
        	  
        	  if(ls_xzvalue!=null&&ls_xzvalue.equals(fyxx.getXmmc())) {
                  continue;
               }  
        	  
            SimpleDateFormat sdfDate1 = new SimpleDateFormat("yyyy-MM-dd");
            String is_cl_flag = ls_return.get("cl_flag");
            String is_xmtype = (String) blgyxm.get("xmtype");
            setWgjlItem("项目[" + fyxx.getXmmc() + "]不能与[" + ls_xzvalue + "]同时开,日期为:" + sdfDate.format(fyxx.getFydate()), is_jktype, fyxx, ids_log, brxx, is_xmtype, tiptype);
          }
        }

      }
    }
  }

  /**
   * 审核男性项目
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xzlx(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> xlx = of_get_tiptype("xlx", fyxx, ids_log, brxx);
    String tiptype = (String) xlx.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xlx.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {
      if (  ("2".equals(brxx.getSex()) || "女".equals(brxx.getSex()))) {
        String is_xmtype = (String) xlx.get("xmtype");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]限男姓使用，请修改，日期为:" + sdfDate.format(fyxx.getFydate()), "xls", fyxx, ids_log, brxx, is_xmtype, tiptype, ybgkXzxmList.get(0).getShowcolor());
      }
    }
  }

  /**
   * 审核女性项目
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xznx(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> xnx = of_get_tiptype("xnx", fyxx, ids_log, brxx);
    String tiptype = (String) xnx.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xnx.get("xzxm_list");

    if (ybgkXzxmList.size() > 0) {
      if ( ("1".equals(brxx.getSex()) || "男".equals(brxx.getSex()))) {
        String is_xmtype = (String) xnx.get("xmtype");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]限女姓使用，请修改，日期为:" + sdfDate.format(fyxx.getFydate()), "xnx", fyxx, ids_log, brxx, is_xmtype, tiptype, ybgkXzxmList.get(0).getShowcolor());
      }
    }
  }

  /**
   * 审核儿童项目
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xzet(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> yzetsy = of_get_tiptype("yzetsy", fyxx, ids_log, brxx);
    String tiptype = (String) yzetsy.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }

    if (brxx.getAge() == null || "".equals(brxx.getAge())) {
      return;
    }
    int age_idx = brxx.getAge().indexOf("岁");
    if (age_idx != -1) {
      brxx.setAge(brxx.getAge().substring(0, age_idx));
//      brxx.getAge().replace("岁", "");
    }

    //只取含小数点的数字
    String brage = brxx.getAge().replaceAll("[^0-9.]", "");

    if (NumberUtils.isParsable(brage) == false) {
      return;
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) yzetsy.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {

      String is_jktype = ybgkXzxmList.get(0).getXzcode();// 限制类别

      if (brxx == null) {
        return;
      }

      if ("2".equals(brxx.getBrtype()) && Double.parseDouble(brage) > 15) {
        String is_xmtype = (String) yzetsy.get("xmtype");
        setWgjlItem("项目[" + fyxx.getXmmc() + "]限儿童使用，请修改，日期为:" + sdfDate.format(fyxx.getFydate()), is_jktype, fyxx, ids_log, brxx, is_xmtype, "0", ybgkXzxmList.get(0).getShowcolor());
      }

    }
  }


  /**
   * 显示医保备注
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_ybbz(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> yzetsy = of_get_tiptype("ybbz", fyxx, ids_log, brxx);
    String tiptype = (String) yzetsy.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }
    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) yzetsy.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {
      String is_jktype = ybgkXzxmList.get(0).getXzcode();// 限制类别
      String ls_xzvalue = ybgkXzxmList.get(0).getXzvalue();// 限制类别
      if (ls_xzvalue!=null) {

        String is_xmtype = (String) yzetsy.get("xmtype");
        if(fyxx.getXmbm()!=null&&fyxx.getXmbm().startsWith("0046")) {
          setWgjlItem("项目[" + fyxx.getXmmc() + "]"+"限有中医质职的人员开展，不能由外科人员开展", is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
          return;
        }

        //出院审核审核医生中医资质
        if("1".equals(is_cysh_yszyzz)&&ls_xzvalue.contains("限具有康复或中医医疗资质的医务人员")) {
          String ls_doctorname = StrUtil.isEmpty(fyxx.getYsname()) ? fyxx.getYsid() : fyxx.getYsname();
          if (ls_doctorname==null||"".equals(ls_doctorname)) {
            return ;
          }
          if (sysUserListAll==null) {
            return ;
          }
          if (sysUserListAll.size()==0) {
            return ;
          }

          List<SysUser> filteredPersons = sysUserListAll.stream()
            .filter(p -> p.getNickName().equals(ls_doctorname)&& p.getRemark() != null
              && p.getRemark().contains("中医"))
            .collect(Collectors.toList());
          if (filteredPersons.size()>0)	 {
            return;
          }
        }


        setWgjlItem("项目[" + fyxx.getXmmc() + "]"+ls_xzvalue, is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");
      }

    }
  }
  /**
   * 限制职称
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xzzc(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> xzyszc = of_get_tiptype("xzyszc", fyxx, ids_log, brxx);
    String tiptype = (String) xzyszc.get("tiptype");
    if ("0".equals(tiptype)) {
      return;
    }
    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xzyszc.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {

      String is_jktype = ybgkXzxmList.get(0).getXzcode();// 限制
      String ls_xzvalue = ybgkXzxmList.get(0).getXzvalue();
      String ls_xzzcname = null;
      if (ls_xzvalue==null) {
        return ;
      }

      String ls_doctorname = StrUtil.isEmpty(fyxx.getYsname()) ? fyxx.getYsid() : fyxx.getYsname();
      if (ls_doctorname==null) {
        return ;
      }
      if (sysUserListAll==null) {
        return ;
      }
      if (sysUserListAll.size()==0) {
        return ;
      }

      List<SysUser> filteredPersons = sysUserListAll.stream()
        .filter(p -> p.getNickName().equals(ls_doctorname))
        .collect(Collectors.toList());
      int li_flag = 0;
      String ls_yszc;
      if (filteredPersons.isEmpty()) {
        return ;
      }
      // 遍历所有找到的人员的资称
      for (SysUser person : filteredPersons) {
        if(person.getZc()!=null) {
          ls_yszc = person.getZc();
          if (ls_yszc.compareTo(ls_xzvalue) >= 0) {
            li_flag = 1;
          }
        }
      }
      if(li_flag==0) {
        switch (ls_xzvalue) {
          case "1":
            ls_xzzcname = "医师";
            break;
          case "2":
            ls_xzzcname = "主治医师";
            break;
          case "3":
            ls_xzzcname = "副主任医师";
            break;
          case "4":
            ls_xzzcname = "主任医师";
            break;
          default:
            break;
        }

        if (ls_xzzcname!=null) {
          String is_xmtype = (String) xzyszc.get("xmtype");
          setWgjlItem("项目[" + fyxx.getXmmc() + "]限"+ls_xzzcname+"使用，请修改，日期为:" + sdfDate.format(fyxx.getFydate()), is_jktype, fyxx, ids_log, brxx, is_xmtype, "0");

        }
      }
    }
  }

  /**
   * 审核年龄
   *
   * @param brxx
   * @param fyxx
   * @param ids_log
   */
  @Async
  public void of_check_xzage(Brxx brxx, Fyxx fyxx, List<YbgkWgjl> ids_log) {
    Map<String, Object> xzage = of_get_tiptype("xzage", fyxx, ids_log, brxx);
    String tiptype = (String) xzage.get("tiptype");
    String ls_xzvalue=null;
    if ("0".equals(tiptype)) {
      return;
    }

    if (brxx.getAge() == null || "".equals(brxx.getAge())) {
      return;
    }





    if ("岁".indexOf(brxx.getAge()) != -1) {
      brxx.getAge().replace("岁", "");
    }


    //只取含小数点的数字
    String brage = String.valueOf(convertToDecimalAge(brxx.getAge()));

    if (NumberUtils.isParsable(brage) == false) {
      return;
    }

    List<YbgkXzxm> ybgkXzxmList = (List<YbgkXzxm>) xzage.get("xzxm_list");
    if (ybgkXzxmList.size() > 0) {

      String is_jktype = ybgkXzxmList.get(0).getXzcode();// 限制类别
      ls_xzvalue = ybgkXzxmList.get(0).getXzvalue();
//    String ls_day = "0";

      if (ls_xzvalue==null) {
        return;
      }

      // 解析字符串，提取最小值和最大值
      String[] rangeParts = ls_xzvalue.split("-");
      if (rangeParts.length!=2) {
        return;
      }

      double minAge = Double.parseDouble(rangeParts[0]); // 提取最小值
      double maxAge = Double.parseDouble(rangeParts[1]); // 提取最大值
      if (brxx == null) {
        return;
      }
      if (Double.parseDouble(brage) > maxAge || Double.parseDouble(brage)<minAge) {
        String is_xmtype = (String) xzage.get("xmtype");
        if (fyxx.getXmmc()!=null&&fyxx.getXmmc().contains("新生儿")&&((fyxx.getKsname()!=null&&(fyxx.getKsname().contains("产")||fyxx.getKsname().contains("新生儿")))||(fyxx.getZdxx()!=null&&fyxx.getZdxx().contains("产")))) {
          return;
        }
        setWgjlItem("项目[" + fyxx.getXmmc() + "]限年龄"+ls_xzvalue+"使用请修改，日期为:" + sdfDate.format(fyxx.getFydate()), is_jktype, fyxx, ids_log, brxx, is_xmtype, "0", ybgkXzxmList.get(0).getShowcolor());
      }
    }
  }

  @Async
  public Map<String, Object> of_get_tiptype(String as_xztype, Fyxx fyxx, List<YbgkWgjl> ids_log, Brxx brxx) {
    /*
     * YbjkUseset ybjkUseset = new YbjkUseset(); ybjkUseset.setCode(as_xztype);
     * List<YbjkUseset> ybjkUsesetList =
     * ybjkUsesetService.selectYbjkUsesetList(ybjkUseset); if (ybjkUsesetList.size()
     * > 0) { is_tip_type = ybjkUsesetList.get(0).getUseflag(); ls_mzzy =
     * ybjkUsesetList.get(0).getMzzy(); } else { is_tip_type = ""; }
     * ybgkXzxm.setXzcode(as_xztype); ybgkXzxmList =
     * ybgkXzxmService.selectYbgkXzxmList(ybgkXzxm); if (ybgkXzxmList.size() > 0) {
     * if (Integer.parseInt(ybgkXzxmList.get(0).getUseflag()) > 0) { is_tip_type =
     * ybgkXzxmList.get(0).getUseflag(); } }
     *
     *
     * if (brxx.getBrtype() == "1" && ls_mzzy == "2") { return "0"; } else if (brxx.getBrtype() ==
     * "2" && ls_mzzy == "1") { return "0"; }
     *
     * return is_tip_type;
     */

    Map<String, Object> resultMap = new HashMap<>();

    if (fyxx.getXmbm() == null || "".equals(fyxx.getXmbm())) {    //fyxx xmbh
      resultMap.put("tiptype", "0");
      return resultMap;
    }

    // 有相同的违规记录，则不再检查，如果费用审核每日审核所有明细，有相同的也要加审核
    if (!"1".equals(is_fysh_day_allmx)) {
      try {
        if (ids_log.stream().anyMatch(p -> fyxx.getXmbm().equals(p.getFymId()) && as_xztype.equals(p.getJktype()))) {
//         System.out.println("有相同记录返回");
          resultMap.put("tiptype", "0");
          return resultMap;
        }
      } catch (Exception e) {
        //			System.out.println(e.getMessage());
        resultMap.put("tiptype", "0");
        return resultMap;
      }
    }

    //筛选当前病人费用项目中属于该限制类型的记录
    List<YbgkXzxm> ybgkXzxmList;
    if (brxx.getFlag() == 1) {
      ybgkXzxmList = ybgkXzxmListHistoryAll.stream().filter(ybgkXzxm -> ybgkXzxm.getXmbm().equals(fyxx.getXmbm()) && ybgkXzxm.getXzcode().equals(as_xztype) && ybgkXzxm.getYear() == brxx.getYear()).collect(Collectors.toList());;
    } else {
      ybgkXzxmList = ybgkXzxmListAll.stream().filter(ybgkXzxm -> ybgkXzxm.getXmbm().equals(fyxx.getXmbm()) && ybgkXzxm.getXzcode().equals(as_xztype)).collect(Collectors.toList());;
    }
    if (ybgkXzxmList.size() == 0) {
      resultMap.put("tiptype", "0");
      return resultMap;
    }
    String is_xmtype = "";
    String is_tiptype="2";
    if (ybgkXzxmList.size() > 0) {
      is_xmtype = ybgkXzxmList.get(0).getXmtype();
      is_tiptype = ybgkXzxmList.get(0).getUseflag();
      if (is_tiptype==null||"".equals(is_tiptype)) {
        is_tiptype = "2";
      }

    }

    resultMap.put("tiptype", is_tiptype);
    resultMap.put("xzxm_list", ybgkXzxmList);
    resultMap.put("xmtype", is_xmtype);
    return resultMap;

  }


  @Async
  public Map<String, String> of_get_spxx(Brxx brxx, Fyxx fyxx, String ls_xzvalue) {
    Map<String, String> resultMap = new HashMap<>();
    if (is_tip_type == "3") {
      String is_xzvalue = ls_xzvalue;
//      String ldt_today = of_getservertime();


      Calendar calendar = Calendar.getInstance();
      calendar.add(Calendar.DATE, -3);
      String three_days_ago = sdfDate.format(calendar.getTime());

      String ldt_from = three_days_ago + " 00:00:01";

      double idec_price = 0;
      if (fyxx.getPrice() != null) {
        idec_price = Double.parseDouble(String.valueOf(fyxx.getPrice()));
      }

      if (is_xzvalue == "jczl" && idec_price < 1000) {
        resultMap.put("spxx", "ok");
        return resultMap;
      }
      YbjkShxm ybjkShxm = new YbjkShxm();
      ybjkShxm.setJzh(fyxx.getJzh());
      ybjkShxm.setSqrq(ldt_from);
      ybjkShxm.setDoctorname(fyxx.getYsname());
      ybjkShxm.setFymid(fyxx.getXmbm());
      ybjkShxm.setBrname(brxx.getName());
      List<YbjkShxm> ybjkShxmList = ybjkShxmService.selectShxmCount(ybjkShxm);

      if (ybjkShxmList.size() > 0) {
//        long ll_num;
//        ll_num = is_spxm_id.length;

//        for (int i = 0; i < ybjkShxmList.size(); i++) {
//          is_spxm_id[(int) (ll_num + i)] = ybjkShxmList.get(i).getId();
//        }
        resultMap.put("spxx", "ok");
        return resultMap;
      } else {
        is_allow_next = "2";
        resultMap.put("spxx", "[需要审批]");
        resultMap.put("cl_flag", "3");
        return resultMap;
      }

    }

    if (is_tip_type == "4") {
      is_allow_next = "2";
      resultMap.put("spxx", "[禁止]");
      resultMap.put("cl_flag", "5");
      return resultMap;
    }
    resultMap.put("spxx", "");
    resultMap.put("cl_flag", "0");
    return resultMap;
  }

  public String of_getservertime() {
    Calendar calendar = Calendar.getInstance();
    String ldt_now = String.valueOf(ybgkWgjlService.selectSysDate());
    idt_today = ldt_now;
    if (ldt_now == null) {
      SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      return formatter.format(calendar.getTime());
    } else {
      return ldt_now;
    }
  }

  public String of_get_fromdate(String as_xzcode, Date ldt_today, String ls_day) {
    boolean isnumber = NumberUtils.isParsable(ls_day);
    int ll_day = Integer.parseInt(ls_day);
    if (isnumber) {
      ll_day = Integer.parseInt(ls_day) - 1;
    } else {
      return idt_today;
    }

    if (ldt_today==null) {
      System.out.println(idt_today);
      return idt_today;
    }

    Calendar calendar = Calendar.getInstance();
    calendar.setTime(ldt_today);
    calendar.add(Calendar.DATE, -ll_day);

    SimpleDateFormat sdfDate1 = new SimpleDateFormat("yyyy-MM-dd");
    String ll_day_days_ago = sdfDate1.format(calendar.getTime());
    String ldt_from = ll_day_days_ago + " 00:00:01";

    if (ll_day == 30 || ll_day == 31) {
      SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
      ldt_from = formatter.format(calendar.getTime()) + "-01 00:00:01";
    }

    int year = calendar.get(Calendar.YEAR);

    if (ll_day > 85 && ll_day < 100) {
      int ls_month = calendar.get(Calendar.MONTH) + 1;
      int ll_month = ls_month - 3;
      if (ll_month < 0) {
        ll_month = 1;
      }
      if (ll_month < 10) {
        ls_month = Integer.parseInt("0" + ll_month);
      } else {
        ls_month = ll_month;
      }
      ldt_from = year + "-" + ls_month + "-01 00:00:01";
    }

    if (ll_day > 200) {
      ldt_from = year + "01-01 00:00:00";
    }
	  
	  return ldt_from;
	  
//	// 检查输入参数合法性
//      if (ldt_today == null) {
//          throw new IllegalArgumentException("当前时间(ldt_today)不能为null");
//      }
//      
//      // 检查天数是否为有效数字
//      boolean isnumber = NumberUtils.isParsable(ls_day);
//      if (!isnumber) {
//          throw new IllegalArgumentException("天数(ls_day)格式不正确: " + ls_day);
//      }
//      
//      // 将字符串天数转换为整数
//      int days = Integer.parseInt(ls_day);
//      
//      // 使用Calendar进行日期计算
//      Calendar calendar = Calendar.getInstance();
//      calendar.setTime(ldt_today);
//      // 减去相应的天数（如果是负数则表示加上天数）
//      calendar.add(Calendar.DAY_OF_YEAR, -days);
//      
//      // 格式化计算后的日期并返回
//      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//      return sdf.format(calendar.getTime());

   
  }

  public String getDateId() {
    LocalDateTime now = LocalDateTime.now();
    long year = now.getYear();
    long monthValue = now.getMonthValue();
    long dayOfMonth = now.getDayOfMonth();
    long hour = now.getHour();
    long minute = now.getMinute();
    long second = now.getSecond();

//    Random random = new Random();
//    long i = random.nextInt(1000000);
//    return "" + year + monthValue + dayOfMonth + hour + minute + second +millisInSecond+ i;

    UUID uuid = UUID.randomUUID();
    String uuidStr = uuid.toString();
    return "" + year + monthValue + dayOfMonth + hour + minute + second+uuidStr.replace("-", "");

  }

  public String getCreateDate(Date date) {
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    return dateFormat.format(date);
  }

  @Async
  public void setWgjlItem(String jklog, String jktype, Fyxx fyxx, List<YbgkWgjl> ids_log, Brxx brxx, String is_xmtype, String is_cl_flag) {
    setWgjlItem(jklog, jktype, fyxx, ids_log, brxx, is_xmtype, is_cl_flag, null);
  }

  @Async
  public void setWgjlItem(String jklog, String jktype, Fyxx fyxx, List<YbgkWgjl> ids_log, Brxx brxx, String is_xmtype, String is_cl_flag, String showColor) {

    if ("1".equals(jklog)) {
      return;
    }


    Date date = null;
    if (fyxx.getOpdate() != null) {
      date = fyxx.getOpdate();
    } else if (fyxx.getFydate() != null) {
      date = fyxx.getFydate();
    } else {
      logger.error("费用时间为空，无法记录违规记录, " + brxx.getBrid() + "_" + brxx.getZyid());
      return;
    }

    if (fyxx.getJe()!=null) {
      if (fyxx.getJe().doubleValue()<0) {
        return ;
      }
    }




    YbgkWgjl ybgkWgjl = new YbgkWgjl();
    ybgkWgjl.setId(getDateId());
    ybgkWgjl.setBrname(brxx.getName());
    ybgkWgjl.setBrtype(brxx.getBrtype());
    ybgkWgjl.setKdksname(fyxx.getKsname());
    ybgkWgjl.setDoctorname(StrUtil.isEmpty(fyxx.getYsname()) ? fyxx.getYsid() : fyxx.getYsname());
    ybgkWgjl.setBed(StrUtil.isEmpty(fyxx.getBed()) ? brxx.getBed() : fyxx.getBed());
    ybgkWgjl.setKdks(fyxx.getKsname());
    ybgkWgjl.setDoctor(fyxx.getYsid());
    ybgkWgjl.setFymName(fyxx.getXmmc());
    ybgkWgjl.setSl(fyxx.getSl());


    ybgkWgjl.setFymId(fyxx.getXmbm());
    //add by lt 20240908
    if(fyxx.getJe()==null&&fyxx.getPrice()!=null&&fyxx.getSl()!=null) {
      ybgkWgjl.setJe(fyxx.getPrice().multiply(fyxx.getSl()));
    }else {
      ybgkWgjl.setJe(fyxx.getJe());
    }

    ybgkWgjl.setZyh(fyxx.getZyh());
    if(ybgkWgjl.getZyh()==null||"".equals(ybgkWgjl.getZyh())) {
      ybgkWgjl.setZyh(brxx.getZyh());
    }
    ybgkWgjl.setJzh(fyxx.getJzh());
    ybgkWgjl.setCreateDate(getCreateDate(date));
    ybgkWgjl.setClFlag(is_cl_flag);
    ybgkWgjl.setTshOper(is_tip_type);
    ybgkWgjl.setJklog(jklog);
    ybgkWgjl.setJktype(jktype);
    ybgkWgjl.setDelFlag("0");
    ybgkWgjl.setUpdateBy("localhost");
    ybgkWgjl.setTshOper("每日自动");


    ybgkWgjl.setFyType(is_xmtype);
    if("1".equals(is_wg_writelog)&&"tbyyfw".equals(jktype)) {
      ybgkWgjl.setFymx(brxx.getBlxbs());
    }
    ybgkWgjl.setJkCode("6");
    ybgkWgjl.setXzlb("");
    ybgkWgjl.setCreateBy("每日自动审核");
    ybgkWgjl.setFydate(fyxx.getFydate());

    ybgkWgjl.setBrid(brxx.getBrid());
    ybgkWgjl.setZyid(brxx.getZyid());
    ybgkWgjl.setBrbs(brxx.getBrid()+"_"+brxx.getZyid());
    ybgkWgjl.setShowcolor(showColor);

    ybgkWgjl.setSfz(brxx.getSfz());
    ybgkWgjl.setInsutype(brxx.getCblb());
    ybgkWgjl.setRydate(brxx.getRydate());
    ybgkWgjl.setCydate(brxx.getCydate());
    ybgkWgjl.setYbbx(brxx.getYbbx());
    ybgkWgjl.setTc(brxx.getTc());
    ybgkWgjl.setDe(brxx.getDe());
    ybgkWgjl.setZje(brxx.getZje());


    ybgkWgjl.setYljz(brxx.getYljz());
    ybgkWgjl.setSetl_id(brxx.getSetl_id());
    if("blgyxm".equals(jktype)&&fyxx.getFymx1()!=null) {
      ybgkWgjl.setFymx(fyxx.getFymx()+System.lineSeparator()+fyxx.getFymx1());
    }

    ybgkWgjl.setYbbz(fyxx.getYbbz());
    ybgkWgjl.setWjxzflag(fyxx.getWjxzflag());

    if("rqzdyl".equals(jktype)||"zyzdyl".equals(jktype) ){
      ybgkWgjl.setFymx(fyxx.getFymx());
    }

    ybgkWgjl.setZyzt(brxx.getZyzt());

    if("1".equals(brxx.getBrtype())&&ybgkWgjl.getBzmc()==null&&brxx.getBzname()!=null) {
      ybgkWgjl.setBzmc(brxx.getBzname());
    }
    //add by lt 2025-4-28 药品写住院医师
    if("2".equals(brxx.getBrtype())&&"1".equals(is_wgjl_zyys)&&brxx.getDoctorname()!=null&&!"".equals(brxx.getDoctorname())&&"药品".equals(is_xmtype)) {
      ybgkWgjl.setDoctorname(brxx.getDoctorname());
    }

    if(ybgkWgjl.getBzmc()==null||"".equals(ybgkWgjl.getBzmc())) {
      ybgkWgjl.setBzmc(getFirst255Characters(fyxx.getZdxx()));
    }

    if("1".equals(brxx.getBrtype())&&ybgkWgjl.getXzlb()==null&&brxx.getCblb()!=null) {
      ybgkWgjl.setXzlb(brxx.getCblb());
    }

    ids_log.add(ybgkWgjl);
  }

  public  String getFirst255Characters(String input) {
    if (input == null) {
      return null;
    }
    if (input.length() <= 255) {
      return input;
    }
    return input.substring(0, 255);
  }

  @RequestMapping("/plsh")
  public void plsh() {
    for (Brxx brxx : brxxService.selectBrxxList(new Brxx())) {
      YbgkWgjl ybgkWgjl = new YbgkWgjl();
      ybgkWgjl.setPlshflag("1");
      ybgkWgjl.setJzh(brxx.getJzh());
      cysh(ybgkWgjl);
    }
  }

  public void initYbgkXzxmList() {
    ybgkXzxmListAll = ybgkXzxmService.selectYbgkXzxmList(new YbgkXzxm());
    ybgkXzxmListAll.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getXmbm()) || StringUtils.isBlank(obj.getXzcode()));
    initSysUserList();
    try {
      ybgkXzxmListHistoryAll = ybgkXzxmService.selectXzxmHistoryList(new YbgkXzxm());
      ybgkXzxmListHistoryAll.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getXmbm()) || StringUtils.isBlank(obj.getXzcode()));
    } catch (Exception e) {
      ybgkXzxmListHistoryAll = new ArrayList<>();
    }

    //获取所有的特病的病人
    ybgkTbbrxxAll = ybgkTbbrService.selectYbgkTbbrxxList(new YbgkTbbrxx());

    //获取所有的特病的病种
    ybgkTbbzAll =  ybgkTbyylService.selectYbgkTbyylListbz(new YbgkTbyyl());
    //获取所有的特病用药范围
    ybgkTbyylAll =  ybgkTbyylService.selectYbgkTbyylList(new YbgkTbyyl());
  }

  public void initSysUserList() {
    SysUser user = new SysUser();
    sysUserListAll = userService.getUserList(user);
    sysUserListAll.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getNickName()) );

  }

  public void init() {

    if (is_fysh_shcfzy == null || "".equals(is_fysh_shcfzy)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("fysh_shcfzy");
      if (option != null && option.getcValue() != null) {
        is_fysh_shcfzy = option.getcValue();
      } else {
        is_fysh_shcfzy = "1";
      }
    }
    //取医院名称
    if (is_yymc == null || "".equals(is_yymc)) {
      is_yymc = StringUtils.defaultString(ybjkOptionService.getCompanyName(),"");
    }

    
    if (is_ybdjh_get_jzh == null || "".equals(is_ybdjh_get_jzh)) {
        YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("ybdjh_get_jzh");
        if (option != null && option.getcValue() != null) {
        	is_ybdjh_get_jzh = option.getcValue();
        } else {
        	is_ybdjh_get_jzh = "0";
        }
      }

    if (is_wgjl_zyys == null || "".equals(is_wgjl_zyys)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("wgjl_zyys");
      if (option != null && option.getcValue() != null) {
        is_wgjl_zyys = option.getcValue();
      } else {
        is_wgjl_zyys = "0";
      }
    }

    if (is_wg_writelog == null || "".equals(is_wg_writelog)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("wg_writelog");
      if (option != null && option.getcValue() != null) {
        is_wg_writelog = option.getcValue();
      } else {
        is_wg_writelog = "0";
      }
    }

    if (is_write_lsfyxx == null || "".equals(is_write_lsfyxx)) {
        YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("write_lsfyxx");
        if (option != null && option.getcValue() != null) {
        	is_write_lsfyxx = option.getcValue();
        } else {
        	is_write_lsfyxx = "0";
        }
      }



    if (is_xzbz_frombl == null || "".equals(is_xzbz_frombl)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("xzbz_frombl");
      if (option != null && option.getcValue() != null) {
        is_xzbz_frombl = option.getcValue();
      } else {
        is_xzbz_frombl = "1";
      }
    }

    if (is_tb_usesfzjzh == null || "".equals(is_tb_usesfzjzh)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("tb_usesfzjzh");
      if (option != null && option.getcValue() != null) {
        is_tb_usesfzjzh = option.getcValue();
      } else {
        is_tb_usesfzjzh = "0";
      }
    }



    //出院审核比较费用
    if (is_cysh_bjfy == null || "".equals(is_cysh_bjfy)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("cysh_bjfy");
      if (option != null && option.getcValue() != null) {
        is_cysh_bjfy = option.getcValue();
      } else {
        is_cysh_bjfy = "0";
      }
    }

    //出院审核检查
    if (is_shgdjc == null || "".equals(is_shgdjc)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("shgdjc");
      if (option != null && option.getcValue() != null) {
        is_shgdjc = option.getcValue();
      } else {
        is_shgdjc = "0";
      }
    }

    //门诊启用日期数量限制
    if (is_mz_rqslxz == null || "".equals(is_mz_rqslxz)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("mz_rqslxz");
      if (option != null && option.getcValue() != null) {
        is_mz_rqslxz = option.getcValue();
      } else {
        is_mz_rqslxz = "0";
      }
    }




//    //初始化空的list
//    emptyFyxxList = new ArrayList<>();

    //出院审核显示医保备注
    if (is_cysh_xsybbz == null || "".equals(is_cysh_xsybbz)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("cysh_xsybbz");
      if (option != null && option.getcValue() != null) {
        is_cysh_xsybbz = option.getcValue();
      } else {
        is_cysh_xsybbz = "0";
      }
    }

    //出院审核显示医保备注
    if (is_cyshshjyx == null || "".equals(is_cyshshjyx)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("cyshshjyx");
      if (option != null && option.getcValue() != null) {
        is_cyshshjyx = option.getcValue();
      } else {
        is_cyshshjyx = "0";
      }
    }





    if (is_check_dept == null || "".equals(is_check_dept)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("is_check_dept");
      if (option != null && option.getcValue() != null) {
        is_check_dept = option.getcValue();
      } else {
        is_check_dept = "0";
      }
    }

    if (is_check_zc == null || "".equals(is_check_zc)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("is_check_zc");
      if (option != null && option.getcValue() != null) {
        is_check_zc = option.getcValue();
      } else {
        is_check_zc = "0";
      }
    }

    if("1".equals(is_check_dept)) {
      zyshDeptStr = ybjkOptionService.getOptionInfo("zysh_check_dept","");
      mzshDeptStr = ybjkOptionService.getOptionInfo("mzsh_check_ept","");
    }

    SimpleDateFormat sdfDateHour1 = new SimpleDateFormat("yyyy-MM-dd HH");

    if ("".equals(is_pre_date)) {
      is_pre_date = sdfDateHour1.format(new Date());
    }



    if (!is_pre_date.equals(sdfDateHour1.format(new Date()))) {
      initYbgkXzxmList();
      is_pre_date = sdfDateHour1.format(new Date());
    }

    if (is_cysh_tsqjszd == null || "".equals(is_cysh_tsqjszd)) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("cysh_tsqjszd");
      if (option != null && option.getcValue() != null) {
        is_cysh_tsqjszd = option.getcValue();
      } else {
        is_cysh_tsqjszd = "0";
      }
    }


    if (is_hissoftname == null || "".equals(is_hissoftname)) {
      is_hissoftname = ybjkOptionService.selectYbjkOptionByCCode("hissoftname").getcValue();
    }

    //获取各项配置信息
    if (is_gklog_xb == null || "".equals(is_gklog_xb)) {
      is_gklog_xb = ybjkOptionService.selectYbjkOptionByCCode("gklog_xb").getcValue();
      initYbgkXzxmList();
    }

    if (is_yydj == null) {
      is_yydj = ybjkOptionService.selectYbjkOptionByCCode("yydj").getcValue();
    }
    //费用审核每日审核所有明细
    if (is_fysh_day_allmx == null) {
      is_fysh_day_allmx = ybjkOptionService.selectYbjkOptionByCCode("fysh_day_allmx").getcValue();
      if (is_fysh_day_allmx == null) {
        is_fysh_day_allmx = "0";
      }
    }

    if (is_bl_date_zk == null) {
      is_bl_date_zk = ybjkOptionService.selectYbjkOptionByCCode("bl_date_zk").getcValue();
      if (is_bl_date_zk == null) {
        is_bl_date_zk = "0";
      }
    }

    if (is_check_times_for_day == null) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("check_times_for_day");
      if (option == null || StrUtil.isBlank(option.getcValue())) {
        is_check_times_for_day = "0";
      } else {
        is_check_times_for_day = option.getcValue();
      }
    }

    if (jkdy_fhmr == null) {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("jkdy_fhmr");
      if (option == null || StrUtil.isBlank(option.getcValue())) {
        jkdy_fhmr = "0";
      } else {
        jkdy_fhmr = option.getcValue();
      }
    }

    if(wgjl_history == null) {
      wgjl_history = ybjkOptionService.getOptionInfo("wgjl_history", "0");
    }

    if(wgjl_exclude == null) {
      wgjl_exclude = ybjkOptionService.getOptionInfo("wgjl_exclude", "0");
    }

    if(yb_wgjl_exclude == null) {
      yb_wgjl_exclude = ybjkOptionService.getOptionInfo("yb_wgjl_exclude", "0");
    }

    if(wgjl_wtfjc == null || "".equals(wgjl_wtfjc)) {
      wgjl_wtfjc = ybjkOptionService.getOptionInfo("wgjl_wtfjc", "0");
    }

    if(check_sum_dose == null || "".equals(check_sum_dose)) {
      check_sum_dose = ybjkOptionService.getOptionInfo("check_sum_dose", "0");
    }

  }

  /**
   * 批量费用审核
   */
  @RequestMapping("/batchFyshAll")
  public void batchFyshAll() {
    ExecutorService executorService = Executors.newFixedThreadPool(10);
    List<Brxx> brxxList = brxxService.selectBrxxList(new Brxx());
    for (int i = 0; i < brxxList.size(); i++) {
      Brxx brxx = brxxList.get(i);
      YbgkWgjl ybgkWgjl = new YbgkWgjl();
      ybgkWgjl.setJzh(brxx.getJzh());
      executorService.execute(() -> cysh(ybgkWgjl));
    }
    executorService.shutdown();
  }


  SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

  @RequestMapping("/checkPatThreeDaysAgo")
  public void checkPatThreeDaysAgo() {
    List<JsxxHis> jsxxHisList = jsxxHisService.selectPatThreeDaysAgo();
    for (JsxxHis jsxx : jsxxHisList) {
      String brid = jsxx.getBrid();
      String zyid = jsxx.getZyid();
      String jzid = jsxx.getJzid();
      String jzh = brid + "_" + zyid;

      try {
        if ("2".equals(jzid)) { //住院
          brxxSyncService.fyxxSync(brid,zyid);
        } else { //门诊
          brxxSyncService.mzfyxxSync(jzh,brid,zyid,jsxx.getFixmedinsCode(),simpleDateFormat.format(jsxx.getHisJsdate()));
        }
      } catch (Exception e) {
        logger.error(jzh + "历史病人审核-费用同步失败");
        e.printStackTrace();
      }

      YbgkWgjl ybgkWgjl = new YbgkWgjl();
      ybgkWgjl.setJzh(jzh);
      cysh(ybgkWgjl);
    }
  }


  /**
   * 批量费用审核   审核在院病人和出院三天内的病人
   */
  @RequestMapping("/batchFysh")
  public void batchFysh() {
    ExecutorService executorService = Executors.newFixedThreadPool(10);
    //获取所有在院病人以及出院三天内的病人
    List<Brxx> brxxList = brxxService.selectZyAndCyThreeDaysBrxx(new Brxx());
    for (int i = 0; i < brxxList.size(); i++) {
      //同步诊断
      try {
        brxxSyncService.zdxxSync(brxxList.get(i).getBrid(), brxxList.get(i).getZyid());
      } catch (Exception e) {

      } finally {

      }


      Brxx brxx = brxxList.get(i);
      YbgkWgjl ybgkWgjl = new YbgkWgjl();
      ybgkWgjl.setJzh(brxx.getJzh());
      //出院审核
      executorService.execute(() -> cysh(ybgkWgjl));
    }
    executorService.shutdown();
  }


  /**
   * 批量费用审核   根据配置天数审核在该天数区间内存在违规记录的病人
   */
  @RequestMapping("/batchFyshByWgjlDays")
  public void batchFyshByWgjlDays() {

    //获取配置的规定天数
    if (plcysh_brwgjlts == -1) {
      YbjkOption ybjkOption;
      ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("bl_date_zk");
      if (ybjkOption != null) {
        if (isNumber(ybjkOption.getcValue())) {
          plcysh_brwgjlts = Integer.valueOf(ybjkOption.getcValue());
        }
      }


      if (plcysh_brwgjlts == -1 || plcysh_brwgjlts == 0) {
        plcysh_brwgjlts = 3;
      }
    }

    System.out.println("即将审核" + plcysh_brwgjlts + "天内存在违规记录的病人");

    ExecutorService executorService = Executors.newFixedThreadPool(10);

    //查找规定天数内存在违规记录的病人的jzh
    List<YbgkWgjl> ybgkWgjlList = ybgkWgjlService.selectBrJzhByDays(plcysh_brwgjlts);

    System.out.println(plcysh_brwgjlts + "天内存在违规记录的病人数量：" + ybgkWgjlList.size());

    for (int i = 0; i < ybgkWgjlList.size(); i++) {
      YbgkWgjl ybgkWgjl = ybgkWgjlList.get(i);
      String jzh = ybgkWgjl.getJzh();

      //根据jzh获取brxx
      List<Brxx> brxxList = brxxService.selectBrxxByJzh(jzh);
      if (brxxList.size() > 0) {
        Brxx brxx = brxxList.get(0);

        //同步诊断
        try {
          brxxSyncService.zdxxSync(brxx.getBrid(), brxx.getZyid());
        } catch (Exception e) {

        } finally {

        }
        //费用审核
        executorService.execute(() -> cysh(ybgkWgjl));
      }
    }
    executorService.shutdown();
  }


  public boolean isNumber(String inputText) {
    try {
      Integer.valueOf(inputText);
      return true;
    } catch (Exception e) {
      return false;
    }
  }

  public BigDecimal gf_null_dec(BigDecimal as_input) {
    if (as_input == null) {
      as_input = BigDecimal.ZERO;
    }
    return as_input;
  }


  @GetMapping("/refreshCost/{brbs}")
  public AjaxResult refreshCost(@PathVariable String brbs) {


    fyxxService.deleteFyxxByJzh(brbs);
    fyxxService.deleteFyxxcyByJzh(brbs);
    String[] split = brbs.split("_");
    if (brbs.contains("_")) {
      brxxSyncService.fyxxSync(split[0], split[1]);
    }else {
      Brxx brxx1 = brxxService.selectBrxxByBrbs(brbs);
      if (brxx1!=null) {
        brxxSyncService.fyxxSync(brxx1.getBrid(), brxx1.getZyid());
      }
    }



    return success();
  }


  @GetMapping("/batchFyshByBr")
  public AjaxResult batchFysh(@RequestParam String souTable,@RequestParam String tarTable,@RequestParam String column) {
    try {
      String sql = "select * from " + souTable;
      List<Entity> entityList = Db.use(nativeDataSource).query(sql, new HashMap<>());
      for (Entity entity : entityList) {
        String jzh = entity.getStr(column);
        YbgkWgjl ybgkWgjl = new YbgkWgjl();
        ybgkWgjl.setJzh(jzh);
        TableDataInfo cyshed = cysh(ybgkWgjl);
        List<List> resultList = cyshed.getRows() != null ? (List<List>) cyshed.getRows() : new ArrayList<>();
        List<YbgkWgjl> list = new ArrayList<>();
        if (resultList.size() > 0) {
          list = resultList.get(0);
        }
        for (YbgkWgjl wgjl : list) {
          Db.use(nativeDataSource).insert(
            Entity.create(tarTable)
              .set("id", wgjl.getId())
              .set("jk_code", wgjl.getJkCode())
              .set("brtype", wgjl.getBrtype())
              .set("jzh", wgjl.getJzh())
              .set("bed", wgjl.getBed())
              .set("jktype", wgjl.getJktype())
              .set("jklog", wgjl.getJklog())
              .set("brname", wgjl.getBrname())
              .set("kdks", wgjl.getKdks())
              .set("doctor", wgjl.getDoctor())
              .set("kdksname", wgjl.getKdksname())
              .set("doctorname", wgjl.getDoctorname())
              .set("tsh_oper", wgjl.getTshOper())
              .set("fy_type", wgjl.getFyType())
              .set("fym_id", wgjl.getFymId())
              .set("fym_name", wgjl.getFymName())
              .set("sl", wgjl.getSl())
              .set("je", wgjl.getJe())
              .set("bzmc", wgjl.getBzmc())
              .set("pl", wgjl.getPl())
              .set("jldw", wgjl.getJldw())
              .set("zkyl", wgjl.getZkyl())
              .set("kzdate", wgjl.getKzdate())
              .set("cl_flag", wgjl.getClFlag())
              .set("cl_hfxx", wgjl.getClHfxx())
              .set("jylsh", wgjl.getJylsh())
              .set("create_by", wgjl.getCreateBy())
              .set("create_date", wgjl.getCreateDate())
              .set("update_by", wgjl.getUpdateBy())
              .set("update_date", wgjl.getUpdateDate())
              .set("remarks", wgjl.getRemarks())
              .set("del_flag", wgjl.getDelFlag())
              .set("zyh", wgjl.getZyh())
              .set("ybbz", wgjl.getYbbz())
              .set("zyzt", wgjl.getZyzt())
          );
        }
      }
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
    return success();
  }



  @GetMapping("/batchFyshByJs")
  public AjaxResult batchFyshByJs(@RequestParam String souTable,String tarTable,@RequestParam String column) {
    try {
      String sql = "select * from " + souTable;
      List<Entity> entityList = Db.use(nativeDataSource).query(sql, new HashMap<>());
      for (Entity entity : entityList) {
        String setlId = entity.getStr(column);
        JsxxHis jsxxHis = jsxxHisService.selectJsxxHisBySetlId(setlId);
        if (jsxxHis == null) {
          Db.use(nativeDataSource).execute("update " + souTable + " set flag = 1 where " + column + " = '" + setlId + "'");
          continue;
        }
        String brid = jsxxHis.getBrid();
        String zyid = jsxxHis.getZyid();
        List<Brxx> brxxList = brxxService.selectBrxxList(new Brxx(brid, zyid));
        if (!brxxList.isEmpty()) {
          Brxx brxx = brxxList.get(0);
          String jzh = brxx.getJzh();
          YbgkWgjl ybgkWgjl = new YbgkWgjl();
          ybgkWgjl.setJzh(jzh);
          TableDataInfo cyshed = cysh(ybgkWgjl);
          List<List> resultList = cyshed.getRows() != null ? (List<List>) cyshed.getRows() : new ArrayList<>();
          List<YbgkWgjl> list = new ArrayList<>();
          if (resultList.size() > 0) {
            list = resultList.get(0);
          }
          for (YbgkWgjl wgjl : list) {
            Db.use(nativeDataSource).insert(
              Entity.create(tarTable)
                .set("id", wgjl.getId())
                .set("jk_code", wgjl.getJkCode())
                .set("brtype", wgjl.getBrtype())
                .set("jzh", wgjl.getJzh())
                .set("bed", wgjl.getBed())
                .set("jktype", wgjl.getJktype())
                .set("jklog", wgjl.getJklog())
                .set("brname", wgjl.getBrname())
                .set("kdks", wgjl.getKdks())
                .set("doctor", wgjl.getDoctor())
                .set("kdksname", wgjl.getKdksname())
                .set("doctorname", wgjl.getDoctorname())
                .set("tsh_oper", wgjl.getTshOper())
                .set("fy_type", wgjl.getFyType())
                .set("fym_id", wgjl.getFymId())
                .set("fym_name", wgjl.getFymName())
                .set("sl", wgjl.getSl())
                .set("je", wgjl.getJe())
                .set("bzmc", wgjl.getBzmc())
                .set("pl", wgjl.getPl())
                .set("jldw", wgjl.getJldw())
                .set("zkyl", wgjl.getZkyl())
                .set("kzdate", wgjl.getKzdate())
                .set("cl_flag", wgjl.getClFlag())
                .set("cl_hfxx", wgjl.getClHfxx())
                .set("jylsh", wgjl.getJylsh())
                .set("create_by", wgjl.getCreateBy())
                .set("create_date", wgjl.getCreateDate())
                .set("update_by", wgjl.getUpdateBy())
                .set("update_date", wgjl.getUpdateDate())
                .set("remarks", wgjl.getRemarks())
                .set("del_flag", wgjl.getDelFlag())
                .set("zyh", wgjl.getZyh())
                .set("ybbz", wgjl.getYbbz())
                .set("zyzt", wgjl.getZyzt())
            );
          }
        }
      }
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
    return success();
  }

  public  double convertToDecimalAge(String input) {

    if (input==null) {
      return 0;
    }

    // 如果输入是纯数字（如 "1.2"），直接返回
    if (input.matches("\\d+(\\.\\d+)?")) {
      return Double.parseDouble(input);
    }

    int years = 0, months = 0, days = 0;

    // 正则表达式匹配 "岁"、"月"、"天" 或 "日"
    Pattern pattern = Pattern.compile("(\\d+)岁|(\\d+)月|(\\d+)天|(\\d+)日");
    Matcher matcher = pattern.matcher(input);

    // 提取匹配的值
    while (matcher.find()) {
      if (matcher.group(1) != null) { // 匹配到 "岁"
        years = Integer.parseInt(matcher.group(1));
      } else if (matcher.group(2) != null) { // 匹配到 "月"
        months = Integer.parseInt(matcher.group(2));
      } else if (matcher.group(3) != null || matcher.group(4) != null) { // 匹配到 "天" 或 "日"
        days = Integer.parseInt(matcher.group(3) != null ? matcher.group(3) : matcher.group(4));
      }
    }

    // 转换为小数的年龄
    double ageInYears = years + (double) months / 12 + (double) days / 365;
    return ageInYears;
  }

  @RequestMapping("/reGetOption")
  public void reGetOption() {
    init();
  }
}
