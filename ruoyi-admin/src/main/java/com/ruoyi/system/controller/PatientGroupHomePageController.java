package com.ruoyi.system.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.BaSyjlKsyy;
import com.ruoyi.system.domain.Drgdict;
import com.ruoyi.system.domain.Jyxx;
import com.ruoyi.system.domain.vo.BlQzYkxx;
import com.ruoyi.system.domain.vo.GroupDeptDataVo;
import com.ruoyi.system.domain.vo.GroupDoctorDataVo;
import com.ruoyi.tools.ExcelExp;
import com.ruoyi.tools.ExcelUtilManySheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.system.domain.vo.PatientGroupHomePage;
import com.ruoyi.system.service.IPatientGroupHomePageService;
import com.ruoyi.common.core.page.TableDataInfo;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/tjfx/bzsy")
public class PatientGroupHomePageController extends BaseController {
  @Autowired
  private IPatientGroupHomePageService patientGroupHomePageService;

  @GetMapping("/bzztqk")
  public TableDataInfo getBzztqk(PatientGroupHomePage patientGroupHomePage) {
    List<PatientGroupHomePage> list = patientGroupHomePageService.selectBzztfx(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzczblfyjg")
  public TableDataInfo selectBzczblfyjg(PatientGroupHomePage patientGroupHomePage) {
    List<PatientGroupHomePage> list = patientGroupHomePageService.selectBzczblfyjg(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzjyblfyjg")
  public TableDataInfo selectBzjyblfyjg(PatientGroupHomePage patientGroupHomePage) {
    List<PatientGroupHomePage> list = patientGroupHomePageService.selectBzjyblfyjg(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzczjy")
  public TableDataInfo getBzCzjy(PatientGroupHomePage patientGroupHomePage) {
    List<PatientGroupHomePage> list = patientGroupHomePageService.selectBzCzjy(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzfyjg")
  public TableDataInfo getFyjg(PatientGroupHomePage patientGroupHomePage) {
    List<PatientGroupHomePage> list = patientGroupHomePageService.selectFyjg(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzbllistByDoctor")
  public TableDataInfo selectBzblByDoctor(PatientGroupHomePage patientGroupHomePage) {
    List<GroupDoctorDataVo> list = patientGroupHomePageService.selectBzblByDoctor(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzbllistByDept")
  public TableDataInfo selectBzblByDept(PatientGroupHomePage patientGroupHomePage) {
    List<GroupDeptDataVo> list = patientGroupHomePageService.selectBzblByDept(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzbllxfxlist")
  public TableDataInfo selectBzBllxfx(PatientGroupHomePage patientGroupHomePage) {
    List<PatientGroupHomePage> list = patientGroupHomePageService.selectBzBllxfx(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzczjybllist")
  public TableDataInfo selectBzCzjyBl(PatientGroupHomePage patientGroupHomePage) {
    List<PatientGroupHomePage> list = patientGroupHomePageService.selectBzCzjyBl(patientGroupHomePage);
    return getDataTable(list);
  }


  @PostMapping("/export")
  public void export(HttpServletResponse response, PatientGroupHomePage patientGroupHomePage) throws IOException
  {
    List<GroupDoctorDataVo> doctorDataList = patientGroupHomePageService.selectBzblByDoctor(patientGroupHomePage);
    List<GroupDeptDataVo> deptDataList = patientGroupHomePageService.selectBzblByDept(patientGroupHomePage);
    ExcelExp doctorExp = new ExcelExp("同病组不同医生", doctorDataList, GroupDoctorDataVo.class);
    ExcelExp deptExp = new ExcelExp("同病组不同科室", deptDataList, GroupDeptDataVo.class);
    List<ExcelExp> sheets = new ArrayList<>();
    sheets.add(doctorExp);
    sheets.add(deptExp);
    ExcelUtilManySheet<List<ExcelExp>> util = new ExcelUtilManySheet<>(sheets);
    util.exportExcelManySheet(response, sheets);
  }

  @PostMapping("/exportKsmx")
  public void exportKsmx(HttpServletResponse response, PatientGroupHomePage patientGroupHomePage) throws IOException {
    List<BaSyjlKsyy> ksmx = patientGroupHomePageService.selectBzKsmx(patientGroupHomePage);
    List<BaSyjlKsyy> ksmxDept = patientGroupHomePageService.selectBzKsmxDept(patientGroupHomePage);
    ExcelExp doctorExp = new ExcelExp("亏损明细", ksmx, BaSyjlKsyy.class);
    ExcelExp deptExp = new ExcelExp("按科室", ksmxDept, BaSyjlKsyy.class);
    List<ExcelExp> sheets = new ArrayList<>();
    sheets.add(doctorExp);
    sheets.add(deptExp);
    ExcelUtilManySheet<List<ExcelExp>> util = new ExcelUtilManySheet<>(sheets);
    util.exportExcelManySheet(response, sheets);
  }

  @GetMapping("/bzKsmx")
  public TableDataInfo selectBzKsmx(PatientGroupHomePage patientGroupHomePage) {
    List<BaSyjlKsyy> list = patientGroupHomePageService.selectBzKsmx(patientGroupHomePage);
    return getDataTable(list);
  }

  @GetMapping("/bzKsmxdept")
  public TableDataInfo selectBzKsmxDept(PatientGroupHomePage patientGroupHomePage) {
    List<BaSyjlKsyy> list = patientGroupHomePageService.selectBzKsmxDept(patientGroupHomePage);
    return getDataTable(list);
  }



  @GetMapping("/bzKsxmpm")
  public TableDataInfo selectBzKsxm(PatientGroupHomePage patientGroupHomePage) {
    List<BaSyjlKsyy> list = patientGroupHomePageService.selectBzKsxm(patientGroupHomePage);
    return getDataTable(list);
  }

}
