package com.ruoyi.system.controller;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbgkWgjlHistory;
import com.ruoyi.system.service.IYbgkWgjlHistoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 违规历史记录Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@Anonymous
@RequestMapping("/system/wgjlHistory")
public class YbgkWgjlHistoryController extends BaseController
{
    @Autowired
    private IYbgkWgjlHistoryService ybgkWgjlHistoryService;

    /**
     * 查询违规历史记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:wgjlHistory:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbgkWgjlHistory ybgkWgjlHistory)
    {
        startPage();
        List<YbgkWgjlHistory> list = ybgkWgjlHistoryService.selectYbgkWgjlHistoryList(ybgkWgjlHistory);
        return getDataTable(list);
    }

    @GetMapping("/excludeWgjl")
    public AjaxResult excludeWgjl(YbgkWgjlHistory ybgkWgjlHistory) throws ParseException {
//      logger.info("hhhhhhhhhhhhhhhh");
        ybgkWgjlHistoryService.excludeWgjl(ybgkWgjlHistory);
        return success();
    }

    @GetMapping("/ybExcludeWgjl")
    public AjaxResult ybExcludeWgjl(YbgkWgjlHistory ybgkWgjlHistory) throws ParseException {
      String username = getLoginUser().getUsername();
      ybgkWgjlHistory.setExcludeUser(username)
          .setExcludeDate(new Date());
      ybgkWgjlHistoryService.ybExcludeWgjl(ybgkWgjlHistory);
      return success();
    }

    /**
     * 导出违规历史记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:wgjlHistory:export')")
    @Log(title = "违规历史记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbgkWgjlHistory ybgkWgjlHistory)
    {
        List<YbgkWgjlHistory> list = ybgkWgjlHistoryService.selectYbgkWgjlHistoryList(ybgkWgjlHistory);
        ExcelUtil<YbgkWgjlHistory> util = new ExcelUtil<YbgkWgjlHistory>(YbgkWgjlHistory.class);
        util.exportExcel(response, list, "违规历史记录数据");
    }

    /**
     * 获取违规历史记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:wgjlHistory:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(ybgkWgjlHistoryService.selectYbgkWgjlHistoryById(id));
    }

    /**
     * 新增违规历史记录
     */
    @PreAuthorize("@ss.hasPermi('system:wgjlHistory:add')")
    @Log(title = "违规历史记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbgkWgjlHistory ybgkWgjlHistory)
    {
        return toAjax(ybgkWgjlHistoryService.insertYbgkWgjlHistory(ybgkWgjlHistory));
    }

    /**
     * 修改违规历史记录
     */
    @PreAuthorize("@ss.hasPermi('system:wgjlHistory:edit')")
    @Log(title = "违规历史记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbgkWgjlHistory ybgkWgjlHistory)
    {
        return toAjax(ybgkWgjlHistoryService.updateYbgkWgjlHistory(ybgkWgjlHistory));
    }

    /**
     * 删除违规历史记录
     */
    @PreAuthorize("@ss.hasPermi('system:wgjlHistory:remove')")
    @Log(title = "违规历史记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(ybgkWgjlHistoryService.deleteYbgkWgjlHistoryByIds(ids));
    }
}
