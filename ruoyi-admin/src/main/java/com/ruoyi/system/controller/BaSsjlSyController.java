package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.system.domain.BaSsjl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.BaSsjlSy;
import com.ruoyi.system.service.IBaSsjlSyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 首页临床手术Controller
 *
 * <AUTHOR>
 * @date 2023-11-24
 */
@RestController
@Anonymous
@RequestMapping("/drg/brssxxsy")
public class BaSsjlSyController extends BaseController
{
    @Autowired
    private IBaSsjlSyService baSsjlSyService;

    /**
     * 查询首页临床手术列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BaSsjlSy baSsjlSy)
    {
        List<BaSsjlSy> list = baSsjlSyService.selectBaSsjlSyList(baSsjlSy);
        return getDataTable(list);
    }

    /**
     * 导出首页临床手术列表
     */
    @Log(title = "首页临床手术", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaSsjlSy baSsjlSy)
    {
        List<BaSsjlSy> list = baSsjlSyService.selectBaSsjlSyList(baSsjlSy);
        ExcelUtil<BaSsjlSy> util = new ExcelUtil<BaSsjlSy>(BaSsjlSy.class);
        util.exportExcel(response, list, "首页临床手术数据");
    }

    /**
     * 获取首页临床手术详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(baSsjlSyService.selectBaSsjlSyById(id));
    }

    /**
     * 新增首页临床手术
     */
    @Log(title = "首页临床手术", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaSsjl baSsjlSy)
    {
        return toAjax(baSsjlSyService.insertBaSsjlSy(baSsjlSy));
    }

    /**
     * 修改首页临床手术
     */
    @Log(title = "首页临床手术", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaSsjlSy baSsjlSy)
    {
        return toAjax(baSsjlSyService.updateBaSsjlSy(baSsjlSy));
    }

    /**
     * 删除首页临床手术
     */
    @Log(title = "首页临床手术", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(baSsjlSyService.deleteBaSsjlSyByIds(ids));
    }



    @RequestMapping("/ssxxsy")
    public TableDataInfo ssxxsy(BaSsjlSy baSsjlSy)
    {
        List<BaSsjlSy> list = baSsjlSyService.selectSySsxx(baSsjlSy);
        return getDataTable(list);
    }
}
