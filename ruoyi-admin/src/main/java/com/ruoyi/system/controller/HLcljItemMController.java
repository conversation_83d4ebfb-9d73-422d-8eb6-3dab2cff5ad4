package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HLcljItemM;
import com.ruoyi.system.service.IHLcljItemMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 路径项目管理Controller
 * 
 * <AUTHOR>
 * @date 2023-07-16
 */
@RestController
@RequestMapping("/system/lcljItemM")
public class HLcljItemMController extends BaseController
{
    @Autowired
    private IHLcljItemMService hLcljItemMService;

    /**
     * 查询路径项目管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HLcljItemM hLcljItemM)
    {
        List<HLcljItemM> list = hLcljItemMService.selectHLcljItemMList(hLcljItemM);
        return getDataTable(list);
    }

    /**
     * 导出路径项目管理列表
     */
    @Log(title = "路径项目管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HLcljItemM hLcljItemM)
    {
        List<HLcljItemM> list = hLcljItemMService.selectHLcljItemMList(hLcljItemM);
        ExcelUtil<HLcljItemM> util = new ExcelUtil<HLcljItemM>(HLcljItemM.class);
        util.exportExcel(response, list, "路径项目管理数据");
    }

    /**
     * 获取路径项目管理详细信息
     */
    @GetMapping(value = "/{cId}")
    public AjaxResult getInfo(@PathVariable("cId") String cId)
    {
        return success(hLcljItemMService.selectHLcljItemMByCId(cId));
    }

    /**
     * 新增路径项目管理
     */
    @Log(title = "路径项目管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HLcljItemM hLcljItemM)
    {
        return toAjax(hLcljItemMService.insertHLcljItemM(hLcljItemM));
    }

    /**
     * 修改路径项目管理
     */
    @Log(title = "路径项目管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HLcljItemM hLcljItemM)
    {
        return toAjax(hLcljItemMService.updateHLcljItemM(hLcljItemM));
    }

    /**
     * 删除路径项目管理
     */
    @Log(title = "路径项目管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cIds}")
    public AjaxResult remove(@PathVariable String[] cIds)
    {
        return toAjax(hLcljItemMService.deleteHLcljItemMByCIds(cIds));
    }
}
