package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DrgIcddyss;
import com.ruoyi.system.service.IDrgIcddyssService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 诊断手术对应表Controller
 *
 * <AUTHOR>
 * @date 2024-01-30
 */
@Anonymous
@RestController
@RequestMapping("/system/icddyss")
public class DrgIcddyssController extends BaseController
{
    @Autowired
    private IDrgIcddyssService drgIcddyssService;

    /**
     * 查询诊断手术对应表列表
     */
    @PreAuthorize("@ss.hasPermi('system:icddyss:list')")
    @GetMapping("/list")
    public TableDataInfo list(DrgIcddyss drgIcddyss)
    {
        startPage();
        List<DrgIcddyss> list = drgIcddyssService.selectDrgIcddyssList(drgIcddyss);
        return getDataTable(list);
    }

    @GetMapping("/list2")
    public TableDataInfo list2(DrgIcddyss drgIcddyss)
    {
      List<DrgIcddyss> list = drgIcddyssService.selectDrgIcddyssList(drgIcddyss);
      return getDataTable(list);
    }

    /**
     * 导出诊断手术对应表列表
     */
    @PreAuthorize("@ss.hasPermi('system:icddyss:export')")
    @Log(title = "诊断手术对应表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DrgIcddyss drgIcddyss)
    {
        List<DrgIcddyss> list = drgIcddyssService.selectDrgIcddyssList(drgIcddyss);
        ExcelUtil<DrgIcddyss> util = new ExcelUtil<DrgIcddyss>(DrgIcddyss.class);
        util.exportExcel(response, list, "诊断手术对应表数据");
    }

    /**
     * 获取诊断手术对应表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:icddyss:query')")
    @GetMapping(value = "/{bzbm}")
    public AjaxResult getInfo(@PathVariable("bzbm") String bzbm)
    {
        return success(drgIcddyssService.selectDrgIcddyssByBzbm(bzbm));
    }

    /**
     * 新增诊断手术对应表
     */
    @PreAuthorize("@ss.hasPermi('system:icddyss:add')")
    @Log(title = "诊断手术对应表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DrgIcddyss drgIcddyss)
    {
        return toAjax(drgIcddyssService.insertDrgIcddyss(drgIcddyss));
    }

    /**
     * 修改诊断手术对应表
     */
    @PreAuthorize("@ss.hasPermi('system:icddyss:edit')")
    @Log(title = "诊断手术对应表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DrgIcddyss drgIcddyss)
    {
        return toAjax(drgIcddyssService.updateDrgIcddyss(drgIcddyss));
    }

    /**
     * 删除诊断手术对应表
     */
	@RequestMapping("/remove")
    public AjaxResult remove(DrgIcddyss drgIcddyss)
    {
        return toAjax(drgIcddyssService.deleteDrgIcddyss(drgIcddyss));
    }
}
