package com.ruoyi.system.controller;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/*
智能编码推荐
 */
@RestController
@Anonymous
@RequestMapping("/drg/znbmtj")
public class ZnbmTjController extends BaseController {
  @Autowired
  private IBaSyjlService baSyjlService;
  @Autowired
  private IBaBrzdxxService baBrzdxxService;
  @Autowired
  private IBaSsjlService baSsjlService;
  @Autowired
  private IJyxxService jyxxService;
  @Autowired
  private IBrxxService brxxService;
  @Autowired
  private IBBasyCheckBzService basyCheckBzService;
  @Autowired
  private DrgFzController drgFzController;
  @Autowired
  private IYbgkWgjlService wgjlService;
  @Autowired
  private IFyxxService fyxxService;

  @Autowired
  private BzmlfController bzmlfController;
  @Autowired
  private IDrgdictService drgdictService;
  @Autowired
  private IBaSsjlTjzdService baSsjlTjzdService;
  @Autowired
  private IBzmlService bzmlService;
  @Autowired
  private IZdxxService zdxxService;
  @Autowired
  private BaxyController baxyController;
  @Autowired
  private YfzController yfzController;
  @Autowired
  private IZdpxService zdpxService;
  @Autowired
  private IYbjkOptionService ybjkOptionService;
  @Autowired
  private IZdpdService zdpdService;

  String jzh = "";
  String znbmFlagVal = null;

  @RequestMapping("/znbm")
  public TableDataInfo znbm(@RequestParam(value = "brid") String in_brid, @RequestParam(value = "zyid") String in_zyid, @RequestParam(value = "yhbs") String yhbs) {

    if (znbmFlagVal == null) {
      YbjkOption znbmFlag = ybjkOptionService.selectYbjkOptionByCCode("znbm_flag");
      if (znbmFlag != null) {
        znbmFlagVal = znbmFlag.getcValue();
      }
    }

    if (znbmFlagVal == null || "".equals(znbmFlagVal)) {
      znbmFlagVal = "1";
    }

    List<BaBrzdxx> qtZd = null;  //存放其他诊断
    List<BaBrzdxx> lsqtzd = null;
    List<BaBrzdxx> resultList = null;
    List<BaSsjl> ssjl = null;
    List<String> errorList = null;
    List<Tip> tipList = null;
    List<BaBrzdxx> baBrzdxxList = null;
    List<BaSsjl> baSsjlList = null;
    String brid = "";
    String zyid = "";
    List<String> rybqList = null;

    if ("".equals(in_zyid)) {
      zyid = null;
    } else {
      zyid = in_zyid;
    }

    if ("".equals(in_brid)) {
      errorList = new ArrayList<>();
      errorList.add("病人ID为空");
      return getDataTable(errorList);
    } else {
      brid = in_brid;
    }

    Brxx brxx = brxxService.selectBrxxByBridAndZyid(new Brxx(brid, zyid));
    BaSyjl baSyjl = baSyjlService.selectBaSyjlByBridAndZyid(new BaSyjl(brid, zyid));

    if (baSyjl == null) {
      errorList = new ArrayList<>();
      errorList.add("病案不存在");
      return getDataTable(errorList);
    }

    jzh = baSyjl.getJzh();
    if (jzh == null || "".equals(jzh)) {
      jzh = brxx.getJzh();
    }

    if (tipList == null) {
      tipList = new ArrayList<>();
    } else {
      tipList.clear();
    }
    if (rybqList == null) {
      rybqList = new ArrayList<>();
    } else {
      rybqList.clear();
    }
    if (ssjl == null) {
      ssjl = new ArrayList<>();
    } else {
      ssjl.clear();
    }
    if (qtZd == null) {
      qtZd = new ArrayList<>();
    } else {
      qtZd.clear();
    }
    if (baBrzdxxList == null) {
      baBrzdxxList = new ArrayList<>();
    } else {
      baBrzdxxList.clear();
    }

    BaBrzdxx zzd = null;
    List<Zdpx> tlxzd = null;
    List<BaBrzdxx> tlxqtzd = null;
    String[] zdMcArr = null;
    String[] zdBmArr = null;
    String splitDiag = "";

    baBrzdxxList = baBrzdxxService.selectBaBrzdxxList(new BaBrzdxx(brid, zyid));
    baSsjlList = baSsjlService.selectBaSsjlList(new BaSsjl(brid, zyid));

    baBrzdxxList.removeIf(obj -> obj == null || obj.getJbbm() == null || obj.getZdmc() == null);
    baSsjlList.removeIf(obj -> obj == null || obj.getSsbm() == null || obj.getSsmc() == null);

    System.out.println("0--------------------------------------------诊断数量:" + baBrzdxxList.size());

    if (brxx != null) {
      if (brxx.getBlcyzd() != null && !"".equals(brxx.getBlcyzd())) {
        splitDiag = brxx.getBlcyzd();
      }
      if ("".equals(splitDiag.trim())) {
        splitDiag = brxx.getBlryzd();
      }
      if (splitDiag == null || "".equals(splitDiag.trim())) {
        splitDiag = "";
      }
    }

    System.out.println("0--------------------------------------------出入院诊断:" + splitDiag);

    if (!"".equals(splitDiag) && splitDiag != null) {
      String formatDiag = formatZdxx(splitDiag);
      splitDiag = bzmlfController.getjbbmml("lczd", formatDiag, "cyzd");
    }

    System.out.println("0--------------------------------------------分解后诊断:" + splitDiag);


    if (baBrzdxxList.size() == 0 && ("".equals(splitDiag.trim()) || splitDiag == null || "||".equals(splitDiag.trim()))) {
      errorList = new ArrayList<>();
      errorList.add("患者诊断信息为空");
      return getDataTable(errorList);
    }

    System.out.println("1---------------默认Babrzdxx列表---------------");
    baBrzdxxList.forEach(item -> System.out.print(item.getZdmc() + "  "));
    System.out.println("");
    System.out.println("1---------------------------------------------");


    if (!"".equals(splitDiag.trim()) && !"||".equals(splitDiag.trim())) {
      zdBmArr = splitDiag.split("\\|\\|")[0].split(",");
      zdMcArr = splitDiag.split("\\|\\|")[1].split(",");
    }

    System.out.println("2---------------分解诊断列表---------------------");
    if (zdMcArr != null && zdMcArr.length > 0) {
      for (int i = 0; i < zdMcArr.length; i++) {
        System.out.print(zdMcArr[i] + "   ");
      }
    }
    System.out.println("");
    System.out.println("2---------------------------------------------");


    //将分解的诊断添加到诊断列表
    if (zdBmArr != null && zdBmArr.length > 0) {
      for (int i = 0; i < zdBmArr.length; i++) {
        baBrzdxxList.add(new BaBrzdxx(zdMcArr[i].trim(), zdBmArr[i].trim(), ""));
      }
    }

    //诊断去重
    baBrzdxxList = toUniqueDiagList(baBrzdxxList);
    cleanseErrForSexDiag(baBrzdxxList, baSyjl);


    //分解出主要诊断和其他诊断
    int zzd_bs = 0;
    if (baBrzdxxList.size() > 0) {
      zzd_bs = 1;
      zzd = new BaBrzdxx(baBrzdxxList.get(0).getZdmc(), baBrzdxxList.get(0).getJbbm(), baBrzdxxList.get(0).getRybq());
      qtZd = new ArrayList<>(baBrzdxxList.subList(1, baBrzdxxList.size()));
    }


    System.out.println("3---------------默认诊断+分解诊断---------------------");
    for (int i = 0; i < qtZd.size(); i++) {
      System.out.print(qtZd.get(i).getZdmc() + "   ");
    }
    System.out.println("");
    System.out.println("3-----------------------------------------------" + zzd.getZdmc() + "  " + zzd.getJbbm());


    if ("0".equals(znbmFlagVal)) {
      resultList = new ArrayList<>();

      if (zzd != null && zzd.getJbbm() != null && !"".equals(zzd.getJbbm())) {
        resultList.add(zzd);
        resultList.addAll(qtZd);


        for (int i = 0; i < resultList.size(); i++) {
          resultList.get(i).setJbbm(resultList.get(i).getJbbm().trim());
          resultList.get(i).setZdmc(resultList.get(i).getZdmc().trim());
        }
        //去重
        resultList = toUniqueDiagList(resultList);
        baSsjlList = toUniqueSurgList(baSsjlList);
        resultList.removeIf(obj -> obj == null || obj.getJbbm() == null || "".equals(obj.getJbbm().trim()) || obj.getZdmc() == null || "".equals(obj.getZdmc().trim()));

        for (int i = 0; i < resultList.size(); i++) {
          resultList.get(i).setZdcx(i + 1);
        }

        //获取并设置诊断的CC/MCC类型
        resultList = setDiagType(resultList);

        //保存编码结果
        saveDiagSurgInfo(baBrzdxxList, baSsjlList, brid, zyid, yhbs);
      }

      List<List> list = new ArrayList<>();
      list.add(resultList);
      list.add(baSsjlList);
      list.add(tipList);

      return getDataTable(list);
    }


    List<BaBrzdxx> brzdxxList = new ArrayList<>();
    if (qtZd.size() > 0) {
      brzdxxList.clear();
      brzdxxList.add(zzd);
      brzdxxList.addAll(qtZd);
      brzdxxList = zdhbpd(brzdxxList);    //诊断合并
      brzdxxList = toUniqueDiagList(brzdxxList);  //去重

      zzd = brzdxxList.get(0);
      qtZd = new ArrayList<>(brzdxxList.subList(1, brzdxxList.size()));
    }

    System.out.println("4---------------合并后的诊断信息-----------------------");
    qtZd.forEach(item -> System.out.print(item.getZdmc() + "  "));
    System.out.println("");
    System.out.println("4---------------------------------------------------" + zzd.getZdmc() + "  " + zzd.getJbbm());


    //先保存前边组装的诊断信息
    JSONArray jsonArray = new JSONArray();
    jsonArray.add(new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq(), 1));
    for (int i = 0; i < qtZd.size(); i++) {
      jsonArray.add(new BaBrzdxx(qtZd.get(i).getZdmc(), qtZd.get(i).getJbbm(), qtZd.get(i).getRybq(), i + 2));
    }
    yfzController.save(jsonArray.toJSONString(), "", brid, zyid, yhbs);


    List<BrzdFyxx> brzdssFyxx = baBrzdxxService.selectWkZyzd(baSyjl);

    System.out.println("5-------------------------------------------手术信息数量：" + brzdssFyxx.size());

    //如果手术信息为空 获取同类诊断
    if (brzdssFyxx.size() == 0) {
      List<Zdpx> zdpxes = zdpxService.selectZdpxList(new Zdpx(zzd.getZdmc(), zzd.getJbbm()));

      System.out.println("6-------------------------------------------主诊断诊断排行信息是否存在：" + (zdpxes.size() > 0 ? true : false));

      if (zdpxes.size() > 0) {
        zzd.setYxj(zdpxes.get(0).getZdcx());   //获取主诊断优先级   后面与同类诊断优先级比较
        tlxzd = zdpxService.selectTlxZd(new Zdpx(zzd.getZdmc(), zzd.getJbbm())); //同类型诊断

        System.out.println("7-------------------------------------------同类型诊断排行数量：" + tlxzd.size());

        List<String> tlxzdbm = new ArrayList<>();

        if (tlxzd.size() > 0) {
          System.out.println("8--------------------同类型诊断---------------------------");
          for (Zdpx zdpx : tlxzd) {
            tlxzdbm.add(zdpx.getJbbm().trim());
            System.out.print(zdpx.getZdmc().trim() + "  ");
          }
          System.out.println();
          System.out.println("8-----------------------------------------------------");

          String jbbm = null;
          Integer yxj = null;
          tlxqtzd = new ArrayList<>();
          System.out.println("9-------------------同类型其他诊断----------------------------------");
          for (int i = 0; i < qtZd.size(); i++) {
            jbbm = qtZd.get(i).getJbbm().trim();
            if (tlxzdbm.contains(jbbm)) {
              yxj = tlxzd.get(tlxzdbm.indexOf(jbbm)).getZdcx();
              tlxqtzd.add(new BaBrzdxx(yxj, i));        //yxj是当前诊断优先级  i代表当前诊断在qtzd中的位置
              System.out.print(qtZd.get(i).getZdmc() + "    ");
              continue;
            }
          }
          System.out.println();
          System.out.println("9-----------------------------------------------------");
        }
      }
    } else {   //如果有手术信息就不获取同类诊断，将手术信息加入到手术列表
      //手术去重
      ssjl = toUniqueSurgList(ssjl);
    }

    if (tlxqtzd == null || tlxqtzd.size() == 0) {     //如果同类型诊断为空       继续根据费用获取诊断
      if (zzd.getJbbm() != null)
        if (zzd.getJbbm().charAt(0) == 'C' || zzd.getJbbm().charAt(0) == 'O') {        //主诊断为C诊断或O诊断时，不判断费用
        } else {
          //获取诊断费用信息
          List<BaBrzdxx> brzdFyxxList = baBrzdxxService.selectZdFyxx(baSyjl);

          YbjkOption ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("zdtj_fyxz");
          String zdtj_fyxz = "300";
          if (ybjkOption != null) {
            zdtj_fyxz = ybjkOption.getcValue();
            System.out.println("11-----------------------------------------限制金额：" + zdtj_fyxz);
          }

          //获取最大诊断
          double maxJe = 0;
          for (int i = 0; i < brzdFyxxList.size(); i++) {
            if (brzdFyxxList.get(i).getJe() > maxJe) {
              maxJe = brzdFyxxList.get(i).getJe();
            }
          }
          System.out.println("11-----------------------------------------最大金额：" + maxJe);

          if (maxJe > Double.parseDouble(zdtj_fyxz)) {
            System.out.println("11-------------------------------------------诊断费用数量：" + brzdFyxxList.size());

            if (brzdFyxxList.size() > 0) {
              BaBrzdxx jxzd = null;
              for (BaBrzdxx brzdFyxx : brzdFyxxList) {   //搜索费用列表查看是否存在急性诊断且费用大于100
                if (brzdFyxx.getZdmc().indexOf("急性") > -1 && jxzd != null && brzdFyxx.getJe() > 100) {
                  jxzd = new BaBrzdxx();
                  jxzd.setZdmc(brzdFyxx.getZdmc());
                  jxzd.setJbbm(brzdFyxx.getJbbm());
                }
              }

              System.out.println("12-------------------------------------------急性诊断：" + jxzd);

              if (jxzd != null) {   //如果存在满足条件的急性诊断，将其设为主诊断，之前的主诊断加入其他诊断
                if (zzd_bs == 0) {
                  zzd = jxzd;
                } else {
                  qtZd.add(0, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq()));
                  zzd = jxzd;
                }
                zzd_bs = 1;
              } else {  //如果不存在满足条件的其他诊断，就判断当前主诊断是否存在费用，且费用排在前三名，且与第一名的差距不超过总费用的30%，如果满足条件就不做改动
                int zzd_fy_flag = 0;
                double zfy = 0;
                for (int i = 0; i < brzdFyxxList.size(); i++) {
                  zfy += brzdFyxxList.get(i).getJe();  //总费用
                }
                System.out.println("12-------------------------------------------总费用：" + zfy);
                for (int i = 0; i < brzdFyxxList.size(); i++) {
                  if (brzdFyxxList.get(i).getJbbm().equals(zzd.getJbbm()) && i < 3) {   //当前主诊断存在费用且排在前三名
                    double xcfy = brzdFyxxList.get(0).getJe() - brzdFyxxList.get(i).getJe();
                    System.out.println("121-------------------------------------------相差费用：" + xcfy);
                    if (xcfy < zfy * 0.3) {  //与最高费用相差不超过30%
                      zzd_fy_flag = 1;   //zzd_fy_flag不做改动
                      break;
                    }
                  }
                }
                if (zzd_fy_flag == 0) {  //不满足以上条件（急性诊断、主诊断费用排前三名并与第一名相差不超过30%），则将费用最高的诊断设为主诊断
                  if (zzd_bs == 1) {
                    qtZd.add(0, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq()));  //将之前的主诊断添加到其他诊断
                  }
                  zzd = new BaBrzdxx(brzdFyxxList.get(0).getZdmc(), brzdFyxxList.get(0).getJbbm(), "");
                  zzd_bs = 1;
                }
              }


              System.out.println("13----------------费用判断后的诊断信息----------------------");
              System.out.println("主诊断：" + zzd.getZdmc() + "  " + zzd.getJbbm());
              for (int i = 0; i < qtZd.size(); i++) {
                System.out.print(qtZd.get(i).getZdmc() + " ");
              }
              System.out.println();
              System.out.println("13------------------------------------------------------");
            }
          }

        }
    } else {  //存在同类型诊断 不进行费用判断
      int zzd_yxj = zzd.getYxj();
      int zyxzd_wz = -1;   //同类中最优先诊断位置
      for (int i = 0; i < tlxqtzd.size(); i++) {
        if (tlxqtzd.get(i).getYxj() < zzd_yxj) {
          zyxzd_wz = tlxqtzd.get(i).getWz();
          zzd_yxj = tlxqtzd.get(i).getYxj();
        }
      }
      if (zyxzd_wz > -1) {
        BaBrzdxx brzdxx1 = qtZd.get(zyxzd_wz);
        qtZd.set(zyxzd_wz, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), ""));
        zzd = new BaBrzdxx(brzdxx1.getZdmc(), brzdxx1.getJbbm(), "");
      }


      System.out.println("14----------------同类型诊断判断后的诊断信息----------------------");
      System.out.println("主诊断：" + zzd.getZdmc() + "  " + zzd.getJbbm());
      for (int i = 0; i < qtZd.size(); i++) {
        System.out.print(qtZd.get(i).getZdmc() + " ");
      }
      System.out.println();
      System.out.println("14-----------------------------------------------------------");

    }

    //获取当前病案的分组情况
    Drgdict drgdict = null;
    if (baSyjl.getDrgbh() != null && !baSyjl.getDrgbh().equals("")) {
      drgdict = drgdictService.selectDrgdictByDrgbh(baSyjl.getDrgbh());
    }

    //删除该病人的智能编码信息
    zdxxService.deleteZdxxByBridAndZyid(new Zdxx(brid, zyid));
    baSsjlTjzdService.deleteBaSsjlTjzdByBridAndZyid(new BaSsjlTjzd(brid, zyid));

    System.out.println("15---------------------------------------------------" + qtZd.size());

    //去除其他诊断中与主诊断重复的诊断
    if (qtZd.size() > 0) {
      for (int i = 0; i < qtZd.size(); i++) {
        if (qtZd.get(i).getJbbm().equals(zzd.getJbbm()) || qtZd.get(i).getZdmc().equals(zzd.getZdmc())) {
          qtZd.remove(i);
        }
      }
    }

    if (zzd.getJbbm() != null) {
      otherOperate(baSyjl, qtZd, zzd, tipList, baBrzdxxList, brid, zyid, rybqList);
    }

    //组装主诊断和其他诊断
    resultList = new ArrayList<>();
    resultList.add(zzd);
    resultList.addAll(qtZd);

    resultList.forEach(item -> item.setJbbm(item.getJbbm().trim()));
    resultList = toUniqueDiagList(resultList);

    cleanseErrForSexDiag(resultList, baSyjl);

    System.out.println("1010-------------------");
    for (int i = 0; i < resultList.size(); i++) {
      System.out.println(resultList.get(i).getJbbm());
    }

    //设置次序与默认入院病情
    for (int i = 0; i < resultList.size(); i++) {
      resultList.get(i).setZdcx(i + 1);
      resultList.get(i).setRybq("");
    }
    for (int i = 0; i < ssjl.size(); i++) {
      ssjl.get(i).setSscx(i + 1);
    }

    //将智能编码信息插入到数据库
    for (int i = 0; i < resultList.size(); i++) {
      zdxxService.insertZdxx(new Zdxx(baSyjl.getJzh() == null ? "" : baSyjl.getJzh(), brid, zyid, resultList.get(i).getJbbm(), resultList.get(i).getZdmc(), "znzd", i + 1, baSyjl.getDrgbh() == null ? "" : baSyjl.getDrgbh(), drgdict != null ? drgdict.getZfbz() == null ? BigDecimal.ZERO : drgdict.getZfbz() : BigDecimal.ZERO));
    }
    for (int i = 0; i < ssjl.size(); i++) {
      baSsjlTjzdService.insertBaSsjlTjzd(new BaSsjlTjzd(baSyjl.getJzh() == null ? "" : baSyjl.getJzh(), brid, zyid, ssjl.get(i).getSsbm(), ssjl.get(i).getSsmc(), "znzd", "", i + 1, baSyjl.getDrgbh() == null ? "" : baSyjl.getDrgbh(), drgdict != null ? drgdict.getZfbz() == null ? BigDecimal.ZERO : drgdict.getZfbz() : BigDecimal.ZERO));
    }

    //获取并设置诊断的CC/MCC类型
    resultList = setDiagType(resultList);

    for (BaSsjl baSsjl : ssjl) {
      baSsjl.setSscx(baSsjlList.size());
      baSsjlList.add(baSsjl);
    }
    //按手术次序排序
    baSsjlList = baSsjlList.stream().sorted(Comparator.comparing(BaSsjl::getSscx)).collect(Collectors.toList());

    List<List> list = new ArrayList<>();
    list.add(resultList);
    list.add(baSsjlList);
    list.add(tipList);

    //保存编码结果
    saveDiagSurgInfo(baBrzdxxList, baSsjlList, brid, zyid, yhbs);
    return getDataTable(list);
  }

  //   清洗违反性别限制的字段
  public void cleanseErrForSexDiag(List<BaBrzdxx> zdList, BaSyjl baSyjl) {
    List<String> errCodeList = baxyController.check_sex_code(zdList, baSyjl.getXb());
    List<Integer> removeZd = new ArrayList<>();

    for (int i = 0; i < zdList.size(); i++) {
      BaBrzdxx zd = zdList.get(i);
      if (errCodeList.contains(zd.getJbbm())) {
        removeZd.add(i);
      }
    }

    for (Integer i : removeZd) {
      zdList.remove(i.intValue());
    }

  }


  public void otherOperate(BaSyjl baSyjl, List<BaBrzdxx> qtZd, BaBrzdxx zzd, List<Tip> tipList, List<BaBrzdxx> baBrzdxxList, String brid, String zyid, List<String> rybqList) {
    czdpd(qtZd, zzd, brid, zyid);
    if (qtZd.size() > 0) {
      zzdpd(qtZd, zzd, tipList);
    }

    List<Zdpd> zdpdList = zdpdService.selectZdpdList(new Zdpd());   //所有判断规则

    System.out.println("-----------------------------判断规则数量：" + zdpdList.size());

    String[] lyfsfw = new String[]{};       //离院方式判断范围
    String[] lyfsnfw = new String[]{};      //不属于离院方式判断范围
    String[] zyzdfwArr = new String[]{};    //主要诊断目标范围数组
    String zyzdfw = "";                     //主要诊断目标范围
    String pdlb = "";                       //判断类别

    for (Zdpd zdpd : zdpdList) {

      int i = zdpdList.indexOf(zdpd);

      System.out.println(i + "---------------------------当前判断:" + zdpd.getPdmc());

      zyzdfw = zdpd.getZzd();


      System.out.println(i + "---------------------------当前判断主要诊断范围:" + zyzdfw);

      if (zyzdfw != null) {
        zyzdfwArr = zyzdfw.split(",");
      }

      boolean zdzfpd = zdzfpd(zzd, zdpd.getZzdbz(), zyzdfwArr, new String[]{});   //诊断字符判断

      System.out.println(i + "---------------------------当前主要诊断是否属于目标范围:" + zdzfpd);

      if (!zdzfpd) {  //如果当前主要诊断不属于判断范围且该判断不需要双向判断，则跳过
        if (zdpd.getIstwoway() == 1) {   //进行双向判断

          System.out.println("----------------------------双向判断");
          pdlb = zdpd.getPdlb();
          System.out.println("-----------------------------当前判断规则类别：" + pdlb);
          if (pdlb != null && !"".equals(pdlb)) {
            if ("校验结果".equals(pdlb)) {
              System.out.println("-----------------jyjgsxpd------------------");
              jyjgsxpd(qtZd, zzd, tipList, zdpd);
              continue;
            }
            if ("其他诊断".equals(pdlb)) {
              continue;
            }
            if ("入院病情".equals(pdlb)) {
              continue;
            }
          }

        }
      } else {         //当前主要诊断属于目标范围
        if ((zdpd.getLyfs() != null && !"".equals(zdpd.getLyfs())) ||
          (zdpd.getNotlyfs() != null && !"".equals(zdpd.getNotlyfs()))) {     //离院方式判断

          String lyfs = baSyjl.getLyfs() == null ? "" : baSyjl.getLyfs();    //当前病案出院方式

          Boolean lyfs_flag;   //

          if (zdpd.getNotlyfs() == null || "".equals(zdpd.getNotlyfs())) {
            lyfsfw = zdpd.getLyfs().split(",");
            System.out.println(i + "---------------------------属于当前判断离院方式范围:" + zdpd.getLyfs());
            lyfs_flag = Arrays.asList(lyfsfw).contains(lyfs);
          } else if (zdpd.getLyfs() == null || "".equals(zdpd.getLyfs())) {
            lyfsnfw = zdpd.getNotlyfs().split(",");
            System.out.println(i + "---------------------------不属于当前判断离院方式范围:" + zdpd.getNotlyfs());
            lyfs_flag = !Arrays.asList(lyfsnfw).contains(lyfs);
          } else {
            lyfs_flag = Arrays.asList(lyfsfw).contains(lyfs) && !Arrays.asList(lyfsnfw).contains(lyfs);
          }

          System.out.println(i + "---------------------------当前离院方式是否属于目标范围:" + lyfs_flag);

          if (!lyfs_flag) {
            continue;
          }
        }

        pdlb = zdpd.getPdlb();

        System.out.println("-----------------------------当前判断规则类别：" + pdlb);

        if (pdlb != null && !"".equals(pdlb)) {
          if ("其他诊断".equals(pdlb)) {
            System.out.println("-----------------qtzdpd------------------");
            qtzdpd(qtZd, zzd, tipList, zdpd);
            continue;
          }
          if ("校验结果".equals(pdlb)) {
            System.out.println("-----------------jyjgpd------------------");
            jyjgpd(qtZd, zzd, tipList, zdpd);
            continue;
          }
          if ("入院病情".equals(pdlb)) {
            System.out.println("-----------------rypqpd------------------");
            rypqpd(qtZd, zzd, tipList, zdpd);
            continue;
          }
        }
      }
    }
  }

  public void qtzdpd(List<BaBrzdxx> qtZd, BaBrzdxx zzd, List<Tip> tipList, Zdpd zdpd) {
    String[] qtzdfw = new String[]{};       //其他诊断判断范围
    String[] qtzdnfw = new String[]{};      //不属于其他诊断判断范围
    if (zdpd.getQtzd() != null && !"".equals(zdpd.getQtzd())) {
      System.out.println("--------------------------其他诊断符合范围：" + zdpd.getQtzd());
      qtzdfw = zdpd.getQtzd().split(",");
    }
    if (zdpd.getNotqtzd() != null && !"".equals(zdpd.getNotqtzd())) {
      System.out.println("--------------------------其他诊断不符合范围：" + zdpd.getNotqtzd());
      qtzdnfw = zdpd.getNotqtzd().split(",");
    }
    for (BaBrzdxx baBrzdxx : qtZd) {
      if (zdzfpd(baBrzdxx, zdpd.getQtzdbz(), qtzdfw, qtzdnfw)) {    //判断当前其他诊断是否符合要求
        System.out.println("--------------------------当前诊断符合条件：" + baBrzdxx.getJbbm());
        //是否提示
        if (zdpd.getIstip() == 1 || zdpd.getIstip() == 2) {
          tipList.add(new Tip("[" + zzd.getJbbm() + "]" + zdpd.getTip()));
        }
        if (zdpd.getIsreplace() == 1) {                          //找到满足条件的其他诊断是否与主要诊断进行更换
          qtZd.add(0, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq()));
          BeanUtils.copyProperties(baBrzdxx, zzd);
          qtZd.remove(qtZd.indexOf(baBrzdxx));
        }
        return;
      }
    }
    //没有诊断符合要求  判断是否提示
    if (zdpd.getIstip() == 0 || zdpd.getIstip() == 2) {
      tipList.add(new Tip("[" + zzd.getJbbm() + "]" + zdpd.getTip()));
    }
  }

  public boolean zdzfpd(BaBrzdxx baBrzdxx, Long pdbz, String[] fw, String[] nfw) {   //根据判断标准判断诊断是否满足条件
    if (fw.length == 0 && nfw.length == 0) {   //如果没有规定相关范围与不相关范围，则随便一个其他诊断都返回true
      return true;
    }
    if (pdbz == 1) {    //全字符匹配
      if (fw.length == 0) {     //如果只有不相关范围，判断不包含当前诊断就返回true
        if (nfw.length > 0) {
          return !Arrays.asList(nfw).contains(baBrzdxx.getJbbm());
        }
      }
      if (nfw.length == 0) {    //如果只有相关范围，判断包含当前诊断则返回true
        if (fw.length > 0) {
          return Arrays.asList(fw).contains(baBrzdxx.getJbbm());
        }
      }
      return Arrays.asList(fw).contains(baBrzdxx.getJbbm()) && !Arrays.asList(nfw).contains(baBrzdxx.getJbbm());
    } else {            //非全字符匹配，前几位字符匹配

      if (fw.length == 0) {
        if (nfw.length > 0) {
          for (String qtzdnfwStr : nfw) {
            if (equalStr(qtzdnfwStr, baBrzdxx.getJbbm())) {
              return false;
            }
          }
          return true;
        }
      }
      if (nfw.length == 0) {
        if (fw.length > 0) {
          for (String qtzdfwStr : fw) {
            if (equalStr(qtzdfwStr, baBrzdxx.getJbbm())) {
              return true;
            }
          }
          return false;
        }
      }

      int fwpd = -1;
      int nfwpd = -1;
      for (String qtzdfwStr : fw) {
        if (equalStr(qtzdfwStr, baBrzdxx.getJbbm())) {
          fwpd = 0;
        }
      }
      for (String qtzdnfwStr : nfw) {
        if (equalStr(qtzdnfwStr, baBrzdxx.getJbbm())) {
          nfwpd = 0;
        }
      }
      return (fwpd == 0 && nfwpd == -1);       //属于目标范围，且不属于非目标范围
    }
  }

  public void jyjgpd(List<BaBrzdxx> qtZd, BaBrzdxx zzd, List<Tip> tipList, Zdpd zdpd) {
    String jymc = zdpd.getJymc();           //要判断的检验项目
    String jyjg = zdpd.getJyjg();           //目标检验结果  要符合的范围

    System.out.println("--------------------------相关检验名称：" + jymc);
    System.out.println("--------------------------应该得到的结果：" + jyjg);

    String[] jymcArr = {};
    String[] jyjgArr = {};

    if (jymc != null && !"".equals(jymc)) {
      jymcArr = jymc.split(",");     //需要判断的检验项目名称数组
    } else {
      return;
    }

    if (jyjg != null && !"".equals(jyjg)) {
      jyjgArr = jyjg.split(",");    //检验结果目标范围数组
    } else {
      return;
    }

    if (jymcArr.length != jyjgArr.length) {
      System.out.println("检验项目名称及目标结果应该数量相同");
      return;
    }


    System.out.println("--------------------------要检查的项目数量：" + jymcArr.length);


    List<Jyxx> jyxxList = new ArrayList<>();
    int find = -1;  //是否进行过该检查


    //(0在目标数值之间[存在两个数值，用-隔开]，1大于[存在单个数值，大于目标数值]，2小于[存在单个数值，小于目标数值])
    System.out.println("--------------------------判断标准：" + zdpd.getJyjgbz());

    Long dtjbz = zdpd.getDtjbz();
    if (dtjbz == null || "".equals(dtjbz)) {
      dtjbz = 1L;   //如果为空，默认为”都“
    }

    for (int i = 0; i < jymcArr.length; i++) {
      jyxxList = jyxxService.selectJyxxList(new Jyxx(jzh, jymcArr[i]));   //病人是否有该检验
      if (jyxxList.size() > 0) {
        Jyxx jyxx = jyxxList.get(0);
        if (jyxx.getJyjg() == null || "".equals(jyxx.getJyjg())) {
          tipList.add(new Tip(jymc + "检验结果为空，不能将" + zzd.getZdmc() + "作为主要诊断"));
          return;
        }
        find = 0;
        if (zdpd.getJyjgbz() == 1) {        //校验结果需要大于目标范围
          if (jyxx.getJgbz() != null && !"".equals(jyxx.getJgbz())) {
            if ("↑".equals(jyxx.getJgbz())) {   //先根据检验记录标标志判断  符合条件：当前诊断没问题
              if (dtjbz == 0 || i == jymcArr.length - 1) {
                return;
              }
            }
          } else {              //检验标志为空时，通过判断规则中的目标范围判断
            if (formatJyjg(jyxx.getJyjg()) >= formatJyjg(jyjgArr[i])) {
              if (dtjbz == 0 || i == jymcArr.length - 1) {
                return;
              }
            }
          }
        } else if (zdpd.getJyjgbz() == 2) { //校验结果需要小于目标范围
          if (jyxx.getJgbz() != null && !"".equals(jyxx.getJgbz())) {
            if ("↓".equals(jyxx.getJgbz())) {
              if (dtjbz == 0 || i == jymcArr.length - 1) {
                return;
              }
            }
          } else {
            if (formatJyjg(jyxx.getJyjg()) <= formatJyjg(jyjgArr[i])) {
              if (dtjbz == 0 || i == jymcArr.length - 1) {
                return;
              }
            }
          }
        } else if (zdpd.getJyjgbz() == 0) {  //校验结果需要在目标范围之间
          String[] split = jyjgArr[i].split("-");       //范围之间用“-”相连
          if (formatJyjg(jyxx.getJyjg()) >= formatJyjg(split[0])
            && formatJyjg(jyxx.getJyjg()) <= formatJyjg(split[1])) {
            if (dtjbz == 0 || i == jymcArr.length - 1) {
              return;
            }
          }
        }
      }
    }


    if (find == -1) {    //病人没有进行该检验
      tipList.add(new Tip("没有进行[" + jymc + "]检查或检验，不能将" + zzd.getZdmc() + "作为主要诊断"));
      return;
    }
    tipList.add(new Tip(jymc + "检验结果不符，不能将" + zzd.getZdmc() + "作为主要诊断"));
  }

  public void jyjgsxpd(List<BaBrzdxx> qtZd, BaBrzdxx zzd, List<Tip> tipList, Zdpd zdpd) {
    String jymc = zdpd.getJymc();           //要判断的检验项目
    String jyjg = zdpd.getJyjg();           //目标检验结果  要符合的范围

    System.out.println("--------------------------相关检验名称：" + jymc);
    System.out.println("--------------------------应该得到的结果：" + jyjg);

    String[] jymcArr = {};
    String[] jyjgArr = {};

    if (jymc != null && !"".equals(jymc)) {
      jymcArr = jymc.split(",");     //需要判断的检验项目名称数组
    } else {
      return;
    }

    if (jyjg != null && !"".equals(jyjg)) {
      jyjgArr = jyjg.split(",");    //检验结果目标范围数组
    } else {
      return;
    }

    if (jymcArr.length != jyjgArr.length) {
      return;
    }

    List<Jyxx> jyxxList = new ArrayList<>();

    Long dtjbz = zdpd.getDtjbz();
    if (dtjbz == null || "".equals(dtjbz)) {
      dtjbz = 1L;   //如果为空，默认为”都“
    }

    for (int i = 0; i < jymcArr.length; i++) {
      jyxxList = jyxxService.selectJyxxList(new Jyxx(jzh, jymcArr[i]));   //病人是否有该检验
      if (jyxxList.size() > 0) {
        Jyxx jyxx = jyxxList.get(0);
        if (jyxx.getJyjg() == null || "".equals(jyxx.getJyjg())) {
          return;
        }
        if (zdpd.getJyjgbz() == 1) {        //校验结果需要大于目标范围
          if (jyxx.getJgbz() != null && !"".equals(jyxx.getJgbz())) {
            if ("↑".equals(jyxx.getJgbz())) {   //先根据检验记录标标志判断  符合条件：当前诊断没问题
              if (dtjbz == 0 || i == jymcArr.length - 1) {
                tipList.add(new Tip(zdpd.getJymc() + "检验结果为：" + jyxx.getJyjg() + "，可将" + zdpd.getZzd() + "添加到诊断"));
                return;
              }
              continue;
            }
          } else {              //检验标志为空时，通过判断规则中的目标范围判断
            if (formatJyjg(jyxx.getJyjg()) >= formatJyjg(jyjgArr[i])) {
              if (dtjbz == 0 || i == jymcArr.length - 1) {
                tipList.add(new Tip(zdpd.getJymc() + "检验结果为：" + jyxx.getJyjg() + "，可将" + zdpd.getZzd() + "添加到诊断"));
                return;
              }
              continue;
            }
          }
          return;
        } else if (zdpd.getJyjgbz() == 2) { //校验结果需要小于目标范围
          if (jyxx.getJgbz() != null && !"".equals(jyxx.getJgbz())) {
            if ("↓".equals(jyxx.getJgbz())) {
              if (dtjbz == 0 || i == jymcArr.length - 1) {
                tipList.add(new Tip(zdpd.getJymc() + "检验结果为：" + jyxx.getJyjg() + "，可将" + zdpd.getZzd() + "添加到诊断"));
                return;
              }
              continue;
            }
          } else {
            if (formatJyjg(jyxx.getJyjg()) <= formatJyjg(jyjgArr[i])) {
              if (dtjbz == 0 || i == jymcArr.length - 1) {
                tipList.add(new Tip(zdpd.getJymc() + "检验结果为：" + jyxx.getJyjg() + "，可将" + zdpd.getZzd() + "添加到诊断"));
                return;
              }
              continue;
            }
          }
          return;
        } else if (zdpd.getJyjgbz() == 0) {  //校验结果需要在目标范围之间
          String[] split = jyjgArr[i].split("-");       //范围之间用“-”相连
          if (formatJyjg(jyxx.getJyjg()) >= formatJyjg(split[0])
            && formatJyjg(jyxx.getJyjg()) <= formatJyjg(split[1])) {
            if (dtjbz == 0 || i == jymcArr.length - 1) {
              tipList.add(new Tip(zdpd.getJymc() + "检验结果为：" + jyxx.getJyjg() + "，可将" + zdpd.getZzd() + "添加到诊断"));
              return;
            }
            continue;
          }
          return;
        }
      }
    }

  }

  public double formatJyjg(String jyjg) {
    return Double.parseDouble(jyjg.replaceAll("[^0-9.]", ""));
  }

  public void rypqpd(List<BaBrzdxx> qtZd, BaBrzdxx zzd, List<Tip> tipList, Zdpd zdpd) {
    String rybq = zdpd.getRybq();  //目标入院病情
    if (rybq == null || "".equals(rybq)) {
      return;
    }
    System.out.println("---------------------------------目标入院病情：" + rybq);
    if (zdpd.getRybqbz() == 1) {   //判断主要诊断的入院病情
      System.out.println("---------------------------------判断主要诊断入院病情");
      if (!rybq.equals(zzd.getRybq())) {   //当前主要诊断入院病情不符合规则
        if (zdpd.getIstip() == 1) {        //是否提示
          tipList.add(new Tip(zzd.getJbbm() + "作为主诊断时，其入院病情需要为：" + zdpd.getRybq()));
        }
        if (zdpd.getIsreplace() == 1) {    //判断是否进行更换主要诊断
          BaBrzdxx baBrzdxx = qtZd.get(0);
          qtZd.remove(0);
          qtZd.add(0, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq()));
          BeanUtils.copyProperties(baBrzdxx, zzd);
        }
      }
    } else if (zdpd.getRybqbz() == 0) {   //判断其他诊断的入院病情
      System.out.println("---------------------------------判断其他诊断入院病情");
      int rybq_flag = 0;
      if (zdpd.getDtjbz() == null) {
        zdpd.setDtjbz(1L);
      }
      if (zdpd.getDtjbz() == 1) {  //所有其他诊断都要满足该条件
        for (int i = 0; i < qtZd.size(); i++) {
          if (!rybq.equals(qtZd.get(i).getRybq())) {
            rybq_flag = -1;
            break;
          }
        }
      } else if (zdpd.getDtjbz() == 0) {  //只要有一个其他诊断满足条件即可
        for (int i = 0; i < qtZd.size(); i++) {
          if (rybq.equals(qtZd.get(i).getRybq())) {
            rybq_flag = -1;
          }
        }
      }
      if ((zdpd.getDtjbz() == 1 && rybq_flag == -1) || zdpd.getDtjbz() == 0 && rybq_flag == 0) {   //不符合
        if (zdpd.getIstip() == 1) {        //是否提示
          tipList.add(new Tip(zzd.getJbbm() + "作为主诊断时，其他诊断入院病情需要为：" + zdpd.getRybq()));
        }
        if (zdpd.getIsreplace() == 1) {    //判断是否进行更换主要诊断
          BaBrzdxx baBrzdxx = qtZd.get(0);
          qtZd.remove(0);
          qtZd.add(0, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq()));
          BeanUtils.copyProperties(baBrzdxx, zzd);
        }
      }
    }
  }

  //是否可以成为主诊断  [主诊断判断]
  public void zzdpd(List<BaBrzdxx> qtZd, BaBrzdxx zzd, List<Tip> tipList) {
    String jbbm = zzd.getJbbm();
    BBasyCheckBz bBasyCheckBz = new BBasyCheckBz();
    bBasyCheckBz.setErrtype("notzzd");
    List<BBasyCheckBz> notzzd_list = basyCheckBzService.selectBBasyCheckBzList(bBasyCheckBz);
    for (BBasyCheckBz basyCheckBz : notzzd_list) {
      if (basyCheckBz.getComments().equals(jbbm)) {
        for (int i = 0; i < qtZd.size(); i++) {
          int flag = 0;
          for (int i1 = 0; i1 < notzzd_list.size(); i1++) {
            if (notzzd_list.get(i1).getComments().equals(qtZd.get(i).getJbbm())) {
              flag = 1;
            }
          }
          if (flag == 0) {
            tipList.add(new Tip("诊断" + zzd.getJbbm() + "不能作主诊断"));
            BaBrzdxx brzdxx = qtZd.get(i);
            qtZd.remove(i);
            qtZd.add(0, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq()));
            BeanUtils.copyProperties(brzdxx, zzd);
//            zzd = brzdxx;
            return;
          }
        }
      }
    }
  }


  public void czdpd(List<BaBrzdxx> qtZd, BaBrzdxx zzd, String brid, String zyid) {
    String cjbbm = baBrzdxxService.seelctCzd(new BaSyjl(brid, zyid));
    if (cjbbm != null && !"".equals(cjbbm)) {

      if (zzd.getJbbm().trim().equals(cjbbm.trim())) {
        return;
      }


      for (int i = 0; i < qtZd.size(); i++) {
        BaBrzdxx brzdxx = qtZd.get(i);
        if (qtZd.get(i).getJbbm().trim().equals(cjbbm.trim())) {
          qtZd.add(0, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq()));
          BeanUtils.copyProperties(brzdxx, zzd);
//          zzd = brzdxx;
          qtZd.remove(i);
          return;
        }
      }

      Bzml bzml = new Bzml();
      bzml.setBzbm(cjbbm);
      List<Bzml> bzmls = bzmlService.selectBzmlList(bzml);

      if (bzmls.size() > 0) {
        qtZd.add(0, new BaBrzdxx(zzd.getZdmc(), zzd.getJbbm(), zzd.getRybq()));
        String cbzmc = bzmls.get(0).getBzmc();
        zzd.setJbbm(cjbbm);
        zzd.setZdmc(cbzmc);
        zzd.setRybq("");
      }

    }

  }

  public List<BaBrzdxx> zdhbpd(List<BaBrzdxx> qtZd) {
    List<BaBrzdxx> lsqtzd;
    String hbpdStr = "";
    for (int i = 0; i < qtZd.size(); i++) {
      hbpdStr = hbpdStr + qtZd.get(i).getJbbm() + ",";
    }
    if (hbpdStr.length() > 1) {
      hbpdStr = hbpdStr.substring(0, hbpdStr.length() - 1);
    }
    String dealhb = baxyController.dealhb(hbpdStr);
    System.out.println("dealhb:" + dealhb);
    lsqtzd = qtZd;
    qtZd.clear();
    int hb_flag = 0;
    int num_flag = 0;
    BaBrzdxx brzdxx1 = new BaBrzdxx();
    String[] hbzds = dealhb.split(",");
    for (String s : hbzds) {
      hb_flag = 0;
      for (int i = 0; i < lsqtzd.size(); i++) {
        if (lsqtzd.get(i).getJbbm().equals(s.trim())) {
          hb_flag = 1;
          qtZd.add(new BaBrzdxx(lsqtzd.get(i).getZdmc(), s, lsqtzd.get(i).getRybq()));
        }
        num_flag++;
        if (num_flag > 200) {
          break;
        }
      }
      if (hb_flag == 1) continue;
      s = s.trim();
      brzdxx1.setJbbm(s);
      List<BaBrzdxx> list = baBrzdxxService.selectLcZdByJbbm(brzdxx1);
      if (list.size() > 0) {
        qtZd.add(new BaBrzdxx(list.get(0).getZdmc(), s, ""));
      } else {
        list = baBrzdxxService.selectYbzdByJbbm(brzdxx1);
        if (list.size() > 0) {
          qtZd.add(new BaBrzdxx(list.get(0).getZdmc(), s, ""));
        }
      }
    }
    return qtZd;
  }


  public boolean equalStr(String str1, String str2) {
    if (str1 == null || str2 == null) {
      return false;
    }
    if (str1.length() > str2.length()) {
      return false;
    }
    if (str1.length() == str2.length()) {
      if (str1.equals(str2)) {
        return true;
      } else
        return false;
    }
    if (str1.length() < str2.length()) {
      String newStr2 = str2.substring(0, str1.length());
      if (str1.equals(newStr2)) {
        return true;
      }
    }
    return false;
  }


  @RequestMapping("/znbmtj")
  @ResponseBody
  public String znbm(@RequestParam(value = "brbs") String brbs) {
    if (brbs == null || "".equals(brbs) || "".equals(brbs.trim())) {
      return "请输入正确的病人标识";
    }

    BaSyjl syjl = baSyjlService.selectBaSyjlByBrbs(brbs);

    if (syjl == null) {
      return "没有找到患者病案信息";
    }

    TableDataInfo znbm = znbm(syjl.getBrid(), syjl.getZyid(), "2");

    if (znbm.getRows().size() == 1) {
      return znbm.getRows().get(0).toString();
    }

    String resultStr = "";

    resultStr = resultStr + "[诊断]：";
    // TODO: 2024/1/4 此处是否需要使用json转换，亦或是遍历TableDataInfo中的rows进行类型判断
    List<List> resultList = znbm.getRows() != null ? (List<List>) znbm.getRows() : new ArrayList<>();

    List<BaBrzdxx> brzdxxList = resultList.get(0);
    List<BaSsjl> baSsjlList = resultList.get(1);

    for (int i = 0; i < brzdxxList.size(); i++) {
      resultStr = resultStr + brzdxxList.get(i).getZdmc() + "," + brzdxxList.get(i).getJbbm() + "," + brzdxxList.get(i).getZdcx() + " | ";
    }
    if (baSsjlList.size() > 0) {
      resultStr = resultStr + "         [手术]：";
      for (int i = 0; i < baSsjlList.size(); i++) {
        resultStr = resultStr + baSsjlList.get(i).getSsmc() + "," + baSsjlList.get(i).getSsbm() + "," + baSsjlList.get(i).getSscx() + " | ";
      }
    }
    return resultStr;
  }

  @RequestMapping("/znbmAll")
  public void znbmAll() {
    for (BaSyjl baSyjl1 : baSyjlService.selectBaSyjlList(new BaSyjl())) {
      znbm(baSyjl1.getBrid(), baSyjl1.getZyid(), "2");
    }

  }

  String pattern = "([\\u4e00-\\u9fa5-.，。|、；：？！+*“”‘’（）《》【】{}……—\\uFF10-\\uFF19]+|[0-9]+)";
  Pattern p = Pattern.compile(pattern);
  Matcher m = null;
  StringBuilder result = null;

  private String formatZdxx(String zdxx) {
    if (zdxx == null) {
      zdxx = "";
    }
    if (zdxx.indexOf("</c>") > 0 & zdxx.indexOf("<c>") > 0) {


      if (zdxx != null && !"".equals(zdxx.trim())) {
        m = p.matcher(((zdxx + ">").replaceAll(">", ">；")).replaceAll("<[^<>]+>", ""));
        result = new StringBuilder();
        while (m.find()) {
          result.append(m.group());
        }
        zdxx = result.toString().replaceAll("([\\p{P}])\\1+", "$1").replaceAll("[<>;]", "").replaceAll("；+", "；").replaceFirst("^[\\p{P}\\p{Z}]+", "")
          .replaceAll("（+；", "（").replaceAll("；+）", "）").replaceAll("([-.，。|、；：？！]+)", "$1").replaceAll("([-.，。|、；：？！]){2,}", "$1");
        ;
        if (zdxx.length() >= 1) {
          if (zdxx.substring(0, 1).matches("[\\p{P}；]")) {
            zdxx = zdxx.substring(1);
          }
        }
        zdxx = zdxx.replaceFirst("出；院；医；嘱.*", "");
      }
    }
    int atPosition = zdxx.indexOf("西医诊断");
    if (atPosition != -1) {
      zdxx = zdxx.substring(atPosition + 1);
    }
    atPosition = zdxx.indexOf("西医初步诊断");
    if (atPosition != -1) {
      zdxx = zdxx.substring(atPosition + 1);
    }

    return zdxx;
  }

  @RequestMapping("/getBrfzbmxx")
  public Map<String, Object> getBrfzbmxxMap(@RequestParam(value = "brbs") String brbs) {
    Map<String, Object> resultMap = new HashMap<>();
    if (brbs == null || "".equals(brbs) || "".equals(brbs.trim())) {
      resultMap.put("msg", "请输入正确的病人标识");
      return resultMap;
    }
    BaSyjl syjl = baSyjlService.selectBaSyjlByBrbs(brbs);
    if (syjl == null) {
      resultMap.put("msg", "没有找到患者病案信息");
      return resultMap;
    }
    TableDataInfo znbm = znbm(syjl.getBrid(), syjl.getZyid(), "2");


    if (znbm.getRows().size() == 1) {
      resultMap.put("msg", znbm.getRows().get(0).toString());
      return resultMap;
    }


    //DRG分组信息
    DrgFzResult drgFzResult = drgFzController.basyfz(syjl);

    //DRG分组信息
    String drgfzStr = "";
    drgfzStr += "DRG编号：" + drgFzResult.getDrgbh();
    drgfzStr += "||DRG名称：" + drgFzResult.getDrgmc();
    drgfzStr += "||标杆费用：" + drgFzResult.getZfbz();
    drgfzStr += "||CMI：" + drgFzResult.getZfqz();
    drgfzStr += "||标杆床日：" + drgFzResult.getPjdays();
    drgfzStr += "||总费用：" + syjl.getZfy();
    drgfzStr += "||住院天数：" + syjl.getSjzyts();
    resultMap.put("DRG分组信息", drgfzStr);

    List<List> resultList = znbm.getRows() != null ? (List<List>) znbm.getRows() : new ArrayList<>();
    //推荐诊断
    List<BaBrzdxx> brzdxxList = resultList.get(0);
    //推荐手术
    List<BaSsjl> baSsjlList = resultList.get(1);

    //推荐诊断信息
    String tjzdxx = "";
    for (int i = 0; i < brzdxxList.size(); i++) {
      tjzdxx += brzdxxList.get(i).getJbbm() + "[" + brzdxxList.get(i).getZdmc() + "]||";
    }
    resultMap.put("推荐诊断信息", tjzdxx.length() > 2 ? tjzdxx.substring(0, tjzdxx.length() - 2) : tjzdxx);

    //推荐手术信息
    String tjssxx = "";
    for (int i = 0; i < baSsjlList.size(); i++) {
      tjssxx += baSsjlList.get(i).getSsbm() + "[" + baSsjlList.get(i).getSsmc() + "]||";
    }
    resultMap.put("推荐手术信息", tjssxx.length() > 2 ? tjssxx.substring(0, tjssxx.length() - 2) : tjssxx);

    //漏掉的诊断
    String ylzdxx = "";
    YbgkWgjl wgjl = new YbgkWgjl();
    wgjl.setZyh(syjl.getBah());
    wgjl.setJzh(syjl.getJzh());
    wgjl.setJktype("xzbz");
    //限制病种违规记录
    List<YbgkWgjl> listwgjls = wgjlService.selectYbgkWgjlListByHospital(wgjl);
    String ls_qsdzd;
    for (Iterator iterator = listwgjls.iterator(); iterator.hasNext(); ) {
      YbgkWgjl wgjl2 = (YbgkWgjl) iterator.next();
      if (wgjl2 == null) {
        continue;
      }
      if (wgjl2.getJklog() != null) {
        ls_qsdzd = "";
        ls_qsdzd = wgjl2.getJklog();
        ls_qsdzd = ls_qsdzd.replace("扣分:0", "");
        ls_qsdzd = ls_qsdzd.replace("限[", "适应症为[");
        ls_qsdzd = ls_qsdzd.replace("[出院]", "");
        int ll_have = 0;
        if (brzdxxList.size() > 0) {
          for (int ii = 0; ii < brzdxxList.size(); ii++) {
            if (ls_qsdzd.indexOf(brzdxxList.get(ii).getZdmc()) > -1) {
              ll_have = 1;
            }
            if (ll_have == 0) {
              if (ls_qsdzd.indexOf("适应症为[") > -1) {
                if (ls_qsdzd.indexOf("]", ls_qsdzd.indexOf("适应症为[")) > -1) {
                  String xzzd = ls_qsdzd.substring(ls_qsdzd.indexOf("适应症为[") + 5, ls_qsdzd.indexOf("]", ls_qsdzd.indexOf("适应症为[")));
                  if (xzzd.indexOf(",") > -1) {
                    String[] xzzdArr = xzzd.split(",");
                    for (String zd : xzzdArr) {
                      if (brzdxxList.get(ii).getZdmc().indexOf(zd) > -1) {
                        ll_have = 1;
                        break;
                      }
                    }
                  } else {
                    if (brzdxxList.get(ii).getZdmc().indexOf(xzzd) > -1) {
                      ll_have = 1;
                    }
                  }
                }
              }
            }
            if (ll_have == 1) {
              break;
            }
          }
        }
        if (ll_have == 0) {   //漏掉的诊断
          if (ls_qsdzd.indexOf("适应症为[") > -1) {
            if (ls_qsdzd.indexOf("]", ls_qsdzd.indexOf("适应症为[")) > -1) {
              String xzzd = ls_qsdzd.substring(ls_qsdzd.indexOf("适应症为[") + 5, ls_qsdzd.indexOf("]", ls_qsdzd.indexOf("适应症为[")));
              ylzdxx += "费用名称：" + wgjl2.getFymName() + " 费用:" + wgjl2.getJe() + " 诊断名称：" + xzzd + "||";
            }
          } else {
            ylzdxx += "费用名称：" + wgjl2.getFymName() + " 费用:" + wgjl2.getJe() + " 诊断名称：" + ls_qsdzd + "||";
          }
        }
      }
    }
    resultMap.put("漏掉的诊断信息", ylzdxx.length() > 2 ? ylzdxx.substring(0, ylzdxx.length() - 2) : ylzdxx);

    // 提取漏掉的手术
    String ylssxx = "";
    Fyxx fyxx = new Fyxx();
    fyxx.setBrid(syjl.getBrid());
    fyxx.setZyid(syjl.getZyid());
    fyxx.setXmbm("1");
    //手术费用信息
    List<Fyxx> listfyxxs = fyxxService.selectssfyxx(fyxx);
    for (Iterator iterator = listfyxxs.iterator(); iterator.hasNext(); ) {
      Fyxx fyxx2 = (Fyxx) iterator.next();
      if (fyxx2.getFykmname() == null) {
        //漏掉的手术信息
        Fyxx fyxx1 = new Fyxx();
        fyxx1.setXmbm(fyxx2.getXmbm());
        List<BaSsjl> baSsjls = baSsjlService.selectFydyssxx(fyxx1);
        String fydyss = "";
        for (int i = 0; i < baSsjls.size(); i++) {
          if (i == baSsjls.size() - 1) {
            fydyss += baSsjls.get(i).getSsbm() + "[" + baSsjls.get(i).getSsmc() + "]";
          } else {
            fydyss += baSsjls.get(i).getSsbm() + "[" + baSsjls.get(i).getSsmc() + "],";
          }
        }
        ylssxx += "费用名称：" + fyxx2.getXmmc() + "  费用：" + fyxx2.getJe() + " 手术名称：" + fydyss + "||";
      }
    }
    resultMap.put("漏掉的手术信息", ylssxx.length() > 2 ? ylssxx.substring(0, ylssxx.length() - 2) : ylssxx);


    return resultMap;

  }

  @RequestMapping("/batchCoding")
  public void batchCoding() {
    List<BaSyjl> baSyjls = baSyjlService.selectBaSyjlList(new BaSyjl());
    baSyjls.forEach(item -> znbm(item.getBrid(), item.getZyid(), "2"));
  }


  /**
   * 诊断去重
   *
   * @param baBrzdxxList
   * @return
   */
  public List<BaBrzdxx> toUniqueDiagList(List<BaBrzdxx> baBrzdxxList) {
    List<BaBrzdxx> uniqueList = new ArrayList<>(baBrzdxxList.size());
    Set<String> diagjbbmSet = new HashSet<>();
    Set<String> diagZdmcSet = new HashSet<>();
    for (BaBrzdxx item : baBrzdxxList) {
      String diagjbbm = item.getJbbm();
      String diagZdmc = item.getZdmc();
      if (!diagjbbmSet.contains(diagjbbm) && !diagZdmcSet.contains(diagZdmc)) {
        diagjbbmSet.add(diagjbbm);
        diagZdmcSet.add(diagZdmc);
        uniqueList.add(item);
      }
    }
    return uniqueList;
  }

  /**
   * 手术去重
   *
   * @param baSsjlList
   * @return
   */
  public List<BaSsjl> toUniqueSurgList(List<BaSsjl> baSsjlList) {
    List<BaSsjl> uniqueList = new ArrayList<>(baSsjlList.size());
    Set<String> surgSsbmSet = new HashSet<>();
    Set<String> surgSsmcSet = new HashSet<>();
    for (BaSsjl item : baSsjlList) {
      String surgSsbm = item.getSsbm();
      String surgSsmc = item.getSsmc();
      if (!surgSsbmSet.contains(surgSsbm) && !surgSsmcSet.contains(surgSsmc)) {
        surgSsbmSet.add(surgSsbm);
        surgSsmcSet.add(surgSsmc);
        uniqueList.add(item);
      }
    }
    return uniqueList;
  }


  /**
   * 保存推荐的诊断和手术信息
   */
  public void saveDiagSurgInfo(List<BaBrzdxx> baBrzdxxList, List<BaSsjl> baSsjlList, String brid, String zyid, String yhbs) {
    JSONArray zdxxJsonArray = new JSONArray();
    for (int i = 0; i < baBrzdxxList.size(); i++) {
      zdxxJsonArray.add(new BaBrzdxx(baBrzdxxList.get(i).getZdmc(), baBrzdxxList.get(i).getJbbm(), baBrzdxxList.get(i).getRybq(), i + 1));
    }
    JSONArray ssxxJsonArray = new JSONArray();
    for (int i = 0; i < baSsjlList.size(); i++) {
      ssxxJsonArray.add(new BaSsjl(baSsjlList.get(i).getSsmc(), baSsjlList.get(i).getSsbm(), baSsjlList.get(i).getSscx()));
    }
    yfzController.save(zdxxJsonArray.toJSONString(), ssxxJsonArray.toJSONString(), brid, zyid, yhbs);
  }


  /**
   * 设置诊断类型MCC/CC
   */
  public List<BaBrzdxx> setDiagType(List<BaBrzdxx> baBrzdxxList) {
    List<BaBrzdxx> zdlxList = null;
    for (int i = 0; i < baBrzdxxList.size(); i++) {
      zdlxList = baBrzdxxService.selectBzType(new BaBrzdxx(baBrzdxxList.get(i).getZdmc(), baBrzdxxList.get(i).getJbbm(), baBrzdxxList.get(i).getRybq()));
      if (zdlxList.size() > 0) {
        baBrzdxxList.get(i).setType(zdlxList.get(0).getType());
      }
    }
    return baBrzdxxList;
  }


}
