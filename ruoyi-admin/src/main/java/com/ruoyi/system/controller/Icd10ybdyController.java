package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.Icd10ybdy;
import com.ruoyi.system.domain.vo.CompareIcdHisVo;
import com.ruoyi.system.domain.vo.IcdMaintainQueryVo;
import com.ruoyi.system.domain.vo.IcdMaintainVo;
import com.ruoyi.system.domain.vo.IcdUpdateVo;
import com.ruoyi.system.service.IIcd10ybdyService;
import com.ruoyi.system.service.IIcd9ybdyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * icd10ybdyController
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@RestController
@RequestMapping("/system/icd10ybdy")
public class Icd10ybdyController extends BaseController {
  @Autowired
  private IIcd10ybdyService icd10ybdyService;

  @Autowired
  private IIcd9ybdyService icd9ybdyService;


  @Anonymous
  @RequestMapping("/compareIcdHis")
  public TableDataInfo compareIcdHis(CompareIcdHisVo compareIcdHisVo) {
    String type = compareIcdHisVo.getType();
    if (StringUtils.isBlank(type)) {
      throw new ServiceException("请输入类型");
    }

    List<CompareIcdHisVo> list;
    if ("1".equals(type)) {
      list = icd10ybdyService.compareIcd10His(compareIcdHisVo);
    } else if ("2".equals(type)) {
      list = icd9ybdyService.compareIcd9His(compareIcdHisVo);
    } else {
      list = new ArrayList<>();
    }
    return getDataTable(list);
  }

  @PostMapping("/exportCompareIcdHis")
  public void exportCompareIcdHis(HttpServletResponse response, CompareIcdHisVo compareIcdHisVo) {
    String type = compareIcdHisVo.getType();
    List<CompareIcdHisVo> list;
    if ("1".equals(type)) {
      list = icd10ybdyService.compareIcd10His(compareIcdHisVo);
    } else if ("2".equals(type)) {
      list = icd9ybdyService.compareIcd9His(compareIcdHisVo);
    } else {
      list = new ArrayList<>();
    }
    ExcelUtil<CompareIcdHisVo> util = new ExcelUtil<CompareIcdHisVo>(CompareIcdHisVo.class);
    util.exportExcel(response, list, "ICD-HIS医保码对照");
  }


  /**
   * 查询icd10ybdy列表
   */
  @PreAuthorize("@ss.hasPermi('system:icd10ybdy:list')")
  @GetMapping("/list")
  public TableDataInfo list(Icd10ybdy icd10ybdy) {
    startPage();
    List<Icd10ybdy> list = icd10ybdyService.selectIcd10ybdyList(icd10ybdy);
    return getDataTable(list);
  }

  @PostMapping("/icdMaintainList")
  public TableDataInfo icdMaintainList(@RequestBody IcdMaintainQueryVo queryVo) {
    startPage();
    if (queryVo.getType() == null) {
      throw new ServiceException("类型错误");
    }
    List<IcdMaintainVo> list;
    if (queryVo.getType() == 1) {
      list = icd10ybdyService.selectIcdList(queryVo);
    } else if (queryVo.getType() == 2) {
      list = icd9ybdyService.selectIcd9List(queryVo);
    } else {
      throw new ServiceException("类型错误");
    }

    return getDataTable(list);
  }

  @PostMapping("/icdUpdate")
  public AjaxResult icdUpdate(@RequestBody IcdUpdateVo vo) {

    if (vo.getType() == 1) {
      icd10ybdyService.updateIcdInfo(vo);
    } else {
      icd9ybdyService.updateIcd9Info(vo);
    }

    return success();
  }

  @PostMapping("/icdAdd")
  public AjaxResult icdAdd(@RequestBody IcdUpdateVo vo) {
    if (vo.getType() == 1) {
      icd10ybdyService.icdAdd(vo);
    } else {
      icd9ybdyService.icdAdd(vo);
    }
    return success();
  }

  /**
   * 导出icd10ybdy列表
   */
  @PreAuthorize("@ss.hasPermi('system:icd10ybdy:export')")
  @Log(title = "icd10ybdy", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, Icd10ybdy icd10ybdy) {
    List<Icd10ybdy> list = icd10ybdyService.selectIcd10ybdyList(icd10ybdy);
    ExcelUtil<Icd10ybdy> util = new ExcelUtil<Icd10ybdy>(Icd10ybdy.class);
    util.exportExcel(response, list, "icd10ybdy数据");
  }

  /**
   * 获取icd10ybdy详细信息
   */
  @PreAuthorize("@ss.hasPermi('system:icd10ybdy:query')")
  @GetMapping(value = "/{bzbm}")
  public AjaxResult getInfo(@PathVariable("bzbm") String bzbm) {
    return success(icd10ybdyService.selectIcd10ybdyByBzbm(bzbm));
  }

  /**
   * 新增icd10ybdy
   */
  @PreAuthorize("@ss.hasPermi('system:icd10ybdy:add')")
  @Log(title = "icd10ybdy", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody Icd10ybdy icd10ybdy) {
    return toAjax(icd10ybdyService.insertIcd10ybdy(icd10ybdy));
  }

  /**
   * 修改icd10ybdy
   */
  @PreAuthorize("@ss.hasPermi('system:icd10ybdy:edit')")
  @Log(title = "icd10ybdy", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody Icd10ybdy icd10ybdy) {
    return toAjax(icd10ybdyService.updateIcd10ybdy(icd10ybdy));
  }

  /**
   * 删除icd10ybdy
   */
  @PreAuthorize("@ss.hasPermi('system:icd10ybdy:remove')")
  @Log(title = "icd10ybdy", businessType = BusinessType.DELETE)
  @DeleteMapping("/{bzbms}")
  public AjaxResult remove(@PathVariable String[] bzbms) {
    return toAjax(icd10ybdyService.deleteIcd10ybdyByBzbms(bzbms));
  }
}
