package com.ruoyi.system.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.http.HttpResponse;
import com.ruoyi.system.domain.vo.SettleInfoDetail;
import com.ruoyi.system.domain.vo.SettleInfoDetailQuery;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DrgXmffMx;
import com.ruoyi.system.service.IDrgXmffMxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目付费明细Controller
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
@Anonymous
@RestController
@RequestMapping("/system/xmffMx")
public class DrgXmffMxController extends BaseController
{
    @Autowired
    private IDrgXmffMxService drgXmffMxService;

    /**
     * 计算当月极高费用信息
     */
    @GetMapping("/compute/cost")
    public AjaxResult computeCostInfo(){
        Map<String, Long> map = drgXmffMxService.computeCostInfo();
        return AjaxResult.success(map);
    }
  
    @GetMapping("/cost/compute/{drgbh}")
    public AjaxResult costCompute(@PathVariable("drgbh") String drgbh){
        String info = drgXmffMxService.costCompute(drgbh);

        return success().put("info", info);
    }

    /**
     * 查询该病人付费项目明细
     */
    @RequestMapping("/info/detail")
    public TableDataInfo infoDetail(SettleInfoDetailQuery query){

        startPage();
        List<SettleInfoDetail> list = drgXmffMxService.getSettleInfoDetail(query);

        return getDataTable(list);
    }

    /**
     * 导出病人付费项目明细
     */
    @RequestMapping("/info/detail/export")
    public void exportInfoDetail(HttpServletResponse response, SettleInfoDetailQuery query){
        List<SettleInfoDetail> list = drgXmffMxService.getSettleInfoDetail(query);
        ExcelUtil<SettleInfoDetail> util = new ExcelUtil<SettleInfoDetail>(SettleInfoDetail.class);
        util.exportExcel(response, list, "病人项目付费明细");
    }

    /**
     * 查询项目付费明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:xmffMx:list')")
    @GetMapping("/list")
    public TableDataInfo list(DrgXmffMx drgXmffMx)
    {
        startPage();
        List<DrgXmffMx> list = drgXmffMxService.selectDrgXmffMxList(drgXmffMx);
        return getDataTable(list);
    }

    /**
     * 导出项目付费明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:xmffMx:export')")
    @Log(title = "项目付费明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DrgXmffMx drgXmffMx)
    {
        List<DrgXmffMx> list = drgXmffMxService.selectDrgXmffMxList(drgXmffMx);
        ExcelUtil<DrgXmffMx> util = new ExcelUtil<DrgXmffMx>(DrgXmffMx.class);
        util.exportExcel(response, list, "项目付费明细数据");
    }

    /**
     * 获取项目付费明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:xmffMx:query')")
    @GetMapping(value = "/{brbs}")
    public AjaxResult getInfo(@PathVariable("brbs") String brbs)
    {
        return success(drgXmffMxService.selectDrgXmffMxByBrbs(brbs));
    }

    /**
     * 新增项目付费明细
     */
    @PreAuthorize("@ss.hasPermi('system:xmffMx:add')")
    @Log(title = "项目付费明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DrgXmffMx drgXmffMx)
    {
        return toAjax(drgXmffMxService.insertDrgXmffMx(drgXmffMx));
    }

    /**
     * 修改项目付费明细
     */
    @PreAuthorize("@ss.hasPermi('system:xmffMx:edit')")
    @Log(title = "项目付费明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DrgXmffMx drgXmffMx)
    {
        return toAjax(drgXmffMxService.updateDrgXmffMx(drgXmffMx));
    }

    /**
     * 删除项目付费明细
     */
    @PreAuthorize("@ss.hasPermi('system:xmffMx:remove')")
    @Log(title = "项目付费明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{brbss}")
    public AjaxResult remove(@PathVariable String[] brbss)
    {
        return toAjax(drgXmffMxService.deleteDrgXmffMxByBrbss(brbss));
    }
}
