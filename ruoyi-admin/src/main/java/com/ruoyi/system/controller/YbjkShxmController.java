package com.ruoyi.system.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbjkShxm;
import com.ruoyi.system.service.IYbjkShxmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 审核项目Controller
 *
 * <AUTHOR>
 * @date 2023-07-29
 */
@RestController
@RequestMapping("/gksz/shxm")
public class YbjkShxmController extends BaseController
{
    @Autowired
    private IYbjkShxmService ybjkShxmService;

    /**
     * 查询审核项目列表
     */
    @GetMapping("/list")
    public TableDataInfo list(YbjkShxm ybjkShxm)
    {
        startPage();
        List<YbjkShxm> list = ybjkShxmService.selectYbjkShxmList(ybjkShxm);
        return getDataTable(list);
    }

    /**
     * 导出审核项目列表
     */
    @Log(title = "审核项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbjkShxm ybjkShxm)
    {
        List<YbjkShxm> list = ybjkShxmService.selectYbjkShxmList(ybjkShxm);
        ExcelUtil<YbjkShxm> util = new ExcelUtil<YbjkShxm>(YbjkShxm.class);
        util.exportExcel(response, list, "审核项目数据");
    }

    /**
     * 获取审核项目详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(ybjkShxmService.selectYbjkShxmById(id));
    }

    /**
     * 新增审核项目
     */
    @Log(title = "审核项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbjkShxm ybjkShxm)
    {
      if (StrUtil.isEmpty(ybjkShxm.getId())) {
        ybjkShxm.setId(IdUtil.simpleUUID());
      }
        return toAjax(ybjkShxmService.insertYbjkShxm(ybjkShxm));
    }

    /**
     * 修改审核项目
     */
    @Log(title = "审核项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbjkShxm ybjkShxm)
    {
        return toAjax(ybjkShxmService.updateYbjkShxm(ybjkShxm));
    }

    /**
     * 删除审核项目
     */
    @Log(title = "审核项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(ybjkShxmService.deleteYbjkShxmByIds(ids));
    }
}
