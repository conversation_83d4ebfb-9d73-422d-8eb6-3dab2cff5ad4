package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.YbgkKkmx;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DrgTldy;
import com.ruoyi.system.service.IDrgTldyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * DRG特例单议Controller
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@RestController
@RequestMapping("/jsxx/tldy")
public class DrgTldyController extends BaseController
{
    @Autowired
    private IDrgTldyService drgTldyService;

    /**
     * 查询DRG特例单议列表
     */
    @PreAuthorize("@ss.hasPermi('system:tldy:list')")
    @GetMapping("/list")
    public TableDataInfo list(DrgTldy drgTldy)
    {
        startPage();
        List<DrgTldy> list = drgTldyService.selectDrgTldyList(drgTldy);
        return getDataTable(list);
    }

    /**
     * 导出DRG特例单议列表
     */
    @PreAuthorize("@ss.hasPermi('system:tldy:export')")
    @Log(title = "DRG特例单议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DrgTldy drgTldy)
    {
        List<DrgTldy> list = drgTldyService.selectDrgTldyList(drgTldy);
        ExcelUtil<DrgTldy> util = new ExcelUtil<DrgTldy>(DrgTldy.class);
        util.exportExcel(response, list, "DRG特例单议数据");
    }

    /**
     * 获取DRG特例单议详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tldy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(drgTldyService.selectDrgTldyById(id));
    }

    /**
     * 新增DRG特例单议
     */
    @PreAuthorize("@ss.hasPermi('system:tldy:add')")
    @Log(title = "DRG特例单议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DrgTldy drgTldy)
    {
        return toAjax(drgTldyService.insertDrgTldy(drgTldy));
    }

    /**
     * 修改DRG特例单议
     */
    @PreAuthorize("@ss.hasPermi('system:tldy:edit')")
    @Log(title = "DRG特例单议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DrgTldy drgTldy)
    {
        return toAjax(drgTldyService.updateDrgTldy(drgTldy));
    }

    /**
     * 删除DRG特例单议
     */
    @PreAuthorize("@ss.hasPermi('system:tldy:remove')")
    @Log(title = "DRG特例单议", businessType = BusinessType.DELETE)
	  @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(drgTldyService.deleteDrgTldyByIds(ids));
    }


    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
      ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
      return util.importTemplateExcel("结算明细");
    }

    @Log(title = "扣款明细导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
      ExcelUtil<DrgTldy> util = new ExcelUtil<DrgTldy>(DrgTldy.class);
      List<DrgTldy> drgTldyList = util.importExcel(file.getInputStream());
      String message = drgTldyService.importDrgTldyData(drgTldyList, updateSupport);
      return AjaxResult.success(message);
    }
}
