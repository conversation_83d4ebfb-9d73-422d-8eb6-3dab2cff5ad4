package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbgkJkrule;
import com.ruoyi.system.service.IYbgkJkruleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监控规则Controller
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@RestController
@RequestMapping("/gksz/jkrule")
public class YbgkJkruleController extends BaseController {
  @Autowired
  private IYbgkJkruleService ybgkJkruleService;

  /**
   * 查询监控规则列表
   */
  @GetMapping("/list")
  public TableDataInfo list(YbgkJkrule ybgkJkrule) {
    startPage();
    List<YbgkJkrule> list = ybgkJkruleService.selectYbgkJkruleList(ybgkJkrule);
    return getDataTable(list);
  }

  @GetMapping("/listrules")
  public TableDataInfo listRules(YbgkJkrule ybgkJkrule) {
    List<YbgkJkrule> list = ybgkJkruleService.selectYbgkJkruleList(ybgkJkrule);
    return getDataTable(list);
  }

  /**
   * 导出监控规则列表
   */
  @Log(title = "监控规则", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, YbgkJkrule ybgkJkrule) {
    List<YbgkJkrule> list = ybgkJkruleService.selectYbgkJkruleList(ybgkJkrule);
    ExcelUtil<YbgkJkrule> util = new ExcelUtil<YbgkJkrule>(YbgkJkrule.class);
    util.exportExcel(response, list, "监控规则数据");
  }

  /**
   * 获取监控规则详细信息
   */
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Integer id) {
    return success(ybgkJkruleService.selectYbgkJkruleById(id));
  }

  /**
   * 新增监控规则
   */
  @Log(title = "监控规则", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody YbgkJkrule ybgkJkrule) {
    return toAjax(ybgkJkruleService.insertYbgkJkrule(ybgkJkrule));
  }

  /**
   * 修改监控规则
   */
  @Log(title = "监控规则", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody YbgkJkrule ybgkJkrule) {
    return toAjax(ybgkJkruleService.updateYbgkJkrule(ybgkJkrule));
  }

  /**
   * 删除监控规则
   */
  @Log(title = "监控规则", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Integer[] ids) {
    return toAjax(ybgkJkruleService.deleteYbgkJkruleByIds(ids));
  }
}
