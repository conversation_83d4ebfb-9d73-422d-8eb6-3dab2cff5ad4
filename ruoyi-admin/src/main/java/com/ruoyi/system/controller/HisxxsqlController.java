package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Hisxxsql;
import com.ruoyi.system.service.IHisxxsqlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据同步SQL表Controller
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@RestController
@RequestMapping("/system/hisxxsql")
public class HisxxsqlController extends BaseController
{
    @Autowired
    private IHisxxsqlService hisxxsqlService;

    /**
     * 查询数据同步SQL表列表
     */
    @PreAuthorize("@ss.hasPermi('system:hisxxsql:list')")
    @GetMapping("/list")
    public TableDataInfo list(Hisxxsql hisxxsql)
    {
        startPage();
        List<Hisxxsql> list = hisxxsqlService.selectHisxxsqlList(hisxxsql);
        return getDataTable(list);
    }

    /**
     * 导出数据同步SQL表列表
     */
    @PreAuthorize("@ss.hasPermi('system:hisxxsql:export')")
    @Log(title = "数据同步SQL表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Hisxxsql hisxxsql)
    {
        List<Hisxxsql> list = hisxxsqlService.selectHisxxsqlList(hisxxsql);
        ExcelUtil<Hisxxsql> util = new ExcelUtil<Hisxxsql>(Hisxxsql.class);
        util.exportExcel(response, list, "数据同步SQL表数据");
    }

    /**
     * 获取数据同步SQL表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:hisxxsql:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hisxxsqlService.selectHisxxsqlById(id));
    }

    /**
     * 新增数据同步SQL表
     */
    @PreAuthorize("@ss.hasPermi('system:hisxxsql:add')")
    @Log(title = "数据同步SQL表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Hisxxsql hisxxsql)
    {
        return toAjax(hisxxsqlService.insertHisxxsql(hisxxsql));
    }

    /**
     * 修改数据同步SQL表
     */
    @PreAuthorize("@ss.hasPermi('system:hisxxsql:edit')")
    @Log(title = "数据同步SQL表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Hisxxsql hisxxsql)
    {
        return toAjax(hisxxsqlService.updateHisxxsql(hisxxsql));
    }

    /**
     * 删除数据同步SQL表
     */
    @PreAuthorize("@ss.hasPermi('system:hisxxsql:remove')")
    @Log(title = "数据同步SQL表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hisxxsqlService.deleteHisxxsqlByIds(ids));
    }
}
