package com.ruoyi.system.controller;

import java.time.LocalDateTime;
import java.util.*;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.JsxxHis;
import com.ruoyi.system.service.IJsxxHisService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * HIS结算信息Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@RestController
@RequestMapping("/jsxx/hisjsxx")
public class JsxxHisController extends BaseController
{
    @Autowired
    private IJsxxHisService jsxxHisService;

    /**
     * 查询HIS结算信息列表
     */
    @PreAuthorize("@ss.hasPermi('jsxx:hisjsxx:list')")
    @GetMapping("/list")
    public TableDataInfo list(JsxxHis jsxxHis)
    {
        startPage();
        List<JsxxHis> list = jsxxHisService.selectJsxxHisList(jsxxHis);
        return getDataTable(list);
    }

    /**
     * 导出HIS结算信息列表
     */
    @PreAuthorize("@ss.hasPermi('jsxx:hisjsxx:export')")
    @Log(title = "HIS结算信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JsxxHis jsxxHis)
    {
        List<JsxxHis> list = jsxxHisService.selectJsxxHisList(jsxxHis);
        ExcelUtil<JsxxHis> util = new ExcelUtil<JsxxHis>(JsxxHis.class);
        util.exportExcel(response, list, "HIS结算信息数据");
    }

    /**
     * 获取HIS结算信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('jsxx:hisjsxx:query')")
    @GetMapping(value = "/{setlId}")
    public AjaxResult getInfo(@PathVariable("setlId") String setlId)
    {
        return success(jsxxHisService.selectJsxxHisBySetlId(setlId));
    }

    /**
     * 新增HIS结算信息
     */
    @PreAuthorize("@ss.hasPermi('jsxx:hisjsxx:add')")
    @Log(title = "HIS结算信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JsxxHis jsxxHis)
    {
        return toAjax(jsxxHisService.insertJsxxHis(jsxxHis));
    }

    /**
     * 修改HIS结算信息
     */
    @PreAuthorize("@ss.hasPermi('jsxx:hisjsxx:edit')")
    @Log(title = "HIS结算信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JsxxHis jsxxHis)
    {
        return toAjax(jsxxHisService.updateJsxxHis(jsxxHis));
    }

    /**
     * 删除HIS结算信息
     */
    @PreAuthorize("@ss.hasPermi('jsxx:hisjsxx:remove')")
    @Log(title = "HIS结算信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{setlIds}")
    public AjaxResult remove(@PathVariable String[] setlIds)
    {
        return toAjax(jsxxHisService.deleteJsxxHisBySetlIds(setlIds));
    }

    /**
     * 获取科室列表
     * @return
     */
    @PostMapping("/getDeptnameList")
    public AjaxResult getDeptnameList() {
        List<String> list = jsxxHisService.getDeptnameListWithTwoYears();

        return AjaxResult.success().put("list", list);
    }

    @PostMapping("/getDoctorListByDept/{deptname}")
    public AjaxResult getDoctorList(@PathVariable String deptname) {
        List<String> list = jsxxHisService.getDoctorListByDept(deptname);
        return  success().put("list", list);
    }
}
