package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HLcljItemD;
import com.ruoyi.system.service.IHLcljItemDService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 药品使用记录Controller
 * 
 * <AUTHOR>
 * @date 2023-07-16
 */
@RestController
@RequestMapping("/system/hlcljItemD")
public class HLcljItemDController extends BaseController
{
    @Autowired
    private IHLcljItemDService hLcljItemDService;

    /**
     * 查询药品使用记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HLcljItemD hLcljItemD)
    {
        List<HLcljItemD> list = hLcljItemDService.selectHLcljItemDList(hLcljItemD);
        return getDataTable(list);
    }

    /**
     * 导出药品使用记录列表
     */
    @Log(title = "药品使用记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HLcljItemD hLcljItemD)
    {
        List<HLcljItemD> list = hLcljItemDService.selectHLcljItemDList(hLcljItemD);
        ExcelUtil<HLcljItemD> util = new ExcelUtil<HLcljItemD>(HLcljItemD.class);
        util.exportExcel(response, list, "药品使用记录数据");
    }

    /**
     * 获取药品使用记录详细信息
     */
    @GetMapping(value = "/{cBillId}")
    public AjaxResult getInfo(@PathVariable("cBillId") String cBillId)
    {
        return success(hLcljItemDService.selectHLcljItemDByCBillId(cBillId));
    }

    /**
     * 新增药品使用记录
     */
    @Log(title = "药品使用记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HLcljItemD hLcljItemD)
    {
        return toAjax(hLcljItemDService.insertHLcljItemD(hLcljItemD));
    }

    /**
     * 修改药品使用记录
     */
    @Log(title = "药品使用记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HLcljItemD hLcljItemD)
    {
        System.out.println(hLcljItemD);
        return toAjax(hLcljItemDService.updateHLcljItemD(hLcljItemD));
    }

    /**
     * 删除药品使用记录
     */
    @Log(title = "药品使用记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cBillIds}")
    public AjaxResult remove(@PathVariable String[] cBillIds)
    {
        return toAjax(hLcljItemDService.deleteHLcljItemDByCBillIds(cBillIds));
    }
}
