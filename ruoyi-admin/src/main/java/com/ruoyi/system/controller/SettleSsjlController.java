package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SettleSsjl;
import com.ruoyi.system.service.ISettleSsjlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 结算清单操作手术Controller
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/system/ssjl")
public class SettleSsjlController extends BaseController
{
    @Autowired
    private ISettleSsjlService settleSsjlService;

    /**
     * 查询结算清单操作手术列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SettleSsjl settleSsjl)
    {
        startPage();
        List<SettleSsjl> list = settleSsjlService.selectSettleSsjlList(settleSsjl);
        return getDataTable(list);
    }

    /**
     * 导出结算清单操作手术列表
     */
    @PreAuthorize("@ss.hasPermi('system:ssjl:export')")
    @Log(title = "结算清单操作手术", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SettleSsjl settleSsjl)
    {
        List<SettleSsjl> list = settleSsjlService.selectSettleSsjlList(settleSsjl);
        ExcelUtil<SettleSsjl> util = new ExcelUtil<SettleSsjl>(SettleSsjl.class);
        util.exportExcel(response, list, "结算清单操作手术数据");
    }

    /**
     * 获取结算清单操作手术详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:ssjl:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(settleSsjlService.selectSettleSsjlById(id));
    }

    /**
     * 新增结算清单操作手术
     */
    @PreAuthorize("@ss.hasPermi('system:ssjl:add')")
    @Log(title = "结算清单操作手术", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SettleSsjl settleSsjl)
    {
        return toAjax(settleSsjlService.insertSettleSsjl(settleSsjl));
    }

    /**
     * 修改结算清单操作手术
     */
    @PreAuthorize("@ss.hasPermi('system:ssjl:edit')")
    @Log(title = "结算清单操作手术", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SettleSsjl settleSsjl)
    {
        return toAjax(settleSsjlService.updateSettleSsjl(settleSsjl));
    }

    /**
     * 删除结算清单操作手术
     */
    @PreAuthorize("@ss.hasPermi('system:ssjl:remove')")
    @Log(title = "结算清单操作手术", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(settleSsjlService.deleteSettleSsjlByIds(ids));
    }
}
