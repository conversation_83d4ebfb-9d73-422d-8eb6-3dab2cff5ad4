package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 费用审核Controller
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@RestController
@Anonymous
@RequestMapping("/gksz/fysh")
public class FyshController extends BaseController {
  @Autowired
  private IFyshService fyshService;
  @Autowired
  private CyshController cyshController;
  @Autowired
  private IBrxxService brxxService;
  @Autowired
  private IBrxxSyncService brxxSyncService;
  @Autowired
  private IYbgkBrxxCheckjlService ybgkBrxxCheckjlService;

  /**
   * 查询费用审核列表
   */
  @GetMapping("/list")
  public TableDataInfo list(Fysh fysh) {
    startPage();
    List<Fysh> list = fyshService.selectFyshList(fysh);
    if (list.size()==0&&fysh.getZyh()!=null&&"1".equals(fysh.getZyzt())) {
    	fysh.setZyzt(null);
    	list = fyshService.selectFyshList(fysh);
    }
    return getDataTable(list);
  }

  @GetMapping("/listks")
  public TableDataInfo listks() {
    List<Fysh> list = fyshService.selectFyshks();
    return getDataTable(list);
  }

  /**
   * 导出费用审核列表
   */
  @Log(title = "费用审核", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, Fysh fysh) {
    List<Fysh> list = fyshService.selectFyshList(fysh);
    ExcelUtil<Fysh> util = new ExcelUtil<Fysh>(Fysh.class);
    util.exportExcel(response, list, "费用审核数据");
  }

  /**
   * 获取费用审核详细信息
   */
  @GetMapping(value = "/{brtype}")
  public AjaxResult getInfo(@PathVariable("brtype") String brtype) {
    return success(fyshService.selectFyshByBrtype(brtype));
  }

  /**
   * 新增费用审核
   */
  @Log(title = "费用审核", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody Fysh fysh) {
    return toAjax(fyshService.insertFysh(fysh));
  }

  /**
   * 修改费用审核
   */
  @Log(title = "费用审核", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody Fysh fysh) {
    return toAjax(fyshService.updateFysh(fysh));
  }

  /**
   * 删除费用审核
   */
  @Log(title = "费用审核", businessType = BusinessType.DELETE)
  @DeleteMapping("/{brtypes}")
  public AjaxResult remove(@PathVariable String[] brtypes) {
    return toAjax(fyshService.deleteFyshByBrtypes(brtypes));
  }

  /**
   * 获取病人基本信息与审核信息
   *
   * @param brbs
   * @param zyh
   * @return
   */
  @RequestMapping("/getBrShxxData")
  public Map<String, Object> getBrShxxData(@RequestParam(value = "brbs", required = false) String brbs, @RequestParam(value = "zyh", required = false) String zyh,
                                           @RequestParam(value = "brid", required = false) String brid, @RequestParam(value = "zyid", required = false) String zyid) {
    Map<String, Object> map = new HashMap<>();

    if (StringUtils.isNotBlank(brbs) || StringUtils.isNotBlank(zyh) || (StringUtils.isNotBlank(brid) && StringUtils.isNotBlank(zyid))) {
      Brxx brxx = new Brxx();
      brxx.setJzh(StringUtils.isNotBlank(brbs) ? brbs : null);
      brxx.setZyh(StringUtils.isNotBlank(zyh) ? zyh : null);
      brxx.setBrid(StringUtils.isNotBlank(brid) ? brid : null);
      brxx.setZyid(StringUtils.isNotBlank(zyid) ? zyid : null);
      List<Brxx> brxxList = brxxService.selectBrxxList(brxx);
      
      if (brxxList.isEmpty()) {
    	  if(brbs!=null&&brbs.contains("_")) {
    		  String[] ls_arr = brbs.split("_");
    		  if (ls_arr.length==2) {
    			  brid = ls_arr[0];
    			  zyid = ls_arr[1];
    			  Brxx brxx1 = new Brxx();
    			  brxx1.setBrid(brid);
    			  brxx1.setZyid(zyid);
    			  brxxList = brxxService.selectBrxxList(brxx1);
    		  }
    	  }
      }
      

      if (!brxxList.isEmpty()) {
        brxx = brxxList.get(0);
        brbs = brxx.getJzh();
        String ls_jgid=null;
        if(brxx!=null) {
        	ls_jgid = brxx.getJgid();
        	if (ls_jgid==null) {
        		ls_jgid = "";
        	}
        }

        //这几家没有控费
        if("c182cc62-ee76-4b61-ab6d-75c0b03bfce0".equals(ls_jgid)||"9ae2a358-50c1-4602-a547-9b65bd7a1b18".equals(ls_jgid)||"7fd48753-fb44-47e2-9dfc-cdacc6469966".equals(ls_jgid)||"3be60727-c314-442d-a5ab-f2a77a8a0c8a".equals(ls_jgid)||"eea34cba-29de-44c7-bafc-d7a362557441".equals(ls_jgid))
        {
        	map.put("msg", "当前机构未订购该功能");
            return map;
        }



        //费用信息同步
        try {
          brxxSyncService.fyxxSync(brxx.getBrid(), brxx.getZyid());
        } catch (Exception e) {
          logger.error("费用同步失败，病人就诊号：" + brbs);
        }
        //诊断信息同步
        try {
          brxxSyncService.zdxxSync(brxx.getBrid(), brxx.getZyid());
        } catch (Exception e) {
          logger.error("诊断同步失败，病人就诊号：" + brbs);
        }
        //医嘱信息同步
        try {
          brxxSyncService.yzxxSync(brxx.getBrid(), brxx.getZyid());
        } catch(Exception e) {
          logger.error("医嘱同步失败，病人就诊号: {}", brbs);
          logger.error(e.getMessage());
        }

        brxx.setCydateStr(formatDate(brxx.getCydate()));
        brxx.setRydateStr(formatDate(brxx.getRydate()));

        List<YbgkWgjl> list = new ArrayList<>();
        YbgkWgjl ybgkWgjl = new YbgkWgjl();
        ybgkWgjl.setJzh(brxx.getJzh());
        TableDataInfo cyshed = cyshController.cysh(ybgkWgjl);
        List<List> resultList = cyshed.getRows() != null ? (List<List>) cyshed.getRows() : new ArrayList<>();
        if (resultList.size() > 0) {
          list = resultList.get(0);
        }


//        ybgkBrxxCheckjlService.updateBrxxCheckjl(brxx,list);

        map.put("brxx", brxx);
        map.put("wgjlList", list);
        return map;
      } else {
        map.put("msg", "当前患者不存在");
        return map;
      }
    }

    map.put("msg", "请输入患者标识或住院号");
    return map;
  }


  private String formatDate(Date date) {
    if (date != null) {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      return simpleDateFormat.format(date);
    }
    return "";
  }


  /**
   * 修改病人的审核记录
   * @param brxx
   * @param list
   */
  public void updateBrxxCheckJl(Brxx brxx,List<YbgkWgjl> list) {
    String brid = brxx.getBrid();
    String zyid = brxx.getZyid();
    YbgkBrxxCheckjl ybgkBrxxCheckjl = ybgkBrxxCheckjlService.selectYbgkBrxxCheckjlByBridAndZyid(new YbgkBrxxCheckjl(brid,zyid));
    if (ybgkBrxxCheckjl != null) {
      Integer checkFlag = ybgkBrxxCheckjl.getCheckFlag();
      Long checkFirst = ybgkBrxxCheckjl.getCheckFirst();
      if (checkFlag == null || checkFlag == 0 || checkFirst == null) {
        ybgkBrxxCheckjl.setCheckFirstDate(new Date());
        ybgkBrxxCheckjl.setCheckFirst((long) list.size());
      } else {
        ybgkBrxxCheckjl.setCheckEndDate(new Date());
        ybgkBrxxCheckjl.setCheckEnd((long) list.size());
      }
      ybgkBrxxCheckjl.setCheckFlag(1);
      ybgkBrxxCheckjlService.updateYbgkBrxxCheckjl(ybgkBrxxCheckjl);
    } else {
      ybgkBrxxCheckjl = new YbgkBrxxCheckjl();
      ybgkBrxxCheckjl.setBrid(brid);
      ybgkBrxxCheckjl.setZyid(zyid);
      ybgkBrxxCheckjl.setCheckFlag(1);
      ybgkBrxxCheckjl.setCheckFirstDate(new Date());
      ybgkBrxxCheckjl.setCheckFirst((long) list.size());
      ybgkBrxxCheckjlService.insertYbgkBrxxCheckjl(ybgkBrxxCheckjl);
    }
  }

}
