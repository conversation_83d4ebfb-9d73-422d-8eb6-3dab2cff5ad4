package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HLcljDate;
import com.ruoyi.system.service.IHLcljDateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 时间花费管理Controller
 * 
 * <AUTHOR>
 * @date 2023-07-16
 */
@RestController
@RequestMapping("/lclj/date")
public class HLcljDateController extends BaseController
{
    @Autowired
    private IHLcljDateService hLcljDateService;

    /**
     * 查询时间花费管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HLcljDate hLcljDate)
    {
        List<HLcljDate> list = hLcljDateService.selectHLcljDateList(hLcljDate);
        return getDataTable(list);
    }

    /**
     * 导出时间花费管理列表
     */
    @Log(title = "时间花费管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HLcljDate hLcljDate)
    {
        List<HLcljDate> list = hLcljDateService.selectHLcljDateList(hLcljDate);
        ExcelUtil<HLcljDate> util = new ExcelUtil<HLcljDate>(HLcljDate.class);
        util.exportExcel(response, list, "时间花费管理数据");
    }

    /**
     * 获取时间花费管理详细信息
     */
    @GetMapping(value = "/{cId}")
    public AjaxResult getInfo(@PathVariable("cId") String cId)
    {
        return success(hLcljDateService.selectHLcljDateByCId(cId));
    }

    /**
     * 新增时间花费管理
     */
    @Log(title = "时间花费管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HLcljDate hLcljDate)
    {
        System.out.println(hLcljDate);
        return toAjax(hLcljDateService.insertHLcljDate(hLcljDate));
    }

    /**
     * 修改时间花费管理
     */
    @Log(title = "时间花费管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HLcljDate hLcljDate)
    {
        System.out.println(hLcljDate);
        return toAjax(hLcljDateService.updateHLcljDate(hLcljDate));
    }

    /**
     * 删除时间花费管理
     */
    @Log(title = "时间花费管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cIds}")
    public AjaxResult remove(@PathVariable String[] cIds)
    {
        return toAjax(hLcljDateService.deleteHLcljDateByCIds(cIds));
    }
}
