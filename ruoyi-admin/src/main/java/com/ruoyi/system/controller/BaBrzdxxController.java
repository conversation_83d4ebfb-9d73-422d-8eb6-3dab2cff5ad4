package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.BaBrzdxx;
import com.ruoyi.system.domain.BaSyjl;
import com.ruoyi.system.domain.Brxx;
import com.ruoyi.system.domain.BrzdFyxx;
import com.ruoyi.system.domain.Bzml;
import com.ruoyi.system.service.IBaBrzdxxService;
import com.ruoyi.system.service.IBzmlService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 诊断信息Controller
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
@RestController
@Anonymous
@RequestMapping("/drg/brzdxx")
public class BaBrzdxxController extends BaseController
{
    @Autowired
    private IBaBrzdxxService baBrzdxxService;
    @Autowired
    private IBzmlService bzmlService;

    /**
     * 查询诊断信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BaBrzdxx baBrzdxx)
    {
        startPage();
        List<BaBrzdxx> list = baBrzdxxService.selectBaBrzdxxList(baBrzdxx);
        return getDataTable(list);
    }

    /**
     * 获取单个病人的诊断
     */
    @GetMapping("/getBrzdxxByOnePatient")
    public AjaxResult getBrzdxxByBr(BaBrzdxx brxx) {
        return AjaxResult.success(baBrzdxxService.selectBaBrzdxxList(brxx));
    }

    /**
     * 导出诊断信息列表
     */
    @Log(title = "诊断信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectBaBrzdxxList(baBrzdxx);
        ExcelUtil<BaBrzdxx> util = new ExcelUtil<BaBrzdxx>(BaBrzdxx.class);
        util.exportExcel(response, list, "诊断信息数据");
    }

    /**
     * 获取诊断信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(baBrzdxxService.selectBaBrzdxxById(id));
    }

    /**
     * 新增诊断信息
     */
    @Log(title = "诊断信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaBrzdxx baBrzdxx)
    {
        return toAjax(baBrzdxxService.insertBaBrzdxx(baBrzdxx));
    }

    /**
     * 修改诊断信息
     */
    @Log(title = "诊断信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaBrzdxx baBrzdxx)
    {
        return toAjax(baBrzdxxService.updateBaBrzdxx(baBrzdxx));
    }

    /**
     * 删除诊断信息
     */
    @Log(title = "诊断信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(baBrzdxxService.deleteBaBrzdxxByIds(ids));
    }


    @RequestMapping("/zdxx")
    public TableDataInfo zdxx(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectZdxx(baBrzdxx);
        return getDataTable(list);
    }
    @RequestMapping("/ssxx")
    public TableDataInfo ssxx(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectSsxx(baBrzdxx);
        return getDataTable(list);
    }
    @RequestMapping("/zdxxsy")
    public TableDataInfo zdxxsy(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectSyZdxx(baBrzdxx);
        return getDataTable(list);
    }



    @RequestMapping("/zdxxyb")
    public TableDataInfo zdxxyb(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectYbZdxx(baBrzdxx);
        return getDataTable(list);
    }
    @RequestMapping("/bztype")
    public TableDataInfo selectbztype(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectBzType(baBrzdxx);
        return getDataTable(list);
    }
    @RequestMapping("/Icd10")
    public TableDataInfo selectIcd10(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectIcd10(baBrzdxx);
        return getDataTable(list);
    }
    @RequestMapping("/Icd10Yb")
    public TableDataInfo selectIcd10Yb(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectIcd10Yb(baBrzdxx);
        return getDataTable(list);
    }

    @RequestMapping("/selectYbzdByLczd")
    public TableDataInfo selectYbzdByLczd(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectYbzdByLczd(baBrzdxx);
        return getDataTable(list);
    }
    @RequestMapping("/Icd09")
    public TableDataInfo selectIcd09(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectIcd09(baBrzdxx);
        return getDataTable(list);
    }
    @RequestMapping("/Icd09Yb")
    public TableDataInfo selectIcd09Yb(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectIcd09Yb(baBrzdxx);
        return getDataTable(list);
    }
    /**
     * 删除诊断记录
     */
    @RequestMapping("remove")
    public AjaxResult remove(BaBrzdxx baBrzdxx)
    {
        return toAjax(baBrzdxxService.deleteBaBrzdxxById(baBrzdxx));
    }

    @RequestMapping("icd10getybmc")
    public String icd10getybmc(BaBrzdxx baBrzdxx)
    {
    	//处理+号转为空串的问题
        if(baBrzdxx.getJbbm()!=null) {
          if(baBrzdxx.getJbbm().indexOf(" ")>0) {
            baBrzdxx.setJbbm(baBrzdxx.getJbbm().replace(" ", "+"));
          }
        }
         BaBrzdxx  baBrzdxx1 = baBrzdxxService.selectYbzdByJbbmcc(baBrzdxx);
         if (baBrzdxx1==null) {
        	 baBrzdxx1 = baBrzdxxService.selectzdByJbbmcc(baBrzdxx);
         }
         if (baBrzdxx1==null) {
        	 Bzml bzml = bzmlService.selectBzmlByBzbm(baBrzdxx.getJbbm());
        	 if (bzml!=null) {
        		 return bzml.getBzmc();
        	 }
         }
         if (baBrzdxx1==null) {
        	 return "";
         }else
         {
         return baBrzdxx1.getZdmc();
         }
    }

    @RequestMapping("icd9getybmc")
    public String icd9getybmc(BaBrzdxx baBrzdxx)
    {
         BaBrzdxx  baBrzdxx1 = baBrzdxxService.selectYbzdByybzdbb(baBrzdxx);
         if (baBrzdxx1==null) {
        	 baBrzdxx1 = baBrzdxxService.selectzdByybzdbb(baBrzdxx);
         }
        
         if (baBrzdxx1==null) {
        	 return "";
         }else
         {
         return baBrzdxx1.getZdmc();
         }
    }

    @RequestMapping("icd10getybbm")
    public String icd10getybbm(BaBrzdxx baBrzdxx)
    {
    	//处理+号转为空串的问题
        if(baBrzdxx.getJbbm()!=null) {
          if(baBrzdxx.getJbbm().indexOf(" ")>0) {
            baBrzdxx.setJbbm(baBrzdxx.getJbbm().replace(" ", "+"));
          }
        }

         BaBrzdxx  baBrzdxx1 = baBrzdxxService.selectzdbmtoyb(baBrzdxx);
         if (baBrzdxx1==null) {
        	 return "";
         }else
         {
        	 return baBrzdxx1.getJbbm();
         }

    }



    @GetMapping("/selectZdFyxx")
    public TableDataInfo selectZdFyxx(BaSyjl baSyjl)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectZdFyxx(baSyjl);
        return getDataTable(list);
    }

    @GetMapping("/selectZdFyxxtxt")
    @ResponseBody
    public String selectZdFyxxtxt(BaSyjl baSyjl)
    {
    	String ls_return="";
        List<BaBrzdxx> list = baBrzdxxService.selectZdFyxx(baSyjl);
        for (int i = 0; i < list.size(); i++) {
        	if(i>20) {
        		break;
        	}
        	  if (i == 0) {
                  ls_return = list.get(i).getJbbm() + "|||" + list.get(i).getJe();
                } else {
                  ls_return = ls_return + "&&" +list.get(i).getJbbm() + "|||" + list.get(i).getJe();
           }
        }
        return ls_return;
    }


    @GetMapping("/selectZyzd")
    public TableDataInfo selectZyzd(BaSyjl baSyjl)
    {
        List<BaBrzdxx> list = baBrzdxxService.selectZyzd(baSyjl);
        return getDataTable(list);
    }


  @GetMapping("/selectBrzdfy")
  public TableDataInfo selectBrzdfy(Brxx brxx)
  {
    List<BrzdFyxx> list = baBrzdxxService.selectBrzdfy(brxx);
    return getDataTable(list);
  }

  @GetMapping("/listZdxxByYb")
  public TableDataInfo listZdxxByYb(@RequestParam(value = "condition")String condition)
  {
    System.out.println(condition);
    List<BaBrzdxx> list = baBrzdxxService.selectZdxxByYb(condition);
    return getDataTable(list);
  }

  @GetMapping("/listZdxxByLc")
  public TableDataInfo listZdxxByLc(@RequestParam(value = "condition")String condition)
  {
    List<BaBrzdxx> list = baBrzdxxService.selectZdxxByLc(condition);
    return getDataTable(list);
  }

}
