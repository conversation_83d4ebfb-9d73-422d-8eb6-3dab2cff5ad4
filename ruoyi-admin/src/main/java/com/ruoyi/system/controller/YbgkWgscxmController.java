package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbgkKkmx;
import com.ruoyi.system.domain.YbgkWgscxm;
import com.ruoyi.system.domain.vo.XmWgscVo;
import com.ruoyi.system.service.IYbgkWgscxmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 违规筛查项目Controller
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@RestController
@RequestMapping("/gksz/wgscxm")
public class YbgkWgscxmController extends BaseController {
  @Autowired
  private IYbgkWgscxmService ybgkWgscxmService;

  /**
   * 查询违规筛查项目列表
   */
  @GetMapping("/list")
  public TableDataInfo list(XmWgscVo xmWgscVo) {
    startPage();
    List<XmWgscVo> list = ybgkWgscxmService.selectXmWgscVoList(xmWgscVo);
    return getDataTable(list);
  }

  @Log(title = "违规筛查项目", businessType = BusinessType.EXPORT)
  @PostMapping("/exportxm")
  public void exportxm(HttpServletResponse response) {
    List<YbgkWgscxm> list = ybgkWgscxmService.selectYbgkWgscxmList(new YbgkWgscxm());
    ExcelUtil<YbgkWgscxm> util = new ExcelUtil<YbgkWgscxm>(YbgkWgscxm.class);
    util.exportExcel(response, list, "自查项目列表本院名称");
  }



  /**
   * 导出违规筛查项目列表
   */
  @Log(title = "违规筛查项目", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, XmWgscVo xmWgscVo) {
    List<XmWgscVo> list = ybgkWgscxmService.selectXmWgscVoList(xmWgscVo);
    ExcelUtil<XmWgscVo> util = new ExcelUtil<XmWgscVo>(XmWgscVo.class);
    util.exportExcel(response, list, "违规筛查项目数据");
  }


  @Log(title = "违规筛查项目导入", businessType = BusinessType.IMPORT)
  @PostMapping("/importData")
  @ResponseBody
  public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
	  ybgkWgscxmService.deleteYbgkWgscxmAll();
    ExcelUtil<YbgkWgscxm> util = new ExcelUtil<YbgkWgscxm>(YbgkWgscxm.class);
    List<YbgkWgscxm> wgscxmList = util.importExcel(file.getInputStream());
    String message = ybgkWgscxmService.importWgscxm(wgscxmList, updateSupport);
    ybgkWgscxmService.initYbgkWgscxmXmbm();
    return AjaxResult.success(message);
  }

}
