package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbjkUseset;
import com.ruoyi.system.service.IYbjkUsesetService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 启用设置Controller
 * 
 * <AUTHOR>
 * @date 2023-11-01
 */
@RestController
@RequestMapping("/system/useset")
public class YbjkUsesetController extends BaseController
{
    @Autowired
    private IYbjkUsesetService ybjkUsesetService;

    /**
     * 查询启用设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:useset:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbjkUseset ybjkUseset)
    {
        startPage();
        List<YbjkUseset> list = ybjkUsesetService.selectYbjkUsesetList(ybjkUseset);
        return getDataTable(list);
    }

    /**
     * 导出启用设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:useset:export')")
    @Log(title = "启用设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbjkUseset ybjkUseset)
    {
        List<YbjkUseset> list = ybjkUsesetService.selectYbjkUsesetList(ybjkUseset);
        ExcelUtil<YbjkUseset> util = new ExcelUtil<YbjkUseset>(YbjkUseset.class);
        util.exportExcel(response, list, "启用设置数据");
    }

    /**
     * 获取启用设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:useset:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(ybjkUsesetService.selectYbjkUsesetById(id));
    }

    /**
     * 新增启用设置
     */
    @PreAuthorize("@ss.hasPermi('system:useset:add')")
    @Log(title = "启用设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbjkUseset ybjkUseset)
    {
        return toAjax(ybjkUsesetService.insertYbjkUseset(ybjkUseset));
    }

    /**
     * 修改启用设置
     */
    @PreAuthorize("@ss.hasPermi('system:useset:edit')")
    @Log(title = "启用设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbjkUseset ybjkUseset)
    {
        return toAjax(ybjkUsesetService.updateYbjkUseset(ybjkUseset));
    }

    /**
     * 删除启用设置
     */
    @PreAuthorize("@ss.hasPermi('system:useset:remove')")
    @Log(title = "启用设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(ybjkUsesetService.deleteYbjkUsesetByIds(ids));
    }
}
