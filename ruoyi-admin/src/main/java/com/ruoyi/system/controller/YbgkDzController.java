package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbgkDz;
import com.ruoyi.system.service.IYbgkDzService;
import com.ruoyi.system.service.IYbxmSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 药品管控设置Controller
 *
 * <AUTHOR>
 * @date 2023-07-02
 */
@RestController
@RequestMapping("/gksz/dz")
public class YbgkDzController extends BaseController {
  @Autowired
  private IYbgkDzService ybgkDzService;
  @Autowired
  private IYbxmSyncService ybxmSyncService;

  /**
   * 查询药品管控设置列表
   */
  @GetMapping("/selectYbgkDzAll")
  public TableDataInfo selectYbgkDzAll(YbgkDz ybgkDz) {
    startPage();
    List<YbgkDz> list = ybgkDzService.selectYbgkDzAll(ybgkDz);
    return getDataTable(list);
  }

  @GetMapping("/getProList")
  public TableDataInfo getProList(@RequestParam("value") String value) {
    return getDataTable(ybgkDzService.getProList(value));
  }

  /**
   * 查询药品管控设置列表
   */
  @GetMapping("/list")
  public TableDataInfo list(YbgkDz ybgkDz) {
    startPage();
    List<YbgkDz> list = ybgkDzService.selectYbgkDzList(ybgkDz);
    return getDataTable(list);
  }

  @RequestMapping("/setYbProInfo")
  public AjaxResult setYbProInfo() {
    return toAjax(ybxmSyncService.setYbProInfo());
  }

  /**
   * 导出药品管控设置列表
   */
  @Log(title = "药品管控设置", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, YbgkDz ybgkDz) {
    List<YbgkDz> list = ybgkDzService.selectYbgkDzList(ybgkDz);
    ExcelUtil<YbgkDz> util = new ExcelUtil<YbgkDz>(YbgkDz.class);
    util.exportExcel(response, list, "药品管控设置数据");
  }

  @PostMapping("/ybdz/export")
  public void ybdzExport(HttpServletResponse response, YbgkDz ybgkDz) {
    List<YbgkDz> list = ybgkDzService.selectYbgkDzAll(ybgkDz);
    ExcelUtil<YbgkDz> util = new ExcelUtil<YbgkDz>(YbgkDz.class);
    util.exportExcel(response, list, "医保对照结果");
  }

  /**
   * 获取药品管控设置详细信息
   */
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") String id) {
    return success(ybgkDzService.selectYbgkDzById(id));
  }

  /**
   * 新增药品管控设置
   */
  @Log(title = "药品管控设置", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody YbgkDz ybgkDz) {
    return toAjax(ybgkDzService.insertYbgkDz(ybgkDz));
  }

  /**
   * 修改药品管控设置
   */
  @Log(title = "药品管控设置", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody YbgkDz ybgkDz) {
    return toAjax(ybgkDzService.updateYbgkDz(ybgkDz));
  }

  /**
   * 删除药品管控设置
   */
  @Log(title = "药品管控设置", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable String[] ids) {
    return toAjax(ybgkDzService.deleteYbgkDzByIds(ids));
  }
}
