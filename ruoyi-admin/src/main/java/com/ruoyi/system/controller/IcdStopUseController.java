package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.IcdStopUse;
import com.ruoyi.system.service.IIcdStopUseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * ICD数据停用Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/system/icdUse")
public class IcdStopUseController extends BaseController
{
    @Autowired
    private IIcdStopUseService icdStopUseService;

    /**
     * 查询ICD数据停用列表
     */
    @PreAuthorize("@ss.hasPermi('system:icdUse:list')")
    @GetMapping("/list")
    public TableDataInfo list(IcdStopUse icdStopUse)
    {
        startPage();
        List<IcdStopUse> list = icdStopUseService.selectIcdStopUseList(icdStopUse);
        return getDataTable(list);
    }

    /**
     * 导出ICD数据停用列表
     */
    @PreAuthorize("@ss.hasPermi('system:icdUse:export')")
    @Log(title = "ICD数据停用", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IcdStopUse icdStopUse)
    {
        List<IcdStopUse> list = icdStopUseService.selectIcdStopUseList(icdStopUse);
        ExcelUtil<IcdStopUse> util = new ExcelUtil<IcdStopUse>(IcdStopUse.class);
        util.exportExcel(response, list, "ICD数据停用数据");
    }

    /**
     * 获取ICD数据停用详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:icdUse:query')")
    @GetMapping(value = "/{bm}")
    public AjaxResult getInfo(@PathVariable("bm") String bm)
    {
        return success(icdStopUseService.selectIcdStopUseByBm(bm));
    }

    /**
     * 新增ICD数据停用
     */
    @PreAuthorize("@ss.hasPermi('system:icdUse:add')")
    @Log(title = "ICD数据停用", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IcdStopUse icdStopUse)
    {
        return toAjax(icdStopUseService.insertIcdStopUse(icdStopUse));
    }

    /**
     * 修改ICD数据停用
     */
    @PreAuthorize("@ss.hasPermi('system:icdUse:edit')")
    @Log(title = "ICD数据停用", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IcdStopUse icdStopUse)
    {
        return toAjax(icdStopUseService.updateIcdStopUse(icdStopUse));
    }

    /**
     * 删除ICD数据停用
     */
    @PreAuthorize("@ss.hasPermi('system:icdUse:remove')")
    @Log(title = "ICD数据停用", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bms}")
    public AjaxResult remove(@PathVariable String[] bms)
    {
        return toAjax(icdStopUseService.deleteIcdStopUseByBms(bms));
    }
}
