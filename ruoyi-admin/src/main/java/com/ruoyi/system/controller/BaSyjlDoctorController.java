package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.BaSyjlDoctor;
import com.ruoyi.system.service.IBaSyjlDoctorService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 医生病案首页Controller
 *
 * <AUTHOR>
 * @date 2024-03-28
 */
@RestController
@RequestMapping("/drg/basy_doctor")
public class BaSyjlDoctorController extends BaseController
{
    @Autowired
    private IBaSyjlDoctorService baSyjlDoctorService;

    /**
     * 查询医生病案首页列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BaSyjlDoctor baSyjlDoctor)
    {
        startPage();
        List<BaSyjlDoctor> list = baSyjlDoctorService.selectBaSyjlDoctorList(baSyjlDoctor);
        return getDataTable(list);
    }

    /**
     * 导出医生病案首页列表
     */
    @Log(title = "医生病案首页", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaSyjlDoctor baSyjlDoctor)
    {
        List<BaSyjlDoctor> list = baSyjlDoctorService.selectBaSyjlDoctorList(baSyjlDoctor);
        ExcelUtil<BaSyjlDoctor> util = new ExcelUtil<BaSyjlDoctor>(BaSyjlDoctor.class);
        util.exportExcel(response, list, "医生病案首页数据");
    }

    /**
     * 获取医生病案首页详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(baSyjlDoctorService.selectBaSyjlDoctorById(id));
    }

    /**
     * 新增医生病案首页
     */
    @Log(title = "医生病案首页", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaSyjlDoctor baSyjlDoctor)
    {
        return toAjax(baSyjlDoctorService.insertBaSyjlDoctor(baSyjlDoctor));
    }

    /**
     * 修改医生病案首页
     */
    @Log(title = "医生病案首页", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaSyjlDoctor baSyjlDoctor)
    {
        return toAjax(baSyjlDoctorService.updateBaSyjlDoctor(baSyjlDoctor));
    }

    /**
     * 删除医生病案首页
     */
    @Log(title = "医生病案首页", businessType = BusinessType.DELETE)
	  @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(baSyjlDoctorService.deleteBaSyjlDoctorByIds(ids));
    }
}
