package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.system.domain.BaBrzdxx;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.BaBrzdxxSy;
import com.ruoyi.system.service.IBaBrzdxxSyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 首页临床诊断Controller
 *
 * <AUTHOR>
 * @date 2023-11-24
 */
@RestController
@Anonymous
@RequestMapping("/drg/brzdxxsy")
public class BaBrzdxxSyController extends BaseController
{
    @Autowired
    private IBaBrzdxxSyService baBrzdxxSyService;

    /**
     * 查询首页临床诊断列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BaBrzdxxSy baBrzdxxSy)
    {
        List<BaBrzdxxSy> list = baBrzdxxSyService.selectBaBrzdxxSyList(baBrzdxxSy);
        return getDataTable(list);
    }

    /**
     * 导出首页临床诊断列表
     */
    @Log(title = "首页临床诊断", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaBrzdxxSy baBrzdxxSy)
    {
        List<BaBrzdxxSy> list = baBrzdxxSyService.selectBaBrzdxxSyList(baBrzdxxSy);
        ExcelUtil<BaBrzdxxSy> util = new ExcelUtil<BaBrzdxxSy>(BaBrzdxxSy.class);
        util.exportExcel(response, list, "首页临床诊断数据");
    }

    /**
     * 获取首页临床诊断详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(baBrzdxxSyService.selectBaBrzdxxSyById(id));
    }

    /**
     * 新增首页临床诊断
     */
    @Log(title = "首页临床诊断", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaBrzdxx baBrzdxxSy)
    {
        return toAjax(baBrzdxxSyService.insertBaBrzdxxSy(baBrzdxxSy));
    }

    /**
     * 修改首页临床诊断
     */
    @Log(title = "首页临床诊断", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaBrzdxxSy baBrzdxxSy)
    {
        return toAjax(baBrzdxxSyService.updateBaBrzdxxSy(baBrzdxxSy));
    }

    /**
     * 删除首页临床诊断
     */
    @Log(title = "首页临床诊断", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(baBrzdxxSyService.deleteBaBrzdxxSyByIds(ids));
    }


    @RequestMapping("/zdxxsy")
    public TableDataInfo zdxxsy(BaBrzdxx baBrzdxx)
    {
        List<BaBrzdxxSy> list = baBrzdxxSyService.selectSyZdxx(baBrzdxx);
        return getDataTable(list);
    }
}
