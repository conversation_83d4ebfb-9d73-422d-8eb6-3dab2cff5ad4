package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HLcljType;
import com.ruoyi.system.service.IHLcljTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 临床路径管理Controller
 * 
 * <AUTHOR>
 * @date 2023-07-16
 */
@RestController
@RequestMapping("/lclj/type")
public class HLcljTypeController extends BaseController
{
    @Autowired
    private IHLcljTypeService hLcljTypeService;

    /**
     * 查询临床路径管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HLcljType hLcljType)
    {
        startPage();
        List<HLcljType> list = hLcljTypeService.selectHLcljTypeList(hLcljType);
        return getDataTable(list);
    }

    /**
     * 导出临床路径管理列表
     */
    @Log(title = "临床路径管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HLcljType hLcljType)
    {
        List<HLcljType> list = hLcljTypeService.selectHLcljTypeList(hLcljType);
        ExcelUtil<HLcljType> util = new ExcelUtil<HLcljType>(HLcljType.class);
        util.exportExcel(response, list, "临床路径管理数据");
    }

    /**
     * 获取临床路径管理详细信息
     */
    @GetMapping(value = "/{cId}")
    public AjaxResult getInfo(@PathVariable("cId") String cId)
    {
        return success(hLcljTypeService.selectHLcljTypeByCId(cId));
    }

    /**
     * 新增临床路径管理
     */
    @Log(title = "临床路径管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HLcljType hLcljType)
    {
        return toAjax(hLcljTypeService.insertHLcljType(hLcljType));
    }

    /**
     * 修改临床路径管理
     */
    @Log(title = "临床路径管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HLcljType hLcljType)
    {
        return toAjax(hLcljTypeService.updateHLcljType(hLcljType));
    }

    /**
     * 删除临床路径管理
     */
    @Log(title = "临床路径管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{cIds}")
    public AjaxResult remove(@PathVariable String[] cIds)
    {
        return toAjax(hLcljTypeService.deleteHLcljTypeByCIds(cIds));
    }
}
