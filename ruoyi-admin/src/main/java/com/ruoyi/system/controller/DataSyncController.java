package com.ruoyi.system.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.IDataSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.Map;

@RestController
@RequestMapping("/system/data-api/sync")
public class DataSyncController {

    @Autowired
    private IDataSyncService dataSyncService;

    @PostMapping("/execute/query")
    public AjaxResult executeQuery(@RequestBody Map<String, Object> map){

        dataSyncService.executeQuery(map);

        return AjaxResult.success();
    }

    @GetMapping("/execute/test")
    public AjaxResult executeTest() throws SQLException {
        dataSyncService.executeDataSync();

        return AjaxResult.success();
    }

    @GetMapping("/test/initSource")
    public String testInitSource() throws SQLException {
      dataSyncService.testInitDs();
      return "ok";
    }

    /**
     *
     * @param sqlName
     * @param paramMap
     * @return
     * @throws SQLException
     */
    @GetMapping("/execute/normal/{sqlName}")
    public AjaxResult executeNormal(@PathVariable String sqlName, @RequestParam Map<String, Object> paramMap) throws SQLException {

        dataSyncService.executeNormalDataSync(sqlName, paramMap);

        return AjaxResult.success();
    }

    @GetMapping("/execute/full/{sqlName}")
    public AjaxResult executeFull(@PathVariable String sqlName){
        dataSyncService.executeFullDataSync(sqlName);

        return AjaxResult.success();
    }


}
