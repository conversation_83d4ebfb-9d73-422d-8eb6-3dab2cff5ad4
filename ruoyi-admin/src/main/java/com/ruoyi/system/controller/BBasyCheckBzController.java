package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.YbjkOption;
import com.ruoyi.system.service.IYbjkOptionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.BBasyCheckBz;
import com.ruoyi.system.service.IBBasyCheckBzService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 病案校验Controller
 *
 * <AUTHOR>
 * @date 2024-03-09
 */
@RestController
@RequestMapping("/baxy/checkbz")
public class BBasyCheckBzController extends BaseController
{
  @Autowired
  private IBBasyCheckBzService bBasyCheckBzService;
  @Autowired
  private IYbjkOptionService optionService;

  /**
   * 查询病案校验列表
   */
  @RequestMapping("/list")
  public TableDataInfo list(BBasyCheckBz bBasyCheckBz)
  {
    startPage();
    List<BBasyCheckBz> list = bBasyCheckBzService.selectBBasyCheckBzList(bBasyCheckBz);
    return getDataTable(list);
  }

  @RequestMapping("/list2")
  public TableDataInfo list2(BBasyCheckBz bBasyCheckBz)
  {
    List<BBasyCheckBz> list = bBasyCheckBzService.selectBBasyCheckBzList(bBasyCheckBz);
    return getDataTable(list);
  }

  @RequestMapping("/getBBasyCheckBzErrortype")
  public TableDataInfo getBBasyCheckBzErrortype()
  {
    List<BBasyCheckBz> list = bBasyCheckBzService.selectBBasyCheckBzErrortype();
    return getDataTable(list);
  }

  /**
   * 导出病案校验列表
   */
  @Log(title = "病案校验", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, BBasyCheckBz bBasyCheckBz)
  {
    List<BBasyCheckBz> list = bBasyCheckBzService.selectBBasyCheckBzList(bBasyCheckBz);
    ExcelUtil<BBasyCheckBz> util = new ExcelUtil<BBasyCheckBz>(BBasyCheckBz.class);
    util.exportExcel(response, list, "病案校验数据");
  }

  /**
   * 获取病案校验详细信息
   */
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Long id)
  {
    return success(bBasyCheckBzService.selectBBasyCheckBzById(id));
  }

  /**
   * 新增病案校验
   */
  @PreAuthorize("@ss.hasPermi('system:baxy:add')")
  @Log(title = "病案校验", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody BBasyCheckBz bBasyCheckBz)
  {
    return toAjax(bBasyCheckBzService.insertBBasyCheckBz(bBasyCheckBz));
  }

  /**
   * 修改病案校验
   */
  @PreAuthorize("@ss.hasPermi('system:baxy:edit')")
  @Log(title = "病案校验", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody BBasyCheckBz bBasyCheckBz)
  {
    System.out.println(bBasyCheckBz.toString());
    return toAjax(bBasyCheckBzService.updateBBasyCheckBz(bBasyCheckBz));
  }

  /**
   * 删除病案校验
   */
  @PreAuthorize("@ss.hasPermi('system:baxy:remove')")
  @Log(title = "病案校验", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Long[] ids)
  {
    YbjkOption option = optionService.selectYbjkOptionByCCode("use_baxy_rule_delete");
    if (option == null || !"1".equals(option.getcValue())) {
      throw new ServiceException("不允许删除质控规则");
    }
    return toAjax(bBasyCheckBzService.deleteBBasyCheckBzByIds(ids));
  }
}
