package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.BaSsjlTjzd;
import com.ruoyi.system.service.IBaSsjlTjzdService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 推荐手术Controller
 *
 * <AUTHOR>
 * @date 2023-12-01
 */
@RestController
@Anonymous
@RequestMapping("/drg/ba_ssjl_tjzd")
public class BaSsjlTjzdController extends BaseController
{
    @Autowired
    private IBaSsjlTjzdService baSsjlTjzdService;

    /**
     * 查询推荐手术列表
     */
    @PreAuthorize("@ss.hasPermi('system:ba_ssjl_tjzd:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaSsjlTjzd baSsjlTjzd)
    { 
        startPage();
        List<BaSsjlTjzd> list = baSsjlTjzdService.selectBaSsjlTjzdList(baSsjlTjzd);
        return getDataTable(list);
    }

    /**
     * 导出推荐手术列表
     */
    @PreAuthorize("@ss.hasPermi('system:ba_ssjl_tjzd:export')")
    @Log(title = "推荐手术", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaSsjlTjzd baSsjlTjzd)
    {
        List<BaSsjlTjzd> list = baSsjlTjzdService.selectBaSsjlTjzdList(baSsjlTjzd);
        ExcelUtil<BaSsjlTjzd> util = new ExcelUtil<BaSsjlTjzd>(BaSsjlTjzd.class);
        util.exportExcel(response, list, "推荐手术数据");
    }

    /**
     * 获取推荐手术详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:ba_ssjl_tjzd:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(baSsjlTjzdService.selectBaSsjlTjzdById(id));
    }

    /**
     * 新增推荐手术
     */
    @PreAuthorize("@ss.hasPermi('system:ba_ssjl_tjzd:add')")
    @Log(title = "推荐手术", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaSsjlTjzd baSsjlTjzd)
    {
        return toAjax(baSsjlTjzdService.insertBaSsjlTjzd(baSsjlTjzd));
    }

    /**
     * 修改推荐手术
     */
    @PreAuthorize("@ss.hasPermi('system:ba_ssjl_tjzd:edit')")
    @Log(title = "推荐手术", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaSsjlTjzd baSsjlTjzd)
    {
        return toAjax(baSsjlTjzdService.updateBaSsjlTjzd(baSsjlTjzd));
    }

    /**
     * 删除推荐手术
     */
    @PreAuthorize("@ss.hasPermi('system:ba_ssjl_tjzd:remove')")
    @Log(title = "推荐手术", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(baSsjlTjzdService.deleteBaSsjlTjzdByIds(ids));
    }
}
