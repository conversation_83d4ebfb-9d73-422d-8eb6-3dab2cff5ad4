package com.ruoyi.system.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.JsxxHisZy;
import com.ruoyi.system.domain.wordReport.*;
import com.ruoyi.system.service.ITjfxService;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageSz;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/word/report")
public class WordReportController extends BaseController {

  @Autowired
  private ITjfxService tjfxService;

  private SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

  private SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");

  @GetMapping("/test")
  public AjaxResult testapi(){
    return AjaxResult.success().put("data", "hello");
  }

  @RequestMapping("/DRGWord/export")
  public ResponseEntity<byte[]> generateDrgWordReport(@RequestParam(value = "startDate",name = "startDate")
                                                      String startDateStr,
                                                      @RequestParam(value = "endDate", name = "endDate")
                                                      String endDateStr) throws ParseException, IOException {
//    FileInputStream fis = new FileInputStream("C:\\Users\\<USER>\\Documents\\BaiduSyncdisk\\ZhiKuY\\杂项\\DRG简报模板.docx");
    ClassPathResource resource = new ClassPathResource("wordtemplates/drgTemplate.docx");
    InputStream is = resource.getInputStream();
    XWPFDocument document = new XWPFDocument(is);

    Date startDate = sdfDateTime.parse(startDateStr);
    Date endDate = sdfDateTime.parse(endDateStr);
    SysUser user = getLoginUser().getUser();
    List<SysRole> roles = user.getRoles();
    String jgid;
    if (roles.stream().anyMatch(item -> "admin".equals(item.getRoleKey()) || "yyAdmin".equals(item.getRoleKey()))) {
      jgid = "admin";
    }else {
      jgid = user.getJgid();
    }

    List<Qyyxzb> qyyxzbList = tjfxService.getQyyxzb(jgid, startDate  , endDate);
    List<Ksyxqk> ksyxqkList = tjfxService.getKsyxqk(jgid, startDate, endDate);
    List<Ysyxqk> ysyxqkList = tjfxService.getYsyxqk(jgid, startDate, endDate);
    List<Ksblqk> ksblqkList = tjfxService.getKsblqk(jgid, startDate, endDate);
    List<Bzqk> bzqkListSource = tjfxService.getBzqk(jgid, startDate, endDate);

    List<Bzqk> bzqkList = new ArrayList<>();
    if (bzqkListSource.size() > 20) {
      List<Bzqk> ksbzList = bzqkListSource.subList(0, 10);
      List<Bzqk> ylbzList = bzqkListSource.subList(bzqkListSource.size() - 10, bzqkListSource.size());
      bzqkList.addAll(ksbzList);
      bzqkList.addAll(ylbzList);
    } else {
      bzqkList.addAll(bzqkListSource);
    }

    ByteArrayOutputStream out = new ByteArrayOutputStream();
    try{
      Qyyxzb qyyxzb = qyyxzbList.get(0); // 获取全院运行指标数据
      Map<String, String> placeholderMap = new HashMap<>();
      Calendar cal = Calendar.getInstance();
      cal.setTime(endDate);
      placeholderMap.put("${dataYear}", String.valueOf(cal.get(Calendar.YEAR)));
      placeholderMap.put("${dataMonth}", String.valueOf(cal.get(Calendar.MONTH) + 1));
      // 添加全院运行情况替换项
      placeholderMap.put("${qybls}", String.valueOf(qyyxzb.getBrs()));
      placeholderMap.put("${qydrgzs}", String.valueOf(qyyxzb.getDrgzs()));
      placeholderMap.put("${qyrzbls}", String.valueOf(qyyxzb.getRzls()));
      placeholderMap.put("${qyzqz}", String.valueOf(qyyxzb.getZqz()));
      placeholderMap.put("${qyrzl}", qyyxzb.getRzl() != null ? qyyxzb.getRzl().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP) + "%" : "N/A");
      placeholderMap.put("${qymdczs}", String.valueOf(qyyxzb.getMdczs()));
      placeholderMap.put("${qyzfy}", String.valueOf(qyyxzb.getZfy()));
      placeholderMap.put("${qyfyxhzs}", String.valueOf(qyyxzb.getFyxhzs()));
      placeholderMap.put("${qyzyk}", String.valueOf(qyyxzb.getYkje()));
      placeholderMap.put("${qysjxhzs}", String.valueOf(qyyxzb.getSjxhzs()));
      placeholderMap.put("${qymqzfy}", String.valueOf(qyyxzb.getMqzfy()));
      placeholderMap.put("${qyrcrsb}", String.valueOf(qyyxzb.getRcrtb())); // 住院人次人数比
      placeholderMap.put("${qypjcr}", String.valueOf(qyyxzb.getBrpjdays()));
      placeholderMap.put("${qycmi}", String.valueOf(qyyxzb.getPjqz()));
      placeholderMap.put("${qyzfl}", qyyxzb.getZfl() != null ? qyyxzb.getZfl().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP) + "%" : "N/A");

      // 处理文档中的所有段落
      for (XWPFParagraph paragraph : document.getParagraphs()) {
        replaceParagraphPlaceholders(paragraph, placeholderMap);
      }
      // 处理文档中的所有表格
      for (XWPFTable table : document.getTables()) {
        for (XWPFTableRow row : table.getRows()) {
          for (XWPFTableCell cell : row.getTableCells()) {
            for (XWPFParagraph paragraph : cell.getParagraphs()) {
              replaceParagraphPlaceholders(paragraph, placeholderMap);
            }
          }
        }
      }

      List<List<String>> ksyxqkResult = new ArrayList<>(
        ksyxqkList.stream()
          .map(Ksyxqk::toResultList)
          .collect(Collectors.toList())
      );
      ArrayList<List<String>> ysyxqkResult = new ArrayList<>(
        ysyxqkList.stream()
          .map(Ysyxqk::toResultList)
          .collect(Collectors.toList())
      );
      ArrayList<List<String>> ksblqkResult = new ArrayList<>(
        ksblqkList.stream()
          .map(Ksblqk::toResultList)
          .collect(Collectors.toList())
      );
      ArrayList<List<String>> bzqkResult = new ArrayList<>(
        bzqkList.stream()
          .map(Bzqk::toResultList)
          .collect(Collectors.toList())
      );

      Map<String, List<List<String>>> tableDataMap = new HashMap<>();
      tableDataMap.put("${ksyx_row}", ksyxqkResult);
      tableDataMap.put("${ysyxqk_row}", ysyxqkResult);
      tableDataMap.put("${ksbltj_row}", ksblqkResult);
      tableDataMap.put("${bzyk_row}", bzqkResult);

      //处理动态行数据
      processTableRows(document, tableDataMap);

      // 保存文档并返回
      document.write(out);
      byte[] documentBytes = out.toByteArray();

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setContentDispositionFormData("attachment", "report.docx");
      return ResponseEntity.ok().headers(headers).body(documentBytes);
    } catch (Exception e) {
      e.printStackTrace();
      return ResponseEntity.internalServerError().body(null);
    } finally {
      is.close();
      out.close();
    }

  }

  // 添加新的辅助方法来处理段落中的占位符
  private void replaceParagraphPlaceholders(XWPFParagraph paragraph, Map<String, String> placeholderMap) {
    String paragraphText = paragraph.getText();
    for (Map.Entry<String, String> entry : placeholderMap.entrySet()) {
      if (paragraphText.contains(entry.getKey())) {
        List<XWPFRun> runs = paragraph.getRuns();
        // 保存原有的文本样式
        XWPFRun styleRun = runs.isEmpty() ? paragraph.createRun() : runs.get(0);
        int fontSize = styleRun.getFontSize();
        String fontFamily = styleRun.getFontFamily();
        boolean bold = styleRun.isBold();

        // 清空段落
        for (int i = runs.size() - 1; i >= 0; i--) {
          paragraph.removeRun(i);
        }

        // 创建新的run并应用样式
        XWPFRun newRun = paragraph.createRun();
        newRun.setText(paragraphText.replace(entry.getKey(), entry.getValue()));
        if (fontSize != -1) newRun.setFontSize(fontSize);
        if (fontFamily != null) newRun.setFontFamily(fontFamily);
        newRun.setBold(bold);
      }
    }
  }

  // 处理表格动态行的方法（之前的代码）
  private void processTableRows(XWPFDocument document, Map<String, List<List<String>>> tableDataMap) {
    for (XWPFTable table : document.getTables()) {
      int templateRowIndex = -1;
      XWPFTableRow templateRow = null;
      String templateName = "";

      // 首先找到模板行
      for (int i = 0; i < table.getRows().size(); i++) {
        XWPFTableRow row = table.getRow(i);
        for (XWPFTableCell cell : row.getTableCells()) {
          String cellText = cell.getText().trim();
          if (tableDataMap.containsKey(cellText)) {
            templateName = cellText;
            templateRow = row;
            templateRowIndex = i;
            break;
          }
        }
        if (templateRow != null) break;
      }

      // 如果找到了模板行
      if (templateRow != null) {
        List<List<String>> data = tableDataMap.get(templateName);
        int insertPosition = templateRowIndex;

        // 为每行数据创建新行
        for (List<String> rowData : data) {
          // 创建新行并复制模板行的属性
          XWPFTableRow newRow = table.insertNewTableRow(insertPosition++);
          newRow.getCtRow().setTrPr(templateRow.getCtRow().getTrPr());

          // 为每个单元格创建并填充数据
          for (int i = 0; i < rowData.size(); i++) {
            XWPFTableCell newCell = null;
            if (i < newRow.getTableCells().size()) {
              newCell = newRow.getCell(i);
            } else {
              newCell = newRow.createCell();
            }

            // 获取模板单元格
            XWPFTableCell templateCell = templateRow.getCell(i);

            // 复制单元格属性
            newCell.getCTTc().setTcPr(templateCell.getCTTc().getTcPr());

            // 清空单元格现有内容
            while (newCell.getParagraphs().size() > 0) {
              newCell.removeParagraph(0);
            }

            // 创建新段落并设置文本
            XWPFParagraph newPara = newCell.addParagraph();
            newPara.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun run = newPara.createRun();
            run.setText(rowData.get(i));
            run.setFontSize(8);
            run.setFontFamily("宋体");
          }
        }

        // 删除模板行
        table.removeRow(insertPosition);
//        break; // 处理完一个表格就退出
      }
    }
  }

  private static void copyCellStyle(XWPFTableCell templateCell, XWPFTableCell newCell) {
    for (int i = 0; i < templateCell.getParagraphs().size(); i++) {
      XWPFParagraph templateParagraph = templateCell.getParagraphs().get(i);
      XWPFParagraph newParagraph = newCell.getParagraphs().get(i);

      // 设置段落样式
      newParagraph.setAlignment(templateParagraph.getAlignment());
      newParagraph.setVerticalAlignment(templateParagraph.getVerticalAlignment());

      for (int j = 0; j < templateParagraph.getRuns().size(); j++) {
        XWPFRun templateRun = templateParagraph.getRuns().get(j);
        XWPFRun newRun = newParagraph.createRun();

        // 复制字体和样式
        newRun.setBold(templateRun.isBold());
        newRun.setItalic(templateRun.isItalic());
        newRun.setFontSize(templateRun.getFontSize());
        newRun.setFontFamily(templateRun.getFontFamily());
        newRun.setColor(templateRun.getColor());
        newRun.setText(templateRun.text());
      }
    }

    // 复制单元格其他样式
    newCell.getCTTc().setTcPr(templateCell.getCTTc().getTcPr());
  }

  public static void setPageSize(XWPFDocument document, long width, long height) {
    CTSectPr sectPr = document.getDocument().getBody().addNewSectPr();
    CTPageSz pgsz = sectPr.isSetPgSz() ? sectPr.getPgSz() : sectPr.addNewPgSz();
    pgsz.setW(BigInteger.valueOf(width));
    pgsz.setH(BigInteger.valueOf(height));
  }
}
