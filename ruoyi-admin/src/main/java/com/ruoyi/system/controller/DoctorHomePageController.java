package com.ruoyi.system.controller;

import java.util.List;

import com.ruoyi.system.domain.vo.DoctorHomePage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.system.service.IDoctorHomePageService;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 医生首页Controller
 *
 * <AUTHOR>
 * @date 2024-02-21
 */
@RestController
@RequestMapping("/tjfx/yssy")
public class DoctorHomePageController extends BaseController {
  @Autowired
  private IDoctorHomePageService doctorHomePageService;

  @GetMapping("/ysztsj")
  public TableDataInfo getYsztsj(DoctorHomePage doctorHomePage) {
    System.out.println(doctorHomePage.getDateType());
    List<DoctorHomePage> list = doctorHomePageService.selectYsztsj(doctorHomePage);
    return getDataTable(list);
  }

  @GetMapping("/ysczjybl")
  public TableDataInfo getYsCzjyBl(DoctorHomePage doctorHomePage) {
    List<DoctorHomePage> list = doctorHomePageService.selectYsCzjyBl(doctorHomePage);
    return getDataTable(list);
  }

  @GetMapping("/ysczjybz")
  public TableDataInfo getYsCzjyBz(DoctorHomePage doctorHomePage) {
    List<DoctorHomePage> list = doctorHomePageService.selectYsCzjyBz(doctorHomePage);
    return getDataTable(list);
  }

  @GetMapping("/ysbllxfx")
  public TableDataInfo getYsBllxfx(DoctorHomePage doctorHomePage) {
    List<DoctorHomePage> list = doctorHomePageService.selectYsBllxfx(doctorHomePage);
    return getDataTable(list);
  }


  @GetMapping("/ysfyjg")
  public TableDataInfo getYsFyjg(DoctorHomePage doctorHomePage) {
    List<DoctorHomePage> list = doctorHomePageService.selectYsFyjg(doctorHomePage);
    return getDataTable(list);
  }

}
