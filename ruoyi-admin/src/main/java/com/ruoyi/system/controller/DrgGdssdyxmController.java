package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.YbjkOption;
import com.ruoyi.system.service.IYbjkOptionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DrgGdssdyxm;
import com.ruoyi.system.service.IDrgGdssdyxmService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 手术项目对应明细Controller
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@RestController
@RequestMapping("/drg/gdssdyxm")
public class DrgGdssdyxmController extends BaseController
{
    @Autowired
    private IDrgGdssdyxmService drgGdssdyxmService;
    @Autowired
    private IYbjkOptionService optionService;

    /**
     * 查询手术项目对应明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DrgGdssdyxm drgGdssdyxm)
    {
        startPage();
        List<DrgGdssdyxm> list = drgGdssdyxmService.selectDrgGdssdyxmList(drgGdssdyxm);
        return getDataTable(list);
    }

    /**
     * 导出手术项目对应明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:gdssdyxm:export')")
    @Log(title = "手术项目对应明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DrgGdssdyxm drgGdssdyxm)
    {
        List<DrgGdssdyxm> list = drgGdssdyxmService.selectDrgGdssdyxmList(drgGdssdyxm);
        ExcelUtil<DrgGdssdyxm> util = new ExcelUtil<DrgGdssdyxm>(DrgGdssdyxm.class);
        util.exportExcel(response, list, "手术项目对应明细数据");
    }

    /**
     * 获取手术项目对应明细详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(drgGdssdyxmService.selectDrgGdssdyxmById(id));
    }

    /**
     * 新增手术项目对应明细
     */
    @Log(title = "手术项目对应明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DrgGdssdyxm drgGdssdyxm)
    {
        return toAjax(drgGdssdyxmService.insertDrgGdssdyxm(drgGdssdyxm));
    }

    /**
     * 修改手术项目对应明细
     */
    @Log(title = "手术项目对应明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DrgGdssdyxm drgGdssdyxm)
    {
        return toAjax(drgGdssdyxmService.updateDrgGdssdyxm(drgGdssdyxm));
    }

    /**
     * 删除手术项目对应明细
     */
    @Log(title = "手术项目对应明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
      YbjkOption option = optionService.selectYbjkOptionByCCode("use_gdssdyxm_delete");
      if (option == null || !"1".equals(option.getcValue())) {
        throw new ServiceException("不允许删除手术对应");
      }
        return toAjax(drgGdssdyxmService.deleteDrgGdssdyxmByIds(ids));
    }
}
