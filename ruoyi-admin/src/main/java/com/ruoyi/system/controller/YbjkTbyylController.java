package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbjkTbyyl;
import com.ruoyi.system.service.IYbjkTbyylService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特病用药情况Controller
 *
 * <AUTHOR>
 * @date 2023-07-29
 */
@RestController
@RequestMapping("/gksz/tbyyl")
public class YbjkTbyylController extends BaseController
{
    @Autowired
    private IYbjkTbyylService ybjkTbyylService;

    /**
     * 查询特病用药情况列表
     */
    @GetMapping("/list")
    public TableDataInfo list(YbjkTbyyl ybjkTbyyl)
    {
        startPage();
        List<YbjkTbyyl> list = ybjkTbyylService.selectYbjkTbyylList(ybjkTbyyl);
        return getDataTable(list);
    }

    /**
     * 导出特病用药情况列表
     */
    @Log(title = "特病用药情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbjkTbyyl ybjkTbyyl)
    {
        List<YbjkTbyyl> list = ybjkTbyylService.selectYbjkTbyylList(ybjkTbyyl);
        ExcelUtil<YbjkTbyyl> util = new ExcelUtil<YbjkTbyyl>(YbjkTbyyl.class);
        util.exportExcel(response, list, "特病用药情况数据");
    }

    /**
     * 获取特病用药情况详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ybjkTbyylService.selectYbjkTbyylById(id));
    }

    /**
     * 新增特病用药情况
     */
    @Log(title = "特病用药情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbjkTbyyl ybjkTbyyl)
    {
        return toAjax(ybjkTbyylService.insertYbjkTbyyl(ybjkTbyyl));
    }

    /**
     * 修改特病用药情况
     */
    @Log(title = "特病用药情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbjkTbyyl ybjkTbyyl)
    {
        return toAjax(ybjkTbyylService.updateYbjkTbyyl(ybjkTbyyl));
    }

    /**
     * 删除特病用药情况
     */
    @Log(title = "特病用药情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ybjkTbyylService.deleteYbjkTbyylByIds(ids));
    }
}
