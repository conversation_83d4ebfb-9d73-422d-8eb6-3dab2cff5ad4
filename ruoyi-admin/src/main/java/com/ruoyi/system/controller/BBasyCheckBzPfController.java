package com.ruoyi.system.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.BBasyCheckBzPf;
import com.ruoyi.system.domain.BaSyjl;
import com.ruoyi.system.domain.BaSyjlError;
import com.ruoyi.system.service.IBBasyCheckBzPfService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/baxy/checkpf")
public class BBasyCheckBzPfController extends BaseController {
  @Autowired
  private IBBasyCheckBzPfService bBasyCheckBzPfService;

  @GetMapping("/list")
  public TableDataInfo list(BBasyCheckBzPf bBasyCheckBzPf) {
    List<BBasyCheckBzPf> list = bBasyCheckBzPfService.selectBBasyCheckBzPfList(bBasyCheckBzPf);
    return getDataTable(list);
  }

  @GetMapping("/baScore")
  public Map<String, Object> getBaScoreCjq(BaSyjlError baSyjlError) {
    BigDecimal scoreCjq = bBasyCheckBzPfService.getBaScoreCjq(baSyjlError);
    Map<String, Object> map = new HashMap<>();
    map.put("data",scoreCjq);
    map.put("code",200);
    return map;
  }

  @GetMapping("/errList")
  public TableDataInfo getErrList(BaSyjlError baSyjlError) {
    List<BaSyjlError> errList = bBasyCheckBzPfService.getErrList(baSyjlError);
    return getDataTable(errList);
  }

}
