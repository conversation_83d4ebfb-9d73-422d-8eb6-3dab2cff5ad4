package com.ruoyi.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.system.domain.vo.CjqBajyExportVo;
import com.ruoyi.system.domain.vo.DeductScoreItemVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.BaSyjlError;
import com.ruoyi.system.service.IBaSyjlErrorService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 质控信息Controller
 *
 * <AUTHOR>
 * @date 2024-03-09
 */
@RestController
@RequestMapping("/baxy/error")
public class BaSyjlErrorController extends BaseController
{
  @Autowired
  private IBaSyjlErrorService baSyjlErrorService;

  /**
   * 查询质控信息列表
   */
  @GetMapping("/list")
  public TableDataInfo list(BaSyjlError baSyjlError)
  {
    List<BaSyjlError> list = baSyjlErrorService.selectBaSyjlErrorList(baSyjlError);
    return getDataTable(list);
  }


  @GetMapping("/listCjq")
  public TableDataInfo listCjq(CjqBajyExportVo cjqBajyExportVo)
  {
    List<CjqBajyExportVo> list = baSyjlErrorService.selectBazkCjq(cjqBajyExportVo);
    return getDataTable(list);
  }


  /**
   * 查看扣分详情
   */
  @GetMapping("/deductScoreDetails")
  public TableDataInfo getDeductScoreDetails(BaSyjlError baSyjlError)
  {
    List<DeductScoreItemVo> list = baSyjlErrorService.getDeductScoreDetails(baSyjlError);
    return getDataTable(list);
  }


  @Log(title = "病案质控数据", businessType = BusinessType.EXPORT)
  @PostMapping("/exportCjq")
  public void exportCjq(HttpServletResponse response, CjqBajyExportVo cjqBajyExportVo)
  {
    List<CjqBajyExportVo> list = baSyjlErrorService.selectBazkCjq(cjqBajyExportVo);
    ExcelUtil<CjqBajyExportVo> util = new ExcelUtil<CjqBajyExportVo>(CjqBajyExportVo.class);
    util.exportExcel(response, list, "病案质控数据");
  }

  /**
   * 导出质控信息列表
   */
  @Log(title = "质控信息", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, BaSyjlError baSyjlError)
  {
    List<BaSyjlError> list = baSyjlErrorService.selectBaSyjlErrorList(baSyjlError);
    ExcelUtil<BaSyjlError> util = new ExcelUtil<BaSyjlError>(BaSyjlError.class);
    util.exportExcel(response, list, "质控信息数据");
  }

  @PostMapping("/exportDeductScoreDetails")
  public void exportDeductScoreDetails(HttpServletResponse response, BaSyjlError baSyjlError)
  {
    List<DeductScoreItemVo> list = baSyjlErrorService.getDeductScoreDetails(baSyjlError);
    ExcelUtil<DeductScoreItemVo> util = new ExcelUtil<DeductScoreItemVo>(DeductScoreItemVo.class);
    util.exportExcel(response, list, "病案评分明细");
  }

  /**
   * 获取质控信息详细信息
   */
  @GetMapping(value = "/{id}")
  public AjaxResult getInfo(@PathVariable("id") Integer id)
  {
    return success(baSyjlErrorService.selectBaSyjlErrorById(id));
  }

  /**
   * 新增质控信息
   */
  @PreAuthorize("@ss.hasPermi('baxy:error:add')")
  @Log(title = "质控信息", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody BaSyjlError baSyjlError)
  {
    return toAjax(baSyjlErrorService.insertBaSyjlError(baSyjlError));
  }

  @Log(title = "质控信息", businessType = BusinessType.INSERT)
  @PostMapping("/addError")
  public Map<String,Object> addError(@RequestBody BaSyjlError baSyjlError)
  {
    Map<String,Object> map = new HashMap<>();
    baSyjlError.setCreatetime(DateUtil.now());
    baSyjlError.setUpdatetime(DateUtil.now());
    map.put("data",baSyjlErrorService.insertBaSyjlError(baSyjlError));
    map.put("code",200);
    return map;
  }

  /**
   * 修改质控信息
   */
  @PreAuthorize("@ss.hasPermi('baxy:error:edit')")
  @Log(title = "质控信息", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody BaSyjlError baSyjlError)
  {
    return toAjax(baSyjlErrorService.updateBaSyjlError(baSyjlError));
  }

  /**
   * 删除质控信息
   */
  @PreAuthorize("@ss.hasPermi('baxy:error:remove')")
  @Log(title = "质控信息", businessType = BusinessType.DELETE)
  @DeleteMapping("/{ids}")
  public AjaxResult remove(@PathVariable Integer[] ids)
  {
    return toAjax(baSyjlErrorService.deleteBaSyjlErrorByIds(ids));
  }
}
