package com.ruoyi.system.controller;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.SettleSsjl;
import com.ruoyi.system.domain.SettleZdxx;
import com.ruoyi.system.domain.vo.FzshVo;
import com.ruoyi.system.domain.vo.SettleContentVo;
import com.ruoyi.system.service.IDrgFzService;
import com.ruoyi.system.service.IDrgFzqService;
import com.ruoyi.system.service.IYbjkOptionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.DrgFzsh;
import com.ruoyi.system.service.IDrgFzshService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import sun.security.provider.certpath.SunCertPathBuilder;

/**
 * DRG维护审核Controller
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Anonymous
@RestController
@RequestMapping("/system/fzsh")
public class DrgFzshController extends BaseController
{
    @Autowired
    private IDrgFzshService drgFzshService;

    @Autowired
    private IYbjkOptionService optionService;

    @Autowired
    private IDrgFzService fzService;

    private String useYbqd = "";

    /**
     * 查询DRG维护审核列表
     */

    @GetMapping("/list")
    public TableDataInfo list(DrgFzsh drgFzsh)
    {
        startPage();
        List<DrgFzsh> list = drgFzshService.selectDrgFzshList(drgFzsh);
        return getDataTable(list);
    }

    @PostMapping("/handleSubmit")
    public AjaxResult handleSubmit(@RequestBody DrgFzsh vo) {
      useYbqd = optionService.getCacheOptionInfo("use_ybqd", "0");
      if("1".equals(useYbqd)) {
        fzService.ApiSaveToList(vo.getJzh());
      } else if ("3".equals(useYbqd)) {
        fzService.ApiSaveToList(vo.getJzh());
      } else if ("10".equals(useYbqd)) {
        try {
          fzService.bltSaveToList(vo.getJzh());
        } catch (SQLException e) {
          throw new RuntimeException(e);
        }
      } else {
        drgFzshService.handleSubmit(vo);
      }
        return success();
    }

    @PostMapping("/handleAudits")
    public AjaxResult handleAudits(@RequestBody FzshVo vo) {
        useYbqd = optionService.getCacheOptionInfo("use_ybqd", "0");
        // 若不是通过或打回的审核结果，则抛出异常
        if (!"1".equals(vo.getStatus()) && !"2".equals(vo.getStatus()) && !"3".equals(vo.getStatus())) {
            throw new ServiceException("数据异常，请重试", 10090);
        }

        // 病历通模式打回时，取消清单保存
        if("10".equals(useYbqd) && "2".equals(vo.getStatus())) {
          try {
            fzService.bltSaveCancel(vo.getJzh());
          } catch (SQLException e) {
            throw new RuntimeException(e);
          }
        }

        String username = getLoginUser().getUsername();
        drgFzshService.handleAudits(vo, username);
        return success();
    }


    /**
     * 首页审查情况
     * @return
     */
    @GetMapping("/syscqk")
    public TableDataInfo syscqk(){
        startPage();
        List<Map<String, Object>> list = drgFzshService.selectScqk();
        return getDataTable(list);
    }

    /**
     * 导出DRG维护审核列表
     */

    @Log(title = "DRG维护审核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DrgFzsh drgFzsh)
    {
        List<DrgFzsh> list = drgFzshService.selectDrgFzshList(drgFzsh);
        ExcelUtil<DrgFzsh> util = new ExcelUtil<DrgFzsh>(DrgFzsh.class);
        util.exportExcel(response, list, "DRG维护审核数据");
    }

    /**
     * 获取DRG维护审核详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(drgFzshService.selectDrgFzshById(id));
    }

    /**
     * 新增DRG维护审核
     */

    @Log(title = "DRG维护审核", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DrgFzsh drgFzsh)
    {
        return toAjax(drgFzshService.insertDrgFzsh(drgFzsh));
    }

    /**
     * 修改DRG维护审核
     */

    @Log(title = "DRG维护审核", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DrgFzsh drgFzsh)
    {
        return toAjax(drgFzshService.updateDrgFzsh(drgFzsh));
    }

    /**
     * 删除DRG维护审核
     */

    @Log(title = "DRG维护审核", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(drgFzshService.deleteDrgFzshByIds(ids));
    }

    /**
     * 保存诊断手术信息
     */
    @PostMapping("/saveZdSs")
    public AjaxResult saveZdSs(@RequestBody SettleContentVo vo)
    {
      drgFzshService.saveSettleContent(vo);
        return success();
    }

}
