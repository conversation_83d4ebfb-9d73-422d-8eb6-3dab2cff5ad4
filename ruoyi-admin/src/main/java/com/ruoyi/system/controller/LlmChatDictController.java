package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.LlmChatDict;
import com.ruoyi.system.service.ILlmChatDictService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * AI对话提示字典Controller
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@RestController
@RequestMapping("/system/llmChat")
public class LlmChatDictController extends BaseController
{
    @Autowired
    private ILlmChatDictService llmChatDictService;

    /**
     * 查询AI对话提示字典列表
     */
    @PreAuthorize("@ss.hasPermi('system:llmChat:list')")
    @GetMapping("/list")
    public TableDataInfo list(LlmChatDict llmChatDict)
    {
        startPage();
        List<LlmChatDict> list = llmChatDictService.selectLlmChatDictList(llmChatDict);
        return getDataTable(list);
    }

    /**
     * 导出AI对话提示字典列表
     */
    @PreAuthorize("@ss.hasPermi('system:llmChat:export')")
    @Log(title = "AI对话提示字典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LlmChatDict llmChatDict)
    {
        List<LlmChatDict> list = llmChatDictService.selectLlmChatDictList(llmChatDict);
        ExcelUtil<LlmChatDict> util = new ExcelUtil<LlmChatDict>(LlmChatDict.class);
        util.exportExcel(response, list, "AI对话提示字典数据");
    }

    /**
     * 获取AI对话提示字典详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:llmChat:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(llmChatDictService.selectLlmChatDictById(id));
    }

    @PostMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody LlmChatDict chatDict) {
      llmChatDictService.updateLlmChatDict(chatDict);
      return success();
    }

    /**
     * 新增AI对话提示字典
     */
    @PreAuthorize("@ss.hasPermi('system:llmChat:add')")
    @Log(title = "AI对话提示字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LlmChatDict llmChatDict)
    {
        return toAjax(llmChatDictService.insertLlmChatDict(llmChatDict));
    }

    /**
     * 修改AI对话提示字典
     */
    @PreAuthorize("@ss.hasPermi('system:llmChat:edit')")
    @Log(title = "AI对话提示字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LlmChatDict llmChatDict)
    {
        return toAjax(llmChatDictService.updateLlmChatDict(llmChatDict));
    }

    /**
     * 删除AI对话提示字典
     */
    @PreAuthorize("@ss.hasPermi('system:llmChat:remove')")
    @Log(title = "AI对话提示字典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(llmChatDictService.deleteLlmChatDictByIds(ids));
    }
}
