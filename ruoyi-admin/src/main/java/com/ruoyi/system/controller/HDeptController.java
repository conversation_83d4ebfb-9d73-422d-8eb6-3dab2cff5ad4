package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HDept;
import com.ruoyi.system.service.IHDeptService;
import org.apache.poi.ss.formula.functions.T;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * hdeptController
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@RestController
@RequestMapping("/system/hdept")
public class HDeptController extends BaseController {
  @Autowired
  private IHDeptService hDeptService;

  /**
   * 查询hdept列表
   */
  @PreAuthorize("@ss.hasPermi('system:hdept:list')")
  @GetMapping("/list")
  public TableDataInfo list(HDept hDept) {
    startPage();
    List<HDept> list = hDeptService.selectHDeptList(hDept);
    return getDataTable(list);
  }

  @GetMapping("/getUserDeptList")
  public AjaxResult getUserDeptList() {
    LoginUser loginUser = getLoginUser();
    SysUser user = loginUser.getUser();
    AjaxResult ajax = AjaxResult.success(user);
    ajax.put("deptList", hDeptService.getUserDeptList(user.getUserId()));
    return ajax;
  }


  @GetMapping("/getUserDeptIdList")
  public AjaxResult getUserDeptIdList() {
    LoginUser loginUser = getLoginUser();
    SysUser user = loginUser.getUser();
    AjaxResult ajax = AjaxResult.success(user);
    ajax.put("deptIdList", hDeptService.getUserDeptIdList(user.getUserId()));
    return ajax;
  }

  @GetMapping("/getUserDeptNameList")
  public AjaxResult getUserDeptNameList() {
    LoginUser loginUser = getLoginUser();
    SysUser user = loginUser.getUser();
    AjaxResult ajax = AjaxResult.success(user);
    ajax.put("deptNameList", hDeptService.getUserDeptNameList(user.getUserId()));
    return ajax;
  }


  @GetMapping("/selectHDeptNameList")
  public TableDataInfo selectHDeptNameList() {
    return getDataTable(hDeptService.selectHDeptNameList());
  }

  /**
   * 获取所有部门列表
   */
  @GetMapping("/listAll")
  public AjaxResult listAll() {
    List<HDept> list = hDeptService.selectHDeptList(new HDept());
    return AjaxResult.success(list);
  }

  /**
   * 导出hdept列表
   */
  @PreAuthorize("@ss.hasPermi('system:hdept:export')")
  @Log(title = "hdept", businessType = BusinessType.EXPORT)
  @PostMapping("/export")
  public void export(HttpServletResponse response, HDept hDept) {
    List<HDept> list = hDeptService.selectHDeptList(hDept);
    ExcelUtil<HDept> util = new ExcelUtil<HDept>(HDept.class);
    util.exportExcel(response, list, "hdept数据");
  }

  /**
   * 获取hdept详细信息
   */
  @PreAuthorize("@ss.hasPermi('system:hdept:query')")
  @GetMapping(value = "/{hDeptId}")
  public AjaxResult getInfo(@PathVariable("hDeptId") Long hDeptId) {
    return success(hDeptService.selectHDeptByHDeptId(hDeptId));
  }

  /**
   * 新增hdept
   */
  @PreAuthorize("@ss.hasPermi('system:hdept:add')")
  @Log(title = "hdept", businessType = BusinessType.INSERT)
  @PostMapping
  public AjaxResult add(@RequestBody HDept hDept) {
    return toAjax(hDeptService.insertHDept(hDept));
  }

  /**
   * 修改hdept
   */
  @PreAuthorize("@ss.hasPermi('system:hdept:edit')")
  @Log(title = "hdept", businessType = BusinessType.UPDATE)
  @PutMapping
  public AjaxResult edit(@RequestBody HDept hDept) {
    return toAjax(hDeptService.updateHDept(hDept));
  }

  /**
   * 删除hdept
   */
  @PreAuthorize("@ss.hasPermi('system:hdept:remove')")
  @Log(title = "hdept", businessType = BusinessType.DELETE)
  @DeleteMapping("/{hDeptIds}")
  public AjaxResult remove(@PathVariable Long[] hDeptIds) {
    return toAjax(hDeptService.deleteHDeptByHDeptIds(hDeptIds));
  }
}
