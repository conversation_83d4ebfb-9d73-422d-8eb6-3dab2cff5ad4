package com.ruoyi.system.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import com.ruoyi.tools.YlfkfsEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import javax.sql.DataSource;

@RestController
@Anonymous
@RequestMapping("/drg/fzqcn")
public class DrgcnDrgFzqController extends BaseController {

  @Autowired
  private IBaSyjlService baSyjlService;
  @Autowired
  private IBrxxService brxxService;
  @Autowired
  private ISsmlService ssmlService;
  @Autowired
  private IDrgdictService drgdictService;
  @Autowired
  private IDrgFzqService drgFzqService;
  @Autowired
  private IFyxxService fyxxService;
  @Autowired
  private IBaBrzdxxService baBrzdxxService;
  @Autowired
  private IBaSsjlService baSsjlService;
  @Autowired
  private DataSource nativeDataSource;
  @Autowired
  private IYbjkOptionService ybjkOptionService;
  @Autowired
  private ISysTenantService tenantService;
  @Autowired
  private DrgFzq11Controller drgFzq11Controller;


  static Map<String, String[]> togetherIcdMap = null;
  static final String ADRG_SURGTABLE_INDEX11 = "NA1=1|2&3,JA1=1&2|1&3&4,JA2=1|2&3,FB1=1&2,FE1=1&2,FF2=1&2";
  static final List<String> TOGETHER_OPER_ADRG = Arrays.asList("NA1","JA1","JA2","IB1","FB1","FE1","FF2");  //需要同时包含多个手术的adrg
  static final List<String> TCM_ADRG_GROUP = Arrays.asList("IR1", "IR2", "IS1", "IS2", "IS2", "IS2", "IU1", "IU2", "IU2", "IZ2");

  String is_drgfz_ver = "2.0";
  String drg_use20 = "";
  //  String is_drgfz_ver = "2.0";
  String ls_one_flag = "1";
//  String[] ls_bqy_ssarr = "血液透析;导管插入术;穿刺;引流;检查;闭合术;镇痛;复位术;灌注;药物注入;机械性通气;诊断性操作;内镜下;造影;镇痛;阻滞术;诊断性操作".split(";");

  String[] ls_bqy_ssarr = "血液透析".split(";");


  String ls_dqxadrg = "LA;MA;FD1;SB;BC;RB;";// 低权限的ADRG，当有后面的，则删除
//  String ls_gqxadrg = "NF1";

  String ls_region = null;
  String ls_hissoftname = null;

  String ls_yfz_cndrg=null;

  // 全部是手术的adrg
//  String ls_qbss_adrgbh = "AA1,AB1,AC1,AD1,AE1,AF1,AG1,AG2,AH1,BB2,BC2,BD1,BD2,BJ1,BL1,BM1,CB1,DB1,DB2,DB3,DC1,DC2,DD1,DD2,DE1,DE2,DG1,DG2,DJ1,DK1,FB1,FD3,FE1,FF2,FK2,FK3,FL2,FL3,FM1,FM2,FM3,FM4,FN1,FN2,GD2,GE1,GE2,GF1,GF2,GG1,GJ1,GK1,GK2,GK3,IB2,IB3,IC1,IC2,IC3,IC4,ID1,IE1,IF1,IF2,IF3,IF4,IF5,IG1,IH1,IJ1,JA1,JA2,JB2,LB1,LB2,LC1,LD1,LE1,LF1,LJ1,LL1,MB1,MC1,MD1,MJ1,NA1,PB1,PJ1,PS1,PS2,PS3,PS4,TB1,SB1,WJ1,XJ1,ZB1,ZC1,ZD1,ZJ1,NF1";
  String ls_qbss_adrgbh =  "AA1,AB1,AC1,AD1,AE1,AF1,AG1,AG2,AH1,BB2,BC2,BD1,BD2,BE1,BE2,BJ1,BL1,BM1,CB1,CB2,CB3,CB4,CC1,CD1,CD2,CJ1,DB1,DB2,DB3,DC1,DC2,DD1,DD2,DE1,DE2,DG1,DG2,DJ1,DK1,EB1,EB2,EC1,EC2,ED1,EJ1,FB1,FB2,FC1,FD1,FD2,FD3,FE1,FE2,FF1,FF2,FF3,FJ1,FK2,FK3,FL2,FL3,FM1,FM2,FM3,FM4,FN1,FN2,GB1,GB2,GC1,GC2,GD2,GE1,GE2,GF1,GF2,GG1,GJ1,GK1,GK2,GK3,HB1,HC1,HC2,HC3,HJ1,HK1,HL1,HL2,IB2,IB3,IC1,IC2,IC3,IC4,ID1,IE1,IF1,IF2,IF3,IF4,IF5,IG1,IH1,IJ1,JB1,JB2,JB3,JC1,JD1,JD2,JJ1,KB1,KC1,KD1,KD2,KE1,KJ1,LB1,LB2,LC1,LD1,LE1,LF1,LJ1,LL1,MB1,MC1,MD1,MJ1,NB1,NC1,ND1,NE1,NF1,NJ1,OB1,OC1,OD1,OD2,OJ1,PB1,PC1,PJ1,PK1,QB1,QJ1,RD1,VB1,VC1,VJ1,WC1,ZB1,ZC1,ZD1,ZJ1";
  String ls_zce_bzbm = "P07.300x021,P07.200,P07.200x011,P07.200x021,P07.300,P07.300x001,P07.300x011"; //早产儿诊断

  String ls_qbss_adrgbh20="AA1,AA2,AB1,AE1,AF1,AG1,AG2,AG3,AH1,BB1,BB5,BC1,BD1,BD2,BE1,BE2,BJ1,BL1,BM1,CB1,CB2,CB3,CB4,CB5,CB6,CB7,CD1,CD2,CD3,CJ1,DB1,DB2,DC1,DC2,DC3,DD1,DD2,DE1,DE2,DF1,DF2,DG1,DG2,DH4,DJ1,DK1,EB1,EB2,EC1,EC2,ED1,EJ1,EK1,FB1,FB2,FC1,FD1,FD2,FE1,FE2,FF1,FF2,FF3,FJ1,FK1,FK3,FK4,FL1,FL3,FL4,FM1,FM2,FM3,FM4,FM5,FN1,FN2,GB1,GB2,GB3,GC1,GC2,GC3,GD1,GE1,GE2,GF1,GF2,GF3,GG1,GG2,GH1,GJ1,GK1,GK2,GK3,HB1,HC1,HC2,HC3,HJ1,HK1,HL1,HL2,IB2,IB3,IC1,IC2,IC3,IC4,IE1,IE2,IE3,IE4,IE5,IE6,IF1,IG1,IH1,IJ1,JB1,JB2,JB3,JC1,JD1,JD2,JJ1,KB1,KC1,KD1,KD2,KE1,KJ1,LB1,LB2,LC1,LD1,LE1,LF1,LJ1,LL1,MB1,MC1,MD1,MJ1,NB1,NC1,ND1,NE1,NF1,NJ1,OB1,OC1,OC2,OD1,OD2,OJ1,PB1,PC1,PD1,PJ1,PK1,QB1,QJ1,RL1,RL2,RM1,VB1,VC1,VJ1,ZB1,ZC1,ZD1,ZJ1";

//  if (is_drgfz_ver.equals("2.0")) {
//
//  }


  // 未知参数
  String is_lcfz_cxtbba, ls_pcflag;

  List<Drgdict> ids_drg_dict = new ArrayList<>();

  List<Drgdict> ids_cndrg_dict = new ArrayList<>();

  List<DrgBfz> ids_drg_bfz = new ArrayList<>();
  List<DrgBfz> ids_drg_bfz_pc = new ArrayList<>();
  List<Adrgbzss> ids_drg_adrbzss = new ArrayList<>();
  List<DrgAdrgFz> ids_drg_adrg_fz = new ArrayList<>();


  String hasMultipleOrg = null;

  String checkSaveList = null;

  /**
   * 分组编码命名规则： 第一位：A-Z 代表26个MDC MDC编码的尾字符 第二位：DRG组类型：A-J外科 K-Q非手术室 R-Z内科 第三位：1-9
   * DRG组的顺序码 第四位：“1”表示伴有严重并发症与合并症 “3”表示伴有并发症与合并症 “5”表示不伴并发症与合并症 “7”表示死亡或转院
   * “9”表示未作区分的情况 前三位：ADRG核心疾病诊断相关分组 DRG编码：在ADRG的基础上根据并发症离院方式等进行细分
   *
   * @param drgfz
   * @return
   */
  @RequestMapping("/bdfzjson")
  public DrgFzResult fzqjosn(Drgfz drgfz) {
//	System.out.println(drgfz.toString());
    return fzq(drgfz);
  }

  @RequestMapping("/bdfztxt")
  public String bdfztxt(Drgfz drgfz) {
    DrgFzResult result = fzq(drgfz);
    String ls_drg;
    ls_drg = "DRG编号:" + result.getDrgbh();
    ls_drg = ls_drg + "|" + "CMI:" + result.getZfqz();
    ls_drg = ls_drg + "|" + "总费用:" + result.getZfy();
    ls_drg = ls_drg + "|" + "分组类别:" + result.getFztype();
    ls_drg = ls_drg + "|" + "标杆费用:" + result.getZfbz();
    ls_drg = ls_drg + "|" + "DRG名称:" + result.getDrgmc();
    ls_drg = ls_drg + "|" + "住院天数:" + result.getZydays();
    ls_drg = ls_drg + "|" + "标杆床日:" + result.getPjdays();
    return ls_drg;
  }



  public DrgFzResult fzq(Drgfz drgfz) {
	 String orgLevel = null;
    if (ls_region == null) {
      YbjkOption ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("region");
      if (ybjkOption != null) {
        ls_region = ybjkOption.getcValue();
      } else {
        ls_region = "重庆";
      }
    }

    if ("".equals(drg_use20)) {
      drg_use20 = ybjkOptionService.getOptionInfo("drg_use20", "0");
    }

//    if(drgfz.getFzqver()==null) {
//    	drgfz.setFzqver("");
//    }

    Brxx br = brxxService.selectBrxxByBrbs(drgfz.getBrbs());
    if(!"新疆".equals(ls_region) &&
      br != null && br.getCydate() != null &&!"2".equals(drgfz.getFzqver())&&
      "0".equals(drg_use20) &&
      br.getCydate().before(Date.from(LocalDateTime.of(2025, 7, 1,0, 0, 0).atZone(ZoneId.systemDefault()).toInstant()))) {
      return drgFzq11Controller.fzq(drgfz);
    }

    if (hasMultipleOrg == null) {
      hasMultipleOrg = ybjkOptionService.getOptionInfo("use_multi_tenant", "0");
    }

    if (orgLevel == null) {
    	orgLevel = ybjkOptionService.getOptionInfo("yydj", "2");
      }

    if (checkSaveList == null) {
      checkSaveList = ybjkOptionService.getOptionInfo("check_save_list", "0");
    }


    if (ls_yfz_cndrg == null) {
    	ls_yfz_cndrg = ybjkOptionService.getOptionInfo("yfz_cndrg", "0");
     }




    if (is_drgfz_ver == null) {
//      is_drgfz_ver = ybjkOptionService.getOptionInfo("drgfz_ver","1.1");
//      if (!"1.1".equals(is_drgfz_ver) && !"2.0".equals(is_drgfz_ver)) {
//        is_drgfz_ver = "1.1";
//      }
      is_drgfz_ver = "2.0";
    }

    if (ids_drg_dict == null || ids_drg_dict.size() == 0) {
      Drgdict drgdict = new Drgdict();
      if ("2.0".equals(is_drgfz_ver)) {
        drgdict.setUseflag("1");
      }
      if ("新疆".equals(ls_region)) {
        ids_drg_dict = drgdictService.selectDrgdictListXj(drgdict);
      } else {
        ids_drg_dict = drgdictService.selectDrgdictList(drgdict);
      }

      ids_drg_dict.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getDrgbh()) || StringUtils.isBlank(obj.getDrgmc()));
    }

    if("1".equals(ls_yfz_cndrg)&&((ids_cndrg_dict == null || ids_cndrg_dict.size() == 0))) {
    	 Drgdict drgdict1 = new Drgdict();
    	ids_cndrg_dict = drgdictService.selectcnDrgdictList(drgdict1);
    }



    if ("1".equals(checkSaveList)) {
      //判断清单是否保存
      String brbs = drgfz.getBrbs();
      Brxx brxx = brxxService.selectBrxxByBrbs(brbs);
      if (brxx != null && brxx.getShzt() == 1) {

        //判断是否入组
        BaSyjl basy = baSyjlService.selectBaSyjlByBrbs(brbs);
        if (basy != null) {
          String drgbh = basy.getDrgbh();
          if (StringUtils.isBlank(drgbh) || drgbh.contains("000") || drgbh.contains("QY")) {
            //未入组 继续分组
            logger.info("未入组 继续分组");
          } else if ("yfz".equals(drgfz.getSource())){
            //手动分组 继续分组
            logger.info("手动分组 继续分组");
          } else {
            logger.info("清单已保存，且非手动分组，且病案已入组");
            //清单已保存，且非手动分组，且病案已入组
            Drgdict drgdict = getDrgDictByDrgbh(drgbh);
            DrgFzResult drgFzResult = new DrgFzResult();
            drgFzResult.setBah(basy.getBah());
            drgFzResult.setBrbs(basy.getBrbs());
            drgFzResult.setCode(200);
            drgFzResult.setMessage("更新成功");
            drgFzResult.setDrgbh(basy.getDrgbh());
            drgFzResult.setZfqz(drgdict.getZfqz());
            drgFzResult.setZfy(basy.getZfy());
            drgFzResult.setFztype(drgdict.getFztype());
            drgFzResult.setZfbz(basy.getZfbz());
            drgFzResult.setDrgmc(basy.getDrgmc());
            drgFzResult.setZydays(BigDecimal.valueOf(basy.getSjzyts()));
            drgFzResult.setPjdays(drgdict.getPjdays());
            return drgFzResult;
          }
        }

      } else {
        logger.info("未保存，继续执行分组");
        //未保存，继续执行分组
      }

    }

    //如果是多机构，每次重新取
    if ("1".equals(hasMultipleOrg)) {
      orgLevel = tenantService.selectOrgLevel(drgfz.getBrbs());
    }

    if (togetherIcdMap == null) {
      setTogetherIcdMap();
    }


    if (ls_hissoftname == null) {
      YbjkOption ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("hissoftname");
      if (ybjkOption != null) {
        ls_hissoftname = ybjkOption.getcValue();
      }
    }


    if (is_drgfz_ver.equals("2.0")) {
      ls_qbss_adrgbh = ls_qbss_adrgbh20;
    }

    List<BaSyjl> ids_syjl_all = new ArrayList<>();

    List<DrgAdrgFz> ids_get_adrg_hiv = new ArrayList<>();
    List<DrgAdrgFz> ids_check_qy = new ArrayList<>();
    List<DrgAdrgFz> ids_get_adrg = new ArrayList<>();
    List<DrgAdrgFz> ids_get_adrg_old = new ArrayList<>();

    String ls_ssbm1 = "";
    String ls_ss_subname = "";
    String ls_zdmc = "";
    String ls_ss_name = "";
    String ls_adrgbh = "";
    String ls_sslb = "";
    String ls_mdcfirst = "";
    String ls_icdtype = "";
    String ls_icdtype1 = "";
    String ls_type = "";
    String ls_tj_zdss = "";
    String ls_yb_flag = "";
    String ls_drgbh = "";
    String[] ls_bfz = new String[]{};
    String[] ls_ssjlarr = new String[]{};
    String[] ls_allzd = new String[]{};
    String[] ls_allss = new String[]{};
    String[] ls_mdcarr = new String[]{};

    if (ids_syjl_all == null) {
      ids_syjl_all = new ArrayList<>();
    } else {
      ids_syjl_all.clear();
    }

//    YbjkOption drgfzVer = ybjkOptionService.selectYbjkOptionByCCode("drgfz_ver");
//
//    if (drgfzVer == null || StrUtil.isEmpty(drgfzVer.getcValue())) {
//      is_drgfz_ver = "1.0";
//    }

    String ls_brbs = "";
    String ls_jlly = "";
    String ls_jbdm = "";
    Long ls_nl = null;
    String ls_sex = "";
    Long ls_bzyzsnl = null;
    BigDecimal ls_xserytz = null;
    BigDecimal ls_xsecstz = null;
    String ls_time = "";
    String ls_lyfs = "";
    Long ls_sjzyts = null;
    BigDecimal ls_zfy = new BigDecimal("0");
    String ls_jbdm1_15 = "";
    String ls_ssbm1_7 = "";
    String ls_not_tj = "";
    String ls_bah = "";
    String is_zfbz_qfzgjm = "";
    String ls_org = "";

    if ("1".equals(ls_one_flag)) {
      ls_brbs = drgfz.getBrbs();
      ls_jlly = drgfz.getJlly();
      ls_jbdm = drgfz.getZzdbm();
      ls_nl = drgfz.getNl();
      ls_sex = drgfz.getXb();
      ls_bzyzsnl = drgfz.getBzyzsnl();
      ls_xserytz = drgfz.getXsecstz();
      ls_xsecstz = drgfz.getXsecstz();
      ls_time = drgfz.getTime();
      ls_lyfs = drgfz.getLyfs();
      ls_sjzyts = drgfz.getSjzyts();
      ls_zfy = drgfz.getZfy();
      ls_jbdm1_15 = drgfz.getZdxx();
      ls_ssbm1_7 = drgfz.getSsxx();
      ls_not_tj = drgfz.getNottj();
      ls_bah = drgfz.getBah();
      is_zfbz_qfzgjm = drgfz.getYlfkfs();
      ls_yb_flag = drgfz.getYbflag();
      ls_org = drgfz.getOrg();

      // 处理+号转为空串的问题
      if (ls_jbdm != null) {
        if (ls_jbdm.indexOf(" ") > 0) {
          ls_jbdm = ls_jbdm.replace(" ", "+");
        }
      }
      // 处理+号转为空串的问题
      if (ls_jbdm1_15 != null) {
        if (ls_jbdm1_15.indexOf(" ") > 0) {
          ls_jbdm1_15 = ls_jbdm1_15.replace(" ", "+");
        }
      }

      // 处理+号转为空串的问题
      if (ls_ssbm1_7 != null) {
        if (ls_ssbm1_7.indexOf(" ") > 0) {
          ls_ssbm1_7 = ls_ssbm1_7.replace(" ", "+");
        }
      }

      if (ls_yb_flag == null || "".equals(ls_yb_flag)||"zlbh".equals(ls_hissoftname)) {
        ls_yb_flag = "0";
      }



      if (is_zfbz_qfzgjm != null && !"".equals(is_zfbz_qfzgjm)) {
        if (is_zfbz_qfzgjm.indexOf("职工") > -1) {
          is_zfbz_qfzgjm = "1";
        } else {
          is_zfbz_qfzgjm = "0";
        }
      }

//      System.out.println("0----------------------分组信息-------------------------");
//      System.out.println("主要诊断：" + ls_jbdm);
//      System.out.println("其他诊断：" + ls_jbdm1_15);
//      System.out.println("手术信息：" + ls_ssbm1_7);
//      System.out.println("0-----------------------------------------------------");

      // 如果病案号不为空但病人标识为空：根据病案号获取病人标识
      if ("1".equals(is_lcfz_cxtbba) && ls_bah != null && !"".equals(ls_bah)
        && (ls_brbs == null || "".equals(ls_brbs))) {
        BaSyjl baSyjl = new BaSyjl();
        baSyjl.setBah(ls_bah);
        List<BaSyjl> baSyjls = baSyjlService.selectBaSyjlList(baSyjl);
        if (baSyjls.size() > 0) {
          ls_brbs = baSyjls.get(0).getBrbs();
        }

        if (ls_brbs != null && !"".equals(ls_brbs)) {
//          ino_trans_data.of_save_basy(ls_brbs,datetime(date('2000-01-01'),now()),'0','',0,his)
        }
      }

      // 每次都同步
      if ("1".equals(is_lcfz_cxtbba) && ls_brbs != null && !"".equals(ls_brbs)) {
        if (ls_brbs != null && !"".equals(ls_brbs)) {
//          ino_trans_data.of_save_basy(ls_brbs,datetime(date('2000-01-01'),now()),'0','',0,his)
        }
      }

      // 如果诊断信息、手术信息不为空，获取其对应的医保诊断信息
      if ("1".equals(ls_yb_flag)) {
        // 医保码是不转
      } else {
        if (!"".equals(ls_jbdm) && ls_jbdm != null) {
          String icdbh = baSyjlService.selectYbicdbh(new Drgfz(ls_jbdm, "icd10"));
          if (icdbh != null && !"".equals(icdbh)) {
            ls_jbdm = icdbh;
          }
        }
        if (!"".equals(ls_jbdm1_15) && ls_jbdm1_15 != null) {
          String icdbh = baSyjlService.selectYbicdbh(new Drgfz(ls_jbdm1_15, "icd10"));
          if (icdbh != null && !"".equals(icdbh)) {
            ls_jbdm1_15 = icdbh;
          }
        }

        if (!"".equals(ls_ssbm1_7) && ls_ssbm1_7 != null) {
          String icdbh = baSyjlService.selectYbicdbh(new Drgfz(ls_ssbm1_7, "icd9"));
          if (icdbh != null && !"".equals(icdbh)) {
            ls_ssbm1_7 = icdbh;
          }
        }
      }

      // 如果手术信息不为空，获取首个手术信息
      if (!"".equals(ls_ssbm1_7) && ls_ssbm1_7 != null) {
        if (ls_ssbm1_7.indexOf(',') > -1) {
          ls_ssbm1 = ls_ssbm1_7.substring(0, ls_ssbm1_7.indexOf(','));
        } else {
          ls_ssbm1 = ls_ssbm1_7;
        }
      }

      if ("".equals(ls_jlly) || ls_jlly == null) {
        ls_jlly = "1";
      }

      // 如果病人标识为空且诊断信息为空，则返回
      if (("".equals(ls_brbs) || ls_brbs == null) && ("".equals(ls_jbdm) || ls_jbdm == null)) {
        return new DrgFzResult(-1, "病人标识不能为空");
      }

      // 如果病人标识不为空
      if (ls_brbs != null && !"".equals(ls_brbs) && ("".equals(ls_jbdm) || ls_jbdm == null)) {
        BaSyjl baSyjl = new BaSyjl();
        baSyjl.setBrbs(ls_brbs);
//        baSyjl.setJlly(ls_jlly);
        // 根据病人标识和记录来源获取符合条件的首页信息
        ids_syjl_all = baSyjlService.selectFzData(baSyjl);
        // 如果存在符合条件的记录，且获取信息为空，传入的参数不为空，则将查询到的记录赋值为传入的参数
        if (ids_syjl_all.size() > 0) {
          if ((ids_syjl_all.get(0).getJbdm() == null || "".equals(ids_syjl_all.get(0).getJbdm()))
            && !"".equals(ls_jbdm) && ls_jbdm != null) {
            ids_syjl_all.get(0).setJbbm(ls_jbdm);
          }
          if ((ids_syjl_all.get(0).getQtzd() == null || "".equals(ids_syjl_all.get(0).getQtzd()))
            && !"".equals(ls_jbdm1_15) && ls_jbdm1_15 != null) {
            ids_syjl_all.get(0).setQtzd(ls_jbdm1_15);
          }
          if ((ids_syjl_all.get(0).getSsjl() == null || "".equals(ids_syjl_all.get(0).getSsjl()))
            && !"".equals(ls_ssbm1_7) && ls_ssbm1_7 != null) {
            ids_syjl_all.get(0).setSsjl(ls_ssbm1_7);
          }
          if ((ids_syjl_all.get(0).getZfy() == null || "0".equals(ids_syjl_all.get(0).getZfy()))
            && !"".equals(ls_zfy) && ls_zfy != null) {
            ids_syjl_all.get(0).setZfy(ls_zfy);
          }
        }
      } else { // 如果病人标识为空，则将参数信息包装成首页记录，添加到集合中
        BaSyjl baSyjl = new BaSyjl();
        if (ls_brbs != null)
          baSyjl.setBrbs(ls_brbs);
        if (ls_sex != null)
          baSyjl.setXb(ls_sex);
        if (ls_jbdm != null)
          baSyjl.setJbdm(ls_jbdm);
        if (ls_nl != null)
          baSyjl.setNl(ls_nl);
        if (ls_bzyzsnl != null)
          baSyjl.setBzyzsnl(ls_bzyzsnl);
        if (ls_xserytz != null)
          baSyjl.setXserytz(ls_xserytz);
        if (ls_xsecstz != null)
          baSyjl.setXsecstz(ls_xsecstz);
        if (ls_lyfs != null)
          baSyjl.setLyfs(ls_lyfs);
        if (ls_zfy != null)
          baSyjl.setZfy(ls_zfy);
        if (ls_sjzyts != null)
          baSyjl.setSjzyts(ls_sjzyts);
        if (ls_jbdm1_15 != null)
          baSyjl.setQtzd(ls_jbdm1_15);
        if (ls_ssbm1_7 != null)
          baSyjl.setSsjl(ls_ssbm1_7);
        if (ls_ssbm1 != null)
          baSyjl.setSsjczbm1(ls_ssbm1);
        if (ls_bah != null)
          baSyjl.setBah(ls_bah);
        ids_syjl_all.add(baSyjl);
      }

      if (ids_syjl_all.size() == 0) {
        return new DrgFzResult(-1, "没有找到此病人的信息");
      } else {
        if (ls_jbdm == null || "".equals(ls_jbdm)) {
          ls_jbdm = ids_syjl_all.get(0).getJbdm();
        }
        if (ls_jbdm == null || "".equals(ls_jbdm)) {
          return new DrgFzResult(-1, "没有找到此病人的诊断信息");
        }
      }
    }

    ls_pcflag = "0";

    DrgBfz drgBfz = new DrgBfz();
    drgBfz.setVersion(is_drgfz_ver);
//    if ("2.0".equals(is_drgfz_ver)) {
//    }
    if (ids_drg_bfz == null || ids_drg_bfz.size() == 0) {
      ids_drg_bfz = drgFzqService.selectDrgBfzList(drgBfz);
    }
    if (ids_drg_bfz_pc == null || ids_drg_bfz_pc.size() == 0) {
      ids_drg_bfz_pc = drgFzqService.selectDrgBfzpcList(drgBfz);
    }
    if (ids_drg_adrbzss == null || ids_drg_adrbzss.size() == 0) {
      ids_drg_adrbzss = drgFzqService.selectAdrgbzssList();
    }
    if (ids_drg_adrg_fz == null || ids_drg_adrg_fz.size() == 0) {
      ids_drg_adrg_fz = "1.1".equals(is_drgfz_ver) ? drgFzqService.selectDrgAdrgFzAll() : drgFzqService.selectDrgAdrgFzAll20();
    }

//    of_fz_init();

    DrgAdrgFz drgAdrgFz = new DrgAdrgFz();

    for (int ll_i = 0; ll_i < ids_syjl_all.size(); ll_i++) {

      ls_drgbh = "";
      String ls_xqfz_flag = "";
      ls_mdcarr = new String[]{};

      ls_bah = ids_syjl_all.get(ll_i).getBah();
      ls_brbs = ids_syjl_all.get(ll_i).getBrbs();
      ls_sex = ids_syjl_all.get(ll_i).getXb();
      ls_jbdm = ids_syjl_all.get(ll_i).getJbdm();
      ls_nl = ids_syjl_all.get(ll_i).getNl();
      ls_bzyzsnl = ids_syjl_all.get(ll_i).getBzyzsnl();
      ls_xserytz = ids_syjl_all.get(ll_i).getXserytz();
      ls_xsecstz = ids_syjl_all.get(ll_i).getXsecstz();
      ls_lyfs = ids_syjl_all.get(ll_i).getLyfs();
      ls_zfy = ids_syjl_all.get(ll_i).getZfy();
      ls_sjzyts = ids_syjl_all.get(ll_i).getSjzyts();
      ls_jbdm1_15 = ids_syjl_all.get(ll_i).getQtzd();
      ls_ssbm1_7 = ids_syjl_all.get(ll_i).getSsjl();
      ls_ssbm1 = ids_syjl_all.get(ll_i).getSsjczbm1();

      // 处理佳华医院当，有该操作时，无法入组的情况
      if (ls_ssbm1 != null) {
        if ("17.91100".equals(ls_ssbm1.trim())) {
          ls_ssbm1 = "";
        }
        if ("".equals(ls_ssbm1.trim())) {
          ls_ssbm1 = "####";
        }
      }
      //取实际住院天数
      if (ls_sjzyts == null||ls_sjzyts == 0||ls_sjzyts==1) {
        ls_sjzyts = 10L;
        if (ls_brbs!=null) {
          if(ls_brbs.contains("_")==true) {
            Brxx brxx1 =  brxxService.selectBrxxByBrbs(ls_brbs);
            if (brxx1!=null) {
              //在院
              if ("1".equals(brxx1.getZyzt())) {
                // 计算当前时间与给定时间相差的天数
                try {
                  ls_sjzyts  = DateUtil.between(brxx1.getRydate(), new Date(), DateUnit.DAY);
                } catch (Exception e) {
                  // TODO: handle exception
                }
              }
              //出院
              if ("0".equals(brxx1.getZyzt())) {
                // 计算当前时间与给定时间相差的天数
                try {
                  ls_sjzyts  = DateUtil.between(brxx1.getRydate(), brxx1.getCydate(), DateUnit.DAY);
                } catch (Exception e) {
                  // TODO: handle exception
                }
              }
            }
          }
        }

      }

      if ("".equals(ls_lyfs) || ls_lyfs == null) {
        ls_lyfs = "1";
      }

      if (!"".equals(ls_jbdm1_15)) {
        ls_bfz = ls_jbdm1_15.split(","); // 诊断信息数组
        ls_allzd = new String[ls_bfz.length + 1];
        for (int i = 0; i < ls_bfz.length; i++) {
          ls_allzd[i] = ls_bfz[i];
        }
        ls_allzd[ls_allzd.length - 1] = ls_jbdm;
      } else {
        ls_allzd = new String[1];
        ls_allzd[0] = ls_jbdm;
      }
      ls_ssjlarr = ls_ssbm1_7.split(","); // 手术信息数组
      ls_allss = ls_ssjlarr;

      if (ls_allss.length == 0) {
        ls_allss = new String[1];
        ls_allss[0] = "####";
      }



      // 处理HIV---主要诊断或其他诊断为HIV的病例分入MDCY（HIV感染疾病及相关操作）
      if (ids_get_adrg_hiv != null) {
        ids_get_adrg_hiv.clear();
      }
      ids_get_adrg_hiv = getDrgAdrgFzHiv(ls_allzd);
      if (ids_get_adrg_hiv.size() > 0) {
        ls_xqfz_flag = "1";

        long ll_have = -1;

        for (DrgAdrgFz adrgFz : ids_get_adrg_hiv) {
          if ("YC1".equals(adrgFz.getAdrgbh())||"YR2".equals(adrgFz.getAdrgbh())||"YR1".equals(adrgFz.getAdrgbh())) {

            ll_have = ids_get_adrg_hiv.indexOf(adrgFz);
            if (ll_have > -1) {
              DrgAdrgFz drgadrgfz2= new DrgAdrgFz();
              drgadrgfz2.setIcdbh(ls_ssbm1);
              ll_have = countDrgAdrgFz(ls_ssbm1);

              if (ll_have>0)
              {
                ls_drgbh = "YC1";
                break;
              }
              else
              {
                ls_drgbh = adrgFz.getAdrgbh();
              }
            }

          }
        }

        //如果是1.1 ,单独处理RB1,RB2,如果有HIV的分组，则不进入
        if ("1.1".equals(is_drgfz_ver)&&"".equals(ls_drgbh)) {
          for (DrgAdrgFz adrgFz : ids_get_adrg_hiv) {
            if ("RB1".equals(adrgFz.getAdrgbh())||"RB2".equals(adrgFz.getAdrgbh())) {
              ll_have = ids_get_adrg_hiv.indexOf(adrgFz);
              if (ll_have > -1&&(ls_ssbm1_7.indexOf("00.1500x001")>-1
                ||ls_ssbm1_7.indexOf("00.1500x002")>-1
                ||ls_ssbm1_7.indexOf("99.2500x017")>-1
                ||ls_ssbm1_7.indexOf("99.2500x036")>-1
                ||ls_ssbm1_7.indexOf("99.2500x037")>-1
                ||ls_ssbm1_7.indexOf("99.2502")>-1
                ||ls_ssbm1_7.indexOf("99.2503")>-1
                ||ls_ssbm1_7.indexOf("99.2504")>-1
                ||ls_ssbm1_7.indexOf("99.2505")>-1
                ||ls_ssbm1_7.indexOf("99.2506")>-1
                ||ls_ssbm1_7.indexOf("99.2801")>-1
                ||ls_ssbm1_7.indexOf("99.2800x003")>-1)) {
                ls_drgbh = adrgFz.getAdrgbh();

              }
            }
          }
        }

//        if (ll_have > -1) {
//          ls_drgbh = "YC1";
//        } else {
//          for (DrgAdrgFz adrgFz : ids_get_adrg_hiv) {
//            if ("YR2".equals(adrgFz.getAdrgbh())) {
//              ll_have = ids_get_adrg_hiv.indexOf(adrgFz);
//            }
//          }
//
//          if (ll_have > -1) {
//            ls_drgbh = "YR2";
//          } else {
//            ls_drgbh = "YR1";
//          }
//        }
      }

//      System.out.println("0----------------------------------drgbh:" + ls_drgbh);

      // 如果是1.1
//      if ("1.1".equals(is_drgfz_ver)) {
//        if (ls_ssbm1_7 != null && !"".equals(ls_ssbm1_7)) {
//          if (ls_ssbm1_7.indexOf("96.7201") > -1 || ls_ssbm1_7.indexOf("39.6500") > -1
//            || ls_ssbm1_7.indexOf("37.5200x001") > -1) {
//            ls_drgbh = "AH1";
//          }
//        }
//      }
      if ("1.1".equals(is_drgfz_ver)) {
        if (ls_ssbm1 != null && !"".equals(ls_ssbm1)) {
          if (ls_ssbm1.indexOf("96.7201") > -1 || ls_ssbm1.indexOf("39.6500") > -1
            || ls_ssbm1.indexOf("37.5200x001") > -1) {
            ls_drgbh = "AH1";
          }
        }
      }

      if ("2.0".equals(is_drgfz_ver)) {
        if (ls_ssbm1 != null && !"".equals(ls_ssbm1)) {
          if ( ls_ssbm1.indexOf("39.6500") > -1
            || ls_ssbm1.indexOf("37.5200x001") > -1) {
            ls_drgbh = "AH1";
          }
        }
      }



      if ("2.0".equals(is_drgfz_ver)) {
        if (ls_ssbm1_7 != null && !"".equals(ls_ssbm1_7)) {
          if ( ls_ssbm1_7.indexOf("96.7201") > -1) {
            String ls_qtzd;
            ls_qtzd = ls_jbdm1_15;
            if (ls_qtzd==null) {
              ls_qtzd = "";
            }
            if ( ( ls_ssbm1_7.indexOf("31.1x00x005") > -1||ls_ssbm1_7.indexOf("31.2100x001") > -1||ls_ssbm1_7.indexOf("31.7400") > -1||ls_ssbm1_7.indexOf("31.7400x001") > -1||ls_ssbm1_7.indexOf("96.0400") > -1||ls_ssbm1_7.indexOf("96.5500") > -1||ls_ssbm1_7.indexOf("31.2900x001") > -1||ls_qtzd.indexOf("Z93.000") > -1)) {
              ls_drgbh = "AH2";
            }
          }
        }
      }


//      System.out.println("1----------------------------------drgbh:" + ls_drgbh);

      // 处理多发伤
      long li_ssbwsl = 0;

      if (ls_jbdm==null) {
        ls_jbdm = "";
      }
      long ids_get_adrg_dfs = 0;

      if(ls_jbdm.substring(0, 1).equals("S")||ls_jbdm.substring(0, 1).equals("T")) {
        ids_get_adrg_dfs = countSsbw(ls_allzd);
      }




      if (ids_get_adrg_dfs > 0) {
        li_ssbwsl = ids_get_adrg_dfs;
        // 有多个部份
        if (li_ssbwsl > 1) {
          ls_xqfz_flag = "1";// 先期分组
          // 多发伤无手术，直接ZZ1
          if (StringUtils.isBlank(ls_ssbm1) || StringUtils.isBlank(ls_ssbm1.trim())  || ls_ssbm1.length() < 4) {
            ls_ssbm1 = "####";
          }

          //还要检查主诊断是不是MDCZ
          int li_dfs_zzdflag=0;
          List<DrgAdrgFz> ids_get_adrg1 = getDrgAdrgFzByIcd(ls_jbdm,null);
          if (ls_xqfz_flag!=null) {
            for (int ll_ii = 0; ll_ii < ids_get_adrg1.size(); ll_ii++) {
              if ("MDCZ".equals(ids_get_adrg1.get(ll_ii).getAdrgbh())) {
                li_dfs_zzdflag=1;//主诊断是MDCZ
              }
            }
          }

          if(li_dfs_zzdflag==0) {
            ls_xqfz_flag="0";
            li_ssbwsl = 0;
          }


          if (("####".equals(ls_ssbm1) || ls_ssbm1.substring(0, 4).equals("17.9")||ls_ssbm1.substring(0, 2).equals("93"))&&li_dfs_zzdflag==1&&!"93.9000x003".equals((ls_ssbm1))&&!"93.9001".equals((ls_ssbm1))) {
            ls_drgbh = "ZZ1";
          }

          if (li_ssbwsl>1&&!"ZZ1".equals(ls_drgbh)&&(!"####".equals(ls_ssbm1) && !ls_ssbm1.substring(0, 4).equals("17.9")&&!ls_ssbm1.substring(0, 2).equals("93"))) {
            drgAdrgFz = new DrgAdrgFz();
            drgAdrgFz.setBzbh("");
            drgAdrgFz.setSsbh(ls_ssbm1);
            // 获取与参数信息中诊断信息或手术信息相符合的分组记录
            List<DrgAdrgFz>  drgAdrgFzList = getDrgAdrgFzByIcd(null,ls_ssbm1);

            int li_flag1=0;
            int li_flag2=0;
            for (DrgAdrgFz drgAdrgFz2 : drgAdrgFzList) {
              String adrgbh = drgAdrgFz2.getAdrgbh();
              if (adrgbh != null && !adrgbh.isEmpty() && adrgbh.charAt(0) != 'Z') {
                li_flag2=1;
              }

              if (adrgbh != null && !adrgbh.isEmpty() && adrgbh.charAt(0) == 'Z') {
                li_flag1 = 1;
              }
            }
            if (li_flag1==0&&li_flag2==1) {
              ls_drgbh = "ZQY";
            }
          }

        }
      }

//      System.out.println("2----------------------------------drgbh:" + ls_drgbh);

      // 处理特定编码
      if ("1.1".equals(is_drgfz_ver)) {
	      if (ls_ssbm1_7 != null && !"".equals(ls_ssbm1_7)) {
	        if (ls_ssbm1_7.indexOf("96.7201") > -1) {
	          if (ls_ssbm1_7.indexOf("31.1x00x005") > -1 || ls_ssbm1_7.indexOf("31.1x00x005") > -1
	            || ls_ssbm1_7.indexOf("31.2900x001") > -1 || ls_ssbm1_7.indexOf("31.7400") > -1
	            || ls_ssbm1_7.indexOf("31.7400x001") > -1 || ls_ssbm1_7.indexOf("96.5500") > -1) {
	            ls_drgbh = "AH1";
	          }
	        }



	        if (ls_ssbm1_7.indexOf("39.6500") > -1) {
	          ls_drgbh = "AH1";
	        }
	      }
      }
//      if ("K64.811".equals(ls_jbdm) && "17.98330".equals(ls_ssbm1)) {
//        if (ls_ssbm1.indexOf("94.") > -1) {
//          ls_drgbh = "GQY";
//        }
//      }

//      if ("1.1".equals(is_drgfz_ver)) {
//        if (ls_jbdm.indexOf("I61.") > -1 || ls_jbdm.indexOf("I63.") > -1 || ls_jbdm.indexOf("01.0900") > -1) {
//          ls_drgbh = "BB2";
//        }
//      }

//      System.out.println("3----------------------------------drgbh:" + ls_drgbh);

      // 恶性肿瘤支持治疗--化疗---直接返
//      if ("Z51.500x002".equals(ls_jbdm) || "Z51.500x003".equals(ls_jbdm) || "Z51.800x953".equals(ls_jbdm)) {
//        ls_drgbh = "RW2";
//      }

//      System.out.println("4----------------------------------drgbh:" + ls_drgbh);

      // 慢性肾脏病5期
//      if ("N17.900".equals(ls_jbdm) || "N17.900".equals(ls_jbdm)) {
//        ls_drgbh = "LR1";
//      }

//      if ("D12.800".equals(ls_jbdm) && "48.3600x004".equals(ls_ssbm1)) {
//        ls_drgbh = "GF2";
//      }
//      if ("H04.401".equals(ls_jbdm) && "09.8100x004".equals(ls_ssbm1)) {
//        ls_drgbh = "CD1";
//      }

      if ("重庆".equals(ls_region)&&"N18.902".equals(ls_jbdm)&&"39.9500".equals(ls_ssbm1)){
        ls_drgbh = "LL1";
      }


//      System.out.println("5----------------------------------drgbh:" + ls_drgbh);

      String ls_qyflag = "";
      // 血透析
//      if ("N18.902".equals(ls_jbdm)) {
//
//    	  if ("39.9500".equals("ls_ssbm1")) {
//    		  ls_qyflag = "0";
//    	  }

//        ls_qyflag = "1";
//        if (!"####".equals(ls_ssbm1)) {
//          ls_qyflag = gf_check_ss_qy(ls_bqy_ssarr,ls_ssbm1,ls_qyflag);
//          if ("1".equals(ls_qyflag)) {
//            if (ls_ss_subname.indexOf("血液透析") > -1) {
//              ls_qyflag = "0";
//            }
//          }
//        } else {
//          ls_qyflag = "'0";
//        }
//
//        if ("0".equals(ls_qyflag)) {
//          if (ls_ssbm1.indexOf("38.95") > -1) {
//            ls_drgbh = "LR1";
//          }
//        } else {
//          ls_drgbh = "LQY";
//        }
//      }

//      System.out.println("6----------------------------------drgbh:" + ls_drgbh);

      // 如果没有符合上述条件
      int li_oldadrg_size=0;
      if ("".equals(ls_drgbh)) {
        ls_xqfz_flag = "0";

        String adrgbh = null;

        if ("1".equals(ls_xqfz_flag)) {

        } else {
          drgAdrgFz = new DrgAdrgFz();
          drgAdrgFz.setBzbh(ls_jbdm);
          drgAdrgFz.setSsbh(ls_ssbm1);

//          System.out.println("61---------------------------------------ls_jbdm: " + ls_jbdm);
//          System.out.println("62---------------------------------------ls_ssbm1: " + ls_ssbm1);

          // 获取与参数信息中诊断信息或手术信息相符合的分组记录
          ids_get_adrg = getDrgAdrgFzByIcd(ls_jbdm,ls_ssbm1);
//          ids_get_adrg_old = ids_get_adrg;//备份用来检查是否歧义
          ids_get_adrg_old.clear();
          for (DrgAdrgFz item : ids_get_adrg) {
        	  ids_get_adrg_old.add(item);
          }

          li_oldadrg_size = ids_get_adrg.size();
          System.out.println("0-----------------------ids_get_adrg:" + ids_get_adrg.size());

          if ("1.1".equals(is_drgfz_ver)) {
            adrgbh = checkTogetherSurgAdrg(ids_get_adrg, ls_jbdm1_15, ls_jbdm, ls_ssbm1_7);
          } else {
            adrgbh = checkTogetherIcdArg20(ids_get_adrg, ls_jbdm1_15, ls_jbdm, ls_ssbm1_7);
          }

          System.out.println("0-----------------------adrgbh:" + adrgbh);

        }

        if (adrgbh == null || "".equals(adrgbh)) {

//         if (li_oldadrg_size!=ids_get_adrg.size()&&adrgbh==null) {
//        	 // 获取与参数信息中诊断信息或手术信息相符合的分组记录
//        	 ids_get_adrg = getDrgAdrgFzByIcd(ls_jbdm,ls_ssbm1);
//         }

          System.out.println("0-----------------------ids_get_adrg:" + ids_get_adrg.size());

          System.out.println("----------------   相关分组信息  ------------------");
          for (int i = 0; i < ids_get_adrg.size(); i++) {
            System.out.println("Adrgbh:" + ids_get_adrg.get(i).getAdrgbh() + "   Mdcbh:" + ids_get_adrg.get(i).getMdcbh() +
              "   Icdbh:" + ids_get_adrg.get(i).getIcdbh() + "   Icdname:" + ids_get_adrg.get(i).getIcdname()
              + "   Ssbw:" + ids_get_adrg.get(i).getSsbw()
              + "   Icdtype:" + ids_get_adrg.get(i).getIcdtype());
          }
          System.out.println("-------------------------------------------------");

          // ls_mdcarr:ids_get_adrg中当adrgbh=mdcbh时的adrgbh尾字母数组

          int ll_add = 0;
          for (int ll_ii = 0; ll_ii < ids_get_adrg.size(); ll_ii++) {
            if (ids_get_adrg.get(ll_ii).getMdcbh() != null && ids_get_adrg.get(ll_ii).getAdrgbh() != null) {
              if (ids_get_adrg.get(ll_ii).getMdcbh().equals(ids_get_adrg.get(ll_ii).getAdrgbh())) {
                ls_mdcarr = Arrays.copyOf(ls_mdcarr, ls_mdcarr.length + 1);//创建新数组
                ls_mdcarr[ll_add] = ids_get_adrg.get(ll_ii).getAdrgbh()
                  .substring(ids_get_adrg.get(ll_ii).getAdrgbh().length() - 1);
                ll_add++;
              }

              if (ids_get_adrg.get(ll_ii).getIcdbh() != null) {
                // 取得诊断名称
                if (ls_jbdm.equals(ids_get_adrg.get(ll_ii).getIcdbh())) {
                  ls_zdmc = ids_get_adrg.get(ll_ii).getIcdname();
                }
                // 取得手术名称
                if (ls_ssbm1 != null && !"".equals(ls_ssbm1)) {
                  if ("2".equals(ids_get_adrg.get(ll_ii).getIcdtype())
                    && ids_get_adrg.get(ll_ii).getIcdbh().equals(ls_ssbm1)) {
                    ls_ss_name = ids_get_adrg.get(ll_ii).getIcdname();
                    ls_adrgbh = ids_get_adrg.get(ll_ii).getAdrgbh();
                    ls_sslb = ids_get_adrg.get(ll_ii).getSsbw();
                  }
                }
              }
            }
          }

          // 主要诊断与主要手术对应的信息
//        System.out.println("----------------   根据手术编码与诊断编码获取的信息  ------------------");
//        System.out.println("ls_zdmc:" + ls_zdmc + " ls_ss_name:" + ls_ss_name + " ls_adrgbh:" + ls_adrgbh + " ls_sslb:" + ls_sslb);
//        System.out.println("-----------------------------------------------------------------");

//        //单独处理全部手术的问题
          if (ls_ssbm1 != null && !"".equals(ls_ssbm1)) {
//                for (int ll_ii = 0; ll_ii < ls_mdcarr.length; ll_ii++) {

//                  if (("S".equals(ls_mdcarr[ll_ii]) || "T".equals(ls_mdcarr[ll_ii]) || "X".equals(ls_mdcarr[ll_ii])) && !"####".equals(ls_ssbm1)) {
            int ll_find = -1;

            for (int i = 0; i < ids_get_adrg.size(); i++) {
              // 是手术
              if (ls_ssbm1.equals(ids_get_adrg.get(i).getIcdbh())) {
                // 手术在全部手术adrg列表内,同时检查手术的adrgbh的第一位是否在ls_mdcarr数据中

                if (ls_qbss_adrgbh.indexOf(ids_get_adrg.get(i).getAdrgbh()) > -1&&Arrays.stream(ls_mdcarr).anyMatch(ids_get_adrg.get(i).getAdrgbh().substring(ids_get_adrg.get(i).getAdrgbh().length() - 1)::equals)) {
                  //添加对应的诊断
                  DrgAdrgFz drgAdrgFz1 = new DrgAdrgFz();
                  drgAdrgFz1.setAdrgbh(ids_get_adrg.get(i).getAdrgbh());
                  drgAdrgFz1.setIcdbh(ls_jbdm);
                  drgAdrgFz1.setId(100L);
                  drgAdrgFz1.setIcdtype("1");
                  drgAdrgFz1.setType("2");
                  ids_get_adrg.add(drgAdrgFz1);
                }
              }
            }
          }

//          // 单独处理XJ1---其他接触健康服务的诊断伴手术室操作
//          if (ls_ssbm1 != null && !"".equals(ls_ssbm1)) {
//            for (int ll_ii = 0; ll_ii < ls_mdcarr.length; ll_ii++) {
//              if ((("X".equals(ls_mdcarr[ll_ii])) || "S".equals(ls_mdcarr[ll_ii]) || "W".equals(ls_mdcarr[ll_ii])|| "T".equals(ls_mdcarr[ll_ii])) && !"####".equals(ls_ssbm1)) {
//                int ll_find = -1;
//
//                for (int i = 0; i < ids_get_adrg.size(); i++) {
//                  if (ls_ssbm1.equals(ids_get_adrg.get(i).getIcdbh())) {
//                    if ("1.1".equals(is_drgfz_ver)) {
////                      if ("手术".equals(ids_get_adrg.get(i).getSsbw())
////                        || "介入治疗".equals(ids_get_adrg.get(i).getSsbw())
////                        || "治疗性操作".equals(ids_get_adrg.get(i).getSsbw())) {
//                      ll_find = i;
//                      break;
////                      }
//                    } else {
//                      if ("手术".equals(ids_get_adrg.get(i).getSsbw())
//                        || "介入治疗".equals(ids_get_adrg.get(i).getSsbw())) {
//                        ll_find = i;
//                        break;
//                      }
//                    }
//                  }
//                }
//
//                if (ll_find > -1) {
//                //添加手术
//                  DrgAdrgFz drgAdrgFz1 = new DrgAdrgFz();
//                  if ("S".equals(ls_mdcarr[ll_ii])) {
//                    drgAdrgFz1.setAdrgbh("SB1");
//                    drgAdrgFz1.setMdcbh("MDCS");
//                  }
//                  if ("W".equals(ls_mdcarr[ll_ii])) {
//                      drgAdrgFz1.setAdrgbh("WJ1");
//                      drgAdrgFz1.setMdcbh("MDCW");
//                    }
//                  if ("X".equals(ls_mdcarr[ll_ii])) {
//                    drgAdrgFz1.setAdrgbh("XJ1");
//                    drgAdrgFz1.setMdcbh("MDCX");
//                  }
//                  if ("T".equals(ls_mdcarr[ll_ii])) {
//                    drgAdrgFz1.setAdrgbh("TB1");
//                    drgAdrgFz1.setMdcbh("MDCT");
//                  }
//
//
//                  drgAdrgFz1.setIcdbh(ls_ssbm1);
//                  drgAdrgFz1.setId(100L);
//                  drgAdrgFz1.setIcdtype("2");
//                  drgAdrgFz1.setType("2");
//                  ids_get_adrg.add(drgAdrgFz1);
//                }
//
//                // 删除其他的x的分组
//                if (ll_find > -1) {
//                  for (int i = 0; i < ids_get_adrg.size(); i++) {
//                    if ("X".equals(ls_mdcarr[ll_ii])) {
//                      if (("X".equals(ls_mdcarr[ll_ii])) && !"XJ1".equals(ids_get_adrg.get(i).getAdrgbh())) {
//                        ids_get_adrg.get(i).setId(0L);
//                      }
//                    }
//                    if ("S".equals(ls_mdcarr[ll_ii])) {
//                      if (("S".equals(ls_mdcarr[ll_ii])) && !"SB1".equals(ids_get_adrg.get(i).getAdrgbh())) {
//                        ids_get_adrg.get(i).setId(0L);
//                      }
//                    }
//                    if ("W".equals(ls_mdcarr[ll_ii])) {
//                        if (("W".equals(ls_mdcarr[ll_ii])) && !"WJ1".equals(ids_get_adrg.get(i).getAdrgbh())) {
//                          ids_get_adrg.get(i).setId(0L);
//                        }
//                      }
//                    if ("T".equals(ls_mdcarr[ll_ii])) {
//                      if (("T".equals(ls_mdcarr[ll_ii])) && !"TB1".equals(ids_get_adrg.get(i).getAdrgbh())) {
//                        ids_get_adrg.get(i).setId(0L);
//                      }
//                    }
//                  }
//                }
//
//              }
//            }
//          }

          int ll_have = -1;// 当前病案号对应病案数量
          if (ls_bah == null || "".equals(ls_bah)) {
            ls_bah = "####";
          }
          //当病人标识为空时
          if (ls_brbs == null || "".equals(ls_brbs)) {

            List<BaSyjl> baSyjls = baSyjlService.selectBrbsByBah(ls_bah);


            if (baSyjls.size() > 0) {
              ls_brbs = baSyjls.get(0).getBrbs();
              ll_have = baSyjls.get(0).getCount();
            }

            // 若对应多个病案，则根据病案号与入院时间获取
            if (ll_have > 1) {
              baSyjls = baSyjlService.selectBrbsByBahAndRysj(ls_bah);
              if (baSyjls.size() > 0) {
                if (baSyjls.get(0) != null)
                  // 默认去第一条病案的信息
                  ls_brbs = baSyjls.get(0).getBrbs();
              }
            }
          }

          if (ls_zfy==null) {
            ls_zfy = new BigDecimal("0");
          }

          // 若总费用为空，则获取fyxx表费用信息
          if (new BigDecimal("0").compareTo(ls_zfy)==0) {
            Double zfy = fyxxService.selectZfyByJzh(ls_brbs);
            if (zfy != null && zfy != 0) {
              ls_zfy = BigDecimal.valueOf(zfy);
            }
          }



          // 检查是否是歧义组---用主诊和主手术
          if (ls_ssbm1 != null && !"".equals(ls_ssbm1)) {
            if (!"####".equals(ls_ssbm1)) {

//            System.out.println("--------------------------------歧义组判断-----------------------------------");
//            System.out.println("----------------------------------------------------主要手术：" + ls_ssbm1);

              /**
               * 找到当前手术对应的adrg记录
               * ①找到当前主要手术的手术类型：如果当前手术存在于ssml表或当前手术类型（b_icd_ssbm表获取）包含“操作”----不是歧义组
               * ②若找到相关adrg记录，但不满足条件①，筛选是否存在手术adrg记录，如果存在一条记录中手术室参数==1或者该记录为手术相关，且adrgbh第一位不为2----不是歧义组
               * ③不满足条件②时，若adrgbh为“MDCX”,"MDCS","MDCT"----不是歧义组
               * ④不满足条件③时，若此时手术类型为空，从ssml表中获取其手术类型，若手术类型包含“操作”----不是歧义组 ⑤若不满足以上条件----歧义组
               */

              ls_qyflag = "1"; // 歧义组标识

              // ①找到当前主要手术的手术类型：如果当前手术存在于ssml表或当前手术类型（b_icd_ssbm表获取）包含“操作”----不是歧义组
              int ll_find = -1;
              for (int i = 0; i < ids_get_adrg_old.size(); i++) {
                if (ls_ssbm1.equals(ids_get_adrg_old.get(i).getIcdbh())) {
                  ll_find = i;
                }
              }
              String ls_sstype = "";
              if ("1.1".equals(is_drgfz_ver)||"2.0".equals(is_drgfz_ver)) {
                if (ll_find == -1) {
                  Ssml ssml = new Ssml();
                  if ("".equals(ls_ssbm1)||ls_ssbm1==null) {
                    ls_qyflag = "0";
                  }else
                  {
                    ssml.setSsbm(ls_ssbm1);
                    List<Ssml> ssmls = ssmlService.selectSsmlList(ssml);
                    if (ssmls.size() > 0) {
                      ls_qyflag = "0";
                    }
                  }
                } else {
                  ls_sstype = ids_get_adrg_old.get(ll_find).getSsbw();
                }
              } else {
                ls_sstype = drgFzqService.selectSsType(ls_ssbm1);

//              System.out.println("----------------------------------------------------主要手术类型：" + ls_sstype);
                if ("".equals(ls_sstype) || ls_sstype == null) {
                  Ssml ssml = new Ssml();

                  if ("".equals(ls_ssbm1)||ls_ssbm1==null) {
                    ls_qyflag = "0";
                  }else
                  {
                    ssml.setSsbm(ls_ssbm1);
                    List<Ssml> ssmls = ssmlService.selectSsmlList(ssml);
//                    System.out.println("----------------------------------------------------ssml表是否包含该手术：" + (ssmls.size()>0?"包含":"不包含"));
                    if (ssmls.size() > 0) {
                      ls_qyflag = "0";
                    }
                  }


                } else if (ls_sstype.indexOf("操作") > -1) {
                  ls_qyflag = "0";
                }
              }

//            System.out.println("----------------------------------------------------第一轮判断：" + ("0".equals(ls_qyflag)?"不是歧义组":"是歧义组"));

              // ②若找到相关adrg记录，但不满足条件①，筛选是否存在手术adrg记录，如果存在一条记录中手术室参数==1或者该记录为手术相关，且adrgbh第一位不为2----不是歧义组
              // 没有找到手术,并且是手术的类别
              if (ll_find <= -1 && "1".equals(ls_qyflag)) {
                ls_qyflag = "1";
              } else if (ll_find > -1 && "1".equals(ls_qyflag)) {
                // 有手术
                int lli_find = -1;
                for (int i = 0; i < ids_get_adrg_old.size(); i++) {
                  if ("2".equals(ids_get_adrg_old.get(i).getIcdtype())) {
                    lli_find = i;
                    break;
                  }
                }
//              System.out.println("----------------------------------------------------是否包含手术相关adrg：" + (lli_find>-1?"包含":"不包含"));
// 先找到mdc
                if (lli_find > -1) {
                  DrgAdrgFz adrgllii = null;
                  DrgAdrgFz adrgllj = null;
                  for (int ll_ii = 0; ll_ii < ids_get_adrg_old.size(); ll_ii++) {
                    adrgllii = ids_get_adrg_old.get(ll_ii);
                    if (adrgllii.getMdcbh() != null && adrgllii.getAdrgbh() != null) {

                      if (adrgllii.getMdcbh().equals(adrgllii.getAdrgbh())) {
                        for (int ll_j = 0; ll_j < ids_get_adrg_old.size(); ll_j++) {
                          adrgllj = ids_get_adrg_old.get(ll_j);
                          ls_mdcfirst = String.valueOf(adrgllj.getAdrgbh().charAt(0));
                          ls_icdtype = adrgllj.getIcdtype();
                          //手术的第一个字母与Mdc的最后一位相同
                          if ((ls_mdcfirst
                            .equals(adrgllii.getAdrgbh()
                              .substring(adrgllii.getAdrgbh().length() - 1))
                            && "2".equals(ls_icdtype))
                            || "1".equals(adrgllj.getSssflag())) {
                            if (!"2".equals(ls_mdcfirst)) {
//                            	 ls_qyflag = "0";
                              //如果是---全部是手术的adrg
//                            	System.out.println(adrgllj.getAdrgbh());
                              if (ls_qbss_adrgbh.contains(adrgllj.getAdrgbh())) {
                                if (!"Z".equals(String.valueOf(adrgllj.getAdrgbh().charAt(0)))){
                                  ls_qyflag = "0";
                                }
                                if ("Z".equals(String.valueOf(adrgllj.getAdrgbh().charAt(0)))&&li_ssbwsl>1){
                                  ls_qyflag = "0";
                                }
                              }



                              for (int ll_m = 0; ll_m < ids_get_adrg_old.size(); ll_m++) {
                                //检查诊断和手术是同一adrgbh
                                if("1".equals(ids_get_adrg_old.get(ll_m).getIcdtype())&&"2".equals(ids_get_adrg_old.get(ll_m).getType())&&ids_get_adrg_old.get(ll_j).getAdrgbh().equals(ids_get_adrg_old.get(ll_m).getAdrgbh())) {
                                  ls_qyflag = "0";
                                }
                              }
                            }
                          }
                        }
                      }
                    }

                  }
                }
              }

//            System.out.println("----------------------------------------------------第二轮判断：" + ("0".equals(ls_qyflag)?"不是歧义组":"是歧义组"));

              // ③不满足条件②时，若adrgbh为“MDCX”,"MDCS","MDCT"----不是歧义组
//              if ("1".equals(ls_qyflag)) {
//                for (DrgAdrgFz adrgFz : ids_get_adrg) {
//                  if ("MDCX".equals(adrgFz.getAdrgbh()) || "MDCS".equals(adrgFz.getAdrgbh())
//                    || "MDCT".equals(adrgFz.getAdrgbh())|| "MDCW".equals(adrgFz.getAdrgbh())) {
//                    ls_qyflag = "0";
//                    break;
//                  }
//                }
//              }

//            System.out.println("----------------------------------------------------第三轮判断：" + ("0".equals(ls_qyflag)?"不是歧义组":"是歧义组"));

              // ④不满足条件③时，若此时手术类型为空，从ssml表中获取其手术类型，若手术类型包含“存在”----不是歧义组
              if ("1".equals(ls_qyflag) && ("".equals(ls_sstype) || ls_sstype == null)) {
                Ssml ssml = ssmlService.selectSsxxBySsbm(ls_ssbm1);
                if (ssml != null) {
                  ls_sslb = ssml.getSsmc1();
                  ls_ss_subname = ssml.getSsmc4();
                  ls_ss_name = ssml.getSsmc();
                  ls_sstype = ssml.getType();
                }
//              System.out.println("----------------------------------------------------手术类型：" + ls_sstype);
                if (ls_sstype != null) {
                  if (ls_sstype.indexOf("操作") > -1) {
                    ls_qyflag = "0";
                  }
                }
              }

              //争对包含全部手术或操作
              if (ls_mdcarr.length>0&&!"####".equals(ls_ssbm1)) {
                if("S".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
                  ls_qyflag = "0";
                }
                if("T".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
                  ls_qyflag = "0";
                }
                if("X".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
                  ls_qyflag = "0";
                }
                if("Y".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
                  ls_qyflag = "0";
                }
                if("W".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
                  ls_qyflag = "0";
                }
              }

//            System.out.println("----------------------------------------------------第四轮判断：" + ("0".equals(ls_qyflag)?"不是歧义组":"是歧义组"));

//            System.out.println("--------------------------------歧义组判断结束-----------------------------------" + ("0".equals(ls_qyflag)?"不是歧义组":"是歧义组"));
              // ⑤若不满足以上条件----歧义组
              if ("1".equals(ls_qyflag) && ls_mdcarr.length > 0) {
                ls_drgbh = ls_mdcarr[0] + "QY";

                ll_have = -1;
                for (int i = 0; i < ids_drg_dict.size(); i++) {
                  if (ls_drgbh.equals(ids_drg_dict.get(i).getDrgbh())) {
                    ll_have = i;
                  }
                }
                if (ll_have <= -1) {
                  if (ls_drgbh.length() >= 3) {
                    ls_drgbh = ls_drgbh.substring(0, 3);
                  }
                  ll_have = -1;
                  for (int i = 0; i < ids_drg_dict.size(); i++) {
                    if (ids_drg_dict.get(i).getDrgbh().length() >= 3) {
                      if (ls_drgbh.equals(ids_drg_dict.get(i).getDrgbh().substring(0, 3))) {
                        ll_have = i;
                      }
                    }
                  }
                }



//              System.out.println("7----------------------------------drgbh:" + ls_drgbh);

                DrgFzResult drgFzResult = new DrgFzResult();
                if ("1".equals(ls_one_flag) && ll_have > -1) {

                  BigDecimal zfbz;
                  BigDecimal zfqz;
                  //如果是新疆且版本为2.0则区分成年与未成年
                  if ("新疆".equals(ls_region) && "2.0".equals(is_drgfz_ver)) {
                    zfqz = ids_drg_dict.get(ll_have).getZfqz();
                    if (ls_nl != null && ls_nl <= 6) {
                      logger.info("年龄小于6岁，选择儿童支付标准");
                      zfbz = ids_drg_dict.get(ll_have).getZfbzEt();
                    } else {
                      zfbz = ids_drg_dict.get(ll_have).getZfbz();
                    }
                  } else { //如果是重庆且是多租户则区分医院等级
                    if (orgLevel != null&&"1".equals(hasMultipleOrg)) {
                      zfbz = getZfbz(orgLevel,ids_drg_dict.get(ll_have));
                      zfqz = getZfqz(orgLevel,ids_drg_dict.get(ll_have));
                    } else {
                      zfbz = ids_drg_dict.get(ll_have).getZfbz();
                      zfqz = ids_drg_dict.get(ll_have).getZfqz();
                    }
                  }

                  System.out.println("----------------------分组信息-------------------------");
                  System.out.println("DRG编码：" + ids_drg_dict.get(ll_have).getDrgbh());
                  System.out.println("支付权重：" + zfqz);
                  System.out.println("总费用：" + ls_zfy);
                  System.out.println("分组类型：" + ids_drg_dict.get(ll_have).getFztype());
                  System.out.println("支付标准：" + zfbz);
                  System.out.println("DRG名称：" + ids_drg_dict.get(ll_have).getDrgmc());
                  System.out.println("实际住院天数：" + ls_sjzyts);
                  System.out.println("标准床日：" + ids_drg_dict.get(ll_have).getPjdays());
                  System.out.println("-----------------------------------------------------");

                  drgFzResult.setBah(ls_bah);
                  drgFzResult.setBrbs(ls_brbs);
                  drgFzResult.setCode(200);
                  drgFzResult.setMessage("更新成功");
                  drgFzResult.setDrgbh(ids_drg_dict.get(ll_have).getDrgbh());
                  drgFzResult.setZfbz(zfbz);
                  drgFzResult.setZfy(ls_zfy);
                  drgFzResult.setFztype(ids_drg_dict.get(ll_have).getFztype());
                  drgFzResult.setDrgmc(ids_drg_dict.get(ll_have).getDrgmc());
                  drgFzResult.setZydays(BigDecimal.valueOf(ls_sjzyts));
                  drgFzResult.setPjdays(ids_drg_dict.get(ll_have).getPjdays());

                  BaSyjl baSyjl = new BaSyjl();
                  baSyjl.setBrbs(ls_brbs);
                  baSyjl.setDrgbh(ls_drgbh);
                  baSyjl.setRzflag(0);
                  baSyjl.setWrzyy("分组器返回未入组");
                  baSyjlService.updateBaSyjl(baSyjl);
                } else {
                  drgFzResult.setBah(ls_bah);
                  drgFzResult.setBrbs(ls_brbs);
                  drgFzResult.setDrgbh(ls_drgbh);

                  BaSyjl baSyjl = new BaSyjl();
                  baSyjl.setBrbs(ls_brbs);
                  baSyjl.setDrgbh(ls_drgbh);
                  baSyjl.setRzflag(0);
                  baSyjl.setWrzyy("分组器返回未入组");
                  baSyjlService.updateBaSyjl(baSyjl);
                }
                return drgFzResult;
              }
            }
          }


          // 如果受伤部位小于2，则删除多发伤
          if (li_ssbwsl < 2) {
            // 保留Adrgbh首字母与Adrgbh=Mdcbh记录中Adrgbh尾字母相同的记录
            for (int ll_ii = 0; ll_ii < ids_get_adrg.size(); ll_ii++) {
              if ("Z".equals(ids_get_adrg.get(ll_ii).getAdrgbh().substring(0, 1))) {
                ids_get_adrg.get(ll_ii).setId(0L);
              }
            }

            for (int i = 0; i < ids_get_adrg.size(); i++) {
              if (ids_get_adrg.get(i).getId() == 0) {
                ids_get_adrg.remove(i);
                i--;
              }
            }
          }



          //新生儿大于29天，去除pu分组
          if (ls_bzyzsnl!=null) {
            if (ls_bzyzsnl>29) {
              for (int ll_ii = 0; ll_ii < ids_get_adrg.size(); ll_ii++) {
                if ("PU".equals(ids_get_adrg.get(ll_ii).getAdrgbh().substring(0, 2))) {
                  ids_get_adrg.get(ll_ii).setId(0L);
                }
              }

              for (int i = 0; i < ids_get_adrg.size(); i++) {
                if (ids_get_adrg.get(i).getId() == 0) {
                  ids_get_adrg.remove(i);
                  i--;
                }
              }
            }
          }

//          // 如果受伤部位小于2，则删除多发伤---将代码放前面了
//          if (li_ssbwsl < 2) {
//            // 保留Adrgbh首字母与Adrgbh=Mdcbh记录中Adrgbh尾字母相同的记录
//            for (int ll_ii = 0; ll_ii < ids_get_adrg.size(); ll_ii++) {
//              if ("Z".equals(ids_get_adrg.get(ll_ii).getAdrgbh().substring(0, 1))) {
//                ids_get_adrg.get(ll_ii).setId(0L);
//              }
//
//            }
//
//            for (int i = 0; i < ids_get_adrg.size(); i++) {
//              if (ids_get_adrg.get(i).getId() == 0) {
//                ids_get_adrg.remove(i);
//                i--;
//              }
//            }
//          }

          ls_drgbh = of_drg_fz_sub1(ids_get_adrg, ls_mdcarr, ls_adrgbh, ls_drgbh, ls_jbdm, ls_ssjlarr, ls_sslb,
            ls_zdmc, ls_ssbm1);

        } else {
          ls_drgbh = adrgbh;
        }
      }


      //争对包含全部手术或操作
      if (ls_mdcarr.length>0&&!"####".equals(ls_ssbm1)) {
        if("S".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
          if (checkhavess(ls_ssbm1)>0) {
            ls_drgbh = "SB1";
          }
        }
        if("T".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
          if (checkhavess(ls_ssbm1)>0) {
            ls_drgbh = "TB1";
          }
        }
        if("X".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
          if (checkhavess(ls_ssbm1)>0) {
            ls_drgbh = "XJ1";
          }
        }
        if("Y".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
          if (checkhavess(ls_ssbm1)>0) {
            ls_drgbh = "YC1";
          }
        }
        if("W".equals(ls_mdcarr[0])&&(ls_ssbm1 != null && !"".equals(ls_ssbm1))) {
          if (checkhavess(ls_ssbm1)>0&&(!"WB1".equals(ls_drgbh)&&!"WB2".equals(ls_drgbh)&&!"WB3".equals(ls_drgbh)&&!"WC1".equals(ls_drgbh))) {
            ls_drgbh = "WJ1";
          }
        }
      }


      /**
       * 处理DRG编码第四位（前三位为adrgbh 第一位为mdcbh） 根据并发症合并症及其严重情况与离院方式判断 “1”表示伴有严重并发症与合并症
       * “3”表示伴有并发症与合并症 “5”表示不伴并发症与合并症 “7”表示死亡或转院 “9”表示未作区分的情况 诊断类型： ll_type == 1 :
       * MCC : 严重并发症合并症 ll_type == 2 : CC : 并发症合并症
       */

      Long ll_pretype = 100L;
      Long ll_type = 0L;
      Long ll_listid;
      if (ls_jbdm1_15 != null && !"".equals(ls_jbdm1_15)) {
        ls_bfz = ls_jbdm1_15.split(","); // 其他诊断集合
      } else {
        ls_bfz = new String[0];
      }

//      System.out.println("----------------------------------其他诊断数量:" + ls_bfz.length);
//      for (String s : ls_bfz) {
//        System.out.print(s + "  ");
//      }
//      System.out.println();
//      System.out.println("----------------------------------");

      // 查看其他诊断类型MCC/CC 属于严重并发症合并症还是普通并发症合并症
      for (int ll_ii = 0; ll_ii < ls_bfz.length; ll_ii++) {
        int ll_have = -1;
        for (int i = 0; i < ids_drg_bfz.size(); i++) {
          if (ids_drg_bfz.get(i).getBzbm().equals(ls_bfz[ll_ii])) {
        	  System.out.println("mcc-cc="+ls_bfz[ll_ii]);
            ll_have = i;
            break;
          }
        }
        // 在ids_drg_bfz中找到该诊断编码，获取其ll_listid与ll_type，并判断ids_drg_bfz_pc中是否存在该诊断
        // 若存在则排除其为并发症合并症
        if (ll_have > -1) {
          ll_listid = ids_drg_bfz.get(ll_have).getListid();
          ll_type = ids_drg_bfz.get(ll_have).getType();
          for (int i = 0; i < ids_drg_bfz_pc.size(); i++) {
            if (ids_drg_bfz_pc.get(i).getBzbm().length() >= ls_jbdm.length()) {
              if (ids_drg_bfz_pc.get(i).getListid().equals(ll_listid)
                && ids_drg_bfz_pc.get(i).getBzbm().substring(0, ls_jbdm.length()).equals(ls_jbdm)) {
                ll_type = 0L;
                break;
              }
            }
          }
        }
        if (ll_type > 0 && ll_type < ll_pretype) {
          ll_pretype = ll_type;
        }
//        System.out.println("========================" + ls_bfz);
//        System.out.println("========================" + ll_pretype);

      }

//      if ("1861889_18619".equals(ls_brbs)) {
//        ls_brbs = "1861889_18619";
//      }

      System.out.println("----------------------------------ll_pretype:" + ll_pretype);
      //处理新生儿的体重的分组


      //早产儿
      for (ll_i=0;ll_i<ls_allzd.length;ll_i++) {
        System.out.println(ls_zce_bzbm.indexOf(ls_allzd[ll_i])+ls_allzd[ll_i]);
        if (ls_allzd[ll_i]!=null && !"".equals(ls_allzd[ll_i])) {
          if(ls_zce_bzbm.indexOf(ls_allzd[ll_i]) > -1&&("PU1".equals(ls_drgbh)||"PV1".equals(ls_drgbh))) {
            ls_drgbh = "PS";
          }
          if(ls_zce_bzbm.indexOf(ls_allzd[ll_i]) > -1&&(!"PB1".equals(ls_drgbh)&&!"PC1".equals(ls_drgbh)&&!"PD1".equals(ls_drgbh)&&!"PJ1".equals(ls_drgbh)&&!"PK1".equals(ls_drgbh)&&!"PR1".equals(ls_drgbh))) {
            ls_drgbh = "PS";
          }

        }
      }

      if (ls_drgbh != null && ls_drgbh.length() >= 2) {
        String ls_drgbh_left;
        ls_drgbh_left = ls_drgbh.substring(0, 2);

        if ("PS".equals(ls_drgbh_left)) {
          if (ls_xsecstz == null) {
            ls_drgbh = "PS4";
          }
          if (ls_xsecstz != null) {
            if (ls_xsecstz.compareTo(new BigDecimal(1500)) == -1) {
              ls_drgbh = "PS1";
            } else if (ls_xsecstz.compareTo(new BigDecimal(1999)) <= 0 && ls_xsecstz.compareTo(new BigDecimal(1500)) >= 0) {
              ls_drgbh = "PS2";
            } else if (ls_xsecstz.compareTo(new BigDecimal(2499)) <= 0 && ls_xsecstz.compareTo(new BigDecimal(1999)) >= 0) {
              ls_drgbh = "PS3";
            } else if (ls_xsecstz.compareTo(new BigDecimal(2499)) == 1) {
              ls_drgbh = "PS4";
            }
          }
        }
      }

      String ls_cndrgbh="";
      //开始确定第四位
      if (ls_drgbh.length() >= 3) {

        String adrgbh = ls_drgbh.substring(0, 3);

        ls_cndrgbh = getcndrgbh(ls_drgbh,ll_pretype);

        String drgbhBySjzyts = null;
        if ("新疆".equals(ls_region) && "1.1".equals(is_drgfz_ver) && ls_sjzyts != null && ls_sjzyts > 60) {
          drgbhBySjzyts = findDrgbhBySjzyts(adrgbh, "住院天数大于60天");
        }

        if (drgbhBySjzyts == null || "".equals(drgbhBySjzyts.trim())) {
        	String drgbhByConfig=null;
          if("2.0".equals(is_drgfz_ver)&&"ZC1".equals(adrgbh)&&"ZD1".equals(adrgbh))
           drgbhByConfig = findDrgbhByConfig(adrgbh, ls_jbdm, ls_jbdm1_15, ls_ssbm1_7, ls_nl);  //检查分组配置条件
          else
          {
        	  drgbhByConfig = findDrgbhByConfig(adrgbh, ls_jbdm, ls_jbdm1_15, ls_ssbm1_7, ls_nl);  //检查分组配置条件
          }
          if (drgbhByConfig != null) {
            ls_drgbh = drgbhByConfig;
          } else {
            String drgbhByJointSurg = checkJointSurg(adrgbh, ls_ssbm1_7);   //检查联合手术
            if (drgbhByJointSurg != null) {
              ls_drgbh = drgbhByJointSurg;
            } else {
              String drgbhByAge = null;
              if ("2.0".equals(is_drgfz_ver) && "新疆".equals(ls_region)) {
                drgbhByAge = findDrgbhByAgeAndBfzXjks(adrgbh, ls_nl, ls_bzyzsnl);    //检查年龄
              } else {
            	  if ("1.1".equals(is_drgfz_ver)){
            		  drgbhByAge = findDrgbhByAgeAndBfz(adrgbh, ls_nl, ll_pretype);    //检查年龄
            	  }
              }
              if (drgbhByAge != null) {
                ls_drgbh = drgbhByAge;
              } else {                                                 //检查并发症
                if (ll_pretype == 1) { // MCC
                  ls_drgbh = adrgbh + "1"; // “1”表示伴有严重并发症与合并症
                  if(checkDrgbhExists(ids_drg_dict,ls_drgbh)==false) {
                	  ls_drgbh = adrgbh + "3";
                  }

                } else if (ll_pretype == 2) { // CC
                  ls_drgbh = adrgbh + "3"; // “3”表示伴有并发症与合并症
                  if(checkDrgbhExists(ids_drg_dict,ls_drgbh)==false) {
                	  ls_drgbh = adrgbh + "5";
                  }
                } else {
                  int ll_have = -1;
                  for (int i = 0; i < ids_drg_dict.size(); i++) {
                    if (ids_drg_dict.get(i).getDrgbh().equals(adrgbh + "9")) {
                      ll_have = i;
                      break;
                    }
                  }
                  if (ls_drgbh.length() >= 3) {
                    if (ll_have > -1) {
                      ls_drgbh = adrgbh + "9"; // 如果存在“9”表示未作区分的情况则选择9
                    } else {
                      ls_drgbh = adrgbh + "5"; // 否则选择“5”表示不伴并发症与合并症
                    }
                  }
                }
              }
            }
          }
        } else {
          ls_drgbh = drgbhBySjzyts;
        }
      }




      System.out.println("13----------------------------------drgbh:" + ls_drgbh);

      if (ls_drgbh.length() < 3) {
        ls_drgbh = "000";
      }

//      System.out.println("14----------------------------------drgbh:" + ls_drgbh);

      // 新疆喀什---呼吸系统感染/炎症，年龄小于18岁
      if (ls_nl != null && !"".equals(ls_nl)) {
//        if ("新疆".equals(ls_region) && "ES3".equals(ls_drgbh.substring(0, 3)) && ls_nl < 18 && ls_nl >= 0) {
//          ls_drgbh = "ES30";
//        }

        if ("新疆".equals(ls_region) && "EX2".equals(ls_drgbh.substring(0, 3)) && ls_nl < 18 && ls_nl >= 0) {
          ls_drgbh = "EX20";
        }

        if ("新疆".equals(ls_region) && "PU15".equals(ls_drgbh)) {
          ls_drgbh = "PU13";
        }

        if ("新疆".equals(ls_region) && "PS45".equals(ls_drgbh)) {
          ls_drgbh = "PS43";
        }
        if ("新疆".equals(ls_region) && "FM35".equals(ls_drgbh)) {
          ls_drgbh = "FM33";
        }
        if ("新疆".equals(ls_region) && "HT15".equals(ls_drgbh)) {
          ls_drgbh = "HT13";
        }
        if ("新疆".equals(ls_region) && "IB35".equals(ls_drgbh)) {
          ls_drgbh = "IB33";
        }
        if ("新疆".equals(ls_region) && "IR25".equals(ls_drgbh)) {
          ls_drgbh = "IR25";
        }
        if ("新疆".equals(ls_region) && "OD15".equals(ls_drgbh)) {
          ls_drgbh = "OD15";
        }
        if ("新疆".equals(ls_region) && "PS15".equals(ls_drgbh)) {
          ls_drgbh = "PS13";
        }
        if ("新疆".equals(ls_region) && "RB15".equals(ls_drgbh)) {
          ls_drgbh = "RB13";
        }
        if ("新疆".equals(ls_region) && "ZD15".equals(ls_drgbh)) {
          ls_drgbh = "ZD13";
        }
        if ("新疆".equals(ls_region) && "ZZ15".equals(ls_drgbh)) {
          ls_drgbh = "ZZ13";
        }




        // 当时新生儿组时，年龄大于1，直接000组
        if ("P".equals(ls_drgbh.substring(0, 1)) && ls_nl > 1) {
          ls_drgbh = "000";
        }
      }

      boolean zyFlag = false;
      if ("2.0".equals(is_drgfz_ver) && !"新疆".equals(ls_region)) {
        //检查中医分组
//    	if("1".equals(orgLevel)) {
//    		//一级没得中医优势病组
//    	}else {
    		if (ls_sjzyts >= 10d&&ls_ssbm1_7!=null&&(ls_ssbm1_7.contains("17")||ls_ssbm1_7.contains("93")||ls_ssbm1_7.contains("99"))&&("M".equals(ls_jbdm.substring(0, 1))||"S".equals(ls_jbdm.substring(0, 1)))) {

          List<String> ls_ssbm1_7_list = Arrays.asList(ls_ssbm1_7.split(","));
//          List<String> ls_ssbm1_7_list = Arrays.asList(ls_ssbm1_7);
  	           List<String> larr_zydrgbh = DrgFzqzyGroupMatcher.matchGroup(ls_jbdm, ls_ssbm1_7_list, Math.toIntExact(ls_sjzyts));
  	           if (larr_zydrgbh.size()>0) {
  	        	   ls_drgbh = larr_zydrgbh.get(0);
                 zyFlag = true;
  	           }
    		}
//    	}



         //单独处理CD36
	    if("CD35".equals(ls_drgbh)&&ls_nl<=18) {
			   ls_drgbh = "CD36";
		   }
	    //单独处理AH2
	    if("AH23".equals(ls_drgbh)&&ls_nl>=60) {
			   ls_drgbh = "AH2N";
		   }
	    //单独处理AH2
	    if("AH2".equals(ls_drgbh.substring(0, 3))&&ls_nl>=60&&ls_ssbm1_7!=null&&ls_ssbm1_7.contains("39.9500x007")) {
			   ls_drgbh = "AH2P";
		   }
	  //单独处理AH2
	    if("AH2".equals(ls_drgbh.substring(0, 3))&&ls_nl<60&&ls_ssbm1_7!=null&&ls_ssbm1_7.contains("39.9500x007")) {
			   ls_drgbh = "AH2B";
		   }


	    //细分组
	   if ("BD1,BR2,BT2,BT3,BX2,CB4,CB4,CB7,CD1,EX2,FL1,GB1,GB3,GF3,GT1,HB1,HC1,HJ1,IB3,IB3,IC3,IF1,IT2,JJ1,LA2,LC1,LE1,MA1,NA1,NS1,RK2,RL1,RL1,RN1,RN1,RN1,RN2,TR1,TS1,TU1,XT3,YR1".contains(ls_drgbh.substring(0, 3))){

		   List<String> larr_jbdm1_15 = Arrays.asList(ls_jbdm.split(","));// 诊断列表（主诊断在前）
	        List<String> procedures = Arrays.asList(ls_ssbm1_7.split(",")); // 手术列表（主手术在前）
	        // 获取匹配的DRG完整代码
	        String matchedDRG = DrgFzqCQ20xfzGroupMatcher.classifyDRG(ls_drgbh.substring(0, 3), larr_jbdm1_15, procedures);
	        if (matchedDRG!=null&&!"".equals(matchedDRG)) {
	        	ls_drgbh = matchedDRG;
	        }
	   }

      }




      if ("重庆".equals(ls_region)&&"1.1".equals(is_drgfz_ver)&&"M51.202".equals(ls_jbdm)&&"80.5900x001".equals(ls_ssbm1)){
        ls_drgbh = "IB1C";
      }

      //针对重庆没有IB29的处理，直接替换

      if ("重庆".equals(ls_region)&&"1.1".equals(is_drgfz_ver)&&"IB2".equals(ls_drgbh.substring(0, 3))){
        System.out.println("15----------------------------------drgbh:重庆将IB29直接替换为IB1C" );
        ls_drgbh = "IB1C";
      }

      //找不到原因，直接修改
      if (("01.0900x006".equals(ls_ssbm1)||"01.0900x008".equals(ls_ssbm1)||"01.0900x009".equals(ls_ssbm1)||"04.0304".equals(ls_ssbm1))&&"1.1".equals(is_drgfz_ver)&&"BJ1".equals(ls_drgbh.substring(0, 3))&&"I".equals(ls_jbdm.substring(0, 1))){
        System.out.println("15----------------------------------drgbh:BJ1直接替换为BB2" );
        ls_drgbh = "BB2";
      }

      System.out.println("15----------------------------------drgbh:" + ls_drgbh+ls_drgbh.substring(0, 3));


//      DrgFzqzyGroupMatcher.matchGroup(mainDiagnosis, procedures, daysInHospital);
      // 如果前面找到的分组编码对应分组不存在，则进行以下操作
      int ll_have = -1;
      for (int i = 0; i < ids_drg_dict.size(); i++) {
        if (ids_drg_dict.get(i).getDrgbh().equals(ls_drgbh)) {
          ll_have = i;
          break;
        }
      }

      if (ll_have <= -1 && ls_drgbh.length() >= 3) { // 如果前面找到的分组编码对应分组不存在，依次更换最后一位为“9”，“5”，“1”进行查询
        System.out.println("151----------------------------------"+ll_have);
        ls_drgbh = ls_drgbh.substring(0, 3);
        ll_have = -1;
        for (int i = 0; i < ids_drg_dict.size(); i++) {
          if (ids_drg_dict.get(i).getDrgbh().equals(ls_drgbh.substring(0, 3) + "9")) {
            ll_have = i;
            break;
          }
        }
        if (ll_have > -1) {
          ls_drgbh = ls_drgbh.substring(0, 3) + "9";
        } else {
          ll_have = -1;
          for (int i = 0; i < ids_drg_dict.size(); i++) {
            if (ids_drg_dict.get(i).getDrgbh().equals(ls_drgbh.substring(0, 3) + "5")) {
              ll_have = i;
              break;
            }
          }
          if (ll_have > -1) {
            ls_drgbh = ls_drgbh.substring(0, 3) + "5";
          } else {
            ll_have = -1;
            for (int i = 0; i < ids_drg_dict.size(); i++) {
              if (ids_drg_dict.get(i).getDrgbh().equals(ls_drgbh.substring(0, 3) + "1")) {
                ll_have = i;
                break;
              }
            }
            if (ll_have > -1) {
              ls_drgbh = ls_drgbh.substring(0, 3) + "1";
            }
          }
        }
        if (ll_have <= -1) {
          for (int i = 0; i < ids_drg_dict.size(); i++) {
            if (ids_drg_dict.get(i).getDrgbh().equals(ls_drgbh)) {
              ll_have = i;
              break;
            }
          }
          for (int i = 0; i < ids_drg_dict.size(); i++) {
            if (ids_drg_dict.get(i).getDrgbh().length() >= 3) {
              if (ids_drg_dict.get(i).getDrgbh().substring(0, 3).equals(ls_drgbh)) { // 若都不存在，则获取第一个与当前adrg相同的分组当作最终分组
                ll_have = i;
                break;
              }
            }
          }
        }
      }



      System.out.println("16----------------------------------drgbh:" + ls_drgbh + "  " + ls_one_flag + "  " + ll_have);

      if ("1".equals(ls_one_flag) && ll_have > -1) {

        ls_drgbh = ids_drg_dict.get(ll_have).getDrgbh();

        if (Character.isDigit(ls_drgbh.charAt(ls_drgbh.length() - 1))&&"新疆".equals(ls_region)) {
          ll_have = findOptimalGroups(ll_have, ll_pretype, ls_drgbh);
        }

        System.out.println("17----------------------------------drgbh:" + ls_drgbh);

        DrgFzResult drgFzResult = new DrgFzResult();

        drgFzResult.setBah(ls_bah);
        drgFzResult.setBrbs(ls_brbs);
        drgFzResult.setCode(200);
        drgFzResult.setMessage("更新成功");
        drgFzResult.setDrgbh(ids_drg_dict.get(ll_have).getDrgbh());

        BigDecimal ldec_zfbz;
        BigDecimal ldec_zfqz;
        if (orgLevel != null&&"1".equals(hasMultipleOrg)) {
          ldec_zfbz = getZfbz(orgLevel,ids_drg_dict.get(ll_have));
          ldec_zfqz = getZfqz(orgLevel,ids_drg_dict.get(ll_have));
        } else {
          ldec_zfbz = ids_drg_dict.get(ll_have).getZfbz();
          ldec_zfqz = ids_drg_dict.get(ll_have).getZfqz();
        }

        if ("新疆".equals(ls_region)) {
          ldec_zfqz = ids_drg_dict.get(ll_have).getZfqz();
          //新疆2.0版本区分成年与未成年
          if ("2.0".equals(is_drgfz_ver)) {

            if (ls_nl != null && ls_nl <= 6) {
              logger.info("年龄小于6岁，选择儿童支付标准");
              ldec_zfbz = ids_drg_dict.get(ll_have).getZfbzEt();
            } else {
              ldec_zfbz = ids_drg_dict.get(ll_have).getZfbz();
            }

            int ll_find = -1;
            if (ls_brbs != null && !"".equals(ls_brbs)) {
              ll_find = drgFzqService.selectBrcblb(ls_brbs);
              if (ll_find <= 0) {

                if (ls_nl != null && ls_nl <= 6) {
                  logger.info("年龄小于6岁，选择儿童支付标准");
                  ldec_zfbz = ids_drg_dict.get(ll_have).getZfbzJmEt();
                } else {
                  ldec_zfbz = ids_drg_dict.get(ll_have).getZfbz_jm();
                }

              }
            }
            //新疆1.1版本只区分职工居民
          } else {
            int ll_find = -1;
            if (ls_brbs != null && !"".equals(ls_brbs)) {
              ll_find = drgFzqService.selectBrcblb(ls_brbs);
              if (ll_find <= 0) {
                ldec_zfbz = ids_drg_dict.get(ll_have).getZfbz_jm();
              }
            }
          }
        }

        drgFzResult.setZfqz(ldec_zfqz);
        drgFzResult.setZfy(ls_zfy);
        drgFzResult.setFztype(ids_drg_dict.get(ll_have).getFztype());
        drgFzResult.setZfbz(ldec_zfbz);

        if("1".equals(ls_yfz_cndrg)) {
        	if(ls_cndrgbh!=null&&ls_cndrgbh.length()==3) {
        		ls_cndrgbh = ls_drgbh;
        		 drgFzResult.setCndrgmc(ls_drgbh+"-"+ids_drg_dict.get(ll_have).getDrgmc());
        	}
        	drgFzResult.setCndrgbh(ls_cndrgbh);
        	  for (int i = 0; i < ids_cndrg_dict.size(); i++) {
                  if (ids_cndrg_dict.get(i).getDrgbh().equals(ls_cndrgbh)) {
                	  drgFzResult.setCndrgmc(ids_cndrg_dict.get(i).getDrgmc());
                	  drgFzResult.setCndrgzfqz(ids_cndrg_dict.get(i).getZfqz());
                	  drgFzResult.setCndrgfxdj(ids_cndrg_dict.get(i).getFxdj());
                  }}

        }

        String drgmc = ids_drg_dict.get(ll_have).getDrgmc() + "[权重：" + ldec_zfqz + "]";
        if ("新疆".equals(ls_region)) {
          drgmc=drgmc;
        }
        else
        {
          if (ls_zfy == null) {
            System.out.println("费用异常");
            ls_zfy = new BigDecimal("0");
          }
//          if (ids_drg_dict.get(ll_have) == null) {
//            System.out.println("drgdict异常  " + ll_have);
//          }
          if (orgLevel != null&&"1".equals(hasMultipleOrg)) {

        	  if (ls_zfy.compareTo(new BigDecimal(0)) > 0 && ids_drg_dict.get(ll_have).getJgje() != null&&"1".equals(orgLevel)) {
  	            drgmc += "[极低：" + ids_drg_dict.get(ll_have).getJdje() + "]" + "[极高：" + ids_drg_dict.get(ll_have).getJgje() + "]" ;
              drgFzResult.setJdje(ids_drg_dict.get(ll_have).getJdje());
              drgFzResult.setJgje(ids_drg_dict.get(ll_have).getJgje());
  	          }
        	  if (ls_zfy.compareTo(new BigDecimal(0)) > 0 && ids_drg_dict.get(ll_have).getJgje() != null&&"2".equals(orgLevel)) {
    	            drgmc += "[极低：" + ids_drg_dict.get(ll_have).getJdje2() + "]" + "[极高：" + ids_drg_dict.get(ll_have).getJgje2() + "]" ;
              drgFzResult.setJdje(ids_drg_dict.get(ll_have).getJdje2());
              drgFzResult.setJgje(ids_drg_dict.get(ll_have).getJgje2());
            }
          }
          else {
	          if (ls_zfy.compareTo(new BigDecimal(0)) > 0 && ids_drg_dict.get(ll_have).getJgje() != null) {
	            drgmc += "[极低：" + ids_drg_dict.get(ll_have).getJdje() + "]" + "[极高：" + ids_drg_dict.get(ll_have).getJgje() + "]" ;
              drgFzResult.setJdje(ids_drg_dict.get(ll_have).getJdje());
              drgFzResult.setJgje(ids_drg_dict.get(ll_have).getJgje());
	          }
          }
        }

        if ("重庆".equals(ls_region) && !"qtl".equals(ls_org)) {
          drgmc += drgFzqService.selectCostLowOrHigh(drgFzResult);
        }

        if("重庆".equals(ls_region) && ls_drgbh.length() >= 3 && TCM_ADRG_GROUP.contains(ls_drgbh.substring(0, 3))) {
          String zzl = drgFzqService.selectZzlByBrbs(drgFzResult.getBrbs());
          if(!"00".equals(zzl)) {
            drgmc += String.format("[中治率: %s%%]", new BigDecimal(zzl).multiply(new BigDecimal("100.00")).setScale(2, RoundingMode.HALF_UP));
          }
        }

//        if("1".equals(ls_yfz_cndrg)) {
//        	drgmc += "["+"CNDRG:"+drgFzResult.getCndrgmc()+" 权重："+drgFzResult.getCndrgzfqz()+"]";
//        	if("是".equals(drgFzResult.getCndrgfxdj())) {
//        		drgmc +="低风险";
//        	}
//        }

        if (StringUtils.isNotBlank(drgfz.getYlfkfs())) {
          drgmc += YlfkfsEnum.getDescriptionByCode(drgfz.getYlfkfs());
        }
        if("1".equals(ls_yfz_cndrg)) {
          drgmc += "["+"CNDRG:"+drgFzResult.getCndrgmc()+" 权重："+drgFzResult.getCndrgzfqz()+"]";
          if("是".equals(drgFzResult.getCndrgfxdj())) {
            if("死亡".equals(ls_lyfs)||"5".equals(ls_lyfs)) {
              drgmc +="[低风险死亡]";
            }
            else
            {
              drgmc +="[低风险]";
            }
          }
        }

        if (drgmc!=null&&drgmc.contains("null")) {
        	drgmc.replace("null", "");
        }

        drgFzResult.setDrgmc(drgmc);


        drgFzResult.setZydays(BigDecimal.valueOf(ls_sjzyts));
        drgFzResult.setPjdays(ids_drg_dict.get(ll_have).getPjdays());

        System.out.println("----------------------分组信息-------------------------");
        System.out.println("DRG编码：" + ids_drg_dict.get(ll_have).getDrgbh());
        System.out.println("支付权重：" + ids_drg_dict.get(ll_have).getZfqz());
        System.out.println("总费用：" + ls_zfy);
        System.out.println("分组类型：" + ids_drg_dict.get(ll_have).getFztype());
        System.out.println("支付标准：" + ldec_zfbz);
        System.out.println("DRG名称：" + ids_drg_dict.get(ll_have).getDrgmc());
        System.out.println("实际住院天数：" + ls_sjzyts);
        System.out.println("标准床日：" + ids_drg_dict.get(ll_have).getPjdays());
        System.out.println("-----------------------------------------------------");

        if ("1".equals(ls_not_tj)) {
          ls_tj_zdss = "";
        } else {
          ls_tj_zdss = of_ai_drg_fz(ls_bah, ls_jbdm + ',' + ls_jbdm1_15, ls_ssbm1_7);
        }

        return drgFzResult;
      } else if ("1".equals(ls_one_flag) && ll_have <= -1) {
        System.out.println("----------------------分组信息-------------------------");
        System.out.println("DRG编码：" + ls_drgbh);
        System.out.println("-----------------------------------------------------");

        DrgFzResult drgFzResult = new DrgFzResult();
        drgFzResult.setBah(ls_bah);
        drgFzResult.setBrbs(ls_brbs);

        if("1".equals(ls_yfz_cndrg)) {
        	drgFzResult.setCndrgbh(ls_cndrgbh);
        	  for (int i = 0; i < ids_cndrg_dict.size(); i++) {
                  if (ids_cndrg_dict.get(i).getDrgbh().equals(ls_cndrgbh)) {
                	  drgFzResult.setCndrgmc(ids_cndrg_dict.get(i).getDrgmc());
                	  drgFzResult.setCndrgzfqz(ids_cndrg_dict.get(i).getZfqz());
                	  drgFzResult.setCndrgfxdj(ids_cndrg_dict.get(i).getFxdj());
                  }}


        }

        drgFzResult.setCode(200);
        drgFzResult.setMessage("更新成功");
        drgFzResult.setDrgbh(ls_drgbh);

        return drgFzResult;
      }

      BaSyjl baSyjl = new BaSyjl();
      baSyjl.setBrbs(ls_brbs);
      baSyjl.setDrgbh(ls_drgbh);
      if ("000".equals(ls_drgbh)) {
        baSyjl.setRzflag(0);
        baSyjl.setWrzyy("未找到对应的诊断");
      } else {
        baSyjl.setRzflag(1);
        baSyjl.setWrzyy("");
      }
      baSyjlService.updateBaSyjl(baSyjl);
    }

    return null;
  }


  private String getcndrgbh(String ls_drgbh, long ll_pretype) {
	    // 条件检查：如果不是特定类型直接返回
	    if (!"1".equals(ls_yfz_cndrg)) {
	        return ls_drgbh;
	    }

	    // 确保编码长度足够
	    if (ls_drgbh.length() < 3) {
	        return ls_drgbh;
	    }

	    String adrgbh = ls_drgbh.substring(0, 3);
	    String candidate = null;  // 用于存储找到的有效编码

	    if (ll_pretype == 1) { // MCC 情况
	        // 按优先级检查：1 -> 3 -> 5 -> 9
	        if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "1")) {
	            candidate = adrgbh + "1";
	        } else if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "3")) {
	            candidate = adrgbh + "3";
	        } else if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "5")) {
	            candidate = adrgbh + "5";
	        } else if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "9")) {
	            candidate = adrgbh + "9";
	        }
	    } else if (ll_pretype == 2) { // CC 情况
	        // 按优先级检查：3 -> 5 -> 9
	        if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "3")) {
	            candidate = adrgbh + "3";
	        } else if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "5")) {
	            candidate = adrgbh + "5";
	        } else if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "9")) {
	            candidate = adrgbh + "9";
	        }
	    } else { // 其他情况
	        // 按优先级检查：9 -> 5
	        if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "9")) {
	            candidate = adrgbh + "9";
	        } else if (checkDrgbhExists(ids_cndrg_dict, adrgbh + "5")) {
	            candidate = adrgbh + "5";
	        }
	    }
	    

	    
	    // 返回找到的有效编码或原始值
	    return (candidate != null) ? candidate : ls_drgbh;
	}

  private  boolean isNumeric(String str) {
      if (str == null || str.trim().isEmpty()) {
          return false;
      }
      try {
          Double.parseDouble(str);
          return true;
      } catch (NumberFormatException e) {
          // 处理特殊情况：整数部分以0开头但非纯0
          if (str.matches("^0\\d+.*$")) {
              return false;
          }
          // 处理科学计数法
          return str.matches("^[-+]?\\d*\\.?\\d+(?:[eE][-+]?\\d+)?$");
      }
  }

  // 涉及手术操作目录
  public String gf_check_ss_qy(String[] ls_bqy_ssarr, String ls_ssbm1, String ls_qyflag) {

//    SELECT MAX(chap_name),max(sub_name),max(oprn_oprt_name) into :ls_sslb,:ls_ss_subname,:ls_ss_name FROM 手术操作目录   WHERE oprn_oprt_code LIKE concat(:ls_ssbm1,'%');
//    for (int ll_i = 0; ll_i < ls_bqy_ssarr.length; ll_i++) {
////      if (ls_ss_name.indexOf("缝合术") > -1) {
////        break;
////      }
//
//      if (ls_ss_subname.length() >= ls_bqy_ssarr[ll_i].length()) {
//        if (ls_ss_subname.indexOf(ls_bqy_ssarr[ll_i]) > -1) {
//          ls_qyflag = "0";
//        }
//      }
//    }

    return ls_qyflag;
  }

  public String gf_check_dqx(String ls_dqxadrg, String ls_adrgbh) {
    if (ls_dqxadrg == null || "".equals(ls_dqxadrg)) {
      return "0";
    }
    String[] ls_dqxarr = ls_dqxadrg.split(";");
    String ls_flag = "0";
    for (int ll_i = 0; ll_i < ls_dqxarr.length; ll_i++) {
      if (ls_adrgbh.length() >= ls_dqxarr.length) {
        if (ls_adrgbh.substring(0, ls_dqxarr[ll_i].length()).equals(ls_dqxarr[ll_i])) {
          ls_flag = "1";
        }
      }
    }
    return ls_flag;
  }

  public String of_ai_drg_fz(String bah, String zdxx, String ssxx) {
    String ls_brid = "", ls_zyid = "";
    String ls_return = "";

    if (bah == null || "".equals(bah)) {
      return "";
    }

    Brxx brxx = new Brxx();
    brxx.setZyh(bah);

    List<Brxx> brxxByZyh = brxxService.selectBrxxList(brxx);
    if (brxxByZyh.size() > 0) {
      ls_brid = brxxByZyh.get(0).getBrid();
      ls_zyid = brxxByZyh.get(0).getZyid();
    }

    if (ls_brid == null || "".equals(ls_brid)) {
      return "";
    }

//    ls_return = ino_trans_data.of_ai_drg_fz(ls_brid,ls_zyid,as_zdjl,as_ssjl);

    return ls_return;
  }

//  public void of_fz_init() {
//    if (ids_drg_dict == null || ids_drg_dict.size() == 0) {
//      ids_drg_dict = drgdictService.selectDrgdictList(new Drgdict());
//    }
//    if (ids_drg_bfz == null || ids_drg_bfz.size() == 0) {
//      ids_drg_bfz = drgFzqService.selectDrgBfzList();
//    }
//    if (ids_drg_bfz_pc == null || ids_drg_bfz_pc.size() == 0) {
//      ids_drg_bfz_pc = drgFzqService.selectDrgBfzpcList();
//    }
//    if (ids_drg_adrbzss == null || ids_drg_adrbzss.size() == 0) {
//      ids_drg_adrbzss = drgFzqService.selectAdrgbzssList();
//    }
//  }

  public String of_drg_fz_sub1(List<DrgAdrgFz> ids_get_adrg, String[] ls_mdcarr, String ls_adrgbh, String ls_drgbh,
                               String ls_jbdm, String[] ls_ssjlarr, String ls_sslb, String ls_zdmc, String ls_ssbm1) {

    List<DrgAdrgFz> ids_fz_getbsss = new ArrayList<>();
    DrgAdrgFz drgAdrgFz = new DrgAdrgFz();

    // 删除不符合的adrg,也就是没得对应的mdc
    for (int ll_j = 0; ll_j < ids_get_adrg.size(); ll_j++) {
      ids_get_adrg.get(ll_j).setId(0L);
    }
    String[] ls_ssbm1arr;
    ls_ssbm1arr = new String[1];
    ls_ssbm1arr[0] = ls_ssbm1;
//    if (ls_ssjlarr.length>0 ) {
//    	ls_ssbm1arr[0] = ls_ssbm1;
//    }

    // 保留Adrgbh首字母与Adrgbh=Mdcbh记录中Adrgbh尾字母相同的记录
    for (int ll_ii = 0; ll_ii < ls_mdcarr.length; ll_ii++) {
      for (int ll_j = 0; ll_j < ids_get_adrg.size(); ll_j++) {
        if (ids_get_adrg.get(ll_j).getAdrgbh().length() >= 1) {
          if (ls_mdcarr[ll_ii].equals(ids_get_adrg.get(ll_j).getAdrgbh().substring(0, 1))) {
            ids_get_adrg.get(ll_j).setId(1L);
          }
        }
      }
    }

    for (int i = 0; i < ids_get_adrg.size(); i++) {
      if (ids_get_adrg.get(i).getId() == 0) {
        ids_get_adrg.remove(i);
        i--;
      }
    }
    // 以上删除不符合的MDC

    System.out.println("----------------   剩余相关分组信息1  ------------------");
    for (int i = 0; i < ids_get_adrg.size(); i++) {
      System.out.println("Adrgbh:" + ids_get_adrg.get(i).getAdrgbh() + "   Mdcbh:"
        + ids_get_adrg.get(i).getMdcbh() + "   Icdbh:" + ids_get_adrg.get(i).getIcdbh() + "   Icdname:"
        + ids_get_adrg.get(i).getIcdname() + "   Icdtype:" + ids_get_adrg.get(i).getIcdtype());
    }
    System.out.println("-------------------------------------------------");

    // 如果是手术，则删除R, S, T, U, V, W, X, Y, Z 9个字母表示内科部分
    for (int ll_ii = 0; ll_ii < ids_get_adrg.size(); ll_ii++) {
      DrgAdrgFz adrgllii = ids_get_adrg.get(ll_ii);
      ls_adrgbh = adrgllii.getAdrgbh();
      if ("手术".equals(adrgllii.getSsbw())||"介入治疗".equals(adrgllii.getSsbw())) {
        for (int ll_j = 0; ll_j < ids_get_adrg.size(); ll_j++) {
          DrgAdrgFz adrgllj = ids_get_adrg.get(ll_j);
          if (adrgllj.getAdrgbh().length() >= 1 && ls_adrgbh.length() >= 1
            && adrgllj.getAdrgbh().length() >= 1) {
            if (adrgllj.getAdrgbh().substring(0, 1).equals(ls_adrgbh.substring(0, 1))
              && adrgllj.getAdrgbh().charAt(1) >= 'R') {
              ids_get_adrg.get(ll_j).setId(0L);
            }
          }
        }
      }
    }

//    System.out.println("-------------------------------------------------");

//    System.out.println("8----------------------------------drgbh:" + ls_drgbh);

    // 删除非伴生
    for (int ll_ii = 0; ll_ii < ids_get_adrg.size(); ll_ii++) {
      if ("同时包含".equals(ids_get_adrg.get(ll_ii).getAndflag())
        || "同时包含和".equals(ids_get_adrg.get(ll_ii).getAndflag())) {
        int ll_find = -1;

        ids_fz_getbsss = getTogetherDrgAdrgFz(ls_ssbm1arr,ids_get_adrg.get(ll_ii).getAdrgbh());
        if (ids_fz_getbsss.size() > 0) {
          if (ids_fz_getbsss.size() == 2) {
            ll_find = 0;
            ls_drgbh = ids_get_adrg.get(ll_ii).getAdrgbh();
          }
        }

//        System.out.println("9----------------------------------drgbh:" + ls_drgbh);

        if (ll_find == -1) {
          if ("同时包含".equals(ids_get_adrg.get(ll_ii).getAndflag())) {
            for (DrgAdrgFz adrgFz : ids_get_adrg) {
              if ("同时包含和".equals(adrgFz.getAndflag())
                && ids_get_adrg.get(ll_ii).getAdrgbh().equals(adrgFz.getAdrgbh())
                && !ids_get_adrg.get(ll_ii).getIcdbh().equals(drgAdrgFz.getIcdbh())) {
                ll_find = ids_get_adrg.indexOf(adrgFz);
              }
            }
            if (ll_find == -1) {
              ids_get_adrg.get(ll_ii).setId(0L);
            }
            if (ll_find > -1) {
              ls_drgbh = ids_get_adrg.get(ll_ii).getAdrgbh();
            }
          }

          if ("同时包含和".equals(ids_get_adrg.get(ll_ii).getAndflag())) {
            for (DrgAdrgFz adrgFz : ids_get_adrg) {
              if ("同时包含".equals(adrgFz.getAndflag())
                && ids_get_adrg.get(ll_ii).getAdrgbh().equals(adrgFz.getAdrgbh())
                && !ids_get_adrg.get(ll_ii).getIcdbh().equals(drgAdrgFz.getIcdbh())) {
                ll_find = ids_get_adrg.indexOf(adrgFz);
              }
            }
            if (ll_find == -1) {
              ids_get_adrg.get(ll_ii).setId(0L);
            }
            if (ll_find > -1) {
              ls_drgbh = ids_get_adrg.get(ll_ii).getAdrgbh();
            }
          }
        }
        if (ll_find == -1) {
          ids_get_adrg.get(ll_ii).setId(0L);
        }
      }
    }

    // 通过伴生已确定
    if ("".equals(ls_drgbh)) {
      for (int i = 0; i < ids_get_adrg.size(); i++) {
        if (ids_get_adrg.get(i).getId() == 0) {
          ids_get_adrg.remove(i);
          i--;
        }
      }

      for (int ll_ii = 0; ll_ii < ids_get_adrg.size(); ll_ii++) {
        // 先找有没得对应的adrg
        // 当没有手术时删除没有手术的adrg 只保留内科adrg
        ls_adrgbh = ids_get_adrg.get(ll_ii).getAdrgbh();
        if (ls_adrgbh.length() >= 2) {
          if (ls_adrgbh.charAt(1) >= 'A' && ls_adrgbh.charAt(1) <= 'Q') {
            int ll_find = -1;
            for (DrgAdrgFz adrgFz : ids_get_adrg) {
              if (adrgFz.getAdrgbh().charAt(0) == ls_adrgbh.charAt(0)
                && "2".equals(adrgFz.getIcdtype())) {
                ll_find = ids_get_adrg.indexOf(adrgFz);
              }
            }
            if (ll_find == -1) {
              ids_get_adrg.get(ll_ii).setId(0L);
            }
          }
        }
      }

      for (int i = 0; i < ids_get_adrg.size(); i++) {
        if (ids_get_adrg.get(i).getId() == 0) {
          ids_get_adrg.remove(i);
          i--;
        }
      }

      // 当有手术有多个adrg时，前面的必须满足有相同adrg的诊断----只针对手术
      List<DrgAdrgFz> ids_get_adrg_temp = new ArrayList<>();
      for (int ll_ii = 0; ll_ii < ls_mdcarr.length; ll_ii++) {
        for (DrgAdrgFz adrgFz : ids_get_adrg) {
          if (adrgFz.getAdrgbh().length() >= 1) {
            if (adrgFz.getAdrgbh().substring(0, 1).equals(ls_mdcarr[ll_ii])
              && "2".equals(adrgFz.getIcdtype())) {
              ids_get_adrg_temp.add(adrgFz);
            }
          }
        }

//        System.out.println("-----------------------------手术adrg数量：" + ids_get_adrg_temp.size());

        // 有多个
        if (ids_get_adrg_temp.size() > 1) {
          for (int ll_j = 0; ll_j < ids_get_adrg_temp.size() - 1; ll_j++) {
            ls_adrgbh = ids_get_adrg_temp.get(ll_j).getAdrgbh();

            long ll_find = countDrgAdrgFz(ls_jbdm, "1", ls_adrgbh);

            if ("NF1".equals(ids_get_adrg.get(ll_j + 1).getAdrgbh())
              || "LB1".equals(ids_get_adrg.get(ll_j + 1).getAdrgbh())) {
              break;
            }

            if (ll_find == 0) {
              ids_get_adrg.get(ll_j).setId(0L);
            }
          }
        }
      }
      // 删除
      for (int i = 0; i < ids_get_adrg_temp.size(); i++) {
        if (ids_get_adrg_temp.get(i).getId() == 0) {

          for (int i1 = 0; i1 < ids_get_adrg.size(); i1++) {
            if (ids_get_adrg.get(i1) == ids_get_adrg_temp.get(i)) {
              ids_get_adrg.remove(i1);
            }
          }

          ids_get_adrg_temp.remove(i);
          i--;
        }
      }

      // 删除分诊信息
      for (int i = 0; i < ids_get_adrg.size(); i++) {
        if (ids_get_adrg.get(i).getAdrgbh().length() >= 3) {
          if ("MDC".equals(ids_get_adrg.get(i).getAdrgbh().substring(0, 3))) {
            ids_get_adrg.remove(i);
          }
        }
      }

      // 删除
      for (int i = 0; i < ids_get_adrg.size(); i++) {
        if (ids_get_adrg.get(i).getId() == 0) {
          ids_get_adrg.remove(i);
          i--;
        }
      }

      System.out.println("----------------   剩余相关分组信息2  ------------------");
      for (int i = 0; i < ids_get_adrg.size(); i++) {
        System.out.println("Adrgbh:" + ids_get_adrg.get(i).getAdrgbh() + "   Mdcbh:"
          + ids_get_adrg.get(i).getMdcbh() + "   Icdbh:" + ids_get_adrg.get(i).getIcdbh() + "   Icdname:"
          + ids_get_adrg.get(i).getIcdname() + "   Icdtype:" + ids_get_adrg.get(i).getIcdtype()
          + "   sslb:" + ids_get_adrg.get(i).getSsbw());
      }
      System.out.println("-------------------------------------------------");

      // 当有多个adrg时，前面的必须满足有相同adrg的诊断---针对有诊断和手术
      /**
       * 判断是否包含手术相关adrg： 包含：遍历所有的诊断相关adrg 查看其是否存在对应的手术adrg记录 存在：continue
       * 不存在：当前记录是否为最后一条记录： 是最后一条：跳过 不是最后一条：查看后一条记录是否为手术记录： 不是：跳过
       * 是：若当前记录为低权限记录则删除该记录，不是则跳过 不包含：跳过-后面直接获取第一条adrg的adrgbh
       */
      for (int ll_ii = 0; ll_ii < ls_mdcarr.length; ll_ii++) {
        ids_get_adrg_temp = new ArrayList<>();
        int ll_find = -1;
        for (DrgAdrgFz adrgFz : ids_get_adrg) {
          if (adrgFz.getAdrgbh().length() >= 1) {
            if (adrgFz.getAdrgbh().substring(0, 1).equals(ls_mdcarr[ll_ii])) {
              ids_get_adrg_temp.add(adrgFz);
            }
          }
        }
        // 判断是否有手术
        if (ids_get_adrg_temp.size() > 1) {
          for (int i = 0; i < ids_get_adrg_temp.size(); i++) {
            if ("2".equals(ids_get_adrg_temp.get(i).getIcdtype())) {
              ll_find = i;
              break;
            }
          }
          // 有手术
          if (ll_find > -1) {
            ls_sslb = ids_get_adrg_temp.get(ll_find).getSsbw();
            for (int ll_j = 0; ll_j < ids_get_adrg_temp.size(); ll_j++) {
              ls_adrgbh = ids_get_adrg_temp.get(ll_j).getAdrgbh();
              if ("1".equals(ids_get_adrg_temp.get(ll_j).getIcdtype())) {
                ll_find = -1;
                // 查找是否有对应的手术
                for (int i = 0; i < ids_get_adrg_temp.size(); i++) {
                  if (ls_adrgbh.equals(ids_get_adrg_temp.get(i).getAdrgbh())
                    && "2".equals(ids_get_adrg_temp.get(i).getIcdtype())) {
                    ll_find = i;
                  }
                }
                if (ll_find == -1) { // 如果没有对应手术，则判断当前是否为最后一条记录
                  if (ll_j != ids_get_adrg_temp.size() - 1) { // 如果不是最后一条记录则判断该记录adrg是否为低权限
                    if (ls_adrgbh.length() >= 1
                      && ids_get_adrg_temp.get(ll_j + 1).getAdrgbh().length() >= 1) {
                      if ("2".equals(ids_get_adrg_temp.get(ll_j + 1).getIcdtype())
                        && ls_adrgbh.substring(0, 1).equals(ids_get_adrg_temp.get(ll_j + 1)
                        .getAdrgbh().substring(0, 1))) {
                        if ("1".equals(gf_check_dqx(ls_dqxadrg, ls_adrgbh))) { // 若当前记录不是最后一条，且属于低权限adrg，则删除该条adrg记录
                          ids_get_adrg_temp.get(ll_j).setId(0L);
                        }
                      }
                    }
                  }
                }
              }
            }
          }

        }
      }

      // 删除
      for (int i = 0; i < ids_get_adrg_temp.size(); i++) {
        if (ids_get_adrg_temp.get(i).getId() == 0) {

          for (int i1 = 0; i1 < ids_get_adrg.size(); i1++) {
            if (ids_get_adrg.get(i1) == ids_get_adrg_temp.get(i)) {
              ids_get_adrg.remove(i1);
            }
          }

          ids_get_adrg_temp.remove(i);
          i--;
        }
      }

      System.out.println("----------------   剩余相关分组信息3  ------------------");
      for (int i = 0; i < ids_get_adrg.size(); i++) {
        System.out.println("Adrgbh:" + ids_get_adrg.get(i).getAdrgbh() + "   Mdcbh:"
          + ids_get_adrg.get(i).getMdcbh() + "   Icdbh:" + ids_get_adrg.get(i).getIcdbh() + "   Icdname:"
          + ids_get_adrg.get(i).getIcdname() + "   Icdtype:" + ids_get_adrg.get(i).getIcdtype());
      }
      System.out.println("-------------------------------------------------");

      String[] ls_adrgarr = new String[ids_get_adrg.size()];
      for (int ll_j = 0; ll_j < ids_get_adrg.size(); ll_j++) {
        ls_adrgarr[ll_j] = ids_get_adrg.get(ll_j).getAdrgbh();
      }

//      System.out.println("11----------------------------------drgbh:" + ls_drgbh);

      if (ids_get_adrg.size() > 0) {

        ls_drgbh = ids_get_adrg.get(0).getAdrgbh(); // 默认获取第一条adrg记录的编号


        if (ids_get_adrg.size() > 1) { // 若存在多条adrg记录，以下为特殊情况

          if ("NA1".equals(ids_get_adrg.get(0).getAdrgbh())
            && "NA2".equals(ids_get_adrg.get(1).getAdrgbh())) {
            ls_drgbh = ids_get_adrg.get(1).getAdrgbh();
          }
          if (ids_get_adrg.get(0).getAdrgbh().length() >= 2
            && ids_get_adrg.get(1).getAdrgbh().length() >= 2) {
            if ("JA".equals(ids_get_adrg.get(0).getAdrgbh().substring(0, 2))
              && "JB".equals(ids_get_adrg.get(1).getAdrgbh().substring(0, 2))) {
              ls_drgbh = ids_get_adrg.get(1).getAdrgbh();
            }
            // 除恶性肿瘤手术外的肾、输尿管、膀胱手术LB
            if ("LA".equals(ids_get_adrg.get(0).getAdrgbh().substring(0, 2))
              && "LB".equals(ids_get_adrg.get(1).getAdrgbh().substring(0, 2))
              && ls_zdmc.indexOf("肿瘤") == -1) {
              ls_drgbh = ids_get_adrg.get(1).getAdrgbh();
            }
          }
        }
      }

      System.out.println("12----------------------------------drgbh:" + ls_drgbh);
    }

    return ls_drgbh;
  }


  public int checkhavess(String as_ssbm) {
    int li_have=0;
    if (as_ssbm==null||"".equals(as_ssbm)) {
      return li_have;
    }

    List<DrgAdrgFz> ids_get_adrg_havess = getDrgAdrgFzByIcd(null,as_ssbm);
    if(ids_get_adrg_havess!=null) {
      li_have = ids_get_adrg_havess.size();
    }
    return li_have;
  }

  /**
   * 根据年龄和并发症获取DRG分组编号
   *
   * @param adrgbh     adrgbh
   * @param age        年龄
   * @param ll_pretype 伴并发症合并症标识 1-MCC 2-CC 3-不伴
   * @return
   */
  public String findDrgbhByAgeAndBfz(String adrgbh, Long age, Long ll_pretype) {
    // 如果年龄不符合返回null
    if (age == null || (age >= 18 && age <= 59)) {
      return null;
    }

    List<Drgdict> drgdictList = getDrgDictByAdrgbh(adrgbh);
    drgdictList.removeIf(obj -> obj == null || obj.getDrgbh() == null || obj.getDrgmc() == null);

    // 如果包括年龄的分组数量为0返回null
    if (drgdictList.isEmpty()) {
      return null;
    }

    // 根据年龄和并发症筛选分组
    String ageCondition = age < 18 ? "小于18岁" : "大于等于60岁";
    String bfzCondition = ll_pretype == 1 ? "伴严重" : ll_pretype == 2 ? "伴一般" : "不伴";

    for (Drgdict drgdict : drgdictList) {
      String drgmc = drgdict.getDrgmc();
      if (drgmc.contains(ageCondition) && (drgmc.contains(bfzCondition) || isNotContainBfzCondition(drgmc))) {
        return drgdict.getDrgbh();
      }
    }

    return null;
  }


  public String findDrgbhByAgeAndBfzXjks(String adrgbh, Long age, Long bzyzsnl) {
    if (age == null && bzyzsnl == null) {
      return null;
    }

    List<Drgdict> drgdictList = getDrgDictByAdrgbh(adrgbh);
    drgdictList.removeIf(obj -> obj == null || obj.getDrgbh() == null || obj.getDrgmc() == null);

    if (drgdictList.isEmpty()) {
      return null;
    }

    String ageCondition;
    if (age != null) {
      ageCondition = age <= 14 ? "14岁及以下" : "14岁以上";
    } else {
      ageCondition = bzyzsnl < 29 ? "出生年龄<29天" : "29天≤出生年龄<1周岁";
    }

    for (Drgdict drgdict : drgdictList) {
      if (drgdict.getDrgmc().contains(ageCondition)) {
        return drgdict.getDrgbh();
      }
    }


    return null;
  }

  public boolean isNotContainBfzCondition(String drgmc) {
    return !drgmc.contains("伴严重") && !drgmc.contains("伴一般") && !drgmc.contains("不伴");
  }


  public boolean checkDrgbhExists(List<Drgdict> ids_drg_dict, String ls_drgbh) {
	    if (ids_drg_dict == null || ls_drgbh == null) {
	        return false;
	    }

	    for (Drgdict drg : ids_drg_dict) {
	        if (ls_drgbh.equals(drg.getDrgbh())) {
	            return true;
	        }
	    }

	    return false;
	}

  /**
   * 查找最优分组
   *
   * @param ll_have
   * @param ll_pretype
   * @param ls_drgbh
   * @return
   */
  public int findOptimalGroups(int ll_have, Long ll_pretype, String ls_drgbh) {
    Drgdict drgdict = ids_drg_dict.get(ll_have);
    String drgmc = drgdict.getDrgmc();
    String targetBfzCondition = null;

    List<Drgdict> drgdictByAdrgbh = getDrgDictByAdrgbh(ls_drgbh.substring(0, 3));
    drgdictByAdrgbh.removeIf(Objects::isNull);

    Map<String, Integer> dictIndexMap = buildDictIndexMap(ids_drg_dict);
    if ( "2.0".equals(is_drgfz_ver)) {
//    if ("新疆".equals(ls_region) && "2.0".equals(is_drgfz_ver)) {
      //伴严重
      //不伴严重
      //伴合并
      //不伴合并
      //伴一般
      if (ll_pretype == 1) {
        if (drgmc.contains("伴严重")) {
          return ll_have;
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("伴严重")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("伴合并症")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        return ll_have;
      } else if (ll_pretype == 2) {
        if (drgmc.contains("伴一般") || drgmc.contains("伴合并症") || drgmc.contains("不伴严重")) {
          return ll_have;
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("伴一般")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("伴合并症")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("不伴严重")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        return ll_have;
      } else {
        if (drgmc.contains("不伴合并症") || (!drgmc.contains("伴")&&!drgmc.contains("中医治疗"))) {
          return ll_have;
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("不伴合并症")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("不伴严重")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
            if (drgdictItem.getDrgmc().contains("不伴并发症")) {
              Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
              if (index != null) {
                return index;
              }
            }
          }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (!drgdictItem.getDrgmc().contains("伴")&&!drgmc.contains("中医治疗")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        return ll_have;
      }
    } else {
      if (ll_pretype == 1) {
        if (drgmc.contains("伴严重")) {
          return ll_have;
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("伴严重")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("伴合并症")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        return ll_have;
      } else if (ll_pretype == 2) {
        if (drgmc.contains("伴一般")) {
          return ll_have;
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("伴一般")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("伴合并症")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        return ll_have;
      } else {
        if (drgmc.contains("不伴") || (!drgmc.contains("伴")&&!drgmc.contains("中医治疗"))) {
          return ll_have;
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (drgdictItem.getDrgmc().contains("不伴")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        for (Drgdict drgdictItem : drgdictByAdrgbh) {
          if (!drgdictItem.getDrgmc().contains("伴")&&!drgmc.contains("中医治疗")) {
            Integer index = dictIndexMap.get(drgdictItem.getDrgbh());
            if (index != null) {
              return index;
            }
          }
        }
        return ll_have;
      }
    }
  }


  public Map<String, Integer> buildDictIndexMap(List<Drgdict> drgdictList) {
    Map<String, Integer> indexMap = new HashMap<>();
    for (int i = 0; i < drgdictList.size(); i++) {
      indexMap.put(drgdictList.get(i).getDrgbh(), i);
    }
    return indexMap;
  }

  /**
   * 根据配置的信息找到符合的分组
   */
  public String findDrgbhByConfig(String adrgbh, String ls_jbdm, String ls_jbdm1_15, String ls_ssbm1_7, Long age) {
    if ((ls_jbdm == null || ls_jbdm.isEmpty()) && (ls_ssbm1_7 == null || ls_ssbm1_7.isEmpty())) {
      return null;
    }

    List<Drgdict> drgdictList = getDrgDictByAdrgbh(adrgbh);
    //删除sqstr为空的记录
    drgdictList.removeIf(obj -> obj == null || obj.getSqlstr() == null || obj.getSqlstr().isEmpty()
      || (!obj.getSqlstr().contains(":") && !obj.getSqlstr().toLowerCase().contains("select")));

    if (drgdictList.size() == 0) {
      return null;
    }
    //检查sqstr是否有 icd10,icd9
    boolean needIcd10 = drgdictList.stream().anyMatch(drgdict -> drgdict.getSqlstr().contains("icd10"));
    boolean needIcd09 = drgdictList.stream().anyMatch(drgdict -> drgdict.getSqlstr().contains("icd09"));

    //取所有诊断的名称
    String[] icd10Names = needIcd10
      ? "".equals(ls_jbdm1_15) ? getDiagNameByCode(ls_jbdm) : getDiagNameByCode(ls_jbdm + "," + ls_jbdm1_15)
      : new String[0];
    //取所有手术的名称
    String[] icd09Names = needIcd09 ? getSurgNameByCode(ls_ssbm1_7) : new String[0];


    //进行条件判断
    for (Drgdict drgdict : drgdictList) {
      String sqlStr = drgdict.getSqlstr();
      String drgmc = drgdict.getDrgmc();
      try {
        if (sqlStr.contains("|") || (sqlStr.indexOf(':') == sqlStr.lastIndexOf(':') && sqlStr.indexOf(':') > -1)) { // 只需要满足一个条件
          String[] conditions = sqlStr.split("\\|");
          for (String condition : conditions) {
            if (isContainsConfigCondition(condition, icd10Names, icd09Names)) {
              return drgdict.getDrgbh();
            }
          }
        } else if (sqlStr.toLowerCase().contains("select ")) { // 用sql来取，检查编号
          Integer accordNum = 0;
          List<Entity> zdssxx = null;
          try {
            zdssxx = Db.use(nativeDataSource).query(sqlStr, new HashMap<>());
          } catch (Exception e) {
            // TODO: handle exception
          }
          if (zdssxx != null) {
            if (drgmc.contains("联合手术")) {
              accordNum = -1;
              //联合手术，保比较其他手术
              if (ls_ssbm1_7 != null) {
                int commaIndex = ls_ssbm1_7.indexOf(",");
                if (commaIndex != -1) {
                  ls_ssbm1_7 =  ls_ssbm1_7.substring(commaIndex + 1).trim();
                  for (int ll_i = 0; ll_i < zdssxx.size(); ll_i++) {
                    if (ls_ssbm1_7 != null) {
                      if (ls_ssbm1_7.indexOf(zdssxx.get(ll_i).getStr("bm")) > -1) {
                        accordNum = 2;
                      }
                    }
                  }
                }
              }

            } else if (drgmc.contains("有创呼吸机")) {
              accordNum = -1;
              for (int ll_i = 0; ll_i < zdssxx.size(); ll_i++) {
                if (ls_ssbm1_7 != null) {
                  if (ls_ssbm1_7.indexOf(zdssxx.get(ll_i).getStr("bm")) > -1) {
                    accordNum = 2;
                  }
                }
              }
            }
            else {
              //只比较主手术和主诊断
              String str_zss = null;
              if (ls_ssbm1_7 != null) {
                int commaIndex = ls_ssbm1_7.indexOf(',');
                // 如果找到了逗号
                if (commaIndex != -1) {
                  // 提取逗号前的子字符串（不包括逗号）
                  str_zss = ls_ssbm1_7.substring(0, commaIndex);
                } else {
                  str_zss = ls_ssbm1_7;
                }
              }
              for (int ll_i = 0; ll_i < zdssxx.size(); ll_i++) {
                if (str_zss != null) {
                  if (str_zss.indexOf(zdssxx.get(ll_i).getStr("bm")) > -1) {
                    accordNum++;
                  }
                }
                if (ls_jbdm != null) {
                  if (ls_jbdm.indexOf(zdssxx.get(ll_i).getStr("bm")) > -1) {
                    accordNum++;
                  }
                }
              }
            }

            if (accordNum >= 1) {

              if (drgmc.contains("大于等于18岁")) {
                if (age >= 18) {
                  return drgdict.getDrgbh();
                }
              } else {
                return drgdict.getDrgbh();
              }
            }
          }
        } else if (sqlStr.contains("&")) { // 同时满足多个条件
          String[] conditions = sqlStr.split("&");
          Integer accordNum = 0;
          for (String condition : conditions) {
            if (isContainsConfigCondition(condition, icd10Names, icd09Names)) {
              accordNum++;
            }
          }
          if (accordNum == conditions.length) {
            return drgdict.getDrgbh();
          }
        } else if (sqlStr.contains("-")) { // 只能满足一个
          String[] conditions = sqlStr.split("-");
          Integer accordNum = 0;
          for (String condition : conditions) {
            if (isContainsConfigCondition(condition, icd10Names, icd09Names)) {
              accordNum++;
            }
          }
          if (accordNum == 1) {
            return drgdict.getDrgbh();
          }
        }
      } catch (Exception e) {
        logger.error(sqlStr + "配置执行出错！！！！");
        return null;
      }
    }

    return null;
  }

  /**
   * 根据诊断编码获取诊断名称
   */
  public String[] getDiagNameByCode(String diagCodeStr) {
    if (diagCodeStr == null || diagCodeStr.trim().isEmpty() || diagCodeStr.equals(",")) {
      return new String[0]; // 直接返回空数组以避免无效查询
    }

    String[] diagCodeArr = diagCodeStr.split(",");
    // 过滤掉空字符串，确保数组中不包含 ""
    diagCodeArr = Arrays.stream(diagCodeArr).filter(StringUtils::isNotBlank).toArray(String[]::new);

    if (diagCodeArr.length != 0) {
      return baBrzdxxService.selectIcd10NamesByIcd10Codes(diagCodeArr);
    }
    return new String[0];
  }

  /**
   * 根据手术编码获取手术名称
   */
  public String[] getSurgNameByCode(String surgCodeStr) {
    if (surgCodeStr == null || surgCodeStr.trim().isEmpty() || surgCodeStr.equals(",")) {
      return new String[0]; // 直接返回空数组以避免无效查询
    }

    String[] surgCodeArr = surgCodeStr.split(",");
    // 过滤掉空字符串
    surgCodeArr = Arrays.stream(surgCodeArr).filter(StringUtils::isNotBlank).toArray(String[]::new);

    if (surgCodeArr.length != 0) {
      return baSsjlService.selectIcd09NamesByIcd09Codes(surgCodeArr);
    }
    return new String[0];
  }

  /**
   * 判断是否包含指定的条件
   */
  public boolean isContainsConfigCondition(String conditionStr, String[] icd10Names, String[] icd09Names) {
    Optional<String[]> conditionPairsOpt = Optional.ofNullable(conditionStr).map(str -> str.split(":"));
    if (!conditionPairsOpt.isPresent() || conditionPairsOpt.get().length != 2) {
      return false;
    }

    String conditionKey = conditionPairsOpt.get()[0];
    String conditionValue = conditionPairsOpt.get()[1];

    switch (conditionKey) {
      case "icd10":
        return Arrays.stream(icd10Names).anyMatch(diagName -> diagName.contains(conditionValue));
      case "icd09":
        return Arrays.stream(icd09Names).anyMatch(surgName -> surgName.contains(conditionValue));
      default:
        return false;
    }
  }

  public String checkTogetherIcdArg20(List<DrgAdrgFz> adrgFzList, String otherDiag, String mainDiag, String allOper) {
    List<String> otherDiagList = StringUtils.isNotBlank(otherDiag) ? Arrays.asList(otherDiag.split(",")) : Collections.emptyList();
    List<String> allOperList = StringUtils.isNotBlank(allOper) ? Arrays.asList(allOper.split(",")) : Collections.emptyList();

    Iterator<DrgAdrgFz> iterator = adrgFzList.iterator();
    while (iterator.hasNext()) {

      DrgAdrgFz drgAdrgFz = iterator.next();
      String adrgbh = drgAdrgFz.getAdrgbh();

      if (togetherIcdMap.containsKey(drgAdrgFz.getAdrgbh())) {

        String[] togetherIcds = togetherIcdMap.get(adrgbh);
        List<DrgAdrgFz> list = getDrgAdrgFzByAdrgbh(adrgbh);
        for (String icds : togetherIcds) {
          String[] icdArr = icds.split("&");
          boolean flag = true;
          for (String icd : icdArr) {
            if (icd.startsWith("主要诊断")) {
              flag = list.stream().anyMatch(item -> icd.equals(item.getAndflag()) && mainDiag.equals(item.getIcdbh()));
            } else if (icd.startsWith("其他诊断")) {
              flag = list.stream().anyMatch(item -> icd.equals(item.getAndflag()) && otherDiagList.contains(item.getIcdbh()));
            } else if (icd.startsWith("主要手术")) {
              if (allOperList.isEmpty())  {
                flag = false;
              } else {
                String mainOper = allOperList.get(0);
                flag = list.stream().anyMatch(item -> icd.equals(item.getAndflag()) && mainOper.equals(item.getIcdbh()));
              }
            } else if (icd.startsWith("手术")) {
              flag = list.stream().anyMatch(item -> icd.equals(item.getAndflag()) && allOperList.contains(item.getIcdbh()));
            }
            if (!flag) break;
          }
          if (flag) return adrgbh;
        }

        iterator.remove();
      }
    }
    return null;
  }

  /**
   * 检查需要同时包含多个手术表手术的adrg
   *
   * @param ids_get_adrg
   * @return
   */
  public String checkTogetherSurgAdrg(List<DrgAdrgFz> ids_get_adrg, String ls_jbdm1_15, String ls_jbdm, String ls_ssbm1_7) {
    //提取ids_get_adrg中符合条件的adrgbh，并在ids_get_adrg中删除
    Set<String> togetherAdrgSet = new HashSet<>();
    Iterator<DrgAdrgFz> iterator = ids_get_adrg.iterator();
    while (iterator.hasNext()) {
      DrgAdrgFz drgAdrgFz = iterator.next();
      if (TOGETHER_OPER_ADRG.contains(drgAdrgFz.getAdrgbh())) {
        togetherAdrgSet.add(drgAdrgFz.getAdrgbh());
        iterator.remove();
      }
    }

    for (String adrgbh : togetherAdrgSet) {
      //adrg对应的所有诊断和手术
      List<DrgAdrgFz> drgAdrgFzList = getDrgAdrgFzByAdrgbh(adrgbh);
      drgAdrgFzList.removeIf(Objects::isNull);
      //adrg对应的所有诊断
      List<String> adrgDiagCodeList = drgAdrgFzList.stream().filter(obj -> "1".equals(obj.getIcdtype())).map(DrgAdrgFz::getIcdbh).collect(Collectors.toList());
      //当前病人的诊断
      List<String> patientDiagCodeList = getCodeList(ls_jbdm + ("".equals(ls_jbdm1_15) ? "" : "," + ls_jbdm1_15));
      //如果当前病人的诊断中没有adrg对应的诊断，就判断下一个adrg
      if (!hasCommonRecord(adrgDiagCodeList, patientDiagCodeList) && adrgDiagCodeList.size() > 0) {
        continue;
      }


      //adrg对应的所有手术
      List<DrgAdrgFz> surgList = drgAdrgFzList.stream().filter(obj -> "2".equals(obj.getIcdtype()) && obj.getAndflag() != null && !obj.getAndflag().isEmpty()).collect(Collectors.toList());
      //当前病人的手术编码List
      List<String> patientSurgCodeList = getCodeList(ls_ssbm1_7);
      List<String> adrgSurgCodeList;
      //根据Andflag对手术进行分组
      Map<String, List<DrgAdrgFz>> groupedByAndflagMap = surgList.stream()
        .collect(Collectors.groupingBy(DrgAdrgFz::getAndflag));
      //获取当前adrgbh对应手术表的索引  多种情况满足一种即可
      String[] adrgSurgTableIndexArr = togetherIcdMap.get(adrgbh);

      if (adrgSurgTableIndexArr == null || adrgSurgTableIndexArr.length == 0) {
        continue;
      }

      for (int i = 0; i < adrgSurgTableIndexArr.length; i++) {    //adrgSurgTableIndexArr格式：['1&2','1&3&4']
        String[] adrgSurgTableIndexs = adrgSurgTableIndexArr[i].split("&"); //adrgSurgTableIndexs格式：['1','2']或['1','3','4']
        int surgTableNum = adrgSurgTableIndexs.length;  //手术表数量
        int containsNum = 0;                            //当前病人手术包含的手术表数量
        for (int j = 0; j < adrgSurgTableIndexs.length; j++) {
          if (groupedByAndflagMap.containsKey(adrgSurgTableIndexs[j])) {
            List<DrgAdrgFz> drgAdrgFzs = groupedByAndflagMap.get(adrgSurgTableIndexs[j]);
            adrgSurgCodeList = drgAdrgFzs.stream().map(DrgAdrgFz::getIcdbh).collect(Collectors.toList());  //手术表1的手术/手术表2的手术......
            if (hasCommonRecord(adrgSurgCodeList, patientSurgCodeList)) {   //判断病人的手术是否有这个手术表里面的手术
              containsNum++;
            }
          } else {
            logger.error(adrgbh + "在andflag配置的手术表索引与在option配置的索引不一致");
            break;
          }
        }
        if (surgTableNum == containsNum) {
          return adrgbh;
        }
      }

    }

    return null;
  }


  /**
   * 获取编码列表
   */
  public List<String> getCodeList(String codeStr) {
    return Arrays.asList(codeStr.split(","));
  }

  /**
   * 检查两个List是否有相同的记录
   */
  public boolean hasCommonRecord(List<String> list1, List<String> list2) {
    Set<String> set = new HashSet<>(list1);
    return list2.stream().anyMatch(set::contains);
  }


  public void setTogetherIcdMap() {
    if (is_drgfz_ver.equals("1.1")) {
      togetherIcdMap = new HashMap<>();
      String[] split;
      split = ADRG_SURGTABLE_INDEX11.split(",");
      for (String s : split) {
        String[] keyValue = s.split("=", 2);
        if (keyValue.length >= 2) {
          togetherIcdMap.put(keyValue[0], keyValue[1].split("\\|"));
        }
      }
    } else {
      Map<String,String[]> togetherIcd = new HashMap<>();
//      togetherIcd.put("AH2","其他诊断&手术2|手术1&手术2".split("\\|"));
      togetherIcd.put("IB1","主要诊断&主要手术1|主要手术2|手术3&手术4".split("\\|"));
      togetherIcd.put("JA1","主要诊断&手术1&手术2|主要诊断&手术1&手术3&手术4|主要诊断&手术4&手术5".split("\\|"));
      togetherIcd.put("JA2","主要诊断&主要手术1|主要诊断&手术2&手术3".split("\\|"));
      togetherIcd.put("NA1","主要诊断&主要手术1|主要诊断&手术2&手术3".split("\\|"));
      togetherIcd.put("OF1","主要诊断1&主要手术|主要诊断2&其他诊断&主要手术".split("\\|"));
      togetherIcd.put("WB1","主要诊断&其他诊断1&主要手术|其他诊断2&主要手术".split("\\|"));
      togetherIcd.put("WB2","主要诊断&其他诊断1&主要手术|其他诊断2&主要手术".split("\\|"));
      togetherIcd.put("WB3","主要诊断&主要手术|其他诊断&主要手术".split("\\|"));
      togetherIcd.put("CB2","手术1&手术2".split("\\|"));
      togetherIcd.put("CB3","手术1&手术2".split("\\|"));
      togetherIcdMap = togetherIcd;
    }
  }


  /**
   * 判断联合手术
   */
  public String checkJointSurg(String adrgbh, String ls_ssbm1_7) {
//    if ("".equals(ls_ssbm1_7) || ls_ssbm1_7 == null) return null;
//    String[] surgCodeArr = ls_ssbm1_7.split(",");
//    if (surgCodeArr.length != 2) return null;
//    List<Drgdict> drgdicts = getDrgDictByAdrgbh(adrgbh);
//    drgdicts.removeIf(Objects::isNull);
//    for (int i = 0; i < drgdicts.size(); i++) {
//      if (drgdicts.get(i).getDrgmc().contains("联合手术")) {
//        return drgdicts.get(i).getDrgbh();
//      }
//    }
    return null;
  }


  /**
   * 根据住院天数查找符合条件的分组
   */
  public String findDrgbhBySjzyts(String adrgbh, String condition) {
    List<Drgdict> drgdictList = getDrgDictByAdrgbhAndZyts(adrgbh, condition);
    if (drgdictList.size() > 0) {
      return drgdictList.get(0).getDrgbh();
    }
    return null;
  }


  private List<DrgAdrgFz> getDrgAdrgFzHiv(String[] zdarr) {
    List<String> icdbhList = Arrays.asList(zdarr);
    List<String> adrgbhList = Arrays.asList("RB2","RB1");
    List<DrgAdrgFz> dataList = ids_drg_adrg_fz.stream()
      .filter(item -> ("MDCY".equals(item.getMdcbh()) || adrgbhList.contains(item.getAdrgbh())))
      .filter(item -> icdbhList.contains(item.getIcdbh()))
      .map(DrgAdrgFz::new)
      .collect(Collectors.toList());
    return dataList;
  }

  private long countDrgAdrgFz(String icdbh,String icdtype,String adrgbh) {
    return ids_drg_adrg_fz.stream()
      .filter(item -> icdbh.equals(item.getIcdbh()))
      .filter(item -> icdtype.equals(item.getIcdtype()))
      .filter(item -> adrgbh.equals(item.getAdrgbh()))
      .count();
  }

  private long countDrgAdrgFz(String icdbh) {
    return ids_drg_adrg_fz.stream()
      .filter(item -> icdbh.equals(item.getIcdbh()))
      .count();
  }

  private long countSsbw(String[] zdarr) {
    List<String> icdbhList = Arrays.asList(zdarr);
    return ids_drg_adrg_fz.stream()
      .filter(item -> "MDCZ".equals(item.getAdrgbh()))
      .filter(item -> icdbhList.contains(item.getIcdbh()))
      .map(DrgAdrgFz::getSsbw)
      .distinct()
      .count();
  }


  private List<DrgAdrgFz> getTogetherDrgAdrgFz(String[] zdarr,String adrgbh) {
    List<String> icdbhList = Arrays.asList(zdarr);
    List<DrgAdrgFz> dataList = ids_drg_adrg_fz.stream()
      .filter(item -> ("同时包含和".equals(item.getAndflag()) || "同时包含".equals(item.getAndflag())))
      .filter(item -> adrgbh.equals(item.getAdrgbh()))
      .filter(item -> icdbhList.contains(item.getIcdbh()))
      .map(DrgAdrgFz::new)
      .collect(Collectors.toList());
    return dataList;
  }


  private List<DrgAdrgFz> getDrgAdrgFzByIcd(String icd10,String icd9) {
    List<DrgAdrgFz> dataList = ids_drg_adrg_fz.stream()
      .filter(item -> (StringUtils.isNotBlank(icd10) && icd10.equals(item.getIcdbh())) || (StringUtils.isNotBlank(icd9) && icd9.equals(item.getIcdbh())))
      .map(DrgAdrgFz::new)
      .sorted(Comparator.comparing(DrgAdrgFz::getIcdbh))
      .collect(Collectors.toList());
    return dataList;
  }

  private List<DrgAdrgFz> getDrgAdrgFzByAdrgbh(String adrgbh) {
    List<DrgAdrgFz> dataList = ids_drg_adrg_fz.stream()
      .filter(item -> adrgbh.equals(item.getAdrgbh()))
      .map(DrgAdrgFz::new)
      .collect(Collectors.toList());
    return dataList;
  }


  public BigDecimal getZfbz(String orgLevel,Drgdict drgdict) {
    BigDecimal zfbz = null;
    if (orgLevel != null) {
      if ("1".equals(orgLevel)) {
        zfbz = drgdict.getZfbz();
      } else if ("2".equals(orgLevel)) {
        zfbz = drgdict.getZfbz2();
      } else if ("3".equals(orgLevel)) {
        zfbz = drgdict.getZfbz3();
      }
    }
    return zfbz;
  }

  public BigDecimal getZfqz(String orgLevel,Drgdict drgdict) {
    BigDecimal zfqz = null;
    if (orgLevel != null) {
      if ("1".equals(orgLevel)) {
        zfqz = drgdict.getZfqz3();
      } else if("2".equals(orgLevel)) {
        zfqz = drgdict.getZfqz();
      } else if("3".equals(orgLevel)) {
        zfqz = drgdict.getZfqz();
      }
    }
    return zfqz;
  }


  public Drgdict getDrgDictByDrgbh(String drgbh) {
    return StringUtils.isBlank(drgbh) ? null : ids_drg_dict.stream()
      .filter(drgDict -> drgbh.equals(drgDict.getDrgbh()))
      .findFirst()
      .orElse(null);
  }


  public List<Drgdict> getDrgDictByAdrgbh(String adrgbh) {
    return StringUtils.isBlank(adrgbh) ? new ArrayList<>() : ids_drg_dict.stream()
      .filter(drgDict -> adrgbh.equals(drgDict.getAdrgbh()))
      .collect(Collectors.toList());
  }


  public List<Drgdict> getDrgDictByAdrgbhAndZyts(String adrgbh,String condition) {
    return StringUtils.isBlank(adrgbh) ? new ArrayList<>() : ids_drg_dict.stream()
      .filter(drgDict -> adrgbh.equals(drgDict.getAdrgbh()) && drgDict.getDrgmc().contains(condition))
      .collect(Collectors.toList());
  }


}

