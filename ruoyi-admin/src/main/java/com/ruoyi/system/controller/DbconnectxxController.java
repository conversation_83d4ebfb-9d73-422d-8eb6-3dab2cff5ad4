package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Dbconnectxx;
import com.ruoyi.system.service.IDbconnectxxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据库联接信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-02
 */
@RestController
@RequestMapping("/system/dbconnectxx")
public class DbconnectxxController extends BaseController
{
    @Autowired
    private IDbconnectxxService dbconnectxxService;

    /**
     * 查询数据库联接信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:dbconnectxx:list')")
    @GetMapping("/list")
    public TableDataInfo list(Dbconnectxx dbconnectxx)
    {
        startPage();
        List<Dbconnectxx> list = dbconnectxxService.selectDbconnectxxList(dbconnectxx);
        return getDataTable(list);
    }

    /**
     * 导出数据库联接信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:dbconnectxx:export')")
    @Log(title = "数据库联接信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Dbconnectxx dbconnectxx)
    {
        List<Dbconnectxx> list = dbconnectxxService.selectDbconnectxxList(dbconnectxx);
        ExcelUtil<Dbconnectxx> util = new ExcelUtil<Dbconnectxx>(Dbconnectxx.class);
        util.exportExcel(response, list, "数据库联接信息数据");
    }

    /**
     * 获取数据库联接信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dbconnectxx:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dbconnectxxService.selectDbconnectxxById(id));
    }

    /**
     * 新增数据库联接信息
     */
    @PreAuthorize("@ss.hasPermi('system:dbconnectxx:add')")
    @Log(title = "数据库联接信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Dbconnectxx dbconnectxx)
    {
        return toAjax(dbconnectxxService.insertDbconnectxx(dbconnectxx));
    }

    /**
     * 修改数据库联接信息
     */
    @PreAuthorize("@ss.hasPermi('system:dbconnectxx:edit')")
    @Log(title = "数据库联接信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Dbconnectxx dbconnectxx)
    {
        return toAjax(dbconnectxxService.updateDbconnectxx(dbconnectxx));
    }

    /**
     * 删除数据库联接信息
     */
    @PreAuthorize("@ss.hasPermi('system:dbconnectxx:remove')")
    @Log(title = "数据库联接信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbconnectxxService.deleteDbconnectxxByIds(ids));
    }
}
