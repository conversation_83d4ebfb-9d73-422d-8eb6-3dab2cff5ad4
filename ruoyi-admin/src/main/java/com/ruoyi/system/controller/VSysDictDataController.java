package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.VSysDictData;
import com.ruoyi.system.service.IVSysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 字典目录Controller
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@RestController
@Anonymous
@RequestMapping("/mlcx/data")
public class VSysDictDataController extends BaseController
{
    @Autowired
    private IVSysDictDataService vSysDictDataService;

    /**
     * 查询字典目录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(VSysDictData vSysDictData)
    {
        startPage();
        List<VSysDictData> list = vSysDictDataService.selectVSysDictDataList(vSysDictData);
        return getDataTable(list);
    }

    /**
     * 导出字典目录列表
     */
    @Log(title = "字典目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VSysDictData vSysDictData)
    {
        List<VSysDictData> list = vSysDictDataService.selectVSysDictDataList(vSysDictData);
        ExcelUtil<VSysDictData> util = new ExcelUtil<VSysDictData>(VSysDictData.class);
        util.exportExcel(response, list, "字典目录数据");
    }

    /**
     * 获取字典目录详细信息
     */
    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable("dictCode") Long dictCode)
    {
        return success(vSysDictDataService.selectVSysDictDataByDictCode(dictCode));
    }


}
