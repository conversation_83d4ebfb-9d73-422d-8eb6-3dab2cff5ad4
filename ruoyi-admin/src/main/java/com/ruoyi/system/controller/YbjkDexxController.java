package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbjkDexx;
import com.ruoyi.system.service.IYbjkDexxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 定额信息Controller
 * 
 * <AUTHOR>
 * @date 2024-09-19
 */
@RestController
@RequestMapping("/system/dexx")
public class YbjkDexxController extends BaseController
{
    @Autowired
    private IYbjkDexxService ybjkDexxService;

    /**
     * 查询定额信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:dexx:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbjkDexx ybjkDexx)
    {
        startPage();
        List<YbjkDexx> list = ybjkDexxService.selectYbjkDexxList(ybjkDexx);
        return getDataTable(list);
    }

    /**
     * 导出定额信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:dexx:export')")
    @Log(title = "定额信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbjkDexx ybjkDexx)
    {
        List<YbjkDexx> list = ybjkDexxService.selectYbjkDexxList(ybjkDexx);
        ExcelUtil<YbjkDexx> util = new ExcelUtil<YbjkDexx>(YbjkDexx.class);
        util.exportExcel(response, list, "定额信息数据");
    }

    /**
     * 获取定额信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dexx:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ybjkDexxService.selectYbjkDexxById(id));
    }

    /**
     * 新增定额信息
     */
    @PreAuthorize("@ss.hasPermi('system:dexx:add')")
    @Log(title = "定额信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbjkDexx ybjkDexx)
    {
        return toAjax(ybjkDexxService.insertYbjkDexx(ybjkDexx));
    }

    /**
     * 修改定额信息
     */
    @PreAuthorize("@ss.hasPermi('system:dexx:edit')")
    @Log(title = "定额信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbjkDexx ybjkDexx)
    {
        return toAjax(ybjkDexxService.updateYbjkDexx(ybjkDexx));
    }

    /**
     * 删除定额信息
     */
    @PreAuthorize("@ss.hasPermi('system:dexx:remove')")
    @Log(title = "定额信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ybjkDexxService.deleteYbjkDexxByIds(ids));
    }
}
