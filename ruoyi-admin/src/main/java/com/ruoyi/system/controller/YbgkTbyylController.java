package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbgkTbyyl;
import com.ruoyi.system.service.IYbgkTbyylService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 特病用药情况Controller
 * 
 * <AUTHOR>
 * @date 2025-05-09
 */
@RestController
@RequestMapping("/system/tbyyl")
public class YbgkTbyylController extends BaseController
{
    @Autowired
    private IYbgkTbyylService ybgkTbyylService;

    /**
     * 查询特病用药情况列表
     */
    @PreAuthorize("@ss.hasPermi('system:tbyyl:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbgkTbyyl ybgkTbyyl)
    {
        startPage();
        List<YbgkTbyyl> list = ybgkTbyylService.selectYbgkTbyylList(ybgkTbyyl);
        return getDataTable(list);
    }

    /**
     * 导出特病用药情况列表
     */
    @PreAuthorize("@ss.hasPermi('system:tbyyl:export')")
    @Log(title = "特病用药情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbgkTbyyl ybgkTbyyl)
    {
        List<YbgkTbyyl> list = ybgkTbyylService.selectYbgkTbyylList(ybgkTbyyl);
        ExcelUtil<YbgkTbyyl> util = new ExcelUtil<YbgkTbyyl>(YbgkTbyyl.class);
        util.exportExcel(response, list, "特病用药情况数据");
    }

    /**
     * 获取特病用药情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tbyyl:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ybgkTbyylService.selectYbgkTbyylById(id));
    }

    /**
     * 新增特病用药情况
     */
    @PreAuthorize("@ss.hasPermi('system:tbyyl:add')")
    @Log(title = "特病用药情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbgkTbyyl ybgkTbyyl)
    {
        return toAjax(ybgkTbyylService.insertYbgkTbyyl(ybgkTbyyl));
    }

    /**
     * 修改特病用药情况
     */
    @PreAuthorize("@ss.hasPermi('system:tbyyl:edit')")
    @Log(title = "特病用药情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbgkTbyyl ybgkTbyyl)
    {
        return toAjax(ybgkTbyylService.updateYbgkTbyyl(ybgkTbyyl));
    }

    /**
     * 删除特病用药情况
     */
    @PreAuthorize("@ss.hasPermi('system:tbyyl:remove')")
    @Log(title = "特病用药情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ybgkTbyylService.deleteYbgkTbyylByIds(ids));
    }
}
