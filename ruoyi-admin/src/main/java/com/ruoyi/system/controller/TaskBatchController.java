package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.vo.CheckBatchVo;
import com.ruoyi.system.manager.CheckPatBatchManager;
import com.ruoyi.system.service.impl.AsyncTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/gksz/task")
public class TaskBatchController extends BaseController {

    private final CheckPatBatchManager checkPatBatchManager;

    @Autowired
    private AsyncTaskService asyncTaskService;

    public TaskBatchController(CheckPatBatchManager checkPatBatchManager) {
        this.checkPatBatchManager = checkPatBatchManager;
    }

    @PostMapping("/checkPatBath")
    public AjaxResult checkPatBatch(@RequestBody CheckBatchVo batchVo) {
        String username = getLoginUser().getUsername();

        // 获取执行准许
        if (!checkPatBatchManager.tryExecution(username)) {
            return AjaxResult.warn("操作正在执行中，请稍后再试");
        }
        asyncTaskService.executeBatchCheck(batchVo);

        return success("审核执行开始");
    }

    @GetMapping("/checkBatchStatus")
    public AjaxResult getCheckBatchStatus() {
        return success().put("status", checkPatBatchManager.getStatus());
    }
}
