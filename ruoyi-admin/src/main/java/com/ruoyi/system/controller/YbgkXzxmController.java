package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.YbgkXzxm;
import com.ruoyi.system.service.IYbgkXzxmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 限制信息Controller
 * 
 * <AUTHOR>
 * @date 2023-07-02
 */
@RestController
@RequestMapping("/gksz/xzxm")
public class YbgkXzxmController extends BaseController
{
    @Autowired
    private IYbgkXzxmService ybgkXzxmService;

    /**
     * 查询限制信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(YbgkXzxm ybgkXzxm)
    {
        startPage();
        List<YbgkXzxm> list = ybgkXzxmService.selectYbgkXzxmList(ybgkXzxm);
        return getDataTable(list);
    }

    /**
     * 导出限制信息列表
     */
    @Log(title = "限制信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbgkXzxm ybgkXzxm)
    {
        List<YbgkXzxm> list = ybgkXzxmService.selectYbgkXzxmList(ybgkXzxm);
        ExcelUtil<YbgkXzxm> util = new ExcelUtil<YbgkXzxm>(YbgkXzxm.class);
        util.exportExcel(response, list, "限制信息数据");
    }

    /**
     * 获取限制信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ybgkXzxmService.selectYbgkXzxmById(id));
    }

    /**
     * 新增限制信息
     */
    @Log(title = "限制信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbgkXzxm ybgkXzxm)
    {
        return toAjax(ybgkXzxmService.insertYbgkXzxm(ybgkXzxm));
    }

    /**
     * 修改限制信息
     */
    @Log(title = "限制信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbgkXzxm ybgkXzxm)
    {
        return toAjax(ybgkXzxmService.updateYbgkXzxm(ybgkXzxm));
    }

    /**
     * 删除限制信息
     */
    @Log(title = "限制信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ybgkXzxmService.deleteYbgkXzxmByIds(ids));
    }
}
