package com.ruoyi.system.controller;
import java.util.*;
import java.util.stream.Collectors;

public class DrgFzqzyGroupMatcher {
	// 分组规则定义
    private static final List<GroupRule> RULES = new ArrayList<>();

    static {
        // 1. IR1Z 骨盆骨折（简单分组）
        RULES.add(new GroupRule("IR1Z",
            Arrays.asList("s32.400", "s32.801", "s32.300", "s32.500x002", "s32.500x003", "s32.510"),
            Collections.singletonList(Arrays.asList("93.2100", "93.4403", "93.4402")),
            10, RuleType.SIMPLE));

        // 2. IR2Z 股骨骨折（简单分组）
        RULES.add(new GroupRule("IR2Z",
            Arrays.asList("s72.100x001", "s72.100x002", "s72.101", "s72.200x001"),
            Collections.singletonList(Collections.singletonList("17.972H0")),
            20, RuleType.SIMPLE));

        // 3. IS1Z 前臂腕手足损伤（简单分组）
        RULES.add(new GroupRule("IS1Z",
            Arrays.asList(
            		"s52.500x001", "s52.500x002", "s52.500x003", "s52.500x011", "s52.500x021",
                    "s52.500x022", "s52.500x001", "s52.500x091", "s52.501", "s52.502", "s52.600x002", "s52.600x001",
                    "s52.510", "s52.400x001", "s52.300", "s52.200", "s52.200x011", "s52.300x011",
                    "s52.101", "s52.701", "s52.410", "s52.711", "s62.101", "s62.200", "s62.200x021",
                    "s62.301", "s62.500", "s62.801", "s62.802", "s62.811", "s92.000", "s92.200x001",
                    "s92.300", "s92.310", "s92.300x003", "s92.500x001", "s92.400", "s92.410"
            ),
            Collections.singletonList(Arrays.asList(
                "17.972E0", "17.972A0", "17.972B0", "17.972C0", "17.972D0",
                "17.97290", "17.972G0", "17.972R0", "17.971A0", "17.97200", "17.972F0"
            )),
            10, RuleType.SIMPLE));

        // 4. IS2R 肱骨干骨折（简单分组）
        RULES.add(new GroupRule("IS2R",
            Arrays.asList(
            		"s42.300", "s42.300x002", "s42.301", "s42.310", "s42.311", "s42.200x001",
                    "s42.200x031", "s42.210", "s42.200x091", "s42.200x092", "s42.202", "s42.203", "s42.200x041"
            ),
            Collections.singletonList(Arrays.asList("17.97240", "17.97220", "17.97230")),
            12, RuleType.SIMPLE));

        // 5. IS2Z 其他损伤（简单分组）
        RULES.add(new GroupRule("IS2Z",
            Arrays.asList(
            		 "s22.300", "s22.400", "s42.400x021", "s42.400x031", "s22.400x041", "s22.400x011",
                     "s22.300x011", "s42.000", "s42.000x011", "s42.000x021", "s42.000x031", "s42.000x091",
                     "s42.010", "s42.200x011", "s42.400x001", "s42.400x041", "s42.400x042", "s42.400x043",
                     "s42.400x051", "s42.400x091", "s42.400x092", "s42.400x093", "s42.401", "s42.402",
                     "s42.403", "s42.404", "s42.410", "s43.000", "s43.100", "s43.200", "s52.001", "s53.100",
                     "s73.000", "s73.000x003", "s73.000x011", "s73.000x021", "s73.001", "s82.000", "s83.000",
                     "m84.000x062", "s82.000x004", "s82.100x081", "s82.100x082", "s82.100x084", "s82.100x085",
                     "s82.100x086", "s82.100x087", "s82.100x088", "s82.100x089", "s82.101", "s82.102", "s82.110",
                     "s82.111", "s82.200x011", "s82.200x081", "s82.201", "s82.203", "s82.100x011", "s82.100x012",
                     "s82.400x001", "s82.400x002", "s82.400x011", "s82.400x012", "s82.400x013", "s82.400x014",
                     "s82.400x091", "s82.401", "s82.800x082", "s82.500", "s82.510", "s82.600", "s82.610", "s82.800x081",
                     "s82.803", "s82.802", "s82.812", "s82.801", "s82.811", "s92.100"
            ),
            Collections.singletonList(Arrays.asList(
                "17.97210", "17.97250", "17.97260", "17.97270", "17.972L0", "17.971C0",
                "17.972M0", "17.972NO", "17.972PO", "17.972Q0", "17.97280", "17.97140",
                "17.971B0", "17.97150", "93.2100", "17.972R0", "17.97120", "17.97130"
            )),
            12, RuleType.SIMPLE));

        // 6. IS2F 膝痹（复杂分组）
        List<List<String>> is2fCategories = Arrays.asList(
            // 操作1
            Arrays.asList("17.92690", "17.92300", "17.97430", "93.3907", "17.92500"),
            // 操作2
            Arrays.asList("17.96120", "17.96530", "17.96700", "17.91100", "17.912A0", "17.91220", "17.91240"),
            // 操作3
            Arrays.asList("17.91330", "17.91310", "17.91320", "17.91350", "17.91360", "17.91370"),
            // 操作4
            Arrays.asList(
                "17.95420", "17.91270", "17.95510", "93.3908", "17.95110", "17.95120", "17.95130",
                "17.95140", "17.95150", "99.2700", "17.95210", "17.95220", "17.95230", "17.95240",
                "17.93400", "17.94100", "17.94300"
            )
        );
        RULES.add(new GroupRule("IS2F",
            Arrays.asList("m22.200x001", "m22.200x001", "m22.201", "m22.400", "m23.308", "m23.307", "m23.811", "m23.806"),
            is2fCategories,
            10, RuleType.COMPLEX_IS2F));

        // 7. IU1F 骨痹中医治疗（复杂分组）
        List<List<String>> iu1fCategories = Arrays.asList(
            // 操作1
            Arrays.asList("17.92690", "17.92300", "17.97430", "93.3907", "17.92500"),
            // 操作2
            Arrays.asList("17.96120", "17.96530", "17.96700", "17.91100", "17.912A0", "17.91220", "17.91240"),
            // 操作3
            Arrays.asList("17.91330", "17.91310", "17.91320", "17.91350", "17.91360", "17.91370"),
            // 操作4
            Arrays.asList(
                "17.95420", "17.91270", "17.95510", "93.3908", "17.95110", "17.95120", "17.95130",
                "17.95140", "17.95150", "99.2700", "17.95210", "17.95220", "17.95230", "17.95240",
                "17.93400", "17.94100", "17.94300"
            )
        );
        RULES.add(new GroupRule("IU1F",
            Arrays.asList("m17.900","m17.900x003", "m17.900x004", "m17.900x002",   "m25.501", "m25.602"),
            iu1fCategories,
            10, RuleType.COMPLEX_IU1F));

        // 8. IU2F 项腰痹中医治疗（复杂分组）
        List<List<String>> iu2fCategories = Arrays.asList(
            // 操作1
            Arrays.asList(
                "93.3907", "17.92110", "17.92610", "17.92620", "17.92220",
                "17.92640", "17.92650", "17.92660", "17.92120", "17.92400", "17.92500"
            ),
            // 操作2
            Arrays.asList(
                "17.96110", "17.96210", "17.96500", "17.91100", "17.912A0",
                "17.91220", "17.91240", "17.96520"
            ),
            // 操作3
            Arrays.asList("17.91330", "17.91310", "17.91320", "17.91350", "17.91360", "17.91370"),
            // 操作4
            Arrays.asList(
                "17.95420", "17.91270", "17.95510", "93.3908", "17.95110", "17.95120",
                "17.95130", "17.95140", "17.95150", "99.2700", "17.93400", "17.94100",
                "17.94300", "17.95210", "17.95220", "17.95230", "17.95240"
            )
        );
        RULES.add(new GroupRule("IU2F",
            Arrays.asList(
            		"m47.201", "m47.802", "m50.201", "m50.202", "m50.101+g55.1*", "m50.100",
                    "m53.203", "m51.202", "m51.100x002+g55.1*", "m51.101+g55.1*", "m48.005", "m43.006", "m54.501"
            ),
            iu2fCategories,
            10, RuleType.COMPLEX_IU2F));

        // 9. IU2Z 胸腰椎骨折（简单分组）
        RULES.add(new GroupRule("IU2Z",
            Arrays.asList(
            		"s22.000x006", "s22.000x003", "s22.000x005", "s22.000x007", "s22.000x009",
                    "s22.000x011", "s22.000x021", "s22.000x031", "s22.000x041", "s22.000x051",
                    "s22.000x061", "s22.100", "s32.000x002", "s32.000x011", "s32.000x021",
                    "s32.000x031", "s32.000x041", "s32.000x051", "s32.100", "s32.200"
            ),
            Collections.singletonList(Arrays.asList("17.972S0", "93.2100")),
            10, RuleType.SIMPLE));

        // 10. IZ2F 肩痹中医治疗（复杂分组）
        List<List<String>> iz2fCategories = Arrays.asList(
            // 操作1
            Arrays.asList("93.3907", "17.97430", "17.92300", "17.92690", "17.92500"),
            // 操作2
            Arrays.asList("17.96120", "17.96530", "17.96700", "17.91100", "17.912A0", "17.91220", "17.91240"),
            // 操作3
            Arrays.asList("17.91330", "17.91350", "17.91310", "17.91320", "17.91360", "17.91370"),
            // 操作4
            Arrays.asList(
                "17.95420", "17.91270", "17.95510", "93.3908", "17.95110", "17.95120",
                "17.95130", "17.95140", "17.95150", "99.2700", "17.93400", "17.94100",
                "17.94300", "17.95210", "17.95220", "17.95230", "17.95240"
            )
        );
        RULES.add(new GroupRule("IZ2F",
            Arrays.asList("m75.400", "m75.001", "m75.000", "m75.302", "m75.300",
                    "m75.301", "m75.900", "m75.501", "m75.500"),
            iz2fCategories,
            12, RuleType.COMPLEX_IZ2F));
    }

    // 匹配入口方法
    public static List<String> matchGroup(String mainDiagnosis, List<String> procedures, int daysInHospital) {
        return RULES.stream()
            .filter(rule -> rule.matches(mainDiagnosis, procedures, daysInHospital))
            .map(GroupRule::getGroupCode)
            .collect(Collectors.toList());
    }

    // ==== 支持类定义 ====
    enum RuleType { SIMPLE, COMPLEX_IS2F, COMPLEX_IU1F,COMPLEX_IU2F, COMPLEX_IU2Z,COMPLEX_IZ2F}

    static class GroupRule {
        private final String groupCode;
        private final Set<String> diagnoses;
        private final List<Set<String>> procedureCategories; // 使用Set提高查找效率
        private final int minDays;
        private final RuleType ruleType;

        public GroupRule(String groupCode, List<String> diagnoses,
                        List<List<String>> procedureCategories,
                        int minDays, RuleType ruleType) {
            this.groupCode = groupCode;
            this.diagnoses = new HashSet<>(diagnoses);
            // 将每个手术类别转换为Set
            this.procedureCategories = procedureCategories.stream()
                .map(HashSet::new)
                .collect(Collectors.toList());
            this.minDays = minDays;
            this.ruleType = ruleType;
        }

        public boolean matches(String diagnosis, List<String> procedures, int days) {
            // 检查诊断和住院天数
            if (!diagnoses.contains(diagnosis.toLowerCase()) || days < minDays) {
                return false;
            }
//            COMPLEX_IS2F, COMPLEX_IU1F,COMPLEX_IU2F, COMPLEX_IU2Z,COMPLEX_IZ2F}
            if (ruleType == RuleType.COMPLEX_IS2F) {
            	// 复杂分组：检查整个手术列表，满足组合条件
            	Set<String> procedureSet = new HashSet<>(procedures);
                return satisfiesCondition(procedureSet, new int[]{0, 1, 2}) || // 操作1+2+3
                       satisfiesCondition(procedureSet, new int[]{0, 1, 3}) || // 操作1+2+4
                       satisfiesCondition(procedureSet, new int[]{1, 2, 3}) || // 操作2+3+4
                       satisfiesCondition(procedureSet, new int[]{0, 2, 3}) || // 操作1+3+4
                       satisfiesCondition(procedureSet, new int[]{0, 1, 2, 3}); // 全部
            }
            else if (ruleType == RuleType.COMPLEX_IU1F) {
            	Set<String> procedureSet = new HashSet<>(procedures);
                return satisfiesCondition(procedureSet, new int[]{0, 1, 2}) || // 操作1+2+3
                       satisfiesCondition(procedureSet, new int[]{0, 1, 3}) || // 操作1+2+4
                       satisfiesCondition(procedureSet, new int[]{1, 2, 3}) || // 操作2+3+4
                       satisfiesCondition(procedureSet, new int[]{0, 2, 3}) || // 操作1+3+4
                       satisfiesCondition(procedureSet, new int[]{0, 1, 2, 3}); // 全部
            }
            else if (ruleType == RuleType.COMPLEX_IU2F) {
            	Set<String> procedureSet = new HashSet<>(procedures);
                return satisfiesCondition(procedureSet, new int[]{0, 1, 2}) || // 操作1+2+3
                       satisfiesCondition(procedureSet, new int[]{0, 1, 3}) || // 操作1+2+4
                       satisfiesCondition(procedureSet, new int[]{1, 2, 3}) || // 操作2+3+4
                       satisfiesCondition(procedureSet, new int[]{0, 2, 3}) || // 操作1+3+4
                       satisfiesCondition(procedureSet, new int[]{0, 1, 2, 3}); // 全部
            }

            else if (ruleType == RuleType.COMPLEX_IZ2F) {
            	Set<String> procedureSet = new HashSet<>(procedures);
                return satisfiesCondition(procedureSet, new int[]{0, 1, 2}) || // 操作1+2+3
                       satisfiesCondition(procedureSet, new int[]{0, 1, 3}) || // 操作1+2+4
                       satisfiesCondition(procedureSet, new int[]{1, 2, 3}) || // 操作2+3+4
                       satisfiesCondition(procedureSet, new int[]{0, 2, 3}) || // 操作1+3+4
                       satisfiesCondition(procedureSet, new int[]{0, 1, 2, 3}); // 全部
            }
            else { // COMPLEX

            	 // 简单分组：只检查第一个手术（主手术/主操作）
                if (procedures.isEmpty()) {
                    return false;
                }
                String mainProcedure = procedures.get(0);
                return procedureCategories.get(0).contains(mainProcedure);
            }
        }

        // 检查是否满足指定手术类别的组合条件
        private boolean satisfiesCondition(Set<String> procedures, int[] categoryIndexes) {
            for (int index : categoryIndexes) {
                Set<String> category = procedureCategories.get(index);
                if (category.stream().noneMatch(procedures::contains)) {
                    return false; // 该类别没有匹配的手术
                }
            }
            return true; // 所有指定类别都有匹配的手术
        }

        public String getGroupCode() {
            return groupCode;
        }
    }

    // 示例用法
    public static void main(String[] args) {
        // 案例1：骨盆骨折（IR1Z） - 精确匹配成功
        String diagnosis1 = "S32.400";
        List<String> procedures1 = Arrays.asList("93.2100", "其他手术");
        int days1 = 12;

        // 案例2：股骨骨折（IR2Z） - 主手术匹配
        String diagnosis2 = "S72.101";
        List<String> procedures2 = Arrays.asList("17.972H0", "93.3907"); // 主手术在第一位
        int days2 = 21;

        // 案例3：股骨骨折（IR2Z） - 主手术不匹配（在第二位）
        String diagnosis3 = "S72.101";
        List<String> procedures3 = Arrays.asList("93.3907", "17.972H0"); // 主手术在第二位
        int days3 = 21;

        // 案例4：膝痹（IS2F）- 满足操作1+2+3
        String diagnosis4 = "M22.201";
        List<String> procedures4 = Arrays.asList("17.92690", "17.96120", "17.91330");
        int days4 = 15;

        // 案例5：肩痹（IZ2F）- 满足操作1+2+4
        String diagnosis5 = "M75.001";
        List<String> procedures5 = Arrays.asList("93.3907", "17.96120", "17.95420", "17.94100");
        int days5 = 14;

        // 案例6：胸腰椎骨折（IU2F） - 主手术匹配
        String diagnosis6 = "M50.101+G55.1*";
        List<String> procedures6 = Arrays.asList("99.9200x016","93.3900x001","93.3500x010","17.9500x019","99.9205","17.9500x005");
        int days6 = 11;



        // 案例7：错误代码，不应匹配
        String diagnosis7 = "S32.400";
        List<String> procedures7 = Arrays.asList("93.21001"); // 多了一个"1"
        int days7 = 12;

        System.out.println("案例1分组: " + matchGroup(diagnosis1, procedures1, days1)); // IR1Z
        System.out.println("案例2分组: " + matchGroup(diagnosis2, procedures2, days2)); // IR2Z
        System.out.println("案例3分组: " + matchGroup(diagnosis3, procedures3, days3)); // 空
        System.out.println("案例4分组: " + matchGroup(diagnosis4, procedures4, days4)); // IS2F
        System.out.println("案例5分组: " + matchGroup(diagnosis5, procedures5, days5)); // IZ2F
        System.out.println("案例6分组: " + matchGroup(diagnosis6, procedures6, days6)); // IU2Z
        System.out.println("案例7分组: " + matchGroup(diagnosis7, procedures7, days7)); // 空
    }
}
