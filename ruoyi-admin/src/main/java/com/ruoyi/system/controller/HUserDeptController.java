package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

import com.ruoyi.system.domain.vo.AssignDeptsVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HUserDept;
import com.ruoyi.system.service.IHUserDeptService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户科室关联Controller
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@RestController
@RequestMapping("/system/hDept")
public class HUserDeptController extends BaseController
{
    @Autowired
    private IHUserDeptService hUserDeptService;

    /**
     * 查询用户科室关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list")
    public TableDataInfo list(HUserDept hUserDept)
    {
        startPage();
        List<HUserDept> list = hUserDeptService.selectHUserDeptList(hUserDept);
        return getDataTable(list);
    }

    /**
     * 导出用户科室关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:dept:export')")
    @Log(title = "用户科室关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HUserDept hUserDept)
    {
        List<HUserDept> list = hUserDeptService.selectHUserDeptList(hUserDept);
        ExcelUtil<HUserDept> util = new ExcelUtil<HUserDept>(HUserDept.class);
        util.exportExcel(response, list, "用户科室关联数据");
    }

    /**
     * 获取用户科室关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{hDeptId}")
    public AjaxResult getInfo(@PathVariable("hDeptId") Long hDeptId)
    {
        return success(hUserDeptService.selectHUserDeptByHDeptId(hDeptId));
    }

    /**
     * 新增用户科室关联
     */
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "用户科室关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HUserDept hUserDept)
    {
        return toAjax(hUserDeptService.insertHUserDept(hUserDept));
    }

    /**
     * 修改用户科室关联
     */
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "用户科室关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HUserDept hUserDept)
    {
        return toAjax(hUserDeptService.updateHUserDept(hUserDept));
    }

    /**
     * 删除用户科室关联
     */
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "用户科室关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{hDeptIds}")
    public AjaxResult remove(@PathVariable Long[] hDeptIds)
    {
        return toAjax(hUserDeptService.deleteHUserDeptByHDeptIds(hDeptIds));
    }

    /**
     * 获取指定用户的部门列表
     *
     * @param userId 用户ID
     * @return 部门列表
     */
    @GetMapping("/getUserDepts/{userId}")
    public AjaxResult getUserDepts(@PathVariable("userId") Long userId)
    {
        // 创建查询对象
        HUserDept query = new HUserDept();
        query.sethUserId(userId);

        // 查询该用户关联的所有部门
        List<HUserDept> list = hUserDeptService.selectHUserDeptList(query);

        return AjaxResult.success(list);
    }

    @PostMapping("/assignDepts")
    @Log(title = "用户部门分配", businessType = BusinessType.UPDATE)
    public AjaxResult assignDepts(@RequestBody AssignDeptsVo vo) {
        if (vo.getUserId() == null) {
            return error("用户ID不能为空");
        }

        // 1. 删除用户原有的部门关联
        HUserDept deleteQuery = new HUserDept();
        deleteQuery.sethUserId(vo.getUserId());
        hUserDeptService.deleteHUserDeptByUserId(vo.getUserId());

        // 2. 如果有新的部门分配，则批量插入
        if (vo.getDeptIds() != null && !vo.getDeptIds().isEmpty()) {
            // 去重部门ID
            List<Long> uniqueDeptIds = vo.getDeptIds().stream()
                .distinct()
                .collect(Collectors.toList());

            // 批量构建用户部门关联对象
            List<HUserDept> userDepts = uniqueDeptIds.stream()
                .map(deptId -> {
                    HUserDept userDept = new HUserDept();
                    userDept.sethUserId(vo.getUserId());
                    userDept.sethDeptId(deptId);
                    return userDept;
                })
                .collect(Collectors.toList());

            // 批量插入新的关联关系
          for (HUserDept userDept : userDepts) {
            hUserDeptService.insertHUserDept(userDept);
          }
//            hUserDeptService.batchInsertHUserDept(userDepts);
        }

        return success();
    }
}
