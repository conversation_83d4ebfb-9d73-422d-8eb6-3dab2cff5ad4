package com.ruoyi.system.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import drg_group.chs_drg_11.GroupProxy;
import drg_group.chs_drg_11.GroupResult;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;

@RestController
@Anonymous
@RequestMapping("/drg/drgfz")
public class DrgFzController extends BaseController {
  @Autowired
  private IBaSyjlService baSyjlService;
  @Autowired
  private IDrgFzqService drgFzqService;
  @Autowired
  private IBaBrzdxxService baBrzdxxService;
  @Autowired
  private IBaBrzdxxSyService baBrzdxxSyService;
  @Autowired
  private IBaSsjlService baSsjlService;
  @Autowired
  private IBaSsjlSyService baSsjlSyService;
  @Autowired
  private IDrgFzService drgFzService;
  @Autowired
  private DrgFzqController drgFzqController;
  @Autowired
  private IBrxxService brxxService;
  @Autowired
  private BzmlfController bzmlfController;
  @Autowired
  private IBlfzmxService blfzmxService;
  @Autowired
  private IBrxxSyncService brxxSyncService;
  @Autowired
  private IZdxxService zdxxService;
  @Autowired
  private IFyxxService fyxxService;
  @Autowired
  private IYbjkOptionService ybjkOptionService;

  private String ybqdYyname = "";
  //是否启用医保清单保存 0不启用 1中联通用 2医保文件通用
  private String useYbqd = "";

  @Anonymous
  @RequestMapping("/fz")
  public TableDataInfo drgfz(@RequestBody Drgfz drgfz) throws IOException {


    System.out.println("0--------------------------------");
    System.out.println(drgfz.toString());

    List<Object> fzxx = null;

    if (fzxx == null) {
      fzxx = new ArrayList<>();
    } else {
      fzxx.clear();
    }

    String zzd_bm = drgfz.getZzdbm();
    String qtzd_bm_str = drgfz.getZdxx();
    String ss_bm_str = drgfz.getSsxx();

    System.out.println("0---------------------------主要诊断：" + zzd_bm);
    System.out.println("0---------------------------诊断信息：" + qtzd_bm_str);
    System.out.println("0---------------------------手术信息：" + ss_bm_str);



    BaSyjl baSyjl = baSyjlService.selectBaSyjlByBridAndZyid(new BaSyjl(drgfz.getBrid(), drgfz.getZyid()));
    baSyjl = tszdcl(baSyjl);

    if (drgfz.getBs() == 2) {
      return chs_drg_fz(drgfz, baSyjl, fzxx);
    } else if (drgfz.getBs() == 1) {
      return bd_drg_fz(drgfz, baSyjl, fzxx);
    } else {
      return pb_drg_fz(drgfz, baSyjl, fzxx);
    }
  }

  public TableDataInfo bd_drg_fz(Drgfz drgfz, BaSyjl baSyjl, List<Object> fzxx) throws IOException {

    String zzd_bm = drgfz.getZzdbm();
    String qtzd_bm_str = drgfz.getZdxx();
    String ss_bm_str = drgfz.getSsxx();

    drgfz.setAction("getdrgfz");
    drgfz.setOrg(baSyjl.getOrgcode());
    drgfz.setBrbs(baSyjl.getBrbs());
    drgfz.setBah(baSyjl.getBah());
    drgfz.setNl(baSyjl.getNl());
    drgfz.setBzyzsnl(baSyjl.getBzyzsnl());
    drgfz.setXsecstz(baSyjl.getXsecstz());
    drgfz.setSjzyts(baSyjl.getSjzyts());
    drgfz.setLyfs(baSyjl.getLyfs());
    drgfz.setZzdbm(zzd_bm);
    drgfz.setZdxx(qtzd_bm_str);
    drgfz.setSsxx(ss_bm_str);
    drgfz.setZfy(baSyjl.getZfy());
    drgfz.setTime("");
    drgfz.setNum(String.valueOf(Math.ceil(Math.random() * 10000)));
    drgfz.setYlfkfs(baSyjl.getYlfkfs());

    DrgFzResult fzResult = drgFzqController.fzq(drgfz);
    //在院病人获取病人总费用
    if ("1".equals(baSyjl.getZyzt()) && fzResult.getZfy() == null) {
      Fyxx fyxx = new Fyxx();
      fyxx.setBrid(baSyjl.getBrid());
      fyxx.setZyid(baSyjl.getZyid());
      BigDecimal totalCost = fyxxService.selectPatientTotalCost(fyxx);
      fzResult.setZfy(totalCost);
    }


    fzxx.add(fzResult);
    return getDataTable(fzxx);
  }

  public TableDataInfo pb_drg_fz(Drgfz drgfz, BaSyjl baSyjl, List<Object> fzxx) throws IOException {
    OkHttpClient okHttpClient = new OkHttpClient();

    String zzd_bm = drgfz.getZzdbm();
    String qtzd_bm_str = drgfz.getZdxx();
    String ss_bm_str = drgfz.getSsxx();

    String drgfzIp = baSyjlService.selectDrgFzIp();

    String fzurl = "http://" + drgfzIp + ":9000/?" +
      "action=getdrgfz" +
      "&orgcode=" + baSyjl.getOrgcode() +
      "&bah=" + baSyjl.getBah() +
      "&xb=" + baSyjl.getXb() +
      "&nl=" + baSyjl.getNl() +
      "&bzyzsnl=" + baSyjl.getBzyzsnl() +
      "&xserytz=" + baSyjl.getXsecstz() +
      "&sjzyts=" + baSyjl.getSjzyts() +
      "&lyfs=" + baSyjl.getLyfs() +
      "&jbdm=" + zzd_bm +
      "&jbdm1_15=" + qtzd_bm_str +
      "&ssbm1_7=" + ss_bm_str +
      "&zfy=" + baSyjl.getZfy() +
      "&time=" + "" +
      "&num=" + Math.ceil(Math.random() * 10000) +
      "&ran=\n";
    fzurl = fzurl.replace("null", "");
    System.out.println("--------------------分组传值1-----------------------");
    System.out.println(fzurl);
    System.out.println("-------------------------------------------------");

    Request request = new Request.Builder()
      .url(fzurl)
      .build();
    Response response = okHttpClient.newCall(request).execute();
    String result = response.body().string();
    System.out.println("-----------------------分组结果----------------------");
    System.out.println(result);
    System.out.println("-----------------------分组结果----------------------");

//    JSONObject jsonObject = JSON.parseObject(result);
//    DrgFzResult drgFzResult = new DrgFzResult();
//    drgFzResult.setDrgbh((String) jsonObject.get("drgbh"));
//    drgFzResult.setBah(baSyjl.getBah());
//    drgFzResult.setDrgmc((String) jsonObject.get("drgmc"));
//    drgFzqService.insertPbFzxx(drgFzResult);


    fzxx.add(result);
    return getDataTable(fzxx);
  }

  /**
   * 保存到清单
   */
  @RequestMapping("/save/list")
  public AjaxResult saveToList(String brbs) throws SQLException, IOException {

    String yyname = "";



    if ("".equals(useYbqd)) {
      YbjkOption op = ybjkOptionService.selectYbjkOptionByCCode("use_ybqd");
      if (op == null) {
        useYbqd = "0";
      } else {
        useYbqd = op.getcValue();
      }
    }

    if ("0".equals(useYbqd)) {
      if ("".equals(ybqdYyname)) {
        YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("company_name");
        if (option != null) {
          yyname = option.getcValue();
          if ("岳普湖县医院".equals(yyname)) {
            drgFzService.XjSaveToList(brbs);
            return success();
          } else {
            throw new ServiceException("当前医院未启用医保清单保存");
          }
        } else {
          throw new ServiceException("当前医院未启用医保清单保存");
        }
      }
    }

    BaSyjl syjl = baSyjlService.selectBaSyjlByBrbs(brbs);
    Drgfz drgfz = new Drgfz();

    List<Object> fzxx = new ArrayList<>();
    List<BaBrzdxxSy> zdList = baBrzdxxSyService.selectBaBrzdxxSyList(new BaBrzdxxSy(syjl.getBrid(), syjl.getZyid()));
    List<BaSsjlSy> ssList = baSsjlSyService.selectBaSsjlSyList(new BaSsjlSy(syjl.getBrid(), syjl.getZyid()));
    String zdxx = zdList.stream().filter(zd -> zd.getZdcx() != 1 && StringUtils.isNotBlank(zd.getJbbm()))
      .map(BaBrzdxxSy::getJbbm).collect(Collectors.joining(","));

    Optional<BaBrzdxxSy> zyzd = zdList.stream().filter(zd -> zd.getZdcx() == 1).findFirst();
    Optional<BaSsjlSy> zyss = ssList.stream().filter(ss -> ss.getSscx() == 1).findFirst();

    if (!zyzd.isPresent()) {
      throw new ServiceException("主诊断不能为空");
    }

    drgfz.setZzdbm(zyzd.get().getJbbm());
    drgfz.setSsxx(zyss.isPresent() ? zyss.get().getSsbm() : "");
    drgfz.setBrid(syjl.getBrid());
    drgfz.setZyid(syjl.getZyid());
    drgfz.setBs(1);
    drgfz.setYbflag("2");
    drgfz.setSource("yfz");
    drgfz.setZdxx(zdxx);

    TableDataInfo fzRes = bd_drg_fz(drgfz, syjl, fzxx);

    DrgFzResult result = (DrgFzResult) fzRes.getRows().get(0);

    BaSyjl syjlQuery = new BaSyjl(syjl.getBrbs());
    syjlQuery.setDrgbh(result.getDrgbh());
    syjlQuery.setDrgmc(result.getDrgmc());
    syjlQuery.setZfbz(result.getZfbz());
    baSyjlService.updateBaSyjlFz(syjlQuery);

    // 1中联 2通用 3中联API模式
    if ("1".equals(useYbqd)) {
      drgFzService.XcSaveToList(brbs);
    } else if ("2".equals(useYbqd)) {
      drgFzService.saveToList(brbs);
    } else if ("3".equals(useYbqd)) {
      drgFzService.ApiSaveToList(brbs);
    } else if ("4".equals(useYbqd)) {
      drgFzService.generalSaveToList(brbs);
    } else if ("5".equals(useYbqd)) {
      drgFzService.ZLBHSaveToList(brbs);
    } else if ("6".equals(useYbqd)) {
      drgFzService.ZLBSSaveToList(brbs);
    } else if ("7".equals(useYbqd)) {
      drgFzService.zkySaveToList(brbs);
    } else if ("8".equals(useYbqd)) {
      drgFzService.saveDiseOprnList(brbs);
    } else if ("10".equals(useYbqd)) {
      drgFzService.bltSaveToList(brbs);
    } else if ("13".equals(useYbqd)) {
      drgFzService.dbSaveToList(brbs);
    } else {
      throw new ServiceException("医保清单参数配置异常，请联系管理员");
    }

    return success();
  }

  @RequestMapping("/save/doctorList")
  public AjaxResult saveDoctorList(String brbs) {
    drgFzService.saveDoctorList(brbs);
    return success();
  }

  @RequestMapping("/save/list/cancel")
  public AjaxResult listCancel(String brbs) {

    if ("".equals(useYbqd)) {
      YbjkOption op = ybjkOptionService.selectYbjkOptionByCCode("use_ybqd");
      if (op == null) {
        useYbqd = "0";
      } else {
        useYbqd = op.getcValue();
      }
    }

    if("5".equals(useYbqd)) {
      //zlbh
      drgFzService.listSaveCancel(brbs);
    } else if ("6".equals(useYbqd)) {
      drgFzService.zlbsListSaveCancel(brbs);
    }else if ("13".equals(useYbqd)) {
      //高新区
      drgFzService.dbSaveCancel(brbs);
    }

    return success();
  }

  public TableDataInfo chs_drg_fz(Drgfz drgfz, BaSyjl baSyjl, List<Object> fzxx) throws IOException {

    String zzd_bm = drgfz.getZzdbm();
    String qtzd_bm_str = drgfz.getZdxx();
    String ss_bm_str = drgfz.getSsxx();

    GroupProxy groupProxy = new GroupProxy();

    String dept = "";

    String[] ss_bm_arr = ss_bm_str.split(",");
    String[] zd_bm_arr = qtzd_bm_str.split(",");

    String zd_bm_str = zzd_bm + '|';
    for (String zzbm : zd_bm_arr) {
      zd_bm_str = zd_bm_str + zzbm + '|';
    }
    zd_bm_str = zd_bm_str.substring(0, zd_bm_str.length() - 1);

    for (String ssbm : ss_bm_arr) {
      ss_bm_str = ss_bm_str + ssbm + '|';
    }
    if (!"".equals(ss_bm_str.trim())) {
      ss_bm_str = ss_bm_str.substring(0, ss_bm_str.length() - 1);
    }

    System.out.println("chs_drg_fz---------------------------分解诊断：" + zd_bm_str);
    System.out.println("chs_drg_fz---------------------------分解手术：" + ss_bm_str);

    String fzurl =
      baSyjl.getBah() + "," +
        baSyjl.getXb() + "," +
        baSyjl.getNl() + "," +
        baSyjl.getBzyzsnl() + "," +
        baSyjl.getXsecstz() + "," +
        dept + "," +
        baSyjl.getSjzyts() + "," +
        baSyjl.getLyfs() + "," +
        zd_bm_str + "," +
        ss_bm_str + ",";

    fzurl = fzurl.replace("null", "");

    System.out.println("--------------------分组传值2-----------------------");
    System.out.println(fzurl);
    System.out.println("-------------------------------------------------");


    GroupResult groupResult = groupProxy.group_record(fzurl);
    fzxx.add(groupResult);
    return getDataTable(fzxx);
  }

  @RequestMapping("/zxplfz2")
  public void blfzmxplFz2() {

    BaSyjl syjl = new BaSyjl();
//	  syjl.setBah("23080120");
    List<BaSyjl> baSyjlList = baSyjlService.selectBaSyjlListblfzxm(syjl);

    List<BaBrzdxx> baBrzdxxes = null;
    List<BaSsjl> baSsjls = null;
    for (int i = 0; i < baSyjlList.size(); i++) {
      baBrzdxxes = null;
      baSsjls = null;
      String qtzd_bm_str = "";
      String zzd_bm = "";
      String ss_bm_str = "";
      BaSyjl baSyjl = baSyjlList.get(i);
      zzd_bm = baSyjlList.get(i).getJbdm();
//      ss_bm_str = baSyjlList.get(i).getSsjczbm1();
      qtzd_bm_str = baSyjlList.get(i).getJbdm1() + "," + baSyjlList.get(i).getJbdm2() + "," + baSyjlList.get(i).getJbdm3() + "," + baSyjlList.get(i).getJbdm4() + "," + baSyjlList.get(i).getJbdm5() + "," + baSyjlList.get(i).getJbdm6() + "," + baSyjlList.get(i).getJbdm7() + "," + baSyjlList.get(i).getJbdm8() + "," + baSyjlList.get(i).getJbdm9() + "," + baSyjlList.get(i).getJbdm10();
      ss_bm_str = baSyjlList.get(i).getSsjczbm1() + "," + baSyjlList.get(i).getSsjczbm2() + "," + baSyjlList.get(i).getSsjczbm3() + "," + baSyjlList.get(i).getSsjczbm4() + "," + baSyjlList.get(i).getSsjczbm5() + "," + baSyjlList.get(i).getSsjczbm6() + "," + baSyjlList.get(i).getSsjczbm7();

//      baBrzdxxes = baBrzdxxService.selectBaBrzdxxList(new BaBrzdxx(baSyjlList.get(i).getBrid(), baSyjlList.get(i).getZyid()))
//      if (baBrzdxxes.size() > 0) {
//        zzd_bm = baBrzdxxes.get(0).getJbbm();
//        if (baBrzdxxes.size() > 1) {
//          for (int i1 = 1; i1 < baBrzdxxes.size(); i1++) {
//            qtzd_bm_str += baBrzdxxes.get(i1).getJbbm() + ",";
//          }
//          if (qtzd_bm_str.length() > 0) {
//            qtzd_bm_str = qtzd_bm_str.substring(0,qtzd_bm_str.length()-1);
//          }
//        }
//      } else {
//        continue;
//      }

//      baSsjls = baSsjlService.selectBaSsjlList(new BaSsjl(baSyjlList.get(i).getBrid(), baSyjlList.get(i).getZyid()));
//      if (baSsjls.size() > 0) {
//        for (int i1 = 0; i1 < baSsjls.size(); i1++) {
//          ss_bm_str += baSsjls.get(i1).getSsbm() + ",";
//        }
//      }
//      if (ss_bm_str.length() > 0) {
//        ss_bm_str = ss_bm_str.substring(0,ss_bm_str.length()-1);
//      }

      System.out.println("----------------------------------------------");
      System.out.println("zzd_bm:" + zzd_bm);
      System.out.println("qtzd_bm_str:" + qtzd_bm_str);
      System.out.println("ss_bm_str:" + ss_bm_str);
      System.out.println("----------------------------------------------");


      baSyjl = tszdcl(baSyjl);

      Drgfz drgfz = new Drgfz();
      drgfz.setAction("getdrgfz");
      drgfz.setOrg(baSyjl.getOrgcode());
      drgfz.setBah(baSyjl.getBah());
      drgfz.setNl(baSyjl.getNl());
      drgfz.setBzyzsnl(baSyjl.getBzyzsnl());
      drgfz.setXsecstz(baSyjl.getXsecstz());
      drgfz.setSjzyts(baSyjl.getSjzyts());
      drgfz.setLyfs(baSyjl.getLyfs());
      drgfz.setZzdbm(zzd_bm);
      drgfz.setZdxx(qtzd_bm_str);
      drgfz.setSsxx(ss_bm_str);
      drgfz.setZfy(baSyjl.getZfy());
      drgfz.setTime("");
      drgfz.setNum(String.valueOf(Math.ceil(Math.random() * 10000)));
      drgfz.setYlfkfs(baSyjl.getYlfkfs());

      DrgFzResult drgFzResult = drgFzqController.fzq(drgfz);
      drgFzResult.setCysj(baSyjlList.get(i).getCysj());
      drgFzqService.insertBdFzxx(drgFzResult);
    }

  }


  /**
   * 在院病人批量分组
   */
  @RequestMapping("/zybrPlfz")
  public void zybrPlfz(String ls_brbs) {
    BaSyjl baSyjl1 = new BaSyjl();
    baSyjl1.setZyzt("1");
    if (!"".equals(ls_brbs)) {
      baSyjl1.setBrbs(ls_brbs);
    }

//    baSyjl1.setBrbs("2603370_2");
    List<BaSyjl> baSyjlList = baSyjlService.selectBaSyjlList(baSyjl1);

    for (int i = 0; i < baSyjlList.size(); i++) {
      if (baSyjlList.get(i) == null) {
        continue;
      }
//    for (int i = 0; i < baSyjlList.size(); i++) {
      BaSyjl baSyjl = baSyjlList.get(i);
      String brid = baSyjl.getBrid();
      String zyid = baSyjl.getZyid();
      String jzh = baSyjl.getJzh();


      //①同步zdxx和ba_ssjl_tjzd
      try {
        brxxSyncService.zdxxSync(brid, zyid);
      } catch (Exception e) {
        logger.error("-------------------同步诊断失败，患者：" + jzh+e.getMessage());
      } finally {
      }

      String qtzd_bm_str = "";
      String zzd_bm = "";
      String ss_bm_str = "";


      //②检查出院诊断：类型为3
      Zdxx zdxx = new Zdxx();
      zdxx.setJzh(jzh);
      zdxx.setZdtype("3");
      List<Zdxx> zdxxList = zdxxService.selectZdxxList(zdxx);

      if (zdxxList.isEmpty()) {
        //③检查入院诊断： 类型为2
        zdxx.setZdtype("2");
        zdxxList = zdxxService.selectZdxxList(zdxx);
      }

      if (zdxxList.isEmpty()) {
        //④从blryzd生成
        List<Brxx> brxxList = brxxService.selectBrxxByJzh(jzh);

        if (brxxList.isEmpty() || brxxList.get(0) == null || isNullOrEmpty(brxxList.get(0).getBlryzd())) {
          continue;
        }

        String blryzd = brxxList.get(0).getBlryzd();
        try {
          blryzd = bzmlfController.getjbbmml("lczd", blryzd, "ryzd");
        } catch (Exception e) {
          logger.error("-------------------取诊断失败：" + jzh+e.getMessage());
        } finally {
        }


        String[] fjblryzd = blryzd.split("\\|\\|");

        if (fjblryzd.length > 0) {
          String[] fjbmArr = fjblryzd[0].split(",");
          if (fjbmArr.length > 0) {
            zzd_bm = fjbmArr[0];
            if (fjbmArr.length > 1) {
              qtzd_bm_str = String.join(",", Arrays.copyOfRange(fjbmArr, 1, fjbmArr.length));
            }
          }
        } else {
          continue;
        }
      }

      if (!zdxxList.isEmpty()) {
        zzd_bm = zdxxList.get(0).getZdcode();
        if (zdxxList.size() > 1) {
          StringBuilder qtzd_bm_sb = new StringBuilder();
          for (int ll_i = 1; ll_i < zdxxList.size(); ll_i++) {
            qtzd_bm_sb.append(zdxxList.get(ll_i).getZdcode()).append(",");
          }
          if (qtzd_bm_sb.length() > 0) {
            qtzd_bm_sb.setLength(qtzd_bm_sb.length() - 1);
          }
          qtzd_bm_str = qtzd_bm_sb.toString();
        }
      }

      //手术
      BaSsjl baSsjl = new BaSsjl();
      baSsjl.setJlly("3");
      baSsjl.setBrid(brid);
      baSsjl.setZyid(zyid);
      List<BaSsjl> baSsjls = baSsjlService.selectBaSsjlList(baSsjl);
      if (baSsjls.size() == 0) {   //同步手术记录
        try {
          brxxSyncService.ssjlSync(brid, zyid);
        } catch (Exception e) {
          logger.error("-------------------同步手术失败，患者：" + jzh);
        } finally {
          baSsjls = baSsjlService.selectBaSsjlList(baSsjl);
        }
      }

      if (baSsjls.size() > 0) {
        StringBuilder ss_bm_sb = new StringBuilder();
        for (int ll_i = 0; ll_i < baSsjls.size(); ll_i++) {
          ss_bm_sb.append(baSsjls.get(ll_i).getSsbm()).append(",");
        }
        if (ss_bm_sb.length() > 0) {
          ss_bm_sb.setLength(ss_bm_sb.length() - 1);
        }
        ss_bm_str = ss_bm_sb.toString();
      }


      System.out.println("-----------------------------------");
      System.out.println("zzd_bm：" + zzd_bm);
      System.out.println("qtzd_bm_str：" + qtzd_bm_str);
      System.out.println("ss_bm_str：" + ss_bm_str);
      System.out.println("-----------------------------------");


      baSyjl = tszdcl(baSyjl);

      Drgfz drgfz = new Drgfz();
      drgfz.setAction("getdrgfz");
      drgfz.setOrg(baSyjl.getOrgcode());
      drgfz.setBah(baSyjl.getBah());
      drgfz.setNl(baSyjl.getNl());
      drgfz.setBzyzsnl(baSyjl.getBzyzsnl());
      drgfz.setXsecstz(baSyjl.getXsecstz());
      drgfz.setSjzyts(baSyjl.getSjzyts());
      drgfz.setLyfs(baSyjl.getLyfs());
      drgfz.setZzdbm(zzd_bm);
      drgfz.setZdxx(qtzd_bm_str);
      drgfz.setSsxx(ss_bm_str);
      drgfz.setZfy(baSyjl.getZfy());
      drgfz.setTime("");
      drgfz.setNum(String.valueOf(Math.ceil(Math.random() * 10000)));
      drgfz.setYlfkfs(baSyjl.getYlfkfs());

      DrgFzResult drgFzResult = drgFzqController.fzq(drgfz);

      baSyjl.setDrgbh(drgFzResult.getDrgbh());
      baSyjl.setZfbz(drgFzResult.getZfbz());

      //获取病人总费用
      Fyxx fyxx = new Fyxx();
      fyxx.setBrid(brid);
      fyxx.setZyid(zyid);
      BigDecimal totalCost = fyxxService.selectPatientTotalCost(fyxx);
      System.out.println("------------------------------totalCost:" + totalCost);
      baSyjl.setZfy(totalCost);


      baSyjlService.updateBaSyjl(baSyjl);
    }

  }


  public DrgFzResult basyfz(BaSyjl baSyjl) {
    String otherDiagCodes = "";
    String mainDiagCode = "";
    String operCodes = "";

    mainDiagCode = baSyjl.getJbdm();

    if (mainDiagCode != null && !"".equals(mainDiagCode)) {

      otherDiagCodes = baSyjl.getJbdm1() + "," + baSyjl.getJbdm2() + "," + baSyjl.getJbdm3() + "," + baSyjl.getJbdm4() + "," + baSyjl.getJbdm5() + "," +
        baSyjl.getJbdm6() + "," + baSyjl.getJbdm7() + "," + baSyjl.getJbdm8() + "," + baSyjl.getJbdm9() + "," + baSyjl.getJbdm10()
        + "," + baSyjl.getJbdm11() + "," + baSyjl.getJbdm12() + "," + baSyjl.getJbdm13() + "," + baSyjl.getJbdm14() + "," + baSyjl.getJbdm15();
      operCodes = baSyjl.getSsjczbm1() + "," + baSyjl.getSsjczbm2() + "," + baSyjl.getSsjczbm3() + "," + baSyjl.getSsjczbm4() + "," +
        baSyjl.getSsjczbm5() + "," + baSyjl.getSsjczbm6() + "," + baSyjl.getSsjczbm7();

      otherDiagCodes = String.join(",", Arrays.stream(otherDiagCodes.split(",")).filter(s -> !"null".equals(s)).toArray(String[]::new));
      operCodes = String.join(",", Arrays.stream(operCodes.split(",")).filter(s -> !"null".equals(s)).toArray(String[]::new));

    } else {

      List<BaBrzdxx> diagList = baBrzdxxService.selectBaBrzdxxList(new BaBrzdxx(baSyjl.getBrid(), baSyjl.getZyid()));
      if (diagList.size() > 0) {
        mainDiagCode = diagList.get(0).getJbbm();

        for (BaBrzdxx diag : diagList) {
          otherDiagCodes += diag.getJbbm() + ",";
        }
        if (otherDiagCodes.length() > 0) {
          otherDiagCodes = otherDiagCodes.substring(0, otherDiagCodes.length() - 1);
        }

        List<BaSsjl> operList = baSsjlService.selectBaSsjlList(new BaSsjl(baSyjl.getBrid(), baSyjl.getZyid()));

        for (BaSsjl oper : operList) {
          operCodes += oper.getSsbm() + ",";
        }
        if (operCodes.length() > 0) {
          operCodes = operCodes.substring(0, operCodes.length() - 1);
        }
      }
    }


    DrgFzResult drgFzResult = new DrgFzResult();
    if (StringUtils.isBlank(mainDiagCode)) {
      drgFzResult.setBah(baSyjl.getBah());
      drgFzResult.setBrbs(baSyjl.getBrbs());
      drgFzResult.setDrgbh("未入组");
      drgFzResult.setDrgmc("未入组");
    } else {
      baSyjl = tszdcl(baSyjl);
      Drgfz drgfz = new Drgfz();
      drgfz.setAction("getdrgfz");
      drgfz.setOrg(baSyjl.getOrgcode());
      drgfz.setBah(baSyjl.getBah());
      drgfz.setNl(baSyjl.getNl());
      drgfz.setBzyzsnl(baSyjl.getBzyzsnl());
      drgfz.setXsecstz(baSyjl.getXsecstz());
      drgfz.setSjzyts(baSyjl.getSjzyts());
      drgfz.setLyfs(baSyjl.getLyfs());
      drgfz.setZzdbm(mainDiagCode);
      drgfz.setZdxx(otherDiagCodes);
      drgfz.setSsxx(operCodes);
      drgfz.setZfy(baSyjl.getZfy());
      drgfz.setTime("");
      drgfz.setNum(String.valueOf(Math.ceil(Math.random() * 10000)));
      drgfz.setYlfkfs(baSyjl.getYlfkfs());
      drgFzResult = drgFzqController.fzq(drgfz);
    }

    drgFzqService.insertBdFzxx(drgFzResult);
    return drgFzResult;
//    return drgFzqController.fzq(drgfz);
  }

  /**
   * 特数字段处理
   *
   * @param baSyjl
   * @return
   */
  public BaSyjl tszdcl(BaSyjl baSyjl) {
    //离院医嘱
    if (Objects.isNull(baSyjl.getLyfs())) {
      baSyjl.setLyfs("");
    } else {
      String lyfs = baSyjl.getLyfs();
      if (lyfs != null && !"".equals(lyfs)) {
        switch (lyfs) {
          case "医嘱离院":
            lyfs = "1";
            break;
          case "医嘱转院":
            lyfs = "2";
            break;
          case "医嘱转社区卫生服务机构/乡镇卫生院":
            lyfs = "3";
            break;
          case "非医嘱离院":
            lyfs = "4";
            break;
          case "死亡":
            lyfs = "5";
            break;
          case "其他":
            lyfs = "9";
            break;
          default:
            lyfs = "";
        }
        baSyjl.setLyfs(lyfs);
      }
    }

    //性别
    if (baSyjl.getXb() != null || !"".equals(baSyjl.getXb())) {
      if ("男".equals(baSyjl.getXb())) {
        baSyjl.setXb("1");
      } else if ("女".equals(baSyjl.getXb())) {
        baSyjl.setXb("2");
      } else {
        baSyjl.setXb("");
      }
    }
    return baSyjl;
  }


  /**
   * 病历分组明细表批量分组   Java版分组器
   */
  @RequestMapping("/blfzmxPlfz")
  public void blfzmx_plfz() {
    List<Blfzmx> blfzmxList = blfzmxService.selectBlfzmxList(new Blfzmx());
    for (int i = 0; i < blfzmxList.size(); i++) {
      String zzd_bm = "";
      String qtzd_bm_str = "";
      String ss_bm_str = "";

      Blfzmx blfzmx = blfzmxList.get(i);

      //离院医嘱
      if (Objects.isNull(blfzmx.getLyfs())) {
        blfzmx.setLyfs("");
      } else {
        String lyfs = blfzmx.getLyfs();
        if (lyfs != null && !"".equals(lyfs)) {
          switch (lyfs) {
            case "医嘱离院":
              lyfs = "1";
              break;
            case "医嘱转院":
              lyfs = "2";
              break;
            case "医嘱转社区卫生服务机构/乡镇卫生院":
              lyfs = "3";
              break;
            case "非医嘱离院":
              lyfs = "4";
              break;
            case "死亡":
              lyfs = "5";
              break;
            case "其他":
              lyfs = "9";
              break;
            default:
              lyfs = "";
          }
          blfzmx.setLyfs(lyfs);
        }
      }

      //性别
      if (blfzmx.getXb() != null || !"".equals(blfzmx.getXb())) {
        if ("男".equals(blfzmx.getXb())) {
          blfzmx.setXb("1");
        } else if ("女".equals(blfzmx.getXb())) {
          blfzmx.setXb("2");
        } else {
          blfzmx.setXb("");
        }
      }

      qtzd_bm_str = blfzmx.getJbdm1() + "," + blfzmx.getJbdm2() + "," + blfzmx.getJbdm3() + "," + blfzmx.getJbdm4() + "," + blfzmx.getJbdm5() + "," +
        blfzmx.getJbdm6() + "," + blfzmx.getJbdm7() + "," + blfzmx.getJbdm8() + "," + blfzmx.getJbdm9() + "," + blfzmx.getJbdm10()
        + "," + blfzmx.getJbdm11() + "," + blfzmx.getJbdm12() + "," + blfzmx.getJbdm13() + "," + blfzmx.getJbdm14() + "," + blfzmx.getJbdm15();
      ss_bm_str = blfzmx.getSsjczbm1() + "," + blfzmx.getSsjczbm2() + "," + blfzmx.getSsjczbm3() + "," + blfzmx.getSsjczbm4() + "," +
        blfzmx.getSsjczbm5() + "," + blfzmx.getSsjczbm6() + "," + blfzmx.getSsjczbm7() + "," + blfzmx.getSsjczbm8();

      qtzd_bm_str = String.join(",", Arrays.stream(qtzd_bm_str.split(",")).filter(s -> !"null".equals(s)).toArray(String[]::new));
      ss_bm_str = String.join(",", Arrays.stream(ss_bm_str.split(",")).filter(s -> !"null".equals(s)).toArray(String[]::new));
      zzd_bm = blfzmx.getJbdm();

      System.out.println("-------------------qtzd_bm_str:" + qtzd_bm_str);
      System.out.println("-------------------ss_bm_str:" + ss_bm_str);
      System.out.println("-------------------zzd_bm:" + zzd_bm);

      Drgfz drgfz = new Drgfz();
      drgfz.setAction("getdrgfz");
      drgfz.setBah(blfzmx.getBah());
      drgfz.setNl(blfzmx.getNl());
      drgfz.setBzyzsnl(blfzmx.getBzyzsnl());
      drgfz.setSjzyts(blfzmx.getSjzyts());
      drgfz.setLyfs(blfzmx.getLyfs());
      drgfz.setZzdbm(zzd_bm);
      drgfz.setZdxx(qtzd_bm_str);
      drgfz.setSsxx(ss_bm_str);
      drgfz.setZfy(BigDecimal.valueOf(blfzmx.getZfy()));
      drgfz.setTime("");
      drgfz.setNum(String.valueOf(Math.ceil(Math.random() * 10000)));

      DrgFzResult drgFzResult = drgFzqController.fzq(drgfz);
      System.out.println(drgFzResult.getDrgbh());
      System.out.println(drgFzResult.getDrgmc());

      blfzmx.setCsdrgbh(drgFzResult.getDrgbh());
      blfzmx.setCsdrgmc(drgFzResult.getDrgmc());
      try {
        blfzmxService.updateBlfzmx(blfzmx);
      } catch (Exception e) {
        // TODO: handle exception
      }


    }
  }


  /**
   * 病历分组明细表批量分组  开源分组器
   */
  @RequestMapping("/chsDrgPlfz")
  public void chs_drg_plfz() {
    GroupProxy groupProxy = new GroupProxy();
    List<Blfzmx> blfzmxList = blfzmxService.selectBlfzmxList(new Blfzmx());
    for (int i = 0; i < blfzmxList.size(); i++) {
      String zd_bm_str = "";
      String ss_bm_str = "";

      Blfzmx blfzmx = blfzmxList.get(i);

      //离院医嘱
      if (Objects.isNull(blfzmx.getLyfs())) {
        blfzmx.setLyfs("");
      } else {
        String lyfs = blfzmx.getLyfs();
        if (lyfs != null && !"".equals(lyfs)) {
          switch (lyfs) {
            case "医嘱离院":
              lyfs = "1";
              break;
            case "医嘱转院":
              lyfs = "2";
              break;
            case "医嘱转社区卫生服务机构/乡镇卫生院":
              lyfs = "3";
              break;
            case "非医嘱离院":
              lyfs = "4";
              break;
            case "死亡":
              lyfs = "5";
              break;
            case "其他":
              lyfs = "9";
              break;
            default:
              lyfs = "";
          }
          blfzmx.setLyfs(lyfs);
        }
      }

      //性别
      if (blfzmx.getXb() != null || !"".equals(blfzmx.getXb())) {
        if ("男".equals(blfzmx.getXb())) {
          blfzmx.setXb("1");
        } else if ("女".equals(blfzmx.getXb())) {
          blfzmx.setXb("2");
        } else {
          blfzmx.setXb("");
        }
      }


      zd_bm_str = blfzmx.getJbdm() + "|" + blfzmx.getJbdm1() + "|" + blfzmx.getJbdm2() + "|" + blfzmx.getJbdm3() + "|" + blfzmx.getJbdm4() + "|" + blfzmx.getJbdm5() + "|" +
        blfzmx.getJbdm6() + "|" + blfzmx.getJbdm7() + "|" + blfzmx.getJbdm8() + "|" + blfzmx.getJbdm9() + "|" + blfzmx.getJbdm10()
        + "|" + blfzmx.getJbdm11() + "|" + blfzmx.getJbdm12() + "|" + blfzmx.getJbdm13() + "|" + blfzmx.getJbdm14() + "|" + blfzmx.getJbdm15();
      ss_bm_str = blfzmx.getSsjczbm1() + "|" + blfzmx.getSsjczbm2() + "|" + blfzmx.getSsjczbm3() + "|" + blfzmx.getSsjczbm4() + "|" +
        blfzmx.getSsjczbm5() + "|" + blfzmx.getSsjczbm6() + "|" + blfzmx.getSsjczbm7() + "|" + blfzmx.getSsjczbm8();

      System.out.println("-------------------zd_bm_str:" + zd_bm_str);

      zd_bm_str = String.join("|", Arrays.stream(zd_bm_str.split("\\|")).filter(s -> !"null".equals(s)).toArray(String[]::new));
      ss_bm_str = String.join("|", Arrays.stream(ss_bm_str.split("\\|")).filter(s -> !"null".equals(s)).toArray(String[]::new));

      System.out.println("-------------------zd_bm_str:" + zd_bm_str);
      System.out.println("-------------------ss_bm_str:" + ss_bm_str);

      if(blfzmx.getXb() ==null||"".equals(blfzmx.getXb())) {
        blfzmx.setXb("1");
      }

      String fzurl =
        blfzmx.getBah() + "," +
          blfzmx.getXb() + "," +
          blfzmx.getNl() + "," +
          blfzmx.getBzyzsnl() + "," +
          blfzmx.getXsecstz() + "," +
          "" + "," +
          blfzmx.getSjzyts() + "," +
          blfzmx.getLyfs() + "," +
          zd_bm_str + "," +
          ss_bm_str + ",";


      fzurl = fzurl.replace("null", "");

      GroupResult groupResult = groupProxy.group_record(fzurl);
      DrgFzResult drgFzResult = new DrgFzResult();
      drgFzResult.setBah(blfzmx.getBah());
      if ("分组成功".equals(groupResult.status)) {
        String s = groupResult.messages.get(groupResult.messages.size() - 1);
        s = s.replaceAll("\\*", "");
        blfzmx.setKydrgbh(s.split(" ")[0]);
        blfzmx.setKydrgmc(s.split(" ")[1]);
        blfzmxService.updateBlfzmx(blfzmx);

      }else {
        System.out.println(groupResult.status+groupResult.messages);
      }

    }

  }

  /**
   * 单病人分组
   */
  @RequestMapping("/prepareGroup")
  public String prepareGroup(@RequestParam(value = "brbs", required = false) String brbs) {
//    System.out.println("--------------------------brbs:" + brbs);
    if (StringUtils.isBlank(brbs)) {
      return "请输入正确的brbs";
    }

    BaSyjl basy = new BaSyjl();
    basy.setBrbs(brbs);
    List<BaSyjl> baSyjlList = baSyjlService.selectBaSyjlList(basy);

    if (baSyjlList.size() > 0) {
      basy = baSyjlList.get(0);
      if (basy == null) {
        return "当前病案不存在";
      }

      String brid = basy.getBrid();
      String zyid = basy.getZyid();
      String jzh = basy.getJzh();


      //①同步zdxx和ba_ssjl_tjzd
      try {
        brxxSyncService.zdxxSync(brid, zyid);
      } catch (Exception e) {
        logger.error("-------------------同步诊断失败，患者：" + jzh);
      } finally {
      }

      String qtzd_bm_str = "";
      String zzd_bm = "";
      String ss_bm_str = "";


      //②检查出院诊断：类型为3
      Zdxx zdxx = new Zdxx();
      zdxx.setJzh(jzh);
      zdxx.setZdtype("3");
      List<Zdxx> zdxxList = zdxxService.selectZdxxList(zdxx);


      if (zdxxList.isEmpty()) {
        //③检查入院诊断： 类型为2
        zdxx.setZdtype("2");
        zdxxList = zdxxService.selectZdxxList(zdxx);
      }

      if (zdxxList.isEmpty()) {
        //④从blryzd生成
        List<Brxx> brxxList = brxxService.selectBrxxByJzh(jzh);

        if (brxxList.isEmpty() || brxxList.get(0) == null || isNullOrEmpty(brxxList.get(0).getBlryzd())) {
          return "病人诊断信息为空";
        }

        String blryzd = brxxList.get(0).getBlryzd();
        blryzd = bzmlfController.getjbbmml("lczd", blryzd, "ryzd");

        String[] fjblryzd = blryzd.split("\\|\\|");

        if (fjblryzd.length > 0) {
          String[] fjbmArr = fjblryzd[0].split(",");
          if (fjbmArr.length > 0) {
            zzd_bm = fjbmArr[0];
            if (fjbmArr.length > 1) {
              qtzd_bm_str = String.join(",", Arrays.copyOfRange(fjbmArr, 1, fjbmArr.length));
            }
          }
        } else {
          return "病人诊断信息为空";
        }
      }

      if (!zdxxList.isEmpty()) {
        zzd_bm = zdxxList.get(0).getZdcode();
        if (zdxxList.size() > 1) {
          StringBuilder qtzd_bm_sb = new StringBuilder();
          for (int ll_i = 1; ll_i < zdxxList.size(); ll_i++) {
            qtzd_bm_sb.append(zdxxList.get(ll_i).getZdcode()).append(",");
          }
          if (qtzd_bm_sb.length() > 0) {
            qtzd_bm_sb.setLength(qtzd_bm_sb.length() - 1);
          }
          qtzd_bm_str = qtzd_bm_sb.toString();
        }
      }


      //手术
      BaSsjl baSsjl = new BaSsjl();
      baSsjl.setJlly("3");
      baSsjl.setBrid(brid);
      baSsjl.setZyid(zyid);
      List<BaSsjl> baSsjls = baSsjlService.selectBaSsjlList(baSsjl);
      if (baSsjls.size() == 0) {   //同步手术记录
        try {
          brxxSyncService.ssjlSync(brid, zyid);
        } catch (Exception e) {
          logger.info("-------------------同步手术失败，患者：" + jzh);
        } finally {
          baSsjls = baSsjlService.selectBaSsjlList(baSsjl);
        }
      }

      if (baSsjls.size() > 0) {
        StringBuilder ss_bm_sb = new StringBuilder();
        for (int ll_i = 0; ll_i < baSsjls.size(); ll_i++) {
          ss_bm_sb.append(baSsjls.get(ll_i).getSsbm()).append(",");
        }
        if (ss_bm_sb.length() > 0) {
          ss_bm_sb.setLength(ss_bm_sb.length() - 1);
        }
        ss_bm_str = ss_bm_sb.toString();
      }


      System.out.println("-----------------------------------");
      System.out.println("zzd_bm：" + zzd_bm);
      System.out.println("qtzd_bm_str：" + qtzd_bm_str);
      System.out.println("ss_bm_str：" + ss_bm_str);
      System.out.println("-----------------------------------");


      basy = tszdcl(basy);

      Drgfz drgfz = new Drgfz();
      drgfz.setAction("getdrgfz");
      drgfz.setOrg(basy.getOrgcode());
      drgfz.setBah(basy.getBah());
      drgfz.setNl(basy.getNl());
      drgfz.setBzyzsnl(basy.getBzyzsnl());
      drgfz.setXsecstz(basy.getXsecstz());
      drgfz.setSjzyts(basy.getSjzyts());
      drgfz.setLyfs(basy.getLyfs());
      drgfz.setZzdbm(zzd_bm);
      drgfz.setZdxx(qtzd_bm_str);
      drgfz.setSsxx(ss_bm_str);
      drgfz.setZfy(basy.getZfy());
      drgfz.setTime("");
      drgfz.setNum(String.valueOf(Math.ceil(Math.random() * 10000)));
      drgfz.setYlfkfs(basy.getYlfkfs());

      DrgFzResult drgFzResult = drgFzqController.fzq(drgfz);

      basy.setDrgbh(drgFzResult.getDrgbh());
      basy.setZfbz(drgFzResult.getZfbz());

      baSyjlService.updateBaSyjl(basy);

      return JSONArray.toJSONString(drgFzResult);
    } else {
      return "当前病案不存在";
    }
  }

  private boolean isNullOrEmpty(String str) {
    return str == null || str.trim().isEmpty();
  }

  @RequestMapping("/batchGrouping")
  public void batchGrouping() {
    List<BaSyjl> baSyjlList = baSyjlService.selectBaSyjlList(new BaSyjl());

    ExecutorService executorService = Executors.newFixedThreadPool(30);

    baSyjlList.forEach(baSyjl -> {
      executorService.execute(() -> {
        try {
          test(baSyjl);
        } catch (IOException e) {
          throw new RuntimeException(e);
        }
      });
    });
    executorService.shutdown();

  }


  public void test(BaSyjl baSyjl) throws IOException {

    String zzd_bm = "";
    String qtzd_bm_str = "";
    String ss_bm_str = "";
    Drgfz drgfz = new Drgfz();

//    List<BaBrzdxxSy> baBrzdxxes = baBrzdxxSyService.selectBaBrzdxxSyList(new BaBrzdxxSy(baSyjl.getBrid(), baSyjl.getZyid()));
//    if (baBrzdxxes.size() > 0) {
//      zzd_bm = baBrzdxxes.get(0).getJbbm();
//      if (baBrzdxxes.size() > 1) {
//        for (int i1 = 1; i1 < baBrzdxxes.size(); i1++) {
//          qtzd_bm_str += baBrzdxxes.get(i1).getJbbm() + ",";
//        }
//        if (qtzd_bm_str.length() > 0) {
//          qtzd_bm_str = qtzd_bm_str.substring(0, qtzd_bm_str.length() - 1);
//        }
//      }
//    }
//
//    List<BaSsjlSy> baSsjls = baSsjlSyService.selectBaSsjlSyList(new BaSsjlSy(baSyjl.getBrid(), baSyjl.getZyid()));
//    if (baSsjls.size() > 0) {
//      for (int i1 = 0; i1 < baSsjls.size(); i1++) {
//        ss_bm_str += baSsjls.get(i1).getSsbm() + ",";
//      }
//    }
//    if (ss_bm_str.length() > 0) {
//      ss_bm_str = ss_bm_str.substring(0, ss_bm_str.length() - 1);
//    }

    zzd_bm = baSyjl.getJbdm();
    qtzd_bm_str = baSyjl.getJbdm1() + "," + baSyjl.getJbdm2() + "," + baSyjl.getJbdm3() + "," + baSyjl.getJbdm4() + "," + baSyjl.getJbdm5() + "," +
      baSyjl.getJbdm6() + "," + baSyjl.getJbdm7() + "," + baSyjl.getJbdm8() + "," + baSyjl.getJbdm9() + "," + baSyjl.getJbdm10()
      + "," + baSyjl.getJbdm11() + "," + baSyjl.getJbdm12() + "," + baSyjl.getJbdm13() + "," + baSyjl.getJbdm14() + "," + baSyjl.getJbdm15();
    ss_bm_str = baSyjl.getSsjczbm1() + "," + baSyjl.getSsjczbm2() + "," + baSyjl.getSsjczbm3() + "," + baSyjl.getSsjczbm4() + "," +
      baSyjl.getSsjczbm5() + "," + baSyjl.getSsjczbm6() + "," + baSyjl.getSsjczbm7();

    qtzd_bm_str = String.join(",", Arrays.stream(qtzd_bm_str.split(",")).filter(s -> !"null".equals(s)).toArray(String[]::new));
    ss_bm_str = String.join(",", Arrays.stream(ss_bm_str.split(",")).filter(s -> !"null".equals(s)).toArray(String[]::new));



    System.out.println("0---------------------------主要诊断：" + zzd_bm);
    System.out.println("0---------------------------诊断信息：" + qtzd_bm_str);
    System.out.println("0---------------------------手术信息：" + ss_bm_str);

    baSyjl = tszdcl(baSyjl);
    drgfz.setAction("getdrgfz");
    drgfz.setOrg(baSyjl.getOrgcode());
    drgfz.setBah(baSyjl.getBah());
    drgfz.setNl(baSyjl.getNl());
    drgfz.setBzyzsnl(baSyjl.getBzyzsnl());
    drgfz.setXsecstz(baSyjl.getXsecstz());
    drgfz.setSjzyts(baSyjl.getSjzyts());
    drgfz.setLyfs(baSyjl.getLyfs());
    drgfz.setZzdbm(zzd_bm);
    drgfz.setZdxx(qtzd_bm_str);
    drgfz.setSsxx(ss_bm_str);
    drgfz.setZfy(baSyjl.getZfy());
    drgfz.setTime("");
    drgfz.setNum(String.valueOf(Math.ceil(Math.random() * 10000)));
    drgfz.setYlfkfs(baSyjl.getYlfkfs());

    drgfz.setBrbs(baSyjl.getBrbs());
    DrgFzResult fzResult = drgFzqController.fzq(drgfz);
    drgFzqService.insertBdFzxx(fzResult);
  }

  @RequestMapping("/updateFz")
  public void updateFz() {
    drgFzqService.updateFz();
  }

  @RequestMapping("/updateBx")
  public void updateBx() {
    drgFzqService.updateBx();
  }


  @RequestMapping("/test20")
  public void test20(@RequestParam(value = "brbs",required = false) String brbs) {
    List<Drgfz> list = baSyjlService.selectFzTestBa(brbs);
    for (Drgfz drgfz : list) {
      drgfz.setAction("getdrgfz");
      drgfz.setOrg("test");
      drgfz.setTime("");
      drgfz.setNum(String.valueOf(Math.ceil(Math.random() * 10000)));
      drgfz.setYlfkfs("test");
      DrgFzResult fzResult = drgFzqController.fzq(drgfz);
      drgFzqService.insertBdFzxx(fzResult);
    }
  }

}
