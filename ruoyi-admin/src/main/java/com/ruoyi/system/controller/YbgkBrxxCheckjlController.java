package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.YbgkBrxxCheckjl;
import com.ruoyi.system.service.IYbgkBrxxCheckjlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 病人信息审核记录Controller
 * 
 * <AUTHOR>
 * @date 2024-05-06
 */
@RestController
@RequestMapping("/system/checkjl")
public class YbgkBrxxCheckjlController extends BaseController
{
    @Autowired
    private IYbgkBrxxCheckjlService ybgkBrxxCheckjlService;

    /**
     * 查询病人信息审核记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:checkjl:list')")
    @GetMapping("/list")
    public TableDataInfo list(YbgkBrxxCheckjl ybgkBrxxCheckjl)
    {
        startPage();
        List<YbgkBrxxCheckjl> list = ybgkBrxxCheckjlService.selectYbgkBrxxCheckjlList(ybgkBrxxCheckjl);
        return getDataTable(list);
    }

    /**
     * 导出病人信息审核记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:checkjl:export')")
    @Log(title = "病人信息审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YbgkBrxxCheckjl ybgkBrxxCheckjl)
    {
        List<YbgkBrxxCheckjl> list = ybgkBrxxCheckjlService.selectYbgkBrxxCheckjlList(ybgkBrxxCheckjl);
        ExcelUtil<YbgkBrxxCheckjl> util = new ExcelUtil<YbgkBrxxCheckjl>(YbgkBrxxCheckjl.class);
        util.exportExcel(response, list, "病人信息审核记录数据");
    }

    /**
     * 获取病人信息审核记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:checkjl:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ybgkBrxxCheckjlService.selectYbgkBrxxCheckjlById(id));
    }

    /**
     * 新增病人信息审核记录
     */
    @PreAuthorize("@ss.hasPermi('system:checkjl:add')")
    @Log(title = "病人信息审核记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YbgkBrxxCheckjl ybgkBrxxCheckjl)
    {
        return toAjax(ybgkBrxxCheckjlService.insertYbgkBrxxCheckjl(ybgkBrxxCheckjl));
    }

    /**
     * 修改病人信息审核记录
     */
    @PreAuthorize("@ss.hasPermi('system:checkjl:edit')")
    @Log(title = "病人信息审核记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YbgkBrxxCheckjl ybgkBrxxCheckjl)
    {
        return toAjax(ybgkBrxxCheckjlService.updateYbgkBrxxCheckjl(ybgkBrxxCheckjl));
    }

    /**
     * 删除病人信息审核记录
     */
    @PreAuthorize("@ss.hasPermi('system:checkjl:remove')")
    @Log(title = "病人信息审核记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ybgkBrxxCheckjlService.deleteYbgkBrxxCheckjlByIds(ids));
    }
}
