package com.ruoyi.system.service;

import cn.hutool.db.Entity;
import com.ruoyi.system.domain.Icd9ybdy;
import com.ruoyi.system.domain.vo.CompareIcdHisVo;
import com.ruoyi.system.domain.vo.IcdMaintainQueryVo;
import com.ruoyi.system.domain.vo.IcdMaintainVo;
import com.ruoyi.system.domain.vo.IcdUpdateVo;

import java.util.List;

/**
 * icd9ybdyService接口
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
public interface IIcd9ybdyService {

  List<CompareIcdHisVo> compareIcd9His(CompareIcdHisVo compareIcdHisVo);

  void syncIcd9His(List<Icd9ybdy> icd9ybdyList);

  void syncIcd9(List<Icd9ybdy> icd9ybdyList);

  void clearIcd9();

  void clearIcd9His();

  /**
   * 查询icd9ybdy
   *
   * @param bm icd9ybdy主键
   * @return icd9ybdy
   */
  public Icd9ybdy selectIcd9ybdyByBm(String bm);

  /**
   * 查询icd9ybdy列表
   *
   * @param icd9ybdy icd9ybdy
   * @return icd9ybdy集合
   */
  public List<Icd9ybdy> selectIcd9ybdyList(Icd9ybdy icd9ybdy);

  /**
   * 新增icd9ybdy
   *
   * @param icd9ybdy icd9ybdy
   * @return 结果
   */
  public int insertIcd9ybdy(Icd9ybdy icd9ybdy);

  /**
   * 修改icd9ybdy
   *
   * @param icd9ybdy icd9ybdy
   * @return 结果
   */
  public int updateIcd9ybdy(Icd9ybdy icd9ybdy);

  /**
   * 批量删除icd9ybdy
   *
   * @param bms 需要删除的icd9ybdy主键集合
   * @return 结果
   */
  public int deleteIcd9ybdyByBms(String[] bms);

  /**
   * 删除icd9ybdy信息
   *
   * @param bm icd9ybdy主键
   * @return 结果
   */
  public int deleteIcd9ybdyByBm(String bm);

  List<IcdMaintainVo> selectIcd9List(IcdMaintainQueryVo queryVo);

  void updateIcd9Info(IcdUpdateVo vo);

  void icdAdd(IcdUpdateVo vo);

  void insertIcd9UpdateAndInsertByEntity(List<Entity> icd9List);

  void usp_update_icd10and9_ybdy();

  void deleteAllIcd9();
}
