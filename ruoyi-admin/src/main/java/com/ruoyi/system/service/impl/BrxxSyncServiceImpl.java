package com.ruoyi.system.service.impl;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.Gson;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.controller.BaxyController;
import com.ruoyi.system.controller.BzmlfController;
import com.ruoyi.system.controller.CyshController;
import com.ruoyi.system.controller.DrgFzController;
import com.ruoyi.system.controller.DrgFzqController;
import com.ruoyi.system.domain.BaBrzdxx;
import com.ruoyi.system.domain.BaSsjl;
import com.ruoyi.system.domain.BaSsjlSy;
import com.ruoyi.system.domain.BaSsjlTjzd;
import com.ruoyi.system.domain.BaSyjl;
import com.ruoyi.system.domain.BaSyjlDoctor;
import com.ruoyi.system.domain.BaSyjlExt;
import com.ruoyi.system.domain.Blxx;
import com.ruoyi.system.domain.Brxx;
import com.ruoyi.system.domain.Bzml;
import com.ruoyi.system.domain.DrgFzResult;
import com.ruoyi.system.domain.Drgfz;
import com.ruoyi.system.domain.Fyxx;
import com.ruoyi.system.domain.HUserDept;
import com.ruoyi.system.domain.Hisxxsql;
import com.ruoyi.system.domain.Icd10ybdy;
import com.ruoyi.system.domain.Icd9ybdy;
import com.ruoyi.system.domain.Jcxx;
import com.ruoyi.system.domain.JsxxHis;
import com.ruoyi.system.domain.Jyxx;
import com.ruoyi.system.domain.MztbFyxx;
import com.ruoyi.system.domain.SettleSsjl;
import com.ruoyi.system.domain.SettleZdxx;
import com.ruoyi.system.domain.SysOperLog;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.domain.YbgkDz;
import com.ruoyi.system.domain.YbgkWgjl;
import com.ruoyi.system.domain.YbjkOption;
import com.ruoyi.system.domain.Yzxx;
import com.ruoyi.system.domain.Zdxx;
import com.ruoyi.system.mapper.JsxxHisMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.IAPIDataSyncService;
import com.ruoyi.system.service.IBaBrzdxxService;
import com.ruoyi.system.service.IBaBrzdxxSyService;
import com.ruoyi.system.service.IBaSsjlService;
import com.ruoyi.system.service.IBaSsjlSyService;
import com.ruoyi.system.service.IBaSsjlTjzdService;
import com.ruoyi.system.service.IBaSyjlDoctorService;
import com.ruoyi.system.service.IBaSyjlExtService;
import com.ruoyi.system.service.IBaSyjlService;
import com.ruoyi.system.service.IBlxxService;
import com.ruoyi.system.service.IBrxxService;
import com.ruoyi.system.service.IBrxxSyncService;
import com.ruoyi.system.service.IBzmlService;
import com.ruoyi.system.service.IDataSyncService;
import com.ruoyi.system.service.IFyxxService;
import com.ruoyi.system.service.IHDeptService;
import com.ruoyi.system.service.IHUserDeptService;
import com.ruoyi.system.service.IHisxxsqlService;
import com.ruoyi.system.service.IIcd10ybdyService;
import com.ruoyi.system.service.IIcd9ybdyService;
import com.ruoyi.system.service.IJcxxService;
import com.ruoyi.system.service.IJsxxHisService;
import com.ruoyi.system.service.IJsxxHisZyService;
import com.ruoyi.system.service.IJsxxService;
import com.ruoyi.system.service.IJyxxService;
import com.ruoyi.system.service.IMztbFyxxService;
import com.ruoyi.system.service.ISettleSsjlService;
import com.ruoyi.system.service.ISettleZdxxService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.IYbgkDzService;
import com.ruoyi.system.service.IYbjkOptionService;
import com.ruoyi.system.service.IYzxxService;
import com.ruoyi.system.service.IZdxxService;
import com.ruoyi.tools.DateTools;
import com.ruoyi.tools.MzfsEnum;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Entity;

@Service
public class BrxxSyncServiceImpl implements IBrxxSyncService {

  private Logger logger = LoggerFactory.getLogger(this.getClass());

  @Autowired
  private IBlxxService blxxService;
  @Autowired
  private IBrxxService brxxService;
  @Autowired
  private IBaSyjlService baSyjlService;
  @Autowired
  private IZdxxService zdxxService;
  @Autowired
  private IBzmlService bzmlService;
  @Autowired
  private IDataSyncService dataSyncService;
  @Autowired
  private IBaBrzdxxService baBrzdxxService;
  @Autowired
  private IBaBrzdxxSyService baBrzdxxSyService;
  @Autowired
  private IFyxxService fyxxService;
  @Autowired
  private IYbjkOptionService ybjkOptionService;
  @Autowired
  private ISysDeptService sysDeptService;
  @Autowired
  private SysDeptMapper sysDeptMapper;
  @Autowired
  private ISysUserService sysUserService;
  @Autowired
  private SysUserMapper sysUserMapper;
  @Autowired
  private IHisxxsqlService hisxxsqlService;
  @Autowired
  private IJyxxService jyxxService;
  @Autowired
  private IJcxxService jcxxService;
  @Autowired
  private IJsxxHisService jsxxHisService;
  @Autowired
  private IJsxxHisZyService jsxxHisZyService;
  @Autowired
  private IJsxxService jsxxService;
  @Autowired
  private JsxxHisMapper jsxxHisMapper;
  @Autowired
  private IMztbFyxxService mztbFyxxService;
  @Autowired
  private IYbgkDzService ybgkDzService;
  @Autowired
  private DrgFzqController drgFzqController;
  @Autowired
  private IBaSyjlDoctorService baSyjlDoctorService;
  @Autowired
  private BaxyController baxyController;
  @Autowired
  private CyshController cyshController;
  @Autowired
  private DrgFzController drgFzController;
  @Autowired
  private IBaSsjlTjzdService baSsjlTjzdService;
  @Autowired
  private IBaSsjlSyService baSsjlSyService;
  @Autowired
  private IBaSsjlService baSsjlService;
  @Autowired
  private IAPIDataSyncService iapiDataSyncService;
  @Autowired
  private BzmlfController bzmlfController;
  @Autowired
  private IYzxxService yzxxService;
  @Autowired
  private IIcd10ybdyService icd10ybdyService;
  @Autowired
  private IIcd9ybdyService icd9ybdyService;
  @Autowired
  private IHUserDeptService ihUserDeptService;
  @Autowired
  private SysUserRoleMapper sysUserRoleMapper;
  @Autowired
  private IHDeptService ihDeptService;
  @Autowired
  private ISysTenantService tenantService;
  @Autowired
  private ISysOperLogService operLogService;
  @Autowired
    private ISettleZdxxService setlZdService;
  @Autowired
    private ISettleSsjlService setlSsService;


  List<SysTenant> orgList = new ArrayList<>();
  String hasMultipleHis = null;
  String hasMultipleOrg = null;

  String ls_syly = "";
  String is_hissoftname = "";
  String companyName = "";
  String is_zbrxx_zbasy_flag = "";
  String is_zbasy_zbl = "";
  String is_zbasy_zfy = "1";
  String is_zbasy_zd = "1";
  String is_dj_emr = "";
  String is_kxyy_flag = "";
  String gs_havedrg_flag = "";
  String is_jzh_usebrid = "1";
  String is_trans_hisjs_ztb = "";
  String is_zjs_bfy = "";
  String basy_doctor_sync = "0";
  String is_zdsync_sfsh = "0";
  String basy_sync_with_blxx = "1";
  String basy_sync_with_fyxx = "1";
  String basy_sync_zdly = "1";
  String blxx_sync_for_br = "1";
  String yzxx_sync="1";
  String syncAllFeeByNum="0";
  String basy_sync_from_api=""; //首页是否通过API
  String yzxx_sync_for_br = "0";
  LocalDateTime sync_limit_date;
  String use_ybqd = "0";
  String is_tb_bqfjg="0";//同步不区分机构
  String update_anst = "0";
  String is_patientDataSources=null;
  String fyxx_ctrl_months = "12"; //转费用时控制历史费用判定时长为多少月
  String trans_blxx_ty="0";//转病历用通用方式

  DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
  SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
  @Autowired
  private IBaSyjlExtService iBaSyjlExtService;

  /**
   * 同步病案首页信息
   *
   * @param brbs
   * @return
   */
  public Integer basySync(String brbs, String jgid) {

    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    Date adt_jsdate = null;
    Date adt_cydate = null;
    LocalDateTime ldt_jsdate = null;
    LocalDateTime ldt_cydate = null;
    String as_zyzt = null;
    String as_cyzd = null;

    String old_jbbm = null;
    String old_ssbm = null;
    String old_drgbh = null;
    BigDecimal old_zfbz = null;

    BaSyjl drgbasy = baSyjlService.selectBaSyjlByBrbs(brbs);
    BaSyjlExt drgBaSyjlExt = iBaSyjlExtService.selectBaSyjlExtByBrbs(brbs);

    Brxx br = brxxService.selectBrxxByBrbs(brbs);

    if (("5".equals(use_ybqd) || "13".equals(use_ybqd)) && br != null && br.getShzt() == 1) {
      // 不通过his同步，直接使用已保存的病案诊断
      List<SettleZdxx> bazdList = setlZdService.selectSettleZdxxList(new SettleZdxx().setJlly("6").setBrbs(brbs));
      List<SettleSsjl> bassList = setlSsService.selectSettleSsjlList(new SettleSsjl().setJlly("6").setBrbs(brbs));

      List<BaBrzdxx> zdList = new ArrayList<>();
      List<BaSsjl> ssList = new ArrayList<>();

      bazdList.forEach(item -> {
        zdList.add(item.toBaBrzdxx());
      });
      bassList.forEach(item -> {
        ssList.add(item.toBaSsjl());
      });

      if(!bazdList.isEmpty()){
        baBrzdxxSyService.deleteBaBrzdxxSyById(new BaBrzdxx(br.getBrid(), br.getZyid()));
        baBrzdxxService.deleteBaBrzdxxById(new BaBrzdxx(br.getBrid(), br.getZyid()));

        for (BaBrzdxx zd : zdList) {
          baBrzdxxSyService.insertBaBrzdxxSy(zd);
          baBrzdxxService.insertBaBrzdxx(zd);
        }
        baSsjlSyService.deleteBaSsjlSyById(new BaSsjl(br.getBrid(), br.getZyid()));
        baSsjlService.deleteBaSsjlById(new BaSsjl(br.getBrid(), br.getZyid()));

        for (BaSsjl ss : ssList) {
          baSsjlSyService.insertBaSsjlSy(ss);
          baSsjlService.insertBaSsjl(ss);
        }

      }

      return 1;
    }

    if ("1".equals(hasMultipleHis) && jgid == null) {
      if (drgbasy  != null && StringUtils.isNotBlank(drgbasy.getUsername())) {
        jgid = drgbasy.getUsername();
      } else {

        if (br != null && StringUtils.isNotBlank(br.getJgid())) {
          jgid = br.getJgid();
        }

      }
    }

    String ls_brbs = brbs;
    String ls_brid = ls_brbs;
    String ls_zyid = "";


    if (drgbasy != null) {

      adt_jsdate = drgbasy.getHisJsdate();
      adt_cydate = drgbasy.getCydate();
      as_zyzt = drgbasy.getZyzt();

      if (sync_limit_date != null) {
        ldt_jsdate = DateTools.dateToLocalDateTime(adt_jsdate);
        ldt_cydate = DateTools.dateToLocalDateTime(adt_cydate);
        if ("0".equals(as_zyzt) && ((ldt_jsdate != null && ldt_jsdate.isBefore(sync_limit_date)) || (ldt_cydate != null && ldt_cydate.isBefore(sync_limit_date)))) {
          return 1;
        }
      }

      if (StringUtils.isNotBlank(drgbasy.getDrgbh()) && StringUtils.isNotBlank(drgbasy.getJbdm())) {
        old_drgbh = drgbasy.getDrgbh();
        old_jbbm = drgbasy.getJbdm();
        old_ssbm = drgbasy.getSsjczbm1();
        old_zfbz = drgbasy.getZfbz();
      }

      as_cyzd = drgbasy.getJbdm();
      ls_zyid = drgbasy.getZyid();
    } else {
      adt_jsdate = Date.from(LocalDateTime.of(2000, 01, 01, 01, 00, 00).atZone(ZoneId.systemDefault()).toInstant());
      as_zyzt = "0";
      as_cyzd = "";
    }


    if (ls_brbs.indexOf("_") > -1) {
      ls_brid = ls_brbs.split("_")[0];
      ls_zyid = ls_brbs.split("_")[1];
    }


    System.out.println("brbs:" + ls_brbs + "   zyid:" + ls_zyid + "   brid:" + ls_brid + " is_zbrxx_zbasy_flag=" + is_zbrxx_zbasy_flag + " is_zbasy_zbl=" + is_zbasy_zbl + " basy_sync_with_blxx=" + basy_sync_with_blxx);

    //转病人时转首页不同步
    if (!"1".equals(is_zbrxx_zbasy_flag)) {

      if ("1".equals(is_zbasy_zbl) && "1".equals(basy_sync_with_blxx)) {
        System.out.println("--------------------------------转首页时转病历");

        try {
          blxxSync(ls_brid, ls_zyid);
        } catch (Exception e) {
          // TODO: handle exception
        }
      }

      if ("2".equals(is_zbasy_zbl) && "1".equals(basy_sync_with_blxx)) {
        System.out.println("--------------------------------转首页时转病历2");
        blxxService.deleteBlxxByZyidAndBrid(new Blxx(ls_brid, ls_zyid));

        try {
          blxxSync(ls_brid, ls_zyid);
        } catch (Exception e) {
          // TODO: handle exception
        }
      }

      if ("1".equals(is_zbasy_zfy) && "1".equals(basy_sync_with_fyxx)) {
        System.out.println("--------------------------------转首页时转费用");
        try {
          fyxxSync(ls_brid, ls_zyid);
        } catch (Exception e) {
          e.printStackTrace();
          // TODO: handle exception
        }

      }
    }

    Map<String, Object> paramMap = new HashMap<>();
    paramMap.put("brid", ls_brid);
    paramMap.put("zyid", ls_zyid);


    //HIS系统病案科病案数据
    // todo 使用参数控制
    List<Entity> hisba;
    Brxx brxx = null;
    try {
//      logger.debug("准备同步病案首页");
      brxx = brxxService.selectBrxxByBrbs(ls_brid + "_" + ls_zyid);
      if("1".equals(basy_sync_from_api) && brxx != null && (brxx.getCydate() == null || brxx.getCydate().after(simpleDateFormat.parse("2024-11-20 00:00:00")))) {
        hisba = iapiDataSyncService.syncBasy(ls_brid, ls_zyid);
      }else {
        hisba = dataSyncService.executeQuery(getSqlName("ba_syjl",jgid), paramMap);
        logger.debug("ba_syjl his病案数据: {}", hisba != null ? hisba.size() : "不存在");
      }
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }


//    System.out.println("--------------HIS系统数据-----------------");
//    hisba.forEach(entity -> System.out.println(entity.get("xm")));
//    System.out.println("-----------------------------------------");

    //HIS系统医生病案数据
    List<Entity> hisba_ys = new ArrayList<>();

    System.out.println("--------------当前系统数据-----------------");
    System.out.println(drgbasy != null ? drgbasy.getXm() : "目标数据库不存在当前病案");
    System.out.println("-----------------------------------------");

    //以下是通用处理
    ls_syly = as_zyzt;

    //已结算，或已归档 1--在院，0--出院
    if ("2".equals(as_zyzt) || "3".equals(as_zyzt)) {
      as_zyzt = "0";
    }

    //取医生
    try {
      if (hisba.size() == 0 || hisba.get(0) == null || hisba.get(0).getStr("jbdm") == null || "".equals(hisba.get(0).getStr("jbdm"))) {
        if("1".equals(basy_sync_from_api) && brxx != null && (brxx.getCydate() == null || brxx.getCydate().after(simpleDateFormat.parse("2024-11-20 00:00:00")))) {
          hisba_ys = iapiDataSyncService.syncBasy(ls_brid, ls_zyid);
        } else {
          hisba_ys = dataSyncService.executeQuery(getSqlName("ba_syjl_ys",jgid), paramMap);
        }
      }
    } catch (ParseException e) {
      throw new RuntimeException(e);
    }


//    if (hisba.size() > 0 || hisba_ys.size() > 0) {
    BaSyjl tcxx = baSyjlService.selectTcxxByBrbs(ls_brbs);

    //病人统筹信息
    BigDecimal ldec_oldtc = null;
    Date ldt_oldjsdate = null;
    LocalDateTime ldt_oldopdate = null;
    String ls_oldzyzt = null;

    if (tcxx != null) {
      ldec_oldtc = tcxx.getTczf();
      ldt_oldjsdate = tcxx.getHisJsdate();
      ldt_oldopdate = formatDate(tcxx.getOpdate());
      ls_oldzyzt = tcxx.getZyzt();
    }

    BaSyjl oldSyjl = null;
    if (drgbasy != null) {
      oldSyjl = new BaSyjl();
      BeanUtils.copyProperties(drgbasy, oldSyjl);
    }

    //如果未有扩展表记录，创建一个新对象
    if (drgBaSyjlExt == null) {
      drgBaSyjlExt = new BaSyjlExt();
      drgBaSyjlExt.setBrbs(brbs);
    }


    if (hisba.size() > 0) {
      drgbasy = basyCopy(hisba, drgbasy);
      drgBaSyjlExt = basyExtCopy(hisba, drgBaSyjlExt);
      drgbasy.setJlly("4");
    } else {
      if (hisba_ys.size() > 0) {
        drgbasy = basyCopy(hisba_ys, drgbasy);
        drgBaSyjlExt = basyExtCopy(hisba_ys, drgBaSyjlExt);
        drgbasy.setJlly("3");
      } else {
        if ("1".equals(is_zbasy_zd) && !"1".equals(is_zbrxx_zbasy_flag)) {
          System.out.println("--------------------------------转首页时转诊断");
          zdxxService.deleteZdxxByBridAndZyid(new Zdxx(ls_brid, ls_zyid));
          zdxxSync(ls_brid, ls_zyid);
          updateBrzdxx(ls_brid, ls_zyid);
        }
        if (drgbasy != null && "1".equals(basy_sync_zdly)) {
          System.out.println("--------------------------------从诊断信息或病历信息获取首页诊断");
          try {
            drgbasy = fillBasyDiags(drgbasy);
            baSyjlService.deleteBaSyjlByBrbs(drgbasy.getBrbs());
            iBaSyjlExtService.deleteBaSyjlExtByBrbs(drgBaSyjlExt.getBrbs());
            baSyjlService.insertBaSyjl(drgbasy);
            iBaSyjlExtService.insertBaSyjlExt(drgBaSyjlExt);
          } catch (Exception e) {
            logger.error("从诊断信息或病历信息获取首页诊断时出错,就诊号" + drgbasy.getJzh() + ":" + e.getMessage());
            throw new ServiceException("从诊断信息或病历信息获取首页诊断时出错");
          }
        }
        return 1;
      }
    }

    if (drgbasy != null) {
      drgbasy.setHisJsdate(adt_jsdate);

      //来自在院
      if ("0".equals(as_zyzt)) {
        drgbasy.setZyzt("1");
      }

      if (drgbasy.getYlfkfs() != null) {
        if (drgbasy.getYlfkfs().indexOf("职工") > -1) {
          drgbasy.setYlfkfs("职工");
        } else if (drgbasy.getYlfkfs().indexOf("居民") > -1 && drgbasy.getYlfkfs().indexOf("合作") > -1) {
          drgbasy.setYlfkfs("居民");
        } else {
          drgbasy.setYlfkfs("自费");
        }
      }

      if (StrUtil.isBlank(drgbasy.getJbdm()) && "1".equals(basy_sync_zdly)) {
        try {
          drgbasy = fillBasyDiags(drgbasy);
        } catch (Exception e) {
          logger.error("从诊断信息或病历信息获取首页诊断时出错,就诊号" + drgbasy.getJzh() + ":" + e.getMessage());
          throw new ServiceException("从诊断信息或病历信息获取首页诊断时出错");
        }
      }

      if ((drgbasy.getJbdm() == null || "".equals(drgbasy.getJbdm())) && as_cyzd != null && !"".equals(as_cyzd) && "0".equals(basy_sync_zdly)) {
        Bzml bzml = bzmlService.selectBzmlByBzbm(as_cyzd);
        String ls_cyzd_name = "";
        if (bzml != null) {
          ls_cyzd_name = bzml.getBzmc();
        }
        drgbasy.setJbdm(as_cyzd);
        drgbasy.setZyzd(ls_cyzd_name);
      }

      //来自病案
      if ("3".equals(ls_syly)) {
        drgbasy.setOpdate(adt_jsdate);
        drgbasy.setZyzt("0");
      }

      //来自结算
      if ("2".equals(ls_syly)) {
        drgbasy.setHisJsdate(adt_jsdate);
        drgbasy.setZyzt("0");
      }

      if (ldt_oldopdate != null) {
        LocalDateTime dateTime = LocalDateTime.of(LocalDate.of(2000, 1, 1), LocalTime.now());
        if (ldt_oldopdate.compareTo(dateTime) > 0) {
          drgbasy.setTczf(ldec_oldtc);
          drgbasy.setHisJsdate(ldt_oldjsdate);
          drgbasy.setOpdate(Date.from(ldt_oldopdate.atZone(ZoneId.systemDefault()).toInstant()));
          drgbasy.setZyzt(ls_oldzyzt);
        }
      }

      if ("hzcy".equals(is_hissoftname) && "2".equals(ls_syly)) {
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.of(2020, 1, 1), LocalTime.of(0, 0, 1));
        drgbasy.setOpdate(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
      }

      if (hisba.size() > 0 || hisba_ys.size() > 0) {
        baSyjlService.deleteBaSyjlByBrbs(drgbasy.getBrbs());
        iBaSyjlExtService.deleteBaSyjlExtByBrbs(drgBaSyjlExt.getBrbs());
      }
      if (drgbasy.getCysj() != null) {
        if (drgbasy.getCysj().indexOf("年") > -1) {
          drgbasy.setCysj(formatChineseDate(drgbasy.getCysj()));
        }
      }

      if (drgbasy.getRysj() != null) {
        if (drgbasy.getRysj().indexOf("年") > -1) {
          drgbasy.setRysj(formatChineseDate(drgbasy.getRysj()));
        }
      }

      String new_jbbm = drgbasy.getJbdm();
      String new_ssbm = drgbasy.getSsjczbm1();

      if (hisba.size() > 0 || hisba_ys.size() > 0) {
        baSyjlService.insertBaSyjl(drgbasy);
        iBaSyjlExtService.insertBaSyjlExt(drgBaSyjlExt);
      }


      //执行分组
      Drgfz drgfz = new Drgfz();
      drgfz.setBrbs(drgbasy.getBrbs());
      DrgFzResult FzResult = drgFzqController.fzq(drgfz);

      //分组失败或分组结果未入组则检查新旧诊断是否一直，一致则使用旧分组
      if (FzResult == null || FzResult.getDrgbh() == null || FzResult.getDrgbh().contains("000") || FzResult.getDrgbh().contains("QY")) {
        if (Objects.equals(old_jbbm, new_jbbm) && Objects.equals(old_ssbm, new_ssbm) && StringUtils.isNotBlank(old_drgbh)) {
          logger.info("分组未成功入组，新旧诊断一致，使用旧的分组");
          FzResult = new DrgFzResult();
          FzResult.setDrgbh(old_drgbh);
          FzResult.setZfbz(old_zfbz);
        }
      }

      drgbasy = new BaSyjl();
      drgbasy.setBrbs(ls_brbs);
      drgbasy.setId(null);
      int li_rzflag = 1;

      if (FzResult != null && FzResult.getDrgbh() != null) {
        drgbasy.setDrgbh(FzResult.getDrgbh());
        drgbasy.setZfbz(FzResult.getZfbz());
        if (FzResult.getDrgbh().contains("000") || FzResult.getDrgbh().contains("QY")) {
          li_rzflag = 0;
        }
        drgbasy.setRzflag(li_rzflag);
        baSyjlService.updateBaSyjl(drgbasy);
        //更新cndrg
        if(FzResult.getCndrgbh()!=null) {
        	drgbasy.setCndrgbh(FzResult.getCndrgbh());
        	drgbasy.setZyzt("1");//借用这个字段
        	baSyjlService.updatebasyextcndrg(drgbasy);
        }
      }


      drgbasy = baSyjlService.selectBaSyjlByBrbs(ls_brbs);

      if (drgbasy == null) {
        return 1;
      }

      if (drgbasy.getJbdm() == null || "".equals(drgbasy.getJbdm())) {
        drgFzController.zybrPlfz(ls_brbs);
        drgbasy = baSyjlService.selectBaSyjlByBrbs(ls_brbs);
      }


      if ("1".equals(basy_doctor_sync) && "3".equals(drgbasy.getJlly())) {
        BaSyjlDoctor baSyjlDoctor = new BaSyjlDoctor();
        baSyjlDoctor.setBrbs(ls_brbs);
        List<BaSyjlDoctor> baSyjlDoctors = baSyjlDoctorService.selectBaSyjlDoctorList(baSyjlDoctor);
        if (baSyjlDoctors.size() == 0) {
          baSyjlDoctorService.insertBaSyjlDoctorByBaSyjl(drgbasy);
          baxyController.baxy(drgbasy, "");
        }
      }

      drgbasy.setUsername(companyName);
      baSyjlService.updateDrgzf(drgbasy);

    }

    if ("1".equals(update_anst)) {
      updateOprnAnst(hisba.isEmpty() ? hisba_ys : hisba);
    }
    return 1;
  }



  private void updateOprnAnst(List<Entity> list) {
    if (list.isEmpty()) {
      return;
    }
    Entity syjl = list.get(0);
    if (syjl == null) {
      return;
    }
    List<BaSsjl> oprnList = new ArrayList<>();
    List<BaBrzdxx> zdList = new ArrayList<>();
    String brid = syjl.getStr("brid");
    String zyid = syjl.getStr("zyid");



    for (int i = 1; i < 8; i++) {
      String bm = syjl.containsKey("ssjczbm" + i) ? syjl.getStr("ssjczbm" + i) : null;
      String mc = syjl.containsKey("ssjczmc" + i) ? syjl.getStr("ssjczmc" + i) : null;

      Date kssj = null;
      try {
        kssj = syjl.containsKey("mzkssj" + i) ? syjl.getDate("mzkssj" + i) : null;
      } catch (Exception e) {
      }

      Date jssj = null;
      try {
        jssj = syjl.containsKey("mzjssj" + i) ? syjl.getDate("mzjssj" + i) : null;
      } catch (Exception e) {
      }

      String fs = syjl.containsKey("mzfs" + i) ? syjl.getStr("mzfs" + i) : null;
      String ys = syjl.containsKey("mzys" + i) ? syjl.getStr("mzys" + i) : null;


      Date sskssj = null;
      try {
        sskssj = syjl.containsKey("sskssj" + i) ? syjl.getDate("sskssj" + i) : null;
      } catch (Exception e) {
      }

      Date ssjssj = null;
      try {
        ssjssj = syjl.containsKey("ssjssj" + i) ? syjl.getDate("ssjssj" + i) : null;
      } catch (Exception e) {
      }


      String zdys = syjl.containsKey("zdys" + i) ? syjl.getStr("zdys" + i) : null;
      String ssjb = syjl.containsKey("ssjb" + i) ? syjl.getStr("ssjb" + i) : null;

      Date ssrq = null;
      try {
        ssrq = syjl.containsKey("ssrq" + i) ? syjl.getDate("ssrq" + i) : null;
      } catch (Exception e) {
      }

      String zdysdm = syjl.containsKey("zdysdm" + i) ? syjl.getStr("zdysdm" + i) : null;
      String mzysdm = syjl.containsKey("mzysdm" + i) ? syjl.getStr("mzysdm" + i) : null;
      String jlr = syjl.containsKey("jlr" + i) ? syjl.getStr("jlr" + (i == 1 ? "" : i - 1)) : null;
      String jbbm = syjl.containsKey("jbbm" + i) ? syjl.getStr("jbbm" + (i == 1 ? "" : i - 1)) : null;
      BaBrzdxx zd = new BaBrzdxx();
      zd.setBrid(brid);
      zd.setZyid(zyid);
      zd.setJlr(jlr);
      zd.setZdcx(i);
      zd.setJbbm(jbbm);

      BaSsjl ssjl = new BaSsjl();
      ssjl.setBrid(brid);
      ssjl.setZyid(zyid);
      ssjl.setSsbm(bm);
      ssjl.setSsmc(mc);
      ssjl.setMzfs(fs);
      ssjl.setMzys(ys);
      ssjl.setMzjssj(jssj);
      ssjl.setMzkssj(kssj);
      ssjl.setSscx(i);
      ssjl.setSskssj(sskssj);
      ssjl.setSsjssj(ssjssj);
      ssjl.setSz(zdys);
      ssjl.setSsjb(ssjb);
      ssjl.setSsrq(ssrq);
      ssjl.setZdysdm(zdysdm);
      ssjl.setMzysdm(mzysdm);

      if ("5".equals(use_ybqd)) {
        ssjl.setMzfs(MzfsEnum.getStrCodeByDescription(fs));
      }

      oprnList.add(ssjl);
      zdList.add(zd);
      logger.info("----------------------");
      if (!StrUtil.isEmpty(zd.getJbbm())) {
        logger.info(zd.toString());
      }
      if (!StrUtil.isEmpty(ssjl.getSsbm())) {
        logger.info(ssjl.toString());
      }
      logger.info("----------------------");
      int updateRows = baSsjlService.updateAnstInfo(ssjl);
      int zdRows = baBrzdxxService.updateJlr(zd);
      System.out.println("手麻更新" + updateRows + " | 诊断更新" + zdRows);
//      System.out.println(updateRows + "调手麻记录");
    }

    System.out.println(new Gson().toJson(oprnList));

//    BaSsjl query = new BaSsjl();
//    query.setBrid(brid);
//    query.setZyid(zyid);
//    List<BaSsjl> ssjls = baSsjlService.selectBaSsjlList(query);
  }

  public void clearhisxxsqlMap() {
	  dataSyncService.clearhisxxsqlMap();
  }


  public static String extractChinese(String input) {
      // 正则表达式匹配所有中文字符（包括中文标点）
      Pattern pattern = Pattern.compile("[\u4e00-\u9fa5\uff01-\uff1f\uffe5\u3002\u3001\uFF0C\uFF1B\uFF1A\u201C\u201D\u2018\u2019\uFF08\uFF09\u3010\u3011\u2014\u2026]+");
      Matcher matcher = pattern.matcher(input);

      StringBuilder chineseBuilder = new StringBuilder();
      while (matcher.find()) {
          chineseBuilder.append(matcher.group());
      }

      return chineseBuilder.toString();
  }

  /**
   * 同步病历信息
   *
   * @param as_brid
   * @param as_zyid
   * @return
   */
  public Integer blxxSync(String as_brid, String as_zyid) {

    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }


    String ls_newflag = "";

    String ls_jzh = "";
    String ls_brid = "";
    String ls_zyid = "";
    Long ll_brid;
    Long ll_zyid;
    LocalDateTime ldt_max_kfbldate = null;
    LocalDateTime ldt_max_hisbldate = null;


    List<Entity> ids_zlhis_zybr;
    if (as_brid != null && !"".equals(as_brid)) {
      ids_zlhis_zybr = new ArrayList<>();
      //获取当前病人信息
      Brxx brxx = brxxService.selectBrxxByBridAndZyid(new Brxx(as_brid, as_zyid));
      if (brxx != null) {
        ls_jzh = brxx.getJzh();
      }
      if ("".equals(ls_jzh)) {
        ls_jzh = as_brid + "_" + as_zyid;
      }
      Entity entity = new Entity();
      entity.put("jzh", ls_jzh);
      entity.put("brid", as_brid);
      entity.put("zyid", as_zyid);
      entity.put("jgid", brxx != null ? brxx.getJgid() : null);
      ids_zlhis_zybr.add(0, entity);
    } else if("2".equals(blxx_sync_for_br)) {
      ids_zlhis_zybr = new ArrayList<>();
      List<Brxx> brxxList = brxxService.selectZyAndCyThreeDaysBrxx(new Brxx());
      for (Brxx br : brxxList) {
        Entity entity = new Entity();
        entity.put("jzh", br.getJzh());
        entity.put("brid", br.getBrid());
        entity.put("zyid", br.getZyid());
        entity.put("jgid", br.getJgid());
        ids_zlhis_zybr.add(entity);
      }
    } else {
      ids_zlhis_zybr = getPatData();
    }

//    System.out.println("--------------HIS系统住院病人信息-----------------");
//    ids_zlhis_zybr.forEach(entity -> System.out.println(entity));
//    System.out.println("------------------------------------------------");

    String ls_blnrxx = "";

    //源数据库病历信息
    List<Entity> ids_blxx_sou = new ArrayList<>();

    //SQL语句参数
    Map<String, Object> paramList = new HashMap<>();


    for (int i = 0; i < ids_zlhis_zybr.size(); i++) {
      ls_jzh = ids_zlhis_zybr.get(i).getStr("jzh");
      ls_brid = ids_zlhis_zybr.get(i).getStr("brid");
      ls_zyid = ids_zlhis_zybr.get(i).getStr("zyid");
      String jgid = ids_zlhis_zybr.get(i).getStr("jgid");

      //获取目标数据库病历表的最近更新时间ldt_max_kfbldate
      Blxx blxx = blxxService.selectMaxUpdateDate(new Blxx(ls_jzh));
      if (blxx != null) {
        ldt_max_kfbldate = formatDate(blxx.getUpdatedate());
      }

      System.out.println("目标数据库最近更新时间------------------------------" + ldt_max_kfbldate);
      System.out.println("HIS系统名称--------------------------------------" + is_hissoftname);

      if ("zlhis".equals(is_hissoftname)) {
        if ("1".equals(is_dj_emr)) {
          //获取源数据库最大时间ldt_max_hisbldate
          try {
            paramList = new HashMap<>();
            paramList.put("brid", ls_brid);
            paramList.put("zyid", ls_zyid);
            List<Entity> bl_max_cdate = new ArrayList<>();
            bl_max_cdate = dataSyncService.executeQuery(getSqlName("bl_max_zl_cdate",jgid), paramList);
            if (bl_max_cdate.size() > 0) {
              ldt_max_hisbldate = dateFormat(bl_max_cdate.get(0).getStr("createdate"));
            }
          } catch (Exception e) {
            ldt_max_hisbldate = LocalDateTime.of(2022,01,01,00,00,00);;
          }
        } else {
          try {
            paramList = new HashMap<>();
            paramList.put("brid", ls_brid);
            paramList.put("zyid", ls_zyid);
            List<Entity> bl_max_sdate = dataSyncService.executeQuery(getSqlName("bl_max_sdate",jgid), paramList);
            if (bl_max_sdate.size() > 0) {
              ldt_max_hisbldate = dateFormat(bl_max_sdate.get(0).getStr("savedate"));
            }
          } catch (Exception e) {
            ldt_max_hisbldate = LocalDateTime.of(2022,01,01,00,00,00);
          }
          System.out.println("01HIS系统保存时间-------------------------------------------" + ldt_max_hisbldate);
        }

        //若源数据库病历最大创建时间为空或与目标数据库数据一致，则进行下一轮循环
        if (ldt_max_hisbldate == null || ldt_max_hisbldate.compareTo(ldt_max_kfbldate) == 0) {
          continue;
        } else {
          ls_newflag = "1";
        }

        //获取当前病人的源数据库病历信息
        paramList = new HashMap<>();
        paramList.put("brid", ls_brid);
        paramList.put("zyid", ls_zyid);
        ids_blxx_sou = dataSyncService.executeQuery(getSqlName("blxx",jgid), paramList);

      } else if ("hzcy".equals(is_hissoftname)||"1".equals(trans_blxx_ty)) {
        try {
          paramList = new HashMap<>();
          paramList.put("brid", ls_brid);
          paramList.put("zyid", ls_zyid);
          List<Entity> bl_max_cdate = dataSyncService.executeQuery(getSqlName("bl_max_cdate",jgid), paramList);

          if (bl_max_cdate.size() > 0 && bl_max_cdate.get(0).getStr("createdate") != null) {
            ldt_max_hisbldate = dateFormat(bl_max_cdate.get(0).getStr("createdate"));
          }
        } catch (Exception e) {
          ldt_max_hisbldate = LocalDateTime.of(2022,01,01,00,00,00);
        }

        System.out.println("02HIS创建时间-------------------------------------------" + ldt_max_hisbldate);

        if (ldt_max_hisbldate == null || ldt_max_hisbldate.compareTo(ldt_max_kfbldate) == 0) {
          continue;
        } else {
          ls_newflag = "1";
        }
        //获取当前病人的源数据库病历信息
        paramList = new HashMap<>();
        paramList.put("brid", ls_brid);
        paramList.put("zyid", ls_zyid);
        ids_blxx_sou = dataSyncService.executeQuery(getSqlName("blxx",jgid), paramList);

      } else if ("zlbh".equals(is_hissoftname)&&"0".equals(trans_blxx_ty)) {
        try {
          paramList = new HashMap<>();
          paramList.put("brid", ls_brid);
          paramList.put("zyid", ls_zyid);
          List<Entity> bl_max_udate = dataSyncService.executeQuery(getSqlName("bl_max_udate",jgid), paramList);
          if (bl_max_udate.size() > 0) {
            ldt_max_hisbldate = dateFormat(bl_max_udate.get(0).getStr("updatedate"));
          }
        } catch (Exception e) {
          ldt_max_hisbldate = LocalDateTime.of(2022,01,01,00,00,00);
        }
        System.out.println("03HIS编辑时间-------------------------------------------" + ldt_max_hisbldate);

        if (ldt_max_hisbldate == null || ldt_max_hisbldate.compareTo(ldt_max_kfbldate) == 0) {
          continue;
        } else {
          ls_newflag = "1";
        }
        //获取当前病人的源数据库病历信息
        paramList = new HashMap<>();
        paramList.put("brid", ls_brid);
        paramList.put("zyid", ls_zyid);
        ids_blxx_sou = dataSyncService.executeQuery(getSqlName("blxx",jgid), paramList);
      } else {
        paramList = new HashMap<>();
        if ("1".equals(is_kxyy_flag)) {
          //获取当前病人的源数据库病历信息
          paramList.put("brid", ls_jzh);
          paramList.put("zyid", "");
          ids_blxx_sou = dataSyncService.executeQuery(getSqlName("blxx",jgid), paramList);
        } else {
          //获取当前病人的源数据库病历信息
          paramList.put("brid", ls_brid);
          paramList.put("zyid", ls_zyid);
          ids_blxx_sou = dataSyncService.executeQuery(getSqlName("blxx",jgid), paramList);
        }
      }

      //目标数据库病历信息
      List<Blxx> ids_blxx_des = new ArrayList<>();
      if (ids_blxx_sou.size() > 0) {

        ids_blxx_des = blxxService.selectBlxxList(new Blxx(ls_jzh));

        //获取目标数据库的病历信息
        if (ids_blxx_des.size() == 0) {
          ls_newflag = "1";
        }

        System.out.println("--------------------------HIS是否有新增病历:" + ls_newflag);

        if ("1".equals(ls_newflag)) {
          //删除目标数据库中当前病人的病历信息
          blxxService.deleteBlxxByJzh(new Blxx(ls_jzh));


          String ls_blmc = "";
          String lb_blnr = "";
          Double ldec_brid, ldec_zyid, ldec_blid;
          LocalDateTime ldt_createdate = null;


          ids_blxx_des.clear();
          for (int ll_ii = 0; ll_ii < ids_blxx_sou.size(); ll_ii++) {
            if (("zlhis".equals(is_hissoftname) || "hzcy".equals(is_hissoftname)||"1".equals(trans_blxx_ty)) && StrUtil.isNotBlank((ids_blxx_sou.get(ll_ii).getStr("blnr")))) {



              ls_blmc = ids_blxx_sou.get(ll_ii).getStr("blname");
              ls_blnrxx = ids_blxx_sou.get(ll_ii).getStr("blnr");
              if("1".equals(trans_blxx_ty)) {
            	  lb_blnr = ls_blnrxx;
              }
              ldt_createdate = dateFormat(ids_blxx_sou.get(ll_ii).getStr("createdate"));
              if ((ids_blxx_sou.get(ll_ii).getStr("blnr")).length() > 1000) {
                String ls_blnrxx1 = "";
                if ("zlhis".equals(is_hissoftname)) {
                	ldec_brid = Double.parseDouble(ls_brid);
                    ldec_zyid = Double.parseDouble(ls_zyid);
                    ldec_blid = ids_blxx_sou.get(ll_ii).getDouble("blid");
                  try {
                    List<Entity> get_blxx = new ArrayList<>();
                    if ("1".equals(is_dj_emr)) {
                      paramList = new HashMap<>();
                      paramList.put("brid", ls_brid);
                      paramList.put("zyid", ls_zyid);
                      paramList.put("blmc", ls_blmc);
                      paramList.put("createdate", formatLocalDateTime(ldt_createdate));
                      get_blxx = dataSyncService.executeQuery(getSqlName("zlhis_blnr_emr",jgid), paramList);
                    } else {
                      paramList = new HashMap<>();
                      paramList.put("brid", ldec_brid);
                      paramList.put("zyid", ldec_zyid);
                      paramList.put("blid", ldec_blid);
                      get_blxx = dataSyncService.executeQuery(getSqlName("zlhis_blnr",jgid), paramList);
                    }
                    if (get_blxx.size() > 0) {
                      lb_blnr = get_blxx.get(0).getStr("blnr");
                    }
                  } catch (Exception e) {
                    lb_blnr = null;
                  }
                }

                if ("hzcy".equals(is_hissoftname)) {
                	ldec_brid = Double.parseDouble(ls_brid);
                    ldec_zyid = Double.parseDouble(ls_zyid);
                    ldec_blid = ids_blxx_sou.get(ll_ii).getDouble("blid");
                  try {
                    paramList = new HashMap<>();
                    paramList.put("brid", ldec_brid);
                    paramList.put("zyid", ldec_zyid);
                    paramList.put("sno", ldec_blid);
                    List<Entity> get_blxx = dataSyncService.executeQuery(getSqlName("hzcy_blnr",jgid), paramList);
                    if (get_blxx.size() > 0) {
                      lb_blnr = get_blxx.get(0).getStr("blnr");
                    }
                  } catch (Exception e) {
                    lb_blnr = null;
                  }
                }

                if ("zlbh".equals(is_hissoftname) && StrUtil.isNotBlank(lb_blnr)) {
                	ls_blnrxx1 = lb_blnr.trim();
                    ls_blnrxx1 = extractChinese(lb_blnr.trim());
                    lb_blnr = ls_blnrxx1;
                }

                if (!"zlhis".equals(is_hissoftname) && StrUtil.isNotBlank(lb_blnr)) {
                  ls_blnrxx1 = lb_blnr.trim();
                }



                if (StrUtil.isNotBlank(ls_blnrxx1)) {
                  ls_blnrxx = ls_blnrxx1.trim();
                  ids_blxx_sou.get(ll_ii).put("blnr", ls_blnrxx.length() >= 2000 ? ls_blnrxx.substring(0, 2000) : ls_blnrxx);
                }
              }

              String ls_zs = "";
              String ls_xbs = "";
              String ls_jws = "";
              String ls_ryzd = "";
              String ls_zljg = "";
              String ls_cyzd = "";
              String ls_ss = "";

              if ((ls_blmc.indexOf("入院记录") > -1 || ls_blmc.indexOf("入院病程") > -1
                || ls_blmc.indexOf("入院病历") > -1 || ls_blmc.indexOf("日间手术") > -1)&&!"zlbh".equals(is_hissoftname)) {
                ls_zs = extractSubstring(ls_blnrxx, "主述", "现病史");
                if (ls_zs == null || "".equals(ls_zs)) {
                  ls_zs = extractSubstring(ls_blnrxx, "主诉", "现病史");
                }

                ls_xbs = extractSubstring(ls_blnrxx, "现病史", "既往史");
                ls_jws = extractSubstring(ls_blnrxx, "既往史", "个人史");
                ls_ryzd = extractSubstring(ls_blnrxx, "初步诊断", "医师签名");

                if (ls_ryzd != null && !"".equals(ls_ryzd)) {
                  Brxx brxx = new Brxx(ls_jzh);
                  brxx.setBlzs(ls_zs.replace(" ", "").replace("：", "")
                    .replace("&quot", ""));
                  brxx.setBlryzd(ls_ryzd.replace(" ", "").replace("：", "")
                    .replace("&quot", ""));
                  brxx.setBljws(ls_jws.replace(" ", "").replace("：", "")
                    .replace("&quot", ""));
                  brxx.setBlxbs(ls_xbs.replace(" ", "").replace("：", "")
                    .replace("&quot", ""));
                  brxxService.updateBrxx(brxx);
                }
              }


              if ((ls_blmc.indexOf("出院记录") > -1 || ls_blmc.indexOf("出院病程") > -1
                || ls_blmc.indexOf("出院病历") > -1 || ls_blmc.indexOf("日间手术") > -1
                || ls_blmc.indexOf("死亡") > -1)&&!"zlbh".equals(is_hissoftname)) {
                if (ls_zljg == null || "".equals(ls_zljg)) {
                  ls_zljg = extractSubstring(ls_blnrxx, "诊疗经过", "出院情况");
                }
                ls_cyzd = extractSubstring(ls_blnrxx, "出院诊断", "出院医嘱");

                if (ls_cyzd == null || "".equals(ls_cyzd)) {
                  ls_cyzd = extractSubstring(ls_blnrxx, "死亡诊断", "");
                }

                if (ls_cyzd == null || "".equals(ls_cyzd)) {
                  ls_cyzd = extractSubstring(ls_blnrxx, "出院诊断", "");
                }

                if (ls_cyzd != null && !"".equals(ls_cyzd)) {
                  Brxx brxx = new Brxx(ls_jzh);
                  brxx.setBlcyzd(ls_cyzd.replace(" ", "").replace("：", "")
                    .replace("&quot", ""));
                  brxx.setBlzljg(ls_zljg.replace(" ", "").replace("：", "")
                    .replace("&quot", ""));
                  brxxService.updateBrxx(brxx);
                }
              }


              if ((ls_blmc.indexOf("手术记录") > -1 || ls_blmc.indexOf("手术病程") > -1
                || ls_blmc.indexOf("手术病历") > -1 || ls_blmc.indexOf("日间手术") > -1)&&!"zlbh".equals(is_hissoftname)) {
                ls_ss = extractSubstring(ls_blnrxx, "手术名称", "术后诊断");
                if (ls_ss != null && !"".equals(ls_ss)) {
                  Brxx brxx = new Brxx();
                  brxx.setJzh(ls_jzh);
                  brxx.setBlssjl(ls_ss);
                  try {
                	  brxxService.updateBrxx(brxx);
				} catch (Exception e) {
					// TODO: handle exception
				}

                }
              }

            }

            if ("zlbh".equals(is_hissoftname)&&"0".equals(trans_blxx_ty)) {
              Blxx bl = new Blxx();
              bl.setBlnr(ids_blxx_sou.get(ll_ii).getStr("blnr"));
              bl.setJzh(ls_jzh);
              bl.setBrid(ls_brid);
              bl.setZyid(ls_zyid);
              bl.setBlname(ids_blxx_sou.get(ll_ii).getStr("blname"));
              bl.setDoctor(ids_blxx_sou.get(ll_ii).getStr("doctor"));
              if (isValidDate(ids_blxx_sou.get(ll_ii).getStr("createdate"), dateFormatter)) {
                try {
                  bl.setCreatedate(simpleDateFormat.parse(ids_blxx_sou.get(ll_ii).getStr("createdate")));
                } catch (Exception e) {
                  bl.setCreatedate(null);
                }
              }

              if (isValidDate(ids_blxx_sou.get(ll_ii).getStr("updatedate"), dateFormatter)) {
                try {
                  bl.setUpdatedate(simpleDateFormat.parse(ids_blxx_sou.get(ll_ii).getStr("updatedate")));
                } catch (Exception e) {
                  bl.setUpdatedate(null);
                }
              }
              ids_blxx_des.add(bl);
            } else {
              Blxx bl = new Blxx();
              bl.setBlnr(ids_blxx_sou.get(ll_ii).getStr("blnr"));
              bl.setJzh(ls_jzh);
              bl.setBrid(ls_brid);
              bl.setZyid(ls_zyid);
              bl.setBlname(ids_blxx_sou.get(ll_ii).getStr("blname"));
              bl.setDoctor(ids_blxx_sou.get(ll_ii).getStr("doctor"));

              if (isValidDate(ids_blxx_sou.get(ll_ii).getStr("createdate"), dateFormatter)) {
                try {
                  bl.setCreatedate(simpleDateFormat.parse(ids_blxx_sou.get(ll_ii).getStr("createdate")));
                } catch (Exception e) {
                  bl.setCreatedate(null);
                }
              }

              if (isValidDate(ids_blxx_sou.get(ll_ii).getStr("updatedate"), dateFormatter)) {
                try {
                  bl.setUpdatedate(simpleDateFormat.parse(ids_blxx_sou.get(ll_ii).getStr("updatedate")));
                } catch (Exception e) {
                  bl.setUpdatedate(null);
                }
              }

              ids_blxx_des.add(bl);
            }
          }

          ids_blxx_des.forEach(item -> blxxService.insertBlxx(item));

          if ("zlbh".equals(is_hissoftname)&&"0".equals(trans_blxx_ty)) {
            Long ldec_begin = null, ldec_end = null;

            Blxx bl = null;
            String ls_blryzd = "";
            String ls_blcyzd = "";
            bl = blxxService.selectRyBlidBegin(new Blxx(ls_jzh));
            if (bl != null) {
              ldec_begin = bl.getId();
            }
            bl = blxxService.selectRyBlidEnd(new Blxx(ls_jzh));
            if (bl != null) {
              ldec_end = bl.getId();
            }
            bl = blxxService.selectRyBlryzd(new Blxx(ls_jzh, ldec_begin, ldec_end));
            if (bl != null) {
              ls_blryzd = bl.getBlnr();
            }

            bl = blxxService.selectCyBlidBegin(new Blxx(ls_jzh));
            if (bl != null) {
              ldec_begin = bl.getId();
            }
            bl = blxxService.selectCyBlidEnd(new Blxx(ls_jzh));
            if (bl != null) {
              ldec_end = bl.getId();
            }
            bl = blxxService.selectCyBlcyzd(new Blxx(ls_jzh, ldec_begin, ldec_end));
            if (bl != null) {
              ls_blcyzd = bl.getBlnr();
            }

            ls_blryzd = strReplace(ls_blryzd, "  ", " ", false);
            ls_blcyzd = strReplace(ls_blcyzd, "  ", " ", false);

            Brxx brxx = new Brxx(ls_jzh);
            brxx.setBlryzd(ls_blryzd);
            brxx.setBlcyzd(ls_blcyzd);

            brxxService.updateBrxx(brxx);

          }
        }
      }

      YbjkOption ybjkOption = ybjkOptionService.selectYbjkOptionByCCode("trans_patient_mrInfo");
      if (ybjkOption != null && "1".equals(ybjkOption.getcValue())) {

        Map<String, Object> params = new HashMap<>();
        params.put("brid", ls_brid);
        params.put("zyid", ls_zyid);
        List<Entity> ids_blxx_brqk_sou = dataSyncService.executeQuery(getSqlName("blxx_brqk",jgid), params);
        if (ids_blxx_brqk_sou.size() > 0) {
          String ls_blryzd = ids_blxx_brqk_sou.get(0).getStr("blryzd");
          String ls_blcyzd = ids_blxx_brqk_sou.get(0).getStr("blcyzd");
          String ls_blzs = ids_blxx_brqk_sou.get(0).getStr("blzs");
          String ls_blxbs = ids_blxx_brqk_sou.get(0).getStr("blxbs");
          String ls_bljws = ids_blxx_brqk_sou.get(0).getStr("bljws");
          String ls_blzljg = ids_blxx_brqk_sou.get(0).getStr("blzljg");
          String ls_blssjl = ids_blxx_brqk_sou.get(0).getStr("blssjl");

          Brxx brxx = new Brxx(ls_jzh);
          brxx.setBlcyzd(ls_blcyzd);
          brxx.setBlryzd(ls_blryzd);
          brxx.setBlzs(ls_blzs);
          brxx.setBljws(ls_bljws);
          brxx.setBlxbs(ls_blxbs);
          brxx.setBlzljg(ls_blzljg);
          brxx.setBlssjl(ls_blssjl);
          brxxService.updateBrxx(brxx);
        }
      }
    }

    return 1;
  }


  /**
   * 同步费用信息
   *
   * @param as_brid
   * @param as_zyid
   * @return
   */
  public Integer fyxxSync(String as_brid, String as_zyid) {
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }
    //是否陈家桥
    if (is_patientDataSources==null) {
    	is_patientDataSources = ybjkOptionService.getOptionInfo("patient_data_sources","0");
    	if (is_patientDataSources==null) {
    		is_patientDataSources="0";
    	}
    }


    List<Entity> patList = new ArrayList<>();
    if (StringUtils.isNotBlank(as_brid)) {
      Brxx brxx = brxxService.selectBrxxByBridAndZyid(new Brxx(as_brid, as_zyid));
      String jzh;
      if (brxx != null) {
        jzh = brxx.getJzh();
      } else {
        jzh = as_brid + "_" + as_zyid;
      }
      Entity patItem = new Entity();
      patItem.put("jzh", jzh);
      patItem.put("brid", as_brid);
      patItem.put("zyid", as_zyid);
      patItem.put("jgid", brxx != null ? brxx.getJgid() : null);
      patItem.put("cydate", brxx != null ? brxx.getCydate() : null);
      patList.add(patItem);
    } else {
      patList = getPatData();
    }

    for (Entity patItem : patList) {
      String jzh = patItem.getStr("jzh");
      Date cydate;
      try {
        cydate = patItem.getDate("cydate");
      } catch (Exception e) {
        cydate = null;
      }

      boolean isCurYear = cydate == null || cydate.after(Date.from(LocalDate.now().minusMonths(Integer.parseInt(fyxx_ctrl_months)).atStartOfDay(ZoneId.systemDefault()).toInstant()));
      //陈家桥
      if ("1".equals(is_patientDataSources)) {
        iapiDataSyncService.syncFeeByJzh(jzh, isCurYear);
        continue;
      }

      List<Entity> feeListSou = new ArrayList<>();

      String brid = patItem.getStr("brid");
      String zyid = patItem.getStr("zyid");

      //获取最大操作日期
      Fyxx fyxx = new Fyxx();
      fyxx.setBrid(brid);
      fyxx.setZyid(zyid);
      LocalDateTime maxOpdate;
      if (isCurYear) {
         maxOpdate = formatDate(fyxxService.selectMaxOpdate(fyxx));
      } else {
        maxOpdate = formatDate(fyxxService.selectCyMaxOpdate(fyxx));
      }


      //获取入院日期
      LocalDateTime rydate = null;
      try {
        if (patItem.getDate("rydate") != null) {
          rydate = formatDate(patItem.getDate("rydate"));
        }
      } catch (Exception e) {
        rydate = null;
      }

      LocalDateTime minDate = LocalDateTime.of(2022,01,01,00,00,01);

      if (rydate != null) {
        //如果操作日期小于入院日期，取操作日期为入院日期的00:00:01
        if (maxOpdate.isBefore(rydate)) {
          maxOpdate = rydate.toLocalDate().atStartOfDay().plusSeconds(1);
        }

        //如果入院日期小于2022-01-01 00:00:01，取入院日期为60天前
        if (rydate.isBefore(minDate)) {
          rydate = LocalDateTime.now().minusDays(60);
        }
      }

      //如果操作日期小于2022-01-01 00:00:01，取操作日期为30天前
      if (maxOpdate.isBefore(minDate)) {
        maxOpdate = LocalDateTime.now().minusDays(30);
      }

      System.out.println("--------------------------当前费用信息最大操作日期:" + maxOpdate);
      System.out.println("--------------------------当前入院日期:" + rydate);

      //如果两边费用数量不同则重装所有费用
      if ("1".equals(syncAllFeeByNum)||"2".equals(syncAllFeeByNum)) {
        LocalTime currentTime = LocalTime.now();
        // 定义时间范围
        LocalTime startTime = LocalTime.of(18, 0, 0);
        LocalTime endTime = LocalTime.of(23, 48, 0);
        //为了避免删除时锁表，只有18:00:00到23:48:00在这个时间范围内才允许比较记录
        if ((currentTime.isAfter(startTime) && currentTime.isBefore(endTime))||"2".equals(syncAllFeeByNum)) {
          // 获取对方记录条数
          Map<String, Object> paramMap = new HashMap<>();
          paramMap.put("brid", brid);
          paramMap.put("zyid", zyid);
          paramMap.put("adtTo", formatLocalDateTime(maxOpdate));
          try {
            List<Entity> fyxxConditionCount = dataSyncService.executeQuery(getSqlName("fyxx_count", patItem.getStr("jgid")), paramMap);
            Integer cc = fyxxConditionCount.get(0).getInt("cc");
            // 获取我方记录条数
            int feeCount = fyxxService.selectFyxxCount(brid, zyid);
            // 若不相等则删除我方费用，设置最大时间为2000-01-01 00:00:00
            if (cc != null && cc != feeCount) {
              fyxxService.deleteFyxxByJzh(jzh);
              fyxxService.deleteFyxxcyByJzh(jzh);
              maxOpdate = LocalDateTime.of(2000, 01, 01, 01, 00, 00);
            }
          } catch (Exception e) {
            logger.error("对比记录数失败");
            logger.error(e.toString());
          }
        }
      }

      //获取病人费用信息
      if (maxOpdate != null) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("brid", brid);
        paramMap.put("opdate", formatLocalDateTime(maxOpdate));
        paramMap.put("zyid", zyid);
        feeListSou = dataSyncService.executeQuery(getSqlName("fyxx",patItem.getStr("jgid")), paramMap);
      }

      List<Fyxx> feeListTar = new ArrayList<>();
      if (feeListSou.size() > 0) {
        fyxxCopy(feeListSou, feeListTar);
        for (Fyxx fee : feeListTar) {
          fee.setJzh(jzh);
          fee.setBillno(patItem.getStr("jgid"));
        }

        if (feeListTar.size() > 0) {
          if ("重庆北部妇产医院".equals(companyName)) {
            fyxxService.deleteFeeRepeatHisid(feeListTar);
          }
          try {
            if(isCurYear) {
              fyxxService.syncFyxx(feeListTar);
            } else {
              fyxxService.syncHistoryFyxx(feeListTar);
            }
          } catch (Exception e) {
            fyxxService.deleteFeeRepeatHisid(feeListTar);
            if(isCurYear) {
              fyxxService.syncFyxx(feeListTar);
            } else {
              fyxxService.syncHistoryFyxx(feeListTar);
            }
          }
        }
      }

      if (rydate != null) {
        rydate = rydate.toLocalDate().atStartOfDay();
//        if ("zlbh".equals(is_hissoftname) && rydate != null) {
//          Fyxx fee = new Fyxx();
//          fee.setJzh(jzh);
//          fee.setFydate(formatLocalDateTime(rydate));
//          Integer feeCount = fyxxService.selectFeeCountByFydate(fyxx);
//          if (feeCount > 0) {
//            fyxxService.deleteFyxxByFyDate(fee);
//          }
//        }
      }

    }

    if (patList.size() > 1 && "1".equals(gs_havedrg_flag)) {
      fyxxService.updateZybrKmfy();
    }

    return 1;
  }

  /**
   * 同步费用信息
   *
   * @param as_brid
   * @param as_zyid
   * @return
   */
  public Integer mzfyxxSync(String as_jzh, String as_brid, String as_zyid,String as_jgid,String ls_jsdate) {
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }
    //是否陈家桥
    //是否陈家桥
    if (is_patientDataSources==null) {
    	is_patientDataSources = ybjkOptionService.getOptionInfo("patient_data_sources","0");
    	if (is_patientDataSources==null) {
    		is_patientDataSources="0";
    	}
    }
    List<Entity> patList = new ArrayList<>();
    if (StringUtils.isNotBlank(as_brid)) {
      Entity patItem = new Entity();
      patItem.put("jzh", as_jzh);
      patItem.put("brid", as_brid);
      patItem.put("zyid", as_zyid);
      patItem.put("jgid", as_jgid != null ? as_jgid : null);
      patList.add(patItem);
    } else {
      return 0;
    }



 // 定义日期格式
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    // 将字符串解析为LocalDate
    LocalDate date = LocalDate.parse(ls_jsdate, formatter);
    // 获取当天的开始时间（00:00:00）
    LocalDateTime ldt_startOfDay = date.atStartOfDay();
    // 获取当天的结束时间（23:59:59.999999999）
    LocalDateTime ldt_endOfDay = date.atTime(LocalTime.MAX);

    //获取最大操作日期
    Fyxx fyxx1 = new Fyxx();
    fyxx1.setBrid(as_brid);
    fyxx1.setZyid(as_zyid);
    LocalDateTime maxOpdate = formatDate(fyxxService.selectMaxmzOpdate(fyxx1));
    DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime targetDateTime = LocalDateTime.parse("2022-01-01 00:00:01", formatter1);
    if (!targetDateTime.equals(maxOpdate)) {
    	return 0;
    }


    for (Entity patItem : patList) {
      String jzh = patItem.getStr("jzh");

      //陈家桥
      if ("1".equals(is_patientDataSources)) {
        iapiDataSyncService.syncFeeByJzh(jzh);
        continue;
      }

      List<Entity> feeListSou = new ArrayList<>();

      String brid = patItem.getStr("brid");
      String zyid = patItem.getStr("zyid");



      //获取最大操作日期
      Fyxx fyxx = new Fyxx();
      fyxx.setBrid(brid);
      fyxx.setZyid(zyid);


	    Map<String, Object> paramMap = new HashMap<>();
	    paramMap.put("brid", brid);
	    paramMap.put("zyid", zyid);
//	    paramMap.put("adtFrom", formatLocalDateTime(ldt_startOfDay));
//	    paramMap.put("adtTo", formatLocalDateTime(ldt_endOfDay));
	    feeListSou = dataSyncService.executeQuery(getSqlName("mzfyxx",patItem.getStr("jgid")), paramMap);


      List<Fyxx> feeListTar = new ArrayList<>();
      if (feeListSou.size() > 0) {
        fyxxCopy(feeListSou, feeListTar);
        for (Fyxx fee : feeListTar) {
          fee.setJzh(jzh);
          fee.setZyid(as_zyid);
          fee.setBillno(patItem.getStr("jgid"));
        }

        if (feeListTar.size() > 0) {
          if ("重庆北部妇产医院".equals(companyName)) {
            fyxxService.deleteFeeRepeatHisid(feeListTar);
          }
          try {
            fyxxService.syncMzFyxx(feeListTar);
          } catch (Exception e) {
//            fyxxService.deleteFeeRepeatHisid(feeListTar);
//            fyxxService.syncFyxx(feeListTar);
          }
        }
      }
    }
    return 1;
  }

	// 定义一个方法，检查当前时间是否在 18:00:00 到 23:59:59 之间
    public  boolean isCurrentTimeInRange() {
        // 获取当前时间
        LocalTime currentTime = LocalTime.now();

        // 定义时间范围
        LocalTime startTime = LocalTime.of(18, 0, 0); // 18:00:00
        LocalTime endTime = LocalTime.of(23, 59, 59); // 23:59:59

        // 检查当前时间是否在范围内
        return !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
    }

  /**
   * 同步诊断信息
   *
   * @param as_brid
   * @param as_zyid
   * @return
   */
  public Integer zdxxSync(String as_brid, String as_zyid) {
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    //获取同步诊断的患者列表
    List<Entity> ids_zlhis_zybr = new ArrayList<>();
    if (StringUtils.isNotBlank(as_brid)) {
      Brxx brxx = brxxService.selectBrxxByBridAndZyid(new Brxx(as_brid, as_zyid));
      String ls_jzh = "";
      if (brxx != null) {
        ls_jzh = brxx.getJzh();
      }
      if ("".equals(ls_jzh)) {
        ls_jzh = as_brid + "_" + as_zyid;
      }
      Entity entity = new Entity();
      entity.set("jzh", ls_jzh);
      entity.set("brid", as_brid);
      entity.set("zyid", as_zyid);
      entity.set("jgid", brxx != null ? brxx.getJgid() : null);
      ids_zlhis_zybr.add(entity);
    } else {
      ids_zlhis_zybr = getPatData();
    }


    for (Entity zybrxx : ids_zlhis_zybr) {
      //从his获取诊断信息并检查是否存在更新的诊断
      String ls_newflag = "0";
      String ls_jzh = zybrxx.getStr("jzh");
      String ls_brid = zybrxx.getStr("brid");
      String ls_zyid = zybrxx.getStr("zyid");
      String ls_jgid = zybrxx.getStr("jgid");
      String ls_brbs = ls_brid + "_" + ls_zyid;

      Map<String, Object> paramList = new HashMap<>();
      paramList.put("brid", ls_brid);
      paramList.put("zyid", ls_zyid);
      List<Entity> ids_zdxx_sou = dataSyncService.executeQuery(getSqlName("zdxx",ls_jgid), paramList);
      ids_zdxx_sou.removeIf(zdxx -> zdxx == null || StringUtils.isBlank(zdxx.getStr("zdname")) || StringUtils.isBlank(zdxx.getStr("zdcode")));

      List<Zdxx> ids_zdxx_des;

      System.out.println("his诊断信息数量：" + ids_zdxx_sou.size());

      if (ids_zdxx_sou.size() > 0) {
        ids_zdxx_des = zdxxService.selectZdxxList(new Zdxx(ls_jzh));
        System.out.println("mysql诊断信息数量：" + ids_zdxx_des.size());
        ids_zdxx_des.removeIf(zdxx -> zdxx == null || StringUtils.isBlank(zdxx.getZdname()) || StringUtils.isBlank(zdxx.getZdcode()));


        if (ids_zdxx_des.size() > 0) {
          for (Entity zdxxS : ids_zdxx_sou) {
            String ls_zdname = gfNull(zdxxS.getStr("zdname"));
            if (ls_zdname.length() > 126) {
              ls_zdname = ls_zdname.substring(0, 126);
            }
            boolean hasDiag = false;   //检查当前系统是否存在his的这条诊断
            for (Zdxx zdxxD : ids_zdxx_des) {
              if (gfNull(zdxxD.getJlly()).equals(gfNull(zdxxS.getStr("jlly"))) &&
                gfNull(zdxxD.getZdname()).equals(ls_zdname) &&
                gfNull(zdxxD.getZdtype()).equals(gfNull(zdxxS.getStr("zdtype"))) &&
                zdxxD.getZdsort() == zdxxS.getInt("zdsort")) {
                hasDiag = true;
                break;
              }
            }
            if (!hasDiag) {
              ls_newflag = "1";
              break;
            }
          }
        } else {
          ls_newflag = "1";
        }

        System.out.println("当前患者是否包含新诊断：" + ls_newflag);

        //存在新的诊断则进行更新
        if ("1".equals(ls_newflag)) {
          zdxxService.deleteZdxxByJzh(ls_jzh);
          ids_zdxx_des = new ArrayList<>();
          zdxxCopy(ids_zdxx_sou, ids_zdxx_des);
          for (Zdxx zdxx : ids_zdxx_des) {
            zdxx.setJzh(ls_jzh);
            zdxx.setZdname(zdxx.getZdname().length() > 126 ? zdxx.getZdname().substring(0, 126) : zdxx.getZdname());
          }
          if ("重庆北部妇产医院".equals(companyName)) {
            ids_zdxx_des = updateZdxxSort(ids_zdxx_des);
          }
          ids_zdxx_des.forEach(item -> zdxxService.insertZdxx(item));
          List<Brxx> brxxList = brxxService.selectBrxxByJzh(ls_jzh);

          //是否进行审核
          if ("1".equals(is_zdsync_sfsh) && brxxList.size() > 0) {
            YbgkWgjl ybgkWgjl = new YbgkWgjl();
            ybgkWgjl.setJzh(ls_jzh);

            try {
            	 cyshController.cysh(ybgkWgjl);

               } catch (Exception e) {
               	 logger.error("出院审核失败：{}, {}, {}", e.getMessage(), ls_jzh);
               }
          }
          String ls_zyzt = brxxList.isEmpty() ? "" : brxxList.get(0).getZyzt();
          if ("1".equals(gs_havedrg_flag)) {
            BaBrzdxx baBrzdxx = new BaBrzdxx();
            baBrzdxx.setBrbs(ls_brbs);
            List<BaBrzdxx> baBrzdxxList = baBrzdxxService.selectBaBrzdxxList(baBrzdxx);
            if (("0".equals(ls_zyzt) && baBrzdxxList.size() == 0) || "1".equals(ls_zyzt)) {
              updateBrzdxx(ls_brid, ls_zyid);
            }
          }
        }
      }
    }
    return 1;
  }


  /**
   * 获取多HIS数据
   * @return
   */
  public List<Entity> getPatData() {

    String sqlName = "zybrxx";

    List<Entity> allData = new ArrayList<>();

    if ("1".equals(hasMultipleHis)) {

      boolean hasNullSource = false;

      //分医院获取
      for (SysTenant org : orgList) {
        if (StringUtils.isNotBlank(org.getSourcename())) {
          String dataSource = sqlName + "_" + org.getSourcename();

          logger.info("患者同步1：" + dataSource);

          List<Entity> data = dataSyncService.executeQuery(dataSource, new HashMap<>());
          allData.addAll(data);
        } else {
          hasNullSource = true;
        }
      }

      //如果有机构没有配置sourcename，则作为默认查询
      if (hasNullSource) {
        logger.info("患者同步2：" + sqlName);
        List<Entity> data = dataSyncService.executeQuery(sqlName, new HashMap<>());
        allData.addAll(data);
      }

    } else {

      logger.info("患者同步3：" + sqlName);
      allData = dataSyncService.executeQuery(sqlName, new HashMap<>());

    }

    logger.info("患者同步数量：" + allData.size());

    return allData;

  }


  /**
   * 同步病人信息
   *
   * @return
   */
  public Integer brxxSync()  {
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }


    //获取同步患者数据
    List<Entity> patList = getPatData();

    //同步，新增或更新
    for (Entity entity : patList) {
      String jzh = entity.getStr("jzh");
      if (StringUtils.isNotBlank(jzh)) {
        List<Brxx> brxxList = brxxService.selectBrxxList(new Brxx(jzh));
        Brxx brxx = brxxCopy(entity);
        try {
          if (brxx != null) {
            if (brxxList.size() == 0) {
              brxxService.insertBrxx(brxx);
            } else {
              brxx.setJzh(jzh);
              brxxService.updateBrxx(brxx);
            }
          }
        } catch (Exception e) {
          logger.error("插入或更新病人失败: {}_{}", brxx.getJzh(), brxx.getName());
          logger.error(e.getMessage());
        }
      }
    }

    //如果启用了drg，则进一步同步病案首页
    if ("1".equals(gs_havedrg_flag)) {
      String ls_brbs = "";
      String ls_zyid = "";
      String ls_brid = "";

      for (Entity entity : patList) {
        String jgid = entity.getStr("jgid");
        if ("flv5".equals(is_hissoftname) || "flv7".equals(is_hissoftname)) {
          ls_brbs = entity.getStr("brid");
          ls_zyid = "1";
          ls_brid = entity.getStr("brid");
        } else {
          ls_brbs = entity.getStr("brid") + "_" + entity.getStr("zyid");
          ls_zyid = entity.getStr("zyid");
          ls_brid = entity.getStr("brid");
        }
        List<BaSyjl> baSyjlList = baSyjlService.selectBaSyjlList(new BaSyjl(ls_brbs));
        if (baSyjlList.size() == 0) {
          //同步病案首页
          is_zbrxx_zbasy_flag = "1";

          try {
        	  basySync(ls_brbs,jgid);
			} catch (Exception e) {
				// TODO: handle exception
			}

          is_zbrxx_zbasy_flag = "0";
          BaSyjl baSyjl = baSyjlService.selectYsAndKsByBrbs(ls_brbs);
          //同步失败-未找到病案  从brxx同步病案
          if (baSyjl.getCount() == 0) {
            baSyjl = new BaSyjl(ls_brid, ls_zyid);
            baSyjl.setBrbs(ls_brbs);
            baSyjlService.insertBaSyjlByBrxx(baSyjl);
          }
        } else {
          //同步成功，从brxx更新病案
          BaSyjl baSyjl = new BaSyjl(ls_brid, ls_zyid);
          baSyjlService.updateBaSyjlByBrxx(baSyjl);
        }
      }


    }
    is_zbrxx_zbasy_flag = "0";

    return 1;
  }

  @Override
  public Integer autoYzxxSync() {
    List<Brxx> brxxList = brxxService.selectZyAndCyThreeDaysBrxx(new Brxx());
    for (Brxx br : brxxList) {
      yzxxSync(br.getBrid(), br.getZyid());
    }
    return 1;
  }

  /**
   * 复制病人信息
   *
   * @param entity
   * @return
   */
  public Brxx brxxCopy(Entity entity) {

    try {
      String ls_deptid, ls_doctorid;
      Brxx brxx = new Brxx();
      brxx.setBrtype(entity.getStr("brtype"));
      brxx.setJzh(entity.getStr("jzh"));
      brxx.setBrid(entity.getStr("brid"));
      brxx.setZyid(entity.getStr("zyid"));
      brxx.setZyh(entity.getStr("zyh"));
      brxx.setName(entity.getStr("name"));
      brxx.setBed(entity.getStr("bed"));
      brxx.setAge(entity.getStr("age"));
      if ("1".equals(entity.getStr("sex"))) {
        brxx.setSex("男");
      } else if ("2".equals(entity.getStr("sex"))) {
        brxx.setSex("女");
      } else {
//        System.out.println("--------------------"+entity.getStr("sex"));
        brxx.setSex(entity.getStr("sex"));
      }
      brxx.setTel(entity.getStr("tel"));
      brxx.setYbh(entity.getStr("ybh"));
      brxx.setJgid(entity.getStr("jgid"));

      if (isValidDate(entity.getStr("rydate"), dateFormatter)) {
        try {
          brxx.setRydate(simpleDateFormat.parse(entity.getStr("rydate")));
        } catch (Exception e) {
          brxx.setRydate(entity.getDate("rydate"));
        }
      }

      brxx.setDeptid(entity.getStr("deptid"));
      brxx.setDeptname(entity.getStr("deptname"));

      ls_deptid = brxx.getDeptid();
      if (ls_deptid != null && !"".equals(ls_deptid) && "".equals(brxx.getDeptname())) {
        SysDept sysDept = new SysDept();
        sysDept.setHisid(ls_deptid);
        List<SysDept> sysDepts = sysDeptService.selectDeptList(sysDept);
        if (sysDepts.size() > 0) {
          brxx.setDeptname(sysDepts.get(0).getDeptName());
        }
      }

      brxx.setDoctorid(entity.getStr("doctorid"));
      brxx.setDoctorname(entity.getStr("doctorname"));

      if (isValidDate(entity.getStr("cydate"), dateFormatter)) {
        try {
          brxx.setCydate(simpleDateFormat.parse(entity.getStr("cydate")));
        } catch (Exception e) {
          brxx.setCydate(entity.getDate("cydate"));
        }
      }

      if (isValidDate(entity.getStr("qddate"), dateFormatter)) {
          try {
            brxx.setQddate(simpleDateFormat.parse(entity.getStr("qddate")));
          } catch (Exception e) {
            brxx.setQddate(entity.getDate("qddate"));
          }
        }

      if (isValidDate(entity.getStr("gddate"), dateFormatter)) {
          try {
            brxx.setGddate(simpleDateFormat.parse(entity.getStr("gddate")));
          } catch (Exception e) {
            brxx.setGddate(entity.getDate("gddate"));
          }
        }

      ls_doctorid = brxx.getDoctorid();
      if (ls_doctorid != null && !"".equals(ls_doctorid) && "".equals(brxx.getDoctorname())) {
        SysUser sysUser = new SysUser();
        sysUser.setHisid(ls_doctorid);
        List<SysUser> sysUsers = sysUserService.selectUserList(sysUser);
        if (sysUsers.size() > 0) {
          brxx.setDoctorname(sysUsers.get(0).getUserName());
        }
      }

      brxx.setBzcode(entity.getStr("bzcode"));
      brxx.setBzname(entity.getStr("bzname"));
      brxx.setZyzt(entity.getStr("zyzt"));
      brxx.setCblb(entity.getStr("cblb"));
      brxx.setYbdjlsh(entity.getStr("ybdjlsh"));
      return brxx;
//      ids_brxx_des.add(brxx);
//      brxxService.insertBrxx(brxx);
    } catch (IllegalArgumentException e) {
//      System.out.println("当前字段类型不匹配：" + e.getMessage());
      logger.info("当前字段类型不匹配：" + e.getMessage());
    }
    return null;
  }


  /**
   * 更新诊断信息
   *
   * @param ls_brid
   * @param ls_zyid
   * @return
   */
  public Integer updateBrzdxx(String ls_brid, String ls_zyid) {
//    String ls_jlly = "";
    zdxxService.updateBrzdxx(new Zdxx(ls_brid, ls_zyid));
//    baBrzdxxSyService.deleteBaBrzdxxSyById(new BaBrzdxx(ls_brid, ls_zyid));
//    baBrzdxxService.deleteBaBrzdxxById(new BaBrzdxx(ls_brid, ls_zyid));
//
////    List<Zdxx> zdxxJlly = zdxxService.selectJlly(new Zdxx(ls_brid, ls_zyid));
////    if (zdxxJlly.size() > 0){
////      ls_jlly = zdxxJlly.get(0).getJlly();
////    }
////    zdxx.setJlly(ls_jlly);
//
//    baBrzdxxSyService.insertBaBrzdxxSyBySyZd(new BaBrzdxxSy(ls_brid, ls_zyid));
//    baBrzdxxService.insertBaBrzdxxBySyzd(new BaBrzdxx(ls_brid, ls_zyid));

    return 1;
  }

  /**
   * 提取数据
   *
   * @param asSource 源数据
   * @param asBef    开始部分
   * @param asAft    结束部分
   * @return
   */
  public String extractSubstring(String asSource, String asBef, String asAft) {
    String lsRtn = "";
    String lsBefLower = asBef.toLowerCase();
    String lsAftLower = asAft.toLowerCase();
    String lsSourceLower = asSource.toLowerCase();

    int llPos1 = lsSourceLower.indexOf(lsBefLower);

    if (llPos1 > -1) {
      String lsBefAfter = lsSourceLower.substring(llPos1 + lsBefLower.length());
      if (!"".equals(gfNull(lsAftLower))) {
        int llPos2 = lsBefAfter.indexOf(lsAftLower);
        if (llPos2 > -1) {
          lsRtn = lsBefAfter.substring(0, llPos2);
        } else {
          lsRtn = lsBefAfter;
        }
      } else {
        lsRtn = lsBefAfter;
      }
    }

    if (lsRtn.length() > 0 && (lsRtn.charAt(0) == ':' || lsRtn.charAt(0) == '：')) {
      lsRtn = lsRtn.substring(1);
    }

    return lsRtn;
  }

  /**
   * 字符串字符替换
   *
   * @param as_source
   * @param as_old
   * @param as_new
   * @param ab_ignorecase
   * @return
   */
  public String strReplace(String as_source, String as_old, String as_new, Boolean ab_ignorecase) {
    if (as_source == null || as_old == null || as_new == null || ab_ignorecase == null) {
      return null;
    }

    String ls_Source;
    if (ab_ignorecase) {
      as_old = as_old.toLowerCase();
      ls_Source = as_source.toLowerCase();
    } else {
      ls_Source = as_source;
    }

    as_source = ls_Source.replaceAll(as_old, as_new);

    return as_source;
  }


  /**
   * 数据复制
   *
   * @param instance
   * @param target
   * @param fieldNames
   * @param entity
   * @return
   */
  public Object dataCopy(Object instance, Class<? extends Object> target, Set<String> fieldNames, Entity entity) {
    try {
      instance = target.newInstance();
      for (String fieldName : fieldNames) {
        try {
          if (!"id".equals(fieldName) && entity.get(fieldName) != null && !"hospid".equals(fieldName)) {
            String removeUnderlineFieldName = removeUnderline(fieldName);
            Field field = null;
            field = target.getDeclaredField(removeUnderlineFieldName);
            field.setAccessible(true);
            field.set(instance, typeDecide(field, entity, fieldName));
          }
        } catch (NoSuchFieldException e) {
          logger.info("字段未找到：" + e.getMessage());
//          System.out.println("字段未找到：" + e.getMessage());
        }
      }
    } catch (InstantiationException e) {
      throw new RuntimeException(e);
    } catch (IllegalAccessException e) {
      logger.info("当前字段类型不匹配：" + e.getMessage());
//      System.out.println("当前字段类型不匹配：" + e.getMessage());
    }
    return instance;
  }

  /**
   * 复制病案首页信息
   *
   * @param his
   * @param drgbasy
   * @return
   */
  public BaSyjl basyCopy(List<Entity> his, BaSyjl drgbasy) {
    Entity source = his.get(0);
    Set<String> fieldNames = source.getFieldNames();
    if (drgbasy == null) {
      drgbasy = new BaSyjl();
    }
    Class<? extends BaSyjl> target = drgbasy.getClass();
    BaSyjl baSyjl = null;

    Object dataCopy = dataCopy(baSyjl, target, fieldNames, source);
    BeanUtils.copyProperties((BaSyjl) dataCopy, drgbasy);
    return drgbasy;
  }

  /**
   * 复制病案首页扩展信息
   * @param his
   * @param baSyjlExt
   * @return
   */
  public BaSyjlExt basyExtCopy(List<Entity> his, BaSyjlExt baSyjlExt) {
    Entity source = his.get(0);
    Set<String> fieldNames = source.getFieldNames();
    if (baSyjlExt == null) {
      baSyjlExt = new BaSyjlExt();
    }
    Class<? extends BaSyjlExt> target = baSyjlExt.getClass();
    BaSyjlExt baSyjlExtNew = null;

    Object dataCopy = dataCopy(baSyjlExtNew, target, fieldNames, source);
    BeanUtils.copyProperties((BaSyjlExt) dataCopy, baSyjlExt);
    return baSyjlExt;
  }


  /**
   * 赋值手术信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  public Integer ssxxCopy(List<Entity> sourceList, List<BaSsjlTjzd> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends BaSsjlTjzd> target = BaSsjlTjzd.class;
      BaSsjlTjzd instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((BaSsjlTjzd) dataCopy);
    }
    return 1;
  }

  /**
   * 复制费用信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  public Integer fyxxCopy(List<Entity> sourceList, List<Fyxx> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends Fyxx> target = Fyxx.class;
      Fyxx instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((Fyxx) dataCopy);
    }
    return 1;
  }

  /**
   * 复制医嘱信息
   * @param sourceList
   * @param targetList
   */
  public void yzxxCopy(List<Entity> sourceList, List<Yzxx> targetList) {
    for (Entity entity : sourceList) {
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends Yzxx> yzxxClass = Yzxx.class;
      Yzxx instance = null;
      Object dataCopy = dataCopy(instance, yzxxClass, fieldNames, entity);
      targetList.add((Yzxx) dataCopy);
    }
  }


  /**
   * 复制诊断信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  public Integer zdxxCopy(List<Entity> sourceList, List<Zdxx> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends Zdxx> target = Zdxx.class;
      Zdxx instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((Zdxx) dataCopy);
    }
    return 1;
  }


  /**
   * 日期格式化
   *
   * @param date
   * @return
   */
  public LocalDateTime dateFormat(String date) {
    if (date == null || "".equals(date)) {
      return null;
    }
    if (date.length() > 19) {
      date = date.substring(0, 19);
    }


    date = date.replace("/", "-").replace(".", "-");

    date = date.replaceAll("(?<=-)(\\d)(?=\\D)", "0$1");


    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    return LocalDateTime.parse(date, formatter);
  }


  /**
   * 去除成员变量下划线
   *
   * @param sourceData
   * @return
   */
  public String removeUnderline(String sourceData) {
    if (sourceData.indexOf("_") > -1) {
      String[] parts = sourceData.split("_");
      StringBuilder result = new StringBuilder(parts[0]);
      for (int i = 1; i < parts.length; i++) {
        result.append(Character.toUpperCase(parts[i].charAt(0))).append(parts[i].substring(1));
      }
      return result.toString();
    } else {
      return sourceData;
    }
  }

  /**
   * 成员变量类型判断
   *
   * @param field
   * @param entity
   * @param fieldName
   * @return
   */
  public Object typeDecide(Field field, Entity entity, String fieldName) {
    try {
      if (field.getType() == int.class || field.getType() == Integer.class) {
        return entity.getInt(fieldName);
      } else if (field.getType() == Long.class) {
        return entity.getLong(fieldName);
      } else if (field.getType() == BigDecimal.class) {
        return entity.getBigDecimal(fieldName);
      } else if (field.getType() == Date.class) {
        try {
          return entity.getDate(fieldName);
        } catch (Exception e) {
          String str = entity.getStr(fieldName);
          if (StringUtils.isNotBlank(str)) {
            return DateTools.stringToDate(str);
          } else {
            return null;
          }
        }
      } else {
        String str = entity.getStr(fieldName);
        if (str == null || "".equals(str.trim()) || "-".equals(str.trim())) {
          return null;
        }
        return str.trim();
      }
    } catch (Exception e) {
      logger.error(e.getMessage());
      return null;
    }
  }


  /**
   * 检验信息同步
   * @return
   */
  public Integer jyxxSync() {
    String sqlName = "jyxx";
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }
    if ("1".equals(hasMultipleHis)) {

      LocalDateTime minReportDate = null;

      //分别为每个医院同步检验数据，起始时间为当前医院的最大时间
      for (SysTenant org : orgList) {
        if (StringUtils.isNotBlank(org.getSourcename())) {

          String dataSource = sqlName + "_" + org.getSourcename();
          LocalDateTime startDate = jyxxService.selectMaxReportDate(org.getTenantId());

          logger.info("检验同步1：" + dataSource);
          logger.info(org.getTenantName() +  "，当前最大报告时间：" + startDate);

          jyxxSyncOrg(dataSource,startDate);

        } else {

          //如果有医院未配置sourcename，则取默认查询，如果有多个医院未配置，则取最大时间中最小的
          if (minReportDate == null) {
            minReportDate = jyxxService.selectMaxReportDate(org.getTenantId());
          } else {
            LocalDateTime dateTime = jyxxService.selectMaxReportDate(org.getTenantId());
            if (minReportDate.isAfter(dateTime)) {
              minReportDate = dateTime;
            }
          }

        }
      }

      if (minReportDate != null) {
        logger.info("检验同步2：" + sqlName);
        logger.info("其余未配置医院中最小的最大报告时间：" + minReportDate);
        return jyxxSyncOrg(sqlName,minReportDate);
      }

      return 1;
    } else {

      //未启动多HIS，正常同步
      LocalDateTime minStartDate = null;
      logger.info("检验同步3：" + sqlName);

      //多租户
      if ("1".equals(hasMultipleOrg)&&"0".equals(is_tb_bqfjg)) {
        for (SysTenant org : orgList) {
          LocalDateTime maxReportDate = jyxxService.selectMaxReportDate(org.getTenantId());

          if (minStartDate == null) {
            minStartDate = maxReportDate;
          } else {
            if (minStartDate.isAfter(maxReportDate)) {
              minStartDate = maxReportDate;
            }
          }
        }
        logger.info("单HIS多租户中所有医院最小的最大报告时间：" + minStartDate);
      } else {
        logger.info("单HIS单租户中的最大报告时间：" + minStartDate);
        minStartDate = jyxxService.selectMaxReportDate(null);
      }
      jyxxSyncOrg(sqlName,minStartDate);

      return 1;
    }
  }


  /**
   * 多HIS同步检验信息
   *
   * @return
   */
  public Integer jyxxSyncOrg(String sqlName, LocalDateTime  startDate) {

    //根据配置的sqlName获取检查每次同步的间隔天数--days
    Hisxxsql his = new Hisxxsql();
    his.setName(sqlName);
    his.setHisSoft(is_hissoftname);
    Hisxxsql hisxxsql = hisxxsqlService.selectHisxxByNameAndHis(his);

    if (hisxxsql == null || StringUtils.isBlank(hisxxsql.getSqlstr())) {
      throw new ServiceException(sqlName + "配置不存在");
    }

    int days = Math.toIntExact(hisxxsql.getDays());

    List<Entity> jyxxHis = new ArrayList<>();
    List<Jyxx> jyxxTar = new ArrayList<>();

    //第一次同步的起始事件
    LocalDateTime ldt_from = startDate;
    LocalDateTime ldt_to = startDate.plusDays(days);

    LocalDateTime ldt_today = getServerTime();
    if (ldt_to.compareTo(ldt_today) > 0) {
      ldt_to = ldt_today;
    }

    int li_num = 0;

    Map<String, Object> paramList = new HashMap<>();

    while (ldt_from.compareTo(ldt_today) < 0 && li_num < 365) {

      li_num++;
      paramList.clear();
      jyxxTar.clear();
      jyxxHis.clear();

      paramList.put("adtFrom", formatLocalDateTime(ldt_from));
      paramList.put("adtTo", formatLocalDateTime(ldt_to));

      jyxxHis = dataSyncService.executeQuery(sqlName, paramList);
      if (jyxxHis.size() > 0) {
        li_num = li_num + 10;
        jyxxCopy(jyxxHis, jyxxTar);
      }

      jyxxTar.forEach(item -> jyxxService.insertJyxx(item));

      ldt_from = ldt_to;
      ldt_to = ldt_to.plusDays(days);
      if (ldt_to.compareTo(ldt_today) > 0) {
        ldt_to = ldt_today;
      }
    }

    return 1;
  }


  /**
   * 检查信息同步
   * @return
   */
  public Integer jcxxSync() {
    String sqlName = "jcxx";
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }
    if ("1".equals(hasMultipleHis)) {

      LocalDateTime minReportDate = null;
      //分别为每个医院同步检验数据，起始时间为当前医院的最大时间
      for (SysTenant org : orgList) {
        if (StringUtils.isNotBlank(org.getSourcename())) {

          String dataSource = sqlName + "_" + org.getSourcename();
          LocalDateTime startDate = jcxxService.selectMaxReportDate(org.getTenantId());

          logger.info("检查同步1：" + dataSource);
          logger.info(org.getTenantName() +  "，当前最大报告时间：" + startDate);

          jcxxSyncOrg(dataSource,startDate);

        } else {
          //如果有医院未配置sourcename，则取默认查询，如果有多个医院未配置，则取最大时间中最小的
          if (minReportDate == null) {
            minReportDate = jcxxService.selectMaxReportDate(org.getTenantId());
          } else {
            LocalDateTime dateTime = jcxxService.selectMaxReportDate(org.getTenantId());
            if (minReportDate.isAfter(dateTime)) {
              minReportDate = dateTime;
            }
          }

        }
      }

      if (minReportDate != null) {
        logger.info("检查同步2：" + sqlName);
        logger.info("其余未配置医院中最小的最大报告时间：" + minReportDate);
        return jcxxSyncOrg(sqlName, minReportDate);
      }


      return 1;
    } else {

      //未启动多HIS，正常同步
      LocalDateTime minStartDate = null;
      logger.info("检查同步3：" + sqlName);

      //多租户
      if ("1".equals(hasMultipleOrg)&&"0".equals(is_tb_bqfjg)) {
        for (SysTenant org : orgList) {
          LocalDateTime maxReportDate = jcxxService.selectMaxReportDate(org.getTenantId());

          if (minStartDate == null) {
            minStartDate = maxReportDate;
          } else {
            if (minStartDate.isAfter(maxReportDate)) {
              minStartDate = maxReportDate;
            }
          }
        }
        logger.info("单HIS多租户中所有医院最小的最大报告时间：" + minStartDate);
      } else {
        logger.info("单HIS单租户中的最大报告时间：" + minStartDate);
        minStartDate = jcxxService.selectMaxReportDate(null);
      }
      jcxxSyncOrg(sqlName,minStartDate);

      return 1;
    }
  }


  /**
   * 同步检查信息
   *
   * @return
   */
  public Integer jcxxSyncOrg(String sqlName,LocalDateTime startDate) {

    Hisxxsql his = new Hisxxsql();
    his.setName(sqlName);
    his.setHisSoft(is_hissoftname);
    Hisxxsql hisxxsql = hisxxsqlService.selectHisxxByNameAndHis(his);

    if (hisxxsql == null || StringUtils.isBlank(hisxxsql.getSqlstr())) {
      throw new ServiceException(sqlName + "配置不存在");
    }

    int ll_days = Math.toIntExact(hisxxsql.getDays());

    List<Entity> jcxxHis = new ArrayList<>();
    List<Jcxx> jcxxTar = new ArrayList<>();

    LocalDateTime ldt_from = startDate;
    LocalDateTime ldt_to = startDate.plusDays(ll_days);

    System.out.println("--------------------------ldt_from:" + ldt_from);
    System.out.println("--------------------------ldt_to:" + ldt_to);

    LocalDateTime ldt_today = getServerTime();
    if (ldt_to.compareTo(ldt_today) > 0) {
      ldt_to = ldt_today;
    }

    int li_num = 0;

    Map<String, Object> paramList = new HashMap<>();
    while (ldt_from.compareTo(ldt_today) < 0  && li_num < 365) {

      li_num++;
      paramList.clear();
      jcxxTar.clear();
      jcxxHis.clear();

      paramList.put("adtFrom", formatLocalDateTime(ldt_from));
      paramList.put("adtTo", formatLocalDateTime(ldt_to));
      jcxxHis = dataSyncService.executeQuery(sqlName, paramList);

      if (jcxxHis.size() > 0) {
        li_num = li_num + 10;
        jcxxCopy(jcxxHis, jcxxTar);
      }

      jcxxTar.forEach(item -> jcxxService.insertJcxx(item));

      ldt_from = ldt_to;
      ldt_to = ldt_to.plusDays(ll_days);
      if (ldt_to.compareTo(ldt_today) > 0) {
        ldt_to = ldt_today;
      }
    }

    return 1;
  }



  /**
   * 结算信息同步
   * @return
   */
  public Integer jsxxSync() {
    String sqlName = "his_jsxx";

    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    LocalDateTime minStartDate = null;

    if ("1".equals(hasMultipleHis)) {

      LocalDateTime minJsdate = null;

      //分别为每个医院同步结算数据，起始时间为当前医院的最大时间
      for (SysTenant org : orgList) {
        LocalDateTime maxJsdate = jsxxHisService.selectHisJsdate(org.getTenantId());
        if (StringUtils.isNotBlank(org.getSourcename())) {

          String dataSource = sqlName + "_" + org.getSourcename();

          logger.info("结算同步1：" + dataSource);
          logger.info(org.getTenantName() +  "，当前最大结算时间：" + maxJsdate);

          jsxxSyncOrg(dataSource,maxJsdate);

        } else {

          if (minJsdate == null) {
            minJsdate = maxJsdate;
          } else {
            if (minJsdate.isAfter(maxJsdate)) {
              minJsdate = maxJsdate;
            }
          }

        }

        if (minStartDate == null) {
          minStartDate = maxJsdate;
        } else {
          if (minStartDate.isAfter(maxJsdate)) {
            minStartDate = maxJsdate;
          }
        }
      }

      if (minJsdate != null) {
        logger.info("结算同步2：" + sqlName);
        logger.info("其余未配置医院中最小的最大结算时间：" + minJsdate);
        jsxxSyncOrg(sqlName, minJsdate);
      }


    } else {
      //未启动多HIS，正常同步
      logger.info("结算同步3：" + sqlName);
      if ("1".equals(hasMultipleOrg)&&"0".equals(is_tb_bqfjg)) {
        for (SysTenant org : orgList) {
          LocalDateTime maxJsdate = jsxxHisService.selectHisJsdate(org.getTenantId());

          if (minStartDate == null) {
            minStartDate = maxJsdate;
          } else {
            if (minStartDate.isAfter(maxJsdate)) {
              minStartDate = maxJsdate;
            }
          }
        }
        logger.info("单HIS多租户中所有医院最小的最大结算时间：" + minStartDate);
      } else {
        logger.info("单HIS单租户中的最大结算时间：" + minStartDate);
        minStartDate = jsxxHisService.selectHisJsdate(null);
      }
      jsxxSyncOrg(sqlName,minStartDate);
    }


    JsxxHis jsxxHis1 = new JsxxHis();
    jsxxHis1.setHisJsdate(Date.from(minStartDate.atZone(ZoneId.systemDefault()).toInstant()));
    jsxxHisService.updateJsxxHis1(jsxxHis1);
    jsxxHisService.updateJsxxHis2(jsxxHis1);
    try {
      jsxxService.insertJsxxByHis();
    } catch (Exception e) {
    }

    return 1;
  }


  /**
   * 门诊费用信息同步批量
   * @return
   */
  public Integer mzfyxxplSync() {
    String sqlName = "mzfyxxpl";

    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    LocalDateTime minStartDate = null;

    if ("1".equals(hasMultipleHis)) {

      LocalDateTime minJsdate = null;

      //分别为每个医院同步结算数据，起始时间为当前医院的最大时间
      for (SysTenant org : orgList) {
        LocalDateTime maxJsdate = fyxxService.selectMaxopdatemzfyxx(org.getTenantId());
        if (StringUtils.isNotBlank(org.getSourcename())) {

          String dataSource = sqlName + "_" + org.getSourcename();

          logger.info("门诊费用同步1：" + dataSource);
          logger.info(org.getTenantName() +  "，当前最大门诊费用时间：" + maxJsdate);

          mzfyxxSyncOrgpl(dataSource,maxJsdate);

        } else {

          if (minJsdate == null) {
            minJsdate = maxJsdate;
          } else {
            if (minJsdate.isAfter(maxJsdate)) {
              minJsdate = maxJsdate;
            }
          }

        }

        if (minStartDate == null) {
          minStartDate = maxJsdate;
        } else {
          if (minStartDate.isAfter(maxJsdate)) {
            minStartDate = maxJsdate;
          }
        }
      }

      if (minJsdate != null) {
        logger.info("门诊费用同步2：" + sqlName);
        logger.info("其余未配置医院中最小的最大费用时间：" + minJsdate);
        mzfyxxSyncOrgpl(sqlName, minJsdate);
      }


    } else {
      //未启动多HIS，正常同步
      logger.info("门诊费用同步3：" + sqlName);
      if ("1".equals(hasMultipleOrg)&&"0".equals(is_tb_bqfjg)) {
        for (SysTenant org : orgList) {
         //门诊只转为2的
          if (org.getUseflag()!=2) {
        	  continue;
          }
          LocalDateTime maxJsdate = fyxxService.selectMaxopdatemzfyxx(org.getTenantId());
//          System.out.println(org.getTenantId()+"---"+maxJsdate);
          if (minStartDate == null) {
            minStartDate = maxJsdate;
          } else {
            if (minStartDate.isAfter(maxJsdate)) {
              minStartDate = maxJsdate;
            }
          }
        }
        logger.info("单HIS多租户中所有医院最小的门诊操作时间：" + minStartDate);
        //如果门诊都没有
        if(minStartDate==null) {
        	return 0;
        }
      } else {
        logger.info("单HIS单租户中的最大门诊操作时间：" + minStartDate);
        minStartDate = fyxxService.selectMaxopdatemzfyxx(null);
      }

      mzfyxxSyncOrgpl(sqlName,minStartDate);
    }
    return 1;
  }


  /**
   * 结算信息同步
   * @return
   */
  public Integer jsxxSyncOrg(String sqlName,LocalDateTime startDate) {

    Hisxxsql his = new Hisxxsql();
    his.setName(sqlName);
    his.setHisSoft(is_hissoftname);
    Hisxxsql hisxxsql = hisxxsqlService.selectHisxxByNameAndHis(his);

    if (hisxxsql == null || StringUtils.isBlank(hisxxsql.getSqlstr())) {
      throw new ServiceException(sqlName + "配置不存在");
    }

    int days = Math.toIntExact(hisxxsql.getDays());

    List<Entity> jsxxHis = new ArrayList<>();
    List<JsxxHis> jsxxTar = new ArrayList<>();


    LocalDateTime ldt_from = startDate;
    LocalDateTime ldt_to = startDate.plusDays(days);

    LocalDateTime ldt_today = getServerTime();
    if (ldt_to.compareTo(ldt_today) > 0) {
      ldt_to = ldt_today;
    }


    int li_num = 0;

    Map<String, Object> paramList = new HashMap<>();
    while (ldt_from.compareTo(ldt_today) < 0 && li_num < 600) {
      li_num++;

      jsxxHis.clear();
      jsxxTar.clear();
      paramList.clear();

      paramList.put("adtFrom", formatLocalDateTime(ldt_from));
      paramList.put("adtTo", formatLocalDateTime(ldt_to));

      //源数据库结算数据
      jsxxHis = dataSyncService.executeQuery(sqlName, paramList);


      if (jsxxHis.size() > 0) {
        li_num = li_num + 10;
        jsxxCopy(jsxxHis, jsxxTar);
      }

      if (jsxxTar.size() > 0) {
        String ls_brid = "";
        String ls_zyid = "";
        String ls_mdtrt_id = "";
        Date ldt_cydate = null;
        Date ldt_rydate = null;
        String ls_brname = "";
        String ls_psn_no = "";
        String ls_deptname = "";
        String ls_doctorname = "";
        String ls_med_type = "";
        String ls_zyh = "";
        String ls_bed = "";
        String ls_jzh = "";
        Date ldt_jsdate = null;

        for (int ll_ii = 0; ll_ii < jsxxTar.size(); ll_ii++) {
          if (jsxxTar.get(ll_ii).getMedType() != null) {
            if (jsxxTar.get(ll_ii).getMedType().charAt(0) == '2') {
              ls_brid = jsxxTar.get(ll_ii).getBrid();
              ls_zyid = jsxxTar.get(ll_ii).getZyid();
              ls_mdtrt_id = jsxxTar.get(ll_ii).getMdtrtId();
              ldt_cydate = jsxxTar.get(ll_ii).getEnddate();
              ldt_rydate = jsxxTar.get(ll_ii).getBegndate();
              ls_brname = jsxxTar.get(ll_ii).getPsnName();
              ls_psn_no = jsxxTar.get(ll_ii).getPsnNo();
              ls_deptname = jsxxTar.get(ll_ii).getDeptname();
              ls_doctorname = jsxxTar.get(ll_ii).getDoctorname();
              ls_jzh = ls_mdtrt_id;
              if ("1".equals(is_jzh_usebrid)) {
                ls_jzh = ls_brid + "_" + ls_zyid;
              }

              List<Brxx> brxxList = brxxService.selectBrxxList(new Brxx(ls_brid, ls_zyid));
              if (brxxList.isEmpty()) {
                if (ls_zyh == null || "".contentEquals(ls_zyh)) {
                  ls_zyh = ls_brid;
                }
                Brxx insertBrxx = new Brxx();
                insertBrxx.setBrtype("2");
                insertBrxx.setJzh(ls_jzh);
                insertBrxx.setBrid(ls_brid);
                insertBrxx.setZyid(ls_zyid);
                insertBrxx.setZyh(ls_zyh);
                insertBrxx.setName(ls_brname);
                insertBrxx.setBed(ls_bed);
                insertBrxx.setYbh(ls_psn_no);
                insertBrxx.setRydate(ldt_rydate);
                insertBrxx.setCydate(ldt_cydate);
                insertBrxx.setZyzt("0");
                insertBrxx.setDeptid(ls_deptname);
                insertBrxx.setDeptname(ls_deptname);
                insertBrxx.setDoctorid(ls_doctorname);
                insertBrxx.setDoctorname(ls_doctorname);
                brxxService.insertBrxx(insertBrxx);
              } else {
                if (StringUtils.isNotBlank(ls_deptname) && StringUtils.isNotBlank(ls_doctorname)) {
                  Brxx updateBrxx = new Brxx();
                  updateBrxx.setJzh(ls_jzh);
                  updateBrxx.setCydate(ldt_cydate);
                  updateBrxx.setZyzt("0");
                  updateBrxx.setDeptname(ls_deptname);
                  updateBrxx.setDoctorname(ls_doctorname);
                  brxxService.updateBrxx(updateBrxx);
                }
              }
            }
          }
        }

        //转结算时，转费用
        if ("1".equals(is_zjs_bfy)) {
          for (int ll_ii = 0; ll_ii < jsxxTar.size(); ll_ii++) {
            ls_med_type = jsxxTar.get(ll_ii).getMedType();
            if (!"".equals(ls_med_type) && ls_med_type != null) {
              if (ls_med_type.charAt(0) == '2') {
                ls_brid = jsxxTar.get(ll_ii).getBrid();
                ls_zyid = jsxxTar.get(ll_ii).getZyid();
                ls_mdtrt_id = jsxxTar.get(ll_ii).getMdtrtId();
                ldt_jsdate = jsxxTar.get(ll_ii).getHisJsdate();
                if (StringUtils.isNotBlank(ls_mdtrt_id) && StringUtils.isNotBlank(ls_brid)) {
                  cybrFySync(ls_brid, ls_zyid, ls_mdtrt_id, ldt_jsdate);
                }
              }
            }
          }
        }

        jsxxTar.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getSetlId()));
        for (JsxxHis jsxx : jsxxTar) {
          String setlId = jsxx.getSetlId();
          BigDecimal medfeeSumamt = jsxx.getMedfeeSumamt();
          for (JsxxHis jsxx1 : jsxxTar) {
            if (jsxx1.getSetlId().equals(setlId) && jsxx1 != jsxx && jsxx1.getMedfeeSumamt().compareTo(medfeeSumamt) != 0) {
              jsxx1.setSetlId(jsxx1.getSetlId() + "Z");
            }
          }
        }


        jsxxHisService.syncJsxx(jsxxTar);
      }

      ldt_from = ldt_to;
      ldt_to = ldt_to.plusDays(days);
      if (ldt_to.compareTo(ldt_today) > 0) {
        ldt_to = ldt_today;
      }
    }
    return 1;
  }


  /**
   * 门诊费用同步
   * @return
   */
  public Integer mzfyxxSyncOrgpl(String sqlName,LocalDateTime startDate) {

    Hisxxsql his = new Hisxxsql();
    his.setName(sqlName);
    his.setHisSoft(is_hissoftname);
    Hisxxsql hisxxsql = hisxxsqlService.selectHisxxByNameAndHis(his);

    if (hisxxsql == null || StringUtils.isBlank(hisxxsql.getSqlstr())) {
      throw new ServiceException(sqlName + "配置不存在");
    }

    int days = Math.toIntExact(hisxxsql.getDays());
    if(days<=0) {
    	days = 1;
    }

    List<Entity> fyxxHis = new ArrayList<>();
    List<Fyxx> fyxxTar = new ArrayList<>();


    LocalDateTime ldt_from = startDate;
    LocalDateTime ldt_to = startDate.plusDays(days);

    LocalDateTime ldt_today = getServerTime();
    if (ldt_to.compareTo(ldt_today) > 0) {
      ldt_to = ldt_today;
    }

    int li_num = 0;

    Map<String, Object> paramList = new HashMap<>();
    while (ldt_from.compareTo(ldt_today) < 0 && li_num < 600) {
      li_num++;

      fyxxHis.clear();
      fyxxTar.clear();
      paramList.clear();

      paramList.put("adtFrom", formatLocalDateTime(ldt_from));
      paramList.put("adtTo", formatLocalDateTime(ldt_to));

      //源数据库结算数据
      fyxxHis = dataSyncService.executeQuery(sqlName, paramList);

//      if (fyxxHis.size() > 0) {
//        li_num = li_num + 10;
//
//        for (int i = 0; i < fyxxHis.size(); i++) {
//            Entity entity = fyxxHis.get(i);
//            Set<String> fieldNames = entity.getFieldNames();
//            Class<? extends Fyxx> target = Fyxx.class;
//            JsxxHis instance = null;
//            Object dataCopy = dataCopy(instance, target, fieldNames, entity);
//            fyxxTar.add((Fyxx) dataCopy);
//          }
//        fyxxTar.removeIf(obj -> obj == null );
//        System.out.println("开始保存");
//        fyxxService.syncMzFyxx(fyxxTar);
//        System.out.println("开始结束");
//      }
      if (fyxxHis.size() > 0) {
    	    li_num = li_num + 10;
    	    int batchSize = 1000; // 每批处理量（根据测试调整）
    	    List<Fyxx> batchList = new ArrayList<>(batchSize);

    	    for (int i = 0; i < fyxxHis.size(); i++) {
    	        Entity entity = fyxxHis.get(i);
    	        Set<String> fieldNames = entity.getFieldNames();
    	        Object dataCopy = dataCopy(null, Fyxx.class, fieldNames, entity);
    	        if (dataCopy != null) {
    	            batchList.add((Fyxx) dataCopy);

    	            // 达到批次大小时提交
    	            if (batchList.size() >= batchSize) {
    	                fyxxService.syncMzFyxx(batchList);
    	                batchList.clear(); // 清空后复用
    	            }
    	        }
    	    }

    	    // 提交剩余数据
    	    if (!batchList.isEmpty()) {
    	        fyxxService.syncMzFyxx(batchList);
    	    }
    	}
      ldt_from = ldt_to;
      ldt_to = ldt_to.plusDays(days);
      if (ldt_to.compareTo(ldt_today) > 0) {
        ldt_to = ldt_today;
      }

      }



    return 1;
  }




  /**
   * 同步结算信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  private Integer jsxxCopy(List<Entity> sourceList, List<JsxxHis> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends JsxxHis> target = JsxxHis.class;
      JsxxHis instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((JsxxHis) dataCopy);
    }
    return 1;
  }





  /**
   * 同步出院病人费用信息
   *
   * @param as_brid
   * @param as_zyid
   * @param as_jzh
   * @param adt_jsdate
   * @return
   */
  public Integer cybrFySync(String as_brid, String as_zyid, String as_jzh, Date adt_jsdate) {
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }
    String ls_jzh = as_jzh;
    String ls_brid = "";
    String as_jgid = "";
    if ("".equals(as_brid)) {
      List<Brxx> brxxList = brxxService.selectBrxxByJzh(ls_jzh);
      if (brxxList.size() > 0) {
        as_brid = brxxList.get(0).getBrid();
        as_zyid = brxxList.get(0).getZyid();
        as_jgid = brxxList.get(0).getJgid();
      }
    }
    if (as_brid != null) {
      ls_brid = as_brid;
    }
    LocalDateTime ldt_rydate = null;
    LocalDateTime ldt_opdate = null;
    LocalDate localDate = adt_jsdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    LocalDate ldt_predate_date = localDate.minusDays(60);
    LocalDateTime ldt_predate = ldt_predate_date.atTime(LocalTime.now());

//    brxxQuery.setRydate(Date.from(ldt_predate.atZone(ZoneId.systemDefault()).toInstant()));
    Brxx brxxQueryResult = brxxService.selectRydateByJzh(new Brxx(as_jzh));
    if (brxxQueryResult != null) {
      ldt_rydate = formatDate(brxxQueryResult.getRydate());
    }
    if (ldt_rydate == null || ldt_rydate.compareTo(ldt_predate) < 0) {
      ldt_rydate = ldt_predate;
    }

    Fyxx fyxxQuery = new Fyxx();
    fyxxQuery.setBrid(as_brid);
    Fyxx fyxxQueryResult = fyxxService.selectOpdateByBrid(fyxxQuery);
    if (fyxxQueryResult != null) {
      ldt_opdate = formatDate(fyxxQueryResult.getOpdate());
    }
    if (ldt_opdate.compareTo(ldt_rydate) < 0) {
      ldt_opdate = ldt_rydate;
    }

    if (ldt_opdate.compareTo(LocalDateTime.of(2022, 01, 01, 00, 00, 01)) < 0) {
      ldt_opdate = LocalDateTime.now().minusDays(30);
    }

    if (ldt_rydate.compareTo(LocalDateTime.of(2022, 01, 01, 00, 00, 01)) < 0) {
      ldt_rydate = LocalDateTime.now().minusDays(60);
    }

    List<Entity> ids_fyxx_sou = new ArrayList<>();
    List<Fyxx> ids_fyxx_des = new ArrayList<>();

    Map<String, Object> paramList = new HashMap<>();
    if ("zlhis".equals(is_hissoftname) || "hzcy".equals(is_hissoftname)) {
//      paramList.put("brid",Double.parseDouble(ls_brid));
//      paramList.put("opdate",formatLocalDateTime(ldt_opdate));
//      paramList.put("zyid",Double.parseDouble(as_zyid));
      paramList.put("brid", ls_brid);
      paramList.put("opdate", formatLocalDateTime(ldt_opdate));
      paramList.put("zyid", as_zyid);
      ids_fyxx_sou = dataSyncService.executeQuery(getSqlName("fyxx",as_jgid), paramList);
    } else {
      if ("1".equals(is_kxyy_flag)) {
        paramList.put("brid", ls_jzh);
        paramList.put("opdate", formatLocalDateTime(ldt_opdate));
        paramList.put("zyid", as_zyid);
        ids_fyxx_sou = dataSyncService.executeQuery(getSqlName("fyxx",as_jgid), paramList);
      } else {
        paramList.put("brid", ls_brid);
        paramList.put("opdate", formatLocalDateTime(ldt_opdate));
        paramList.put("zyid", as_zyid);
        ids_fyxx_sou = dataSyncService.executeQuery(getSqlName("fyxx",as_jgid), paramList);
      }
    }

    if (ids_fyxx_sou.size() > 0) {
      fyxxCopy(ids_fyxx_sou, ids_fyxx_des);
      String ls_zyid = "";

      for (int ll_ii = 0; ll_ii < ids_fyxx_des.size(); ll_ii++) {
        ls_zyid = ids_fyxx_des.get(ll_ii).getZyid();
        if (as_zyid.equals(ids_fyxx_des.get(ll_ii).getZyid())) {
          ids_fyxx_des.get(ll_ii).setJzh(ls_jzh);
        }
      }

      for (int i = 0; i < ids_fyxx_des.size(); i++) {
        if ("".equals(ids_fyxx_des.get(i).getJzh()) || ids_fyxx_des.get(i).getJzh() == null) {
          ids_fyxx_des.remove(i);
        }
      }

      ids_fyxx_des.forEach(item -> fyxxService.insertFyxx(item));

    }

    return 1;
  }


  /**
   * 保存特病费用信息
   *
   * @param as_jzh_lsh
   * @param as_input
   * @param as_brid
   * @param as_zyid
   * @param as_jzid
   * @return
   */
  public Integer saveTbfyxx(String as_jzh_lsh, String as_input, String as_brid, String as_zyid, String as_jzid) {
    String ls_output = as_input, ls_fydate;
    List<MztbFyxx> ids_mztb_fyxx = new ArrayList<>();
    if (getKeyValue(ls_output, "成功标志", "@&") == "1") {
      String ls_msg = getKeyValue(ls_output, "结果集", "@&");
      List<MztbFyxx> mztbFyxxes = mztbFyxxService.selectMztbFyxxList(new MztbFyxx());
      int ll_have = mztbFyxxes.size();
      String[] ls_key_array = ls_msg.split("#&#");
      if (ls_key_array.length > 0 && ll_have == 0) {
        for (int ll_k = 0; ll_k < ls_key_array.length; ll_k++) {
          if (ll_have == 0) {
            MztbFyxx mztbFyxx = new MztbFyxx();
            mztbFyxx.setFeedetlSn(as_jzh_lsh);
            mztbFyxx.setRxDrordNo(getKeyValue(ls_key_array[ll_k], "rx_drord_no", "|&|"));
            mztbFyxx.setFixmedinsCode(getKeyValue(ls_key_array[ll_k], "fixmedins_code", "|&|"));
            mztbFyxx.setFixmedinsName(getKeyValue(ls_key_array[ll_k], "fixmedins_name", "|&|"));
            mztbFyxx.setPsnNo(getKeyValue(ls_key_array[ll_k], "psn_no", "|&|"));
            mztbFyxx.setMedType(getKeyValue(ls_key_array[ll_k], "med_type", "|&|"));
            ls_fydate = getKeyValue(ls_key_array[ll_k], "fee_ocur_time", "|&|");

            if (isValidDate(ls_fydate, dateFormatter)) {
              SimpleDateFormat sdfDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
              try {
                mztbFyxx.setFeeOcurTime(sdfDateFormatter.parse(ls_fydate));
              } catch (ParseException e) {
              }
            }

            mztbFyxx.setCnt(new BigDecimal(getKeyValue(ls_key_array[ll_k], "cnt", "|&|")));
            mztbFyxx.setPric(new BigDecimal(getKeyValue(ls_key_array[ll_k], "pric", "|&|")));
            mztbFyxx.setChrgitmLv(getKeyValue(ls_key_array[ll_k], "chrgitm_lv", "|&|"));
            mztbFyxx.setHilistCode(getKeyValue(ls_key_array[ll_k], "hilist_code", "|&|"));
            mztbFyxx.setHilistName(getKeyValue(ls_key_array[ll_k], "hilist_name", "|&|"));
            mztbFyxx.setListType(getKeyValue(ls_key_array[ll_k], "list_type", "|&|"));

            mztbFyxx.setMedListCodg(getKeyValue(ls_key_array[ll_k], "med_list_codg", "|&|"));
            mztbFyxx.setMedinsListCodg(getKeyValue(ls_key_array[ll_k], "medins_list_codg", "|&|"));
            mztbFyxx.setMedinsListName(getKeyValue(ls_key_array[ll_k], "medins_list_name", "|&|"));
            mztbFyxx.setMedChrgitmType(getKeyValue(ls_key_array[ll_k], "med_chrgitm_type", "|&|"));
            mztbFyxx.setProdname(getKeyValue(ls_key_array[ll_k], "prodname", "|&|"));
            mztbFyxx.setSpec(getKeyValue(ls_key_array[ll_k], "spec", "|&|"));
            mztbFyxx.setDosformName(getKeyValue(ls_key_array[ll_k], "dosform_name", "|&|"));
            mztbFyxx.setLmtUsedFlag(getKeyValue(ls_key_array[ll_k], "lmt_used_flag", "|&|"));
            mztbFyxx.setHospPrepFlag(getKeyValue(ls_key_array[ll_k], "hosp_prep_flag", "|&|"));
            mztbFyxx.setHospApprFlag(getKeyValue(ls_key_array[ll_k], "hosp_appr_flag", "|&|"));
            mztbFyxx.setTcmdrugUsedWay(getKeyValue(ls_key_array[ll_k], "tcmdrug_used_way", "|&|"));
            mztbFyxx.setProdplacType(getKeyValue(ls_key_array[ll_k], "prodplac_type", "|&|"));
            mztbFyxx.setBasMednFlag(getKeyValue(ls_key_array[ll_k], "bas_medn_flag", "|&|"));
            mztbFyxx.setHiNegoDrugFlag(getKeyValue(ls_key_array[ll_k], "hi_nego_drug_flag", "|&|"));
            mztbFyxx.setChldMedcFlag(getKeyValue(ls_key_array[ll_k], "chld_medc_flag", "|&|"));
            mztbFyxx.setEtipFlag(getKeyValue(ls_key_array[ll_k], "etip_flag", "|&|"));
            mztbFyxx.setEtipHospCode(getKeyValue(ls_key_array[ll_k], "etip_hosp_code", "|&|"));
            mztbFyxx.setDscgTkdrugFlag(getKeyValue(ls_key_array[ll_k], "dscg_tkdrug_flag", "|&|"));
            mztbFyxx.setListSpItemFlag(getKeyValue(ls_key_array[ll_k], "list_sp_item_flag", "|&|"));
            mztbFyxx.setMatnFeeFlag(getKeyValue(ls_key_array[ll_k], "matn_fee_flag", "|&|"));
            mztbFyxx.setBrid(as_brid);
            mztbFyxx.setZyid(as_zyid);
            mztbFyxx.setJzid(as_jzid);
          }
        }
      }
    }

    if (ids_mztb_fyxx.size() > 0) {
      ids_mztb_fyxx.forEach(item -> mztbFyxxService.insertMztbFyxx(item));
    }

    return 1;
  }

  /**
   * 获取系统日期
   * @return
   */
  public LocalDateTime getServerTime() {
    Date systemServerTime = ybjkOptionService.getSystemServerTime();
    if (systemServerTime == null) {
      return LocalDateTime.now();
    }
    return formatDate(systemServerTime);
  }


  /**
   * 验证日期格式
   *
   * @param date
   * @param formatter
   * @return
   */
  public boolean isValidDate(String date, DateTimeFormatter formatter) {
    try {
      if (date!=null) {
        date = date.length() >= 20 ? date.substring(0, 19) : date;//处理sql server 多一位的问题
      }
      LocalDate.parse(date, formatter);
      return true;
    } catch (Exception e) {
      return false;
    }
  }


  public String getKeyValue(String as_source, String as_keyword, String as_separator) {
    boolean lb_done = false;
    int li_keyword, li_separator, li_equal;
    String ls_keyvalue, ls_source, ls_exact;
    if (as_source == null || as_keyword == null || as_separator == null) {
      return null;
    }
    ls_keyvalue = "";
    while (!lb_done) {
      li_keyword = as_source.toLowerCase().indexOf(as_keyword.toLowerCase());
      if (li_keyword > -1) {
        ls_source = as_source;
        as_source = as_source.substring(as_source.length() - (li_keyword + as_keyword.length() - 1));
        as_source = leftTrim(as_source);
        li_equal = li_keyword - as_separator.length();
        if (li_equal > 0) {
          ls_exact = ls_source.substring(li_equal, li_equal + as_separator.length());
          if (!ls_exact.equals(as_separator)) {
            continue;
          }
        }

        if (as_source.charAt(0) == '=') {
          li_separator = as_source.indexOf(as_separator);
          if (li_separator > -1) {
            li_separator = as_source.substring(as_source.indexOf(as_separator)).indexOf(as_separator);
          }
          if (li_separator > -1) {
            if (as_source.length() >= 1 + li_separator - 2) {
              ls_keyvalue = as_source.substring(1, 1 + li_separator - 2);
            }
          } else {
            ls_keyvalue = as_source.substring(1);
          }
          ls_keyvalue = ls_keyvalue.trim();
          lb_done = true;
        }
      } else {
        lb_done = true;
      }
    }
    return ls_keyvalue;
  }

  /**
   * 去除左侧字符串
   *
   * @param input
   * @return
   */
  public String leftTrim(String input) {
    int startIndex = 0;
    while (startIndex < input.length() && input.charAt(startIndex) == ' ') {
      startIndex++;
    }
    return input.substring(startIndex);
  }


  /**
   * 复制检查信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  public Integer jcxxCopy(List<Entity> sourceList, List<Jcxx> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends Jcxx> target = Jcxx.class;
      Jcxx instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((Jcxx) dataCopy);
    }
    return 1;
  }

  /**
   * 复制校验信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  public Integer jyxxCopy(List<Entity> sourceList, List<Jyxx> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends Jyxx> target = Jyxx.class;
      Jyxx instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((Jyxx) dataCopy);
    }
    return 1;
  }

  /**
   * 复制科室信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  public Integer ksxxCopy(List<Entity> sourceList, List<SysDept> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends SysDept> target = SysDept.class;
      SysDept instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((SysDept) dataCopy);
    }
    return 1;
  }

  /**
   * 复制医生信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  public Integer ysxxCopy(List<Entity> sourceList, List<SysUser> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends SysUser> target = SysUser.class;
      SysUser instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((SysUser) dataCopy);
    }
    return 1;
  }

  /**
   * 复制对照信息
   *
   * @param sourceList
   * @param targetList
   * @return
   */
  public Integer dzxxCopy(List<Entity> sourceList, List<YbgkDz> targetList) {
    for (int i = 0; i < sourceList.size(); i++) {
      Entity entity = sourceList.get(i);
      Set<String> fieldNames = entity.getFieldNames();
      Class<? extends YbgkDz> target = YbgkDz.class;
      YbgkDz instance = null;
      Object dataCopy = dataCopy(instance, target, fieldNames, entity);
      targetList.add((YbgkDz) dataCopy);
    }
    return 1;
  }



  @Override
  public void refreshOption() {
    this.getHissoftName();
    dataSyncService.refreshOptions();
  }


  /**
   * 获取His系统名称
   */
  public void getHissoftName() {
    companyName = ybjkOptionService.getCompanyName();
    is_hissoftname = ybjkOptionService.getOptionInfo("hissoftname",null);
    if (StringUtils.isBlank(is_hissoftname)) {
      throw new ServiceException("未配置HIS系统名称");
    }
    basy_sync_zdly = ybjkOptionService.getOptionInfo("basy_sync_zdly","1");
    is_zbasy_zbl = ybjkOptionService.getOptionInfo("zbasy_zbl","");
    is_zbasy_zfy = ybjkOptionService.getOptionInfo("zbasy_zfy","1");
    is_zbasy_zd = ybjkOptionService.getOptionInfo("zbasy_zd","1");
    is_dj_emr = ybjkOptionService.getOptionInfo("dj_emr","");
    is_kxyy_flag = ybjkOptionService.getOptionInfo("kxyy_flag","");
    gs_havedrg_flag = ybjkOptionService.getOptionInfo("use_drg","");
    is_jzh_usebrid = ybjkOptionService.getOptionInfo("jzh_usebrid","1");
    is_zjs_bfy = ybjkOptionService.getOptionInfo("zjs_bfy","");
    basy_doctor_sync = ybjkOptionService.getOptionInfo("basy_doctor_sync","0");
    is_zdsync_sfsh = ybjkOptionService.getOptionInfo("zdsync_sfsh","0");
    basy_sync_with_blxx = ybjkOptionService.getOptionInfo("basy_sync_with_blxx","1");
    basy_sync_with_fyxx = ybjkOptionService.getOptionInfo("basy_sync_with_fyxx","1");
    blxx_sync_for_br = ybjkOptionService.getOptionInfo("blxx_sync_for_br","1");
    yzxx_sync = ybjkOptionService.getOptionInfo("yzxx_sync","1");
    syncAllFeeByNum = ybjkOptionService.getOptionInfo("sync_all_fee_by_num","0");
    update_anst = ybjkOptionService.getOptionInfo("update_anst", "0");
    basy_sync_from_api = ybjkOptionService.getOptionInfo("basy_sync_from_api", "0");
    yzxx_sync_for_br = ybjkOptionService.getOptionInfo("yzxx_sync_for_br", "0");
    use_ybqd = ybjkOptionService.getOptionInfo("use_ybqd", "0");

    trans_blxx_ty = ybjkOptionService.getOptionInfo("trans_blxx_ty", "0");

    hasMultipleHis = ybjkOptionService.getOptionInfo("has_multiple_his", "0");
    hasMultipleOrg = ybjkOptionService.getOptionInfo("use_multi_tenant", "0");
    orgList = tenantService.selectSysTenantList(new SysTenant());
    orgList.removeIf(obj -> obj == null || StringUtils.isBlank(obj.getTenantId()));
    //同步不区分机构
    is_tb_bqfjg = ybjkOptionService.getOptionInfo("use_multi_tenant", "0");
    fyxx_ctrl_months = ybjkOptionService.getOptionInfo("fyxx_ctrl_months", "12");

    try {
      YbjkOption option = ybjkOptionService.selectYbjkOptionByCCode("sync_limit_date");
      if (option != null && StringUtils.isNotBlank(option.getcValue())) {
        sync_limit_date = LocalDateTime.of(LocalDate.parse(option.getcValue(), DateTimeFormatter.ofPattern("yyyy-MM-dd")),LocalTime.of(0,0,0));
      }
    } catch (Exception e) {
      sync_limit_date = null;
    }
  }

  /**
   * LocalDateTime转Date
   *
   * @param dateTime
   * @return
   */
  public Date formatLocalDateTime(LocalDateTime dateTime) {
    return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
  }


  /**
   * Date转LocalDateTime
   *
   * @param date
   * @return
   */
  public LocalDateTime formatDate(Date date) {
    return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
  }

  /**
   * 医保返回自查同步费用并审核
   *
   * @param
   * @return
   */
  public Integer ybfhzcmzzyfyxxsyn() {
	  String ls_medtype;
	  List<JsxxHis> list = jsxxHisMapper.selectJsxxHisybfhzcList(new JsxxHis());
	  for (JsxxHis jsxxHis : list) {
		  JsxxHis jsxxHis1 =  new JsxxHis();
		  jsxxHis1.setMdtrtId(jsxxHis.getMdtrtId());
		  ls_medtype = jsxxHis.getMedType();
		  List<JsxxHis> list1 = jsxxHisMapper.selectJsxxHisList(jsxxHis1);
		  if (list1.size()>0) {
			  if (list1.get(0)!=null) {
				  String brid = list1.get(0).getBrid();
				  String zyid = list1.get(0).getZyid();
				  if ("21".equals(ls_medtype)||"24".equals(ls_medtype)||"52".equals(ls_medtype)||"9902".equals(ls_medtype)||"9904".equals(ls_medtype)||"9907".equals(ls_medtype)||"9925".equals(ls_medtype)||"9921".equals(ls_medtype)) {
					  //转住院费用
					  try {
						  zdxxSync(brid, zyid);
						  fyxxSync(brid, zyid);
						  YbgkWgjl wgjl = new YbgkWgjl();
					      wgjl.setJzh(brid+"_"+zyid);
						  cyshController.cysh(wgjl);//暂时只审核住院
					} catch (Exception e) {
						// TODO: handle exception
					}


				  }else {
					    //转门诊费用
					  try {
						  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					        String ls_jsdate = sdf.format(list1.get(0).getHisJsdate());
						   mzfyxxSync(list1.get(0).getMdtrtId(), list1.get(0).getBrid(), list1.get(0).getZyid(),list1.get(0).getFixmedinsCode(),ls_jsdate);
					} catch (Exception e) {
						// TODO: handle exception
					}

				  }
			  }




		  }
	}

	  return 0;
  }


//  /**
//   * 同步科室信息
//   */
//  public Integer ksxxSync() {
//    List<Entity> ids_ksxx_sou = dataSyncService.executeQuery("ksxx", new HashMap<>());
//    List<SysDept> sysDeptList = new ArrayList<>();
//    if (ids_ksxx_sou.size() > 0) {
//      ksxxCopy(ids_ksxx_sou, sysDeptList);
//
//      if (sysDeptList.size() > 0) {
//        SysDept dept = new SysDept();
//        for (SysDept sysDept : sysDeptList) {
//          dept.setDeptName(sysDept.getDeptName());
//          dept.setParentId(sysDept.getParentId());
//          dept.setHisid(sysDept.getHisid());
//          List<SysDept> deptList = sysDeptService.selectSyncDeptList(dept);
//          System.out.println(deptList.size());
//          if (deptList.size() > 0) {
//            sysDept.setDeptId(deptList.get(0).getDeptId());
//            sysDeptService.updateDept(sysDept);
//          } else {
//            try {
//              sysDeptService.insertDept(sysDept);
//            } catch (Exception e) {
//              sysDeptService.updateDept(sysDept);
//            }
//          }
//        }
//      }
//    }
//    return 1;
//  }
//
//  /**
//   * 同步医生信息
//   */
//  public Integer ysxxSync() {
//    List<Entity> ids_ysxx_sou = dataSyncService.executeQuery("ysxx", new HashMap<>());
//    List<SysUser> sysUserList = new ArrayList<>();
//    if (ids_ysxx_sou.size() > 0) {
//      ysxxCopy(ids_ysxx_sou, sysUserList);
//
//      if (sysUserList.size() > 0) {
//        SysUser user = new SysUser();
//        for (SysUser sysUser : sysUserList) {
//          user.setUserName(sysUser.getUserName());
//          user.setSfz(sysUser.getSfz());
//          user.setDeptId(sysUser.getDeptId());
//          List<SysUser> userList = sysUserService.selectSyncUserList(user);
//          if (userList.size() > 0) {
//            sysUser.setUserId(userList.get(0).getUserId());
//            sysUserService.updateSyncUser(sysUser);
//          } else {
//            try {
//              sysUserService.insertSyncUser(sysUser);
//            } catch (Exception e) {
//              sysUserService.updateSyncUser(sysUser);
//            }
//          }
//        }
//      }
//    }
//    return 1;
//  }

  /**
   * 同步对照信息
   */
  public Integer dzxxSync() {
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }


    List<Entity> dzxxListHis;
    //如果是多HIS，则分别从每个数据源获取数据合在一起
    if ("1".equals(hasMultipleHis)) {

      List<Entity> list = new ArrayList<>();

      boolean hasNullSource = false;

      for (SysTenant org : orgList) {

        if (StringUtils.isNotBlank(org.getSourcename())) {
          List<Entity> entityList = dataSyncService.executeQuery(getSqlName("dzxx", org.getTenantId()), new HashMap<>());
          list.addAll(entityList);
        } else {
          hasNullSource = true;
        }

      }

      if (hasNullSource || list.size() == 0) {
        list.addAll(dataSyncService.executeQuery("dzxx", new HashMap<>()));
      }

      dzxxListHis = list;

    } else {
      dzxxListHis = dataSyncService.executeQuery("dzxx", new HashMap<>());
    }


    List<YbgkDz> ybgkDzList = new ArrayList<>();
    if (dzxxListHis.size() > 0) {
      dzxxCopy(dzxxListHis, ybgkDzList);

      for (int i = 0; i < ybgkDzList.size(); i++) {
        if (ybgkDzList.get(i).getXmbm() == null || ybgkDzList.get(i).getFymid() == null) {
          ybgkDzList.remove(i);
          i--;
          continue;
        }
        YbgkDz dz = ybgkDzList.get(i);
        if (StrUtil.isEmpty(dz.getId())) {
          dz.setId(StrUtil.isEmpty(dz.getXmbm()) ? dz.getFymid() : dz.getXmbm() + "-" + dz.getFymid());
        }
      }

      if (ybgkDzList.size() > 0) {
        ybgkDzService.deleteYbgkDzByFymidAndXmbm(ybgkDzList);
        ybgkDzService.syncDzxx(ybgkDzList);
      }
    }
    return 1;
  }

  @Override
  public Integer basySyncForDays() throws ParseException {

    Brxx brxx = new Brxx();
    brxx.setZyzt("1");

    List<Brxx> inList = brxxService.selectBrxxList(brxx);
    List<Brxx> threeDaysList = brxxService.selectOutForDaysBrxx(3);
    List<Brxx> sixDaysList = brxxService.selectOutForDaysBrxx(6);
    List<Brxx> tenDaysList = brxxService.selectOutForDaysBrxx(10);
    List<Brxx> todayList = brxxService.selectOutForDaysBrxx(1);

    List<String> brbsList = new ArrayList<>();
    inList.forEach(item -> {
      brbsList.add(item.getJzh());
    });
    threeDaysList.forEach(item -> {
      brbsList.add(item.getJzh());
    });
    tenDaysList.forEach(item -> {
      brbsList.add(item.getJzh());
    });
    todayList.forEach(item -> {
      brbsList.add(item.getJzh());
    });

    for (String brbs : brbsList) {
    	try {
    		basySync(brbs,null);
		} catch (Exception e) {
			// TODO: handle exception
		}


    }

    return 1;
  }

  @Override
  public void yzxxSync(String brid, String zyid) {
    if (!"1".equals(yzxx_sync)) {
      logger.warn("单病人医嘱信息同步未启用");
      return;
    }

    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    Brxx brxx = null;
    List<Brxx> brxxList = brxxService.selectBrxxList(new Brxx(brid,zyid));
    if (brxxList.size() > 0) {
      brxx = brxxList.get(0);
    }

    if (brxx == null) {
      return;
    }

    String jgid = null;
    if ("1".equals(hasMultipleHis) && StringUtils.isNotBlank(brxx.getJgid())) {
      jgid = brxx.getJgid();
    }

    // 获取当前病人医嘱信息的最大时间
    Yzxx lastYz = yzxxService.selectMaxOpdate(brid, zyid);

    Map<String, Object> params = new HashMap<>();
    params.put("brid", brid);
    params.put("zyid", zyid);

    if (lastYz != null && lastYz.getOpdate() != null) {
      String maxOpdate = simpleDateFormat.format(lastYz.getOpdate());
//      LocalDateTime startDate = lastYz.getOpdate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
      params.put("adtFrom", maxOpdate);
    } else {
      params.put("adtFrom", "2000-01-01 00:00:00");
    }

    String endDate = LocalDateTime.now().plusDays(1).format(dateFormatter);
    params.put("adtTo", endDate);

    List<Entity> list = dataSyncService.executeQuery(getSqlName("singleYzxx",jgid), params);
    if (list == null || list.isEmpty()) {
      logger.info("该病人无医嘱信息: " + brid + "_" + zyid);
      return;
    }
    List<Yzxx> yzxxList = new ArrayList<>();
    yzxxCopy(list, yzxxList);

    for (Yzxx yzxx : yzxxList) {
      yzxx.setJzh(brxx.getJzh());
    }

    yzxxService.insertBatchYzxx(yzxxList);


//    YbjkOption updateAn = ybjkOptionService.selectYbjkOptionByCCode("update_anst");
//    if (updateAn != null) {
//      update_anst = updateAn.getcValue();
//    }

  }


  SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy年MM月dd日", Locale.CHINA);
  SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

  public String formatChineseDate(String chineseDate) {
    //0000年00月00日   11位
    if (chineseDate.length() > 11) {
      chineseDate = chineseDate.substring(0, 11);
    }
    try {
      Date date = inputFormat.parse(chineseDate);
      String outputDateStr = outputFormat.format(date);
      return outputDateStr;
    } catch (ParseException e) {
      chineseDate.replace("年", "-");
      chineseDate.replace("月", "-");
      chineseDate.replace("日", "");
      return chineseDate;
    }
  }


  public String gfNull(String as_input) {
    if (as_input == null || as_input.isEmpty()) {
      as_input = "";
    }
    return as_input.trim();
  }


  /**
   * 同步手术信息
   *
   * @param as_brid
   * @param as_zyid
   * @return
   */
  public Integer ssjlSync(String as_brid, String as_zyid) {
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    List<Entity> ids_zlhis_zybr = new ArrayList<>();
    if (!"".equals(gfNull(as_brid))) {
      Brxx brxx = brxxService.selectBrxxByBridAndZyid(new Brxx(as_brid, as_zyid));
      String ls_jzh = "";
      if (brxx != null) {
        ls_jzh = brxx.getJzh();
      }
      if ("".equals(ls_jzh)) {
        ls_jzh = as_brid + "_" + as_zyid;
      }
      Entity entity = new Entity();
      entity.set("jzh", ls_jzh);
      entity.set("brid", as_brid);
      entity.set("zyid", as_zyid);
      entity.set("jgid", brxx != null ? brxx.getJgid() : null);
      ids_zlhis_zybr.add(entity);
    } else {
      ids_zlhis_zybr = getPatData();
    }


    System.out.println("--------------------------------在院病人人数：" + ids_zlhis_zybr.size());


    String ls_newflag = "0";

    for (int ll_i = 0; ll_i < ids_zlhis_zybr.size(); ll_i++) {
      String ls_jzh = gfNull(ids_zlhis_zybr.get(ll_i).getStr("jzh"));
      String ls_brid = gfNull(ids_zlhis_zybr.get(ll_i).getStr("brid"));
      String ls_zyid = gfNull(ids_zlhis_zybr.get(ll_i).getStr("zyid"));
      String ls_jgid = gfNull(ids_zlhis_zybr.get(ll_i).getStr("jgid"));
//      String ls_brbs = ls_brid + "_" + ls_zyid;

      ls_newflag = "0";

      Map<String, Object> params = new HashMap<>();
      params.put("brid", ls_brid);
      params.put("zyid", ls_zyid);
      List<Entity> ids_ssjl_tjzd_sou = dataSyncService.executeQuery(getSqlName("ba_ssjl",ls_jgid), params);


      System.out.println("--------------------------------病人手术数量：" + ids_ssjl_tjzd_sou.size() + "   病人就诊号：" + ls_jzh);


      if (ids_ssjl_tjzd_sou.size() > 0) {
        BaSsjlTjzd baSsjlTjzd = new BaSsjlTjzd();
        baSsjlTjzd.setJzh(ls_jzh);
        List<BaSsjlTjzd> ids_ssjl_tjzd_des = baSsjlTjzdService.selectBaSsjlTjzdListNotZnzd(baSsjlTjzd);


        if (ids_ssjl_tjzd_des.size() > 0) {
          for (int ll_ii = 0; ll_ii < ids_ssjl_tjzd_sou.size(); ll_ii++) {
            String ls_zdname = gfNull(ids_ssjl_tjzd_sou.get(ll_ii).getStr("ssname"));
            if (ls_zdname.length() > 126) {
              ls_zdname = ls_zdname.substring(0, 126);
            }
            String ls_sslx = gfNull(ids_ssjl_tjzd_sou.get(ll_ii).getStr("sslx"));

            int ll_find = -1;
            for (int i = 0; i < ids_ssjl_tjzd_des.size(); i++) {
              if (ids_ssjl_tjzd_des.get(i).getSsname().equals(ls_zdname) && ids_ssjl_tjzd_des.get(i).getSslx().equals(ls_sslx)) {
                ll_find = i;
                break;
              }
            }

            if (ll_find == -1) {
              ls_newflag = "1";
              break;
            }
          }
        } else {
          ls_newflag = "1";
        }

        System.out.println("--------------ls_newflag:" + ls_newflag);
        if ("1".equals(ls_newflag)) {
          baSsjlTjzdService.deleteBaSsjlTjzdByJzhAndNotZnzd(ls_jzh);
          ssxxCopy(ids_ssjl_tjzd_sou, ids_ssjl_tjzd_des);
          ids_ssjl_tjzd_des.forEach(ssjl -> baSsjlTjzdService.insertBaSsjlTjzd(ssjl));

          updateBaSsjl(ls_brid, ls_zyid);  //将手术同步到ba_ssjl 和 ba_ssjl_sy

        }
      }
    }
    return 1;
  }

  public Integer updateBaSsjl(String as_brid, String as_zyid) {
    //删除原来的
    baSsjlSyService.deleteBaSsjlSyById(new BaSsjl(as_brid, as_zyid));
    baSsjlService.deleteBaSsjlById(new BaSsjl(as_brid, as_zyid));

    //添加新的
    baSsjlSyService.insertBaSsjlSyByTjzd(new BaSsjlSy(as_brid, as_zyid));
    baSsjlService.insertBaSsjlByTjzd(new BaSsjl(as_brid, as_zyid));
    return 1;
  }


  /**
   * 填充病案首页诊断信息
   */
  public BaSyjl fillBasyDiags(BaSyjl basy) throws Exception {
    List<Zdxx> zdxxList = new ArrayList<>();

    //获取诊断信息的参数
    Map<String, Object> params = new HashMap<>();
    params.put("brid", basy.getBrid());
    params.put("zyid", basy.getZyid());
    List<Entity> zdxxes = null;
    try {
      zdxxes = dataSyncService.executeQuery(getSqlName("zdxx",basy.getUsername()), params);
    } catch (Exception e) {
      logger.error("同步诊断信息获取失败，就诊号：" + basy.getJzh());
      throw new ServiceException("同步诊断信息获取失败");
    }

    if (zdxxes != null && zdxxes.size() > 0) {

      List<Entity> zdxxFiltered = findDiagsByTypeAndOrigin(zdxxes,"出院诊断","4");
      if (zdxxFiltered.size() == 0) {
    	  zdxxFiltered = findDiagsByTypeAndOrigin(zdxxes,"出院诊断","3");
      }
      if (zdxxFiltered.size() == 0) {
    	  zdxxFiltered = findDiagsByTypeAndOrigin(zdxxes,"入院诊断","4");
      }


      if (zdxxFiltered.size() == 0) {
    	  zdxxFiltered = findDiagsByTypeAndOrigin(zdxxes,"入院诊断","3");
      }

      if (zdxxFiltered.size() == 0) {
    	  zdxxFiltered = findDiagsByTypeAndOrigin(zdxxes,"3","4");
      }
      if (zdxxFiltered.size() == 0) {
        zdxxFiltered = findDiagsByTypeAndOrigin(zdxxes,"3","3");
        if (zdxxFiltered.size() == 0) {
          zdxxFiltered = findDiagsByTypeAndOrigin(zdxxes,"2","4");
          if (zdxxFiltered.size() == 0) {
            zdxxFiltered = findDiagsByTypeAndOrigin(zdxxes,"2","3");
          }
        }
      }

      zdxxes = zdxxFiltered;

      for (Entity entity : zdxxes) {
        Zdxx zdxx = new Zdxx();
        zdxx.setZdcode(entity.getStr("zdcode"));
        zdxx.setZdname(entity.getStr("zdname"));
        zdxx.setZdsort(entity.getInt("zdsort"));
        zdxx.setRybq(entity.getStr("rybq"));
        zdxxList.add(zdxx);
      }

      if ("重庆北部妇产医院".equals(companyName)) {
        zdxxList = updateZdxxSort(zdxxList);
      }

    }
    if (zdxxList.size() == 0) {
      List<Brxx> brxxList = brxxService.selectBrxxByJzh(basy.getJzh());
      if (brxxList.size() > 0) {
        Brxx brxx = brxxList.get(0);
        if (brxx != null) {
          String blcyzd = brxx.getBlcyzd();
          String blryzd = brxx.getBlryzd();
          String diagInfoStr;
          if (StrUtil.isNotBlank(blcyzd)) {
            diagInfoStr = bzmlfController.getjbbmml("lczd", blcyzd, "cyzd");
          } else {
            diagInfoStr = bzmlfController.getjbbmml("lczd", blryzd, "ryzd");
          }
          if (StrUtil.isNotBlank(diagInfoStr)) {
            String[] diagInfos = diagInfoStr.split("\\|\\|");
            if (diagInfos.length > 0) {
              String[] diagCodes = diagInfos[0].split(",");
              String[] diagNames = diagInfos[1].split(",");
              for (int i = 0; i < diagCodes.length; i++) {
                Zdxx zdxx = new Zdxx();
                zdxx.setZdcode(diagCodes[i]);
                zdxx.setZdname(diagNames[i]);
                zdxx.setZdsort(i + 1);
                zdxx.setRybq(null);
                zdxxList.add(zdxx);
              }
            }
          }
        }
      }
    }

    if (zdxxList.size() > 0) {
      //过滤空数据
      zdxxList.removeIf(zdxx -> zdxx == null || StrUtil.isBlank(zdxx.getZdname()) || StrUtil.isBlank(zdxx.getZdcode()));
      //去重
      Set<Zdxx> zdxxSet = new TreeSet<>(Comparator.comparing(Zdxx::getZdcode));
      zdxxSet.addAll(zdxxList);
      zdxxList = new ArrayList<>(zdxxSet);
      //排序
      zdxxList.sort(Comparator.comparing(Zdxx::getZdsort));
      //通过反射将诊断依次填充到首页的字段中
      for (int i = 0; i < 16; i++) {
        String jbdmFieldName = "jbdm" + (i == 0 ? "" : (i + ""));
        String zdmcFieldName = i == 0 ? "zyzd" : "qtzd" + i + "";
        String rybqFieldName = "rybq" + (i == 0 ? "" : (i + ""));

        try {
          Field jbdmField = BaSyjl.class.getDeclaredField(jbdmFieldName);
          Field zdmcField = BaSyjl.class.getDeclaredField(zdmcFieldName);
          Field rybqField = BaSyjl.class.getDeclaredField(rybqFieldName);

          jbdmField.setAccessible(true);
          zdmcField.setAccessible(true);
          rybqField.setAccessible(true);

          if (i < zdxxList.size()) {
            jbdmField.set(basy, zdxxList.get(i).getZdcode());
            zdmcField.set(basy, zdxxList.get(i).getZdname());
            rybqField.set(basy, zdxxList.get(i).getRybq());
          } else {
            jbdmField.set(basy, null);
            zdmcField.set(basy, null);
            rybqField.set(basy, null);
          }
        } catch (NoSuchFieldException e) {
          logger.error("从诊断信息或病历信息获取首页诊断时出错，反射未找到对应变量");
        }
      }
    }
    return basy;
  }


  /**
   * 根据诊断类型和诊断来源筛选诊断
   * @param diagList
   * @param diagType
   * @param diagOrigin
   * @return
   */
  public List<Entity> findDiagsByTypeAndOrigin(List<Entity> diagList,String diagType,String diagOrigin) {
    return diagList.stream()
      .filter(diag -> (diagOrigin.equals(diag.getStr("jlly"))) && diagType.equals(diag.getStr("zdtype")))
      .collect(Collectors.toList());
  }

  /**
   * 给其他诊断默认排序
   * @param zdxxList
   * @return
   */
  public List<Zdxx> updateZdxxSort(List<Zdxx> zdxxList) {
    List<Zdxx> mainDiags = new ArrayList<>();
    List<Zdxx> otherDiags = new ArrayList<>();
    for (Zdxx diagnosis : zdxxList) {
      if (diagnosis.getZdsort() == 1) {
        mainDiags.add(diagnosis);
      } else {
        otherDiags.add(diagnosis);
      }
    }
    // 重排次要诊断的顺序
    int counter = 2;
    for (Zdxx diagnosis : otherDiags) {
      diagnosis.setZdsort(counter++);
    }
    zdxxList.clear();
    zdxxList.addAll(mainDiags);
    zdxxList.addAll(otherDiags);
    return zdxxList;
  }

  /**
   * 同步icd9ybdy和icd10ybdy
   */
  @Transactional
  @Override
  public Integer syncIcdHis() {
    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    List<Entity> icd10HisListSou = dataSyncService.executeQuery("icd10_his", new HashMap<>());
    List<Icd10ybdy> icd10HisTar = new ArrayList<>();
    for (Entity icd10His : icd10HisListSou) {
      Icd10ybdy icd10ybdy = new Icd10ybdy();
      icd10ybdy.setBzbm(icd10His.getStr("bzbm"));
      icd10ybdy.setBzmc(icd10His.getStr("bzmc"));
      icd10ybdy.setYbbzbm(icd10His.getStr("ybbzbm"));
      icd10ybdy.setYbbzmc(icd10His.getStr("ybbzmc"));
      icd10HisTar.add(icd10ybdy);
    }
    icd10ybdyService.clearIcd10();
    icd10ybdyService.syncIcd10(icd10HisTar);
    icd10ybdyService.clearIcd10His();
    icd10ybdyService.syncIcd10His(icd10HisTar);


    List<Entity> icd9HisListSou = dataSyncService.executeQuery("icd9_his", new HashMap<>());
    List<Icd9ybdy> icd9HisTar = new ArrayList<>();
    for (Entity icd9His : icd9HisListSou) {
      Icd9ybdy icd9ybdy = new Icd9ybdy();
      icd9ybdy.setBm(icd9His.getStr("bm"));
      icd9ybdy.setMc(icd9His.getStr("mc"));
      icd9ybdy.setYbbm(icd9His.getStr("ybbm"));
      icd9ybdy.setYbmc(icd9His.getStr("ybmc"));
      icd9HisTar.add(icd9ybdy);
    }

    icd9ybdyService.clearIcd9();
    icd9ybdyService.syncIcd9(icd9HisTar);
    icd9ybdyService.clearIcd9His();
    icd9ybdyService.syncIcd9His(icd9HisTar);

    icd10ybdyService.uspUpdateBzAndSsmlDz();

    return 1;
  }


  /**
   * 同步医生
   */
  @Override
  @Transactional
  public Integer syncDoctor() {

    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    List<Entity> doctorListHis;
    if ("1".equals(hasMultipleHis)) {

      List<Entity> list = new ArrayList<>();

      boolean hasNullSource = false;

      for (SysTenant org : orgList) {

        if (StringUtils.isNotBlank(org.getSourcename())) {
          List<Entity> entityList = dataSyncService.executeQuery(getSqlName("ysxx", org.getTenantId()), new HashMap<>());
          list.addAll(entityList);
        } else {
          hasNullSource = true;
        }

      }

      if (hasNullSource || list.size() == 0) {
        list.addAll(dataSyncService.executeQuery("ysxx", new HashMap<>()));
      }

      doctorListHis = list;

    } else {
      doctorListHis = dataSyncService.executeQuery("ysxx", new HashMap<>());
    }


    for (Entity doctorHis : doctorListHis) {
      String hisid = doctorHis.getStr("hisid");
      String name = doctorHis.getStr("user_name");
      String sfz = doctorHis.getStr("sfz");
      String code = doctorHis.getStr("code");
      String nccd = doctorHis.getStr("nccd");
      String zc = doctorHis.getStr("zc");
      String gjcode = doctorHis.getStr("gjcode");
      String jgid = doctorHis.containsKey("jgid") ? doctorHis.getStr("jgid") : null;

      if ("管理员".equals(name) || StrUtil.isBlank(hisid)) {
        continue;
      }

      SysUser sysUser = sysUserMapper.getUserByName(name);

      Long deptId;
      String deptHisid = doctorHis.containsKey("dept_id") ? doctorHis.getStr("dept_id") : null;
      System.out.println("-----------------------deptHisid:" + deptHisid);
      if (deptHisid == null) {
        deptId = 100L;
      } else {
        deptId = sysDeptMapper.selectDeptIdByHisid(deptHisid);
      }


      String password = "$2a$10$RgR6VhYd7X0/c8rRM/eMC.vrlY9aFNp/eV5ETRZ2vcdqrkrVwx1LO";
      SysUser user = new SysUser();
      user.setUserName(name);
      user.setSfz(sfz);
      user.setCode(code);
      user.setHisid(hisid);
      user.setZc(zc);
      user.setGjcode(gjcode);
      user.setJgid(jgid);
      user.setDeptId(deptId);
      user.setNickName(name);
      user.setNccd(nccd);
      if (sysUser.getNum() > 0) {
        user.setUserId(sysUser.getUserId());
        sysUserMapper.updateUser(user);
      } else {
        user.setSex("1");
        user.setPassword(password);
        user.setStatus("0");
        user.setCreateBy("admin");
        sysUserMapper.insertUser(user);
      }

      sysUser = sysUserMapper.getUserByName(name);
      Long userMaxId = sysUser.getUserId();
      HUserDept hUserDept = new HUserDept();
      hUserDept.sethDeptId(deptId);
      hUserDept.sethUserId(userMaxId);
      List<HUserDept> hUserDepts = ihUserDeptService.selectHUserDeptList(hUserDept);
      if (hUserDepts.size() == 0) {
        hUserDept.setJgid(jgid);
        ihUserDeptService.insertHUserDept(hUserDept);
      }

    }
    sysUserRoleMapper.setCommonRole();
    sysUserRoleMapper.setDoctorRole();

    return 1;
  }


  @Override
  @Transactional
  public Integer syncDept() {

    if ("".equals(is_hissoftname)) {
      getHissoftName();
    }

    List<Entity> deptListHis;
    if ("1".equals(hasMultipleHis)) {

      List<Entity> list = new ArrayList<>();

      boolean hasNullSource = false;

      for (SysTenant org : orgList) {

        if (StringUtils.isNotBlank(org.getSourcename())) {
          List<Entity> entityList = dataSyncService.executeQuery(getSqlName("ksxx", org.getTenantId()), new HashMap<>());
          list.addAll(entityList);
        } else {
          hasNullSource = true;
        }

      }

      if (hasNullSource || list.size() == 0) {
        list.addAll(dataSyncService.executeQuery("ksxx", new HashMap<>()));
      }

      deptListHis = list;

    } else {
      deptListHis = dataSyncService.executeQuery("ksxx", new HashMap<>());
    }

    ihDeptService.deleteHisKs();
    for (Entity deptHis : deptListHis) {
      String hisid = deptHis.getStr("dept_id");
      String code = deptHis.getStr("code");
      String name = deptHis.getStr("dept_name");
      String nccd = deptHis.getStr("nccd");
      Long parentid = deptHis.getLong("parent_id");
      String jgid = deptHis.getStr("jgid");

      SysDept sysDept = new SysDept();
      sysDept.setHisid(hisid);
      sysDept.setCode(code);
      sysDept.setDeptName(name);
      sysDept.setParentId(parentid);
      sysDept.setNccd(nccd);
      sysDept.setJgid(jgid);

      ihDeptService.insertHisKs(sysDept);
    }
    ihDeptService.insertKsDz();
    ihDeptService.updateKsDz();
    ihDeptService.clearDept();
    ihDeptService.clearHDept();
    ihDeptService.insertSysDeptByHisKs();
    ihDeptService.insertHDeptBySysDept();

    return 1;
  }


  @Override
  public void syncBasyForCondition(String sqlStr) {

    List<Entity> list = dataSyncService.executeQuery(sqlStr, new HashMap<>());

    for (Entity entity : list) {
      String brid = entity.getStr("brid");
      String zyid = entity.getStr("zyid");
      basySync(brid + "_" + zyid, null);

    }

  }

  //重新同步分组失败的病案
  @Override
  public void syncFzErrBasy() {
    List<String> list = baSyjlService.selectFzErrBasy();
    for (String brbs : list) {
      basySync(brbs, null);
    }
  }


  public String getSqlName(String oldSqlName,String jgid) {
    String newSqlName = oldSqlName;
    if ("1".equals(hasMultipleHis) &&  StringUtils.isNotBlank(jgid)) {
      for (SysTenant org : orgList) {
        if (jgid.equals(org.getTenantId()) && StringUtils.isNotBlank(org.getSourcename())) {
          newSqlName = oldSqlName + "_" + org.getSourcename();
          break;
        }
      }
    }
    return newSqlName;
  }



  public void syncAllBasy() {
    List<BaSyjl> list = baSyjlService.selectBaSyjlList(new BaSyjl());
    for (BaSyjl syjl : list) {
      basySync(syjl.getBrbs(),null);
    }
  }

  @Override
  public void syncBasyForRepeat() {
    List<BaSyjl> list = baSyjlService.selectRepeatBasy();
    for (BaSyjl syjl : list) {
      basySync(syjl.getBrbs(),syjl.getUsername());
    }
  }


  private void addOperLog(String title,String result) {
    SysOperLog operLog = new SysOperLog();
    operLog.setJsonResult(result);
    operLog.setTitle(title);
    operLog.setMethod("");
    operLog.setRequestMethod("");
    operLog.setOperatorType(0);
    operLog.setOperTime(new Date());
    operLog.setOperName("admin");
    operLog.setOperUrl("/sync");
    operLog.setOperIp("127.0.0.1");
    operLog.setOperLocation("内网");
    operLogService.insertOperlog(operLog);

  }
}
