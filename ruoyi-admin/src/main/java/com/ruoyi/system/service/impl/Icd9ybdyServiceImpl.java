package com.ruoyi.system.service.impl;

import cn.hutool.db.Entity;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.Icd9ybdy;
import com.ruoyi.system.domain.vo.CompareIcdHisVo;
import com.ruoyi.system.domain.vo.IcdMaintainQueryVo;
import com.ruoyi.system.domain.vo.IcdMaintainVo;
import com.ruoyi.system.domain.vo.IcdUpdateVo;
import com.ruoyi.system.mapper.Icd10ybdyMapper;
import com.ruoyi.system.mapper.Icd9ybdyMapper;
import com.ruoyi.system.service.IIcd9ybdyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * icd9ybdyService业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Service
public class Icd9ybdyServiceImpl implements IIcd9ybdyService {
  @Autowired
  private Icd9ybdyMapper icd9ybdyMapper;

  @Autowired
  private Icd10ybdyMapper icd10ybdyMapper;

  @Override
  public List<CompareIcdHisVo> compareIcd9His(CompareIcdHisVo compareIcdHisVo) {
    return icd9ybdyMapper.compareIcd9His(compareIcdHisVo);
  }

  @Override
  public void syncIcd9His(List<Icd9ybdy> icd9ybdyList) {
    icd9ybdyMapper.syncIcd9His(icd9ybdyList);
  }

  @Override
  public void syncIcd9(List<Icd9ybdy> icd9ybdyList) {
    icd9ybdyMapper.syncIcd9(icd9ybdyList);
  }

  @Override
  public void clearIcd9() {
    icd9ybdyMapper.clearIcd9();
  }

  @Override
  public void clearIcd9His() {
    icd9ybdyMapper.clearIcd9His();
  }

  /**
   * 查询icd9ybdy
   *
   * @param bm icd9ybdy主键
   * @return icd9ybdy
   */
  @Override
  public Icd9ybdy selectIcd9ybdyByBm(String bm) {
    return icd9ybdyMapper.selectIcd9ybdyByBm(bm);
  }

  /**
   * 查询icd9ybdy列表
   *
   * @param icd9ybdy icd9ybdy
   * @return icd9ybdy
   */
  @Override
  public List<Icd9ybdy> selectIcd9ybdyList(Icd9ybdy icd9ybdy) {
    return icd9ybdyMapper.selectIcd9ybdyList(icd9ybdy);
  }

  /**
   * 新增icd9ybdy
   *
   * @param icd9ybdy icd9ybdy
   * @return 结果
   */
  @Override
  public int insertIcd9ybdy(Icd9ybdy icd9ybdy) {
    return icd9ybdyMapper.insertIcd9ybdy(icd9ybdy);
  }

  /**
   * 修改icd9ybdy
   *
   * @param icd9ybdy icd9ybdy
   * @return 结果
   */
  @Override
  public int updateIcd9ybdy(Icd9ybdy icd9ybdy) {
    return icd9ybdyMapper.updateIcd9ybdy(icd9ybdy);
  }

  /**
   * 批量删除icd9ybdy
   *
   * @param bms 需要删除的icd9ybdy主键
   * @return 结果
   */
  @Override
  public int deleteIcd9ybdyByBms(String[] bms) {
    return icd9ybdyMapper.deleteIcd9ybdyByBms(bms);
  }

  /**
   * 删除icd9ybdy信息
   *
   * @param bm icd9ybdy主键
   * @return 结果
   */
  @Override
  public int deleteIcd9ybdyByBm(String bm) {
    return icd9ybdyMapper.deleteIcd9ybdyByBm(bm);
  }

  @Override
  public List<IcdMaintainVo> selectIcd9List(IcdMaintainQueryVo queryVo) {
    return icd9ybdyMapper.selectIcd9List(queryVo);
  }

  @Override
  public void updateIcd9Info(IcdUpdateVo vo) {
    icd9ybdyMapper.updateIcd9Info(vo);
  }

  @Override
  public void icdAdd(IcdUpdateVo vo) {
    Icd9ybdy icd9 = new Icd9ybdy();
    icd9.setBm(vo.getYybm());
    icd9.setYbbm(vo.getYbbm());

    List<Icd9ybdy> list = icd9ybdyMapper.selectIcd9ybdyList(icd9);
    if (!list.isEmpty()) {
      throw new ServiceException("该对照已存在");
    }

    icd9.setMc(vo.getYymc());
    icd9.setHm(vo.getHm().longValue());
    icd9.setYbmc(vo.getYbmc());

    // 生成拼音码
    String yymcNccd = icd10ybdyMapper.getNccd(vo.getYymc());

    icd9.setNccd(yymcNccd);

    // 写入cmi
    icd9.setCmi(icd10ybdyMapper.getCmi(vo.getYbbm()));

    // 设置并发症标识
    icd9.setCcflag(icd10ybdyMapper.getCCFlag(vo.getYbbm()));

//    icd10ybdyMapper.icdAdd(vo);
    icd9ybdyMapper.insertIcd9ybdy(icd9);

  }

  @Override
  public void insertIcd9UpdateAndInsertByEntity(List<Entity> icd9List) {
    icd9ybdyMapper.insertIcd9UpdateAndInsertByEntity(icd9List);
  }

  @Override
  public void usp_update_icd10and9_ybdy() {
    icd9ybdyMapper.usp_update_icd10and9_ybdy();
  }

  @Override
  public void deleteAllIcd9() {
    icd9ybdyMapper.deleteAllIcd9();
  }


}
