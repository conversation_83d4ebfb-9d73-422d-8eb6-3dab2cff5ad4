package com.ruoyi.components.validation;

import com.ruoyi.components.annotation.DeptCheck;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class DeptValidate implements ConstraintValidator<DeptCheck, String> {
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return false;
    }

    @Override
    public void initialize(DeptCheck constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }
}
