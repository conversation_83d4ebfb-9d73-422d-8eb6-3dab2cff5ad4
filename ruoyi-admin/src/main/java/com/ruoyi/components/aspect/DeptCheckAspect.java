package com.ruoyi.components.aspect;

import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.components.annotation.DeptCheck;
import com.ruoyi.framework.web.service.TokenService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.List;

@Aspect
@Component
public class DeptCheckAspect {

    private final TokenService tokenService;

    @Autowired
    public DeptCheckAspect(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Around("execution(* com.ruoyi.system.controller..*(..)) && @annotation(com.ruoyi.components.annotation.DeptCheck)")
    public Object checkDept(ProceedingJoinPoint joinPoint) throws Throwable {
        System.out.println("---------触发科室参数检查-----------");
        HttpServletRequest req = getCurrentHttpRequest();
        LoginUser loginUser = tokenService.getLoginUser(req);
        SysUser user = loginUser.getUser();
        Object[] args = joinPoint.getArgs();

        for (Object arg : args) {
            Field[] fields = arg.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(DeptCheck.class)) {
                    field.setAccessible(true);
                    //如果是医生
                    if (isDoctor(user.getRoles())) {
                        field.set(arg, user.getDept().getDeptName());
                    }
                }
            }
        }

        return joinPoint.proceed();
    }

    //判断是否是医生角色
    private boolean isDoctor(List<SysRole> roles){
        return roles.stream().anyMatch(item -> "ys".equals(item.getRoleKey()) && roles.size() == 1);
    }

    private HttpServletRequest getCurrentHttpRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }

}
