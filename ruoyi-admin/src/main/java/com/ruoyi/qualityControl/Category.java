package com.ruoyi.qualityControl;

import java.util.HashMap;
import java.util.Map;

public class Category {

    private final Map<String, String> points;

    public Category(){
        this.points = new HashMap<>();
    }

    public Category addPoint(String pointName, String content){
        this.points.put(pointName, content);
        return this;
    }

    public String getContent(String point){
        return this.points.get(point);
    }

    public void removePoint(String pointName){
        this.points.remove(pointName);
    }

}
