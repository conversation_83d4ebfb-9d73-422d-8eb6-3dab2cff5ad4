package com.ruoyi.qualityControl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

public class RuleEngine {

    private final List<Rule> ruleList;

    public RuleEngine() {
        this.ruleList = new ArrayList<>();
    }

    public RuleEngine addRule(Rule rule) {
        ruleList.add(rule);
        return this;
    }

    public RuleEngine removeRule(Rule rule) {
        ruleList.remove(rule);
        return this;
    }

    public ValidationResult executeRules(Document document){

        ExecutorService executor = Executors.newCachedThreadPool();
        List<Future<ValidationResult>> futureList = new ArrayList<>();

        for (Rule rule : ruleList) {
            Callable<ValidationResult> task = () -> rule.validate(document);
            Future<ValidationResult> submit = executor.submit(task);
            futureList.add(submit);
        }

        executor.shutdown();


        ValidationResult finalResult = new ValidationResult();
        for (Future<ValidationResult> future : futureList) {
            try {
                ValidationResult result = future.get();
                // 合并结果
                finalResult.merge(result);
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        }

        return finalResult;
    }

}
