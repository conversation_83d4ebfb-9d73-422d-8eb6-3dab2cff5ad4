package com.ruoyi.qualityControl;

import java.util.*;
import java.util.stream.Collectors;

public class MRContentParser {

    public static Category parse(String content, List<String> keywords){

        Category category = new Category();


        List<Map.Entry<String, Integer>> keywordPositions = getPosition(content, keywords);

//        Map<String, String> result = new LinkedHashMap<>();
        // 遍历排序后的列表
        for (int i = 0; i < keywordPositions.size(); i++) {
            Map.Entry<String, Integer> entry = keywordPositions.get(i);
            String keyword = entry.getKey();
            int start = entry.getValue();
            int end = (i < keywordPositions.size() - 1) ? keywordPositions.get(i + 1).getValue() : content.length();
            // 从长文本中截取从当前位置到下一个位置的子字符串
            String value = content.substring(start + keyword.length(), end).trim();
            // 将关键词和子字符串作为一个键值对添加到结果Map中
//            result.put(keyword, value);
            category.addPoint(keyword, value);
        }

        return category;
    }

    private static List<Map.Entry<String, Integer>> getPosition(String content, List<String> keywords){
        List<Map.Entry<String, Integer>> keywordPositions = new ArrayList<>();

        for (String keyword : keywords) {
            int position = content.indexOf(keyword);
            if (position != -1) {
                keywordPositions.add(new AbstractMap.SimpleEntry<>(keyword, position));
            }
        }

        // 将列表按照位置排序
        keywordPositions.sort(Map.Entry.comparingByValue());

        return keywordPositions;
    }

}
