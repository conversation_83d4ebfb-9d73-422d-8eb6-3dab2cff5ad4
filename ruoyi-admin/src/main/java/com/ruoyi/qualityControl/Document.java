package com.ruoyi.qualityControl;

import java.util.HashMap;
import java.util.Map;

public class Document {

    private final Map<String, Category> categories;

    public Document() {
        this.categories = new HashMap<>();
    }

    public Document addCategory(String categoryName, Category category){
        this.categories.put(categoryName, category);
        return this;
    }

    public Category getCategory(String categoryName){
        return this.categories.get(categoryName);
    }

}
