package com.ruoyi.qualityControl.rule;

import com.ruoyi.qualityControl.Category;
import com.ruoyi.qualityControl.Document;
import com.ruoyi.qualityControl.Rule;
import com.ruoyi.qualityControl.ValidationResult;

import java.math.BigDecimal;

public class WordLimitRule implements Rule {

    private String pointName;

    private String categoryName;

    private String ruleValue;

    @Override
    public ValidationResult validate(Document document) {

        ValidationResult result = new ValidationResult();

        Category category = document.getCategory(categoryName);
        String content = category.getContent(pointName);
        if(content.length() > Integer.parseInt(ruleValue)){
            // todo 此处需要以规则内的分处和信息进行扣分和提示
            result.updateScore(new BigDecimal("-1"));
            result.addMessage(pointName + "字数超过限制");
        }

        return result;
    }

    public String getPointName() {
        return pointName;
    }

    public void setPointName(String pointName) {
        this.pointName = pointName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(String ruleValue) {
        this.ruleValue = ruleValue;
    }
}
