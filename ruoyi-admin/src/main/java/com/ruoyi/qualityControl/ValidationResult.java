package com.ruoyi.qualityControl;

import com.sun.xml.bind.v2.util.StackRecorder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ValidationResult {

    //影响分值，负多少就是扣多少分
    private BigDecimal score;

    // 结果描述列表
    private final List<String> message;

    public ValidationResult() {
        this.score = new BigDecimal("0");
        this.message = new ArrayList<>();
    }

    public ValidationResult addMessage(String message) {
        this.message.add(message);
        return this;
    }

    public List<String> getMessage() {
        return this.message;
    }

    public ValidationResult updateScore(BigDecimal score) {
        this.score = this.score.add(score);
        return this;
    }

    public BigDecimal getScore() {
        return this.score;
    }

    public ValidationResult merge(ValidationResult validationResult) {
        this.message.addAll(validationResult.getMessage());
        this.score = this.score.add(validationResult.getScore());
        return this;
    }

}
