<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>病案校验页面</title>
  <style>
    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      text-align: center;
      border-bottom: solid 1px #ccc;
      border-right: solid 1px #ccc;
      height: 35px;
      font-weight: normal;
    }

    th {
      background-color: #f2f2f2;
    }

    tr:nth-child(even) {
      background-color: #f2f2f2;
    }


    .brxx {
      margin-top: 15px;
      border-top: solid 1px #ccc;
      border-left: solid 1px #ccc;
      width: 100%;
      border-spacing: 0px;
    }

    .brxx td {
      text-align: center;
      border-bottom: solid 1px #ccc;
      border-right: solid 1px #ccc;
      height: 35px;
      font-weight: normal;
    }


    * {
      font-family: Consolas, '幼圆';
    }
  </style>
</head>
<body>

<div>
  <div id="msg">
    正在校验中...
  </div>
  <div>
    <div>
      <table class="brxx" id="brxx" style="width: 100%">

      </table>
    </div>


    <table class="checkRecords" id="checkRecords" style="margin-top: 20px;" border="1">
    </table>
    <table class="tipRecords" id="tipRecords" style="margin-top: 20px;" border="1">
    </table>
  </div>
</div>


<script>
  function ifNull(inputStr) {
    return inputStr != null ? inputStr : '';
  }

  window.onload = function () {
    let params = window.location.search
    if (params == null || params == "" || params == undefined) {
      document.getElementById("msg").innerText = '请输入参数！';
      return
    }
    let msg = "";
    let brxx = {};
    let checkRecords = [];
    let tipRecords = [];
    let apiUrl = "/drg/baxy/getbajyData";
    apiUrl = apiUrl + params;

    fetch(apiUrl)
      .then(response => {
        if (!response.ok) {
          document.getElementById("msg").innerText = '';
          throw new Error('请求失败！');
        }
        return response.json();
      })
      .then((responseData) => {
        console.log(responseData)
        if (responseData.hasOwnProperty("msg")) {
          msg = responseData.msg;
          document.getElementById("msg").innerText = msg;
        } else {
          document.getElementById("msg").innerText = '';
          if (responseData.hasOwnProperty("brxx")) {
            brxx = responseData.brxx;
            let brxxStr = "";
            brxxStr = "" +
              "<tr>" +
              "          <td style=\"background-color: #f8f8f9\">姓名</td>" +
              "          <td style=\"min-width: 40px\">" + ifNull(brxx.xm) + "</td>" +
              "          <td style=\"background-color: #f8f8f9\">住院号</td>" +
              "          <td style=\"min-width: 40px\">" + ifNull(brxx.bah) + "</td>" +
              "          <td style=\"background-color: #f8f8f9\">性别</td>" +
              "          <td style=\"min-width: 40px\">" + ifNull(brxx.xb) + "</td>" +
              "          <td style=\"background-color: #f8f8f9\">年龄</td>" +
              "          <td style=\"min-width: 40px\">" + ifNull(brxx.nl) + "</td>" +
              "        </tr>" +
              "" +
              "        <tr>" +
              "          <td style=\"background-color: #f8f8f9\">住院科室</td>" +
              "          <td colspan=\"2\">" + ifNull(brxx.cykb == null ? brxx.rykb : brxx.cykb) + "</td>" +
              "          <td style=\"background-color: #f8f8f9\">住院医生</td>" +
              "          <td colspan=\"2\">" + ifNull(brxx.zyys) + "</td>" +
              "          <td style=\"background-color: #f8f8f9\">住院次数</td>" +
              "          <td colspan=\"2\">" + ifNull(brxx.zycs) + "</td>" +
              "        </tr>\n" +
              "" +
              "        <tr>" +
              "          <td style=\"background-color: #f8f8f9\">入院时间</td>" +
              "          <td colspan=\"2\">" + ifNull(brxx.rydateStr) + "</td>" +
              "          <td style=\"background-color: #f8f8f9\">出院时间</td>" +
              "          <td colspan=\"4\">" + ifNull(brxx.zyzt == 1 ? '' : brxx.cydateStr) + "</td>" +
              "        </tr>"

            document.getElementById("brxx").innerHTML = brxxStr;
          }
          if (responseData.hasOwnProperty("checkRecords")) {
            checkRecords = responseData.checkRecords;
            let checkRecordStr = "<thead>" +
              "      <tr>" +
              "        <th>序号</th>" +
              "        <th>质控对象</th>" +
              "        <th>质控信息</th>" +
              "      </tr>" +
              "      </thead>";

            for (let i = 0; i < checkRecords.length; i++) {
              checkRecordStr += "" +
                "<tr>" +
                "   <td style='width: 5%'>" + (i + 1) + "</td>" +
                "   <td style='width: 15%'>" + ifNull(checkRecords[i].tipObject) + "</td>" +
                "   <td style='width: 80%'>" + ifNull(checkRecords[i].errordes) + "</td>" +
                "</tr>"
            }
            document.getElementById("checkRecords").innerHTML = checkRecordStr;
          }
          if (responseData.hasOwnProperty("tipRecords")) {
            tipRecords = responseData.tipRecords;
            let tipRecordStr = "<thead>" +
              "      <tr>" +
              "        <th>序号</th>" +
              "        <th>提示对象</th>" +
              "        <th>提示信息</th>" +
              "      </tr>" +
              "      </thead>";

            for (let i = 0; i < tipRecords.length; i++) {
              tipRecordStr += "" +
                "<tr>" +
                "   <td style='width: 5%'>" + (i + 1) + "</td>" +
                "   <td style='width: 15%'>" + ifNull(tipRecords[i].tipObject) + "</td>" +
                "   <td style='width: 80%'>" + ifNull(tipRecords[i].errordes) + "</td>" +
                "</tr>"
            }
            document.getElementById("tipRecords").innerHTML = tipRecordStr;


          }
        }
      })
      .catch(error => {
        document.getElementById("msg").innerText = '';
        throw new Error(error);
      });
  };
</script>
</body>
</html>
