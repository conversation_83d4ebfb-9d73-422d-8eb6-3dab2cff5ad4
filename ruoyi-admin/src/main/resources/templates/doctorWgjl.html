<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>医生违规记录</title>
  <style>
    :root {
      --el-color-primary: #409EFF;
      --el-color-success: #67C23A;
      --el-color-warning: #E6A23C;
      --el-color-danger: #F56C6C;
      --el-color-info: #909399;
      --el-border-color-light: #DCDFE6;
      --el-border-color: #E4E7ED;
      --el-text-color-primary: #303133;
      --el-text-color-regular: #606266;
      --el-bg-color: #F5F7FA;
      --el-font-size-base: 14px;
      --el-border-radius-base: 4px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    }

    body {
      color: var(--el-text-color-regular);
      font-size: var(--el-font-size-base);
      background-color: #fff;
      padding: 20px;
    }

    .el-container {
      max-width: 100%;
      margin: 0 auto;
    }

    .el-header {
      margin-bottom: 20px;
    }

    .el-main {
      padding: 0;
    }

    .el-card {
      border-radius: var(--el-border-radius-base);
      border: 1px solid var(--el-border-color-light);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      overflow: hidden;
      transition: all .3s;
    }

    .el-card-header {
      padding: 15px 20px;
      border-bottom: 1px solid var(--el-border-color-light);
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;
      color: var(--el-text-color-primary);
      font-weight: bold;
    }

    .el-card-body {
      padding: 20px;
      overflow-x: auto;
    }

    .el-table {
      width: 100%;
      border-collapse: collapse;
      font-size: var(--el-font-size-base);
      border: 1px solid var(--el-border-color-light);
      border-radius: var(--el-border-radius-base);
      overflow: hidden;
      table-layout: auto;
    }

    .el-table thead tr {
      background-color: var(--el-bg-color);
      color: var(--el-text-color-regular);
      font-weight: 500;
    }

    .el-table th,
    .el-table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid var(--el-border-color-light);
      position: relative;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .el-table th {
      white-space: nowrap;
    }

    .el-table td {
      color: var(--el-text-color-primary);
    }

    .el-table-striped .el-table__row:nth-child(even) {
      background-color: #FAFAFA;
    }

    .el-table__row:hover {
      background-color: #F5F7FA;
    }

    /* 针对特定内容的列宽调整 */
    .el-table .cell-short {
      white-space: nowrap;
      min-width: 60px;
    }

    .el-table .cell-medium {
      min-width: 80px;
    }

    .el-table .cell-long {
      min-width: 120px;
    }

    /* 医保备注限制或适应症的显示样式 */
    .el-table .ybbz-cell {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 限制最多显示2行 */
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.5;
      max-height: 3em; /* 约等于2行的高度 */
    }

    /* 表格内容鼠标悬浮提示样式 */
    .el-tooltip {
      cursor: pointer;
      position: relative;
    }

    .el-tooltip:hover::after {
      content: attr(data-tooltip);
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      white-space: normal;
      max-width: 300px;
      z-index: 1000;
    }

    .el-button {
      display: inline-block;
      line-height: 1;
      white-space: nowrap;
      cursor: pointer;
      background: #fff;
      border: 1px solid var(--el-border-color);
      color: var(--el-text-color-regular);
      text-align: center;
      box-sizing: border-box;
      outline: none;
      transition: .1s;
      font-weight: 500;
      padding: 9px 15px;
      font-size: var(--el-font-size-base);
      border-radius: var(--el-border-radius-base);
    }

    .el-button--primary {
      color: #fff;
      background-color: var(--el-color-primary);
      border-color: var(--el-color-primary);
    }

    .el-button--primary:hover,
    .el-button--primary:focus {
      background: #66b1ff;
      border-color: #66b1ff;
    }

    .el-button--success {
      color: #fff;
      background-color: var(--el-color-success);
      border-color: var(--el-color-success);
    }

    .el-button--success:hover,
    .el-button--success:focus {
      background: #85ce61;
      border-color: #85ce61;
    }

    .el-form {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 15px;
    }

    .el-form-item {
      display: flex;
      align-items: center;
      margin-bottom: 0;
    }

    .el-select {
      height: 36px;
      line-height: 36px;
      background-color: #fff;
      border: 1px solid var(--el-border-color);
      border-radius: var(--el-border-radius-base);
      padding: 0 15px;
      margin: 0 10px 0 5px;
      color: var(--el-text-color-regular);
    }

    .el-divider {
      height: 1px;
      margin: 20px 0;
      background-color: var(--el-border-color-light);
    }

    .el-alert {
      padding: 10px 15px;
      margin-bottom: 15px;
      border-radius: var(--el-border-radius-base);
      display: flex;
      align-items: center;
      background-color: #f0f9eb;
      color: #67c23a;
    }

    .el-alert--warning {
      background-color: #fdf6ec;
      color: #e6a23c;
    }

    .el-alert--error {
      background-color: #fef0f0;
      color: #f56c6c;
    }

    .el-pagination {
      text-align: right;
      margin-top: 15px;
    }

    @media screen and (max-width: 768px) {
      .el-form {
        flex-direction: column;
        align-items: flex-start;
      }

      .el-form-item {
        width: 100%;
        margin-bottom: 10px;
      }

      .el-table {
        font-size: 12px;
      }

      .el-table th,
      .el-table td {
        padding: 8px;
      }
    }
  </style>
</head>
<body>
<div class="el-container">
  <div class="el-main">
    <div class="el-card">
      <div class="el-card-header">筛选条件</div>
      <div class="el-card-body">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <form id="filterForm" class="el-form">
            <div class="el-form-item">
              <span>科室：</span>
              <select name="department" id="departmentSelect" class="el-select">
                <option value="">请选择科室</option>
                <option th:each="department : ${departments}"
                        th:value="${department.deptname}"
                        th:text="${department.deptname}"></option>
              </select>
            </div>
            <div class="el-form-item">
              <span>医生：</span>
              <select name="doctor" id="doctorSelect" class="el-select">
                <option value="">请选择医生</option>
              </select>
            </div>
            <button type="button" name="query" id="queryButton" class="el-button el-button--primary">查询</button>
          </form>
          <form id="exportForm" action="/gksz/fysh/exportDoctorWgjl" method="get">
            <button type="submit" class="el-button el-button--success">导出数据</button>
          </form>
        </div>
      </div>
    </div>

    <div th:if="${not #strings.isEmpty(msg)}" class="el-alert el-alert--warning">
      <p th:text="${msg}"></p>
    </div>

    <div th:if="${wgjlList != null}" class="el-card">
      <div class="el-card-header">医生违规记录</div>
      <div class="el-card-body">
        <div style="overflow-x: auto;">
          <table class="el-table el-table-striped" style="table-layout: fixed;">
            <colgroup>
              <col style="width: 60px;"> <!-- 序号 -->
              <col style="width: 80px;"> <!-- 住院号 -->
              <col style="width: 80px;"> <!-- 姓名 -->
              <col style="width: 25%;"> <!-- 违规内容 -->
              <col style="width: 10%;"> <!-- 费用项目名称 -->
              <col style="width: 60px;"> <!-- 床号 -->
              <col style="width: 60px;"> <!-- 数量 -->
              <col style="width: 80px;"> <!-- 金额 -->
              <col style="width: 80px;"> <!-- 科室 -->
              <col style="width: 80px;"> <!-- 医生 -->
              <col style="width: 120px;"> <!-- 创建时间 -->
              <col style="width: 15%;"> <!-- 医保备注限制或适应症 -->
            </colgroup>
            <thead>
            <tr>
              <th>序号</th>
              <th>住院号</th>
              <th>姓名</th>
              <th>违规内容</th>
              <th>费用项目名称</th>
              <th>床号</th>
              <th>数量</th>
              <th>金额</th>
              <th>科室</th>
              <th>医生</th>
              <th>创建时间</th>
              <th>医保备注限制或适应症</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="wgjl, index : ${wgjlList}" class="el-table__row">
              <td th:text="${index.index + 1}" style="white-space: nowrap;"></td>
              <td th:text="${wgjl.zyh}" style="white-space: nowrap;"></td>
              <td th:text="${wgjl.brname}" style="white-space: nowrap;"></td>
              <td th:text="${wgjl.jklog}"
                  th:style="${wgjl.xzlb != null && wgjl.xzlb != '' && wgjl.xzlb != '#FFFFFF' ? 'color: ' + wgjl.xzlb : ''}">
              </td>
              <td th:text="${wgjl.fymName}"></td>
              <td th:text="${wgjl.bed}" style="white-space: nowrap;"></td>
              <td th:text="${wgjl.sl}" style="white-space: nowrap;"></td>
              <td th:text="${wgjl.je}" style="white-space: nowrap;"></td>
              <td th:text="${wgjl.kdksname}" style="white-space: nowrap;"></td>
              <td th:text="${wgjl.doctorname}" style="white-space: nowrap;"></td>
              <td th:text="${wgjl.createDate}" style="white-space: nowrap;"></td>
              <td>
                <div th:text="${wgjl.ybbz}" th:title="${wgjl.ybbz}" class="ybbz-cell el-tooltip" th:attr="data-tooltip=${wgjl.ybbz}"></div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.getElementById("exportForm").addEventListener("submit", function(event) {
    // 阻止默认提交行为
    event.preventDefault();

    // 获取 URL 参数
    const urlParams = new URLSearchParams(window.location.search);
    console.log(urlParams.toString())
    const param = urlParams.toString();

    // 重写表单的 action URL，附带上地址栏参数
    let actionUrl = this.action;
    if (param) {
      actionUrl += `?${param}`;
    }

    // 使用新 URL 提交表单
    window.location.href = actionUrl;
  });

  document.getElementById('departmentSelect').addEventListener('change', function(event) {
    var departmentName = this.value;
    var jgid = getQueryStr('jgid');

    var doctorSelect = document.getElementById('doctorSelect');
    doctorSelect.innerHTML = '<option value="">请选择医生</option>';

    if (departmentName) {
      var url = '/mlcx/brxx/selectDoctorByDept?deptname=' + departmentName;
      if(jgid != null && jgid !== '') {
        url = url + `&jgid=${jgid}`
      }
      // 获取医生
      fetch(url)
        .then(response => response.json())
        .then(data => {
          data.rows.forEach(function(doctor) {
            console.log(doctor)
            var option = document.createElement('option');
            option.value = doctor.doctorname;
            option.text = doctor.doctorname;
            doctorSelect.appendChild(option);
          });
        });
    }
  });

  document.getElementById('queryButton').addEventListener('click', function() {
    // 获取选中的医生
    var doctorSelect = document.getElementById('doctorSelect');
    var departmentSelect = document.getElementById('departmentSelect');
    var jgid = getQueryStr('jgid');
    var deptName = departmentSelect.value;
    var doctorName = doctorSelect.value;

    if (deptName) {
      getWgjl(deptName, doctorName, jgid, zyzt, startDate, endDate);
    } else {
      alert("请选择科室！");
    }
  });

  function getWgjl(deptName, doctorName, jgid, zyzt, startDate, endDate) {
    let bq = getQueryStr('bq');
    if (bq == null || bq === '' || bq === 'null') {
      bq = getBq(deptName);
    }
    var url = `/gksz/fysh/getDoctorWgjl?deptName=${deptName}&doctorName=${doctorName}&bq=${bq}&zyzt=${zyzt}&createDateStart=${startDate}&createDateEnd=${endDate}`;
    if(jgid != null && jgid !== '') {
      url = url + `&jgid=${jgid}`
    }
    window.location.href = url
  }

  function getBq(deptName) {
    //如果传入的deptName包含括号，返回括号前的内容
    if (deptName.includes("(")) {
      return deptName.substring(0, deptName.indexOf("("));
    } else if (deptName.includes("（")) {
      return deptName.substring(0, deptName.indexOf("（"));
    } else {
      return deptName;
    }
  }

  function getQueryStr(name) {
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    var r = window.location.search.substr(1).match(reg);
    if (r != null) {
      return unescape(r[2]);
    }
    return null;
  }
</script>
</body>
</html>
