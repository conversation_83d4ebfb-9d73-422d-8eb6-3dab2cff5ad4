<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8">
  <title>违规记录</title>
  <style>
    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    th {
      background-color: #f2f2f2;
    }

    tr:nth-child(even) {
      background-color: #f2f2f2;
    }

    tr:hover {
      background-color: #ddd;
    }

    .flat-button {
      background-color: #3498db;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s;
      margin: 20px;
      float: right;
    }

    .flat-button:hover {
      background-color: #2980b9;
    }


    * {
      font-family:  Consolas,'幼圆';
    }
  </style>
</head>
<body>

<table border="1">
  <thead>
  <tr>
    <th>序号</th>
    <th>姓名</th>
    <th>违规内容</th>
    <th>医保备注限制或适应症</th>
  </tr>
  </thead>
  <tbody>
  <tr th:each="wgjl, index : ${wgjlList}">
    <td th:text="${index.index + 1}"></td>
    <td th:text="${wgjl.brname}"></td>
    <td th:text="${wgjl.jklog}"
        th:style="${wgjl.xzlb != null && wgjl.xzlb != '' && wgjl.xzlb != '#FFFFFF' ? 'color: ' + wgjl.xzlb : ''}"
        width="30%">
    </td>
    <td th:text="${wgjl.ybbz}"></td>
  </tr>
  </tbody>
</table>

<button class="flat-button" onclick="go()">继续</button>
<button class="flat-button" onclick="back()">返回</button>

<script>
  function go() {
    window.opener.postMessage("1", '*');
    // window.close();
  }
  function back() {
    window.opener.postMessage("0", '*');
    // window.close();
  }
</script>

</body>
</html>
