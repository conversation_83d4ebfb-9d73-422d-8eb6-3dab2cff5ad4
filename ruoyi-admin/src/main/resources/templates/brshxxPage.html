<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>病人基本信息与审核信息</title>
  <style>
    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      text-align: center;
      border-bottom: solid 1px #ccc;
      border-right: solid 1px #ccc;
      height: 35px;
      font-weight: normal;
    }

    th {
      background-color: #f2f2f2;
    }

    tr:nth-child(even) {
      background-color: #f2f2f2;
    }


    .brxx {
      margin-top: 15px;
      border-top: solid 1px #ccc;
      border-left: solid 1px #ccc;
      width: 100%;
      border-spacing: 0px;
    }

    .brxx td {
      text-align: center;
      border-bottom: solid 1px #ccc;
      border-right: solid 1px #ccc;
      height: 35px;
      font-weight: normal;
    }


    * {
      font-family: Consolas, '幼圆';
    }
  </style>
</head>
<body>

<script>

  function ifNull(inputStr) {
    return inputStr != null ? inputStr : '';
  }

  // 获取URL参数
  function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return decodeURIComponent(r[2]); return null;
  }

  // 格式化日期函数
  function formatDate(date) {
    var year = date.getFullYear();
    var month = (date.getMonth() + 1).toString().padStart(2, '0');
    var day = date.getDate().toString().padStart(2, '0');
    var hours = date.getHours().toString().padStart(2, '0');
    var minutes = date.getMinutes().toString().padStart(2, '0');
    var seconds = date.getSeconds().toString().padStart(2, '0');
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
  }

  // 不再提醒功能
  function excludeWgjl(wgid) {
    var operName = getUrlParam('operName') || '';
    var currentDate = formatDate(new Date());
    console.log(wgid, '行数据')

    // 构建URL并添加查询参数
    var url = '/system/wgjlHistory/excludeWgjl' +
      '?excludeUser=' + encodeURIComponent(operName) +
      '&excludeDate=' + encodeURIComponent(currentDate) +
      '&id=' + encodeURIComponent(wgid)

    fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'same-origin'
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('请求失败！');
      }
      return response.json();
    })
    .then(data => {
      console.log(data)
      alert('该记录已标记为不违规');
      // 刷新页面
      location.reload();
    })
    .catch(error => {
      console.error('提交异常：', error);
      alert('操作失败');
    });
  }

  window.onload = function () {
    let msg = "";
    let brxx = {};
    let wgjlList = [];
    let apiUrl = "/gksz/fysh/getBrShxxData";
    // 添加控制变量
    let showExcludeButton = false; // 控制是否显示不再提醒按钮
    console.log(window.location.search)
    apiUrl = apiUrl + window.location.search;

    fetch("/drg/option/wgjl_exclude", {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'same-origin'
    })
      .then(response => {
        if(response.ok) {
          return response.json();
        }
      })
      .then(res => {
        console.log(res)
        if (res.hasOwnProperty("data")) {
          let data = res.data;
          if( data && data.cValue == '1') {
            showExcludeButton = true;
          }
        }
      })

    fetch(apiUrl)
      .then(response => {
        if (!response.ok) {
          document.getElementById("msg").innerText = '';
          throw new Error('请求失败！');
        }
        return response.json();
      })
      .then((responseData) => {
        console.log(responseData)
        if (responseData.hasOwnProperty("msg")) {
          msg = responseData.msg;
          document.getElementById("msg").innerText = msg;
        } else {
          document.getElementById("msg").innerText = '';
          if (responseData.hasOwnProperty("wgjlList")) {
            wgjlList = responseData.wgjlList;
            let wgjlListStr = "<thead>" +
              "      <tr>" +
              "        <th>序号</th>" +
              "        <th>违规内容</th>" +
              "        <th>医保备注限制或适应症</th>" +
              (showExcludeButton ? "        <th>操作</th>" : "") +
              "      </tr>" +
              "      </thead>";

            for (let i = 0; i < wgjlList.length; i++) {
              if (wgjlList[i].xzlb == '#FFFFFF' || wgjlList[i].xzlb == null || wgjlList[i].xzlb == '') {
                wgjlListStr += "" +
                  "<tr>" +
                  "   <td>" + (i + 1) + "</td>" +
                  "   <td>" + ifNull(wgjlList[i].jklog) + "</td>" +
                  "   <td>" + ifNull(wgjlList[i].ybbz) + "</td>" +
                  (showExcludeButton ? "   <td><button onclick='excludeWgjl(\"" + wgjlList[i].id + "\")' style='padding: 4px 8px; background-color: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;'>确认未违规</button></td>" : "") +
                  "</tr>"
              } else {
                wgjlListStr += "" +
                  "<tr>" +
                  "   <td>" + (i + 1) + "</td>" +
                  "   <td style='" + "color:" + wgjlList[i].xzlb + "'>" + ifNull(wgjlList[i].jklog) + "</td>" +
                  "   <td>" + ifNull(wgjlList[i].ybbz) + "</td>" +
                  (showExcludeButton ? "   <td><button onclick='excludeWgjl(\"" + wgjlList[i].id + "\")' style='padding: 4px 8px; background-color: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;'>确认未违规</button></td>" : "") +
                  "</tr>"
              }
            }
            document.getElementById("wgjlList").innerHTML = wgjlListStr;


          }
          if (responseData.hasOwnProperty("brxx")) {
            brxx = responseData.brxx;
            let brxxStr = "";
            brxxStr = "" +
            "<tr>" +
            "          <td style=\"background-color: #f8f8f9\">姓名</td>" +
            "          <td style=\"min-width: 40px\">" + ifNull(brxx.name) + "</td>" +
            "          <td style=\"background-color: #f8f8f9\">住院号</td>" +
            "          <td style=\"min-width: 40px\">" + ifNull(brxx.zyh) + "</td>" +
            "          <td style=\"background-color: #f8f8f9\">性别</td>" +
            "          <td style=\"min-width: 40px\">" + ifNull(brxx.sex) + "</td>" +
            "          <td style=\"background-color: #f8f8f9\">年龄</td>" +
            "          <td style=\"min-width: 40px\">" + ifNull(brxx.age) + "</td>" +
            "        </tr>" +
            "" +
            "        <tr>" +
            "          <td style=\"background-color: #f8f8f9\">住院科室</td>" +
            "          <td colspan=\"3\">" + ifNull(brxx.deptname) + "</td>" +
            "          <td style=\"background-color: #f8f8f9\">住院医生</td>" +
            "          <td colspan=\"3\">" + ifNull(brxx.doctorname) + "</td>" +
            "        </tr>\n" +
            "" +
            "        <tr>" +
            "          <td style=\"background-color: #f8f8f9\">入院时间</td>" +
            "          <td colspan=\"2\">" + ifNull(brxx.rydateStr) + "</td>" +
            "          <td style=\"background-color: #f8f8f9\">出院时间</td>" +
            "          <td colspan=\"4\">" + ifNull(brxx.cydateStr) + "</td>" +
              "        </tr>"

            document.getElementById("brxx").innerHTML = brxxStr;


          }
        }
      })
      .catch(error => {
        document.getElementById("msg").innerText = '';
        throw new Error(error);
      });
  };
</script>


<div>
  <div id="msg">
    正在审核中...
  </div>
  <div>
    <div>
      <table class="brxx" id="brxx" style="width: 100%">

      </table>
    </div>


    <table class="wgjlList" id="wgjlList" style="margin-top: 20px;" border="1">
    </table>
  </div>
</div>

</body>

</html>
