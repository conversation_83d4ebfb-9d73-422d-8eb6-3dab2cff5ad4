<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.clinicalPath.mapper.PathWayMapper">

  <resultMap type="PathWay" id="PathWayResult">
    <result property="id" column="id"/>
    <result property="name" column="name"/>
    <result property="version" column="version"/>
    <result property="icd10Main" column="icd10_main"/>
    <result property="icd10Second" column="icd10_second"/>
    <result property="icd10Other" column="icd10_other"/>
    <result property="icd10Pc" column="icd10_pc"/>
    <result property="icd9" column="icd9"/>
    <result property="icd9Pc" column="icd9_pc"/>
    <result property="content" column="content"/>
    <result property="minAge" column="min_age"/>
    <result property="maxAge" column="max_age"/>
    <result property="blzd" column="blzd"/>
    <result property="needIcd9" column="need_icd9"/>
    <result property="status" column="status"/>
  </resultMap>

  <sql id="selectPathWayVo">
      select id,
             name,
             version,
             icd10_main,
             icd10_second,
             icd10_other,
             icd10_pc,
             icd9,
             icd9_pc,
             min_age,
             max_age,
             blzd,
             need_icd9,
             status
      from path_way
  </sql>


  <select id="selectPathWayList" parameterType="PathWay" resultMap="PathWayResult">
    <include refid="selectPathWayVo"/>
    <where>
      <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
      <if test="version != null  and version != ''">and version = #{version}</if>
      <if test="icd10Main != null  and icd10Main != ''">and icd10_main like concat('%', #{icd10Main}, '%')</if>
      <if test="icd10Second != null  and icd10Second != ''">and icd10_second = #{icd10Second}</if>
      <if test="icd10Other != null  and icd10Other != ''">and icd10_other like concat('%', #{icd10Other}, '%')</if>
      <if test="icd10Pc != null  and icd10Pc != ''">and icd10_pc = #{icd10Pc}</if>
      <if test="icd9 != null  and icd9 != ''">and icd9 like concat('%', #{icd9}, '%')</if>
      <if test="icd9Pc != null  and icd9Pc != ''">and icd9_pc = #{icd9Pc}</if>
      <if test="content != null  and content != ''">and content like concat('%', #{content}, '%')</if>
      <if test="minAge != null ">and min_age = #{minAge}</if>
      <if test="maxAge != null ">and max_age = #{maxAge}</if>
      <if test="blzd != null  and blzd != ''">and blzd = #{blzd}</if>
      <if test="needIcd9 != null  and needIcd9 != ''">and need_icd9 = #{needIcd9}</if>
      <if test="status != null  and status != ''">and status = #{status}</if>
    </where>
  </select>


  <select id="searchPath" parameterType="String" resultMap="PathWayResult">
    select name,content
    from path_way
    where name like concat('%',#{name},'%') and status = '1'
  </select>

  <select id="selectPathWayList2" parameterType="PathWay" resultMap="PathWayResult">
    select id,
    name,
    version,
    icd10_main,
    icd10_second,
    icd10_other,
    icd10_pc,
    icd9,
    icd9_pc,
    min_age,
    max_age,
    blzd,
    need_icd9,
    status
    from path_way
    <where>
      <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
      <if test="version != null  and version != ''">and version = #{version}</if>
      <if test="icd10Main != null  and icd10Main != ''">and icd10_main like concat('%', #{icd10Main}, '%')</if>
      <if test="icd10Second != null  and icd10Second != ''">and icd10_second = #{icd10Second}</if>
      <if test="icd10Other != null  and icd10Other != ''">and icd10_other like concat('%', #{icd10Other}, '%')</if>
      <if test="icd10Pc != null  and icd10Pc != ''">and icd10_pc = #{icd10Pc}</if>
      <if test="icd9 != null  and icd9 != ''">and icd9 like concat('%', #{icd9}, '%')</if>
      <if test="icd9Pc != null  and icd9Pc != ''">and icd9_pc = #{icd9Pc}</if>
      <if test="content != null  and content != ''">and content like concat('%', #{content}, '%')</if>
      <if test="minAge != null ">and min_age = #{minAge}</if>
      <if test="maxAge != null ">and max_age = #{maxAge}</if>
      <if test="blzd != null  and blzd != ''">and blzd = #{blzd}</if>
      <if test="needIcd9 != null  and needIcd9 != ''">and need_icd9 = #{needIcd9}</if>
      <if test="status != null  and status != ''">and status = #{status}</if>
    </where>
  </select>

  <select id="selectContentById" parameterType="Long" resultType="String">
    select content from path_way where id = #{id}
  </select>



  <select id="selectPathWayById" parameterType="Long" resultType="PathWay">
    <include refid="selectPathWayVo"/>
    where id = #{id}
  </select>

  <select id="selectPathPdfCon" parameterType="Long" resultType="java.io.InputStream">
    select pdf from path_way_pdf
    where id = #{id}
  </select>


  <insert id="insertPathWay" parameterType="PathWay" useGeneratedKeys="true" keyProperty="id">
    insert into path_way
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null and name != ''">name,</if>
      <if test="version != null and version != ''">version,</if>
      <if test="icd10Main != null">icd10_main,</if>
      <if test="icd10Second != null">icd10_second,</if>
      <if test="icd10Other != null">icd10_other,</if>
      <if test="icd10Pc != null">icd10_pc,</if>
      <if test="icd9 != null">icd9,</if>
      <if test="icd9Pc != null">icd9_pc,</if>
      <if test="content != null">content,</if>
      <if test="minAge != null">min_age,</if>
      <if test="maxAge != null">max_age,</if>
      <if test="blzd != null">blzd,</if>
      <if test="needIcd9 != null">need_icd9,</if>
      <if test="status != null">status,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null and name != ''">#{name},</if>
      <if test="version != null and version != ''">#{version},</if>
      <if test="icd10Main != null">#{icd10Main},</if>
      <if test="icd10Second != null">#{icd10Second},</if>
      <if test="icd10Other != null">#{icd10Other},</if>
      <if test="icd10Pc != null">#{icd10Pc},</if>
      <if test="icd9 != null">#{icd9},</if>
      <if test="icd9Pc != null">#{icd9Pc},</if>
      <if test="content != null">#{content},</if>
      <if test="minAge != null">#{minAge},</if>
      <if test="maxAge != null">#{maxAge},</if>
      <if test="blzd != null">#{blzd},</if>
      <if test="needIcd9 != null">#{needIcd9},</if>
      <if test="status != null">#{status},</if>
    </trim>
  </insert>

  <update id="updatePathWay" parameterType="PathWay">
    update path_way
    <trim prefix="SET" suffixOverrides=",">
      <if test="name != null and name != ''">name = #{name},</if>
      <if test="version != null and version != ''">version = #{version},</if>
      <if test="icd10Main != null">icd10_main = #{icd10Main},</if>
      <if test="icd10Second != null">icd10_second = #{icd10Second},</if>
      <if test="icd10Other != null">icd10_other = #{icd10Other},</if>
      <if test="icd10Pc != null">icd10_pc = #{icd10Pc},</if>
      <if test="icd9 != null">icd9 = #{icd9},</if>
      <if test="icd9Pc != null">icd9_pc = #{icd9Pc},</if>
      <if test="minAge != null">min_age = #{minAge},</if>
      <if test="maxAge != null">max_age = #{maxAge},</if>
      <if test="blzd != null">blzd = #{blzd},</if>
      <if test="needIcd9 != null">need_icd9 = #{needIcd9},</if>
      <if test="status != null">status = #{status},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deletePathWayById" parameterType="Long">
    delete
    from path_way
    where id = #{id}
  </delete>

  <delete id="deletePathWayByIds" parameterType="String">
    delete from path_way where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
