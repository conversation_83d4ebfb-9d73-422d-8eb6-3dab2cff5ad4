<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.clinicalPath.mapper.LcljFyxmMapper">

  <resultMap type="LcljFyxm" id="LcljFyxmResult">
    <result property="pathId" column="pathId"/>
    <result property="drgbh" column="drgbh"/>
    <result property="pcssbm" column="pcssbm"/>
    <result property="bzbm" column="bzbm"/>
    <result property="ssbm" column="ssbm"/>
    <result property="sybrs" column="sybrs"/>
    <result property="zbrs" column="zbrs"/>
    <result property="sygxmzb" column="sygxmzb"/>
    <result property="xmbm" column="xmbm"/>
    <result property="xmmc" column="xmmc"/>
    <result property="fykmname" column="fykmname"/>
    <result property="rpjyl" column="rpjyl"/>
    <result property="pjfy" column="pjfy"/>
    <result property="syzts" column="syzts"/>
    <result property="scsyts" column="scsyts"/>
    <result property="fyzb" column="fyzb"/>
    <result property="pjyl" column="pjyl"/>
    <result property="fyzsyxmdzb" column="fyzsyxmdzb"/>
    <result property="pjzyf" column="pjzyf"/>
    <result property="zl" column="zl"/>
    <result property="ypf" column="ypf"/>
    <result property="jyf" column="jyf"/>
    <result property="jcf" column="jcf"/>
    <result property="hcf" column="hcf"/>
    <result property="ssf" column="ssf"/>
    <result property="zlf" column="zlf"/>
    <result property="adtFrom" column="adt_from"/>
    <result property="adtTo" column="adt_to"/>
    <result property="xmtype" column="xmtype"/>
    <result property="zfbz" column="zfbz"/>
    <result property="selectStatus" column="select_status"/>
  </resultMap>

  <resultMap type="LcljFyxmSh" id="LcljFyxmShResult">
    <id property="id" column="id"/>
    <result property="zfbz" column="zfbz"/>
    <result property="drgbh" column="drgbh"/>
    <result property="drgmc" column="drgmc"/>
    <result property="bzbm" column="bzbm"/>
    <result property="bzmc" column="bzmc"/>
    <result property="ssbm" column="ssbm"/>
    <result property="pcssbm" column="pcssbm"/>
    <result property="status" column="status"/>
    <result property="remark" column="remark"/>
    <result property="submitStatus" column="submit_status"/>
    <result property="createDate" column="createdate"/>
    <result property="submitDate" column="submitdate"/>
    <collection property="fyxmList" ofType="LcljFyxm">
      <result property="pathId" column="pathId"/>
      <result property="sybrs" column="sybrs"/>
      <result property="zbrs" column="zbrs"/>
      <result property="sygxmzb" column="sygxmzb"/>
      <result property="xmbm" column="xmbm"/>
      <result property="xmmc" column="xmmc"/>
      <result property="fykmname" column="fykmname"/>
      <result property="rpjyl" column="rpjyl"/>
      <result property="pjfy" column="pjfy"/>
      <result property="syzts" column="syzts"/>
      <result property="scsyts" column="scsyts"/>
      <result property="fyzb" column="fyzb"/>
      <result property="pjyl" column="pjyl"/>
      <result property="fyzsyxmdzb" column="fyzsyxmdzb"/>
      <result property="pjzyf" column="pjzyf"/>
      <result property="zl" column="zl"/>
      <result property="ypf" column="ypf"/>
      <result property="jyf" column="jyf"/>
      <result property="jcf" column="jcf"/>
      <result property="hcf" column="hcf"/>
      <result property="ssf" column="ssf"/>
      <result property="zlf" column="zlf"/>
      <result property="adtFrom" column="adt_from"/>
      <result property="adtTo" column="adt_to"/>
      <result property="xmtype" column="xmtype"/>
    </collection>
  </resultMap>

  <select id="selectLcljFyxmList" parameterType="LcljFyxmSh" resultMap="LcljFyxmResult">
    call usp_make_lclj_fyxx(#{drgbh},#{bzbm},#{ssbm},#{cyStartDate},#{cyEndDate},#{pcssbm},#{id},'admin');
  </select>

  <select id="selectLcljFyxmByPathId" parameterType="String" resultMap="LcljFyxmResult">
    select *
    from lclj_fyxm
    where pathId = #{pathId}
  </select>

  <select id="selectLcljItem" parameterType="LcljFyxmSh" resultType="LcljFyxmSh">
    select * from lclj_fyxm_sh
    where drgbh = #{drgbh}
      and bzbm = #{bzbm}
      and (case when #{ssbm} is null or #{ssbm} = '' then ssbm is null else ssbm = #{ssbm} end)
      and (case when #{pcssbm} is null or #{pcssbm} = '' then pcssbm is null else pcssbm = #{pcssbm} end)
      and submit_status = #{submitStatus}
    limit 1
  </select>

  <select id="selectLcljFyxmShById" parameterType="LcljFyxmSh" resultType="LcljFyxmSh">
    select id,drgbh,bzbm,ssbm,pcssbm,status,remark,submit_status as submitStatus,createdate as createDate,submitdate as submitDate  from lclj_fyxm_sh where id = #{id}
  </select>

  <update id="setResourceConsumption" parameterType="String">
    call usp_set_zyxh(#{pathId})
  </update>

  <select id="selectLcljFyxmByzd" parameterType="LcljFyxmSh" resultMap="LcljFyxmResult">
    SELECT
           distinct
           xmbm     as xmbm,
           xmmc     as xmmc,
           scsyts   as scsyts,
           syzts    as syzts,
           rpjyl    as pjyl,
           pjfy     as pjfy,
           a.drgbh  as drgbh,
           b.xmtype as xmtype
    FROM lclj_fyxm_sh a,
         lclj_fyxm b
    WHERE a.id = b.pathId
      AND a.submit_status = 1
      AND left(bzbm, 5) = #{bzbm}
      AND a.drgbh = (SELECT MAX(drgbh) FROM lclj_fyxm_sh WHERE left(bzbm, 5) = #{bzbm} and submit_status = 1)
    ORDER BY drgbh, scsyts
  </select>

  <insert id="saveLcljFyxmList" parameterType="java.util.List">
    INSERT INTO lclj_fyxm (
    pathId, sybrs, zbrs, sygxmzb, xmbm, xmmc, fykmname, rpjyl, pjfy, syzts, scsyts,fyzb,pjyl, fyzsyxmdzb, pjzyf, zl,
    ypf,jyf, jcf,
    ssf, zlf, hcf,xmtype
    ) VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.pathId}, #{item.sybrs}, #{item.zbrs}, #{item.sygxmzb}, #{item.xmbm}, #{item.xmmc},
      #{item.fykmname}, #{item.rpjyl}, #{item.pjfy}, #{item.syzts}, #{item.scsyts},
      #{item.fyzb}, #{item.pjyl}, #{item.fyzsyxmdzb}, #{item.pjzyf}, #{item.zl}, #{item.ypf},
      #{item.jyf}, #{item.jcf}, #{item.ssf}, #{item.zlf}, #{item.hcf},#{item.xmtype}
      )
    </foreach>
  </insert>

  <insert id="saveLclj" parameterType="LcljFyxmSh">
    INSERT INTO lclj_fyxm_sh (id, drgbh, bzbm, ssbm, pcssbm, status, submit_status, createdate, submitDate)
    VALUES (#{id}, #{drgbh}, #{bzbm}, #{ssbm}, #{pcssbm}, 0, #{submitStatus}, #{createDate}, #{submitDate})
  </insert>

  <update id="updateLcljFyxmStatus" parameterType="LcljFyxmSh">
    update lclj_fyxm_sh
    set status = #{status},
        remark = #{remark}
    where id = #{id}
  </update>

  <update id="updateLcljSubmitStatus" parameterType="LcljFyxmSh">
    update lclj_fyxm_sh
    set submit_status = #{submitStatus},
        submitdate = #{submitDate}
    where id = #{id}
  </update>

  <delete id="deleteLclj" parameterType="LcljFyxmSh">
    delete
    from lclj_fyxm_sh
    where drgbh = #{drgbh}
    and bzbm = #{bzbm}
    and (case when #{ssbm} is null or #{ssbm} = '' then ssbm is null else ssbm = #{ssbm} end)
    and (case when #{pcssbm} is null or #{pcssbm} = '' then pcssbm is null else pcssbm = #{pcssbm} end)
    <if test="id != null and id != ''">and id = #{id}</if>
  </delete>

  <delete id="deleteLcljFyxmByPathId" parameterType="String">
    delete
    from lclj_fyxm
    where pathId = #{pathId}
  </delete>

  <delete id="deleteLcljById" parameterType="String">
    delete
    from lclj_fyxm_sh
    where id = #{id}
  </delete>

  <select id="selectLcljList" parameterType="LcljFyxmSh" resultMap="LcljFyxmShResult">
    SELECT a.*, b.*
    FROM lclj_fyxm_sh a
    LEFT JOIN lclj_fyxm b ON a.id = b.pathId
    <where>
      <if test="id != null and status != ''">and a.id = #{id}</if>
      <if test="status != null and status != ''">and status = #{status}</if>
      <if test="bzbm != null  and bzbm != ''">and bzbm = #{bzbm}</if>
      <if test="drgbh != null  and drgbh != ''">and drgbh = #{drgbh}</if>
      <if test="ssbm != null  and ssbm != ''">and ssbm = #{ssbm}</if>
      <if test="pcssbm != null  and pcssbm != ''">and pcssbm = #{pcssbm}</if>
      <if test="submitStatus != null  and submitStatus != ''">and submit_status = #{submitStatus}</if>
      <if test="startCreateDate != null  and startCreateDate != ''">and createdate >= #{startCreateDate}</if>
      <if test="endCreateDate != null  and endCreateDate != ''">and createdate &lt;= #{endCreateDate}</if>
      <if test="startSubmitDate != null  and startSubmitDate != ''">and submitdate >= #{startSubmitDate}</if>
      <if test="endSubmitDate != null  and endSubmitDate != ''">and submitdate &lt;= #{endSubmitDate}</if>
    </where>
  </select>


  <select id="selectLcljLShist" parameterType="LcljFyxmSh" resultMap="LcljFyxmShResult">
    SELECT a.*, b.drgmc,c.bzmc,
    CASE
    WHEN a.jgid = 'admin' OR a.jgid = '' or a.jgid is null THEN b.zfbz
    ELSE
    CASE (SELECT yyjb FROM sys_tenant WHERE tenant_id = a.jgid)
    WHEN '2' THEN b.zfbz2
    WHEN '3' THEN b.zfbz3
    ELSE b.zfbz
    END
    END AS zfbz
    FROM lclj_fyxm_sh a
    LEFT JOIN drgdict b ON a.drgbh = b.drgbh
    LEFT JOIN icd10ybdy c ON a.bzbm = c.bzbm
    <where>
      <if test="id != null and status != ''">and a.id = #{id}</if>
      <if test="status != null and status != ''">and a.status = #{status}</if>
      <if test="bzbm != null  and bzbm != ''">and a.bzbm = #{bzbm}</if>
      <if test="drgbh != null  and drgbh != ''">and a.drgbh = #{drgbh}</if>
      <if test="ssbm != null  and ssbm != ''">and a.ssbm = #{ssbm}</if>
      <if test="pcssbm != null  and pcssbm != ''">and a.pcssbm = #{pcssbm}</if>
      <if test="submitStatus != null  and submitStatus != ''">and a.submit_status = #{submitStatus}</if>
      <if test="startCreateDate != null  and startCreateDate != ''">and a.createdate >= #{startCreateDate}</if>
      <if test="endCreateDate != null  and endCreateDate != ''">and a.createdate &lt;= #{endCreateDate}</if>
      <if test="startSubmitDate != null  and startSubmitDate != ''">and submitdate >= #{startSubmitDate}</if>
      <if test="endSubmitDate != null  and endSubmitDate != ''">and a.submitdate &lt;= #{endSubmitDate}</if>
    </where>
  </select>



  <select id="selectLcljFyxmByLclj" parameterType="LcljFyxmSh" resultMap="LcljFyxmResult">
    select a.drgbh, a.bzbm, a.ssbm, a.pcssbm, b.*
    from lclj_fyxm_sh a
           join lclj_fyxm b on a.id = b.pathId
    where drgbh = #{drgbh}
      and bzbm = #{bzbm}
      and (case when #{ssbm} is null or #{ssbm} = '' then ssbm is null else ssbm = #{ssbm} end)
      and (case when #{pcssbm} is null or #{pcssbm} = '' then pcssbm is null else pcssbm = #{pcssbm} end)
      and submit_status = 1
  </select>

</mapper>
