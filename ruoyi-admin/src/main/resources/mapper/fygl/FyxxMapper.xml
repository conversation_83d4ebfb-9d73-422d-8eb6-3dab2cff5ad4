<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FyxxMapper">

  <resultMap type="Fyxx" id="FyxxResult">
    <result property="jzh" column="jzh"/>
    <result property="zyh" column="zyh"/>
    <result property="brid" column="brid"/>
    <result property="zyid" column="zyid"/>
    <result property="yyxmbm" column="yyxmbm"/>
    <result property="xmbm" column="xmbm"/>
    <result property="xmmc" column="xmmc"/>
    <result property="bed" column="bed"/>
    <result property="sl" column="sl"/>
    <result property="price" column="price"/>
    <result property="je" column="je"/>
    <result property="dw" column="dw"/>
    <result property="guige" column="guige"/>
    <result property="fydate" column="fydate"/>
    <result property="opdate" column="opdate"/>
    <result property="ksid" column="ksid"/>
    <result property="ksname" column="ksname"/>
    <result property="ysid" column="ysid"/>
    <result property="ysname" column="ysname"/>
    <result property="yzid" column="yzid"/>
    <result property="billno" column="billno"/>
    <result property="hisid" column="hisid"/>
    <result property="opname" column="opname"/>
    <result property="id" column="id"/>
    <result property="jzid" column="jzid"/>
    <result property="fykmname" column="fykmname"/>
  </resultMap>


  <resultMap type="FyfxVo" id="FyfxResult">
    <result property="syrszb" column="jzh"/>
    <result property="xmmc" column="xmmc"/>
    <result property="fykmname" column="fykmname"/>
    <result property="qtrpjje" column="je"/>
    <result property="gbrje" column="price"/>
    <result property="qtrpjts" column="ksid"/>
    <result property="gbrts" column="ksname"/>
    <result property="bz" column="ysid"/>
  </resultMap>


  <delete id="delProFeeItem" parameterType="BrzdFyxx">
    delete from drg_icddyxm where icdbh = #{bzbm} and xmbm = #{xmbm}
  </delete>

  <select id="selectExcessProjects" parameterType="Fyxx" resultMap="FyxxResult">
    SELECT xmmc,
           fykmname,
           MAX(price) AS price,
           SUM(sl)    AS sl,
           SUM(je)    AS je
    FROM fyxx
    WHERE fyxx.jzh = #{jzh}
      AND NOT EXISTS (SELECT 1
                      FROM lclj_resource_consume
                      WHERE lclj_resource_consume.xmmc = fyxx.xmmc
                        AND lclj_resource_consume.lclj_id = #{lcljId})
    GROUP BY xmmc,
             fykmname
    ORDER BY je DESC;
  </select>

  <select id="selectXmNumByJzh" parameterType="Fyxx" resultType="Double">
    SELECT ROUND(IFNULL(SUM(sl),0),2) as sl  FROM fyxx WHERE jzh= #{jzh}  AND xmbm = #{xmbm};
  </select>


  <sql id="selectFyxxVo">
    select distinct jzh, brid, zyid, yyxmbm, xmbm, xmmc, sl, price, je, dw, guige, fydate, opdate, ksid, ksname, ysid, ysname, opname, hisid, billno, yzid, fykmname, jzid from fyxx
  </sql>

  <select id="selectFyDySsNum" resultType="Integer" parameterType="Fyxx">
    select count(*)
    from drg_gdssdyxm
    where xmmc = #{xmmc}
      and sstype = '手术'
  </select>

  <select id="selectkmfyandbg" parameterType="Fyxx" resultMap="FyxxResult">
    call usp_get_brkmfy_and_bg(#{jzh})
  </select>

  <select id="selectzdlcljfy" parameterType="Fyxx" resultMap="FyfxResult">
    call usp_make_lclj_fyxx_onebr(#{jzh},'')
  </select>

  <select id="updateZybrKmfy">
    call usp_update_zybr_kmfy()
  </select>

  <select id="selectFeeCountByFydate" parameterType="Fyxx" resultType="Integer">
    select count(*) as cc from fyxx where jzh = #{jzh} and fydate &lt; #{fydate}
  </select>


  <select id="selectftpmaxdate" resultMap="FyxxResult">
    SELECT MAX(update_date) as opdate
    FROM HIS_FTPFILE
  </select>

  <select id="selectFyxxByBr" parameterType="Fyxx" resultMap="FyxxResult">
    SELECT xmmc, MAX(fykmname) as fykmname, MAX(price) AS price, SUM(sl) AS sl, SUM(je) AS je
    FROM fyxx
    WHERE jzh = #{jzh}
       OR (brid = #{brid} AND zyid = #{zyid})
    GROUP BY xmmc, fykmname
    order by je desc
  </select>

  <select id="selectFyxxcyByBr" parameterType="Fyxx" resultMap="FyxxResult">
    SELECT xmmc, MAX(fykmname) as fykmname, MAX(price) AS price, SUM(sl) AS sl, SUM(je) AS je
    FROM fyxx_cy
    WHERE jzh = #{jzh}
       OR (brid = #{brid} AND zyid = #{zyid})
    GROUP BY xmmc, fykmname
    order by je desc
  </select>

  <insert id="syncFyxx" parameterType="java.util.List">
    insert into fyxx
    (jzh,brid,zyid,yyxmbm,xmbm,xmmc,sl,price,je,dw,guige,fydate,opdate,ksid,ksname,ysid,ysname,yzid,billno,hisid,opname,jzid,fykmname)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.jzh},#{item.brid},#{item.zyid},#{item.yyxmbm},#{item.xmbm},#{item.xmmc},#{item.sl},
      #{item.price},#{item.je},#{item.dw},#{item.guige},#{item.fydate},#{item.opdate},
      #{item.ksid},#{item.ksname},#{item.ysid},#{item.ysname},#{item.yzid},#{item.billno},
      #{item.hisid},#{item.opname},#{item.jzid},#{item.fykmname})
    </foreach>
    on duplicate key update
    xmbm = values(xmbm),
    fydate = values(fydate),
    hisid = values(hisid);
  </insert>

  <select id="selectCount" resultType="integer">
    select count(*) as cc from fyxx where brid = #{lsBrid} and zyid = #{lsZyid}
  </select>


  <delete id="deleteFeeRepeatHisid" parameterType="java.util.List">
    delete from fyxx where hisid in
    <foreach collection="list" index="index" separator="," item="item" open="(" close=")">
      #{item.hisid}
    </foreach>
  </delete>

  <select id="selectOpdateByBrid" parameterType="Fyxx" resultType="Fyxx">
    select ifnull(max(opdate), '2022-01-01 00:00:01') as opdate
    from fyxx
    where brid = #{brid};
  </select>

  <select id="selectMaxopdatemzfyxx" parameterType="String" resultType="java.time.LocalDateTime">
    select ifnull(max(opdate),'2024-01-01 00:00:00') as ldt_from
    from fyxx_mz
    <where>
      <if test="jgid != null and jgid != ''"> and billno = #{jgid}</if>
    </where>
  </select>

  <select id="selectMaxOpdate" parameterType="Fyxx" resultType="Date">
    select ifnull(max(opdate), '2022-01-01 00:00:01') as opdate
    from fyxx
    where brid = #{brid} and zyid = #{zyid};
  </select>

   <select id="selectMaxmzOpdate" parameterType="Fyxx" resultType="Date">
    select ifnull(max(opdate), '2022-01-01 00:00:01') as opdate
    from fyxx_mz
    where brid = #{brid} and zyid = #{zyid};
  </select>

  <select id="selectZfyByJzh" parameterType="String" resultType="Double">
    select sum(je)
    from fyxx
    where jzh = #{jzh};
  </select>

  <select id="selectFyxxList" parameterType="Fyxx" resultMap="FyxxResult">
    <include refid="selectFyxxVo"/>
    <where>
      <if test="jzh != null and jzh != ''">and jzh = #{jzh}</if>
      <if test="xmbm != null  and xmbm != ''">and xmbm = #{xmbm}</if>
      <if test="xmmc != null  and xmmc != ''">and xmmc = #{xmmc}</if>
      <if test="sl != null ">and sl = #{sl}</if>
      <if test="price != null ">and price = #{price}</if>
      <if test="je != null ">and je = #{je}</if>
      <if test="dw != null  and dw != ''">and dw = #{dw}</if>
      <if test="guige != null  and guige != ''">and guige = #{guige}</if>
      <if
        test="params.beginFydate != null and params.beginFydate != '' and params.endFydate != null and params.endFydate != ''">
        and fydate between #{params.beginFydate} and #{params.endFydate}
      </if>
      <if test="opdate != null ">and opdate = #{opdate}</if>
      <if test="ksname != null  and ksname != ''">and ksname like concat('%', #{ksname}, '%')</if>
    </where>
    order by fydate desc
  </select>

  <select id="selectFyxxById" parameterType="Long" resultMap="FyxxResult">
    <include refid="selectFyxxVo"/>
    where id = #{id}
  </select>

  <select id="selectssfyxx" parameterType="Fyxx" resultMap="FyxxResult">
    call usp_get_bzssfyxx(#{brid},#{zyid},#{jzh},#{xmbm})
  </select>

  <select id="selectFyxxByJzh" parameterType="String" resultMap="FyxxResult">
    SELECT a.jzh    AS jzh,
           b.ksname AS ksname,
           b.xmbm   AS xmbm,
           b.xmmc   AS xmmc,
           b.sl     AS sl,
           b.price  AS price,
           b.je     AS je,
           b.ysname AS ysname,
           a.bed    AS bed,
           a.zyh    AS zyh,
           b.xmbm   as xmbm,
           b.xmmc   as xmmc,
           b.ysname as ysname,
           b.fydate as fydate,
           b.opdate as opdate
    FROM brxx a,
         fyxx b
    WHERE a.jzh = b.jzh
      AND a.jzh = #{jzh}
    order by b.fydate desc
  </select>

  <select id="selectLastFeeByPatientId" resultMap="FyxxResult">
    <include refid="selectFyxxVo"/>
    <where>
      and jzh = #{jzh}
    </where>
    order by fydate
    limit 1
  </select>

  <select id="selectPatientTotalCost" resultType="java.math.BigDecimal" parameterType="Fyxx">
    select ifnull(sum(je), 0.00) as je
    from fyxx
    where brid = #{brid}
      and zyid = #{zyid}
  </select>
    <select id="selectZslByXmbmArray" resultType="java.math.BigDecimal">
      select ifnull(sum(sl), 0) as zsl
      from fyxx
      <where>
        and jzh = #{jzh}
        and fydate &gt;= #{fydate}
        and fydate &lt;= #{endDate}
        <if test="array != null">
          and
            <foreach collection="array" item="bm" open="(" close=")" separator=" or ">
              xmbm = #{bm}
            </foreach>
        </if>
      </where>
    </select>
  <select id="selectMaxFydate" resultType="com.ruoyi.system.domain.Fyxx">
    select ifnull(max(fydate), '2022-01-01 00:00:00')
    from fyxx
    where brid = #{brid}
  </select>
    <select id="selectPreCheckFyxxByJzh" resultType="com.ruoyi.system.domain.Fyxx">
      SELECT a.jzh    AS jzh,
             MAX(b.ksname) AS ksname,
            CASE WHEN b.xmbm = "001203000010000-ABJB0001" OR  b.xmbm ="001203000010000-ABJA0001" OR b.xmbm ="003106030010000-310603001" OR  b.xmbm = "003106030020000-310603002" OR b.xmbm = "003106070060000-310607005" OR b.xmbm = "001203000010000-120300001"  THEN  '001203000010000-ABJB0001' ELSE b.xmbm END      AS xmbm,
             MAX(b.xmmc)   AS xmmc,
             SUM(b.sl)     AS sl,
             MIN(CASE WHEN b.price=0 AND b.sl>0 THEN ROUND(b.je/b.sl,2) ELSE   b.price END)  AS price,
             SUM(b.je)     AS je,
             MAX(b.ysname) AS ysname,
             MAX(a.bed)    AS bed,
             MAX(a.zyh)    AS zyh,
             MAX(b.xmbm)   AS xmbm,
             MAX(b.xmmc)   AS xmmc,
             MAX(b.ysname) AS ysname,
             MAX(b.dw) AS dw,
             MAX(b.fydate) AS fydate,
             MAX(b.opdate) AS opdate,
             MAX(yzid) AS yzid,
             SUBSTR(b.fydate, 1, 10) AS datess,
             max(b.fykmname) as fykmname,
              GROUP_CONCAT(CONCAT(ksname,'-',xmmc,'-',fydate,'-',ysname,'-',sl) ORDER BY fydate SEPARATOR 'CHAR(10) ') AS fymx
      FROM brxx a,
           fyxx b
      WHERE a.jzh = b.jzh
        AND a.jzh = #{jzh} and b.xmbm is not null
      GROUP BY b.jzh, CASE WHEN b.xmbm = "001203000010000-ABJB0001" OR  b.xmbm ="001203000010000-ABJA0001" OR b.xmbm ="003106030010000-310603001" OR  b.xmbm = "003106030020000-310603002" OR b.xmbm = "003106070060000-310607005" OR b.xmbm = "001203000010000-120300001"  THEN  '001203000010000-ABJB0001' ELSE b.xmbm END   , SUBSTR(b.fydate, 1, 10)
      HAVING sl > 0
      ORDER BY b.fydate DESC
    </select>
    <select id="selectFyxxUseTen" resultType="com.ruoyi.system.domain.Fyxx">
      select xmbm, xmmc, sum(je) as je, sum(sl) as sl, dw, brid, zyid, jzh
      from fyxx
      where brid = #{brid} and zyid = #{zyid}
      and fykmname NOT LIKE '麻醉费' AND xmmc NOT LIKE'护理费' AND fykmname NOT LIKE '注射费' AND fykmname NOT LIKE'护理费' AND fykmname NOT LIKE '床位费'  AND xmmc NOT LIKE '%床位%' AND  xmmc NOT LIKE '%空调%' AND xmmc NOT LIKE '%氯化钠注射液%'
	AND xmmc NOT LIKE '%葡萄糖注射液%' AND  xmmc NOT LIKE '%维生素%' AND xmmc NOT LIKE '%葡萄糖注射液%' 
	AND  xmmc NOT LIKE '%床位费%'
	AND xmmc NOT LIKE '%氯化钠注射液%'
	AND xmmc NOT LIKE '%静脉输液%'
	AND xmmc NOT LIKE '%电脑血糖监测%'
	AND xmmc NOT LIKE '%留置针%'
	AND xmmc NOT LIKE '%陪伴%'
	AND xmmc NOT LIKE '%护理%'
	AND xmmc NOT LIKE '%静脉注射%'
	AND xmmc NOT LIKE '%诊查费%'
	AND xmmc NOT LIKE '%诊察费%'
      group by xmmc
      order by je desc
      limit 35
    </select>
    <select id="selectHistoryCheckFyxx" resultType="com.ruoyi.system.domain.Fyxx">
      SELECT b.jzh    AS jzh,
             MAX(b.ksname) AS ksname,
             CASE WHEN b.xmbm = "001203000010000-ABJB0001" OR  b.xmbm ="001203000010000-ABJA0001" OR b.xmbm ="003106030010000-310603001" OR  b.xmbm = "003106030020000-310603002" OR b.xmbm = "003106070060000-310607005" OR b.xmbm = "001203000010000-120300001"  THEN  '001203000010000-ABJB0001' ELSE b.xmbm END      AS xmbm,
             MAX(b.xmmc)   AS xmmc,
             SUM(b.sl)     AS sl,
            MIN(CASE WHEN b.price=0 AND b.sl>0 THEN ROUND(b.je/b.sl,2) ELSE   b.price END)  AS price,
             SUM(b.je)     AS je,
             MAX(b.ysname) AS ysname,
             MAX(a.bed)    AS bed,
             MAX(a.zyh)    AS zyh,
             MAX(b.xmbm)   AS xmbm,
             MAX(b.xmmc)   AS xmmc,
             MAX(b.ysname) AS ysname,
             MAX(b.dw) AS dw,
             MAX(b.fydate) AS fydate,
             MAX(b.opdate) AS opdate,
              MAX(yzid) AS yzid,
             SUBSTR(b.fydate, 1, 10) AS datess,
             max(b.fykmname) as fykmname,
             GROUP_CONCAT(CONCAT(ksname,'-',xmmc,'-',fydate,'-',ysname,'-',sl) ORDER BY fydate SEPARATOR 'CHAR(10) ') AS fymx
      FROM
        brxx a,
        fyxx_cy b
      WHERE
        a.jzh = b.jzh
        and a.jzh = #{jzh} and b.xmbm is not null
      GROUP BY b.jzh, CASE WHEN b.xmbm = "001203000010000-ABJB0001" OR  b.xmbm ="001203000010000-ABJA0001" OR b.xmbm ="003106030010000-310603001" OR  b.xmbm = "003106030020000-310603002" OR b.xmbm = "003106070060000-310607005" OR b.xmbm = "001203000010000-120300001"  THEN  '001203000010000-ABJB0001' ELSE b.xmbm END   , SUBSTR(b.fydate, 1, 10)
      HAVING sl > 0
      ORDER BY b.fydate DESC
    </select>
  <select id="selectMzFyxxByJzh" resultType="com.ruoyi.system.domain.Fyxx">
    SELECT b.jzh    AS jzh,
           MAX(b.ksname) AS ksname,
           CASE WHEN b.xmbm = "001203000010000-ABJB0001" OR  b.xmbm ="001203000010000-ABJA0001" OR b.xmbm ="003106030010000-310603001" OR  b.xmbm = "003106030020000-310603002" OR b.xmbm = "003106070060000-310607005" OR b.xmbm = "001203000010000-120300001"  THEN  '001203000010000-ABJB0001' ELSE b.xmbm END      AS xmbm,
           MAX(b.xmmc)   AS xmmc,
           SUM(b.sl)     AS sl,
          MIN(CASE WHEN b.price=0 AND b.sl>0 THEN ROUND(b.je/b.sl,2) ELSE   b.price END)  AS price,
           SUM(b.je)     AS je,
           MAX(b.ysname) AS ysname,
           MAX(b.xmbm)   AS xmbm,
           MAX(b.xmmc)   AS xmmc,
           MAX(b.ysname) AS ysname,
           MAX(b.dw) AS dw,
           MAX(b.fydate) AS fydate,
           MAX(b.opdate) AS opdate,
           SUBSTR(b.fydate, 1, 10) AS datess,
           max(b.fykmname) as fykmname,
           GROUP_CONCAT(CONCAT(ksname,'-',xmmc,'-',fydate,'-',ysname,'-',sl) ORDER BY fydate SEPARATOR 'CHAR(10) ') AS fymx
    FROM fyxx_mz b
    WHERE b.jzh = #{jzh} and b.xmbm is not null
    GROUP BY b.jzh, CASE WHEN b.xmbm = "001203000010000-ABJB0001" OR  b.xmbm ="001203000010000-ABJA0001" OR b.xmbm ="003106030010000-310603001" OR  b.xmbm = "003106030020000-310603002" OR b.xmbm = "003106070060000-310607005" OR b.xmbm = "001203000010000-120300001"  THEN  '001203000010000-ABJB0001' ELSE b.xmbm END   , SUBSTR(b.fydate, 1, 10)
    HAVING sl > 0
    ORDER BY b.fydate DESC
  </select>
  <select id="selectCyMaxOpdate" resultType="java.util.Date">
    select ifnull(max(opdate), '2022-01-01 00:00:01') as opdate
    from fyxx_cy
    where brid = #{brid} and zyid = #{zyid};
  </select>
    <select id="selectRefundByOpdateAndXmbm" resultMap="FyxxResult">
      select jzh, brid, zyid, sl, je, xmmc, fydate, opdate,
      CASE WHEN xmbm = "001203000010000-ABJB0001" OR  xmbm ="001203000010000-ABJA0001" OR xmbm ="003106030010000-310603001" OR  xmbm = "003106030020000-310603002" OR xmbm = "003106070060000-310607005" OR xmbm = "001203000010000-120300001"  THEN  '001203000010000-ABJB0001' ELSE xmbm END as xmbm
      from fyxx
      <where>
        <if test="jzh != null and jzh != ''">and jzh = #{jzh}</if>
        <if test="xmbm != null and xmbm != ''">and xmbm = #{xmbm}</if>
        <if test="opdate != null">and to_days(opdate) = to_days(#{opdate})</if>
      and sl &lt; 0
      </where>
    </select>


    <insert id="insertFyxx" parameterType="Fyxx" useGeneratedKeys="true" keyProperty="id">
    insert into fyxx
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jzh != null">jzh,</if>
      <if test="brid != null and brid != ''">brid,</if>
      <if test="zyid != null">zyid,</if>
      <if test="yyxmbm != null">yyxmbm,</if>
      <if test="xmbm != null">xmbm,</if>
      <if test="xmmc != null">xmmc,</if>
      <if test="sl != null">sl,</if>
      <if test="price != null">price,</if>
      <if test="je != null">je,</if>
      <if test="dw != null">dw,</if>
      <if test="guige != null">guige,</if>
      <if test="fydate != null">fydate,</if>
      <if test="opdate != null">opdate,</if>
      <if test="ksid != null">ksid,</if>
      <if test="ksname != null">ksname,</if>
      <if test="ysid != null">ysid,</if>
      <if test="ysname != null">ysname,</if>
      <if test="yzid != null">yzid,</if>
      <if test="billno != null">billno,</if>
      <if test="hisid != null">hisid,</if>
      <if test="opname != null">opname,</if>
      <if test="jzid != null">jzid,</if>
      <if test="fykmname != null">fykmname,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jzh != null">#{jzh},</if>
      <if test="brid != null and brid != ''">#{brid},</if>
      <if test="zyid != null">#{zyid},</if>
      <if test="yyxmbm != null">#{yyxmbm},</if>
      <if test="xmbm != null">#{xmbm},</if>
      <if test="xmmc != null">#{xmmc},</if>
      <if test="sl != null">#{sl},</if>
      <if test="price != null">#{price},</if>
      <if test="je != null">#{je},</if>
      <if test="dw != null">#{dw},</if>
      <if test="guige != null">#{guige},</if>
      <if test="fydate != null">#{fydate},</if>
      <if test="opdate != null">#{opdate},</if>
      <if test="ksid != null">#{ksid},</if>
      <if test="ksname != null">#{ksname},</if>
      <if test="ysid != null">#{ysid},</if>
      <if test="ysname != null">#{ysname},</if>
      <if test="yzid != null">#{yzid},</if>
      <if test="billno != null">#{billno},</if>
      <if test="hisid != null">#{hisid},</if>
      <if test="opname != null">#{opname},</if>
      <if test="jzid != null">#{jzid},</if>
      <if test="fykmname != null">#{fykmname},</if>
    </trim>
  </insert>
  <insert id="syncMzFyxx">
    insert into fyxx_mz
    (jzh,brid,zyid,yyxmbm,xmbm,xmmc,sl,price,je,dw,guige,fydate,opdate,ksid,ksname,ysid,ysname,yzid,billno,hisid,opname,jzid,fykmname)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.jzh},#{item.brid},#{item.zyid},#{item.yyxmbm},#{item.xmbm},#{item.xmmc},#{item.sl},
      #{item.price},#{item.je},#{item.dw},#{item.guige},#{item.fydate},#{item.opdate},
      #{item.ksid},#{item.ksname},#{item.ysid},#{item.ysname},#{item.yzid},#{item.billno},
      #{item.hisid},#{item.opname},#{item.jzid},#{item.fykmname})
    </foreach>
    on duplicate key update
    xmbm = values(xmbm),
    fydate = values(fydate),
    hisid = values(hisid);
  </insert>
  
  <insert id="syncMzFyxx1">
    insert into fyxx_mz
    (jzh,brid,zyid,yyxmbm,xmbm,xmmc,sl,price,je,dw,guige,fydate,opdate,ksid,ksname,ysid,ysname,yzid,billno,hisid,opname,jzid,fykmname)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.jzh},#{item.brid},#{item.zyid},#{item.yyxmbm},#{item.xmbm},#{item.xmmc},#{item.sl},
      #{item.price},#{item.je},#{item.dw},#{item.guige},#{item.fydate},#{item.opdate},
      #{item.ksid},#{item.ksname},#{item.ysid},#{item.ysname},#{item.yzid},#{item.billno},
      #{item.hisid},#{item.opname},#{item.jzid},#{item.fykmname})
    </foreach>
    on duplicate key update
    xmbm = values(xmbm),
    fydate = values(fydate),
    hisid = values(hisid);
  </insert>
  
  <insert id="syncHistoryFyxx">
    insert into fyxx_cy
    (jzh,brid,zyid,yyxmbm,xmbm,xmmc,sl,price,je,dw,guige,fydate,opdate,ksid,ksname,ysid,ysname,yzid,billno,hisid,opname,jzid,fykmname)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.jzh},#{item.brid},#{item.zyid},#{item.yyxmbm},#{item.xmbm},#{item.xmmc},#{item.sl},
      #{item.price},#{item.je},#{item.dw},#{item.guige},#{item.fydate},#{item.opdate},
      #{item.ksid},#{item.ksname},#{item.ysid},#{item.ysname},#{item.yzid},#{item.billno},
      #{item.hisid},#{item.opname},#{item.jzid},#{item.fykmname})
    </foreach>
    on duplicate key update
    xmbm = values(xmbm),
    fydate = values(fydate),
    hisid = values(hisid);
  </insert>

  <update id="updateFyxx" parameterType="Fyxx">
    update fyxx
    <trim prefix="SET" suffixOverrides=",">
      <if test="jzh != null">jzh = #{jzh},</if>
      <if test="brid != null and brid != ''">brid = #{brid},</if>
      <if test="zyid != null">zyid = #{zyid},</if>
      <if test="yyxmbm != null">yyxmbm = #{yyxmbm},</if>
      <if test="xmbm != null">xmbm = #{xmbm},</if>
      <if test="xmmc != null">xmmc = #{xmmc},</if>
      <if test="sl != null">sl = #{sl},</if>
      <if test="price != null">price = #{price},</if>
      <if test="je != null">je = #{je},</if>
      <if test="dw != null">dw = #{dw},</if>
      <if test="guige != null">guige = #{guige},</if>
      <if test="fydate != null">fydate = #{fydate},</if>
      <if test="opdate != null">opdate = #{opdate},</if>
      <if test="ksid != null">ksid = #{ksid},</if>
      <if test="ksname != null">ksname = #{ksname},</if>
      <if test="ysid != null">ysid = #{ysid},</if>
      <if test="ysname != null">ysname = #{ysname},</if>
      <if test="yzid != null">yzid = #{yzid},</if>
      <if test="billno != null">billno = #{billno},</if>
      <if test="hisid != null">hisid = #{hisid},</if>
      <if test="opname != null">opname = #{opname},</if>
      <if test="jzid != null">jzid = #{jzid},</if>
      <if test="fykmname != null">fykmname = #{fykmname},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteFyxxById" parameterType="Long">
    delete
    from fyxx
    where id = #{id}
  </delete>

  <delete id="deleteFyxxByJzh" parameterType="String">
    delete
    from fyxx
    where jzh = #{jzh}
  </delete>

  <delete id="deleteFyxxcyByJzh" parameterType="String">
    delete
    from fyxx_cy
    where jzh = #{jzh}
  </delete>

  <delete id="deleteFyxxByFydate" parameterType="String">
    delete
    from fyxx
    where jzh = #{jzh}
      and fydate &lt;
          (select max(rydate) from brxx where jzh = #{jzh})
  </delete>

  <delete id="deleteFyxxByIds" parameterType="String">
    delete from fyxx where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <delete id="deleteFyxxByFyDate" parameterType="Fyxx">
    delete
    from fyxx
    where jzh = #{jzh}
      and fydate &lt; #{fydate}
  </delete>

</mapper>
