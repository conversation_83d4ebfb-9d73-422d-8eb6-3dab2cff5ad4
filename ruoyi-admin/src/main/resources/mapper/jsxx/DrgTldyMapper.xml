<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgTldyMapper">

    <resultMap type="DrgTldy" id="DrgTldyResult">
        <result property="id"    column="id"    />
        <result property="xm"    column="xm"    />
        <result property="jsh"    column="jsh"    />
        <result property="yljgbm"    column="yljgbm"    />
        <result property="yljgmc"    column="yljgmc"    />
        <result property="xz"    column="xz"    />
        <result property="zfy"    column="zfy"    />
        <result property="jbbm"    column="jbbm"    />
        <result property="zdmc"    column="zdmc"    />
        <result property="ssbm"    column="ssbm"    />
        <result property="ssmc"    column="ssmc"    />
        <result property="drgbm"    column="drgbm"    />
        <result property="drgmc"    column="drgmc"    />
        <result property="drgzfbz"    column="drgzfbz"    />
        <result property="drgzgtc"    column="drgzgtc"    />
        <result property="zgtc"    column="zgtc"    />
        <result property="drgjmtc"    column="drgjmtc"    />
        <result property="jmtc"    column="jmtc"    />
        <result property="jsrq"    column="jsrq"    />
        <result property="dysqsj"    column="dysqsj"    />
        <result property="sqdybllb"    column="sqdybllb"    />
        <result property="shzt"    column="shzt"    />
        <result property="bah"    column="bah"    />
        <result property="sjzyts"    column="sjzyts"    />
        <result property="cykb"    column="cykb"    />
        <result property="sqyy"    column="sqyy"    />
        <result property="jgtjzt"    column="jgtjzt"    />
        <result property="bl"    column="bl"    />
    </resultMap>

    <sql id="selectDrgTldyVo">
        select id, xm, jsh, yljgbm, yljgmc, xz, zfy, jbbm, zdmc, ssbm, ssmc, drgbm, drgmc, drgzfbz, drgzgtc, zgtc, drgjmtc, jmtc, jsrq, dysqsj, sqdybllb, shzt, bah, bl, sjzyts, cykb, sqyy, jgtjzt from drg_tldy
    </sql>

    <select id="selectDrgTldyList" parameterType="DrgTldy" resultMap="DrgTldyResult">
        <include refid="selectDrgTldyVo"/>
        <where>
            <if test="xm != null  and xm != ''"> and xm = #{xm}</if>
            <if test="jsh != null  and jsh != ''"> and jsh = #{jsh}</if>
            <if test="yljgbm != null  and yljgbm != ''"> and yljgbm = #{yljgbm}</if>
            <if test="yljgmc != null  and yljgmc != ''"> and yljgmc = #{yljgmc}</if>
            <if test="xz != null  and xz != ''"> and xz = #{xz}</if>
            <if test="zfy != null "> and zfy = #{zfy}</if>
            <if test="jbbm != null  and jbbm != ''"> and jbbm = #{jbbm}</if>
            <if test="zdmc != null  and zdmc != ''"> and zdmc = #{zdmc}</if>
            <if test="ssbm != null  and ssbm != ''"> and ssbm = #{ssbm}</if>
            <if test="ssmc != null  and ssmc != ''"> and ssmc = #{ssmc}</if>
            <if test="drgbm != null  and drgbm != ''"> and drgbm = #{drgbm}</if>
            <if test="drgmc != null  and drgmc != ''"> and drgmc = #{drgmc}</if>
            <if test="drgzfbz != null  and drgzfbz != ''"> and drgzfbz = #{drgzfbz}</if>
            <if test="drgzgtc != null  and drgzgtc != ''"> and drgzgtc = #{drgzgtc}</if>
            <if test="zgtc != null  and zgtc != ''"> and zgtc = #{zgtc}</if>
            <if test="drgjmtc != null  and drgjmtc != ''"> and drgjmtc = #{drgjmtc}</if>
            <if test="jmtc != null  and jmtc != ''"> and jmtc = #{jmtc}</if>
            <if test="jsrq != null "> and jsrq = #{jsrq}</if>
            <if test="dysqsj != null "> and dysqsj = #{dysqsj}</if>
            <if test="sqdybllb != null  and sqdybllb != ''"> and sqdybllb = #{sqdybllb}</if>
            <if test="shzt != null  and shzt != ''"> and shzt = #{shzt}</if>
            <if test="bah != null  and bah != ''"> and bah = #{bah}</if>
            <if test="sjzyts != null "> and sjzyts = #{sjzyts}</if>
            <if test="cykb != null  and cykb != ''"> and cykb = #{cykb}</if>
            <if test="sqyy != null  and sqyy != ''"> and sqyy = #{sqyy}</if>
            <if test="jgtjzt != null  and jgtjzt != ''"> and jgtjzt = #{jgtjzt}</if>
            <if test="bl != null  and bl != ''"> and bl = #{bl}</if>
        </where>
    </select>

    <select id="selectDrgTldyById" parameterType="Long" resultMap="DrgTldyResult">
        <include refid="selectDrgTldyVo"/>
        where id = #{id}
    </select>

    <insert id="insertDrgTldy" parameterType="DrgTldy" useGeneratedKeys="true" keyProperty="id">
        insert into drg_tldy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xm != null">xm,</if>
            <if test="jsh != null">jsh,</if>
            <if test="yljgbm != null">yljgbm,</if>
            <if test="yljgmc != null">yljgmc,</if>
            <if test="xz != null">xz,</if>
            <if test="zfy != null">zfy,</if>
            <if test="jbbm != null">jbbm,</if>
            <if test="zdmc != null">zdmc,</if>
            <if test="ssbm != null">ssbm,</if>
            <if test="ssmc != null">ssmc,</if>
            <if test="drgbm != null">drgbm,</if>
            <if test="drgmc != null">drgmc,</if>
            <if test="drgzfbz != null">drgzfbz,</if>
            <if test="drgzgtc != null">drgzgtc,</if>
            <if test="zgtc != null">zgtc,</if>
            <if test="drgjmtc != null">drgjmtc,</if>
            <if test="jmtc != null">jmtc,</if>
            <if test="jsrq != null">jsrq,</if>
            <if test="dysqsj != null">dysqsj,</if>
            <if test="sqdybllb != null">sqdybllb,</if>
            <if test="shzt != null">shzt,</if>
            <if test="bah != null">bah,</if>
            <if test="sjzyts != null">sjzyts,</if>
            <if test="cykb != null">cykb,</if>
            <if test="sqyy != null">sqyy,</if>
            <if test="jgtjzt != null">jgtjzt,</if>
            <if test="bl != null">bl,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xm != null">#{xm},</if>
            <if test="jsh != null">#{jsh},</if>
            <if test="yljgbm != null">#{yljgbm},</if>
            <if test="yljgmc != null">#{yljgmc},</if>
            <if test="xz != null">#{xz},</if>
            <if test="zfy != null">#{zfy},</if>
            <if test="jbbm != null">#{jbbm},</if>
            <if test="zdmc != null">#{zdmc},</if>
            <if test="ssbm != null">#{ssbm},</if>
            <if test="ssmc != null">#{ssmc},</if>
            <if test="drgbm != null">#{drgbm},</if>
            <if test="drgmc != null">#{drgmc},</if>
            <if test="drgzfbz != null">#{drgzfbz},</if>
            <if test="drgzgtc != null">#{drgzgtc},</if>
            <if test="zgtc != null">#{zgtc},</if>
            <if test="drgjmtc != null">#{drgjmtc},</if>
            <if test="jmtc != null">#{jmtc},</if>
            <if test="jsrq != null">#{jsrq},</if>
            <if test="dysqsj != null">#{dysqsj},</if>
            <if test="sqdybllb != null">#{sqdybllb},</if>
            <if test="shzt != null">#{shzt},</if>
            <if test="bah != null">#{bah},</if>
            <if test="sjzyts != null">#{sjzyts},</if>
            <if test="cykb != null">#{cykb},</if>
            <if test="sqyy != null">#{sqyy},</if>
            <if test="jgtjzt != null">#{jgtjzt},</if>
            <if test="bl != null">#{bl},</if>
         </trim>
    </insert>

    <update id="updateDrgTldy" parameterType="DrgTldy">
        update drg_tldy
        <trim prefix="SET" suffixOverrides=",">
            <if test="xm != null">xm = #{xm},</if>
            <if test="jsh != null">jsh = #{jsh},</if>
            <if test="yljgbm != null">yljgbm = #{yljgbm},</if>
            <if test="yljgmc != null">yljgmc = #{yljgmc},</if>
            <if test="xz != null">xz = #{xz},</if>
            <if test="zfy != null">zfy = #{zfy},</if>
            <if test="jbbm != null">jbbm = #{jbbm},</if>
            <if test="zdmc != null">zdmc = #{zdmc},</if>
            <if test="ssbm != null">ssbm = #{ssbm},</if>
            <if test="ssmc != null">ssmc = #{ssmc},</if>
            <if test="drgbm != null">drgbm = #{drgbm},</if>
            <if test="drgmc != null">drgmc = #{drgmc},</if>
            <if test="drgzfbz != null">drgzfbz = #{drgzfbz},</if>
            <if test="drgzgtc != null">drgzgtc = #{drgzgtc},</if>
            <if test="zgtc != null">zgtc = #{zgtc},</if>
            <if test="drgjmtc != null">drgjmtc = #{drgjmtc},</if>
            <if test="jmtc != null">jmtc = #{jmtc},</if>
            <if test="jsrq != null">jsrq = #{jsrq},</if>
            <if test="dysqsj != null">dysqsj = #{dysqsj},</if>
            <if test="sqdybllb != null">sqdybllb = #{sqdybllb},</if>
            <if test="shzt != null">shzt = #{shzt},</if>
            <if test="bah != null">bah = #{bah},</if>
            <if test="sjzyts != null">sjzyts = #{sjzyts},</if>
            <if test="cykb != null">cykb = #{cykb},</if>
            <if test="sqyy != null">sqyy = #{sqyy},</if>
            <if test="jgtjzt != null">jgtjzt = #{jgtjzt},</if>
            <if test="bl != null">bl = #{bl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDrgTldyById" parameterType="Long">
        delete from drg_tldy where id = #{id}
    </delete>

    <delete id="deleteDrgTldyByIds" parameterType="String">
        delete from drg_tldy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
