<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JsxxMapper">

    <resultMap type="Jsxx" id="JsxxResult">
        <result property="setlId"    column="setl_id"    />
        <result property="mdtrtId"    column="mdtrt_id"    />
        <result property="psnNo"    column="psn_no"    />
        <result property="psnName"    column="psn_name"    />
        <result property="psnCertType"    column="psn_cert_type"    />
        <result property="certno"    column="certno"    />
        <result property="gend"    column="gend"    />
        <result property="naty"    column="naty"    />
        <result property="brdy"    column="brdy"    />
        <result property="age"    column="age"    />
        <result property="insutype"    column="insutype"    />
        <result property="psnType"    column="psn_type"    />
        <result property="cvlservFlag"    column="cvlserv_flag"    />
        <result property="flxempeFlag"    column="flxempe_flag"    />
        <result property="nwbFlag"    column="nwb_flag"    />
        <result property="insuOptins"    column="insu_optins"    />
        <result property="empName"    column="emp_name"    />
        <result property="payLoc"    column="pay_loc"    />
        <result property="fixmedinsCode"    column="fixmedins_code"    />
        <result property="fixmedinsName"    column="fixmedins_name"    />
        <result property="hospLv"    column="hosp_lv"    />
        <result property="fixmedinsPoolarea"    column="fixmedins_poolarea"    />
        <result property="lmtpricHospLv"    column="lmtpric_hosp_lv"    />
        <result property="dedcHospLv"    column="dedc_hosp_lv"    />
        <result property="begndate"    column="begndate"    />
        <result property="enddate"    column="enddate"    />
        <result property="setlTime"    column="setl_time"    />
        <result property="mdtrtCertType"    column="mdtrt_cert_type"    />
        <result property="medType"    column="med_type"    />
        <result property="clrType"    column="clr_type"    />
        <result property="clrWay"    column="clr_way"    />
        <result property="clrOptins"    column="clr_optins"    />
        <result property="medfeeSumamt"    column="medfee_sumamt"    />
        <result property="fulamtOwnpayAmt"    column="fulamt_ownpay_amt"    />
        <result property="overlmtSelfpay"    column="overlmt_selfpay"    />
        <result property="preselfpayAmt"    column="preselfpay_amt"    />
        <result property="inscpScpAmt"    column="inscp_scp_amt"    />
        <result property="actPayDedc"    column="act_pay_dedc"    />
        <result property="hifpPay"    column="hifp_pay"    />
        <result property="poolPropSelfpay"    column="pool_prop_selfpay"    />
        <result property="cvlservPay"    column="cvlserv_pay"    />
        <result property="hifesPay"    column="hifes_pay"    />
        <result property="hifmiPay"    column="hifmi_pay"    />
        <result property="hifobPay"    column="hifob_pay"    />
        <result property="mafPay"    column="maf_pay"    />
        <result property="othPay"    column="oth_pay"    />
        <result property="fundPaySumamt"    column="fund_pay_sumamt"    />
        <result property="psnPay"    column="psn_pay"    />
        <result property="acctPay"    column="acct_pay"    />
        <result property="cashPayamt"    column="cash_payamt"    />
        <result property="balc"    column="balc"    />
        <result property="acctMulaidPay"    column="acct_mulaid_pay"    />
        <result property="medinsSetlId"    column="medins_setl_id"    />
        <result property="refdSetlFlag"    column="refd_setl_flag"    />
        <result property="year"    column="year"    />
        <result property="diseCodg"    column="dise_codg"    />
        <result property="diseName"    column="dise_name"    />
        <result property="invono"    column="invono"    />
        <result property="opterId"    column="opter_id"    />
        <result property="opterName"    column="opter_name"    />
        <result property="optTime"    column="opt_time"    />
        <result property="hisJsdate"    column="his_jsdate"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="jzid"    column="jzid"    />
    </resultMap>

    <sql id="selectJsxxVo">
        select setl_id, mdtrt_id, psn_no, psn_name, psn_cert_type, certno, gend, naty, brdy, age, insutype, psn_type, cvlserv_flag, flxempe_flag, nwb_flag, insu_optins, emp_name, pay_loc, fixmedins_code, fixmedins_name, hosp_lv, fixmedins_poolarea, lmtpric_hosp_lv, dedc_hosp_lv, begndate, enddate, setl_time, mdtrt_cert_type, med_type, clr_type, clr_way, clr_optins, medfee_sumamt, fulamt_ownpay_amt, overlmt_selfpay, preselfpay_amt, inscp_scp_amt, act_pay_dedc, hifp_pay, pool_prop_selfpay, cvlserv_pay, hifes_pay, hifmi_pay, hifob_pay, maf_pay, oth_pay, fund_pay_sumamt, psn_pay, acct_pay, cash_payamt, balc, acct_mulaid_pay, medins_setl_id, refd_setl_flag, year, dise_codg, dise_name, invono, opter_id, opter_name, opt_time, his_jsdate, brid, zyid, jzid from jsxx
    </sql>

    <insert id="insertJsxxByHis">
      INSERT INTO jsxx (setl_id, mdtrt_id, psn_no, psn_name, psn_cert_type, certno, gend, naty, brdy, age, insutype, psn_type, cvlserv_flag, flxempe_flag, nwb_flag, insu_optins, emp_name, pay_loc, fixmedins_code, fixmedins_name, hosp_lv, fixmedins_poolarea, lmtpric_hosp_lv, dedc_hosp_lv, begndate, enddate, setl_time, mdtrt_cert_type, med_type, clr_type, clr_way, clr_optins, medfee_sumamt, fulamt_ownpay_amt, overlmt_selfpay, preselfpay_amt, inscp_scp_amt, act_pay_dedc, hifp_pay, pool_prop_selfpay, cvlserv_pay, hifes_pay, hifmi_pay, hifob_pay, maf_pay, oth_pay, fund_pay_sumamt, psn_pay, acct_pay, cash_payamt, balc, acct_mulaid_pay, medins_setl_id, refd_setl_flag, year, dise_codg, dise_name, invono, opter_id, opter_name, opt_time, his_jsdate, brid, zyid, jzid)
      select setl_id, mdtrt_id, psn_no, psn_name, psn_cert_type, certno, gend, naty, brdy, age, insutype, psn_type, cvlserv_flag, flxempe_flag, nwb_flag, insu_optins, emp_name, pay_loc, fixmedins_code, fixmedins_name, hosp_lv, fixmedins_poolarea, lmtpric_hosp_lv, dedc_hosp_lv, begndate, enddate, setl_time, mdtrt_cert_type, med_type, clr_type, clr_way, clr_optins, medfee_sumamt, fulamt_ownpay_amt, overlmt_selfpay, preselfpay_amt, inscp_scp_amt, act_pay_dedc, hifp_pay, pool_prop_selfpay, cvlserv_pay, hifes_pay, hifmi_pay, hifob_pay, maf_pay, oth_pay, fund_pay_sumamt, psn_pay, acct_pay, cash_payamt, balc, acct_mulaid_pay, medins_setl_id, refd_setl_flag, year, dise_codg, dise_name, invono, opter_id, opter_name, opt_time, his_jsdate, brid, zyid, jzid
      from jsxx_his a
      WHERE  a.his_jsdate>(SELECT IFNULL(MAX(his_jsdate),'2023-01-01') FROM jsxx);
    </insert>

    <select id="selectJsxxList" parameterType="Jsxx" resultMap="JsxxResult">
        <include refid="selectJsxxVo"/>
        <where>
            <if test="setlId != null  and setlId != ''"> and setl_id = #{setlId}</if>
            <if test="mdtrtId != null  and mdtrtId != ''"> and mdtrt_id = #{mdtrtId}</if>
            <if test="psnNo != null  and psnNo != ''"> and psn_no = #{psnNo}</if>
            <if test="psnName != null  and psnName != ''"> and psn_name like concat('%', #{psnName}, '%')</if>
            <if test="psnCertType != null  and psnCertType != ''"> and psn_cert_type = #{psnCertType}</if>
            <if test="certno != null  and certno != ''"> and certno = #{certno}</if>
            <if test="gend != null  and gend != ''"> and gend = #{gend}</if>
            <if test="naty != null  and naty != ''"> and naty = #{naty}</if>
            <if test="brdy != null "> and brdy = #{brdy}</if>
            <if test="age != null "> and age = #{age}</if>
            <if test="insutype != null  and insutype != ''"> and insutype = #{insutype}</if>
            <if test="psnType != null  and psnType != ''"> and psn_type = #{psnType}</if>
            <if test="cvlservFlag != null  and cvlservFlag != ''"> and cvlserv_flag = #{cvlservFlag}</if>
            <if test="flxempeFlag != null  and flxempeFlag != ''"> and flxempe_flag = #{flxempeFlag}</if>
            <if test="nwbFlag != null  and nwbFlag != ''"> and nwb_flag = #{nwbFlag}</if>
            <if test="insuOptins != null  and insuOptins != ''"> and insu_optins = #{insuOptins}</if>
            <if test="empName != null  and empName != ''"> and emp_name like concat('%', #{empName}, '%')</if>
            <if test="payLoc != null  and payLoc != ''"> and pay_loc = #{payLoc}</if>
            <if test="fixmedinsCode != null  and fixmedinsCode != ''"> and fixmedins_code = #{fixmedinsCode}</if>
            <if test="fixmedinsName != null  and fixmedinsName != ''"> and fixmedins_name like concat('%', #{fixmedinsName}, '%')</if>
            <if test="hospLv != null  and hospLv != ''"> and hosp_lv = #{hospLv}</if>
            <if test="fixmedinsPoolarea != null  and fixmedinsPoolarea != ''"> and fixmedins_poolarea = #{fixmedinsPoolarea}</if>
            <if test="lmtpricHospLv != null  and lmtpricHospLv != ''"> and lmtpric_hosp_lv = #{lmtpricHospLv}</if>
            <if test="dedcHospLv != null  and dedcHospLv != ''"> and dedc_hosp_lv = #{dedcHospLv}</if>
            <if test="begndate != null "> and begndate = #{begndate}</if>
            <if test="enddate != null "> and enddate = #{enddate}</if>
            <if test="setlTime != null "> and setl_time = #{setlTime}</if>
            <if test="mdtrtCertType != null  and mdtrtCertType != ''"> and mdtrt_cert_type = #{mdtrtCertType}</if>
            <if test="medType != null  and medType != ''"> and med_type = #{medType}</if>
            <if test="clrType != null  and clrType != ''"> and clr_type = #{clrType}</if>
            <if test="clrWay != null  and clrWay != ''"> and clr_way = #{clrWay}</if>
            <if test="clrOptins != null  and clrOptins != ''"> and clr_optins = #{clrOptins}</if>
            <if test="medfeeSumamt != null "> and medfee_sumamt = #{medfeeSumamt}</if>
            <if test="fulamtOwnpayAmt != null "> and fulamt_ownpay_amt = #{fulamtOwnpayAmt}</if>
            <if test="overlmtSelfpay != null "> and overlmt_selfpay = #{overlmtSelfpay}</if>
            <if test="preselfpayAmt != null "> and preselfpay_amt = #{preselfpayAmt}</if>
            <if test="inscpScpAmt != null "> and inscp_scp_amt = #{inscpScpAmt}</if>
            <if test="actPayDedc != null "> and act_pay_dedc = #{actPayDedc}</if>
            <if test="hifpPay != null "> and hifp_pay = #{hifpPay}</if>
            <if test="poolPropSelfpay != null "> and pool_prop_selfpay = #{poolPropSelfpay}</if>
            <if test="cvlservPay != null "> and cvlserv_pay = #{cvlservPay}</if>
            <if test="hifesPay != null "> and hifes_pay = #{hifesPay}</if>
            <if test="hifmiPay != null "> and hifmi_pay = #{hifmiPay}</if>
            <if test="hifobPay != null "> and hifob_pay = #{hifobPay}</if>
            <if test="mafPay != null "> and maf_pay = #{mafPay}</if>
            <if test="othPay != null "> and oth_pay = #{othPay}</if>
            <if test="fundPaySumamt != null "> and fund_pay_sumamt = #{fundPaySumamt}</if>
            <if test="psnPay != null "> and psn_pay = #{psnPay}</if>
            <if test="acctPay != null "> and acct_pay = #{acctPay}</if>
            <if test="cashPayamt != null "> and cash_payamt = #{cashPayamt}</if>
            <if test="balc != null "> and balc = #{balc}</if>
            <if test="acctMulaidPay != null "> and acct_mulaid_pay = #{acctMulaidPay}</if>
            <if test="medinsSetlId != null  and medinsSetlId != ''"> and medins_setl_id = #{medinsSetlId}</if>
            <if test="refdSetlFlag != null  and refdSetlFlag != ''"> and refd_setl_flag = #{refdSetlFlag}</if>
            <if test="year != null  and year != ''"> and year = #{year}</if>
            <if test="diseCodg != null  and diseCodg != ''"> and dise_codg = #{diseCodg}</if>
            <if test="diseName != null  and diseName != ''"> and dise_name like concat('%', #{diseName}, '%')</if>
            <if test="invono != null  and invono != ''"> and invono = #{invono}</if>
            <if test="opterId != null  and opterId != ''"> and opter_id = #{opterId}</if>
            <if test="opterName != null  and opterName != ''"> and opter_name like concat('%', #{opterName}, '%')</if>
            <if test="optTime != null "> and opt_time = #{optTime}</if>
            <if test="hisJsdate != null "> and his_jsdate = #{hisJsdate}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="jzid != null  and jzid != ''"> and jzid = #{jzid}</if>
        </where>
        order by setl_time desc
    </select>

    <select id="selectJsxxBySetlId" parameterType="String" resultMap="JsxxResult">
        <include refid="selectJsxxVo"/>
        where setl_id = #{setlId}
    </select>

    <insert id="insertJsxx" parameterType="Jsxx">
        insert into jsxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="setlId != null">setl_id,</if>
            <if test="mdtrtId != null">mdtrt_id,</if>
            <if test="psnNo != null">psn_no,</if>
            <if test="psnName != null">psn_name,</if>
            <if test="psnCertType != null">psn_cert_type,</if>
            <if test="certno != null">certno,</if>
            <if test="gend != null">gend,</if>
            <if test="naty != null">naty,</if>
            <if test="brdy != null">brdy,</if>
            <if test="age != null">age,</if>
            <if test="insutype != null">insutype,</if>
            <if test="psnType != null">psn_type,</if>
            <if test="cvlservFlag != null">cvlserv_flag,</if>
            <if test="flxempeFlag != null">flxempe_flag,</if>
            <if test="nwbFlag != null">nwb_flag,</if>
            <if test="insuOptins != null">insu_optins,</if>
            <if test="empName != null">emp_name,</if>
            <if test="payLoc != null">pay_loc,</if>
            <if test="fixmedinsCode != null">fixmedins_code,</if>
            <if test="fixmedinsName != null">fixmedins_name,</if>
            <if test="hospLv != null">hosp_lv,</if>
            <if test="fixmedinsPoolarea != null">fixmedins_poolarea,</if>
            <if test="lmtpricHospLv != null">lmtpric_hosp_lv,</if>
            <if test="dedcHospLv != null">dedc_hosp_lv,</if>
            <if test="begndate != null">begndate,</if>
            <if test="enddate != null">enddate,</if>
            <if test="setlTime != null">setl_time,</if>
            <if test="mdtrtCertType != null">mdtrt_cert_type,</if>
            <if test="medType != null">med_type,</if>
            <if test="clrType != null">clr_type,</if>
            <if test="clrWay != null">clr_way,</if>
            <if test="clrOptins != null">clr_optins,</if>
            <if test="medfeeSumamt != null">medfee_sumamt,</if>
            <if test="fulamtOwnpayAmt != null">fulamt_ownpay_amt,</if>
            <if test="overlmtSelfpay != null">overlmt_selfpay,</if>
            <if test="preselfpayAmt != null">preselfpay_amt,</if>
            <if test="inscpScpAmt != null">inscp_scp_amt,</if>
            <if test="actPayDedc != null">act_pay_dedc,</if>
            <if test="hifpPay != null">hifp_pay,</if>
            <if test="poolPropSelfpay != null">pool_prop_selfpay,</if>
            <if test="cvlservPay != null">cvlserv_pay,</if>
            <if test="hifesPay != null">hifes_pay,</if>
            <if test="hifmiPay != null">hifmi_pay,</if>
            <if test="hifobPay != null">hifob_pay,</if>
            <if test="mafPay != null">maf_pay,</if>
            <if test="othPay != null">oth_pay,</if>
            <if test="fundPaySumamt != null">fund_pay_sumamt,</if>
            <if test="psnPay != null">psn_pay,</if>
            <if test="acctPay != null">acct_pay,</if>
            <if test="cashPayamt != null">cash_payamt,</if>
            <if test="balc != null">balc,</if>
            <if test="acctMulaidPay != null">acct_mulaid_pay,</if>
            <if test="medinsSetlId != null">medins_setl_id,</if>
            <if test="refdSetlFlag != null">refd_setl_flag,</if>
            <if test="year != null">year,</if>
            <if test="diseCodg != null">dise_codg,</if>
            <if test="diseName != null">dise_name,</if>
            <if test="invono != null">invono,</if>
            <if test="opterId != null">opter_id,</if>
            <if test="opterName != null">opter_name,</if>
            <if test="optTime != null">opt_time,</if>
            <if test="hisJsdate != null">his_jsdate,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="jzid != null">jzid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="setlId != null">#{setlId},</if>
            <if test="mdtrtId != null">#{mdtrtId},</if>
            <if test="psnNo != null">#{psnNo},</if>
            <if test="psnName != null">#{psnName},</if>
            <if test="psnCertType != null">#{psnCertType},</if>
            <if test="certno != null">#{certno},</if>
            <if test="gend != null">#{gend},</if>
            <if test="naty != null">#{naty},</if>
            <if test="brdy != null">#{brdy},</if>
            <if test="age != null">#{age},</if>
            <if test="insutype != null">#{insutype},</if>
            <if test="psnType != null">#{psnType},</if>
            <if test="cvlservFlag != null">#{cvlservFlag},</if>
            <if test="flxempeFlag != null">#{flxempeFlag},</if>
            <if test="nwbFlag != null">#{nwbFlag},</if>
            <if test="insuOptins != null">#{insuOptins},</if>
            <if test="empName != null">#{empName},</if>
            <if test="payLoc != null">#{payLoc},</if>
            <if test="fixmedinsCode != null">#{fixmedinsCode},</if>
            <if test="fixmedinsName != null">#{fixmedinsName},</if>
            <if test="hospLv != null">#{hospLv},</if>
            <if test="fixmedinsPoolarea != null">#{fixmedinsPoolarea},</if>
            <if test="lmtpricHospLv != null">#{lmtpricHospLv},</if>
            <if test="dedcHospLv != null">#{dedcHospLv},</if>
            <if test="begndate != null">#{begndate},</if>
            <if test="enddate != null">#{enddate},</if>
            <if test="setlTime != null">#{setlTime},</if>
            <if test="mdtrtCertType != null">#{mdtrtCertType},</if>
            <if test="medType != null">#{medType},</if>
            <if test="clrType != null">#{clrType},</if>
            <if test="clrWay != null">#{clrWay},</if>
            <if test="clrOptins != null">#{clrOptins},</if>
            <if test="medfeeSumamt != null">#{medfeeSumamt},</if>
            <if test="fulamtOwnpayAmt != null">#{fulamtOwnpayAmt},</if>
            <if test="overlmtSelfpay != null">#{overlmtSelfpay},</if>
            <if test="preselfpayAmt != null">#{preselfpayAmt},</if>
            <if test="inscpScpAmt != null">#{inscpScpAmt},</if>
            <if test="actPayDedc != null">#{actPayDedc},</if>
            <if test="hifpPay != null">#{hifpPay},</if>
            <if test="poolPropSelfpay != null">#{poolPropSelfpay},</if>
            <if test="cvlservPay != null">#{cvlservPay},</if>
            <if test="hifesPay != null">#{hifesPay},</if>
            <if test="hifmiPay != null">#{hifmiPay},</if>
            <if test="hifobPay != null">#{hifobPay},</if>
            <if test="mafPay != null">#{mafPay},</if>
            <if test="othPay != null">#{othPay},</if>
            <if test="fundPaySumamt != null">#{fundPaySumamt},</if>
            <if test="psnPay != null">#{psnPay},</if>
            <if test="acctPay != null">#{acctPay},</if>
            <if test="cashPayamt != null">#{cashPayamt},</if>
            <if test="balc != null">#{balc},</if>
            <if test="acctMulaidPay != null">#{acctMulaidPay},</if>
            <if test="medinsSetlId != null">#{medinsSetlId},</if>
            <if test="refdSetlFlag != null">#{refdSetlFlag},</if>
            <if test="year != null">#{year},</if>
            <if test="diseCodg != null">#{diseCodg},</if>
            <if test="diseName != null">#{diseName},</if>
            <if test="invono != null">#{invono},</if>
            <if test="opterId != null">#{opterId},</if>
            <if test="opterName != null">#{opterName},</if>
            <if test="optTime != null">#{optTime},</if>
            <if test="hisJsdate != null">#{hisJsdate},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="jzid != null">#{jzid},</if>
         </trim>
    </insert>

    <update id="updateJsxx" parameterType="Jsxx">
        update jsxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="mdtrtId != null">mdtrt_id = #{mdtrtId},</if>
            <if test="psnNo != null">psn_no = #{psnNo},</if>
            <if test="psnName != null">psn_name = #{psnName},</if>
            <if test="psnCertType != null">psn_cert_type = #{psnCertType},</if>
            <if test="certno != null">certno = #{certno},</if>
            <if test="gend != null">gend = #{gend},</if>
            <if test="naty != null">naty = #{naty},</if>
            <if test="brdy != null">brdy = #{brdy},</if>
            <if test="age != null">age = #{age},</if>
            <if test="insutype != null">insutype = #{insutype},</if>
            <if test="psnType != null">psn_type = #{psnType},</if>
            <if test="cvlservFlag != null">cvlserv_flag = #{cvlservFlag},</if>
            <if test="flxempeFlag != null">flxempe_flag = #{flxempeFlag},</if>
            <if test="nwbFlag != null">nwb_flag = #{nwbFlag},</if>
            <if test="insuOptins != null">insu_optins = #{insuOptins},</if>
            <if test="empName != null">emp_name = #{empName},</if>
            <if test="payLoc != null">pay_loc = #{payLoc},</if>
            <if test="fixmedinsCode != null">fixmedins_code = #{fixmedinsCode},</if>
            <if test="fixmedinsName != null">fixmedins_name = #{fixmedinsName},</if>
            <if test="hospLv != null">hosp_lv = #{hospLv},</if>
            <if test="fixmedinsPoolarea != null">fixmedins_poolarea = #{fixmedinsPoolarea},</if>
            <if test="lmtpricHospLv != null">lmtpric_hosp_lv = #{lmtpricHospLv},</if>
            <if test="dedcHospLv != null">dedc_hosp_lv = #{dedcHospLv},</if>
            <if test="begndate != null">begndate = #{begndate},</if>
            <if test="enddate != null">enddate = #{enddate},</if>
            <if test="setlTime != null">setl_time = #{setlTime},</if>
            <if test="mdtrtCertType != null">mdtrt_cert_type = #{mdtrtCertType},</if>
            <if test="medType != null">med_type = #{medType},</if>
            <if test="clrType != null">clr_type = #{clrType},</if>
            <if test="clrWay != null">clr_way = #{clrWay},</if>
            <if test="clrOptins != null">clr_optins = #{clrOptins},</if>
            <if test="medfeeSumamt != null">medfee_sumamt = #{medfeeSumamt},</if>
            <if test="fulamtOwnpayAmt != null">fulamt_ownpay_amt = #{fulamtOwnpayAmt},</if>
            <if test="overlmtSelfpay != null">overlmt_selfpay = #{overlmtSelfpay},</if>
            <if test="preselfpayAmt != null">preselfpay_amt = #{preselfpayAmt},</if>
            <if test="inscpScpAmt != null">inscp_scp_amt = #{inscpScpAmt},</if>
            <if test="actPayDedc != null">act_pay_dedc = #{actPayDedc},</if>
            <if test="hifpPay != null">hifp_pay = #{hifpPay},</if>
            <if test="poolPropSelfpay != null">pool_prop_selfpay = #{poolPropSelfpay},</if>
            <if test="cvlservPay != null">cvlserv_pay = #{cvlservPay},</if>
            <if test="hifesPay != null">hifes_pay = #{hifesPay},</if>
            <if test="hifmiPay != null">hifmi_pay = #{hifmiPay},</if>
            <if test="hifobPay != null">hifob_pay = #{hifobPay},</if>
            <if test="mafPay != null">maf_pay = #{mafPay},</if>
            <if test="othPay != null">oth_pay = #{othPay},</if>
            <if test="fundPaySumamt != null">fund_pay_sumamt = #{fundPaySumamt},</if>
            <if test="psnPay != null">psn_pay = #{psnPay},</if>
            <if test="acctPay != null">acct_pay = #{acctPay},</if>
            <if test="cashPayamt != null">cash_payamt = #{cashPayamt},</if>
            <if test="balc != null">balc = #{balc},</if>
            <if test="acctMulaidPay != null">acct_mulaid_pay = #{acctMulaidPay},</if>
            <if test="medinsSetlId != null">medins_setl_id = #{medinsSetlId},</if>
            <if test="refdSetlFlag != null">refd_setl_flag = #{refdSetlFlag},</if>
            <if test="year != null">year = #{year},</if>
            <if test="diseCodg != null">dise_codg = #{diseCodg},</if>
            <if test="diseName != null">dise_name = #{diseName},</if>
            <if test="invono != null">invono = #{invono},</if>
            <if test="opterId != null">opter_id = #{opterId},</if>
            <if test="opterName != null">opter_name = #{opterName},</if>
            <if test="optTime != null">opt_time = #{optTime},</if>
            <if test="hisJsdate != null">his_jsdate = #{hisJsdate},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="jzid != null">jzid = #{jzid},</if>
        </trim>
        where setl_id = #{setlId}
    </update>

    <delete id="deleteJsxxBySetlId" parameterType="String">
        delete from jsxx where setl_id = #{setlId}
    </delete>

    <delete id="deleteJsxxBySetlIds" parameterType="String">
        delete from jsxx where setl_id in
        <foreach item="setlId" collection="array" open="(" separator="," close=")">
            #{setlId}
        </foreach>
    </delete>
</mapper>
