<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BBasyCheckBzPfMapper">

  <resultMap type="BBasyCheckBzPf" id="BBasyCheckBzPfResult">
    <result property="id" column="id"/>
    <result property="name" column="name"/>
    <result property="sort" column="sort"/>
    <result property="parentId" column="parent_id"/>
    <result property="score" column="score"/>
    <result property="maxScore" column="max_score"/>
    <result property="field" column="field"/>
    <result property="remark" column="remark"/>
    <result property="value" column="value"/>
    <result property="label" column="label"/>
  </resultMap>

  <resultMap type="BaSyjlError" id="BaSyjlErrorResult">
    <result property="id" column="id"/>
    <result property="sno" column="sno"/>
    <result property="brbs" column="brbs"/>
    <result property="bah" column="bah"/>
    <result property="code" column="code"/>
    <result property="errordes" column="errordes"/>
    <result property="type" column="type"/>
    <result property="trust" column="trust"/>
    <result property="errortype" column="errortype"/>
    <result property="field" column="field"/>
    <result property="effectdrg" column="effectdrg"/>
    <result property="coltype" column="coltype"/>
    <result property="displayorder" column="displayorder"/>
    <result property="originval" column="originval"/>
    <result property="score" column="score"/>
    <result property="care" column="care"/>
    <result property="updatetime" column="updatetime"/>
    <result property="createtime" column="createtime"/>
    <result property="jlly" column="jlly"/>
    <result property="source" column="source"/>
    <result property="maxScore" column="max_score"/>
  </resultMap>

  <sql id="selectBBasyCheckBzPfVo">
    select id,
           name,
           sort,
           parent_id,
           score,
           max_score,
           field,
           remark,
           field as value,
           name as label
    from b_basy_check_bz_pf
  </sql>

  <select id="selectBBasyCheckBzPfList" parameterType="BBasyCheckBzPf" resultMap="BBasyCheckBzPfResult">
    <include refid="selectBBasyCheckBzPfVo"/>
    <where>
      <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
      <if test="sort != null ">and sort = #{sort}</if>
      <if test="parentId != null  and parentId != ''">and parent_id = #{parentId}</if>
      <if test="score != null ">and score = #{score}</if>
      <if test="maxScore != null ">and max_score = #{maxScore}</if>
      <if test="field != null  and field != ''">and field = #{field}</if>
    </where>
  </select>


  <select id="getBaScoreCjq" parameterType="BaSyjlError" resultType="BigDecimal">
    SELECT fn_get_bascore(#{brbs},#{jlly})
  </select>


  <select id="getErrList" parameterType="BaSyjlError" resultMap="BaSyjlErrorResult">
    SELECT a.id,
           b.id AS sno,
           brbs,
           bah,
           code,
           errordes,
           type,
           CASE
             WHEN errortype = 0 THEN '非编码错误'
             WHEN errortype = 1 THEN '编码错误'
             WHEN errortype = 2 THEN '清单生成错误'
             WHEN errortype = 3 THEN '主要诊断错误'
             WHEN errortype = 4 THEN '过滤错误'
             WHEN errortype = 5 THEN '漏填错误'
             WHEN errortype = 6 THEN '多填错误'
             WHEN errortype = 7 THEN '名称错误'
           ELSE '' END     AS errortype,
           concat(b.id,b.name)  AS field,
           effectdrg,
           coltype,
           displayorder,
           originval,
           b.score AS score,
           b.max_score AS max_score,
           care,
           updatetime,
           createtime,
           source,
           (CASE WHEN jlly = 3 THEN '医生' WHEN jlly = 4 THEN '病案室' ELSE jlly END)  AS jlly
    FROM ba_syjl_error a LEFT JOIN ba_check_rule_cjq_f b ON a.field = b.field
    WHERE
      a.brbs = #{brbs}
      <if test="jlly != null  and jlly != ''">and a.jlly = #{jlly}</if>
    ORDER BY b.name
  </select>

</mapper>
