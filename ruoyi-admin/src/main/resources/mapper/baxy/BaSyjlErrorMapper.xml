<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaSyjlErrorMapper">

  <resultMap type="BaSyjlError" id="BaSyjlErrorResult">
    <result property="id" column="id"/>
    <result property="brbs" column="brbs"/>
    <result property="bah" column="bah"/>
    <result property="code" column="code"/>
    <result property="errordes" column="errordes"/>
    <result property="type" column="type"/>
    <result property="trust" column="trust"/>
    <result property="errortype" column="errortype"/>
    <result property="field" column="field"/>
    <result property="effectdrg" column="effectdrg"/>
    <result property="coltype" column="coltype"/>
    <result property="displayorder" column="displayorder"/>
    <result property="originval" column="originval"/>
    <result property="score" column="score"/>
    <result property="care" column="care"/>
    <result property="updatetime" column="updatetime"/>
    <result property="createtime" column="createtime"/>
    <result property="jlly" column="jlly"/>
    <result property="source" column="source"/>
  </resultMap>


  <resultMap type="CjqBajyExportVo" id="CjqBajyExportVoResult">
    <result property="cykb" column="cykb"/>
    <result property="brbs" column="brbs"/>
    <result property="bah" column="bah"/>
    <result property="zyys" column="zyys"/>
    <result property="zzys" column="zzys"/>
    <result property="a01" column="A01"/>
    <result property="a02" column="A02"/>
    <result property="a03" column="A03"/>
    <result property="a04" column="A04"/>
    <result property="a05" column="A05"/>
    <result property="a06" column="A06"/>
    <result property="a07" column="A07"/>
    <result property="a08" column="A08"/>
    <result property="a09" column="A09"/>
    <result property="a10" column="A10"/>
    <result property="a11" column="A11"/>
    <result property="a12" column="A12"/>
    <result property="a13" column="A13"/>
    <result property="a14" column="A14"/>
    <result property="a15" column="A15"/>
    <result property="a16" column="A16"/>
    <result property="a17" column="A17"/>
    <result property="a18" column="A18"/>
    <result property="a19" column="A19"/>
    <result property="a20" column="A20"/>
    <result property="a21" column="A21"/>
    <result property="a22" column="A22"/>
    <result property="a23" column="A23"/>
    <result property="a24" column="A24"/>
    <result property="a25" column="A25"/>
    <result property="a26" column="A26"/>
    <result property="a27" column="A27"/>
    <result property="a28" column="A28"/>
    <result property="a29" column="A29"/>
    <result property="a30" column="A30"/>
    <result property="a31" column="A31"/>
    <result property="a32" column="A32"/>
    <result property="a33" column="A33"/>
    <result property="a34" column="A34"/>
    <result property="a35" column="A35"/>
    <result property="a36" column="A36"/>
    <result property="a37" column="A37"/>
    <result property="a38" column="A38"/>
    <result property="a39" column="A39"/>
    <result property="a40" column="A40"/>
    <result property="a41" column="A41"/>
    <result property="a42" column="A42"/>
    <result property="zkf" column="zkf"/>
    <result property="zdf" column="zdf"/>
    <result property="bz" column="bz"/>
  </resultMap>

  <sql id="selectBaSyjlErrorVo">
    SELECT id,
           brbs,
           bah,
           CODE,
           errordes,
           type,
           (CASE WHEN trust = 0 THEN '疑似' WHEN trust = 1 THEN '强制' ELSE trust END) AS trust,
           CASE
             WHEN errortype = 0 THEN '非编码错误'
             WHEN errortype = 1 THEN '编码错误'
             WHEN errortype = 2 THEN '清单生成错误'
             WHEN errortype = 3 THEN '主要诊断错误'
             WHEN errortype = 4 THEN '过滤错误'
             WHEN errortype = 5 THEN '漏填错误'
             WHEN errortype = 6 THEN '多填错误'
             WHEN errortype = 7 THEN '名称错误'
             ELSE '' END     AS errortype,
           field,
           effectdrg,
           coltype,
           displayorder,
           originval,
           score,
           care,
           updatetime,
           createtime,
           source,
           (CASE WHEN jlly = 3 THEN '医生' WHEN jlly = 4 THEN '病案室' ELSE jlly END)  AS jlly
    FROM ba_syjl_error
  </sql>


  <select id="selectBazkCjq" parameterType="CjqBajyExportVo" resultMap="CjqBajyExportVoResult">
    call usp_get_bazk_cjq(#{adtFrom},#{adtTo},#{dateType},#{jlly},#{cykb})
  </select>

  <select id="selectBaSyjlErrorList" parameterType="BaSyjlError" resultMap="BaSyjlErrorResult">
    <include refid="selectBaSyjlErrorVo"/>
    <where>
      <if test="brbs != null  and brbs != ''">and brbs = #{brbs}</if>
      <if test="code != null  and code != ''">and code = #{code}</if>
      <if test="errordes != null  and errordes != ''">and errordes = #{errordes}</if>
      <if test="type != null  and type != ''">and type = #{type}</if>
      <if test="trust != null  and trust != ''">and trust = #{trust}</if>
      <if test="errortype != null  and errortype != ''">and errortype = #{errortype}</if>
      <if test="field != null  and field != ''">and field = #{field}</if>
      <if test="effectdrg != null ">and effectdrg = #{effectdrg}</if>
      <if test="coltype != null ">and coltype = #{coltype}</if>
      <if test="displayorder != null ">and displayorder = #{displayorder}</if>
      <if test="originval != null  and originval != ''">and originval = #{originval}</if>
      <if test="score != null ">and score = #{score}</if>
      <if test="care != null ">and care = #{care}</if>
      <if test="updatetime != null ">and updatetime = #{updatetime}</if>
      <if test="jlly != null ">and jlly = #{jlly}</if>
      <if test="source != null and source != ''">and source = #{source}</if>
      <if
        test="params.beginCreatetime != null and params.beginCreatetime != '' and params.endCreatetime != null and params.endCreatetime != ''">
        and createtime between #{params.beginCreatetime} and #{params.endCreatetime}
      </if>
    </where>
    order by source
  </select>

  <select id="selectBaSyjlErrorById" parameterType="Integer" resultMap="BaSyjlErrorResult">
    <include refid="selectBaSyjlErrorVo"/>
    where id = #{id}
  </select>

  <insert id="insertBaSyjlError" parameterType="BaSyjlError" useGeneratedKeys="true" keyProperty="id">
    insert into ba_syjl_error
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brbs != null and brbs != ''">brbs,</if>
      <if test="bah != null">bah,</if>
      <if test="code != null">code,</if>
      <if test="errordes != null">errordes,</if>
      <if test="type != null">type,</if>
      <if test="trust != null and trust != ''">trust,</if>
      <if test="errortype != null and errortype != ''">errortype,</if>
      <if test="field != null">field,</if>
      <if test="effectdrg != null">effectdrg,</if>
      <if test="coltype != null">coltype,</if>
      <if test="displayorder != null">displayorder,</if>
      <if test="originval != null">originval,</if>
      <if test="score != null">score,</if>
      <if test="care != null">care,</if>
      <if test="updatetime != null">updatetime,</if>
      <if test="createtime != null">createtime,</if>
      <if test="jlly != null">jlly,</if>
      <if test="source != null">source,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brbs != null and brbs != ''">#{brbs},</if>
      <if test="bah != null">#{bah},</if>
      <if test="code != null">#{code},</if>
      <if test="errordes != null">#{errordes},</if>
      <if test="type != null">#{type},</if>
      <if test="trust != null and trust != ''">#{trust},</if>
      <if test="errortype != null and errortype != ''">#{errortype},</if>
      <if test="field != null">#{field},</if>
      <if test="effectdrg != null">#{effectdrg},</if>
      <if test="coltype != null">#{coltype},</if>
      <if test="displayorder != null">#{displayorder},</if>
      <if test="originval != null">#{originval},</if>
      <if test="score != null">#{score},</if>
      <if test="care != null">#{care},</if>
      <if test="updatetime != null">#{updatetime},</if>
      <if test="createtime != null">#{createtime},</if>
      <if test="jlly != null">#{jlly},</if>
      <if test="source != null">#{source},</if>
    </trim>
  </insert>

  <update id="updateBaSyjlError" parameterType="BaSyjlError">
    update ba_syjl_error
    <trim prefix="SET" suffixOverrides=",">
      <if test="brbs != null and brbs != ''">brbs = #{brbs},</if>
      <if test="bah != null">bah = #{bah},</if>
      <if test="code != null">code = #{code},</if>
      <if test="errordes != null">errordes = #{errordes},</if>
      <if test="type != null">type = #{type},</if>
      <if test="trust != null and trust != ''">trust = #{trust},</if>
      <if test="errortype != null and errortype != ''">errortype = #{errortype},</if>
      <if test="field != null">field = #{field},</if>
      <if test="effectdrg != null">effectdrg = #{effectdrg},</if>
      <if test="coltype != null">coltype = #{coltype},</if>
      <if test="displayorder != null">displayorder = #{displayorder},</if>
      <if test="originval != null">originval = #{originval},</if>
      <if test="score != null">score = #{score},</if>
      <if test="care != null">care = #{care},</if>
      <if test="updatetime != null">updatetime = #{updatetime},</if>
      <if test="createtime != null">createtime = #{createtime},</if>
      <if test="jlly != null">jlly = #{jlly},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteBaSyjlErrorById" parameterType="String">
    delete
    from ba_syjl_error
    where brbs = #{id}
  </delete>

  <delete id="deleteBaSyjlErrorByBrbsAndJlly" parameterType="BaSyjlError">
    delete from ba_syjl_error where source != '1' and brbs = #{brbs}
    <if test="jlly != null">and jlly = #{jlly}</if>
  </delete>


  <delete id="deleteBaSyjlErrorByIds" parameterType="String">
    delete from ba_syjl_error where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
