<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CndrgMapper">

    <resultMap type="CndrgFx" id="CndrgResult">


    </resultMap>
  <select id="getCndrgQyfx" parameterType="CndrgVo" resultMap="CndrgResult">
    call usp_drg_cndrgkstj_qy(#{startDate}, #{endDate},#{datatype}, #{as_cblb}, #{ksname}  , #{qsfs}, #{rydateflag}, #{rystartDate}, #{ryendDate},'admin')
  </select>



</mapper>
