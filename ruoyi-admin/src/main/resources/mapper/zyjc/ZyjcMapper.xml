<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ZyjcMapper">

    <resultMap type="Zyjc" id="ZyjcResult">
        <result property="jzh"    column="jzh"    />
        <result property="zyh"    column="zyh"    />
        <result property="ybh"    column="ybh"    />
        <result property="brid"    column="brid"    />
        <result property="bah"    column="bah"    />
        <result property="brbs"    column="brbs"    />
        <result property="zyid"    column="zyid"    />
        <result property="xm"    column="xm"    />
        <result property="xb"    column="xb"    />
        <result property="nl"    column="nl"    />
        <result property="cykb"    column="cykb"    />
        <result property="zyys"    column="zyys"    />
        <result property="drgbh"    column="drgbh"    />
        <result property="zfy"    column="zfy"    />
        <result property="hcf"    column="hcf"    />
        <result property="ypf"    column="ypf"    />
        <result property="jcf"    column="jcf"    />
        <result property="jyf"    column="jyf"    />
        <result property="zfbz"    column="zfbz"    />
        <result property="zfqz"    column="zfqz"    />
        <result property="drgmc"    column="drgmc"    />
        <result property="zdmc"    column="zdmc"    />
        <result property="ssmc"    column="ssmc"    />
        <result property="jlsccyts"    column="jlsccyts"    />
        <result property="bghcf"    column="bghcf"    />
        <result property="bgypf"    column="bgypf"    />
        <result property="bgjcf"    column="bgjcf"    />
        <result property="bgjyf"    column="bgjyf"    />
    </resultMap>


  <sql id="zyjc">
    SELECT a.jzh,
           a.bah                                                                                       AS zyh,
           a.xm,
           a.nl,
           a.xb,
           a.cykb,
           a.zyys,
           a.brid,
           a.brbs,
           a.zyid,
           a.bah,
           ifnull(a.zfy, 0)                                                                            as zfy,
           a.drgbh,
           ifnull((ifnull(a.hcyyclf, 0) + ifnull(a.yyclf, 0) + ifnull(a.ycxyyclf, 0)), 0)              AS hcf,
           ifnull((ifnull(a.xyf, 0) + ifnull(a.kjywf, 0) + ifnull(a.zcyf, 0) + ifnull(a.zcyf1, 0)), 0) AS ypf,
           ifnull((ifnull(a.yxxzdf, 0) + ifnull(a.lczdxmf, 0)), 0)                                     AS jcf,
           ifnull((ifnull(a.blzdf, 0) + ifnull(a.syszdf, 0)), 0)                                       AS jyf,
           c.drgmc,
           ifnull(a.zfbz, 0)                                                                           as zfbz,
           ifnull(c.zfqz, 0)                                                                           as zfqz,
           ifnull(c.ypf_bg, 0)                                                                         AS bgypf,
           ifnull(c.hcf_bg, 0)                                                                         AS bghcf,
           ifnull(c.jcf_bg, 0)                                                                         AS bgjcf,
           ifnull(c.jyf_bg, 0)                                                                         AS bgjyf,
           (select datediff(a.rydate, ba.cydate)
            from ba_syjl ba
            where ba.sfzh = a.sfzh
              and ba.cydate &lt; a.cydate
              and a.sfzh is not null
              and a.sfzh != ''
              and a.sfzh != '-'
            order by cydate desc
            limit 1)                                                                                   as jlsccyts
    FROM ba_syjl a
           LEFT JOIN drgdict c ON a.drgbh = c.drgbh
  </sql>


  <select id="selectJlsccyts" parameterType="String" resultType="Long">
    SELECT fn_jlsccyts(#{jzh}) as jlsccyts
  </select>

    <select id="selectZyjcList" parameterType="Zyjc" resultMap="ZyjcResult">
        <include refid="zyjc"/>
        <where>
            <if test="brbs != null  and brbs != ''"> and a.brbs = #{brbs}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="bah != null  and bah != ''"> and bah = #{bah}</if>
            <if test="zyh != null  and zyh != ''"> and bah = #{zyh}</if>
            <if test="ybh != null  and ybh != ''"> and ybh = #{ybh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="xm != null  and xm != ''"> and xm = #{xm}</if>
            <if test="xb != null  and xb != ''"> and xb = #{xb}</if>
            <if test="nl != null "> and nl = #{nl}</if>
            <if test="cykb != null  and cykb != ''"> and cykb = #{cykb}</if>
            <if test="zyys != null  and zyys != ''"> and zyys = #{zyys}</if>
            <if test="drgbh != null  and drgbh != ''"> and drgbh = #{drgbh}</if>
            <if test="zfy != null "> and zfy = #{zfy}</if>
            <if test="hcf != null "> and hcf = #{hcf}</if>
            <if test="ypf != null "> and ypf = #{ypf}</if>
            <if test="jcf != null "> and jcf = #{jcf}</if>
            <if test="jyf != null "> and jyf = #{jyf}</if>
            <if test="zfbz != null "> and zfbz = #{zfbz}</if>
            <if test="drgmc != null  and drgmc != ''"> and drgmc = #{drgmc}</if>
            <if test="zdmc != null  and zdmc != ''"> and zdmc = #{zdmc}</if>
            <if test="ssmc != null  and ssmc != ''"> and ssmc = #{ssmc}</if>
            <if test="jlsccyts != null "> and jlsccyts = #{jlsccyts}</if>
            <if test="zyzt != null "> and a.zyzt = #{zyzt} </if>
        </where>
        <if test="sortColumn != null and sortColumn != '' and sortOrder != null and sortOrder != ''">
            order by ${sortColumn} ${sortOrder}
        </if>
    </select>

</mapper>
