<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbjkOptionMapper">

  <resultMap type="YbjkOption" id="YbjkOptionResult">
    <result property="id" column="id"/>
    <result property="cCode" column="c_code"/>
    <result property="cName" column="c_name"/>
    <result property="cType" column="c_type"/>
    <result property="cValue" column="c_value"/>
    <result property="createBy" column="create_by"/>
    <result property="createDate" column="create_date"/>
    <result property="updateBy" column="update_by"/>
    <result property="updateDate" column="update_date"/>
    <result property="remarks" column="remarks"/>
    <result property="delFlag" column="del_flag"/>
  </resultMap>

  <sql id="selectYbjkOptionVo">
    select id,
           c_code,
           c_name,
           c_type,
           c_value,
           create_by,
           create_date,
           update_by,
           update_date,
           remarks,
           del_flag
    from ybjk_option
  </sql>

  <select id="selectYbjkOptionList" parameterType="YbjkOption" resultMap="YbjkOptionResult">
    <include refid="selectYbjkOptionVo"/>
    <where>
      <if test="cName != null  and cName != ''">and c_name like concat('%', #{cName}, '%')</if>
      <if test="cCode != null  and cCode != ''">and cCode = #{cCode}</if>
      <if test="cType != null ">and c_type = #{cType}</if>
      <if test="cValue != null  and cValue != ''">and c_value = #{cValue}</if>
      <if test="createDate != null ">and create_date = #{createDate}</if>
      <if test="updateDate != null ">and update_date = #{updateDate}</if>
      <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
    </where>
  </select>

  <select id="selectYbjkOptionByCCode" parameterType="String" resultMap="YbjkOptionResult">
    <include refid="selectYbjkOptionVo"/>
    where c_code = #{cCode}
  </select>
  <select id="getSystemExpiryDate" resultType="java.lang.String">
    select c_expiry_date
    from p_company
    where c_name = #{companyName}
  </select>

  <select id="getCompanyName" resultType="java.lang.String">
    select c_name
    from p_company limit 1
  </select>

  <select id="getSystemServerTime" resultType="Date">
    select SYSDATE()
    from p_company
    where c_id = 'Y'
  </select>

  <insert id="insertYbjkOption" parameterType="YbjkOption">
    insert into ybjk_option
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">id,</if>
      <if test="cCode != null">c_code,</if>
      <if test="cName != null and cName != ''">c_name,</if>
      <if test="cType != null">c_type,</if>
      <if test="cValue != null">c_value,</if>
      <if test="createBy != null">create_by,</if>
      <if test="createDate != null">create_date,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="updateDate != null">update_date,</if>
      <if test="remarks != null">remarks,</if>
      <if test="delFlag != null and delFlag != ''">del_flag,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">#{id},</if>
      <if test="cCode != null">#{cCode},</if>
      <if test="cName != null and cName != ''">#{cName},</if>
      <if test="cType != null">#{cType},</if>
      <if test="cValue != null">#{cValue},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="createDate != null">#{createDate},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="updateDate != null">#{updateDate},</if>
      <if test="remarks != null">#{remarks},</if>
      <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
    </trim>
  </insert>

  <update id="updateYbjkOption" parameterType="YbjkOption">
    update ybjk_option
    <trim prefix="SET" suffixOverrides=",">
      <if test="id != null and id != ''">id = #{id},</if>
      <if test="cName != null and cName != ''">c_name = #{cName},</if>
      <if test="cType != null">c_type = #{cType},</if>
      <if test="cValue != null">c_value = #{cValue},</if>
      <if test="createBy != null">create_by = #{createBy},</if>
      <if test="createDate != null">create_date = #{createDate},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="updateDate != null">update_date = #{updateDate},</if>
      <if test="remarks != null">remarks = #{remarks},</if>
      <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
    </trim>
    where c_code = #{cCode}
  </update>

  <delete id="deleteYbjkOptionByCCode" parameterType="String">
    delete
    from ybjk_option
    where c_code = #{cCode}
  </delete>

  <delete id="deleteYbjkOptionByCCodes" parameterType="String">
    delete from ybjk_option where c_code in
    <foreach item="cCode" collection="array" open="(" separator="," close=")">
      #{cCode}
    </foreach>
  </delete>
</mapper>
