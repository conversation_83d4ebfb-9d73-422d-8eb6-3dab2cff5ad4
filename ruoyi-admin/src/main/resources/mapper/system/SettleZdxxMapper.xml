<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SettleZdxxMapper">

    <resultMap type="SettleZdxx" id="SettleZdxxResult">
        <result property="id"    column="id"    />
        <result property="brbs"    column="brbs"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="zdlx"    column="zdlx"    />
        <result property="zdcx"    column="zdcx"    />
        <result property="jbbm"    column="jbbm"    />
        <result property="zdmc"    column="zdmc"    />
        <result property="rybq"    column="rybq"    />
        <result property="cyqk"    column="cyqk"    />
        <result property="zdlxid"    column="zdlxid"    />
        <result property="jlly"    column="jlly"    />
        <result property="jlr"    column="jlr"    />
        <result property="jgid"    column="jgid"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectSettleZdxxVo">
        select id, brbs, brid, zyid, zdlx, zdcx, jbbm, zdmc, rybq, cyqk, zdlxid, jlly, jlr, jgid, remark, create_time, update_time, update_by, create_by from settle_zdxx
    </sql>

    <select id="selectSettleZdxxList" parameterType="SettleZdxx" resultMap="SettleZdxxResult">
        <include refid="selectSettleZdxxVo"/>
        <where>
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="zdlx != null  and zdlx != ''"> and zdlx = #{zdlx}</if>
            <if test="zdcx != null "> and zdcx = #{zdcx}</if>
            <if test="jbbm != null  and jbbm != ''"> and jbbm = #{jbbm}</if>
            <if test="zdmc != null  and zdmc != ''"> and zdmc = #{zdmc}</if>
            <if test="rybq != null  and rybq != ''"> and rybq = #{rybq}</if>
            <if test="cyqk != null  and cyqk != ''"> and cyqk = #{cyqk}</if>
            <if test="zdlxid != null "> and zdlxid = #{zdlxid}</if>
            <if test="jlly != null  and jlly != ''"> and jlly = #{jlly}</if>
            <if test="jlr != null  and jlr != ''"> and jlr = #{jlr}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
        </where>
        order by zdcx
    </select>

    <select id="selectSettleZdxxById" parameterType="Long" resultMap="SettleZdxxResult">
        <include refid="selectSettleZdxxVo"/>
        where id = #{id}
    </select>

    <insert id="insertSettleZdxx" parameterType="SettleZdxx" useGeneratedKeys="true" keyProperty="id">
        insert into settle_zdxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="zdlx != null">zdlx,</if>
            <if test="zdcx != null">zdcx,</if>
            <if test="jbbm != null">jbbm,</if>
            <if test="zdmc != null">zdmc,</if>
            <if test="rybq != null">rybq,</if>
            <if test="cyqk != null">cyqk,</if>
            <if test="zdlxid != null">zdlxid,</if>
            <if test="jlly != null">jlly,</if>
            <if test="jlr != null">jlr,</if>
            <if test="jgid != null">jgid,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="zdlx != null">#{zdlx},</if>
            <if test="zdcx != null">#{zdcx},</if>
            <if test="jbbm != null">#{jbbm},</if>
            <if test="zdmc != null">#{zdmc},</if>
            <if test="rybq != null">#{rybq},</if>
            <if test="cyqk != null">#{cyqk},</if>
            <if test="zdlxid != null">#{zdlxid},</if>
            <if test="jlly != null">#{jlly},</if>
            <if test="jlr != null">#{jlr},</if>
            <if test="jgid != null">#{jgid},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>
    
    <insert id="batchinsertSettleZdxx" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO settle_zdxx (
        brbs, brid, zyid, zdlx, zdcx, jbbm, zdmc, 
        rybq, cyqk, zdlxid, jlly, jlr, jgid, remark, 
        create_time, update_time, update_by, create_by
    ) VALUES  <!-- 关键修复：添加 VALUES 关键字 -->
    <foreach collection="list" item="item" separator=",">
        (
        #{item.brbs},
        #{item.brid},
        #{item.zyid},
        #{item.zdlx},
        #{item.zdcx},
        #{item.jbbm},
        #{item.zdmc},
        #{item.rybq},
        #{item.cyqk},
        #{item.zdlxid},
        #{item.jlly},
        #{item.jlr},
        #{item.jgid},
        #{item.remark},
        #{item.createTime},
        #{item.updateTime},
        #{item.updateBy},
        #{item.createBy}
        )
    </foreach>
</insert>

    <update id="updateSettleZdxx" parameterType="SettleZdxx">
        update settle_zdxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="brbs != null">brbs = #{brbs},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="zdlx != null">zdlx = #{zdlx},</if>
            <if test="zdcx != null">zdcx = #{zdcx},</if>
            <if test="jbbm != null">jbbm = #{jbbm},</if>
            <if test="zdmc != null">zdmc = #{zdmc},</if>
            <if test="rybq != null">rybq = #{rybq},</if>
            <if test="cyqk != null">cyqk = #{cyqk},</if>
            <if test="zdlxid != null">zdlxid = #{zdlxid},</if>
            <if test="jlly != null">jlly = #{jlly},</if>
            <if test="jlr != null">jlr = #{jlr},</if>
            <if test="jgid != null">jgid = #{jgid},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSettleZdxxById" parameterType="Long">
        delete from settle_zdxx where id = #{id}
    </delete>

    <delete id="deleteSettleZdxxByIds" parameterType="String">
        delete from settle_zdxx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteSettleZdxxByBrbs">
    <!-- 删除暂存清单诊断，当brbs为空时，使用brid和zyid作为条件 -->
      delete from settle_zdxx
      <where>
        and brbs = #{brbs}
        <if test="jlly != null and jlly != ''">and jlly = #{jlly}</if>
      </where>
    </delete>
</mapper>
