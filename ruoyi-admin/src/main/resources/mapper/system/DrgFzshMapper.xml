<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgFzshMapper">

    <resultMap type="DrgFzsh" id="DrgFzshResult">
        <result property="id"    column="id"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="jzh"    column="jzh"    />
        <result property="zyh"    column="zyh"    />
        <result property="xm"    column="xm"    />
        <result property="drgbh"    column="drgbh"    />
        <result property="drgmc"    column="drgmc"    />
        <result property="zyzd"    column="zyzd"    />
        <result property="jbdm"    column="jbdm"    />
        <result property="zyss"    column="zyss"    />
        <result property="ssbm"    column="ssbm"    />
        <result property="zfy"    column="zfy"    />
        <result property="zfbz"    column="zfbz"    />
        <result property="status"    column="status"    />
        <result property="submitBy"    column="submit_by"    />
        <result property="checkBy"    column="check_by"    />
        <result property="checkTime"    column="check_time"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
      <result property="cydate"    column="cydate"    />
      <result property="hisJsdate"    column="his_jsdate"    />
      <result property="difference"    column="difference"    />
    </resultMap>

    <sql id="selectDrgFzshVo">
        select id, brid, zyid, jzh, zyh, xm, drgbh, drgmc, zyzd, jbdm, zyss, ssbm, zfy, zfbz, status, submit_by, check_by, check_time, remark, create_by, update_by, create_time, update_time, difference from drg_fzsh
    </sql>

    <sql id="selectDrgFzshVoS">
        select df.id as id,
               df.brid as brid,
               df.zyid as zyid,
               df.jzh as jzh,
               zyh,
               bs.xm as xm,
               df.drgbh as drgbh,
               df.drgmc as drgmc,
               df.zyzd as zyzd,
               df.jbdm as jbdm,
               zyss,
               ssbm,
               bs.zfy as zfy,
               df.zfbz as zfbz,
               status,
               submit_by,
               check_by,
               check_time,
               remark,
               create_by,
               update_by,
               create_time,
               update_time,
               bs.cydate as cydate,
               jhz.his_jsdate as his_jsdate,
               jhz.clr_type as clr_type,
               df.difference as difference
        from drg_fzsh df left join ba_syjl bs on df.jzh = bs.brbs left join jsxx_his_zy jhz on df.brid = jhz.brid and df.zyid = jhz.zyid
    </sql>

    <select id="selectDrgFzshList" parameterType="DrgFzsh" resultMap="DrgFzshResult">
        <include refid="selectDrgFzshVoS"/>
        <where>
            <if test="brid != null  and brid != ''"> and df.brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and df.zyid = #{zyid}</if>
            <if test="jzh != null  and jzh != ''"> and df.jzh = #{jzh}</if>
            <if test="zyh != null  and zyh != ''"> and zyh = #{zyh}</if>
            <if test="xm != null  and xm != ''"> and bs.xm like concat('%', #{xm}, '%')</if>
            <if test="drgbh != null  and drgbh != ''"> and drgbh = #{drgbh}</if>
            <if test="drgmc != null  and drgmc != ''"> and drgmc = #{drgmc}</if>
            <if test="zyzd != null  and zyzd != ''"> and zyzd = #{zyzd}</if>
            <if test="jbdm != null  and jbdm != ''"> and jbdm = #{jbdm}</if>
            <if test="zyss != null  and zyss != ''"> and zyss = #{zyss}</if>
            <if test="ssbm != null  and ssbm != ''"> and ssbm = #{ssbm}</if>
            <if test="zfy != null "> and zfy = #{zfy}</if>
            <if test="zfbz != null "> and zfbz = #{zfbz}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="submitBy != null  and submitBy != ''"> and submit_by = #{submitBy}</if>
            <if test="checkBy != null  and checkBy != ''"> and check_by = #{checkBy}</if>
<!--            <if test="checkTime != null "> and check_time = #{checkTime}</if>-->
<!--            <if test="startDate != null"> and create_time &gt;= #{startDate} </if>-->
<!--            <if test="endDate != null"> and create_time &lt;= #{endDate}</if>-->
          <if test="dateType != null and dateType == 0 and startDate != null">and bs.cydate >= #{startDate}</if>
          <if test="dateType != null and dateType == 0 and endDate != null">and bs.cydate &lt;= #{endDate}</if>
          <if test="dateType != null and dateType == 1 and startDate != null">and create_time >= #{startDate}</if>
          <if test="dateType != null and dateType == 1 and endDate != null">and create_time &lt;= #{endDate}</if>
          <if test="dateType != null and dateType == 2 and startDate != null">and check_time >= #{startDate}</if>
          <if test="dateType != null and dateType == 2 and endDate != null">and check_time &lt;= #{endDate}</if>
          <if test="dateType != null and dateType == 3 and startDate != null">and jhz.his_jsdate >= #{startDate}</if>
          <if test="dateType != null and dateType == 3 and endDate != null">and jhz.his_jsdate &lt;= #{endDate}</if>
          <if test="dateType != null and dateType == 4 and startDate != null">and df.update_time >= #{startDate}</if>
          <if test="dateType != null and dateType == 4 and endDate != null">and df.update_time &lt;= #{endDate}</if>
        </where>
        <if test="orderKey != null and orderKey != ''">
          order by ${orderKey} ${orderFun}
        </if>
    </select>

    <select id="selectDrgFzshById" parameterType="Long" resultMap="DrgFzshResult">
        <include refid="selectDrgFzshVo"/>
        where id = #{id}
    </select>

    <insert id="insertDrgFzsh" parameterType="DrgFzsh" useGeneratedKeys="true" keyProperty="id">
        insert into drg_fzsh
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="jzh != null">jzh,</if>
            <if test="zyh != null">zyh,</if>
            <if test="xm != null">xm,</if>
            <if test="drgbh != null">drgbh,</if>
            <if test="drgmc != null">drgmc,</if>
            <if test="zyzd != null">zyzd,</if>
            <if test="jbdm != null">jbdm,</if>
            <if test="zyss != null">zyss,</if>
            <if test="ssbm != null">ssbm,</if>
            <if test="zfy != null">zfy,</if>
            <if test="zfbz != null">zfbz,</if>
            <if test="status != null">status,</if>
            <if test="submitBy != null">submit_by,</if>
            <if test="checkBy != null">check_by,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="difference != null">difference,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="jzh != null">#{jzh},</if>
            <if test="zyh != null">#{zyh},</if>
            <if test="xm != null">#{xm},</if>
            <if test="drgbh != null">#{drgbh},</if>
            <if test="drgmc != null">#{drgmc},</if>
            <if test="zyzd != null">#{zyzd},</if>
            <if test="jbdm != null">#{jbdm},</if>
            <if test="zyss != null">#{zyss},</if>
            <if test="ssbm != null">#{ssbm},</if>
            <if test="zfy != null">#{zfy},</if>
            <if test="zfbz != null">#{zfbz},</if>
            <if test="status != null">#{status},</if>
            <if test="submitBy != null">#{submitBy},</if>
            <if test="checkBy != null">#{checkBy},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="difference != null">#{difference},</if>
         </trim>
    </insert>

    <update id="updateDrgFzsh" parameterType="DrgFzsh">
        update drg_fzsh
        <trim prefix="SET" suffixOverrides=",">
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="zyh != null">zyh = #{zyh},</if>
            <if test="xm != null">xm = #{xm},</if>
            <if test="drgbh != null">drgbh = #{drgbh},</if>
            <if test="drgmc != null">drgmc = #{drgmc},</if>
            <if test="zyzd != null">zyzd = #{zyzd},</if>
            <if test="jbdm != null">jbdm = #{jbdm},</if>
            <if test="zyss != null">zyss = #{zyss},</if>
            <if test="zyss == null">zyss = null,</if>
            <if test="ssbm == null">ssbm = null,</if>
            <if test="ssbm != null">ssbm = #{ssbm},</if>
            <if test="zfy != null">zfy = #{zfy},</if>
            <if test="zfbz != null">zfbz = #{zfbz},</if>
            <if test="status != null">status = #{status},</if>
            <if test="submitBy != null">submit_by = #{submitBy},</if>
            <if test="checkBy != null">check_by = #{checkBy},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="difference != null">difference = #{difference},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDrgFzshById" parameterType="Long">
        delete from drg_fzsh where id = #{id}
    </delete>

    <delete id="deleteDrgFzshByIds" parameterType="String">
        delete from drg_fzsh where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectRecordByBrbs" parameterType="String" resultMap="DrgFzshResult">
        <include refid="selectDrgFzshVo"/>
        <where>
            and jzh = #{brbs}
        </where>
    </select>

    <select id="selectScqk" resultType="map">
        select a.xm as xm, bah, a.jzh as jzh, rydate, ifnull(status, -1) as status from ba_syjl a left join drg_fzsh b on a.brbs = b.jzh
        order by rydate desc
    </select>
</mapper>
