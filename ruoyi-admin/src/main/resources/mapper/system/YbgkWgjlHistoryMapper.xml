<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkWgjlHistoryMapper">

    <resultMap type="YbgkWgjlHistory" id="YbgkWgjlHistoryResult">
        <result property="id"    column="id"    />
        <result property="jkCode"    column="jk_code"    />
        <result property="xzlb"    column="xzlb"    />
        <result property="brtype"    column="brtype"    />
        <result property="jzh"    column="jzh"    />
        <result property="bed"    column="bed"    />
        <result property="jktype"    column="jktype"    />
        <result property="jklog"    column="jklog"    />
        <result property="brname"    column="brname"    />
        <result property="kdks"    column="kdks"    />
        <result property="doctor"    column="doctor"    />
        <result property="kdksname"    column="kdksname"    />
        <result property="doctorname"    column="doctorname"    />
        <result property="tshOper"    column="tsh_oper"    />
        <result property="fyType"    column="fy_type"    />
        <result property="fymId"    column="fym_id"    />
        <result property="fymName"    column="fym_name"    />
        <result property="sl"    column="sl"    />
        <result property="je"    column="je"    />
        <result property="bzmc"    column="bzmc"    />
        <result property="pl"    column="pl"    />
        <result property="jldw"    column="jldw"    />
        <result property="zkyl"    column="zkyl"    />
        <result property="kzdate"    column="kzdate"    />
        <result property="clFlag"    column="cl_flag"    />
        <result property="clHfxx"    column="cl_hfxx"    />
        <result property="jylsh"    column="jylsh"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="zyh"    column="zyh"    />
        <result property="ybbz"    column="ybbz"    />
        <result property="zyzt"    column="zyzt"    />
        <result property="jgid"    column="jgid"    />
        <result property="wjxzflag"    column="wjxzflag"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="brbs"    column="brbs"    />
        <result property="showcolor"    column="showcolor"    />
        <result property="insutype"    column="insutype"    />
        <result property="rydate"    column="rydate"    />
        <result property="cydate"    column="cydate"    />
        <result property="fydj"    column="fydj"    />
        <result property="ybbx"    column="ybbx"    />
        <result property="tc"    column="tc"    />
        <result property="de"    column="de"    />
        <result property="yljz"    column="yljz"    />
        <result property="sfz"    column="sfz"    />
        <result property="setlId"    column="setl_id"    />
        <result property="fymx"    column="fymx"    />
        <result property="fingerPrint"    column="finger_print"    />
      <result property="status"    column="status"    />
      <result property="excludeUser"    column="exclude_user"    />
      <result property="excludeDate"    column="exclude_date"    />
      <result property="jltype"    column="jltype"    />
    </resultMap>

    <sql id="selectYbgkWgjlHistoryVo">
        select id, jk_code, xzlb, brtype, jzh, bed, jktype, jklog, brname, kdks, doctor, kdksname, doctorname, tsh_oper, fy_type, fym_id, fym_name, sl, je, bzmc, pl, jldw, zkyl, kzdate, cl_flag, cl_hfxx, jylsh, create_by, create_date, update_by, update_date, remarks, del_flag, zyh, ybbz, zyzt, jgid, wjxzflag, brid, zyid, brbs, showcolor, insutype, rydate, cydate, fydj, ybbx, tc, de, yljz, sfz, setl_id, fymx, finger_print, status, exclude_user, exclude_date, jltype from ybgk_wgjl_history
    </sql>

    <select id="selectYbgkWgjlHistoryList" parameterType="YbgkWgjlHistory" resultMap="YbgkWgjlHistoryResult">
        <include refid="selectYbgkWgjlHistoryVo"/>
        <where>
            <if test="jkCode != null  and jkCode != ''"> and jk_code = #{jkCode}</if>
            <if test="xzlb != null  and xzlb != ''"> and xzlb = #{xzlb}</if>
            <if test="brtype != null  and brtype != ''"> and brtype = #{brtype}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="bed != null  and bed != ''"> and bed = #{bed}</if>
            <if test="jktype != null  and jktype != ''"> and jktype = #{jktype}</if>
            <if test="jklog != null  and jklog != ''"> and jklog = #{jklog}</if>
            <if test="brname != null  and brname != ''"> and brname like concat('%', #{brname}, '%')</if>
            <if test="kdks != null  and kdks != ''"> and kdks = #{kdks}</if>
            <if test="doctor != null  and doctor != ''"> and doctor = #{doctor}</if>
            <if test="kdksname != null  and kdksname != ''"> and kdksname like concat('%', #{kdksname}, '%')</if>
            <if test="doctorname != null  and doctorname != ''"> and doctorname like concat('%', #{doctorname}, '%')</if>
            <if test="tshOper != null  and tshOper != ''"> and tsh_oper = #{tshOper}</if>
            <if test="fyType != null  and fyType != ''"> and fy_type = #{fyType}</if>
            <if test="fymId != null  and fymId != ''"> and fym_id = #{fymId}</if>
            <if test="fymName != null  and fymName != ''"> and fym_name like concat('%', #{fymName}, '%')</if>
            <if test="sl != null "> and sl = #{sl}</if>
            <if test="je != null "> and je = #{je}</if>
            <if test="bzmc != null  and bzmc != ''"> and bzmc = #{bzmc}</if>
            <if test="pl != null  and pl != ''"> and pl = #{pl}</if>
            <if test="jldw != null  and jldw != ''"> and jldw = #{jldw}</if>
            <if test="zkyl != null  and zkyl != ''"> and zkyl = #{zkyl}</if>
            <if test="kzdate != null "> and kzdate = #{kzdate}</if>
            <if test="clFlag != null  and clFlag != ''"> and cl_flag = #{clFlag}</if>
            <if test="clHfxx != null  and clHfxx != ''"> and cl_hfxx = #{clHfxx}</if>
            <if test="jylsh != null  and jylsh != ''"> and jylsh = #{jylsh}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="zyh != null  and zyh != ''"> and zyh = #{zyh}</if>
            <if test="ybbz != null  and ybbz != ''"> and ybbz = #{ybbz}</if>
            <if test="zyzt != null  and zyzt != ''"> and zyzt = #{zyzt}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
            <if test="wjxzflag != null  and wjxzflag != ''"> and wjxzflag = #{wjxzflag}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="showcolor != null  and showcolor != ''"> and showcolor = #{showcolor}</if>
            <if test="insutype != null  and insutype != ''"> and insutype = #{insutype}</if>
            <if test="rydate != null "> and rydate = #{rydate}</if>
            <if test="cydate != null "> and cydate = #{cydate}</if>
            <if test="fydj != null  and fydj != ''"> and fydj = #{fydj}</if>
            <if test="ybbx != null "> and ybbx = #{ybbx}</if>
            <if test="tc != null "> and tc = #{tc}</if>
            <if test="de != null "> and de = #{de}</if>
            <if test="yljz != null "> and yljz = #{yljz}</if>
            <if test="sfz != null  and sfz != ''"> and sfz = #{sfz}</if>
            <if test="setlId != null  and setlId != ''"> and setl_id = #{setlId}</if>
            <if test="fymx != null  and fymx != ''"> and fymx = #{fymx}</if>
            <if test="fingerPrint != null  and fingerPrint != ''"> and finger_print = #{fingerPrint}</if>
          <if test="status != null  and status != ''"> and status = #{status}</if>
          <if test="jltype != null  and jltype != ''"> and jltype = #{jltype}</if>
           <if test="startDate != null and startDate != ''"> and create_date &gt;= #{startDate} </if>
          <if test="endDate != null and endDate != ''"> and create_date &lt;= #{endDate} </if>
        </where>
    </select>

    <select id="selectYbgkWgjlHistoryById" parameterType="String" resultMap="YbgkWgjlHistoryResult">
        <include refid="selectYbgkWgjlHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertYbgkWgjlHistory" parameterType="YbgkWgjlHistory">
        insert into ybgk_wgjl_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="jkCode != null">jk_code,</if>
            <if test="xzlb != null">xzlb,</if>
            <if test="brtype != null">brtype,</if>
            <if test="jzh != null">jzh,</if>
            <if test="bed != null">bed,</if>
            <if test="jktype != null">jktype,</if>
            <if test="jklog != null">jklog,</if>
            <if test="brname != null">brname,</if>
            <if test="kdks != null">kdks,</if>
            <if test="doctor != null">doctor,</if>
            <if test="kdksname != null">kdksname,</if>
            <if test="doctorname != null">doctorname,</if>
            <if test="tshOper != null">tsh_oper,</if>
            <if test="fyType != null">fy_type,</if>
            <if test="fymId != null">fym_id,</if>
            <if test="fymName != null">fym_name,</if>
            <if test="sl != null">sl,</if>
            <if test="je != null">je,</if>
            <if test="bzmc != null">bzmc,</if>
            <if test="pl != null">pl,</if>
            <if test="jldw != null">jldw,</if>
            <if test="zkyl != null">zkyl,</if>
            <if test="kzdate != null">kzdate,</if>
            <if test="clFlag != null">cl_flag,</if>
            <if test="clHfxx != null">cl_hfxx,</if>
            <if test="jylsh != null">jylsh,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="zyh != null">zyh,</if>
            <if test="ybbz != null">ybbz,</if>
            <if test="zyzt != null">zyzt,</if>
            <if test="jgid != null">jgid,</if>
            <if test="wjxzflag != null">wjxzflag,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="brbs != null">brbs,</if>
            <if test="showcolor != null">showcolor,</if>
            <if test="insutype != null">insutype,</if>
            <if test="rydate != null">rydate,</if>
            <if test="cydate != null">cydate,</if>
            <if test="fydj != null">fydj,</if>
            <if test="ybbx != null">ybbx,</if>
            <if test="tc != null">tc,</if>
            <if test="de != null">de,</if>
            <if test="yljz != null">yljz,</if>
            <if test="sfz != null">sfz,</if>
            <if test="setlId != null">setl_id,</if>
            <if test="fymx != null">fymx,</if>
            <if test="fingerPrint != null">finger_print,</if>
          <if test="status != null">status,</if>
          <if test="excludeUser != null">exclude_user,</if>
          <if test="excludeDate != null">exclude_date,</if>
          <if test="jltype != null">jltype,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="jkCode != null">#{jkCode},</if>
            <if test="xzlb != null">#{xzlb},</if>
            <if test="brtype != null">#{brtype},</if>
            <if test="jzh != null">#{jzh},</if>
            <if test="bed != null">#{bed},</if>
            <if test="jktype != null">#{jktype},</if>
            <if test="jklog != null">#{jklog},</if>
            <if test="brname != null">#{brname},</if>
            <if test="kdks != null">#{kdks},</if>
            <if test="doctor != null">#{doctor},</if>
            <if test="kdksname != null">#{kdksname},</if>
            <if test="doctorname != null">#{doctorname},</if>
            <if test="tshOper != null">#{tshOper},</if>
            <if test="fyType != null">#{fyType},</if>
            <if test="fymId != null">#{fymId},</if>
            <if test="fymName != null">#{fymName},</if>
            <if test="sl != null">#{sl},</if>
            <if test="je != null">#{je},</if>
            <if test="bzmc != null">#{bzmc},</if>
            <if test="pl != null">#{pl},</if>
            <if test="jldw != null">#{jldw},</if>
            <if test="zkyl != null">#{zkyl},</if>
            <if test="kzdate != null">#{kzdate},</if>
            <if test="clFlag != null">#{clFlag},</if>
            <if test="clHfxx != null">#{clHfxx},</if>
            <if test="jylsh != null">#{jylsh},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="zyh != null">#{zyh},</if>
            <if test="ybbz != null">#{ybbz},</if>
            <if test="zyzt != null">#{zyzt},</if>
            <if test="jgid != null">#{jgid},</if>
            <if test="wjxzflag != null">#{wjxzflag},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="brbs != null">#{brbs},</if>
            <if test="showcolor != null">#{showcolor},</if>
            <if test="insutype != null">#{insutype},</if>
            <if test="rydate != null">#{rydate},</if>
            <if test="cydate != null">#{cydate},</if>
            <if test="fydj != null">#{fydj},</if>
            <if test="ybbx != null">#{ybbx},</if>
            <if test="tc != null">#{tc},</if>
            <if test="de != null">#{de},</if>
            <if test="yljz != null">#{yljz},</if>
            <if test="sfz != null">#{sfz},</if>
            <if test="setlId != null">#{setlId},</if>
            <if test="fymx != null">#{fymx},</if>
            <if test="fingerPrint != null">#{fingerPrint},</if>
          <if test="status != null">#{status},</if>
          <if test="excludeUser != null">#{excludeUser},</if>
          <if test="excludeDate != null">#{excludeDate},</if>
          <if test="jltype != null">#{jltype},</if>
         </trim>
    </insert>

    <update id="updateYbgkWgjlHistory" parameterType="YbgkWgjlHistory">
        update ybgk_wgjl_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="jkCode != null">jk_code = #{jkCode},</if>
            <if test="xzlb != null">xzlb = #{xzlb},</if>
            <if test="brtype != null">brtype = #{brtype},</if>
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="bed != null">bed = #{bed},</if>
            <if test="jktype != null">jktype = #{jktype},</if>
            <if test="jklog != null">jklog = #{jklog},</if>
            <if test="brname != null">brname = #{brname},</if>
            <if test="kdks != null">kdks = #{kdks},</if>
            <if test="doctor != null">doctor = #{doctor},</if>
            <if test="kdksname != null">kdksname = #{kdksname},</if>
            <if test="doctorname != null">doctorname = #{doctorname},</if>
            <if test="tshOper != null">tsh_oper = #{tshOper},</if>
            <if test="fyType != null">fy_type = #{fyType},</if>
            <if test="fymId != null">fym_id = #{fymId},</if>
            <if test="fymName != null">fym_name = #{fymName},</if>
            <if test="sl != null">sl = #{sl},</if>
            <if test="je != null">je = #{je},</if>
            <if test="bzmc != null">bzmc = #{bzmc},</if>
            <if test="pl != null">pl = #{pl},</if>
            <if test="jldw != null">jldw = #{jldw},</if>
            <if test="zkyl != null">zkyl = #{zkyl},</if>
            <if test="kzdate != null">kzdate = #{kzdate},</if>
            <if test="clFlag != null">cl_flag = #{clFlag},</if>
            <if test="clHfxx != null">cl_hfxx = #{clHfxx},</if>
            <if test="jylsh != null">jylsh = #{jylsh},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="zyh != null">zyh = #{zyh},</if>
            <if test="ybbz != null">ybbz = #{ybbz},</if>
            <if test="zyzt != null">zyzt = #{zyzt},</if>
            <if test="jgid != null">jgid = #{jgid},</if>
            <if test="wjxzflag != null">wjxzflag = #{wjxzflag},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="brbs != null">brbs = #{brbs},</if>
            <if test="showcolor != null">showcolor = #{showcolor},</if>
            <if test="insutype != null">insutype = #{insutype},</if>
            <if test="rydate != null">rydate = #{rydate},</if>
            <if test="cydate != null">cydate = #{cydate},</if>
            <if test="fydj != null">fydj = #{fydj},</if>
            <if test="ybbx != null">ybbx = #{ybbx},</if>
            <if test="tc != null">tc = #{tc},</if>
            <if test="de != null">de = #{de},</if>
            <if test="yljz != null">yljz = #{yljz},</if>
            <if test="sfz != null">sfz = #{sfz},</if>
            <if test="setlId != null">setl_id = #{setlId},</if>
            <if test="fymx != null">fymx = #{fymx},</if>
            <if test="fingerPrint != null">finger_print = #{fingerPrint},</if>
          <if test="status != null">status = #{status},</if>
          <if test="jltype != null">jltype = #{jltype},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbgkWgjlHistoryById" parameterType="String">
        delete from ybgk_wgjl_history where id = #{id}
    </delete>

    <delete id="deleteYbgkWgjlHistoryByIds" parameterType="String">
        delete from ybgk_wgjl_history where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
