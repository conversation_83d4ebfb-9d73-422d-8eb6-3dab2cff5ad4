<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgIcddyssMapper">

    <resultMap type="DrgIcddyss" id="DrgIcddyssResult">
        <result property="bzbm"    column="bzbm"    />
        <result property="ssbm"    column="ssbm"    />
        <result property="ssmc"    column="ssmc"    />
        <result property="bzmc"    column="bzmc"    />
        <result property="xmbm"    column="xmbm"    />
        <result property="bzbmssbm"    column="bzbmssbm"    />
        <result property="flag"    column="flag"    />
    </resultMap>

    <sql id="selectDrgIcddyssVo">
        select distinct bzbm, ssbm, ssmc, bzmc, xmbm, bzbmssbm, flag from drg_icddyss
    </sql>

    <select id="selectDrgIcddyssList" parameterType="DrgIcddyss" resultMap="DrgIcddyssResult">
         select distinct bzbm, ssbm, ssmc, bzmc, xmbm, bzbmssbm, flag from drg_icddyss
        <where>
            <if test="bzbm != null  and bzbm != ''"> and bzbm = #{bzbm}</if>
            <if test="ssbm != null  and ssbm != ''"> and ssbm = #{ssbm}</if>
            <if test="ssmc != null  and ssmc != ''"> and ssmc = #{ssmc}</if>
            <if test="bzmc != null  and bzmc != ''"> and bzmc = #{bzmc}</if>
            <if test="xmbm != null  and xmbm != ''"> and xmbm = #{xmbm}</if>
            <if test="bzbmssbm != null  and bzbmssbm != ''"> and bzbmssbm = #{bzbmssbm}</if>
            <if test="flag != null "> and flag = #{flag}</if>
        </where>
    </select>

    <select id="selectDrgIcddyssByBzbm" parameterType="String" resultMap="DrgIcddyssResult">
        <include refid="selectDrgIcddyssVo"/>
        where bzbm = #{bzbm}
    </select>

    <insert id="insertDrgIcddyss" parameterType="DrgIcddyss">
        insert into drg_icddyss
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bzbm != null and bzbm != ''">bzbm,</if>
            <if test="ssbm != null and ssbm != ''">ssbm,</if>
            <if test="ssmc != null">ssmc,</if>
            <if test="bzmc != null">bzmc,</if>
            <if test="xmbm != null">xmbm,</if>
            <if test="bzbmssbm != null">bzbmssbm,</if>
            <if test="flag != null">flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bzbm != null and bzbm != ''">#{bzbm},</if>
            <if test="ssbm != null and ssbm != ''">#{ssbm},</if>
            <if test="ssmc != null">#{ssmc},</if>
            <if test="bzmc != null">#{bzmc},</if>
            <if test="xmbm != null">#{xmbm},</if>
            <if test="bzbmssbm != null">#{bzbmssbm},</if>
            <if test="flag != null">#{flag},</if>
         </trim>
    </insert>
 
    <update id="updateDrgIcddyss" parameterType="DrgIcddyss">
        update drg_icddyss
        <trim prefix="SET" suffixOverrides=",">
            <if test="ssbm != null and ssbm != ''">ssbm = #{ssbm},</if>
            <if test="ssmc != null">ssmc = #{ssmc},</if>
            <if test="bzmc != null">bzmc = #{bzmc},</if>
            <if test="xmbm != null">xmbm = #{xmbm},</if>
            <if test="bzbmssbm != null">bzbmssbm = #{bzbmssbm},</if>
            <if test="flag != null">flag = #{flag},</if>
        </trim>
        where bzbm = #{bzbm}
    </update>

    <delete id="deleteDrgIcddyssByBzbm" parameterType="String">
        delete from drg_icddyss where bzbm = #{bzbm}
    </delete>
  <delete id="deleteDrgIcddyss" parameterType="DrgIcddyss">
    delete from drg_icddyss where bzbm = #{bzbm}
    and ssmc = #{ssmc}  and bzmc = #{bzmc}  and ssbm = #{ssbm}
  </delete>

    <delete id="deleteDrgIcddyssByBzbms" parameterType="String">
        delete from drg_icddyss where bzbm in
        <foreach item="bzbm" collection="array" open="(" separator="," close=")">
            #{bzbm}
        </foreach>
    </delete>
</mapper>
