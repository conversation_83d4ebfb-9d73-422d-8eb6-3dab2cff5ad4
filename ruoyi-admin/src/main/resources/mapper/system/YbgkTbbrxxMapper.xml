<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkTbbrxxMapper">
    
    <resultMap type="YbgkTbbrxx" id="YbgkTbbrxxResult">
        <result property="sfz"    column="sfz"    />
    </resultMap>

    <sql id="selectYbgkTbbrxxVo">
        select sfz from ybgk_tbbrxx
    </sql>

    <select id="selectYbgkTbbrxxList" parameterType="YbgkTbbrxx" resultMap="YbgkTbbrxxResult">
        <include refid="selectYbgkTbbrxxVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectYbgkTbbrxxBySfz" parameterType="String" resultMap="YbgkTbbrxxResult">
        <include refid="selectYbgkTbbrxxVo"/>
        where sfz = #{sfz}
    </select>
        
    <insert id="insertYbgkTbbrxx" parameterType="YbgkTbbrxx">
        insert into ybgk_tbbrxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sfz != null">sfz,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sfz != null">#{sfz},</if>
         </trim>
    </insert>

    <update id="updateYbgkTbbrxx" parameterType="YbgkTbbrxx">
        update ybgk_tbbrxx
        <trim prefix="SET" suffixOverrides=",">
        </trim>
        where sfz = #{sfz}
    </update>

    <delete id="deleteYbgkTbbrxxBySfz" parameterType="String">
        delete from ybgk_tbbrxx where sfz = #{sfz}
    </delete>

    <delete id="deleteYbgkTbbrxxBySfzs" parameterType="String">
        delete from ybgk_tbbrxx where sfz in 
        <foreach item="sfz" collection="array" open="(" separator="," close=")">
            #{sfz}
        </foreach>
    </delete>
</mapper>