<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.Icd10Mapper">

    <resultMap type="Icd10" id="Icd10Result">
        <result property="bzbm"    column="bzbm"    />
        <result property="bzmc"    column="bzmc"    />
        <result property="zjm"    column="zjm"    />
        <result property="bzfl"    column="bzfl"    />
        <result property="tjm"    column="tjm"    />
        <result property="jmbzfl"    column="jmbzfl"    />
        <result property="sybzfl"    column="sybzfl"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectIcd10Vo">
        select bzbm, bzmc, zjm, bzfl, tjm, jmbzfl, sybzfl, type from icd10
    </sql>

    <select id="selectIcd10List" parameterType="Icd10" resultMap="Icd10Result">
        <include refid="selectIcd10Vo"/>
        <where>
            <if test="bzmc != null  and bzmc != ''"> and bzmc = #{bzmc}</if>
            <if test="zjm != null  and zjm != ''"> and zjm = #{zjm}</if>
            <if test="bzfl != null  and bzfl != ''"> and bzfl = #{bzfl}</if>
            <if test="tjm != null  and tjm != ''"> and tjm = #{tjm}</if>
            <if test="jmbzfl != null  and jmbzfl != ''"> and jmbzfl = #{jmbzfl}</if>
            <if test="sybzfl != null  and sybzfl != ''"> and sybzfl = #{sybzfl}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
        </where>
    </select>

    <select id="selectIcd10ByBzbm" parameterType="String" resultMap="Icd10Result">
        <include refid="selectIcd10Vo"/>
        where bzbm = #{bzbm}
    </select>

    <insert id="insertIcd10" parameterType="Icd10">
        insert into icd10
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bzbm != null">bzbm,</if>
            <if test="bzmc != null">bzmc,</if>
            <if test="zjm != null">zjm,</if>
            <if test="bzfl != null">bzfl,</if>
            <if test="tjm != null">tjm,</if>
            <if test="jmbzfl != null">jmbzfl,</if>
            <if test="sybzfl != null">sybzfl,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bzbm != null">#{bzbm},</if>
            <if test="bzmc != null">#{bzmc},</if>
            <if test="zjm != null">#{zjm},</if>
            <if test="bzfl != null">#{bzfl},</if>
            <if test="tjm != null">#{tjm},</if>
            <if test="jmbzfl != null">#{jmbzfl},</if>
            <if test="sybzfl != null">#{sybzfl},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateIcd10" parameterType="Icd10">
        update icd10
        <trim prefix="SET" suffixOverrides=",">
            <if test="bzmc != null">bzmc = #{bzmc},</if>
            <if test="zjm != null">zjm = #{zjm},</if>
            <if test="bzfl != null">bzfl = #{bzfl},</if>
            <if test="tjm != null">tjm = #{tjm},</if>
            <if test="jmbzfl != null">jmbzfl = #{jmbzfl},</if>
            <if test="sybzfl != null">sybzfl = #{sybzfl},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where bzbm = #{bzbm}
    </update>

    <delete id="deleteIcd10ByBzbm" parameterType="String">
        delete from icd10 where bzbm = #{bzbm}
    </delete>

    <delete id="deleteIcd10ByBzbms" parameterType="String">
        delete from icd10 where bzbm in
        <foreach item="bzbm" collection="array" open="(" separator="," close=")">
            #{bzbm}
        </foreach>
    </delete>
  <delete id="deleteAllIcd10" >
    delete from icd10
  </delete>
  <insert id="insertIcd10UpdateAndInsertByEntity">
    INSERT INTO drgs.icd10 (bzbm,bzmc)
    VALUES
    <foreach collection="list" item="entity" separator=",">
      (#{entity.bzbm}, #{entity.bzmc})
    </foreach>
    ON DUPLICATE KEY UPDATE bzmc = VALUES(bzmc);
  </insert>
</mapper>
