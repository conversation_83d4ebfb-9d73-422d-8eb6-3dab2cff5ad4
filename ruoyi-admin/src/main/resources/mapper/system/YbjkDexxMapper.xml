<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbjkDexxMapper">
    
    <resultMap type="YbjkDexx" id="YbjkDexxResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="officeId"    column="office_id"    />
        <result property="xzlb"    column="xzlb"    />
        <result property="de"    column="de"    />
        <result property="debh"    column="debh"    />
        <result property="debegin"    column="debegin"    />
        <result property="deend"    column="deend"    />
        <result property="jkCode"    column="jk_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectYbjkDexxVo">
        select id, type, office_id, xzlb, de, debh, debegin, deend, jk_code, create_by, create_date, update_by, update_date, remarks, del_flag from ybjk_dexx
    </sql>

    <select id="selectYbjkDexxList" parameterType="YbjkDexx" resultMap="YbjkDexxResult">
        <include refid="selectYbjkDexxVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="officeId != null  and officeId != ''"> and office_id = #{officeId}</if>
            <if test="xzlb != null  and xzlb != ''"> and xzlb = #{xzlb}</if>
            <if test="de != null "> and de = #{de}</if>
            <if test="debh != null  and debh != ''"> and debh = #{debh}</if>
            <if test="debegin != null "> and debegin = #{debegin}</if>
            <if test="deend != null "> and deend = #{deend}</if>
            <if test="jkCode != null  and jkCode != ''"> and jk_code = #{jkCode}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectYbjkDexxById" parameterType="Long" resultMap="YbjkDexxResult">
        <include refid="selectYbjkDexxVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertYbjkDexx" parameterType="YbjkDexx" useGeneratedKeys="true" keyProperty="id">
        insert into ybjk_dexx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="officeId != null">office_id,</if>
            <if test="xzlb != null and xzlb != ''">xzlb,</if>
            <if test="de != null">de,</if>
            <if test="debh != null">debh,</if>
            <if test="debegin != null">debegin,</if>
            <if test="deend != null">deend,</if>
            <if test="jkCode != null">jk_code,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="officeId != null">#{officeId},</if>
            <if test="xzlb != null and xzlb != ''">#{xzlb},</if>
            <if test="de != null">#{de},</if>
            <if test="debh != null">#{debh},</if>
            <if test="debegin != null">#{debegin},</if>
            <if test="deend != null">#{deend},</if>
            <if test="jkCode != null">#{jkCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateYbjkDexx" parameterType="YbjkDexx">
        update ybjk_dexx
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="officeId != null">office_id = #{officeId},</if>
            <if test="xzlb != null and xzlb != ''">xzlb = #{xzlb},</if>
            <if test="de != null">de = #{de},</if>
            <if test="debh != null">debh = #{debh},</if>
            <if test="debegin != null">debegin = #{debegin},</if>
            <if test="deend != null">deend = #{deend},</if>
            <if test="jkCode != null">jk_code = #{jkCode},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbjkDexxById" parameterType="Long">
        delete from ybjk_dexx where id = #{id}
    </delete>

    <delete id="deleteYbjkDexxByIds" parameterType="String">
        delete from ybjk_dexx where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>