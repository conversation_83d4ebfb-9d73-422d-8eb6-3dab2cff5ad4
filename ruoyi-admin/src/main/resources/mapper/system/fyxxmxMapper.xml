<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fyxxmxMapper">
    <resultMap id="fyxxResult" type="fyxxmxResult">
      <result property="xmbm" column="xmbm"/>
      <result property="xmmc" column="xmmc"/>
      <result property="sl" column="sl"/>
      <result property="price" column="price"/>
      <result property="je" column="je"/>
      <result property="xm" column="xm"/>
      <result property="sfz" column="sfz"/>
      <result property="rydate" column="rydate"/>
      <result property="cydate" column="cydate"/>
      <result property="jsdate" column="jsdate"/>
      <result property="insutype" column="insutype"/>
    </resultMap>
    <resultMap type="fyxxmx" id="fyxxmxResult">
        <result property="id"    column="id"    />
        <result property="xmbm"    column="xmbm"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="sl"    column="sl"    />
        <result property="price"    column="price"    />
        <result property="je"    column="je"    />
        <result property="xm"    column="xm"    />
        <result property="sfz"    column="sfz"    />
        <result property="rydate"    column="rydate"    />
        <result property="cydate"    column="cydate"    />
        <result property="jsdate"    column="jsdate"    />
        <result property="insutype"    column="insutype"    />
        <result property="insutypeName"    column="insutype_name"    />
        <result property="ksname"    column="ksname"    />
        <result property="ysname"    column="ysname"    />
        <result property="monthPeriod"    column="month_period"    />
    </resultMap>

    <sql id="selectfyxxmxVo">
        select id, xmbm, xmmc, sl, price, je, xm, sfz, rydate, cydate, jsdate, insutype, insutype_name, ksname, ysname, month_period from temp_final_result
    </sql>
  <select id="selectFyxxMxByTime" parameterType="fyxxmx" resultMap="fyxxResult">
    SELECT  a.xmbm, a.xmmc, a.sl, a.price, a.je, b.psn_name AS xm,b.certno AS sfz, b.begndate AS rydate,
    b.enddate AS cydate, b.his_jsdate AS jsdate ,
    CASE WHEN  insutype ='310' THEN '职工' WHEN insutype ='390' THEN '居民' WHEN  insutype ='3101' THEN '异地职工' WHEN insutype ='3901' THEN '异地居民'  ELSE '' END AS insutype
    FROM fyxx a
    LEFT JOIN  jsxx_his_zy b ON a.jzh = b.brbs
    WHERE fydate &gt;= #{startTime} AND fydate &lt; #{endTime} AND xmbm = #{xmbm}
      <if test="billno != null and billno !='' ">and a.billno = #{billno}</if>
    AND b.psn_name IS NOT  NULL
    UNION ALL
    SELECT a.xmbm, a.xmmc, a.sl, a.price, a.je, b.psn_name AS xm,b.certno AS sfz, b.begndate AS rydate,
    b.enddate AS cydate, b.his_jsdate AS jsdate ,
    CASE WHEN  insutype ='310' THEN '职工' WHEN insutype ='390' THEN '居民' WHEN  insutype ='3101' THEN '异地职工' WHEN insutype ='3901' THEN '异地居民'  ELSE '' END AS insutype
    FROM fyxx_cy a
    LEFT JOIN  jsxx_his_zy b ON a.jzh = b.brbs
    WHERE fydate &gt;= #{startTime} AND fydate &lt; #{endTime} AND xmbm = #{xmbm}
    <if test="billno != null and billno !='' ">and a.billno = #{billno}</if>
    AND b.psn_name IS NOT  NULL
    ORDER BY xmbm,jsdate;
  </select>

    <select id="selectfyxxmxList" parameterType="fyxxmx" resultMap="fyxxmxResult">
        <include refid="selectfyxxmxVo"/>
        <where>
            <if test="xmbm != null  and xmbm != ''"> and xmbm = #{xmbm}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc = #{xmmc}</if>
            <if test="sl != null "> and sl = #{sl}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="je != null "> and je = #{je}</if>
            <if test="xm != null  and xm != ''"> and xm = #{xm}</if>
            <if test="sfz != null  and sfz != ''"> and sfz = #{sfz}</if>
            <if test="rydate != null "> and rydate = #{rydate}</if>
            <if test="cydate != null "> and cydate = #{cydate}</if>
            <if test="jsdate != null "> and jsdate = #{jsdate}</if>
            <if test="insutype != null  and insutype != ''"> and insutype = #{insutype}</if>
            <if test="insutypeName != null  and insutypeName != ''"> and insutype_name like concat('%', #{insutypeName}, '%')</if>
            <if test="ksname != null  and ksname != ''"> and ksname like concat('%', #{ksname}, '%')</if>
            <if test="ysname != null  and ysname != ''"> and ysname like concat('%', #{ysname}, '%')</if>
        </where>
    </select>

    <select id="selectfyxxmxById" parameterType="Long" resultMap="fyxxmxResult">
        <include refid="selectfyxxmxVo"/>
        where id = #{id}
    </select>

    <insert id="insertfyxxmx" parameterType="fyxxmx" useGeneratedKeys="true" keyProperty="id">
        insert into temp_final_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xmbm != null">xmbm,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="sl != null">sl,</if>
            <if test="price != null">price,</if>
            <if test="je != null">je,</if>
            <if test="xm != null">xm,</if>
            <if test="sfz != null">sfz,</if>
            <if test="rydate != null">rydate,</if>
            <if test="cydate != null">cydate,</if>
            <if test="jsdate != null">jsdate,</if>
            <if test="insutype != null">insutype,</if>
            <if test="insutypeName != null">insutype_name,</if>
            <if test="ksname != null">ksname,</if>
            <if test="ysname != null">ysname,</if>
            <if test="monthPeriod != null">month_period,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xmbm != null">#{xmbm},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="sl != null">#{sl},</if>
            <if test="price != null">#{price},</if>
            <if test="je != null">#{je},</if>
            <if test="xm != null">#{xm},</if>
            <if test="sfz != null">#{sfz},</if>
            <if test="rydate != null">#{rydate},</if>
            <if test="cydate != null">#{cydate},</if>
            <if test="jsdate != null">#{jsdate},</if>
            <if test="insutype != null">#{insutype},</if>
            <if test="insutypeName != null">#{insutypeName},</if>
            <if test="ksname != null">#{ksname},</if>
            <if test="ysname != null">#{ysname},</if>
            <if test="monthPeriod != null">#{monthPeriod},</if>
         </trim>
    </insert>

    <update id="updatefyxxmx" parameterType="fyxxmx">
        update temp_final_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="xmbm != null">xmbm = #{xmbm},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="sl != null">sl = #{sl},</if>
            <if test="price != null">price = #{price},</if>
            <if test="je != null">je = #{je},</if>
            <if test="xm != null">xm = #{xm},</if>
            <if test="sfz != null">sfz = #{sfz},</if>
            <if test="rydate != null">rydate = #{rydate},</if>
            <if test="cydate != null">cydate = #{cydate},</if>
            <if test="jsdate != null">jsdate = #{jsdate},</if>
            <if test="insutype != null">insutype = #{insutype},</if>
            <if test="insutypeName != null">insutype_name = #{insutypeName},</if>
            <if test="ksname != null">ksname = #{ksname},</if>
            <if test="ysname != null">ysname = #{ysname},</if>
            <if test="monthPeriod != null">month_period = #{monthPeriod},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletefyxxmxById" parameterType="Long">
        delete from temp_final_result where id = #{id}
    </delete>

    <delete id="deletefyxxmxByIds" parameterType="String">
        delete from temp_final_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
