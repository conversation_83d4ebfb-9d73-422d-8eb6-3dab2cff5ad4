<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YzxxMapper">

    <resultMap type="Yzxx" id="YzxxResult">
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="yznr"    column="yznr"    />
        <result property="yszt"    column="yszt"    />
        <result property="yzqx"    column="yzqx"    />
        <result property="opdate"    column="opdate"    />
        <result property="stopdate"    column="stopdate"    />
        <result property="kzksid"    column="kzksid"    />
        <result property="kzks"    column="kzks"    />
        <result property="zxksid"    column="zxksid"    />
        <result property="zxks"    column="zxks"    />
        <result property="kzys"    column="kzys"    />
        <result property="tzys"    column="tzys"    />
        <result property="yzid"    column="yzid"    />
        <result property="hisid"    column="hisid"    />
        <result property="kmname"    column="kmname"    />
        <result property="id"    column="id"    />
    </resultMap>

    <sql id="selectYzxxVo">
        select jzh, brid, zyid, yznr, yszt, yzqx, opdate, stopdate, kzksid, kzks, zxksid, zxks, kzys, tzys, yzid, hisid, kmname, id from yzxx
    </sql>

    <select id="selectYzxxList" parameterType="Yzxx" resultMap="YzxxResult">
        <include refid="selectYzxxVo"/>
        <where>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="yznr != null  and yznr != ''"> and yznr = #{yznr}</if>
            <if test="yszt != null  and yszt != ''"> and yszt = #{yszt}</if>
            <if test="yzqx != null  and yzqx != ''"> and yzqx = #{yzqx}</if>
            <if test="opdate != null "> and opdate = #{opdate}</if>
            <if test="stopdate != null "> and stopdate = #{stopdate}</if>
            <if test="kzksid != null  and kzksid != ''"> and kzksid = #{kzksid}</if>
            <if test="kzks != null  and kzks != ''"> and kzks = #{kzks}</if>
            <if test="zxksid != null  and zxksid != ''"> and zxksid = #{zxksid}</if>
            <if test="zxks != null  and zxks != ''"> and zxks = #{zxks}</if>
            <if test="kzys != null  and kzys != ''"> and kzys = #{kzys}</if>
            <if test="tzys != null  and tzys != ''"> and tzys = #{tzys}</if>
            <if test="yzid != null  and yzid != ''"> and yzid = #{yzid}</if>
            <if test="hisid != null  and hisid != ''"> and hisid = #{hisid}</if>
            <if test="kmname != null  and kmname != ''"> and kmname like concat('%', #{kmname}, '%')</if>
        </where>
    </select>

    <select id="selectYzxxById" parameterType="Long" resultMap="YzxxResult">
        <include refid="selectYzxxVo"/>
        where id = #{id}
    </select>

    <insert id="insertYzxx" parameterType="Yzxx" useGeneratedKeys="true" keyProperty="id">
        insert into yzxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jzh != null">jzh,</if>
            <if test="brid != null and brid != ''">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="yznr != null">yznr,</if>
            <if test="yszt != null">yszt,</if>
            <if test="yzqx != null">yzqx,</if>
            <if test="opdate != null">opdate,</if>
            <if test="stopdate != null">stopdate,</if>
            <if test="kzksid != null">kzksid,</if>
            <if test="kzks != null">kzks,</if>
            <if test="zxksid != null">zxksid,</if>
            <if test="zxks != null">zxks,</if>
            <if test="kzys != null">kzys,</if>
            <if test="tzys != null">tzys,</if>
            <if test="yzid != null">yzid,</if>
            <if test="hisid != null">hisid,</if>
            <if test="kmname != null">kmname,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jzh != null">#{jzh},</if>
            <if test="brid != null and brid != ''">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="yznr != null">#{yznr},</if>
            <if test="yszt != null">#{yszt},</if>
            <if test="yzqx != null">#{yzqx},</if>
            <if test="opdate != null">#{opdate},</if>
            <if test="stopdate != null">#{stopdate},</if>
            <if test="kzksid != null">#{kzksid},</if>
            <if test="kzks != null">#{kzks},</if>
            <if test="zxksid != null">#{zxksid},</if>
            <if test="zxks != null">#{zxks},</if>
            <if test="kzys != null">#{kzys},</if>
            <if test="tzys != null">#{tzys},</if>
            <if test="yzid != null">#{yzid},</if>
            <if test="hisid != null">#{hisid},</if>
            <if test="kmname != null">#{kmname},</if>
         </trim>
    </insert>

    <update id="updateYzxx" parameterType="Yzxx">
        update yzxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="brid != null and brid != ''">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="yznr != null">yznr = #{yznr},</if>
            <if test="yszt != null">yszt = #{yszt},</if>
            <if test="yzqx != null">yzqx = #{yzqx},</if>
            <if test="opdate != null">opdate = #{opdate},</if>
            <if test="stopdate != null">stopdate = #{stopdate},</if>
            <if test="kzksid != null">kzksid = #{kzksid},</if>
            <if test="kzks != null">kzks = #{kzks},</if>
            <if test="zxksid != null">zxksid = #{zxksid},</if>
            <if test="zxks != null">zxks = #{zxks},</if>
            <if test="kzys != null">kzys = #{kzys},</if>
            <if test="tzys != null">tzys = #{tzys},</if>
            <if test="yzid != null">yzid = #{yzid},</if>
            <if test="hisid != null">hisid = #{hisid},</if>
            <if test="kmname != null">kmname = #{kmname},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYzxxById" parameterType="Long">
        delete from yzxx where id = #{id}
    </delete>

    <delete id="deleteYzxxByIds" parameterType="String">
        delete from yzxx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMaxOpdateYzxx" resultType="yzxx">
        <include refid="selectYzxxVo"/>
        where brid = #{brid}
        <if test="zyid != null and zyid != ''">
            and zyid = #{zyid}
        </if>
        order by opdate desc
        limit 1
    </select>

    <insert id="insertBatchYzxx" parameterType="java.util.List">
        insert into yzxx
        (jzh, brid, zyid, yznr, yszt, yzqx, opdate, stopdate, kzksid, kzks, zxksid, zxks, kzys, tzys, yzid, hisid, kmname)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.jzh}, #{item.brid}, #{item.zyid}, #{item.yznr}, #{item.yszt}, #{item.yzqx}, #{item.opdate}, #{item.stopdate}, #{item.kzksid}, #{item.kzks}, #{item.zxksid}, #{item.zxks}, #{item.kzys}, #{item.tzys}, #{item.yzid}, #{item.hisid}, #{item.kmname})
        </foreach>
    </insert>
</mapper>
