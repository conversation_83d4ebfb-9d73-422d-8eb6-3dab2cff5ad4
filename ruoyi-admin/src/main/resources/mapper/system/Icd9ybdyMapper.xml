<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.Icd9ybdyMapper">

  <resultMap type="Icd9ybdy" id="Icd9ybdyResult">
    <result property="bm" column="bm"/>
    <result property="mc" column="mc"/>
    <result property="ybbm" column="ybbm"/>
    <result property="ybmc" column="ybmc"/>
    <result property="nccd" column="nccd"/>
    <result property="ssjb" column="ssjb"/>
    <result property="sstype" column="sstype"/>
    <result property="ismust" column="ismust"/>
    <result property="cmi" column="cmi"/>
    <result property="ccflag" column="ccflag"/>
    <result property="hm" column="hm"/>
  </resultMap>

  <resultMap type="CompareIcdHisVo" id="CompareIcdHisVoResult">
    <result property="bm" column="bm"/>
    <result property="mc" column="mc"/>
    <result property="ybbm" column="ybbm"/>
    <result property="ybmc" column="ybmc"/>
    <result property="ybbmHis" column="ybbm_his"/>
    <result property="ybmcHis" column="ybmc_his"/>
  </resultMap>

  <sql id="selectIcd9ybdyVo">
    select bm,
           mc,
           ybbm,
           ybmc,
           nccd,
           ssjb,
           sstype,
           ismust,
           cmi,
           ccflag,
           hm
    from icd9ybdy
  </sql>

  <select id="compareIcd9His" resultMap="CompareIcdHisVoResult"  parameterType="CompareIcdHisVo">
      SELECT a.bm, a.mc, a.ybbm as ybbm_his, a.ybmc as ybmc_his, b.ybbm, b.ybmc
      FROM icd9ybdy_his a
               JOIN icd9ybdy_wangzheng b ON a.bm = b.bm and a.mc = b.mc
      WHERE (a.ybbm != b.ybbm OR a.ybmc != b.ybmc)
    <if test="bm != null  and bm != ''">and a.bm like concat('%',#{bm},'%')</if>
    <if test="mc != null  and mc != ''">and a.mc like concat('%',#{mc},'%')</if>
    <if test="nccd != null  and nccd != ''">and nccd like concat('%',#{nccd},'%')</if>
  </select>

  <insert id="syncIcd9" parameterType="java.util.List">
    insert into icd9ybdy
    (bm,mc,ybbm,ybmc)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.bm},#{item.mc},#{item.ybbm},#{item.ybmc})
    </foreach>
  </insert>

  <insert id="syncIcd9His" parameterType="java.util.List">
    insert into icd9ybdy_his
    (bm,mc,ybbm,ybmc)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.bm},#{item.mc},#{item.ybbm},#{item.ybmc})
    </foreach>
  </insert>


  <select id="selectIcd9ybdyList" parameterType="Icd9ybdy" resultMap="Icd9ybdyResult">
    <include refid="selectIcd9ybdyVo"/>
    <where>
      <if test="bm != null  and bm != ''">and bm = #{bm}</if>
      <if test="mc != null  and mc != ''">and mc = #{mc}</if>
      <if test="ybbm != null  and ybbm != ''">and ybbm = #{ybbm}</if>
      <if test="ybmc != null  and ybmc != ''">and ybmc = #{ybmc}</if>
      <if test="nccd != null  and nccd != ''">and nccd = #{nccd}</if>
      <if test="ssjb != null  and ssjb != ''">and ssjb = #{ssjb}</if>
      <if test="sstype != null  and sstype != ''">and sstype = #{sstype}</if>
      <if test="ismust != null  and ismust != ''">and ismust = #{ismust}</if>
      <if test="cmi != null ">and cmi = #{cmi}</if>
      <if test="ccflag != null  and ccflag != ''">and ccflag = #{ccflag}</if>
      <if test="hm != null ">and hm = #{hm}</if>
    </where>
  </select>

  <select id="selectIcd9ybdyByBm" parameterType="String" resultMap="Icd9ybdyResult">
    <include refid="selectIcd9ybdyVo"/>
    where bm = #{bm}
  </select>

  <insert id="insertIcd9ybdy" parameterType="Icd9ybdy">
    insert into icd9ybdy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bm != null">bm,</if>
      <if test="mc != null">mc,</if>
      <if test="ybbm != null">ybbm,</if>
      <if test="ybmc != null">ybmc,</if>
      <if test="nccd != null">nccd,</if>
      <if test="ssjb != null">ssjb,</if>
      <if test="sstype != null">sstype,</if>
      <if test="ismust != null">ismust,</if>
      <if test="cmi != null">cmi,</if>
      <if test="ccflag != null">ccflag,</if>
      <if test="hm != null">hm,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bm != null">#{bm},</if>
      <if test="mc != null">#{mc},</if>
      <if test="ybbm != null">#{ybbm},</if>
      <if test="ybmc != null">#{ybmc},</if>
      <if test="nccd != null">#{nccd},</if>
      <if test="ssjb != null">#{ssjb},</if>
      <if test="sstype != null">#{sstype},</if>
      <if test="ismust != null">#{ismust},</if>
      <if test="cmi != null">#{cmi},</if>
      <if test="ccflag != null">#{ccflag},</if>
      <if test="hm != null">#{hm},</if>
    </trim>
  </insert>

  <update id="updateIcd9ybdy" parameterType="Icd9ybdy">
    update icd9ybdy
    <trim prefix="SET" suffixOverrides=",">
      <if test="mc != null">mc = #{mc},</if>
      <if test="ybbm != null">ybbm = #{ybbm},</if>
      <if test="ybmc != null">ybmc = #{ybmc},</if>
      <if test="nccd != null">nccd = #{nccd},</if>
      <if test="ssjb != null">ssjb = #{ssjb},</if>
      <if test="sstype != null">sstype = #{sstype},</if>
      <if test="ismust != null">ismust = #{ismust},</if>
      <if test="cmi != null">cmi = #{cmi},</if>
      <if test="ccflag != null">ccflag = #{ccflag},</if>
      <if test="hm != null">hm = #{hm},</if>
    </trim>
    where bm = #{bm}
  </update>

  <delete id="deleteIcd9ybdyByBm" parameterType="String">
    delete
    from icd9ybdy
    where bm = #{bm}
  </delete>

  <delete id="deleteIcd9ybdyByBms" parameterType="String">
    delete from icd9ybdy where bm in
    <foreach item="bm" collection="array" open="(" separator="," close=")">
      #{bm}
    </foreach>
  </delete>

  <select id="selectIcd9List" resultType="icdMaintainVo" parameterType="icdMaintainQueryVo">
    SELECT a.bm AS yybm, a.mc AS yymc, a.ybbm, a.ybmc, a.hm, a.cmi, a.ccflag AS ccFlag,
           CASE WHEN b.bm IS NULL THEN 1 ELSE 0 END AS STATUS
    FROM icd9ybdy a
     left JOIN icd_stop_use b ON a.ybbm = b.bm
    <where>
      <if test="yybm != null and yybm != ''">and (a.bm = #{yybm} or a.bm like concat(#{yybm}, '%'))</if>
      <if test="yymc != null and yymc != ''">and (a.mc like concat('%', #{yymc}, '%') or nccd like concat('%', #{yymc},
        '%'))
      </if>
      <if test="ybbm != null and ybbm != ''">and (ybbm = #{ybbm} or ybbm like concat(#{ybbm}, '%'))</if>
      <if test="ybmc != null and ybmc != ''">and (ybmc like concat('%', #{ybmc}, '%') or nccd like concat('%', #{yymc},
        '%'))
      </if>
    </where>
    order by a.bm
  </select>

  <delete id="clearIcd9">
    truncate table icd9ybdy
  </delete>


  <delete id="clearIcd9His">
    truncate table icd9ybdy_his
  </delete>
  <delete id="deleteAllIcd9">
    truncate table drgs.icd9ybdy
  </delete>

  <update id="updateIcd9Info" parameterType="icdUpdateVo">
    update icd9ybdy
    <trim prefix="SET" suffixOverrides=",">
      <if test="yymc != null">mc = #{yymc},</if>
      <if test="ybmc != null">ybmc = #{ybmc},</if>
      <if test="ybbm != null">ybbm = #{ybbm},</if>
      <if test="yybm != null">bm = #{yybm},</if>
      <if test="hm != null">hm = #{hm},</if>
    </trim>
    where bm = #{oldYybm} and ybbm = #{oldYbbm}
    and mc = #{oldYymc} and ybmc = #{oldYbmc}
  </update>

  <insert id="insertIcd9UpdateAndInsertByEntity">
    INSERT INTO icd9ybdy (bm,mc,ybbm,ybmc)
    VALUES
    <foreach collection="list" item="entity" separator=",">
      (#{entity.bm}, #{entity.mc},#{entity.ybbm}, #{entity.ybmc})
    </foreach>
    ON DUPLICATE KEY UPDATE mc = VALUES(mc),
                            ybbm = VALUES(ybbm),
                            ybmc = VALUES(ybmc)
  </insert>

  <select id="usp_update_icd10and9_ybdy" statementType="CALLABLE">
    {call usp_update_icd10and9_ybdy()}
  </select>






</mapper>
