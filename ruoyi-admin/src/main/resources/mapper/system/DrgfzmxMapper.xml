<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgfzmxMapper">

    <resultMap type="Drgfzmx" id="DrgfzmxResult">
        <result property="yljgbm"    column="yljgbm"    />
        <result property="yljgmc"    column="yljgmc"    />
        <result property="mdcbh"    column="mdcbh"    />
        <result property="mdcmc"    column="mdcmc"    />
        <result property="adrgbh"    column="adrgbh"    />
        <result property="adrgmc"    column="adrgmc"    />
        <result property="drgbh"    column="drgbh"    />
        <result property="drgmc"    column="drgmc"    />
        <result property="zb"    column="zb"    />
        <result property="bah"    column="bah"    />
        <result property="jsid"    column="jsid"    />
        <result property="xm"    column="xm"    />
    </resultMap>

    <sql id="selectDrgfzmxVo">
        select yljgbm, yljgmc, mdcbh, mdcmc, adrgbh, adrgmc, drgbh, drgmc, zb, bah, jsid, xm from drgfzmx
    </sql>

    <select id="selectDrgfzmxList" parameterType="Drgfzmx" resultMap="DrgfzmxResult">
        <include refid="selectDrgfzmxVo"/>
        <where>
            <if test="yljgbm != null  and yljgbm != ''"> and yljgbm = #{yljgbm}</if>
            <if test="yljgmc != null  and yljgmc != ''"> and yljgmc = #{yljgmc}</if>
            <if test="mdcbh != null  and mdcbh != ''"> and mdcbh = #{mdcbh}</if>
            <if test="mdcmc != null  and mdcmc != ''"> and mdcmc = #{mdcmc}</if>
            <if test="adrgbh != null  and adrgbh != ''"> and adrgbh = #{adrgbh}</if>
            <if test="adrgmc != null  and adrgmc != ''"> and adrgmc = #{adrgmc}</if>
            <if test="drgbh != null  and drgbh != ''"> and drgbh = #{drgbh}</if>
            <if test="drgmc != null  and drgmc != ''"> and drgmc = #{drgmc}</if>
            <if test="zb != null  and zb != ''"> and zb = #{zb}</if>
            <if test="bah != null  and bah != ''"> and bah = #{bah}</if>
            <if test="jsid != null  and jsid != ''"> and jsid = #{jsid}</if>
            <if test="xm != null  and xm != ''"> and xm = #{xm}</if>
        </where>
    </select>

    <insert id="insertDrgfzmx" parameterType="Drgfzmx">
        insert into drgfzmx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="yljgbm != null">yljgbm,</if>
            <if test="yljgmc != null">yljgmc,</if>
            <if test="mdcbh != null">mdcbh,</if>
            <if test="mdcmc != null">mdcmc,</if>
            <if test="adrgbh != null">adrgbh,</if>
            <if test="adrgmc != null">adrgmc,</if>
            <if test="drgbh != null">drgbh,</if>
            <if test="drgmc != null">drgmc,</if>
            <if test="zb != null">zb,</if>
            <if test="bah != null">bah,</if>
            <if test="jsid != null">jsid,</if>
            <if test="xm != null">xm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="yljgbm != null">#{yljgbm},</if>
            <if test="yljgmc != null">#{yljgmc},</if>
            <if test="mdcbh != null">#{mdcbh},</if>
            <if test="mdcmc != null">#{mdcmc},</if>
            <if test="adrgbh != null">#{adrgbh},</if>
            <if test="adrgmc != null">#{adrgmc},</if>
            <if test="drgbh != null">#{drgbh},</if>
            <if test="drgmc != null">#{drgmc},</if>
            <if test="zb != null">#{zb},</if>
            <if test="bah != null">#{bah},</if>
            <if test="jsid != null">#{jsid},</if>
            <if test="xm != null">#{xm},</if>
         </trim>
    </insert>

    <update id="updateDrgfzmx" parameterType="Drgfzmx">
        update drgfzmx
        <trim prefix="SET" suffixOverrides=",">
            <if test="yljgmc != null">yljgmc = #{yljgmc},</if>
            <if test="mdcbh != null">mdcbh = #{mdcbh},</if>
            <if test="mdcmc != null">mdcmc = #{mdcmc},</if>
            <if test="adrgbh != null">adrgbh = #{adrgbh},</if>
            <if test="adrgmc != null">adrgmc = #{adrgmc},</if>
            <if test="drgbh != null">drgbh = #{drgbh},</if>
            <if test="drgmc != null">drgmc = #{drgmc},</if>
            <if test="zb != null">zb = #{zb},</if>
            <if test="bah != null">bah = #{bah},</if>
            <if test="jsid != null">jsid = #{jsid},</if>
            <if test="xm != null">xm = #{xm},</if>
        </trim>
        where yljgbm = #{yljgbm}
    </update>
    
    <delete id="deleteDrgfzmx" parameterType="Drgfzmx">
        delete from drgfzmx;
    </delete>
    

</mapper>
