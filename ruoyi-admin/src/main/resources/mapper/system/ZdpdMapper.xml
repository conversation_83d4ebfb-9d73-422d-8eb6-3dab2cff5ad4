<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ZdpdMapper">
    
    <resultMap type="Zdpd" id="ZdpdResult">
        <result property="id"    column="id"    />
        <result property="pdmc"    column="pdmc"    />
        <result property="pdbh"    column="pdbh"    />
        <result property="pdlb"    column="pdlb"    />
        <result property="zzd"    column="zzd"    />
        <result property="qtzd"    column="qtzd"    />
        <result property="notqtzd"    column="notqtzd"    />
        <result property="rybq"    column="rybq"    />
        <result property="jymc"    column="jymc"    />
        <result property="jyjg"    column="jyjg"    />
        <result property="lyfs"    column="lyfs"    />
        <result property="notlyfs"    column="notlyfs"    />
        <result property="zzdbz"    column="zzdbz"    />
        <result property="rybqbz"    column="rybqbz"    />
        <result property="jyjgbz"    column="jyjgbz"    />
        <result property="qtzdbz"    column="qtzdbz"    />
        <result property="dtjbz"    column="dtjbz"    />
        <result property="isreplace"    column="isreplace"    />
        <result property="istwoway"    column="istwoway"    />
        <result property="istip"    column="istip"    />
        <result property="tip"    column="tip"    />
    </resultMap>

    <sql id="selectZdpdVo">
        select id, pdmc, pdbh, pdlb, zzd, qtzd, notqtzd, rybq, jymc, jyjg, lyfs, notlyfs, zzdbz, rybqbz, jyjgbz, qtzdbz, dtjbz, isreplace, istwoway, istip, tip from zdpd
    </sql>

    <select id="selectZdpdList" parameterType="Zdpd" resultMap="ZdpdResult">
        <include refid="selectZdpdVo"/>
        <where>  
            <if test="pdmc != null  and pdmc != ''"> and pdmc = #{pdmc}</if>
            <if test="pdbh != null  and pdbh != ''"> and pdbh = #{pdbh}</if>
            <if test="pdlb != null  and pdlb != ''"> and pdlb = #{pdlb}</if>
            <if test="zzd != null  and zzd != ''"> and zzd = #{zzd}</if>
            <if test="qtzd != null  and qtzd != ''"> and qtzd = #{qtzd}</if>
            <if test="notqtzd != null  and notqtzd != ''"> and notqtzd = #{notqtzd}</if>
            <if test="rybq != null  and rybq != ''"> and rybq = #{rybq}</if>
            <if test="jymc != null  and jymc != ''"> and jymc = #{jymc}</if>
            <if test="jyjg != null  and jyjg != ''"> and jyjg = #{jyjg}</if>
            <if test="lyfs != null  and lyfs != ''"> and lyfs = #{lyfs}</if>
            <if test="notlyfs != null  and notlyfs != ''"> and notlyfs = #{notlyfs}</if>
            <if test="zzdbz != null "> and zzdbz = #{zzdbz}</if>
            <if test="rybqbz != null "> and rybqbz = #{rybqbz}</if>
            <if test="jyjgbz != null "> and jyjgbz = #{jyjgbz}</if>
            <if test="qtzdbz != null "> and qtzdbz = #{qtzdbz}</if>
            <if test="dtjbz != null  and dtjbz != ''"> and dtjbz = #{dtjbz}</if>
            <if test="isreplace != null "> and isreplace = #{isreplace}</if>
            <if test="istwoway != null "> and istwoway = #{istwoway}</if>
            <if test="istip != null "> and istip = #{istip}</if>
            <if test="tip != null  and tip != ''"> and tip = #{tip}</if>
        </where>
    </select>
    
    <select id="selectZdpdById" parameterType="Long" resultMap="ZdpdResult">
        <include refid="selectZdpdVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertZdpd" parameterType="Zdpd" useGeneratedKeys="true" keyProperty="id">
        insert into zdpd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pdmc != null">pdmc,</if>
            <if test="pdbh != null">pdbh,</if>
            <if test="pdlb != null">pdlb,</if>
            <if test="zzd != null">zzd,</if>
            <if test="qtzd != null">qtzd,</if>
            <if test="notqtzd != null">notqtzd,</if>
            <if test="rybq != null">rybq,</if>
            <if test="jymc != null">jymc,</if>
            <if test="jyjg != null">jyjg,</if>
            <if test="lyfs != null">lyfs,</if>
            <if test="notlyfs != null">notlyfs,</if>
            <if test="zzdbz != null">zzdbz,</if>
            <if test="rybqbz != null">rybqbz,</if>
            <if test="jyjgbz != null">jyjgbz,</if>
            <if test="qtzdbz != null">qtzdbz,</if>
            <if test="dtjbz != null">dtjbz,</if>
            <if test="isreplace != null">isreplace,</if>
            <if test="istwoway != null">istwoway,</if>
            <if test="istip != null">istip,</if>
            <if test="tip != null">tip,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pdmc != null">#{pdmc},</if>
            <if test="pdbh != null">#{pdbh},</if>
            <if test="pdlb != null">#{pdlb},</if>
            <if test="zzd != null">#{zzd},</if>
            <if test="qtzd != null">#{qtzd},</if>
            <if test="notqtzd != null">#{notqtzd},</if>
            <if test="rybq != null">#{rybq},</if>
            <if test="jymc != null">#{jymc},</if>
            <if test="jyjg != null">#{jyjg},</if>
            <if test="lyfs != null">#{lyfs},</if>
            <if test="notlyfs != null">#{notlyfs},</if>
            <if test="zzdbz != null">#{zzdbz},</if>
            <if test="rybqbz != null">#{rybqbz},</if>
            <if test="jyjgbz != null">#{jyjgbz},</if>
            <if test="qtzdbz != null">#{qtzdbz},</if>
            <if test="dtjbz != null">#{dtjbz},</if>
            <if test="isreplace != null">#{isreplace},</if>
            <if test="istwoway != null">#{istwoway},</if>
            <if test="istip != null">#{istip},</if>
            <if test="tip != null">#{tip},</if>
         </trim>
    </insert>

    <update id="updateZdpd" parameterType="Zdpd">
        update zdpd
        <trim prefix="SET" suffixOverrides=",">
            <if test="pdmc != null">pdmc = #{pdmc},</if>
            <if test="pdbh != null">pdbh = #{pdbh},</if>
            <if test="pdlb != null">pdlb = #{pdlb},</if>
            <if test="zzd != null">zzd = #{zzd},</if>
            <if test="qtzd != null">qtzd = #{qtzd},</if>
            <if test="notqtzd != null">notqtzd = #{notqtzd},</if>
            <if test="rybq != null">rybq = #{rybq},</if>
            <if test="jymc != null">jymc = #{jymc},</if>
            <if test="jyjg != null">jyjg = #{jyjg},</if>
            <if test="lyfs != null">lyfs = #{lyfs},</if>
            <if test="notlyfs != null">notlyfs = #{notlyfs},</if>
            <if test="zzdbz != null">zzdbz = #{zzdbz},</if>
            <if test="rybqbz != null">rybqbz = #{rybqbz},</if>
            <if test="jyjgbz != null">jyjgbz = #{jyjgbz},</if>
            <if test="qtzdbz != null">qtzdbz = #{qtzdbz},</if>
            <if test="dtjbz != null">dtjbz = #{dtjbz},</if>
            <if test="isreplace != null">isreplace = #{isreplace},</if>
            <if test="istwoway != null">istwoway = #{istwoway},</if>
            <if test="istip != null">istip = #{istip},</if>
            <if test="tip != null">tip = #{tip},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZdpdById" parameterType="Long">
        delete from zdpd where id = #{id}
    </delete>

    <delete id="deleteZdpdByIds" parameterType="String">
        delete from zdpd where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>