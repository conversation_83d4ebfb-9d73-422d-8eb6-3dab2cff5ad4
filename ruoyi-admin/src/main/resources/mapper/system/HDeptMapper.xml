<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HDeptMapper">

  <resultMap type="HDept" id="HDeptResult">
    <result property="hDeptId" column="h_dept_id"/>
    <result property="hDeptName" column="h_dept_name"/>
    <result property="hisid" column="hisid"/>
    <result property="code" column="code"/>
    <result property="nccd" column="nccd"/>
    <result property="jgid" column="jgid"/>
    <result property="deptLeader" column="dept_leader"/>
    <result property="createTime" column="create_time"/>
    <result property="createBy" column="create_by"/>
    <result property="updateTime" column="update_time"/>
    <result property="updateBy" column="update_by"/>
  </resultMap>

  <sql id="selectHDeptVo">
    select h_dept_id,
           h_dept_name,
           hisid,
           code,
           nccd,
           jgid,
           dept_leader,
           create_time,
           create_by,
           update_time,
           update_by
    from h_dept
  </sql>

  <delete id="deleteHisKs">
    delete from his_ks;
  </delete>

  <insert id="insertHisKs" parameterType="SysDept">
    insert into his_ks(id,code,name,nccd,parentid,jgid)
    values(#{hisid},#{code},#{deptName},#{nccd},#{parentId},#{jgid})
  </insert>

  <insert id="insertKsDz">
    insert into ks_dz(id,code,name,tyname)
    select id,id,name,name FROM his_ks
    where id not in (select code from ks_dz)
  </insert>

  <update id="updateKsDz">
    update ks_dz join his_ks
    on ks_dz.code = his_ks.code
      set ks_dz.name = his_ks.name,ks_dz.parentid = his_ks.parentid,ks_dz.id = his_ks.id
  </update>

  <delete id="clearDept">
    delete from sys_dept where dept_id &lt;> 100
  </delete>

  <delete id="clearHDept">
    delete from h_dept
  </delete>

  <insert id="insertSysDeptByHisKs">
    insert into sys_dept (parent_id, ancestors, dept_name, order_num, leader,
                           phone, email, status, del_flag, create_by, create_time, update_by, update_time, hisid,
                           code,nccd,jgid)
    select 100,'0,100',name,1,'未知',
           '未知','未知','0','0','系统','2020-07-01','系统','2020-07-01',id,code,nccd,jgid
    from his_ks
  </insert>

  <insert id="insertHDeptBySysDept">
    insert into h_dept(h_dept_id,h_dept_name,hisid,code,nccd,jgid)
    select dept_id,dept_name,hisid,code,nccd,jgid
    from sys_dept where
      dept_id  NOT IN (select h_dept_id from h_dept);
  </insert>


  <select id="selectHDeptNameList" resultMap="HDeptResult">
    select distinct h_dept_id, h_dept_name from h_dept
  </select>

  <select id="selectHDeptByUserId" parameterType="Long" resultMap="HDeptResult">
    select b.* from h_user_dept a join h_dept b on a.h_dept_id = b.h_dept_id where a.h_user_id = #{userId}
  </select>

  <select id="selectHDeptList" parameterType="HDept" resultMap="HDeptResult">
    <include refid="selectHDeptVo"/>
    <where>
      <if test="hDeptName != null  and hDeptName != ''">and h_dept_name like concat('%', #{hDeptName}, '%')</if>
      <if test="hisid != null ">and hisid = #{hisid}</if>
      <if test="code != null  and code != ''">and code = #{code}</if>
      <if test="nccd != null  and nccd != ''">and nccd = #{nccd}</if>
      <if test="jgid != null ">and jgid = #{jgid}</if>
      <if test="deptLeader != null  and deptLeader != ''">and dept_leader = #{deptLeader}</if>
    </where>
  </select>

  <select id="selectHDeptByHDeptId" parameterType="Long" resultMap="HDeptResult">
    <include refid="selectHDeptVo"/>
    where h_dept_id = #{hDeptId}
  </select>

  <insert id="insertHDept" parameterType="HDept">
    insert into h_dept
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hDeptId != null">h_dept_id,</if>
      <if test="hDeptName != null and hDeptName != ''">h_dept_name,</if>
      <if test="hisid != null">hisid,</if>
      <if test="code != null">code,</if>
      <if test="nccd != null">nccd,</if>
      <if test="jgid != null">jgid,</if>
      <if test="deptLeader != null">dept_leader,</if>
      <if test="createTime != null">create_time,</if>
      <if test="createBy != null">create_by,</if>
      <if test="updateTime != null">update_time,</if>
      <if test="updateBy != null">update_by,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hDeptId != null">#{hDeptId},</if>
      <if test="hDeptName != null and hDeptName != ''">#{hDeptName},</if>
      <if test="hisid != null">#{hisid},</if>
      <if test="code != null">#{code},</if>
      <if test="nccd != null">#{nccd},</if>
      <if test="jgid != null">#{jgid},</if>
      <if test="deptLeader != null">#{deptLeader},</if>
      <if test="createTime != null">#{createTime},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="updateTime != null">#{updateTime},</if>
      <if test="updateBy != null">#{updateBy},</if>
    </trim>
  </insert>

  <update id="updateHDept" parameterType="HDept">
    update h_dept
    <trim prefix="SET" suffixOverrides=",">
      <if test="hDeptName != null and hDeptName != ''">h_dept_name = #{hDeptName},</if>
      <if test="hisid != null">hisid = #{hisid},</if>
      <if test="code != null">code = #{code},</if>
      <if test="nccd != null">nccd = #{nccd},</if>
      <if test="jgid != null">jgid = #{jgid},</if>
      <if test="deptLeader != null">dept_leader = #{deptLeader},</if>
      <if test="createTime != null">create_time = #{createTime},</if>
      <if test="createBy != null">create_by = #{createBy},</if>
      <if test="updateTime != null">update_time = #{updateTime},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
    </trim>
    where h_dept_id = #{hDeptId}
  </update>

  <delete id="deleteHDeptByHDeptId" parameterType="Long">
    delete
    from h_dept
    where h_dept_id = #{hDeptId}
  </delete>

  <delete id="deleteHDeptByHDeptIds" parameterType="String">
    delete from h_dept where h_dept_id in
    <foreach item="hDeptId" collection="array" open="(" separator="," close=")">
      #{hDeptId}
    </foreach>
  </delete>
</mapper>
