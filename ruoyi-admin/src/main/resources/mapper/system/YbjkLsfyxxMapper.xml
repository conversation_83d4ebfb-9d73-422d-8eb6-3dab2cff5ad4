<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbjkLsfyxxMapper">

    <resultMap type="YbjkLsfyxx" id="YbjkLsfyxxResult">
        <result property="id"    column="id"    />
        <result property="jzh"    column="jzh"    />
        <result property="bed"    column="bed"    />
        <result property="brname"    column="brname"    />
        <result property="kdks"    column="kdks"    />
        <result property="doctor"    column="doctor"    />
        <result property="kdksname"    column="kdksname"    />
        <result property="doctorname"    column="doctorname"    />
        <result property="fyType"    column="fy_type"    />
        <result property="fymId"    column="fym_id"    />
        <result property="fymName"    column="fym_name"    />
        <result property="sl"    column="sl"    />
        <result property="je"    column="je"    />
        <result property="bzmc"    column="bzmc"    />
        <result property="pl"    column="pl"    />
        <result property="jldw"    column="jldw"    />
        <result property="zkyl"    column="zkyl"    />
        <result property="kzdate"    column="kzdate"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
    </resultMap>

    <sql id="selectYbjkLsfyxxVo">
        select id, jzh, bed, brname, kdks, doctor, kdksname, doctorname, fy_type, fym_id, fym_name, sl, je, bzmc, pl, jldw, zkyl, kzdate, create_by, create_date from ybjk_lsfyxx
    </sql>

    <select id="selectYbjkLsfyxxList" parameterType="YbjkLsfyxx" resultMap="YbjkLsfyxxResult">
        <include refid="selectYbjkLsfyxxVo"/>
        <where>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="bed != null  and bed != ''"> and bed = #{bed}</if>
            <if test="brname != null  and brname != ''"> and brname like concat('%', #{brname}, '%')</if>
            <if test="kdks != null  and kdks != ''"> and kdks = #{kdks}</if>
            <if test="doctor != null  and doctor != ''"> and doctor = #{doctor}</if>
            <if test="kdksname != null  and kdksname != ''"> and kdksname like concat('%', #{kdksname}, '%')</if>
            <if test="doctorname != null  and doctorname != ''"> and doctorname like concat('%', #{doctorname}, '%')</if>
            <if test="fyType != null  and fyType != ''"> and fy_type = #{fyType}</if>
            <if test="fymId != null  and fymId != ''"> and fym_id = #{fymId}</if>
            <if test="fymName != null  and fymName != ''"> and fym_name like concat('%', #{fymName}, '%')</if>
            <if test="sl != null "> and sl = #{sl}</if>
            <if test="je != null "> and je = #{je}</if>
            <if test="bzmc != null  and bzmc != ''"> and bzmc = #{bzmc}</if>
            <if test="pl != null  and pl != ''"> and pl = #{pl}</if>
            <if test="jldw != null  and jldw != ''"> and jldw = #{jldw}</if>
            <if test="zkyl != null  and zkyl != ''"> and zkyl = #{zkyl}</if>
            <if test="kzdate != null "> and kzdate = #{kzdate}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
        </where>
    </select>

    <select id="selectYbjkLsfyxxById" parameterType="Long" resultMap="YbjkLsfyxxResult">
        <include refid="selectYbjkLsfyxxVo"/>
        where id = #{id}
    </select>

    <insert id="insertYbjkLsfyxx" parameterType="YbjkLsfyxx" useGeneratedKeys="true" keyProperty="id">
        insert into ybjk_lsfyxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jzh != null">jzh,</if>
            <if test="bed != null">bed,</if>
            <if test="brname != null">brname,</if>
            <if test="kdks != null">kdks,</if>
            <if test="doctor != null">doctor,</if>
            <if test="kdksname != null">kdksname,</if>
            <if test="doctorname != null">doctorname,</if>
            <if test="fyType != null">fy_type,</if>
            <if test="fymId != null">fym_id,</if>
            <if test="fymName != null">fym_name,</if>
            <if test="sl != null">sl,</if>
            <if test="je != null">je,</if>
            <if test="bzmc != null">bzmc,</if>
            <if test="pl != null">pl,</if>
            <if test="jldw != null">jldw,</if>
            <if test="zkyl != null">zkyl,</if>
            <if test="kzdate != null">kzdate,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jzh != null">#{jzh},</if>
            <if test="bed != null">#{bed},</if>
            <if test="brname != null">#{brname},</if>
            <if test="kdks != null">#{kdks},</if>
            <if test="doctor != null">#{doctor},</if>
            <if test="kdksname != null">#{kdksname},</if>
            <if test="doctorname != null">#{doctorname},</if>
            <if test="fyType != null">#{fyType},</if>
            <if test="fymId != null">#{fymId},</if>
            <if test="fymName != null">#{fymName},</if>
            <if test="sl != null">#{sl},</if>
            <if test="je != null">#{je},</if>
            <if test="bzmc != null">#{bzmc},</if>
            <if test="pl != null">#{pl},</if>
            <if test="jldw != null">#{jldw},</if>
            <if test="zkyl != null">#{zkyl},</if>
            <if test="kzdate != null">#{kzdate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
         </trim>
    </insert>
    
    <insert id="plinsertYbjkLsfyxx" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into ybjk_lsfyxx
    <trim prefix="(" suffix=")" suffixOverrides=",">
        jzh, bed, brname, kdks, doctor, kdksname, doctorname, 
        fy_type, fym_id, fym_name, sl, je, bzmc, pl, jldw, zkyl, 
        kzdate, create_by, create_date
    </trim>
    values
    <foreach collection="list" item="item" separator=",">
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{item.jzh}, #{item.bed}, #{item.brname}, #{item.kdks}, 
            #{item.doctor}, #{item.kdksname}, #{item.doctorname}, 
            #{item.fyType}, #{item.fymId}, #{item.fymName}, 
            #{item.sl}, #{item.je}, #{item.bzmc}, #{item.pl}, 
            #{item.jldw}, #{item.zkyl}, #{item.kzdate}, 
            #{item.createBy}, #{item.createDate}
        </trim>
    </foreach>
</insert>
    

    <update id="updateYbjkLsfyxx" parameterType="YbjkLsfyxx">
        update ybjk_lsfyxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="bed != null">bed = #{bed},</if>
            <if test="brname != null">brname = #{brname},</if>
            <if test="kdks != null">kdks = #{kdks},</if>
            <if test="doctor != null">doctor = #{doctor},</if>
            <if test="kdksname != null">kdksname = #{kdksname},</if>
            <if test="doctorname != null">doctorname = #{doctorname},</if>
            <if test="fyType != null">fy_type = #{fyType},</if>
            <if test="fymId != null">fym_id = #{fymId},</if>
            <if test="fymName != null">fym_name = #{fymName},</if>
            <if test="sl != null">sl = #{sl},</if>
            <if test="je != null">je = #{je},</if>
            <if test="bzmc != null">bzmc = #{bzmc},</if>
            <if test="pl != null">pl = #{pl},</if>
            <if test="jldw != null">jldw = #{jldw},</if>
            <if test="zkyl != null">zkyl = #{zkyl},</if>
            <if test="kzdate != null">kzdate = #{kzdate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbjkLsfyxxById" parameterType="Long">
        delete from ybjk_lsfyxx where id = #{id}
    </delete>

    <delete id="deleteYbjkLsfyxxByIds" parameterType="String">
        delete from ybjk_lsfyxx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="clearLsfyxx">
        truncate ybjk_lsfyxx
    </select>
</mapper>
