<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgXmffMxMapper">

    <resultMap type="DrgXmffMx" id="DrgXmffMxResult">
        <result property="brbs"    column="brbs"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="zfy"    column="zfy"    />
        <result property="zfbz"    column="zfbz"    />
        <result property="hisJsdate"    column="his_jsdate"    />
        <result property="cydate"    column="cydate"    />
        <result property="xmffFlag"    column="xmff_flag"    />
    </resultMap>

    <sql id="selectDrgXmffMxVo">
        select brbs, brid, zyid, zfy, zfbz, his_jsdate, cydate, xmff_flag from drg_xmff_mx
    </sql>

    <select id="selectDrgXmffMxList" parameterType="DrgXmffMx" resultMap="DrgXmffMxResult">
        <include refid="selectDrgXmffMxVo"/>
        <where>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="zfy != null "> and zfy = #{zfy}</if>
            <if test="zfbz != null "> and zfbz = #{zfbz}</if>
            <if test="hisJsdate != null "> and his_jsdate = #{hisJsdate}</if>
            <if test="cydate != null "> and cydate = #{cydate}</if>
            <if test="xmffFlag != null "> and xmff_flag = #{xmffFlag}</if>
        </where>
    </select>

    <select id="selectDrgXmffMxByBrbs" parameterType="String" resultMap="DrgXmffMxResult">
        <include refid="selectDrgXmffMxVo"/>
        where brbs = #{brbs}
    </select>

    <select id="countThisMonthSettleAmount" resultType="java.lang.Long">
        SELECT COUNT(mdtrt_id) FROM jsxx_his jh
        WHERE med_type='21'
        AND jh.his_jsdate &gt;= #{first} and jh.his_jsdate &lt;= #{last}
    </select>

    <select id="countThisMonthSettleExHigh" resultType="java.lang.Long">
        select count(brid) from drg_xmff_mx dxm
        where dxm.his_jsdate &gt;= #{first} and dxm.his_jsdate &lt;= #{last} and xmff_flag = 1
    </select>

    <select id="getSettleInfoDetail" resultType="com.ruoyi.system.domain.vo.SettleInfoDetail">
        SELECT dxm.brbs as brbs, dxm.brid as brid, dxm.zyid as zyid, dxm.zfy as zfy, dxm.his_jsdate as hisJsdate,
               dxm.cydate as cydate, bs.zyzd as zyzd, bs.jbdm as jbdm, bs.drgbh as drgbh
        FROM drg_xmff_mx dxm LEFT JOIN ba_syjl bs on dxm.brbs = bs.brbs
        <where>
            <if test="brbs != null">and dxm.brbs = #{brbs}</if>
            <if test="cykb != null">and bs.cykb = #{cykb}</if>
            <if test="zyys != null">and bs.zyys = #{zyys}</if>
            and bs.cydate like concat(date_format(now(), '%Y-%m'), '%')
        </where>
        order by bs.cydate desc
    </select>
    <select id="getCostCompute" parameterType="String" resultType="java.lang.String">
        CALL usp_get_ex_fee(#{drgbh},'admin')
    </select>

    <insert id="insertDrgXmffMx" parameterType="DrgXmffMx">
        insert into drg_xmff_mx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="zfy != null">zfy,</if>
            <if test="zfbz != null">zfbz,</if>
            <if test="hisJsdate != null">his_jsdate,</if>
            <if test="cydate != null">cydate,</if>
            <if test="xmffFlag != null">xmff_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="zfy != null">#{zfy},</if>
            <if test="zfbz != null">#{zfbz},</if>
            <if test="hisJsdate != null">#{hisJsdate},</if>
            <if test="cydate != null">#{cydate},</if>
            <if test="xmffFlag != null">#{xmffFlag},</if>
         </trim>
    </insert>

    <update id="updateDrgXmffMx" parameterType="DrgXmffMx">
        update drg_xmff_mx
        <trim prefix="SET" suffixOverrides=",">
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="zfy != null">zfy = #{zfy},</if>
            <if test="zfbz != null">zfbz = #{zfbz},</if>
            <if test="hisJsdate != null">his_jsdate = #{hisJsdate},</if>
            <if test="cydate != null">cydate = #{cydate},</if>
            <if test="xmffFlag != null">xmff_flag = #{xmffFlag},</if>
        </trim>
        where brbs = #{brbs}
    </update>

    <delete id="deleteDrgXmffMxByBrbs" parameterType="String">
        delete from drg_xmff_mx where brbs = #{brbs}
    </delete>

    <delete id="deleteDrgXmffMxByBrbss" parameterType="String">
        delete from drg_xmff_mx where brbs in
        <foreach item="brbs" collection="array" open="(" separator="," close=")">
            #{brbs}
        </foreach>
    </delete>
</mapper>
