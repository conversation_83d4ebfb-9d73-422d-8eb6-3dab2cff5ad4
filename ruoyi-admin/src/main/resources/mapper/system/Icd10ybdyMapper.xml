<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.Icd10ybdyMapper">

  <resultMap type="Icd10ybdy" id="Icd10ybdyResult">
    <result property="bzbm" column="bzbm"/>
    <result property="bzmc" column="bzmc"/>
    <result property="ybbzbm" column="ybbzbm"/>
    <result property="ybbzmc" column="ybbzmc"/>
    <result property="bzmcNccd" column="bzmc_nccd"/>
    <result property="ybbzmcNccd" column="ybbzmc_nccd"/>
    <result property="cmi" column="cmi"/>
    <result property="ccflag" column="ccflag"/>
    <result property="hm" column="hm"/>
    <result property="status" column="status"/>
  </resultMap>

  <resultMap type="CompareIcdHisVo" id="CompareIcdHisVoResult">
    <result property="bm" column="bm"/>
    <result property="mc" column="mc"/>
    <result property="ybbm" column="ybbm"/>
    <result property="ybmc" column="ybmc"/>
    <result property="ybbmHis" column="ybbm_his"/>
    <result property="ybmcHis" column="ybmc_his"/>
  </resultMap>

  <sql id="selectIcd10ybdyVo">
    select bzbm,
           bzmc,
           ybbzbm,
           ybbzmc,
           bzmc_nccd,
           ybbzmc_nccd,
           cmi,
           ccflag,
           hm
    from icd10ybdy
  </sql>

  <select id="compareIcd10His" resultMap="CompareIcdHisVoResult" parameterType="CompareIcdHisVo">
      SELECT a.bzbm as bm, a.bzmc as mc, a.ybbzbm as ybbm_his, a.ybbzmc as ybmc_his, b.ybbzbm as ybbm, b.ybbzmc as ybmc
      FROM icd10ybdy_his a
               JOIN icd10ybdy_wangzheng b ON a.bzbm = b.bzbm and a.bzmc = b.bzmc
      WHERE (a.ybbzbm != b.ybbzbm OR a.ybbzmc != b.ybbzmc)
    <if test="bm != null  and bm != ''">and a.bzbm like concat('%',#{bm},'%')</if>
    <if test="mc != null  and mc != ''">and a.bzmc like concat('%',#{mc},'%')</if>
    <if test="nccd != null  and nccd != ''">and bzmc_nccd like concat('%',#{nccd},'%')</if>
  </select>

  <insert id="syncIcd10" parameterType="java.util.List">
    insert into icd10ybdy
    (bzbm,bzmc,ybbzbm,ybbzmc)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.bzbm},#{item.bzmc},#{item.ybbzbm},#{item.ybbzmc})
    </foreach>
  </insert>

  <insert id="syncIcd10His" parameterType="java.util.List">
    insert into icd10ybdy_his
    (bzbm,bzmc,ybbzbm,ybbzmc)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.bzbm},#{item.bzmc},#{item.ybbzbm},#{item.ybbzmc})
    </foreach>
  </insert>

  <select id="selectIcd10ybdyList" parameterType="Icd10ybdy" resultMap="Icd10ybdyResult">
    <include refid="selectIcd10ybdyVo"/>
    <where>
      <if test="bzbm != null  and bzbm != ''">and bzbm = #{bzbm}</if>
      <if test="bzmc != null  and bzmc != ''">and bzmc = #{bzmc}</if>
      <if test="ybbzbm != null  and ybbzbm != ''">and ybbzbm = #{ybbzbm}</if>
      <if test="ybbzmc != null  and ybbzmc != ''">and ybbzmc = #{ybbzmc}</if>
      <if test="bzmcNccd != null  and bzmcNccd != ''">and bzmc_nccd = #{bzmcNccd}</if>
      <if test="ybbzmcNccd != null  and ybbzmcNccd != ''">and ybbzmc_nccd = #{ybbzmcNccd}</if>
      <if test="cmi != null ">and cmi = #{cmi}</if>
      <if test="ccflag != null  and ccflag != ''">and ccflag = #{ccflag}</if>
      <if test="hm != null ">and hm = #{hm}</if>
    </where>
  </select>

  <select id="selectIcd10ybdyByBzbm" parameterType="String" resultMap="Icd10ybdyResult">
    <include refid="selectIcd10ybdyVo"/>
    where bzbm = #{bzbm}
  </select>

  <insert id="insertIcd10ybdy" parameterType="Icd10ybdy">
    insert into icd10ybdy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bzbm != null">bzbm,</if>
      <if test="bzmc != null">bzmc,</if>
      <if test="ybbzbm != null">ybbzbm,</if>
      <if test="ybbzmc != null">ybbzmc,</if>
      <if test="bzmcNccd != null">bzmc_nccd,</if>
      <if test="ybbzmcNccd != null">ybbzmc_nccd,</if>
      <if test="cmi != null">cmi,</if>
      <if test="ccflag != null">ccflag,</if>
      <if test="hm != null">hm,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bzbm != null">#{bzbm},</if>
      <if test="bzmc != null">#{bzmc},</if>
      <if test="ybbzbm != null">#{ybbzbm},</if>
      <if test="ybbzmc != null">#{ybbzmc},</if>
      <if test="bzmcNccd != null">#{bzmcNccd},</if>
      <if test="ybbzmcNccd != null">#{ybbzmcNccd},</if>
      <if test="cmi != null">#{cmi},</if>
      <if test="ccflag != null">#{ccflag},</if>
      <if test="hm != null">#{hm},</if>
    </trim>
  </insert>

  <update id="updateIcd10ybdy" parameterType="Icd10ybdy">
    update icd10ybdy
    <trim prefix="SET" suffixOverrides=",">
      <if test="bzmc != null">bzmc = #{bzmc},</if>
      <if test="ybbzbm != null">ybbzbm = #{ybbzbm},</if>
      <if test="ybbzmc != null">ybbzmc = #{ybbzmc},</if>
      <if test="bzmcNccd != null">bzmc_nccd = #{bzmcNccd},</if>
      <if test="ybbzmcNccd != null">ybbzmc_nccd = #{ybbzmcNccd},</if>
      <if test="cmi != null">cmi = #{cmi},</if>
      <if test="ccflag != null">ccflag = #{ccflag},</if>
      <if test="hm != null">hm = #{hm},</if>
    </trim>
    where bzbm = #{bzbm}
  </update>

  <select id="selectIcdLisByConditions" parameterType="icdMaintainQueryVo" resultType="icdMaintainVo">
    SELECT bzbm AS yybm, bzmc AS yymc, ybbzbm AS ybbm, ybbzmc AS ybmc, bzmc_nccd AS yyNccd,
    ybbzmc_nccd AS ybNccd, cmi, hm ,CASE WHEN b.bm IS NULL THEN 1 ELSE 0 END AS STATUS
    FROM icd10ybdy a
    left JOIN icd_stop_use b ON a.ybbzbm = b.bm
    <where>
      <if test="yybm != null and yybm != ''">and (a.bzbm = #{yybm} or a.bzbm like concat(#{yybm}, '%'))</if>
      <if test="yymc != null and yymc != ''">and (a.bzmc like concat('%', #{yymc}, '%') or a.bzmc_nccd like concat('%',
        #{yymc}, '%'))
      </if>
      <if test="ybbm != null and ybbm != ''">and (a.ybbzbm = #{ybbm} or a.ybbzbm like concat(#{ybbm}, '%'))</if>
      <if test="ybmc != null and ybmc != ''">and (a.ybbzmc like concat('%', #{ybmc}, '%') or a.ybbzmc_nccd like concat('%',
        #{ybmc}, '%'))
      </if>
    </where>
    order by a.bzbm
  </select>

  <update id="uspUpdateBzAndSsmlDz">
    call usp_update_bz_and_ssml_dz()
  </update>

  <update id="updateIcdInfo" parameterType="icdUpdateVo">
    update icd10ybdy
    <trim prefix="SET" suffixOverrides=",">
      <if test="yybm != null and yybm != ''">bzbm = #{yybm},</if>
      <if test="ybbm != null and ybbm != ''">ybbzbm = #{ybbm},</if>
      <if test="yymc != null and yymc != ''">bzmc = #{yymc},</if>
      <if test="ybmc != null and ybmc != ''">ybbzmc = #{ybmc},</if>
      <if test="hm != null">hm = #{hm},</if>
    </trim>
    where bzbm = #{oldYybm} and ybbzbm = #{oldYbbm}
    and bzmc = #{oldYymc} and ybbzmc = #{oldYbmc}
  </update>

  <delete id="deleteIcd10ybdyByBzbm" parameterType="String">
    delete
    from icd10ybdy
    where bzbm = #{bzbm}
  </delete>

  <delete id="deleteIcd10ybdyByBzbms" parameterType="String">
    delete from icd10ybdy where bzbm in
    <foreach item="bzbm" collection="array" open="(" separator="," close=")">
      #{bzbm}
    </foreach>
  </delete>

  <insert id="icdAdd" parameterType="icdUpdateVo">
    insert into icd10ybdy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="yybm != null and yybm != ''">bzbm,</if>
      <if test="yymc != null and yymc != ''">bzmc,</if>
      <if test="ybbm != null and ybbm != ''">ybbzbm,</if>
      <if test="ybmc != null and ybmc != ''">ybbzmc,</if>
      <if test="hm != null">hm,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="yybm != null and yybm != ''">#{bzbm,}</if>
      <if test="yymc != null and yymc != ''">#{bzmc,}</if>
      <if test="ybbm != null and ybbm != ''">#{ybbzbm,}</if>
      <if test="ybmc != null and ybmc != ''">#{ybbzmc,}</if>
      <if test="hm != null">#{hm,}</if>
    </trim>
  </insert>

  <select id="getNccd" parameterType="string" resultType="string">
    select pinyin(#{value})
  </select>

  <select id="getCmi" parameterType="string" resultType="decimal">
    select min(zfqz)
    from drgdict a
           join drg_adrg_fz b on a.adrgbh = b.adrgbh
    where b.icdbh = #{icdbh}
  </select>

  <select id="getCCFlag" parameterType="string" resultType="string">
    select case when type = '1' then ' MCC' when type = '2' then ' CC' else '' end
    from drg_bfz
    where bzbm = #{ybbm}
  </select>

  <delete id="clearIcd10">
    truncate table icd10ybdy
  </delete>

  <delete id="clearIcd10His">
    truncate table icd10ybdy_his
  </delete>

</mapper>
