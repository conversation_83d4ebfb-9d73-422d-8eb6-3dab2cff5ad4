<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HUserDeptMapper">

    <resultMap type="HUserDept" id="HUserDeptResult">
        <result property="hDeptId"    column="h_dept_id"    />
        <result property="hUserId"    column="h_user_id"    />
        <result property="jgid"    column="jgid"    />
    </resultMap>

    <sql id="selectHUserDeptVo">
        select h_dept_id, h_user_id, jgid from h_user_dept
    </sql>

    <select id="selectHUserDeptList" parameterType="HUserDept" resultMap="HUserDeptResult">
        <include refid="selectHUserDeptVo"/>
        <where>
            <if test="hDeptId != null "> and h_dept_id = #{hDeptId}</if>
            <if test="hUserId != null "> and h_user_id = #{hUserId}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
        </where>
    </select>

    <select id="selectHUserDeptByHDeptId" parameterType="Long" resultMap="HUserDeptResult">
        <include refid="selectHUserDeptVo"/>
        where h_dept_id = #{hDeptId}
    </select>
  <select id="selectHUserDeptDict" resultType="java.lang.String">
    select h_dept_name from sys_user su join h_user_dept hud on su.user_id = hud.h_user_id
                                        join h_dept hd on hd.h_dept_id = hud.h_dept_id
    where user_name = #{sysUserCode}
  </select>
  <select id="selectHConsolidation" resultType="java.lang.String" parameterType="java.util.List">
    select source_dept_name from dept_consolidation
    <where>
      and target_dept_name in
      <foreach collection="list" index="index" separator="," item="item" open="(" close=")">
        #{item}
      </foreach>
      and status = 1
    </where>
    union all
    select target_dept_name from dept_consolidation
    <where>
      and source_dept_name in
      <foreach collection="list" index="index" separator="," item="item" open="(" close=")">
        #{item}
      </foreach>
      and status = 1
    </where>
  </select>

  <insert id="insertHUserDept" parameterType="HUserDept">
        insert into h_user_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hDeptId != null">h_dept_id,</if>
            <if test="hUserId != null">h_user_id,</if>
            <if test="jgid != null">jgid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hDeptId != null">#{hDeptId},</if>
            <if test="hUserId != null">#{hUserId},</if>
            <if test="jgid != null">#{jgid},</if>
         </trim>
    </insert>

    <update id="updateHUserDept" parameterType="HUserDept">
        update h_user_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="hUserId != null">h_user_id = #{hUserId},</if>
            <if test="jgid != null">jgid = #{jgid},</if>
        </trim>
        where h_dept_id = #{hDeptId}
    </update>

    <delete id="deleteHUserDeptByHDeptId" parameterType="Long">
        delete from h_user_dept where h_dept_id = #{hDeptId}
    </delete>

    <delete id="deleteHUserDeptByHDeptIds" parameterType="String">
        delete from h_user_dept where h_dept_id in
        <foreach item="hDeptId" collection="array" open="(" separator="," close=")">
            #{hDeptId}
        </foreach>
    </delete>
    <delete id="deleteHUserDeptByUserId" parameterType="Long">
      delete from h_user_dept where h_user_dept.h_user_id = #{userId}
    </delete>
</mapper>
