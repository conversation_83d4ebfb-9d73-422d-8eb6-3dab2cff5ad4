<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MztbFyxxMapper">
    
    <resultMap type="MztbFyxx" id="MztbFyxxResult">
        <result property="id"    column="id"    />
        <result property="feedetlSn"    column="feedetl_sn"    />
        <result property="rxDrordNo"    column="rx_drord_no"    />
        <result property="fixmedinsCode"    column="fixmedins_code"    />
        <result property="fixmedinsName"    column="fixmedins_name"    />
        <result property="psnNo"    column="psn_no"    />
        <result property="medType"    column="med_type"    />
        <result property="feeOcurTime"    column="fee_ocur_time"    />
        <result property="cnt"    column="cnt"    />
        <result property="pric"    column="pric"    />
        <result property="chrgitmLv"    column="chrgitm_lv"    />
        <result property="hilistCode"    column="hilist_code"    />
        <result property="hilistName"    column="hilist_name"    />
        <result property="listType"    column="list_type"    />
        <result property="medListCodg"    column="med_list_codg"    />
        <result property="medinsListCodg"    column="medins_list_codg"    />
        <result property="medinsListName"    column="medins_list_name"    />
        <result property="medChrgitmType"    column="med_chrgitm_type"    />
        <result property="prodname"    column="prodname"    />
        <result property="spec"    column="spec"    />
        <result property="dosformName"    column="dosform_name"    />
        <result property="lmtUsedFlag"    column="lmt_used_flag"    />
        <result property="hospPrepFlag"    column="hosp_prep_flag"    />
        <result property="hospApprFlag"    column="hosp_appr_flag"    />
        <result property="tcmdrugUsedWay"    column="tcmdrug_used_way"    />
        <result property="prodplacType"    column="prodplac_type"    />
        <result property="basMednFlag"    column="bas_medn_flag"    />
        <result property="hiNegoDrugFlag"    column="hi_nego_drug_flag"    />
        <result property="chldMedcFlag"    column="chld_medc_flag"    />
        <result property="etipFlag"    column="etip_flag"    />
        <result property="etipHospCode"    column="etip_hosp_code"    />
        <result property="dscgTkdrugFlag"    column="dscg_tkdrug_flag"    />
        <result property="listSpItemFlag"    column="list_sp_item_flag"    />
        <result property="matnFeeFlag"    column="matn_fee_flag"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="jzid"    column="jzid"    />
    </resultMap>

    <sql id="selectMztbFyxxVo">
        select id, feedetl_sn, rx_drord_no, fixmedins_code, fixmedins_name, psn_no, med_type, fee_ocur_time, cnt, pric, chrgitm_lv, hilist_code, hilist_name, list_type, med_list_codg, medins_list_codg, medins_list_name, med_chrgitm_type, prodname, spec, dosform_name, lmt_used_flag, hosp_prep_flag, hosp_appr_flag, tcmdrug_used_way, prodplac_type, bas_medn_flag, hi_nego_drug_flag, chld_medc_flag, etip_flag, etip_hosp_code, dscg_tkdrug_flag, list_sp_item_flag, matn_fee_flag, brid, zyid, jzid from mztb_fyxx
    </sql>

    <select id="selectMztbFyxxList" parameterType="MztbFyxx" resultMap="MztbFyxxResult">
        <include refid="selectMztbFyxxVo"/>
        <where>  
            <if test="feedetlSn != null  and feedetlSn != ''"> and feedetl_sn = #{feedetlSn}</if>
            <if test="rxDrordNo != null  and rxDrordNo != ''"> and rx_drord_no = #{rxDrordNo}</if>
            <if test="fixmedinsCode != null  and fixmedinsCode != ''"> and fixmedins_code = #{fixmedinsCode}</if>
            <if test="fixmedinsName != null  and fixmedinsName != ''"> and fixmedins_name like concat('%', #{fixmedinsName}, '%')</if>
            <if test="psnNo != null  and psnNo != ''"> and psn_no = #{psnNo}</if>
            <if test="medType != null  and medType != ''"> and med_type = #{medType}</if>
            <if test="feeOcurTime != null "> and fee_ocur_time = #{feeOcurTime}</if>
            <if test="cnt != null "> and cnt = #{cnt}</if>
            <if test="pric != null "> and pric = #{pric}</if>
            <if test="chrgitmLv != null  and chrgitmLv != ''"> and chrgitm_lv = #{chrgitmLv}</if>
            <if test="hilistCode != null  and hilistCode != ''"> and hilist_code = #{hilistCode}</if>
            <if test="hilistName != null  and hilistName != ''"> and hilist_name like concat('%', #{hilistName}, '%')</if>
            <if test="listType != null  and listType != ''"> and list_type = #{listType}</if>
            <if test="medListCodg != null  and medListCodg != ''"> and med_list_codg = #{medListCodg}</if>
            <if test="medinsListCodg != null  and medinsListCodg != ''"> and medins_list_codg = #{medinsListCodg}</if>
            <if test="medinsListName != null  and medinsListName != ''"> and medins_list_name like concat('%', #{medinsListName}, '%')</if>
            <if test="medChrgitmType != null  and medChrgitmType != ''"> and med_chrgitm_type = #{medChrgitmType}</if>
            <if test="prodname != null  and prodname != ''"> and prodname like concat('%', #{prodname}, '%')</if>
            <if test="spec != null  and spec != ''"> and spec = #{spec}</if>
            <if test="dosformName != null  and dosformName != ''"> and dosform_name like concat('%', #{dosformName}, '%')</if>
            <if test="lmtUsedFlag != null  and lmtUsedFlag != ''"> and lmt_used_flag = #{lmtUsedFlag}</if>
            <if test="hospPrepFlag != null  and hospPrepFlag != ''"> and hosp_prep_flag = #{hospPrepFlag}</if>
            <if test="hospApprFlag != null  and hospApprFlag != ''"> and hosp_appr_flag = #{hospApprFlag}</if>
            <if test="tcmdrugUsedWay != null  and tcmdrugUsedWay != ''"> and tcmdrug_used_way = #{tcmdrugUsedWay}</if>
            <if test="prodplacType != null  and prodplacType != ''"> and prodplac_type = #{prodplacType}</if>
            <if test="basMednFlag != null  and basMednFlag != ''"> and bas_medn_flag = #{basMednFlag}</if>
            <if test="hiNegoDrugFlag != null  and hiNegoDrugFlag != ''"> and hi_nego_drug_flag = #{hiNegoDrugFlag}</if>
            <if test="chldMedcFlag != null  and chldMedcFlag != ''"> and chld_medc_flag = #{chldMedcFlag}</if>
            <if test="etipFlag != null  and etipFlag != ''"> and etip_flag = #{etipFlag}</if>
            <if test="etipHospCode != null  and etipHospCode != ''"> and etip_hosp_code = #{etipHospCode}</if>
            <if test="dscgTkdrugFlag != null  and dscgTkdrugFlag != ''"> and dscg_tkdrug_flag = #{dscgTkdrugFlag}</if>
            <if test="listSpItemFlag != null  and listSpItemFlag != ''"> and list_sp_item_flag = #{listSpItemFlag}</if>
            <if test="matnFeeFlag != null  and matnFeeFlag != ''"> and matn_fee_flag = #{matnFeeFlag}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="jzid != null  and jzid != ''"> and jzid = #{jzid}</if>
        </where>
    </select>
    
    <select id="selectMztbFyxxById" parameterType="Long" resultMap="MztbFyxxResult">
        <include refid="selectMztbFyxxVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMztbFyxx" parameterType="MztbFyxx" useGeneratedKeys="true" keyProperty="id">
        insert into mztb_fyxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="feedetlSn != null">feedetl_sn,</if>
            <if test="rxDrordNo != null">rx_drord_no,</if>
            <if test="fixmedinsCode != null">fixmedins_code,</if>
            <if test="fixmedinsName != null">fixmedins_name,</if>
            <if test="psnNo != null">psn_no,</if>
            <if test="medType != null">med_type,</if>
            <if test="feeOcurTime != null">fee_ocur_time,</if>
            <if test="cnt != null">cnt,</if>
            <if test="pric != null">pric,</if>
            <if test="chrgitmLv != null">chrgitm_lv,</if>
            <if test="hilistCode != null">hilist_code,</if>
            <if test="hilistName != null">hilist_name,</if>
            <if test="listType != null">list_type,</if>
            <if test="medListCodg != null">med_list_codg,</if>
            <if test="medinsListCodg != null">medins_list_codg,</if>
            <if test="medinsListName != null">medins_list_name,</if>
            <if test="medChrgitmType != null">med_chrgitm_type,</if>
            <if test="prodname != null">prodname,</if>
            <if test="spec != null">spec,</if>
            <if test="dosformName != null">dosform_name,</if>
            <if test="lmtUsedFlag != null">lmt_used_flag,</if>
            <if test="hospPrepFlag != null">hosp_prep_flag,</if>
            <if test="hospApprFlag != null">hosp_appr_flag,</if>
            <if test="tcmdrugUsedWay != null">tcmdrug_used_way,</if>
            <if test="prodplacType != null">prodplac_type,</if>
            <if test="basMednFlag != null">bas_medn_flag,</if>
            <if test="hiNegoDrugFlag != null">hi_nego_drug_flag,</if>
            <if test="chldMedcFlag != null">chld_medc_flag,</if>
            <if test="etipFlag != null">etip_flag,</if>
            <if test="etipHospCode != null">etip_hosp_code,</if>
            <if test="dscgTkdrugFlag != null">dscg_tkdrug_flag,</if>
            <if test="listSpItemFlag != null">list_sp_item_flag,</if>
            <if test="matnFeeFlag != null">matn_fee_flag,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="jzid != null">jzid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="feedetlSn != null">#{feedetlSn},</if>
            <if test="rxDrordNo != null">#{rxDrordNo},</if>
            <if test="fixmedinsCode != null">#{fixmedinsCode},</if>
            <if test="fixmedinsName != null">#{fixmedinsName},</if>
            <if test="psnNo != null">#{psnNo},</if>
            <if test="medType != null">#{medType},</if>
            <if test="feeOcurTime != null">#{feeOcurTime},</if>
            <if test="cnt != null">#{cnt},</if>
            <if test="pric != null">#{pric},</if>
            <if test="chrgitmLv != null">#{chrgitmLv},</if>
            <if test="hilistCode != null">#{hilistCode},</if>
            <if test="hilistName != null">#{hilistName},</if>
            <if test="listType != null">#{listType},</if>
            <if test="medListCodg != null">#{medListCodg},</if>
            <if test="medinsListCodg != null">#{medinsListCodg},</if>
            <if test="medinsListName != null">#{medinsListName},</if>
            <if test="medChrgitmType != null">#{medChrgitmType},</if>
            <if test="prodname != null">#{prodname},</if>
            <if test="spec != null">#{spec},</if>
            <if test="dosformName != null">#{dosformName},</if>
            <if test="lmtUsedFlag != null">#{lmtUsedFlag},</if>
            <if test="hospPrepFlag != null">#{hospPrepFlag},</if>
            <if test="hospApprFlag != null">#{hospApprFlag},</if>
            <if test="tcmdrugUsedWay != null">#{tcmdrugUsedWay},</if>
            <if test="prodplacType != null">#{prodplacType},</if>
            <if test="basMednFlag != null">#{basMednFlag},</if>
            <if test="hiNegoDrugFlag != null">#{hiNegoDrugFlag},</if>
            <if test="chldMedcFlag != null">#{chldMedcFlag},</if>
            <if test="etipFlag != null">#{etipFlag},</if>
            <if test="etipHospCode != null">#{etipHospCode},</if>
            <if test="dscgTkdrugFlag != null">#{dscgTkdrugFlag},</if>
            <if test="listSpItemFlag != null">#{listSpItemFlag},</if>
            <if test="matnFeeFlag != null">#{matnFeeFlag},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="jzid != null">#{jzid},</if>
         </trim>
    </insert>

    <update id="updateMztbFyxx" parameterType="MztbFyxx">
        update mztb_fyxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="feedetlSn != null">feedetl_sn = #{feedetlSn},</if>
            <if test="rxDrordNo != null">rx_drord_no = #{rxDrordNo},</if>
            <if test="fixmedinsCode != null">fixmedins_code = #{fixmedinsCode},</if>
            <if test="fixmedinsName != null">fixmedins_name = #{fixmedinsName},</if>
            <if test="psnNo != null">psn_no = #{psnNo},</if>
            <if test="medType != null">med_type = #{medType},</if>
            <if test="feeOcurTime != null">fee_ocur_time = #{feeOcurTime},</if>
            <if test="cnt != null">cnt = #{cnt},</if>
            <if test="pric != null">pric = #{pric},</if>
            <if test="chrgitmLv != null">chrgitm_lv = #{chrgitmLv},</if>
            <if test="hilistCode != null">hilist_code = #{hilistCode},</if>
            <if test="hilistName != null">hilist_name = #{hilistName},</if>
            <if test="listType != null">list_type = #{listType},</if>
            <if test="medListCodg != null">med_list_codg = #{medListCodg},</if>
            <if test="medinsListCodg != null">medins_list_codg = #{medinsListCodg},</if>
            <if test="medinsListName != null">medins_list_name = #{medinsListName},</if>
            <if test="medChrgitmType != null">med_chrgitm_type = #{medChrgitmType},</if>
            <if test="prodname != null">prodname = #{prodname},</if>
            <if test="spec != null">spec = #{spec},</if>
            <if test="dosformName != null">dosform_name = #{dosformName},</if>
            <if test="lmtUsedFlag != null">lmt_used_flag = #{lmtUsedFlag},</if>
            <if test="hospPrepFlag != null">hosp_prep_flag = #{hospPrepFlag},</if>
            <if test="hospApprFlag != null">hosp_appr_flag = #{hospApprFlag},</if>
            <if test="tcmdrugUsedWay != null">tcmdrug_used_way = #{tcmdrugUsedWay},</if>
            <if test="prodplacType != null">prodplac_type = #{prodplacType},</if>
            <if test="basMednFlag != null">bas_medn_flag = #{basMednFlag},</if>
            <if test="hiNegoDrugFlag != null">hi_nego_drug_flag = #{hiNegoDrugFlag},</if>
            <if test="chldMedcFlag != null">chld_medc_flag = #{chldMedcFlag},</if>
            <if test="etipFlag != null">etip_flag = #{etipFlag},</if>
            <if test="etipHospCode != null">etip_hosp_code = #{etipHospCode},</if>
            <if test="dscgTkdrugFlag != null">dscg_tkdrug_flag = #{dscgTkdrugFlag},</if>
            <if test="listSpItemFlag != null">list_sp_item_flag = #{listSpItemFlag},</if>
            <if test="matnFeeFlag != null">matn_fee_flag = #{matnFeeFlag},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="jzid != null">jzid = #{jzid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMztbFyxxById" parameterType="Long">
        delete from mztb_fyxx where id = #{id}
    </delete>

    <delete id="deleteMztbFyxxByIds" parameterType="String">
        delete from mztb_fyxx where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>