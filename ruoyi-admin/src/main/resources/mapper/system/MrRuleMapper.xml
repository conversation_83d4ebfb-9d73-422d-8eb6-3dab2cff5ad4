<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MrRuleMapper">
    
    <resultMap type="MrRule" id="MrRuleResult">
        <result property="id"    column="id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="potintName"    column="potint_name"    />
        <result property="ruleType"    column="rule_type"    />
        <result property="ruleValue"    column="rule_value"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMrRuleVo">
        select id, category_name, potint_name, rule_type, rule_value, is_enabled, create_time, update_time from mr_rule
    </sql>

    <select id="selectMrRuleList" parameterType="MrRule" resultMap="MrRuleResult">
        <include refid="selectMrRuleVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="potintName != null  and potintName != ''"> and potint_name like concat('%', #{potintName}, '%')</if>
            <if test="ruleType != null  and ruleType != ''"> and rule_type = #{ruleType}</if>
            <if test="ruleValue != null  and ruleValue != ''"> and rule_value = #{ruleValue}</if>
            <if test="isEnabled != null "> and is_enabled = #{isEnabled}</if>
        </where>
    </select>
    
    <select id="selectMrRuleById" parameterType="Long" resultMap="MrRuleResult">
        <include refid="selectMrRuleVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMrRule" parameterType="MrRule" useGeneratedKeys="true" keyProperty="id">
        insert into mr_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="potintName != null and potintName != ''">potint_name,</if>
            <if test="ruleType != null and ruleType != ''">rule_type,</if>
            <if test="ruleValue != null and ruleValue != ''">rule_value,</if>
            <if test="isEnabled != null">is_enabled,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="potintName != null and potintName != ''">#{potintName},</if>
            <if test="ruleType != null and ruleType != ''">#{ruleType},</if>
            <if test="ruleValue != null and ruleValue != ''">#{ruleValue},</if>
            <if test="isEnabled != null">#{isEnabled},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMrRule" parameterType="MrRule">
        update mr_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="potintName != null and potintName != ''">potint_name = #{potintName},</if>
            <if test="ruleType != null and ruleType != ''">rule_type = #{ruleType},</if>
            <if test="ruleValue != null and ruleValue != ''">rule_value = #{ruleValue},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMrRuleById" parameterType="Long">
        delete from mr_rule where id = #{id}
    </delete>

    <delete id="deleteMrRuleByIds" parameterType="String">
        delete from mr_rule where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>