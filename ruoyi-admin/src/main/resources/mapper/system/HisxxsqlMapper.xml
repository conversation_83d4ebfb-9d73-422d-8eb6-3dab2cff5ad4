<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HisxxsqlMapper">

    <resultMap type="Hisxxsql" id="HisxxsqlResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="sqlstr"    column="sqlstr"    />
        <result property="param"    column="param"    />
        <result property="type"    column="type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="dbtype"    column="dbtype"    />
        <result property="days"    column="days"    />
        <result property="hisSoft"    column="his_soft"    />
        <result property="count"    column="count"    />
        <result property="source"    column="source"    />
        <result property="targetTable" column="target_table"/>
        <result property="updateKeys" column="update_keys"/>
        <result property="incrementKey" column="increament_key"/>
    </resultMap>

    <sql id="selectHisxxsqlVo">
        select source,id, name, sqlstr, param, type, create_by, create_time, update_by, update_time, remark, dbtype, days, his_soft, target_table, update_keys, increament_key from hisxxsql
    </sql>

    <select id="selectHisxxBySqlName" parameterType="String" resultMap="HisxxsqlResult">
      select * from hisxxsql where name = #{name}
    </select>

    <select id="selectHisxxByNameAndHis" parameterType="Hisxxsql" resultType="Hisxxsql">
      SELECT max(days) as days,count(*) as count, sqlstr
      FROM hisxxsql where name = #{name} and his_soft = #{hisSoft};
    </select>

    <select id="selectHisxxsqlList" parameterType="Hisxxsql" resultMap="HisxxsqlResult">
        <include refid="selectHisxxsqlVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sqlstr != null  and sqlstr != ''"> and sqlstr = #{sqlstr}</if>
            <if test="param != null  and param != ''"> and param = #{param}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="dbtype != null  and dbtype != ''"> and dbtype = #{dbtype}</if>
            <if test="days != null "> and days = #{days}</if>
            <if test="hisSoft != null  and hisSoft != ''"> and his_soft = #{hisSoft}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
        </where>
    </select>

    <select id="selectHisxxsqlById" parameterType="Long" resultMap="HisxxsqlResult">
        <include refid="selectHisxxsqlVo"/>
        where id = #{id}
    </select>

    <insert id="insertHisxxsql" parameterType="Hisxxsql" useGeneratedKeys="true" keyProperty="id">
        insert into hisxxsql
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="sqlstr != null and sqlstr != ''">sqlstr,</if>
            <if test="param != null">param,</if>
            <if test="type != null">type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="dbtype != null">dbtype,</if>
            <if test="days != null">days,</if>
            <if test="hisSoft != null">his_soft,</if>
            <if test="source != null">source,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="sqlstr != null and sqlstr != ''">#{sqlstr},</if>
            <if test="param != null">#{param},</if>
            <if test="type != null">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="dbtype != null">#{dbtype},</if>
            <if test="days != null">#{days},</if>
            <if test="hisSoft != null">#{hisSoft},</if>
            <if test="source != null">#{source},</if>
         </trim>
    </insert>

    <update id="updateHisxxsql" parameterType="Hisxxsql">
        update hisxxsql
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sqlstr != null and sqlstr != ''">sqlstr = #{sqlstr},</if>
            <if test="param != null">param = #{param},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="dbtype != null">dbtype = #{dbtype},</if>
            <if test="days != null">days = #{days},</if>
            <if test="hisSoft != null">his_soft = #{hisSoft},</if>
            <if test="source != null">source = #{source},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHisxxsqlById" parameterType="Long">
        delete from hisxxsql where id = #{id}
    </delete>

    <delete id="deleteHisxxsqlByIds" parameterType="String">
        delete from hisxxsql where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
