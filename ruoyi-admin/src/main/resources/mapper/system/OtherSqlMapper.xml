<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.OtherSqlMapper">

    <resultMap type="OtherSql" id="OtherSqlResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="sqlstr"    column="sqlstr"    />
        <result property="param"    column="param"    />
        <result property="type"    column="type"    />
        <result property="dbtype"    column="dbtype"    />
        <result property="days"    column="days"    />
        <result property="hisSoft"    column="his_soft"    />
        <result property="resultType" column="result_type"  />
        <result property="targetTable" column="target_table"  />
        <result property="targetDbType" column="target_dbType"  />
        <result property="sourceDataSource" column="source_data_source"  />
        <result property="targetDataSource" column="target_data_source"  />
        <result property="executeType" column="execute_type"  />
        <result property="syncKey" column="sync_key"/>
        <result property="sourceTable" column="source_table"/>
        <result property="incrementSourceKey" column="increment_source_key"/>
        <result property="incrementSourceField" column="increment_source_field"/>
        <result property="incrementTargetField" column="increment_target_field"/>
        <result property="checkSumField" column="check_sum_field"/>
        <result property="sourceName" column="source_name"/>
    </resultMap>

    <sql id="selectOtherSqlVo">
        select id, name, sqlstr, param, type, dbtype, dbtype, target_table, target_dbType, source_data_source, target_data_source, execute_type, days, his_soft, result_type, sync_key, source_table, increment_source_key, increment_source_field, increment_target_field, check_sum_field, source_name from other_sql
    </sql>

    <select id="selectOtherSqlList" parameterType="OtherSql" resultMap="OtherSqlResult">
        <include refid="selectOtherSqlVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sqlstr != null  and sqlstr != ''"> and sqlstr = #{sqlstr}</if>
            <if test="param != null  and param != ''"> and param = #{param}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="dbtype != null  and dbtype != ''"> and dbtype = #{dbtype}</if>
            <if test="days != null "> and days = #{days}</if>
            <if test="hisSoft != null  and hisSoft != ''"> and his_soft = #{hisSoft}</if>
             <if test="executeType != null and executeType != ''">and execute_type = #{executeType}</if>
        </where>
    </select>

    <select id="selectOtherSqlById" parameterType="Long" resultMap="OtherSqlResult">
        <include refid="selectOtherSqlVo"/>
        where id = #{id}
    </select>
    <select id="selectOtherSqlByName" resultMap="OtherSqlResult">
        <include refid="selectOtherSqlVo"/>
        where name = #{name}
    </select>

    <insert id="insertOtherSql" parameterType="OtherSql">
        insert into other_sql
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="sqlstr != null">sqlstr,</if>
            <if test="param != null">param,</if>
            <if test="type != null">type,</if>
            <if test="dbtype != null">dbtype,</if>
            <if test="days != null">days,</if>
            <if test="hisSoft != null">his_soft,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="sqlstr != null">#{sqlstr},</if>
            <if test="param != null">#{param},</if>
            <if test="type != null">#{type},</if>
            <if test="dbtype != null">#{dbtype},</if>
            <if test="days != null">#{days},</if>
            <if test="hisSoft != null">#{hisSoft},</if>
         </trim>
    </insert>

    <update id="updateOtherSql" parameterType="OtherSql">
        update other_sql
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="sqlstr != null">sqlstr = #{sqlstr},</if>
            <if test="param != null">param = #{param},</if>
            <if test="type != null">type = #{type},</if>
            <if test="dbtype != null">dbtype = #{dbtype},</if>
            <if test="days != null">days = #{days},</if>
            <if test="hisSoft != null">his_soft = #{hisSoft},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOtherSqlById" parameterType="Long">
        delete from other_sql where id = #{id}
    </delete>

    <delete id="deleteOtherSqlByIds" parameterType="String">
        delete from other_sql where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
