<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.IcdStopUseMapper">
    
    <resultMap type="IcdStopUse" id="IcdStopUseResult">
        <result property="bm"    column="bm"    />
        <result property="mc"    column="mc"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectIcdStopUseVo">
        select bm, mc, type from icd_stop_use
    </sql>

    <select id="selectIcdStopUseList" parameterType="IcdStopUse" resultMap="IcdStopUseResult">
        <include refid="selectIcdStopUseVo"/>
        <where>  
            <if test="bm != null  and bm != ''"> and bm = #{bm}</if>
            <if test="mc != null  and mc != ''"> and mc = #{mc}</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectIcdStopUseByBm" parameterType="String" resultMap="IcdStopUseResult">
        <include refid="selectIcdStopUseVo"/>
        where bm = #{bm}
    </select>
        
    <insert id="insertIcdStopUse" parameterType="IcdStopUse">
        insert into icd_stop_use
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bm != null">bm,</if>
            <if test="mc != null">mc,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bm != null">#{bm},</if>
            <if test="mc != null">#{mc},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateIcdStopUse" parameterType="IcdStopUse">
        update icd_stop_use
        <trim prefix="SET" suffixOverrides=",">
            <if test="mc != null">mc = #{mc},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where bm = #{bm}
    </update>

    <delete id="deleteIcdStopUseByBm" parameterType="String">
        delete from icd_stop_use where bm = #{bm}
    </delete>

    <delete id="deleteIcdStopUseByBms" parameterType="String">
        delete from icd_stop_use where bm in 
        <foreach item="bm" collection="array" open="(" separator="," close=")">
            #{bm}
        </foreach>
    </delete>
</mapper>