<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkZnfxMapper">

    <resultMap type="YbgkZnfx" id="YbgkZnfxResult">
        <result property="id"    column="id"    />
        <result property="brbs"    column="brbs"    />
        <result property="jzh"    column="brbs"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="zyh"    column="zyh"    />
        <result property="brname"    column="brname"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="xmbm"    column="xmbm"    />
        <result property="category"    column="category"    />
        <result property="cost"    column="cost"    />
        <result property="reason"    column="reason"    />
        <result property="evidence"    column="evidence"    />
        <result property="severity"    column="severity"    />
        <result property="type"    column="type"    />
      <result property="doctorname"    column="doctorname"    />
      <result property="deptname"    column="deptname"    />
      <result property="createTime"    column="create_time"    />

    </resultMap>

    <sql id="selectYbgkZnfxVo">
        select id, brbs, brbs as jzh,brid, zyid, zyh, brname, xmmc, xmbm, category, cost, reason, evidence, severity, type, doctorname, deptname, create_time from ybgk_znfx
    </sql>

    <select id="selectYbgkZnfxList" parameterType="YbgkZnfx" resultMap="YbgkZnfxResult">
        <include refid="selectYbgkZnfxVo"/>
        <where>
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="zyh != null  and zyh != ''"> and zyh = #{zyh}</if>
            <if test="brname != null  and brname != ''"> and brname like concat('%', #{brname}, '%')</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc = #{xmmc}</if>
            <if test="xmbm != null  and xmbm != ''"> and xmbm = #{xmbm}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="cost != null "> and cost = #{cost}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="evidence != null  and evidence != ''"> and evidence = #{evidence}</if>
            <if test="severity != null  and severity != ''"> and severity = #{severity}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
             <if test="deptname != null and deptname != ''"> and deptname = #{deptname}</if>
          <if test="doctorname != null and doctorname != ''"> and doctorname = #{doctorname}</if>
        </where>
    </select>

    <select id="selectYbgkZnfxById" parameterType="Long" resultMap="YbgkZnfxResult">
        <include refid="selectYbgkZnfxVo"/>
        where id = #{id}
    </select>

    <insert id="insertYbgkZnfx" parameterType="YbgkZnfx" useGeneratedKeys="true" keyProperty="id">
        insert into ybgk_znfx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="zyh != null">zyh,</if>
            <if test="brname != null">brname,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="xmbm != null">xmbm,</if>
            <if test="category != null">category,</if>
            <if test="cost != null">cost,</if>
            <if test="reason != null">reason,</if>
            <if test="evidence != null">evidence,</if>
            <if test="severity != null">severity,</if>
            <if test="type != null">type,</if>
            <if test="doctorname != null">doctorname,</if>
          <if test="deptname != null">deptname,</if>
          <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="zyh != null">#{zyh},</if>
            <if test="brname != null">#{brname},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="xmbm != null">#{xmbm},</if>
            <if test="category != null">#{category},</if>
            <if test="cost != null">#{cost},</if>
            <if test="reason != null">#{reason},</if>
            <if test="evidence != null">#{evidence},</if>
            <if test="severity != null">#{severity},</if>
            <if test="type != null">#{type},</if>
            <if test="doctorname != null">#{doctorname},</if>
          <if test="deptname != null">#{deptname},</if>
          <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateYbgkZnfx" parameterType="YbgkZnfx">
        update ybgk_znfx
        <trim prefix="SET" suffixOverrides=",">
            <if test="brbs != null">brbs = #{brbs},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="zyh != null">zyh = #{zyh},</if>
            <if test="brname != null">brname = #{brname},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="xmbm != null">xmbm = #{xmbm},</if>
            <if test="category != null">category = #{category},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="evidence != null">evidence = #{evidence},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbgkZnfxById" parameterType="Long">
        delete from ybgk_znfx where id = #{id}
    </delete>

    <delete id="deleteYbgkZnfxByIds" parameterType="String">
        delete from ybgk_znfx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
  <delete id="deleteByBridAndZYid">
    delete from ybgk_znfx where brid = #{brid} and zyid = #{zyid}
  </delete>
</mapper>
