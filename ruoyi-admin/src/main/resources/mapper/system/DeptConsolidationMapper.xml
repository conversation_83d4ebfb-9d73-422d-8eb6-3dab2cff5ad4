<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DeptConsolidationMapper">
    
    <resultMap type="DeptConsolidation" id="DeptConsolidationResult">
        <result property="id"    column="id"    />
        <result property="sourceDeptName"    column="source_dept_name"    />
        <result property="targetDeptName"    column="target_dept_name"    />
        <result property="jgid"    column="jgid"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectDeptConsolidationVo">
        select id, source_dept_name, target_dept_name, jgid, status from dept_consolidation
    </sql>

    <select id="selectDeptConsolidationList" parameterType="DeptConsolidation" resultMap="DeptConsolidationResult">
        <include refid="selectDeptConsolidationVo"/>
        <where>  
            <if test="sourceDeptName != null  and sourceDeptName != ''"> and source_dept_name like concat('%', #{sourceDeptName}, '%')</if>
            <if test="targetDeptName != null  and targetDeptName != ''"> and target_dept_name like concat('%', #{targetDeptName}, '%')</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDeptConsolidationById" parameterType="Long" resultMap="DeptConsolidationResult">
        <include refid="selectDeptConsolidationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDeptConsolidation" parameterType="DeptConsolidation" useGeneratedKeys="true" keyProperty="id">
        insert into dept_consolidation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sourceDeptName != null">source_dept_name,</if>
            <if test="targetDeptName != null">target_dept_name,</if>
            <if test="jgid != null">jgid,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sourceDeptName != null">#{sourceDeptName},</if>
            <if test="targetDeptName != null">#{targetDeptName},</if>
            <if test="jgid != null">#{jgid},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateDeptConsolidation" parameterType="DeptConsolidation">
        update dept_consolidation
        <trim prefix="SET" suffixOverrides=",">
            <if test="sourceDeptName != null">source_dept_name = #{sourceDeptName},</if>
            <if test="targetDeptName != null">target_dept_name = #{targetDeptName},</if>
            <if test="jgid != null">jgid = #{jgid},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeptConsolidationById" parameterType="Long">
        delete from dept_consolidation where id = #{id}
    </delete>

    <delete id="deleteDeptConsolidationByIds" parameterType="String">
        delete from dept_consolidation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>