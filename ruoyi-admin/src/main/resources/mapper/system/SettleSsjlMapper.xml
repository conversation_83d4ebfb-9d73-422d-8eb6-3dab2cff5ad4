<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SettleSsjlMapper">

    <resultMap type="SettleSsjl" id="SettleSsjlResult">
        <result property="id"    column="id"    />
        <result property="brbs"    column="brbs"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="ssbm"    column="ssbm"    />
        <result property="ssmc"    column="ssmc"    />
        <result property="sscx"    column="sscx"    />
        <result property="ssjb"    column="ssjb"    />
        <result property="ssrq"    column="ssrq"    />
        <result property="sskssj"    column="sskssj"    />
        <result property="ssjssj"    column="ssjssj"    />
        <result property="sz"    column="sz"    />
        <result property="qkyhdj"    column="qkyhdj"    />
        <result property="mzfs"    column="mzfs"    />
        <result property="mzys"    column="mzys"    />
        <result property="ssqk"    column="ssqk"    />
        <result property="mzkssj"    column="mzkssj"    />
        <result property="mzjssj"    column="mzjssj"    />
        <result property="zdysdm"    column="zdysdm"    />
        <result property="mzysdm"    column="mzysdm"    />
        <result property="jlly"    column="jlly"    />
        <result property="jgid"    column="jgid"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectSettleSsjlVo">
        select id, brbs, brid, zyid, ssbm, ssmc, sscx, ssjb, ssrq, sskssj, ssjssj, sz, qkyhdj, mzfs, mzys, ssqk, mzkssj, mzjssj, zdysdm, mzysdm, jlly, jgid, update_time, create_time, update_by, create_by from settle_ssjl
    </sql>

    <select id="selectSettleSsjlList" parameterType="SettleSsjl" resultMap="SettleSsjlResult">
        <include refid="selectSettleSsjlVo"/>
        <where>
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="ssbm != null  and ssbm != ''"> and ssbm = #{ssbm}</if>
            <if test="ssmc != null  and ssmc != ''"> and ssmc = #{ssmc}</if>
            <if test="sscx != null "> and sscx = #{sscx}</if>
            <if test="ssjb != null  and ssjb != ''"> and ssjb = #{ssjb}</if>
            <if test="ssrq != null "> and ssrq = #{ssrq}</if>
            <if test="sskssj != null "> and sskssj = #{sskssj}</if>
            <if test="ssjssj != null "> and ssjssj = #{ssjssj}</if>
            <if test="sz != null  and sz != ''"> and sz = #{sz}</if>
            <if test="qkyhdj != null  and qkyhdj != ''"> and qkyhdj = #{qkyhdj}</if>
            <if test="mzfs != null  and mzfs != ''"> and mzfs = #{mzfs}</if>
            <if test="mzys != null  and mzys != ''"> and mzys = #{mzys}</if>
            <if test="ssqk != null  and ssqk != ''"> and ssqk = #{ssqk}</if>
            <if test="mzkssj != null "> and mzkssj = #{mzkssj}</if>
            <if test="mzjssj != null "> and mzjssj = #{mzjssj}</if>
            <if test="zdysdm != null  and zdysdm != ''"> and zdysdm = #{zdysdm}</if>
            <if test="mzysdm != null  and mzysdm != ''"> and mzysdm = #{mzysdm}</if>
            <if test="jlly != null  and jlly != ''"> and jlly = #{jlly}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
        </where>
        order by sscx
    </select>

    <select id="selectSettleSsjlById" parameterType="Long" resultMap="SettleSsjlResult">
        <include refid="selectSettleSsjlVo"/>
        where id = #{id}
    </select>

    <insert id="insertSettleSsjl" parameterType="SettleSsjl" useGeneratedKeys="true" keyProperty="id">
        insert into settle_ssjl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="ssbm != null">ssbm,</if>
            <if test="ssmc != null">ssmc,</if>
            <if test="sscx != null">sscx,</if>
            <if test="ssjb != null">ssjb,</if>
            <if test="ssrq != null">ssrq,</if>
            <if test="sskssj != null">sskssj,</if>
            <if test="ssjssj != null">ssjssj,</if>
            <if test="sz != null">sz,</if>
            <if test="qkyhdj != null">qkyhdj,</if>
            <if test="mzfs != null">mzfs,</if>
            <if test="mzys != null">mzys,</if>
            <if test="ssqk != null">ssqk,</if>
            <if test="mzkssj != null">mzkssj,</if>
            <if test="mzjssj != null">mzjssj,</if>
            <if test="zdysdm != null">zdysdm,</if>
            <if test="mzysdm != null">mzysdm,</if>
            <if test="jlly != null">jlly,</if>
            <if test="jgid != null">jgid,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="ssbm != null">#{ssbm},</if>
            <if test="ssmc != null">#{ssmc},</if>
            <if test="sscx != null">#{sscx},</if>
            <if test="ssjb != null">#{ssjb},</if>
            <if test="ssrq != null">#{ssrq},</if>
            <if test="sskssj != null">#{sskssj},</if>
            <if test="ssjssj != null">#{ssjssj},</if>
            <if test="sz != null">#{sz},</if>
            <if test="qkyhdj != null">#{qkyhdj},</if>
            <if test="mzfs != null">#{mzfs},</if>
            <if test="mzys != null">#{mzys},</if>
            <if test="ssqk != null">#{ssqk},</if>
            <if test="mzkssj != null">#{mzkssj},</if>
            <if test="mzjssj != null">#{mzjssj},</if>
            <if test="zdysdm != null">#{zdysdm},</if>
            <if test="mzysdm != null">#{mzysdm},</if>
            <if test="jlly != null">#{jlly},</if>
            <if test="jgid != null">#{jgid},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>
    
 
<insert id="batchInsert" parameterType="list">
    INSERT INTO settle_ssjl (  <!-- 移除了 id 字段 -->
        brbs, brid, zyid, ssbm, ssmc, sscx, ssjb, ssrq,
        sskssj, ssjssj, sz, qkyhdj, mzfs, mzys, ssqk, mzkssj,
        mzjssj, zdysdm, mzysdm, jlly, jgid, update_time,
        create_time, update_by, create_by
    ) VALUES
    <foreach collection="list" item="item" separator=",">
        (
            <!-- #{item.id},  已移除  --> 
            #{item.brbs},
            #{item.brid},
            #{item.zyid},
            #{item.ssbm},
            #{item.ssmc},
            #{item.sscx},
            #{item.ssjb},
            #{item.ssrq},
            #{item.sskssj},
            #{item.ssjssj},
            #{item.sz},
            #{item.qkyhdj},
            #{item.mzfs},
            #{item.mzys},
            #{item.ssqk},
            #{item.mzkssj},
            #{item.mzjssj},
            #{item.zdysdm},
            #{item.mzysdm},
            #{item.jlly},
            #{item.jgid},
            #{item.updateTime,jdbcType=TIMESTAMP},  <!-- 添加jdbcType -->
            #{item.createTime,jdbcType=TIMESTAMP},  <!-- 添加jdbcType -->
            #{item.updateBy},
            #{item.createBy}
        )
    </foreach>
</insert>

    <update id="updateSettleSsjl" parameterType="SettleSsjl">
        update settle_ssjl
        <trim prefix="SET" suffixOverrides=",">
            <if test="brbs != null">brbs = #{brbs},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="ssbm != null">ssbm = #{ssbm},</if>
            <if test="ssmc != null">ssmc = #{ssmc},</if>
            <if test="sscx != null">sscx = #{sscx},</if>
            <if test="ssjb != null">ssjb = #{ssjb},</if>
            <if test="ssrq != null">ssrq = #{ssrq},</if>
            <if test="sskssj != null">sskssj = #{sskssj},</if>
            <if test="ssjssj != null">ssjssj = #{ssjssj},</if>
            <if test="sz != null">sz = #{sz},</if>
            <if test="qkyhdj != null">qkyhdj = #{qkyhdj},</if>
            <if test="mzfs != null">mzfs = #{mzfs},</if>
            <if test="mzys != null">mzys = #{mzys},</if>
            <if test="ssqk != null">ssqk = #{ssqk},</if>
            <if test="mzkssj != null">mzkssj = #{mzkssj},</if>
            <if test="mzjssj != null">mzjssj = #{mzjssj},</if>
            <if test="zdysdm != null">zdysdm = #{zdysdm},</if>
            <if test="mzysdm != null">mzysdm = #{mzysdm},</if>
            <if test="jlly != null">jlly = #{jlly},</if>
            <if test="jgid != null">jgid = #{jgid},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSettleSsjlById" parameterType="Long">
        delete from settle_ssjl where id = #{id}
    </delete>

    <delete id="deleteSettleSsjlByIds" parameterType="String">
        delete from settle_ssjl where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
  <delete id="deleteSettleSsjlByBrbs">
    delete from settle_ssjl
    <where>
      and brbs = #{brbs}
      <if test="jlly != null and jlly != ''">and jlly = #{jlly}</if>
    </where>
  </delete>
</mapper>
