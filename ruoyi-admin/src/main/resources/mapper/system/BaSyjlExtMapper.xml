<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaSyjlExtMapper">

    <resultMap type="BaSyjlExt" id="BaSyjlExtResult">
        <result property="brbs"    column="brbs"    />
        <result property="drgbh20"    column="drgbh20"    />
        <result property="drgmc20"    column="drgmc20"    />
        <result property="zfbz20"    column="zfbz20"    />
        <result property="cndrgbh"    column="cndrgbh"    />
        <result property="cndrgmc"    column="cndrgmc"    />
        <result property="cndrgzfqz"    column="cndrgzfqz"    />
        <result property="cndrgfxdj"    column="cndrgfxdj"    />
      <result property="qy"    column="qy"    />
    </resultMap>

    <sql id="selectBaSyjlExtVo">
        select brbs, drgbh20, drgmc20, zfbz20, cndrgbh, cndrgmc, cndrgzfqz, cndrgfxdj, qy from ba_syjl_ext
    </sql>

    <select id="selectBaSyjlExtList" parameterType="BaSyjlExt" resultMap="BaSyjlExtResult">
        <include refid="selectBaSyjlExtVo"/>
        <where>
            <if test="drgbh20 != null  and drgbh20 != ''"> and drgbh20 = #{drgbh20}</if>
            <if test="drgmc20 != null  and drgmc20 != ''"> and drgmc20 = #{drgmc20}</if>
            <if test="zfbz20 != null "> and zfbz20 = #{zfbz20}</if>
            <if test="cndrgbh != null  and cndrgbh != ''"> and cndrgbh = #{cndrgbh}</if>
            <if test="cndrgmc != null  and cndrgmc != ''"> and cndrgmc = #{cndrgmc}</if>
            <if test="cndrgzfqz != null "> and cndrgzfqz = #{cndrgzfqz}</if>
            <if test="cndrgfxdj != null  and cndrgfxdj != ''"> and cndrgfxdj = #{cndrgfxdj}</if>
          <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
          <if test="qy != null  and qy != ''"> and qy = #{qy}</if>
        </where>
    </select>

    <select id="selectBaSyjlExtByBrbs" parameterType="String" resultMap="BaSyjlExtResult">
        <include refid="selectBaSyjlExtVo"/>
        where brbs = #{brbs}
    </select>

    <insert id="insertBaSyjlExt" parameterType="BaSyjlExt">
        insert into ba_syjl_ext
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="drgbh20 != null">drgbh20,</if>
            <if test="drgmc20 != null">drgmc20,</if>
            <if test="zfbz20 != null">zfbz20,</if>
            <if test="cndrgbh != null">cndrgbh,</if>
            <if test="cndrgmc != null">cndrgmc,</if>
            <if test="cndrgzfqz != null">cndrgzfqz,</if>
            <if test="cndrgfxdj != null">cndrgfxdj,</if>
          <if test="qy != null">qy,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="drgbh20 != null">#{drgbh20},</if>
            <if test="drgmc20 != null">#{drgmc20},</if>
            <if test="zfbz20 != null">#{zfbz20},</if>
            <if test="cndrgbh != null">#{cndrgbh},</if>
            <if test="cndrgmc != null">#{cndrgmc},</if>
            <if test="cndrgzfqz != null">#{cndrgzfqz},</if>
            <if test="cndrgfxdj != null">#{cndrgfxdj},</if>
          <if test="qy != null">#{qy},</if>
         </trim>
    </insert>

    <update id="updateBaSyjlExt" parameterType="BaSyjlExt">
        update ba_syjl_ext
        <trim prefix="SET" suffixOverrides=",">
            <if test="drgbh20 != null">drgbh20 = #{drgbh20},</if>
            <if test="drgmc20 != null">drgmc20 = #{drgmc20},</if>
            <if test="zfbz20 != null">zfbz20 = #{zfbz20},</if>
            <if test="cndrgbh != null">cndrgbh = #{cndrgbh},</if>
            <if test="cndrgmc != null">cndrgmc = #{cndrgmc},</if>
            <if test="cndrgzfqz != null">cndrgzfqz = #{cndrgzfqz},</if>
            <if test="cndrgfxdj != null">cndrgfxdj = #{cndrgfxdj},</if>
          <if test="qy != null">qy = #{qy},</if>
        </trim>
        where brbs = #{brbs}
    </update>

    <delete id="deleteBaSyjlExtByBrbs" parameterType="String">
        delete from ba_syjl_ext where brbs = #{brbs}
    </delete>

    <delete id="deleteBaSyjlExtByBrbss" parameterType="String">
        delete from ba_syjl_ext where brbs in
        <foreach item="brbs" collection="array" open="(" separator="," close=")">
            #{brbs}
        </foreach>
    </delete>
</mapper>
