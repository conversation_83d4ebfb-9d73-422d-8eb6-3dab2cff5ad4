<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ListSaveRecordMapper">

    <resultMap type="ListSaveRecord" id="ListSaveRecordResult">
        <result property="id"    column="id"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="infoJson"    column="info_json"    />
        <result property="resultJson"    column="result_json"    />
        <result property="submitTime"    column="submit_time"    />
      <result property="targetUrl" column="target_url" />
    </resultMap>

    <sql id="selectListSaveRecordVo">
        select id, brid, zyid, info_json, result_json, submit_time, target_url from list_save_record
    </sql>

    <select id="selectListSaveRecordList" parameterType="ListSaveRecord" resultMap="ListSaveRecordResult">
        <include refid="selectListSaveRecordVo"/>
        <where>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="infoJson != null  and infoJson != ''"> and info_json = #{infoJson}</if>
            <if test="resultJson != null  and resultJson != ''"> and result_json = #{resultJson}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
        </where>
    </select>

    <select id="selectListSaveRecordById" parameterType="Long" resultMap="ListSaveRecordResult">
        <include refid="selectListSaveRecordVo"/>
        where id = #{id}
    </select>
  <select id="selectLastSubmitByPatient" resultMap="ListSaveRecordResult">
    <include refid="selectListSaveRecordVo"/>
    where brid = #{brid} and zyid = #{zyid}
    and target_url like '%saveSettle%'
    order by submit_time desc
    limit 1
  </select>

  <insert id="insertListSaveRecord" parameterType="ListSaveRecord" useGeneratedKeys="true" keyProperty="id">
        insert into list_save_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="infoJson != null">info_json,</if>
            <if test="resultJson != null">result_json,</if>
            <if test="submitTime != null">submit_time,</if>
          <if test="targetUrl != null">target_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="infoJson != null">#{infoJson},</if>
            <if test="resultJson != null">#{resultJson},</if>
            <if test="submitTime != null">#{submitTime},</if>
          <if test="targetUrl != null">#{targetUrl}</if>
         </trim>
    </insert>

    <update id="updateListSaveRecord" parameterType="ListSaveRecord">
        update list_save_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="infoJson != null">info_json = #{infoJson},</if>
            <if test="resultJson != null">result_json = #{resultJson},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteListSaveRecordById" parameterType="Long">
        delete from list_save_record where id = #{id}
    </delete>

    <delete id="deleteListSaveRecordByIds" parameterType="String">
        delete from list_save_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
