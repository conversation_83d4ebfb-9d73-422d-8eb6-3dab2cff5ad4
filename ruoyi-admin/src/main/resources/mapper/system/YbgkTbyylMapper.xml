<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkTbyylMapper">
    
    <resultMap type="YbgkTbyyl" id="YbgkTbyylResult">
        <result property="id"    column="id"    />
        <result property="bzbm"    column="bzbm"    />
        <result property="bzmc"    column="bzmc"    />
        <result property="type"    column="type"    />
        <result property="xmbm"    column="xmbm"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="usecs"    column="usecs"    />
        <result property="usesl"    column="usesl"    />
        <result property="monthsl"    column="monthsl"    />
        <result property="yearsl"    column="yearsl"    />
        <result property="flag"    column="flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="jgid"    column="jgid"    />
         <result property="tbbz"    column="tbbz"    />
          <result property="tbbm"    column="tbbm"    />
    </resultMap>

    <sql id="selectYbgkTbyylVo">
        select id, bzbm, bzmc, type, xmbm, xmmc, usecs, usesl, monthsl, yearsl, flag, create_by, create_date, update_by, update_date, remarks, del_flag, jgid,tbbz,tbbm from ybgk_tbyyl
    </sql>

    <select id="selectYbgkTbyylList" parameterType="YbgkTbyyl" resultMap="YbgkTbyylResult">
        <include refid="selectYbgkTbyylVo"/>
        <where>  
            <if test="bzbm != null  and bzbm != ''"> and bzbm = #{bzbm}</if>
            <if test="bzmc != null  and bzmc != ''"> and bzmc = #{bzmc}</if>
            <if test="xmbm != null  and xmbm != ''"> and xmbm = #{xmbm}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc = #{xmmc}</if>
        </where>
    </select>
    
     <select id="selectYbgkTbyylListbz" parameterType="YbgkTbyyl" resultMap="YbgkTbyylResult">
        select  tbbm,bzbm ,max(bzmc)as bzmc,max(tbbz) as tbbz  from ybgk_tbyyl
        group by tbbm,bzbm
    </select>
    
    <select id="selectYbgkTbyylById" parameterType="Long" resultMap="YbgkTbyylResult">
        <include refid="selectYbgkTbyylVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertYbgkTbyyl" parameterType="YbgkTbyyl" useGeneratedKeys="true" keyProperty="id">
        insert into ybgk_tbyyl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bzbm != null and bzbm != ''">bzbm,</if>
            <if test="bzmc != null and bzmc != ''">bzmc,</if>
            <if test="type != null">type,</if>
            <if test="xmbm != null">xmbm,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="usecs != null">usecs,</if>
            <if test="usesl != null">usesl,</if>
            <if test="monthsl != null">monthsl,</if>
            <if test="yearsl != null">yearsl,</if>
            <if test="flag != null">flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="jgid != null">jgid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bzbm != null and bzbm != ''">#{bzbm},</if>
            <if test="bzmc != null and bzmc != ''">#{bzmc},</if>
            <if test="type != null">#{type},</if>
            <if test="xmbm != null">#{xmbm},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="usecs != null">#{usecs},</if>
            <if test="usesl != null">#{usesl},</if>
            <if test="monthsl != null">#{monthsl},</if>
            <if test="yearsl != null">#{yearsl},</if>
            <if test="flag != null">#{flag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="jgid != null">#{jgid},</if>
         </trim>
    </insert>

    <update id="updateYbgkTbyyl" parameterType="YbgkTbyyl">
        update ybgk_tbyyl
        <trim prefix="SET" suffixOverrides=",">
            <if test="bzbm != null and bzbm != ''">bzbm = #{bzbm},</if>
            <if test="bzmc != null and bzmc != ''">bzmc = #{bzmc},</if>
            <if test="type != null">type = #{type},</if>
            <if test="xmbm != null">xmbm = #{xmbm},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="usecs != null">usecs = #{usecs},</if>
            <if test="usesl != null">usesl = #{usesl},</if>
            <if test="monthsl != null">monthsl = #{monthsl},</if>
            <if test="yearsl != null">yearsl = #{yearsl},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="jgid != null">jgid = #{jgid},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbgkTbyylById" parameterType="Long">
        delete from ybgk_tbyyl where id = #{id}
    </delete>

    <delete id="deleteYbgkTbyylByIds" parameterType="String">
        delete from ybgk_tbyyl where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>