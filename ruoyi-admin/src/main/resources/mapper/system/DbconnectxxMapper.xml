<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DbconnectxxMapper">

    <resultMap type="Dbconnectxx" id="DbconnectxxResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="dbtype"    column="dbtype"    />
        <result property="connecttype"    column="connecttype"    />
        <result property="servername"    column="servername"    />
        <result property="username"    column="username"    />
        <result property="password"    column="password"    />
        <result property="param"    column="param"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="dbname"    column="dbname"    />
        <result property="hisSoft"    column="his_soft"    />
        <result property="connectStr"    column="connect_str"    />
          <result property="connectnum"    column="connectnum"    />
    </resultMap>

    <sql id="selectDbconnectxxVo">
        select id, name, dbtype, connecttype, servername, connect_str, username, password, param, create_by, create_time, update_by, update_time, remark, dbname, his_soft,connectnum from dbconnectxx
    </sql>

    <select id="selectDbconnectxxList" parameterType="Dbconnectxx" resultMap="DbconnectxxResult">
        <include refid="selectDbconnectxxVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="dbtype != null  and dbtype != ''"> and dbtype = #{dbtype}</if>
            <if test="connecttype != null  and connecttype != ''"> and connecttype = #{connecttype}</if>
            <if test="servername != null  and servername != ''"> and servername like concat('%', #{servername}, '%')</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="param != null  and param != ''"> and param = #{param}</if>
            <if test="dbname != null  and dbname != ''"> and dbname like concat('%', #{dbname}, '%')</if>
            <if test="hisSoft != null  and hisSoft != ''"> and his_soft = #{hisSoft}</if>
        </where>
    </select>

    <select id="selectDbconnectxxById" parameterType="Long" resultMap="DbconnectxxResult">
        <include refid="selectDbconnectxxVo"/>
        where id = #{id}
    </select>
    <select id="getSelfSysHis" resultMap="DbconnectxxResult">
        SELECT * FROM dbconnectxx a,ybjk_option b WHERE a.name=b.c_value AND b.c_code='hissoftname'
    </select>
  <select id="getSourceInfo" resultMap="DbconnectxxResult">
    select * from dbconnectxx where name = #{sourceName}
  </select>
  <select id="selectDbconnectxxByName" parameterType="String" resultMap="DbconnectxxResult">
    <include refid="selectDbconnectxxVo"/>
    where name = #{name}
  </select>

    <insert id="insertDbconnectxx" parameterType="Dbconnectxx" useGeneratedKeys="true" keyProperty="id">
        insert into dbconnectxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="dbtype != null and dbtype != ''">dbtype,</if>
            <if test="connecttype != null and connecttype != ''">connecttype,</if>
            <if test="servername != null and servername != ''">servername,</if>
            <if test="username != null and username != ''">username,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="param != null">param,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="dbname != null">dbname,</if>
            <if test="hisSoft != null">his_soft,</if>
            <if test="connectnum != null">connectnum,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="dbtype != null and dbtype != ''">#{dbtype},</if>
            <if test="connecttype != null and connecttype != ''">#{connecttype},</if>
            <if test="servername != null and servername != ''">#{servername},</if>
            <if test="username != null and username != ''">#{username},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="param != null">#{param},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="dbname != null">#{dbname},</if>
            <if test="hisSoft != null">#{hisSoft},</if>
             <if test="connectnum != null">#{connectnum},</if>
         </trim>
    </insert>

    <update id="updateDbconnectxx" parameterType="Dbconnectxx">
        update dbconnectxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="dbtype != null and dbtype != ''">dbtype = #{dbtype},</if>
            <if test="connecttype != null and connecttype != ''">connecttype = #{connecttype},</if>
            <if test="servername != null and servername != ''">servername = #{servername},</if>
            <if test="username != null and username != ''">username = #{username},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="param != null">param = #{param},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="dbname != null">dbname = #{dbname},</if>
            <if test="hisSoft != null">his_soft = #{hisSoft},</if>
             <if test="connectnum != null">his_soft = #{connectnum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbconnectxxById" parameterType="Long">
        delete from dbconnectxx where id = #{id}
    </delete>

    <delete id="deleteDbconnectxxByIds" parameterType="String">
        delete from dbconnectxx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
