<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LlmChatDictMapper">

    <resultMap type="LlmChatDict" id="LlmChatDictResult">
        <result property="id"    column="id"    />
        <result property="label"    column="label"    />
        <result property="value"    column="value"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectLlmChatDictVo">
        select id, label, value, status from llm_chat_dict
    </sql>

    <select id="selectLlmChatDictList" parameterType="LlmChatDict" resultMap="LlmChatDictResult">
        <include refid="selectLlmChatDictVo"/>
        <where>
            <if test="label != null  and label != ''"> and label = #{label}</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectLlmChatDictById" parameterType="Long" resultMap="LlmChatDictResult">
        <include refid="selectLlmChatDictVo"/>
        where id = #{id}
    </select>

    <insert id="insertLlmChatDict" parameterType="LlmChatDict" useGeneratedKeys="true" keyProperty="id">
        insert into llm_chat_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="label != null">label,</if>
            <if test="value != null">value,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="label != null">#{label},</if>
            <if test="value != null">#{value},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateLlmChatDict" parameterType="LlmChatDict">
        update llm_chat_dict
        <trim prefix="SET" suffixOverrides=",">
            <if test="label != null">label = #{label},</if>
            <if test="value != null">value = #{value},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLlmChatDictById" parameterType="Long">
        delete from llm_chat_dict where id = #{id}
    </delete>

    <delete id="deleteLlmChatDictByIds" parameterType="String">
        delete from llm_chat_dict where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
