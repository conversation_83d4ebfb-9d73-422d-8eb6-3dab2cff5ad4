<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbjkUsesetMapper">
    
    <resultMap type="YbjkUseset" id="YbjkUsesetResult">
        <result property="id"    column="id"    />
        <result property="xzlb"    column="xzlb"    />
        <result property="jkCode"    column="jk_code"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="useflag"    column="useflag"    />
        <result property="mzzy"    column="mzzy"    />
        <result property="ismust"    column="ismust"    />
        <result property="mustchar"    column="mustchar"    />
        <result property="sort"    column="sort"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectYbjkUsesetVo">
        select id, xzlb, jk_code, code, name, useflag, mzzy, ismust, mustchar, sort, create_by, create_date, update_by, update_date, remarks, del_flag from ybjk_useset
    </sql>

    <select id="selectYbjkUsesetList" parameterType="YbjkUseset" resultMap="YbjkUsesetResult">
        <include refid="selectYbjkUsesetVo"/>
        <where>  
            <if test="xzlb != null  and xzlb != ''"> and xzlb = #{xzlb}</if>
            <if test="jkCode != null  and jkCode != ''"> and jk_code = #{jkCode}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="useflag != null  and useflag != ''"> and useflag = #{useflag}</if>
            <if test="mzzy != null  and mzzy != ''"> and mzzy = #{mzzy}</if>
            <if test="ismust != null  and ismust != ''"> and ismust = #{ismust}</if>
            <if test="mustchar != null  and mustchar != ''"> and mustchar = #{mustchar}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>
    
    <select id="selectYbjkUsesetById" parameterType="Integer" resultMap="YbjkUsesetResult">
        <include refid="selectYbjkUsesetVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertYbjkUseset" parameterType="YbjkUseset" useGeneratedKeys="true" keyProperty="id">
        insert into ybjk_useset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xzlb != null">xzlb,</if>
            <if test="jkCode != null">jk_code,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="useflag != null">useflag,</if>
            <if test="mzzy != null">mzzy,</if>
            <if test="ismust != null">ismust,</if>
            <if test="mustchar != null">mustchar,</if>
            <if test="sort != null">sort,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xzlb != null">#{xzlb},</if>
            <if test="jkCode != null">#{jkCode},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="useflag != null">#{useflag},</if>
            <if test="mzzy != null">#{mzzy},</if>
            <if test="ismust != null">#{ismust},</if>
            <if test="mustchar != null">#{mustchar},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateYbjkUseset" parameterType="YbjkUseset">
        update ybjk_useset
        <trim prefix="SET" suffixOverrides=",">
            <if test="xzlb != null">xzlb = #{xzlb},</if>
            <if test="jkCode != null">jk_code = #{jkCode},</if>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="useflag != null">useflag = #{useflag},</if>
            <if test="mzzy != null">mzzy = #{mzzy},</if>
            <if test="ismust != null">ismust = #{ismust},</if>
            <if test="mustchar != null">mustchar = #{mustchar},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbjkUsesetById" parameterType="Integer">
        delete from ybjk_useset where id = #{id}
    </delete>

    <delete id="deleteYbjkUsesetByIds" parameterType="String">
        delete from ybjk_useset where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>