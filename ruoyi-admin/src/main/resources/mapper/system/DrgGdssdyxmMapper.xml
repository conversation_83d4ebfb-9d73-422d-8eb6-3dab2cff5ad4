<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgGdssdyxmMapper">
    
    <resultMap type="DrgGdssdyxm" id="DrgGdssdyxmResult">
        <result property="sno"    column="sno"    />
        <result property="ssbm"    column="ssbm"    />
        <result property="ssmc"    column="ssmc"    />
        <result property="ssjb"    column="ssjb"    />
        <result property="sstype"    column="sstype"    />
        <result property="c1"    column="c1"    />
        <result property="c2"    column="c2"    />
        <result property="xmbm"    column="xmbm"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="xmmc1"    column="xmmc1"    />
        <result property="xmbmold"    column="xmbmold"    />
        <result property="flag"    column="flag"    />
        <result property="ybssbm"    column="ybssbm"    />
        <result property="drgflag"    column="drgflag"    />
        <result property="mrflag"    column="mrflag"    />
        <result property="id"    column="id"    />
        <result property="ssmcxmmc"    column="ssmcxmmc"    />
        <result property="xsd"    column="xsd"    />
    </resultMap>

    <sql id="selectDrgGdssdyxmVo">
        select sno, ssbm, ssmc, ssjb, sstype, c1, c2, xmbm, xmmc, xmmc1, xmbmold, flag, ybssbm, drgflag, mrflag, id, ssmcxmmc, xsd from drg_gdssdyxm
    </sql>

    <select id="selectDrgGdssdyxmList" parameterType="DrgGdssdyxm" resultMap="DrgGdssdyxmResult">
        <include refid="selectDrgGdssdyxmVo"/>
        <where>  
            <if test="sno != null  and sno != ''"> and sno = #{sno}</if>
            <if test="ssbm != null  and ssbm != ''"> and ssbm = #{ssbm}</if>
            <if test="ssmc != null  and ssmc != ''"> and ssmc = #{ssmc}</if>
            <if test="ssjb != null  and ssjb != ''"> and ssjb = #{ssjb}</if>
            <if test="sstype != null  and sstype != ''"> and sstype = #{sstype}</if>
            <if test="c1 != null  and c1 != ''"> and c1 = #{c1}</if>
            <if test="c2 != null  and c2 != ''"> and c2 = #{c2}</if>
            <if test="xmbm != null  and xmbm != ''"> and xmbm = #{xmbm}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc = #{xmmc}</if>
            <if test="xmmc1 != null  and xmmc1 != ''"> and xmmc1 = #{xmmc1}</if>
            <if test="xmbmold != null  and xmbmold != ''"> and xmbmold = #{xmbmold}</if>
            <if test="flag != null "> and flag = #{flag}</if>
            <if test="ybssbm != null  and ybssbm != ''"> and ybssbm = #{ybssbm}</if>
            <if test="drgflag != null "> and drgflag = #{drgflag}</if>
            <if test="mrflag != null "> and mrflag = #{mrflag}</if>
            <if test="ssmcxmmc != null  and ssmcxmmc != ''"> and ssmcxmmc = #{ssmcxmmc}</if>
            <if test="xsd != null "> and xsd = #{xsd}</if>
        </where>
    </select>
    
    <select id="selectDrgGdssdyxmById" parameterType="Long" resultMap="DrgGdssdyxmResult">
        <include refid="selectDrgGdssdyxmVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDrgGdssdyxm" parameterType="DrgGdssdyxm" useGeneratedKeys="true" keyProperty="id">
        insert into drg_gdssdyxm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sno != null">sno,</if>
            <if test="ssbm != null">ssbm,</if>
            <if test="ssmc != null">ssmc,</if>
            <if test="ssjb != null">ssjb,</if>
            <if test="sstype != null">sstype,</if>
            <if test="c1 != null">c1,</if>
            <if test="c2 != null">c2,</if>
            <if test="xmbm != null">xmbm,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="xmmc1 != null">xmmc1,</if>
            <if test="xmbmold != null">xmbmold,</if>
            <if test="flag != null">flag,</if>
            <if test="ybssbm != null">ybssbm,</if>
            <if test="drgflag != null">drgflag,</if>
            <if test="mrflag != null">mrflag,</if>
            <if test="ssmcxmmc != null">ssmcxmmc,</if>
            <if test="xsd != null">xsd,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sno != null">#{sno},</if>
            <if test="ssbm != null">#{ssbm},</if>
            <if test="ssmc != null">#{ssmc},</if>
            <if test="ssjb != null">#{ssjb},</if>
            <if test="sstype != null">#{sstype},</if>
            <if test="c1 != null">#{c1},</if>
            <if test="c2 != null">#{c2},</if>
            <if test="xmbm != null">#{xmbm},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="xmmc1 != null">#{xmmc1},</if>
            <if test="xmbmold != null">#{xmbmold},</if>
            <if test="flag != null">#{flag},</if>
            <if test="ybssbm != null">#{ybssbm},</if>
            <if test="drgflag != null">#{drgflag},</if>
            <if test="mrflag != null">#{mrflag},</if>
            <if test="ssmcxmmc != null">#{ssmcxmmc},</if>
            <if test="xsd != null">#{xsd},</if>
         </trim>
    </insert>

    <update id="updateDrgGdssdyxm" parameterType="DrgGdssdyxm">
        update drg_gdssdyxm
        <trim prefix="SET" suffixOverrides=",">
            <if test="sno != null">sno = #{sno},</if>
            <if test="ssbm != null">ssbm = #{ssbm},</if>
            <if test="ssmc != null">ssmc = #{ssmc},</if>
            <if test="ssjb != null">ssjb = #{ssjb},</if>
            <if test="sstype != null">sstype = #{sstype},</if>
            <if test="c1 != null">c1 = #{c1},</if>
            <if test="c2 != null">c2 = #{c2},</if>
            <if test="xmbm != null">xmbm = #{xmbm},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="xmmc1 != null">xmmc1 = #{xmmc1},</if>
            <if test="xmbmold != null">xmbmold = #{xmbmold},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="ybssbm != null">ybssbm = #{ybssbm},</if>
            <if test="drgflag != null">drgflag = #{drgflag},</if>
            <if test="mrflag != null">mrflag = #{mrflag},</if>
            <if test="ssmcxmmc != null">ssmcxmmc = #{ssmcxmmc},</if>
            <if test="xsd != null">xsd = #{xsd},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDrgGdssdyxmById" parameterType="Long">
        delete from drg_gdssdyxm where id = #{id}
    </delete>

    <delete id="deleteDrgGdssdyxmByIds" parameterType="String">
        delete from drg_gdssdyxm where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>