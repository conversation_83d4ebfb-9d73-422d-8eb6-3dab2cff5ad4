<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkXzxmMapper">

    <resultMap type="YbgkXzxm" id="YbgkXzxmResult">
        <result property="id"    column="id"    />
        <result property="xmtype"    column="xmtype"    />
        <result property="orgcode"    column="orgcode"    />
        <result property="xmbm"    column="xmbm"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="xzcode"    column="xzcode"    />
        <result property="xzname"    column="xzname"    />
        <result property="xzvalue"    column="xzvalue"    />
        <result property="ybxz"    column="ybxz"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
        <result property="useflag"    column="useflag"    />
         <result property="wjxzflag"    column="wjxzflag"    />
        <result property="showcolor"    column="showcolor"    />
    </resultMap>

    <sql id="selectYbgkXzxmVo">
        select id, xmtype, orgcode, xmbm, xmmc, xzcode, xzname, xzvalue, ybxz, create_by, create_date, update_by, update_date, remarks, useflag, wjxzflag,showcolor from ybgk_xzxm
    </sql>

    <select id="selectShowColor" parameterType="YbgkXzxm" resultType="String">
      select IFNULL(max(showcolor),'') from ybgk_xzxm where xmbm = #{xmbm} and xzcode = #{xzcode}
    </select>

    <select id="selectXzxmHistoryList" resultMap="YbgkXzxmResult" parameterType="YbgkXzxm">
      select * from ybgk_xzxm_history
    </select>

    <select id="selectYbgkXzxmList" parameterType="YbgkXzxm" resultMap="YbgkXzxmResult">
      <include refid="selectYbgkXzxmVo"/>
      <where>
        <if test="xmtype != null  and xmtype != ''">and xmtype = #{xmtype}</if>
        <if test="orgcode != null  and orgcode != ''">and orgcode = #{orgcode}</if>
        <if test="xmbm != null  and xmbm != ''">and xmbm = #{xmbm}</if>
        <if test="xmmc != null  and xmmc != ''">and xmmc = #{xmmc}</if>
        <if test="xzcode != null  and xzcode != ''">and xzcode = #{xzcode}</if>
        <if test="xzname != null  and xzname != ''">and xzname like concat('%', #{xzname}, '%')</if>
        <if test="xzvalue != null  and xzvalue != ''">and xzvalue = #{xzvalue}</if>
        <if test="ybxz != null  and ybxz != ''">and ybxz = #{ybxz}</if>
        <if test="createDate != null ">and create_date = #{createDate}</if>
        <if test="updateDate != null ">and update_date = #{updateDate}</if>
        <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
        <if test="useflag != null  and useflag != ''">and useflag = #{useflag}</if>
        and (case when #{showcolor} = '#FFFFFF' then showcolor is null or showcolor = '' or showcolor = '#FFFFFF'
        when #{showcolor} is not null and #{showcolor} != '' then showcolor = #{showcolor}
        else 1 = 1 end)
      </where>
    </select>

    <select id="selectYbgkXzxmById" parameterType="Long" resultMap="YbgkXzxmResult">
        <include refid="selectYbgkXzxmVo"/>
        where id = #{id}
    </select>

    <insert id="insertYbgkXzxm" parameterType="YbgkXzxm" useGeneratedKeys="true" keyProperty="id">
        insert into ybgk_xzxm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xmtype != null and xmtype != ''">xmtype,</if>
            <if test="orgcode != null">orgcode,</if>
            <if test="xmbm != null">xmbm,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="xzcode != null">xzcode,</if>
            <if test="xzname != null">xzname,</if>
            <if test="xzvalue != null">xzvalue,</if>
            <if test="ybxz != null">ybxz,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="useflag != null">useflag,</if>
            <if test="showcolor != null">showcolor,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xmtype != null and xmtype != ''">#{xmtype},</if>
            <if test="orgcode != null">#{orgcode},</if>
            <if test="xmbm != null">#{xmbm},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="xzcode != null">#{xzcode},</if>
            <if test="xzname != null">#{xzname},</if>
            <if test="xzvalue != null">#{xzvalue},</if>
            <if test="ybxz != null">#{ybxz},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="useflag != null">#{useflag},</if>
            <if test="showcolor != null">#{showcolor},</if>
         </trim>
    </insert>

    <update id="updateYbgkXzxm" parameterType="YbgkXzxm">
      update ybgk_xzxm
      <trim prefix="SET" suffixOverrides=",">
        <if test="xmtype != null and xmtype != ''">xmtype = #{xmtype},</if>
        <if test="orgcode != null">orgcode = #{orgcode},</if>
        <if test="xmbm != null">xmbm = #{xmbm},</if>
        <if test="xmmc != null">xmmc = #{xmmc},</if>
        <if test="xzcode != null">xzcode = #{xzcode},</if>
        <if test="xzname != null">xzname = #{xzname},</if>
        <if test="xzvalue != null">xzvalue = #{xzvalue},</if>
        <if test="ybxz != null">ybxz = #{ybxz},</if>
        <if test="createBy != null">create_by = #{createBy},</if>
        <if test="createDate != null">create_date = #{createDate},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        <if test="updateDate != null">update_date = #{updateDate},</if>
        <if test="remarks != null">remarks = #{remarks},</if>
        <if test="useflag != null">useflag = #{useflag},</if>
        <if test="showcolor != null">showcolor = #{showcolor},</if>
        <if test="showcolor == null or showcolor == ''">showcolor = null,</if>
      </trim>
      where id = #{id}
    </update>

    <delete id="deleteYbgkXzxmById" parameterType="Long">
        delete from ybgk_xzxm where id = #{id}
    </delete>

    <delete id="deleteYbgkXzxmByIds" parameterType="String">
        delete from ybgk_xzxm where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
