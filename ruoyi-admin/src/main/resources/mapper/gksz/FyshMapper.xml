<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FyshMapper">

    <resultMap type="Fysh" id="FyshResult">
        <result property="brtype"    column="brtype"    />
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="zyh"    column="zyh"    />
        <result property="name"    column="name"    />
        <result property="bed"    column="bed"    />
        <result property="age"    column="age"    />
        <result property="sex"    column="sex"    />
        <result property="tel"    column="tel"    />
        <result property="ybh"    column="ybh"    />
        <result property="jgid"    column="jgid"    />
        <result property="rydate"    column="rydate"    />
        <result property="zyzt"    column="zyzt"    />
        <result property="deptid"    column="deptid"    />
        <result property="doctorname"    column="doctorname"    />
        <result property="bzname"    column="bzname"    />
        <result property="doctorid"    column="doctorid"    />
        <result property="bzcode"    column="bzcode"    />
        <result property="deptname"    column="deptname"    />
        <result property="cydate"    column="cydate"    />
    </resultMap>

    <sql id="selectFyshVo">
        select brtype, jzh, brid, zyid, zyh, name, bed, age, sex, tel, ybh, jgid, rydate, zyzt, deptid, doctorname, bzname, doctorid, bzcode, deptname, cydate from brxx
    </sql>

    <select id="selectFyshList" parameterType="Fysh" resultMap="FyshResult">
        <include refid="selectFyshVo"/>
        <where>
            <if test="brtype != null  and brtype != ''"> and brtype = #{brtype}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="zyh != null  and zyh != ''"> and zyh = #{zyh}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="bed != null  and bed != ''"> and bed = #{bed}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="tel != null  and tel != ''"> and tel = #{tel}</if>
            <if test="ybh != null  and ybh != ''"> and ybh = #{ybh}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
            <if test="zyzt != null  and zyzt != ''"> and zyzt = #{zyzt}</if>
            <if test="deptid != null  and deptid != ''"> and deptid = #{deptid}</if>
            <if test="doctorname != null  and doctorname != ''"> and doctorname like concat('%', #{doctorname}, '%')</if>
            <if test="bzname != null  and bzname != ''"> and bzname like concat('%', #{bzname}, '%')</if>
            <if test="doctorid != null  and doctorid != ''"> and doctorid = #{doctorid}</if>
            <if test="bzcode != null  and bzcode != ''"> and bzcode = #{bzcode}</if>
            <if test="deptname != null  and deptname != ''"> and deptname like concat('%', #{deptname}, '%')</if>
            <if test="cydate != null "> and cydate = #{cydate}</if>
          <if test="dateType != null">
            AND ((#{dateType} = 'cydate' AND cydate >= #{adtFrom} AND cydate &lt; #{adtTo}) OR (#{dateType} = 'rydate' AND rydate >= #{adtFrom} AND rydate &lt; #{adtTo}))
          </if>

        </where>
        order by rydate desc
    </select>

    <select id="selectFyshks"  resultMap="FyshResult">
      select deptname from brxx where deptname is not null
      group by deptname
    </select>

    <select id="selectCompanyNumByCid" resultType="Fysh">
      select count(*) as num from p_company where c_id="Y"
    </select>

    <select id="selectFyshByBrtype" parameterType="String" resultMap="FyshResult">
        <include refid="selectFyshVo"/>
        where brtype = #{brtype}
    </select>

    <insert id="insertFysh" parameterType="Fysh">
        insert into brxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brtype != null">brtype,</if>
            <if test="jzh != null">jzh,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="zyh != null">zyh,</if>
            <if test="name != null">name,</if>
            <if test="bed != null">bed,</if>
            <if test="age != null">age,</if>
            <if test="sex != null">sex,</if>
            <if test="tel != null">tel,</if>
            <if test="ybh != null">ybh,</if>
            <if test="jgid != null">jgid,</if>
            <if test="rydate != null">rydate,</if>
            <if test="zyzt != null">zyzt,</if>
            <if test="deptid != null">deptid,</if>
            <if test="doctorname != null">doctorname,</if>
            <if test="bzname != null">bzname,</if>
            <if test="doctorid != null">doctorid,</if>
            <if test="bzcode != null">bzcode,</if>
            <if test="deptname != null">deptname,</if>
            <if test="cydate != null">cydate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brtype != null">#{brtype},</if>
            <if test="jzh != null">#{jzh},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="zyh != null">#{zyh},</if>
            <if test="name != null">#{name},</if>
            <if test="bed != null">#{bed},</if>
            <if test="age != null">#{age},</if>
            <if test="sex != null">#{sex},</if>
            <if test="tel != null">#{tel},</if>
            <if test="ybh != null">#{ybh},</if>
            <if test="jgid != null">#{jgid},</if>
            <if test="rydate != null">#{rydate},</if>
            <if test="zyzt != null">#{zyzt},</if>
            <if test="deptid != null">#{deptid},</if>
            <if test="doctorname != null">#{doctorname},</if>
            <if test="bzname != null">#{bzname},</if>
            <if test="doctorid != null">#{doctorid},</if>
            <if test="bzcode != null">#{bzcode},</if>
            <if test="deptname != null">#{deptname},</if>
            <if test="cydate != null">#{cydate},</if>
         </trim>
    </insert>

    <update id="updateFysh" parameterType="Fysh">
        update brxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="zyh != null">zyh = #{zyh},</if>
            <if test="name != null">name = #{name},</if>
            <if test="bed != null">bed = #{bed},</if>
            <if test="age != null">age = #{age},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="ybh != null">ybh = #{ybh},</if>
            <if test="jgid != null">jgid = #{jgid},</if>
            <if test="rydate != null">rydate = #{rydate},</if>
            <if test="zyzt != null">zyzt = #{zyzt},</if>
            <if test="deptid != null">deptid = #{deptid},</if>
            <if test="doctorname != null">doctorname = #{doctorname},</if>
            <if test="bzname != null">bzname = #{bzname},</if>
            <if test="doctorid != null">doctorid = #{doctorid},</if>
            <if test="bzcode != null">bzcode = #{bzcode},</if>
            <if test="deptname != null">deptname = #{deptname},</if>
            <if test="cydate != null">cydate = #{cydate},</if>
        </trim>
        where brtype = #{brtype}
    </update>

    <delete id="deleteFyshByBrtype" parameterType="String">
        delete from brxx where brtype = #{brtype}
    </delete>

    <delete id="deleteFyshByBrtypes" parameterType="String">
        delete from brxx where brtype in
        <foreach item="brtype" collection="array" open="(" separator="," close=")">
            #{brtype}
        </foreach>
    </delete>
</mapper>
