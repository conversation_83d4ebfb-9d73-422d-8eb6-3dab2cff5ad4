<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkWgjlCheckResultMapper">

    <resultMap type="YbgkWgjlCheckResult" id="YbgkWgjlCheckResultResult">
        <result property="id"    column="id"    />
        <result property="dept"    column="dept"    />
        <result property="zyh"    column="zyh"    />
        <result property="doctor"    column="doctor"    />
        <result property="wgtype"    column="wgtype"    />
        <result property="wgyy"    column="wgyy"    />
        <result property="jcjg"    column="jcjg"    />
        <result property="score"    column="score"    />
        <result property="wgjlId"    column="wgjl_id"    />
        <result property="jgid"    column="jgid"    />
        <result property="sendDate"    column="send_date"    />
    </resultMap>

    <sql id="selectYbgkWgjlCheckResultVo">
        select id, dept, zyh, doctor, wgtype, wgyy, jcjg, score, wgjl_id, send_date from ybgk_wgjl_check_result
    </sql>

    <select id="selectYbgkWgjlCheckResultList" parameterType="YbgkWgjlCheckResult" resultMap="YbgkWgjlCheckResultResult">
        <include refid="selectYbgkWgjlCheckResultVo"/>
        <where>
            <if test="dept != null  and dept != ''"> and dept = #{dept}</if>
            <if test="zyh != null  and zyh != ''"> and zyh = #{zyh}</if>
            <if test="doctor != null  and doctor != ''"> and doctor = #{doctor}</if>
            <if test="wgtype != null  and wgtype != ''"> and wgtype = #{wgtype}</if>
            <if test="wgyy != null  and wgyy != ''"> and wgyy = #{wgyy}</if>
            <if test="jcjg != null  and jcjg != ''"> and jcjg = #{jcjg}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="wgjlId != null "> and wgjl_id = #{wgjlId}</if>
            <if test="sendDateStart != null ">and send_date >= #{sendDateStart}</if>
            <if test="sendDateEnd != null ">and send_date &lt; #{sendDateEnd}</if>
        </where>
    </select>

    <select id="selectYbgkWgjlCheckResultById" parameterType="Long" resultMap="YbgkWgjlCheckResultResult">
        <include refid="selectYbgkWgjlCheckResultVo"/>
        where id = #{id}
    </select>

    <insert id="insertYbgkWgjlCheckResult" parameterType="YbgkWgjlCheckResult" useGeneratedKeys="true" keyProperty="id">
        insert into ybgk_wgjl_check_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dept != null">dept,</if>
            <if test="zyh != null">zyh,</if>
            <if test="doctor != null">doctor,</if>
            <if test="wgtype != null">wgtype,</if>
            <if test="wgyy != null">wgyy,</if>
            <if test="jcjg != null">jcjg,</if>
            <if test="score != null">score,</if>
            <if test="wgjlId != null">wgjl_id,</if>
            <if test="jgid != null">jgid,</if>
            <if test="sendDate != null">send_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dept != null">#{dept},</if>
            <if test="zyh != null">#{zyh},</if>
            <if test="doctor != null">#{doctor},</if>
            <if test="wgtype != null">#{wgtype},</if>
            <if test="wgyy != null">#{wgyy},</if>
            <if test="jcjg != null">#{jcjg},</if>
            <if test="score != null">#{score},</if>
            <if test="wgjlId != null">#{wgjlId},</if>
            <if test="jgid != null">#{jgid},</if>
            <if test="sendDate != null">#{sendDate},</if>
        </trim>
    </insert>

    <update id="updateYbgkWgjlCheckResult" parameterType="YbgkWgjlCheckResult">
        update ybgk_wgjl_check_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="dept != null">dept = #{dept},</if>
            <if test="zyh != null">zyh = #{zyh},</if>
            <if test="doctor != null">doctor = #{doctor},</if>
            <if test="wgtype != null">wgtype = #{wgtype},</if>
            <if test="wgyy != null">wgyy = #{wgyy},</if>
            <if test="jcjg != null">jcjg = #{jcjg},</if>
            <if test="score != null">score = #{score},</if>
            <if test="wgjlId != null">wgjl_id = #{wgjlId},</if>
            <if test="sendDate != null">send_date = #{sendDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbgkWgjlCheckResultById" parameterType="Long">
        delete from ybgk_wgjl_check_result where id = #{id}
    </delete>

    <delete id="deleteYbgkWgjlCheckResultByIds" parameterType="String">
        delete from ybgk_wgjl_check_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
