<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbjkJklogMapper">

  <resultMap type="YbjkJklog" id="YbjkJklogResult">
    <result property="id"    column="id"    />
    <result property="jkCode"    column="jk_code"    />
    <result property="xzlb"    column="xzlb"    />
    <result property="brtype"    column="brtype"    />
    <result property="jzh"    column="jzh"    />
    <result property="bed"    column="bed"    />
    <result property="jktype"    column="jktype"    />
    <result property="jklog"    column="jklog"    />
    <result property="brname"    column="brname"    />
    <result property="kdks"    column="kdks"    />
    <result property="doctor"    column="doctor"    />
    <result property="kdksname"    column="kdksname"    />
    <result property="doctorname"    column="doctorname"    />
    <result property="tshOper"    column="tsh_oper"    />
    <result property="fyType"    column="fy_type"    />
    <result property="fymId"    column="fym_id"    />
    <result property="fymName"    column="fym_name"    />
    <result property="sl"    column="sl"    />
    <result property="je"    column="je"    />
    <result property="bzmc"    column="bzmc"    />
    <result property="pl"    column="pl"    />
    <result property="jldw"    column="jldw"    />
    <result property="zkyl"    column="zkyl"    />
    <result property="kzdate"    column="kzdate"    />
    <result property="clFlag"    column="cl_flag"    />
    <result property="clHfxx"    column="cl_hfxx"    />
    <result property="jylsh"    column="jylsh"    />
    <result property="createBy"    column="create_by"    />
    <result property="createDate"    column="create_date"    />
    <result property="updateBy"    column="update_by"    />
    <result property="updateDate"    column="update_date"    />
    <result property="remarks"    column="remarks"    />
    <result property="delFlag"    column="del_flag"    />
    <result property="zyh"    column="zyh"    />
  </resultMap>

  <sql id="selectYbjkJklogVo">
    select id, jk_code, xzlb, brtype, jzh, bed, jktype, jklog, brname, kdks, doctor, kdksname, doctorname, tsh_oper, fy_type, fym_id, fym_name, sl, je, bzmc, pl, jldw, zkyl, kzdate, cl_flag, cl_hfxx, jylsh, create_by, create_date, update_by, update_date, remarks, del_flag, zyh from ybjk_jklog
  </sql>

  <select id="selectYbjkJklogList" parameterType="YbjkJklog" resultMap="YbjkJklogResult">
    <include refid="selectYbjkJklogVo"/>
    <where>
      <if test="jkCode != null  and jkCode != ''"> and jk_code = #{jkCode}</if>
      <if test="xzlb != null  and xzlb != ''"> and xzlb = #{xzlb}</if>
      <if test="brtype != null  and brtype != ''"> and brtype = #{brtype}</if>
      <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
      <if test="bed != null  and bed != ''"> and bed = #{bed}</if>
      <if test="jktype != null  and jktype != ''"> and jktype = #{jktype}</if>
      <if test="jklog != null  and jklog != ''"> and jklog = #{jklog}</if>
      <if test="brname != null  and brname != ''"> and brname like concat('%', #{brname}, '%')</if>
      <if test="kdks != null  and kdks != ''"> and kdks = #{kdks}</if>
      <if test="doctor != null  and doctor != ''"> and doctor = #{doctor}</if>
      <if test="kdksname != null  and kdksname != ''"> and kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null  and doctorname != ''"> and doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="tshOper != null  and tshOper != ''"> and tsh_oper = #{tshOper}</if>
      <if test="fyType != null  and fyType != ''"> and fy_type = #{fyType}</if>
      <if test="fymId != null  and fymId != ''"> and fym_id = #{fymId}</if>
      <if test="fymName != null  and fymName != ''"> and fym_name like concat('%', #{fymName}, '%')</if>
      <if test="sl != null "> and sl = #{sl}</if>
      <if test="je != null "> and je = #{je}</if>
      <if test="bzmc != null  and bzmc != ''"> and bzmc = #{bzmc}</if>
      <if test="pl != null  and pl != ''"> and pl = #{pl}</if>
      <if test="jldw != null  and jldw != ''"> and jldw = #{jldw}</if>
      <if test="zkyl != null  and zkyl != ''"> and zkyl = #{zkyl}</if>
      <if test="kzdate != null "> and kzdate = #{kzdate}</if>
      <if test="clFlag != null  and clFlag != ''"> and cl_flag = #{clFlag}</if>
      <if test="clHfxx != null  and clHfxx != ''"> and cl_hfxx = #{clHfxx}</if>
      <if test="jylsh != null  and jylsh != ''"> and jylsh = #{jylsh}</if>
      <if test="createDate != null "> and create_date = #{createDate}</if>
      <if test="updateDate != null "> and update_date = #{updateDate}</if>
      <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
      <if test="zyh != null  and zyh != ''"> and zyh = #{zyh}</if>
    </where>
  </select>

  <select id="selectYbjkJklogById" parameterType="String" resultMap="YbjkJklogResult">
    <include refid="selectYbjkJklogVo"/>
    where id = #{id}
  </select>

  <insert id="insertYbjkJklog" parameterType="YbjkJklog">
    insert into ybjk_jklog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="jkCode != null">jk_code,</if>
      <if test="xzlb != null">xzlb,</if>
      <if test="brtype != null">brtype,</if>
      <if test="jzh != null">jzh,</if>
      <if test="bed != null">bed,</if>
      <if test="jktype != null">jktype,</if>
      <if test="jklog != null">jklog,</if>
      <if test="brname != null">brname,</if>
      <if test="kdks != null">kdks,</if>
      <if test="doctor != null">doctor,</if>
      <if test="kdksname != null">kdksname,</if>
      <if test="doctorname != null">doctorname,</if>
      <if test="tshOper != null">tsh_oper,</if>
      <if test="fyType != null">fy_type,</if>
      <if test="fymId != null">fym_id,</if>
      <if test="fymName != null">fym_name,</if>
      <if test="sl != null">sl,</if>
      <if test="je != null">je,</if>
      <if test="bzmc != null">bzmc,</if>
      <if test="pl != null">pl,</if>
      <if test="jldw != null">jldw,</if>
      <if test="zkyl != null">zkyl,</if>
      <if test="kzdate != null">kzdate,</if>
      <if test="clFlag != null">cl_flag,</if>
      <if test="clHfxx != null">cl_hfxx,</if>
      <if test="jylsh != null">jylsh,</if>
      <if test="createBy != null">create_by,</if>
      <if test="createDate != null">create_date,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="updateDate != null">update_date,</if>
      <if test="remarks != null">remarks,</if>
      <if test="delFlag != null">del_flag,</if>
      <if test="zyh != null">zyh,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id},</if>
      <if test="jkCode != null">#{jkCode},</if>
      <if test="xzlb != null">#{xzlb},</if>
      <if test="brtype != null">#{brtype},</if>
      <if test="jzh != null">#{jzh},</if>
      <if test="bed != null">#{bed},</if>
      <if test="jktype != null">#{jktype},</if>
      <if test="jklog != null">#{jklog},</if>
      <if test="brname != null">#{brname},</if>
      <if test="kdks != null">#{kdks},</if>
      <if test="doctor != null">#{doctor},</if>
      <if test="kdksname != null">#{kdksname},</if>
      <if test="doctorname != null">#{doctorname},</if>
      <if test="tshOper != null">#{tshOper},</if>
      <if test="fyType != null">#{fyType},</if>
      <if test="fymId != null">#{fymId},</if>
      <if test="fymName != null">#{fymName},</if>
      <if test="sl != null">#{sl},</if>
      <if test="je != null">#{je},</if>
      <if test="bzmc != null">#{bzmc},</if>
      <if test="pl != null">#{pl},</if>
      <if test="jldw != null">#{jldw},</if>
      <if test="zkyl != null">#{zkyl},</if>
      <if test="kzdate != null">#{kzdate},</if>
      <if test="clFlag != null">#{clFlag},</if>
      <if test="clHfxx != null">#{clHfxx},</if>
      <if test="jylsh != null">#{jylsh},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="createDate != null">#{createDate},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="updateDate != null">#{updateDate},</if>
      <if test="remarks != null">#{remarks},</if>
      <if test="delFlag != null">#{delFlag},</if>
      <if test="zyh != null">#{zyh},</if>
    </trim>
  </insert>

  <update id="updateYbjkJklog" parameterType="YbjkJklog">
    update ybjk_jklog
    <trim prefix="SET" suffixOverrides=",">
      <if test="jkCode != null">jk_code = #{jkCode},</if>
      <if test="xzlb != null">xzlb = #{xzlb},</if>
      <if test="brtype != null">brtype = #{brtype},</if>
      <if test="jzh != null">jzh = #{jzh},</if>
      <if test="bed != null">bed = #{bed},</if>
      <if test="jktype != null">jktype = #{jktype},</if>
      <if test="jklog != null">jklog = #{jklog},</if>
      <if test="brname != null">brname = #{brname},</if>
      <if test="kdks != null">kdks = #{kdks},</if>
      <if test="doctor != null">doctor = #{doctor},</if>
      <if test="kdksname != null">kdksname = #{kdksname},</if>
      <if test="doctorname != null">doctorname = #{doctorname},</if>
      <if test="tshOper != null">tsh_oper = #{tshOper},</if>
      <if test="fyType != null">fy_type = #{fyType},</if>
      <if test="fymId != null">fym_id = #{fymId},</if>
      <if test="fymName != null">fym_name = #{fymName},</if>
      <if test="sl != null">sl = #{sl},</if>
      <if test="je != null">je = #{je},</if>
      <if test="bzmc != null">bzmc = #{bzmc},</if>
      <if test="pl != null">pl = #{pl},</if>
      <if test="jldw != null">jldw = #{jldw},</if>
      <if test="zkyl != null">zkyl = #{zkyl},</if>
      <if test="kzdate != null">kzdate = #{kzdate},</if>
      <if test="clFlag != null">cl_flag = #{clFlag},</if>
      <if test="clHfxx != null">cl_hfxx = #{clHfxx},</if>
      <if test="jylsh != null">jylsh = #{jylsh},</if>
      <if test="createBy != null">create_by = #{createBy},</if>
      <if test="createDate != null">create_date = #{createDate},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="updateDate != null">update_date = #{updateDate},</if>
      <if test="remarks != null">remarks = #{remarks},</if>
      <if test="delFlag != null">del_flag = #{delFlag},</if>
      <if test="zyh != null">zyh = #{zyh},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteYbjkJklogById" parameterType="String">
    delete from ybjk_jklog where id = #{id}
  </delete>
  <delete id="deleteYbjkJklogByJzh" parameterType="String">
    delete from ybjk_jklog where jzh = #{jzh}
  </delete>

  <delete id="deleteYbjkJklogByIds" parameterType="String">
    delete from ybjk_jklog where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>


  <select id="selectYbjkJklogjzjiById" parameterType="String" resultType="com.ruoyi.system.domain.Zdxx">
    select a.jzh,
           b.zyh,
           a.brid,
           a.zyid,
           a.zdcode,
           a.zdname,
           a.id,
           (
             CASE
               WHEN a.zdtype = '1' THEN '门诊诊断'
               WHEN a.zdtype = '2' THEN '入院诊断'
               WHEN a.zdtype = '3' THEN '出院诊断'
               WHEN a.zdtype = '5' THEN '院内感染'
               WHEN a.zdtype = '6' THEN '病理诊断'
               WHEN a.zdtype = '7' THEN '损伤中毒'
               WHEN a.zdtype = '8' THEN '术前诊断'
               WHEN a.zdtype = '9' THEN '术后诊断'
               WHEN a.zdtype = '10' THEN '并发症'
               WHEN a.zdtype = '11' THEN '门诊诊断'
               WHEN a.zdtype = '12' THEN '入院诊断'
               WHEN a.zdtype = '13' THEN '出院诊断'
               WHEN a.zdtype = '21' THEN '病原学诊断'
               WHEN a.zdtype = '22' THEN '影像学诊断'
               ELSE '其他诊断'
               END
             ) AS zdtype,
           a.zdsort,
           ( CASE WHEN a.jlly = '4' THEN '病案室' WHEN a.jlly = '3' THEN '医生' when a.jlly = '8' then '病历' ELSE '' END ) AS jlly,
           a.rybq,
           a.cyqk,
           a.fy
    from zdxx a join brxx b on a.jzh = b.jzh
    where  a.jzh = #{jzh}
    order by a.jlly,a.zdtype,a.zdsort
  </select>


  <select id="selectYbjkJklogbljlById" parameterType="String" resultType="com.ruoyi.system.domain.Blxx">
    select a.jzh,
           b.zyh,
           a.blname,
           a.blnr,
           a.doctor,
           a.createdate,
           a.updatedate
    from blxx a ,brxx b
    where a.jzh = b.jzh and a.jzh = #{jzh} order by blname,id
  </select>


  <select id="selectYbjkJklogfyxxcyById" parameterType="com.ruoyi.system.domain.Fyxx" resultType="com.ruoyi.system.domain.Fyxx">
    select jzh,
           brid,
           xmbm,
           xmmc,
           sl,
           price,
           je,
           dw,
           guige,
           fydate,
           opdate,
           fykmname,
           ysid,
           ysname,
           yzid,
           billno,
           hisid
    from fyxx_cy
    <where>
      and jzh = #{jzh}
      <if test="xmmc != null and xmmc != ''">
        and (xmmc like concat('%',#{xmmc},'%')
        <if test="gyxmmc != null and gyxmmc != ''">
          or xmmc like concat('%', #{gyxmmc}, '%')
        </if>)
      </if>
      <if test="startDate != null">
        and fydate &gt;= #{startDate}
      </if>
      <if test="endDate != null">
        and fydate &lt;= #{endDate}
      </if>
    </where>
    order by fydate,je desc
  </select>
  <select id="selectYbjkJklogfyxxById" parameterType="com.ruoyi.system.domain.Fyxx" resultType="com.ruoyi.system.domain.Fyxx">
    select jzh,
           brid,
           xmbm,
           xmmc,
           sl,
           price,
           je,
           dw,
           guige,
           fydate,
           opdate,
           fykmname,
           ysid,
           ysname,
           yzid,
           billno,
           hisid
    from fyxx
    <where>
      and jzh = #{jzh}
      <if test="xmmc != null and xmmc != ''">
        and (xmmc like concat('%',#{xmmc},'%')
        <if test="gyxmmc != null and gyxmmc != ''">
          or xmmc like concat('%', #{gyxmmc}, '%')
        </if>)
      </if>
      <if test="startDate != null">
        and fydate &gt;= #{startDate}
      </if>
      <if test="endDate != null">
        and fydate &lt;= #{endDate}
      </if>
    </where>
    order by fydate,je desc
  </select>

  <select id="selectYbjkJklogfyxxByKs" parameterType="com.ruoyi.system.domain.Fyxx" resultType="com.ruoyi.system.domain.Fyxx">
    select
      sum(sl) as sl,
      sum(price) as price,
      sum(je) as je,
      fykmname,xmmc
    from fyxx
    where jzh = #{jzh} and xmmc like concat('%',#{xmmc},'%') group  by fykmname,xmmc order by je desc
  </select>


  <select id="selectYbjkJklogbrxxById" parameterType="String" resultType="com.ruoyi.system.domain.Brxx">
    select jzh,
           zyh,
           name,
           bed,
           age,
           sex,
           tel,
           ybh,
           rydate,
           doctorname,
           bzname,
           bzcode,
           deptname,
           cydate
    from brxx
    where jzh = #{jzh}
  </select>
  <select id="selectYbjkJklogFyxxCy" parameterType="com.ruoyi.system.domain.Fyxx" resultType="com.ruoyi.system.domain.Fyxx">
    select jzh,
    brid,
    xmbm,
    xmmc,
    sl,
    price,
    je,
    dw,
    guige,
    fydate,
    opdate,
    fykmname,
    ysid,
    ysname,
    yzid,
    billno,
    hisid
    from fyxx_cy
    <where>
      and jzh = #{jzh}
      <if test="xmmc != null and xmmc != ''">
        and (xmmc like concat('%',#{xmmc},'%')
        <if test="gyxmmc != null and gyxmmc != ''">
          or xmmc like concat('%', #{gyxmmc}, '%')
        </if>)
      </if>
      <if test="startDate != null">
        and fydate &gt;= #{startDate}
      </if>
      <if test="endDate != null">
        and fydate &lt;= #{endDate}
      </if>
    </where>
    order by fydate,je desc
  </select>
</mapper>
