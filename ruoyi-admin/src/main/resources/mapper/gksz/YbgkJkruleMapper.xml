<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkJkruleMapper">

  <resultMap type="YbgkJkrule" id="YbgkJkruleResult">
    <result property="id" column="id"/>
    <result property="gjcode" column="gjcode"/>
    <result property="code" column="code"/>
    <result property="name" column="name"/>
    <result property="xzvalue" column="xzvalue"/>
    <result property="useflag" column="useflag"/>
    <result property="mzzy" column="mzzy"/>
    <result property="mxxzflag" column="mxxzflag"/>
    <result property="mustxzxx" column="mustxzxx"/>
    <result property="mustchar" column="mustchar"/>
    <result property="sort" column="sort"/>
    <result property="createBy" column="create_by"/>
    <result property="createTime" column="create_time"/>
    <result property="updateBy" column="update_by"/>
    <result property="updateTime" column="update_time"/>
    <result property="remark" column="remark"/>
    <result property="showcolor" column="showcolor"/>
  </resultMap>

  <sql id="selectYbgkJkruleVo">
    select id,
           gjcode,
           code,
           name,
           xzvalue,
           useflag,
           mzzy,
           mxxzflag,
           mustxzxx,
           mustchar,
           sort,
           create_by,
           create_time,
           update_by,
           update_time,
           remark,
           showcolor
    from ybgk_jkrule
  </sql>

  <select id="selectYbgkJkruleList" parameterType="YbgkJkrule" resultMap="YbgkJkruleResult">
    <include refid="selectYbgkJkruleVo"/>
    <where>
      <if test="gjcode != null  and gjcode != ''">and gjcode = #{gjcode}</if>
      <if test="code != null  and code != ''">and code = #{code}</if>
      <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
      <if test="xzvalue != null ">and xzvalue = #{xzvalue}</if>
      <if test="useflag != null  and useflag != ''">and useflag = #{useflag}</if>
      <if test="mzzy != null  and mzzy != ''">and mzzy = #{mzzy}</if>
      <if test="mustxzxx != null  and mustxzxx != ''">and mustxzxx = #{mustxzxx}</if>
      <if test="mustchar != null  and mustchar != ''">and mustchar = #{mustchar}</if>
      <if test="sort != null ">and sort = #{sort}</if>
      and mxxzflag = 1
      and (case when #{showcolor} = '#FFFFFF' then showcolor is null or showcolor = '' or showcolor = '#FFFFFF'
      when #{showcolor} is not null and #{showcolor} != '' then showcolor = #{showcolor}
      else 1 = 1 end)
    </where>
  </select>

  <select id="selectYbgkJkruleById" parameterType="Integer" resultMap="YbgkJkruleResult">
    <include refid="selectYbgkJkruleVo"/>
    where id = #{id}
  </select>

  <insert id="insertYbgkJkrule" parameterType="YbgkJkrule" useGeneratedKeys="true" keyProperty="id">
    insert into ybgk_jkrule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gjcode != null">gjcode,</if>
      <if test="code != null">code,</if>
      <if test="name != null">name,</if>
      <if test="xzvalue != null">xzvalue,</if>
      <if test="useflag != null">useflag,</if>
      <if test="mzzy != null">mzzy,</if>
      <if test="mxxzflag != null">mxxzflag,</if>
      <if test="mustxzxx != null">mustxzxx,</if>
      <if test="mustchar != null">mustchar,</if>
      <if test="sort != null">sort,</if>
      <if test="createBy != null">create_by,</if>
      <if test="createTime != null">create_time,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="updateTime != null">update_time,</if>
      <if test="remark != null">remark,</if>
      <if test="showcolor != null">showcolor,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gjcode != null">#{gjcode},</if>
      <if test="code != null">#{code},</if>
      <if test="name != null">#{name},</if>
      <if test="xzvalue != null">#{xzvalue},</if>
      <if test="useflag != null">#{useflag},</if>
      <if test="mzzy != null">#{mzzy},</if>
      <if test="mxxzflag != null">#{mxxzflag},</if>
      <if test="mustxzxx != null">#{mustxzxx},</if>
      <if test="mustchar != null">#{mustchar},</if>
      <if test="sort != null">#{sort},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="createTime != null">#{createTime},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="updateTime != null">#{updateTime},</if>
      <if test="remark != null">#{remark},</if>
      <if test="showcolor != null">#{showcolor},</if>
    </trim>
  </insert>

  <update id="updateYbgkJkrule" parameterType="YbgkJkrule">
    update ybgk_jkrule
    <trim prefix="SET" suffixOverrides=",">
      <if test="gjcode != null">gjcode = #{gjcode},</if>
      <if test="code != null">code = #{code},</if>
      <if test="name != null">name = #{name},</if>
      <if test="xzvalue != null">xzvalue = #{xzvalue},</if>
      <if test="useflag != null">useflag = #{useflag},</if>
      <if test="mzzy != null">mzzy = #{mzzy},</if>
      <if test="mxxzflag != null">mxxzflag = #{mxxzflag},</if>
      <if test="mustxzxx != null">mustxzxx = #{mustxzxx},</if>
      <if test="mustchar != null">mustchar = #{mustchar},</if>
      <if test="sort != null">sort = #{sort},</if>
      <if test="createBy != null">create_by = #{createBy},</if>
      <if test="createTime != null">create_time = #{createTime},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="updateTime != null">update_time = #{updateTime},</if>
      <if test="remark != null">remark = #{remark},</if>
      <if test="showcolor != null">showcolor = #{showcolor},</if>
      <if test="showcolor == null or showcolor == ''">showcolor = null,</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteYbgkJkruleById" parameterType="Integer">
    delete
    from ybgk_jkrule
    where id = #{id}
  </delete>

  <delete id="deleteYbgkJkruleByIds" parameterType="String">
    delete from ybgk_jkrule where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
