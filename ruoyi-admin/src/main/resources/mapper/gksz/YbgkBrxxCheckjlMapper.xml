<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkBrxxCheckjlMapper">

    <resultMap type="YbgkBrxxCheckjl" id="YbgkBrxxCheckjlResult">
        <result property="id"    column="id"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="checkFlag"    column="check_flag"    />
        <result property="checkFirst"    column="check_first"    />
        <result property="checkFirstDate"    column="check_first_date"    />
        <result property="checkEnd"    column="check_end"    />
        <result property="checkEndDate"    column="check_end_date"    />
    </resultMap>

    <sql id="selectYbgkBrxxCheckjlVo">
        select id, brid, zyid, check_flag, check_first, check_first_date, check_end, check_end_date from ybgk_brxx_checkjl
    </sql>

    <select id="selectYbgkBrxxCheckjlList" parameterType="YbgkBrxxCheckjl" resultMap="YbgkBrxxCheckjlResult">
        <include refid="selectYbgkBrxxCheckjlVo"/>
        <where>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="checkFlag != null "> and check_flag = #{checkFlag}</if>
            <if test="checkFirst != null "> and check_first = #{checkFirst}</if>
            <if test="checkFirstDate != null "> and check_first_date = #{checkFirstDate}</if>
            <if test="checkEnd != null "> and check_end = #{checkEnd}</if>
            <if test="checkEndDate != null "> and check_end_date = #{checkEndDate}</if>
        </where>
    </select>


  <select id="selectYbgkBrxxCheckjlByBridAndZyid" parameterType="YbgkBrxxCheckjl" resultType="YbgkBrxxCheckjl">
    select id, brid, zyid, check_flag as checkFlag, check_first as checkFirst, check_first_date as checkFirstDate, check_end as checkEnd, check_end_date as checkEndDate from ybgk_brxx_checkjl
    <where>
      <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
    </where>
  </select>

    <select id="selectYbgkBrxxCheckjlById" parameterType="Long" resultMap="YbgkBrxxCheckjlResult">
        <include refid="selectYbgkBrxxCheckjlVo"/>
        where id = #{id}
    </select>

    <insert id="insertYbgkBrxxCheckjl" parameterType="YbgkBrxxCheckjl" useGeneratedKeys="true" keyProperty="id">
        insert into ybgk_brxx_checkjl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="checkFlag != null">check_flag,</if>
            <if test="checkFirst != null">check_first,</if>
            <if test="checkFirstDate != null">check_first_date,</if>
            <if test="checkEnd != null">check_end,</if>
            <if test="checkEndDate != null">check_end_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="checkFlag != null">#{checkFlag},</if>
            <if test="checkFirst != null">#{checkFirst},</if>
            <if test="checkFirstDate != null">#{checkFirstDate},</if>
            <if test="checkEnd != null">#{checkEnd},</if>
            <if test="checkEndDate != null">#{checkEndDate},</if>
         </trim>
    </insert>

    <update id="updateYbgkBrxxCheckjl" parameterType="YbgkBrxxCheckjl">
        update ybgk_brxx_checkjl
        <trim prefix="SET" suffixOverrides=",">
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="checkFlag != null">check_flag = #{checkFlag},</if>
            <if test="checkFirst != null">check_first = #{checkFirst},</if>
            <if test="checkFirstDate != null">check_first_date = #{checkFirstDate},</if>
            <if test="checkEnd != null">check_end = #{checkEnd},</if>
            <if test="checkEndDate != null">check_end_date = #{checkEndDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbgkBrxxCheckjlById" parameterType="Long">
        delete from ybgk_brxx_checkjl where id = #{id}
    </delete>

    <delete id="deleteYbgkBrxxCheckjlByIds" parameterType="String">
        delete from ybgk_brxx_checkjl where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
