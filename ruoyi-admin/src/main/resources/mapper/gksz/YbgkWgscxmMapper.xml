<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkWgscxmMapper">

  <resultMap type="YbgkWgscxm" id="YbgkWgscxmResult">
    <result property="xmmc" column="xmmc"/>
  </resultMap>

  <resultMap type="XmWgscVo" id="XmWgscVoResult">
    <result property="brname" column="brname"/>
    <result property="kdksname" column="kdksname"/>
    <result property="doctorname" column="doctorname"/>
    <result property="zyh" column="zyh"/>
    <result property="jklog" column="jklog"/>
    <result property="fym_id" column="fym_id"/>
    <result property="fym_name" column="fym_name"/>
    <result property="sl" column="sl"/>
    <result property="je" column="je"/>
    <result property="ybbz" column="ybbz"/>
    <result property="fydj" column="fydj"/>
    <result property="sfz" column="sfz"/>
    <result property="rydate" column="rydate"/>
    <result property="cydate" column="cydate"/>
    <result property="jktype" column="jktype"/>
    <result property="price" column="price"/>
    <result property="zdxx" column="zdxx"/>
    <result property="zcpc" column="zcpc"/>
    <result property="createDate" column="create_date"/>
    <result property="brtype" column="brtype"/>
    <result property="ybbx" column="ybbx"/>
    <result property="tc" column="tc"/>
    <result property="de" column="de"/>
    <result property="fymx" column="fymx"/>
    <result property="yljz" column="yljz"/>
  </resultMap>

  <select id="selectXmWgscVoList" parameterType="XmWgscVo" resultMap="XmWgscVoResult">
    select 
    case when a.brtype='1' then '门诊' else '住院' end as  brtype,a.brname,a.kdksname,a.doctorname,a.zyh,a.jklog,a.fym_id,a.fym_name,a.sl,a.je,a.ybbz,a.jzh,b.fydj,
    a.insutype cblb,a.sfz as sfz ,DATE_FORMAT(a.rydate, '%Y-%m-%d') as rydate,DATE_FORMAT(a.cydate, '%Y-%m-%d') as cydate,d.name AS jktype,
      ROUND(a.je/a.sl) AS price,a.bzmc as zdxx ,a.create_date,b.zcpc,a.ybbx,a.tc,a.de,a.yljz,a.fymx
    FROM ybgk_wgjl a
      JOIN ybgk_wgscxm_xmbm b ON a.fym_id = b.xmbm
      JOIN ybgk_jkrule d ON a.jktype=d.code 
    <where>
      <if test="kdksname != null  and kdksname != ''">
        and (a.kdksname = #{kdksname} or #{kdksname} = '所有')
      </if>
      <if test="doctorname != null  and doctorname != ''">
        and a.doctorname = #{doctorname}
      </if>
      <if test="zcpc != null  and zcpc != ''">
        and b.zcpc like  concat("%",#{zcpc},"%")
      </if>
      <if test="adtFrom != null  and adtFrom != ''">and a.create_date >= #{adtFrom}</if>
      <if test="adtTo != null  and adtTo != ''">and a.create_date &lt; #{adtTo}</if>
    </where>
    
    
  </select>

  <select id="initYbgkWgscxmXmbm">
    call usp_get_zcxmwgjl()
  </select>

  <sql id="selectYbgkWgscxmVo">
    select xmmc,zcpc,fydj
    from ybgk_wgscxm
  </sql>

  <select id="selectYbgkWgscxmList" parameterType="YbgkWgscxm" resultMap="YbgkWgscxmResult">
    <include refid="selectYbgkWgscxmVo"/>
    <where>
      <if test="xmmc != null  and xmmc != ''">and xmmc = #{xmmc}</if>
    </where>
  </select>

  <select id="selectYbgkWgscxmByXmmc" parameterType="String" resultMap="YbgkWgscxmResult">
    <include refid="selectYbgkWgscxmVo"/>
    where xmmc = #{xmmc}
  </select>

  <insert id="insertYbgkWgscxm" parameterType="YbgkWgscxm">
    insert into ybgk_wgscxm
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="xmmc != null">xmmc,</if>
      <if test="zcpc != null">zcpc</if>
      <if test="fydj != null">fydj,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="xmmc != null">#{xmmc},</if>
       <if test="zcpc != null">#{zcpc},</if>
        <if test="fydj != null">#{fydj},</if>
    </trim>
  </insert>

  <update id="updateYbgkWgscxm" parameterType="YbgkWgscxm">
    update ybgk_wgscxm
    <trim prefix="SET" suffixOverrides=",">
    </trim>
    where xmmc = #{xmmc}
  </update>

  <delete id="deleteYbgkWgscxmByXmmc" parameterType="String">
    delete
    from ybgk_wgscxm
    where xmmc = #{xmmc}
  </delete>

  <delete id="deleteYbgkWgscxmAll" >
    delete
    from ybgk_wgscxm
  </delete>

  <delete id="deleteYbgkWgscxmByXmmcs" parameterType="String">
    delete from ybgk_wgscxm where xmmc in
    <foreach item="xmmc" collection="array" open="(" separator="," close=")">
      #{xmmc}
    </foreach>
  </delete>
</mapper>
