<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbjkShxmMapper">
    
    <resultMap type="YbjkShxm" id="YbjkShxmResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="jzh"    column="jzh"    />
        <result property="brname"    column="brname"    />
        <result property="sex"    column="sex"    />
        <result property="age"    column="age"    />
        <result property="ybh"    column="ybh"    />
        <result property="zyh"    column="zyh"    />
        <result property="bed"    column="bed"    />
        <result property="tel"    column="tel"    />
        <result property="ksname"    column="ksname"    />
        <result property="doctorname"    column="doctorname"    />
        <result property="zdqk"    column="zdqk"    />
        <result property="sqly"    column="sqly"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="xmsl"    column="xmsl"    />
        <result property="xmje"    column="xmje"    />
        <result property="fymid"    column="fymid"    />
        <result property="sqrq"    column="sqrq"    />
        <result property="kzrname"    column="kzrname"    />
        <result property="kzrtel"    column="kzrtel"    />
        <result property="kzrshdate"    column="kzrshdate"    />
        <result property="clsyksmc"    column="clsyksmc"    />
        <result property="clsykskzr"    column="clsykskzr"    />
        <result property="clsykskzrtel"    column="clsykskzrtel"    />
        <result property="ybkyj"    column="ybkyj"    />
        <result property="shzt"    column="shzt"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="sfId"    column="sf_id"    />
    </resultMap>

    <sql id="selectYbjkShxmVo">
        select id, type, jzh, brname, sex, age, ybh, zyh, bed, tel, ksname, doctorname, zdqk, sqly, xmmc, xmsl, xmje, fymid, sqrq, kzrname, kzrtel, kzrshdate, clsyksmc, clsykskzr, clsykskzrtel, ybkyj, shzt, create_by, create_date, update_by, update_date, remarks, del_flag, sf_id from ybjk_shxm
    </sql>

    <select id="selectShxmCount" resultMap="YbjkShxmResult">
        select count(*) as xmsl,max(id) as fymid
            from
                ybjk_shxm
                where (brname=#{brname}  or remarks=#{jzh})
                and fymid=#{fymid}
                and shzt='4'
                and sqrq>#{sqrq}
                and doctorname=#{doctorname}
    </select>

    <select id="selectYbjkShxmList" parameterType="YbjkShxm" resultMap="YbjkShxmResult">
        <include refid="selectYbjkShxmVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="brname != null  and brname != ''"> and brname like concat('%', #{brname}, '%')</if>
            <if test="zyh != null  and zyh != ''"> and zyh = #{zyh}</if>
            <if test="ksname != null  and ksname != ''"> and ksname like concat('%', #{ksname}, '%')</if>
            <if test="doctorname != null  and doctorname != ''"> and doctorname like concat('%', #{doctorname}, '%')</if>
            <if test="params.beginSqrq != null and params.beginSqrq != '' and params.endSqrq != null and params.endSqrq != ''"> and sqrq between #{params.beginSqrq} and #{params.endSqrq}</if>
        </where>
        order by sqrq desc
    </select>
    
    <select id="selectYbjkShxmById" parameterType="String" resultMap="YbjkShxmResult">
        <include refid="selectYbjkShxmVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertYbjkShxm" parameterType="YbjkShxm">
        insert into ybjk_shxm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="jzh != null and jzh != ''">jzh,</if>
            <if test="brname != null and brname != ''">brname,</if>
            <if test="sex != null and sex != ''">sex,</if>
            <if test="age != null and age != ''">age,</if>
            <if test="ybh != null and ybh != ''">ybh,</if>
            <if test="zyh != null">zyh,</if>
            <if test="bed != null">bed,</if>
            <if test="tel != null">tel,</if>
            <if test="ksname != null">ksname,</if>
            <if test="doctorname != null">doctorname,</if>
            <if test="zdqk != null and zdqk != ''">zdqk,</if>
            <if test="sqly != null">sqly,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="xmsl != null">xmsl,</if>
            <if test="xmje != null">xmje,</if>
            <if test="fymid != null">fymid,</if>
            <if test="sqrq != null">sqrq,</if>
            <if test="kzrname != null">kzrname,</if>
            <if test="kzrtel != null">kzrtel,</if>
            <if test="kzrshdate != null">kzrshdate,</if>
            <if test="clsyksmc != null">clsyksmc,</if>
            <if test="clsykskzr != null">clsykskzr,</if>
            <if test="clsykskzrtel != null">clsykskzrtel,</if>
            <if test="ybkyj != null">ybkyj,</if>
            <if test="shzt != null">shzt,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="sfId != null">sf_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="jzh != null and jzh != ''">#{jzh},</if>
            <if test="brname != null and brname != ''">#{brname},</if>
            <if test="sex != null and sex != ''">#{sex},</if>
            <if test="age != null and age != ''">#{age},</if>
            <if test="ybh != null and ybh != ''">#{ybh},</if>
            <if test="zyh != null">#{zyh},</if>
            <if test="bed != null">#{bed},</if>
            <if test="tel != null">#{tel},</if>
            <if test="ksname != null">#{ksname},</if>
            <if test="doctorname != null">#{doctorname},</if>
            <if test="zdqk != null and zdqk != ''">#{zdqk},</if>
            <if test="sqly != null">#{sqly},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="xmsl != null">#{xmsl},</if>
            <if test="xmje != null">#{xmje},</if>
            <if test="fymid != null">#{fymid},</if>
            <if test="sqrq != null">#{sqrq},</if>
            <if test="kzrname != null">#{kzrname},</if>
            <if test="kzrtel != null">#{kzrtel},</if>
            <if test="kzrshdate != null">#{kzrshdate},</if>
            <if test="clsyksmc != null">#{clsyksmc},</if>
            <if test="clsykskzr != null">#{clsykskzr},</if>
            <if test="clsykskzrtel != null">#{clsykskzrtel},</if>
            <if test="ybkyj != null">#{ybkyj},</if>
            <if test="shzt != null">#{shzt},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="sfId != null">#{sfId},</if>
         </trim>
    </insert>

    <update id="updateYbjkShxm" parameterType="YbjkShxm">
        update ybjk_shxm
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="jzh != null and jzh != ''">jzh = #{jzh},</if>
            <if test="brname != null and brname != ''">brname = #{brname},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="age != null and age != ''">age = #{age},</if>
            <if test="ybh != null and ybh != ''">ybh = #{ybh},</if>
            <if test="zyh != null">zyh = #{zyh},</if>
            <if test="bed != null">bed = #{bed},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="ksname != null">ksname = #{ksname},</if>
            <if test="doctorname != null">doctorname = #{doctorname},</if>
            <if test="zdqk != null and zdqk != ''">zdqk = #{zdqk},</if>
            <if test="sqly != null">sqly = #{sqly},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="xmsl != null">xmsl = #{xmsl},</if>
            <if test="xmje != null">xmje = #{xmje},</if>
            <if test="fymid != null">fymid = #{fymid},</if>
            <if test="sqrq != null">sqrq = #{sqrq},</if>
            <if test="kzrname != null">kzrname = #{kzrname},</if>
            <if test="kzrtel != null">kzrtel = #{kzrtel},</if>
            <if test="kzrshdate != null">kzrshdate = #{kzrshdate},</if>
            <if test="clsyksmc != null">clsyksmc = #{clsyksmc},</if>
            <if test="clsykskzr != null">clsykskzr = #{clsykskzr},</if>
            <if test="clsykskzrtel != null">clsykskzrtel = #{clsykskzrtel},</if>
            <if test="ybkyj != null">ybkyj = #{ybkyj},</if>
            <if test="shzt != null">shzt = #{shzt},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="sfId != null">sf_id = #{sfId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbjkShxmById" parameterType="String">
        delete from ybjk_shxm where id = #{id}
    </delete>

    <delete id="deleteYbjkShxmByIds" parameterType="String">
        delete from ybjk_shxm where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>