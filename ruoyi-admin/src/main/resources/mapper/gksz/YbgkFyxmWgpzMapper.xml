<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkFyxmWgpzMapper">

    <resultMap type="YbgkFyxmWgpz" id="YbgkFyxmWgpzResult">
        <result property="id"    column="id"    />
        <result property="xmbm"    column="xmbm"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="type"    column="type"    />
        <result property="percentage"    column="percentage"    />
        <result property="isBind"    column="is_bind"    />
        <result property="bindXmbm"    column="bind_xmbm"    />
        <result property="bindXmmc"    column="bind_xmmc"    />
    </resultMap>

    <sql id="selectYbgkFyxmWgpzVo">
        select id, xmbm, xmmc, type, percentage, is_bind, bind_xmbm, bind_xmmc from ybgk_fyxm_wgpz
    </sql>

    <select id="selectYbgkFyxmWgpzList" parameterType="YbgkFyxmWgpz" resultMap="YbgkFyxmWgpzResult">
        <include refid="selectYbgkFyxmWgpzVo"/>
        <where>
            <if test="xmbm != null  and xmbm != ''"> and xmbm = #{xmbm}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc like concat('%',#{xmmc},'%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="percentage != null "> and percentage = #{percentage}</if>
            <if test="isBind != null "> and is_bind = #{isBind}</if>
            <if test="bindXmbm != null  and bindXmbm != ''"> and bind_xmbm = #{bindXmbm}</if>
            <if test="bindXmmc != null  and bindXmmc != ''"> and bind_xmmc like concat('%',#{bindXmmc},'%')</if>
        </where>
    </select>

    <select id="selectYbgkFyxmWgpzById" parameterType="Long" resultMap="YbgkFyxmWgpzResult">
        <include refid="selectYbgkFyxmWgpzVo"/>
        where id = #{id}
    </select>

    <insert id="insertYbgkFyxmWgpz" parameterType="YbgkFyxmWgpz" useGeneratedKeys="true" keyProperty="id">
        insert into ybgk_fyxm_wgpz
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xmbm != null and xmbm != ''">xmbm,</if>
            <if test="xmmc != null and xmmc != ''">xmmc,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="percentage != null">percentage,</if>
            <if test="isBind != null">is_bind,</if>
            <if test="bindXmbm != null">bind_xmbm,</if>
            <if test="bindXmmc != null">bind_xmmc,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xmbm != null and xmbm != ''">#{xmbm},</if>
            <if test="xmmc != null and xmmc != ''">#{xmmc},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="percentage != null">#{percentage},</if>
            <if test="isBind != null">#{isBind},</if>
            <if test="bindXmbm != null">#{bindXmbm},</if>
            <if test="bindXmmc != null">#{bindXmmc},</if>
         </trim>
    </insert>

    <update id="updateYbgkFyxmWgpz" parameterType="YbgkFyxmWgpz">
        update ybgk_fyxm_wgpz
        <trim prefix="SET" suffixOverrides=",">
            <if test="xmbm != null and xmbm != ''">xmbm = #{xmbm},</if>
            <if test="xmmc != null and xmmc != ''">xmmc = #{xmmc},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="percentage != null">percentage = #{percentage},</if>
            <if test="isBind != null">is_bind = #{isBind},</if>
            <if test="bindXmbm != null">bind_xmbm = #{bindXmbm},</if>
            <if test="bindXmmc != null">bind_xmmc = #{bindXmmc},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbgkFyxmWgpzById" parameterType="Long">
        delete from ybgk_fyxm_wgpz where id = #{id}
    </delete>

    <delete id="deleteYbgkFyxmWgpzByIds" parameterType="String">
        delete from ybgk_fyxm_wgpz where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
