<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkKkmxMapper">

  <resultMap type="YbgkKkmx" id="YbgkKkmxResult">
    <result property="id" column="id"/>
    <result property="jzhlsh" column="jzhlsh"/>
    <result property="ybqh" column="ybqh"/>
    <result property="jzje" column="jzje"/>
    <result property="yyjg" column="yyjg"/>
    <result property="jglx" column="jglx"/>
    <result property="jgdj" column="jgdj"/>
    <result property="brname" column="brname"/>
    <result property="sfz" column="sfz"/>
    <result property="sex" column="sex"/>
    <result property="yllx" column="yllx"/>
    <result property="rydate" column="rydate"/>
    <result property="cydate" column="cydate"/>
    <result property="jsdate" column="jsdate"/>
    <result property="cyzd" column="cyzd"/>
    <result property="deptname" column="deptname"/>
    <result property="djjd" column="djjd"/>
    <result property="clzt" column="clzt"/>
    <result property="sfkk" column="sfkk"/>
    <result property="xfdate" column="xfdate"/>
    <result property="ldzt" column="ldzt"/>
    <result property="ldname" column="ldname"/>
    <result property="wgxm" column="wgxm"/>
    <result property="wgnr" column="wgnr"/>
    <result property="wgje" column="wgje"/>
    <result property="ssje" column="ssje"/>
    <result property="ssly" column="ssly"/>
    <result property="kddoctor" column="kddoctor"/>
    <result property="fymxlsh" column="fymxlsh"/>
    <result property="cfdate" column="cfdate"/>
    <result property="wgsl" column="wgsl"/>
    <result property="yyxmbh" column="yyxmbh"/>
    <result property="yyxmmc" column="yyxmmc"/>
    <result property="createDate" column="create_date"/>
    <result property="remarks" column="remarks"/>
    <result property="kkFlag" column="kk_flag"/>
    <result property="appealContent" column="appeal_content"/>
    <result property="appealDate" column="appeal_date"/>
    <result property="appealer" column="appealer"/>
    <result property="appealStatus" column="appeal_status"/>
    <result property="isExpire" column="is_expire"/>
    <result property="appealerDept" column="appealer_dept"/>
    <result property="sendDate" column="send_date"/>
    <result property="sendStatus" column="send_status"/>
  </resultMap>

  <sql id="selectYbgkKkmxVo">
    select id,
           jzhlsh,
           ybqh,
           jzje,
           yyjg,
           jglx,
           jgdj,
           brname,
           sfz,
           sex,
           yllx,
           rydate,
           cydate,
           jsdate,
           cyzd,
           deptname,
           djjd,
           clzt,
           sfkk,
           xfdate,
           ldzt,
           ldname,
           wgxm,
           wgnr,
           wgje,
           ssje,
           ssly,
           kddoctor,
           fymxlsh,
           cfdate,
           wgsl,
           yyxmbh,
           yyxmmc,
           create_date,
           remarks,
           appealer,
           appeal_date,
           appeal_content,
           kk_flag,
           appeal_status,
           appealer_dept,
           send_date,
           send_status
    from ybgk_kkxx
  </sql>

  <select id="selectDeptList" resultType="String">
    select distinct deptname
    from ybgk_kkxx
  </select>

  <select id="selectDoctorListByDept" resultType="String" parameterType="String">
    select distinct kddoctor
    from ybgk_kkxx
    where deptname = #{deptname}
  </select>

  <select id="selectDoctorList" resultType="String">
    select distinct kddoctor
    from ybgk_kkxx
  </select>

  <select id="selectYbgkKkmxList1" parameterType="YbgkKkmx" resultMap="YbgkKkmxResult">
    select * from ybgk_kkxx
    <where>
      <if test="jzhlsh != null  and jzhlsh != ''">and jzhlsh = #{jzhlsh}</if>
      <if test="ybqh != null  and ybqh != ''">and ybqh = #{ybqh}</if>
      <if test="jzje != null ">and jzje = #{jzje}</if>
      <if test="yyjg != null  and yyjg != ''">and yyjg = #{yyjg}</if>
      <if test="jglx != null  and jglx != ''">and jglx = #{jglx}</if>
      <if test="jgdj != null  and jgdj != ''">and jgdj = #{jgdj}</if>
      <if test="brname != null  and brname != ''">and brname like concat('%', #{brname}, '%')</if>
      <if test="sfz != null  and sfz != ''">and sfz = #{sfz}</if>
      <if test="sex != null  and sex != ''">and sex = #{sex}</if>
      <if test="yllx != null  and yllx != ''">and yllx = #{yllx}</if>
      <if test="rydate != null  and rydate != ''">and rydate = #{rydate}</if>
      <if test="cydate != null  and cydate != ''">and cydate = #{cydate}</if>
      <if test="jsdate != null ">and jsdate = #{jsdate}</if>
      <if test="cyzd != null  and cyzd != ''">and cyzd = #{cyzd}</if>
      <if test="deptname != null  and deptname != ''">and deptname like concat('%', #{deptname}, '%')</if>
      <if test="djjd != null  and djjd != ''">and djjd = #{djjd}</if>
      <if test="clzt != null  and clzt != ''">and clzt = #{clzt}</if>
      <if test="sfkk != null  and sfkk != ''">and sfkk = #{sfkk}</if>
      <if test="xfdate != null  and xfdate != ''">and xfdate = #{xfdate}</if>
      <if test="ldzt != null  and ldzt != ''">and ldzt = #{ldzt}</if>
      <if test="ldname != null  and ldname != ''">and ldname like concat('%', #{ldname}, '%')</if>
      <if test="wgxm != null  and wgxm != ''">and wgxm = #{wgxm}</if>
      <if test="wgnr != null  and wgnr != ''">and wgnr = #{wgnr}</if>
      <if test="wgje != null ">and wgje = #{wgje}</if>
      <if test="ssje != null ">and ssje = #{ssje}</if>
      <if test="ssly != null  and ssly != ''">and ssly = #{ssly}</if>
      <if test="kddoctor != null  and kddoctor != ''">and kddoctor = #{kddoctor}</if>
      <if test="fymxlsh != null  and fymxlsh != ''">and fymxlsh = #{fymxlsh}</if>
      <if test="cfdate != null ">and cfdate = #{cfdate}</if>
      <if test="wgsl != null ">and wgsl = #{wgsl}</if>
      <if test="yyxmbh != null  and yyxmbh != ''">and yyxmbh = #{yyxmbh}</if>
      <if test="yyxmmc != null  and yyxmmc != ''">and yyxmmc = #{yyxmmc}</if>
    </where>
  </select>

  <select id="selectYbgkKkmxList" parameterType="YbgkKkmx" resultMap="YbgkKkmxResult">
    select id, jzhlsh, ybqh, jzje, yyjg, jglx, jgdj, brname, sfz, sex, yllx, rydate, cydate, jsdate, cyzd, deptname,
    djjd, clzt, sfkk, xfdate, ldzt, ldname, wgxm, wgnr, wgje, ssje, ssly, kddoctor, fymxlsh, cfdate, wgsl, yyxmbh,
    yyxmmc, create_date, remarks, appealer, appeal_date, appeal_content, kk_flag, appealer_dept, send_date, send_status,
    case when create_date >= #{limitDate} then '未过期' else '已过期' end as is_expire,
    case when appeal_status = '1' then '已申诉' else '未申诉' end as appeal_status
    from ybgk_kkxx
    <where>
      <if test="jzhlsh != null  and jzhlsh != ''">and jzhlsh = #{jzhlsh}</if>
      <if test="ybqh != null  and ybqh != ''">and ybqh = #{ybqh}</if>
      <if test="jzje != null ">and jzje = #{jzje}</if>
      <if test="yyjg != null  and yyjg != ''">and yyjg = #{yyjg}</if>
      <if test="jglx != null  and jglx != ''">and jglx = #{jglx}</if>
      <if test="jgdj != null  and jgdj != ''">and jgdj = #{jgdj}</if>
      <if test="brname != null  and brname != ''">and brname like concat('%', #{brname}, '%')</if>
      <if test="sfz != null  and sfz != ''">and sfz = #{sfz}</if>
      <if test="sex != null  and sex != ''">and sex = #{sex}</if>
      <if test="yllx != null  and yllx != ''">and yllx = #{yllx}</if>
      <if test="rydate != null  and rydate != ''">and rydate = #{rydate}</if>
      <if test="cydate != null  and cydate != ''">and cydate = #{cydate}</if>
      <if test="jsdate != null ">and jsdate = #{jsdate}</if>
      <if test="cyzd != null  and cyzd != ''">and cyzd = #{cyzd}</if>
      <if test="deptname != null  and deptname != ''">and deptname like concat('%', #{deptname}, '%')</if>
      <if test="djjd != null  and djjd != ''">and djjd = #{djjd}</if>
      <if test="clzt != null  and clzt != ''">and clzt = #{clzt}</if>
      <if test="sfkk != null  and sfkk != ''">and sfkk = #{sfkk}</if>
      <if test="xfdate != null  and xfdate != ''">and xfdate = #{xfdate}</if>
      <if test="ldzt != null  and ldzt != ''">and ldzt = #{ldzt}</if>
      <if test="ldname != null  and ldname != ''">and ldname like concat('%', #{ldname}, '%')</if>
      <if test="wgxm != null  and wgxm != ''">and wgxm = #{wgxm}</if>
      <if test="wgnr != null  and wgnr != ''">and wgnr = #{wgnr}</if>
      <if test="wgje != null ">and wgje = #{wgje}</if>
      <if test="ssje != null ">and ssje = #{ssje}</if>
      <if test="ssly != null  and ssly != ''">and ssly = #{ssly}</if>
      <if test="kddoctor != null  and kddoctor != ''">and kddoctor = #{kddoctor}</if>
      <if test="fymxlsh != null  and fymxlsh != ''">and fymxlsh = #{fymxlsh}</if>
      <if test="cfdate != null ">and cfdate = #{cfdate}</if>
      <if test="wgsl != null ">and wgsl = #{wgsl}</if>
      <if test="yyxmbh != null  and yyxmbh != ''">and yyxmbh = #{yyxmbh}</if>
      <if test="yyxmmc != null  and yyxmmc != ''">and yyxmmc = #{yyxmmc}</if>
      <if test="createDate != null ">and create_date = #{createDate}</if>
      <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
      <if test="appealStatus != null and appealStatus != ''">and appeal_status = #{appealStatus}</if>
      <if test="appealer != null  and appealer != ''">and appealer = #{appealer}</if>
      <if test="deptList != null and deptList.size() > 0">
        and appealer_dept in
        <foreach item="item" index="index" collection="deptList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="dateLimit == 'wgq'">
        AND create_date >= #{limitDate}
      </if>
      <if test="dateLimit == 'ygq'">0
        AND create_date &lt;= #{limitDate}
      </if>
      <if test="startDate != null">and create_date >= #{startDate}</if>
      <if test="endDate != null">and create_date &lt; #{endDate}</if>
      <if test="startDateSend != null">and send_date >= #{startDateSend}</if>
      <if test="endDateSend != null">and send_date &lt; #{endDateSend}</if>
      <if test="sendStatus != null">and send_status = #{sendStatus}</if>
    </where>
    order by is_expire,send_status desc
  </select>

  <update id="send" parameterType="YbgkKkmx">
    update ybgk_kkxx set send_status = '1',send_date = #{sendDate},appealer = #{appealer},appealer_dept =
    #{appealerDept}
    where id in
    <foreach item="item" index="index" collection="sendList" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <select id="selectYbgkKkmxOneDay" resultMap="YbgkKkmxResult">
    select appeal_content
    from ybgk_kkxx
    where appeal_content is not null
      and appeal_content != ''
      and (create_date >= DATE_SUB(CURDATE(), INTERVAL 1 DAY) or appeal_date >= DATE_SUB(CURDATE(), INTERVAL 1 DAY))
  </select>

  <update id="appeal" parameterType="YbgkKkmx">
    update ybgk_kkxx
    set appeal_date    = #{appealDate},
        appeal_content = #{appealContent},
        appeal_status  = '1'
    where id = #{id}
  </update>

  <select id="selectYbgkKkmxById" parameterType="Integer" resultMap="YbgkKkmxResult">
    <include refid="selectYbgkKkmxVo"/>
    where id = #{id}
  </select>

  <insert id="insertYbgkKkmx" parameterType="YbgkKkmx" useGeneratedKeys="true" keyProperty="id">
    insert into ybgk_kkxx
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jzhlsh != null">jzhlsh,</if>
      <if test="ybqh != null">ybqh,</if>
      <if test="jzje != null">jzje,</if>
      <if test="yyjg != null">yyjg,</if>
      <if test="jglx != null">jglx,</if>
      <if test="jgdj != null">jgdj,</if>
      <if test="brname != null">brname,</if>
      <if test="sfz != null">sfz,</if>
      <if test="sex != null">sex,</if>
      <if test="yllx != null">yllx,</if>
      <if test="rydate != null">rydate,</if>
      <if test="cydate != null">cydate,</if>
      <if test="jsdate != null">jsdate,</if>
      <if test="cyzd != null">cyzd,</if>
      <if test="deptname != null">deptname,</if>
      <if test="djjd != null">djjd,</if>
      <if test="clzt != null">clzt,</if>
      <if test="sfkk != null">sfkk,</if>
      <if test="xfdate != null">xfdate,</if>
      <if test="ldzt != null">ldzt,</if>
      <if test="ldname != null">ldname,</if>
      <if test="wgxm != null">wgxm,</if>
      <if test="wgnr != null">wgnr,</if>
      <if test="wgje != null">wgje,</if>
      <if test="ssje != null">ssje,</if>
      <if test="ssly != null">ssly,</if>
      <if test="kddoctor != null">kddoctor,</if>
      <if test="fymxlsh != null">fymxlsh,</if>
      <if test="cfdate != null">cfdate,</if>
      <if test="wgsl != null">wgsl,</if>
      <if test="yyxmbh != null">yyxmbh,</if>
      <if test="yyxmmc != null">yyxmmc,</if>
      <if test="createDate != null">create_date,</if>
      <if test="remarks != null">remarks,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jzhlsh != null">#{jzhlsh},</if>
      <if test="ybqh != null">#{ybqh},</if>
      <if test="jzje != null">#{jzje},</if>
      <if test="yyjg != null">#{yyjg},</if>
      <if test="jglx != null">#{jglx},</if>
      <if test="jgdj != null">#{jgdj},</if>
      <if test="brname != null">#{brname},</if>
      <if test="sfz != null">#{sfz},</if>
      <if test="sex != null">#{sex},</if>
      <if test="yllx != null">#{yllx},</if>
      <if test="rydate != null">#{rydate},</if>
      <if test="cydate != null">#{cydate},</if>
      <if test="jsdate != null">#{jsdate},</if>
      <if test="cyzd != null">#{cyzd},</if>
      <if test="deptname != null">#{deptname},</if>
      <if test="djjd != null">#{djjd},</if>
      <if test="clzt != null">#{clzt},</if>
      <if test="sfkk != null">#{sfkk},</if>
      <if test="xfdate != null">#{xfdate},</if>
      <if test="ldzt != null">#{ldzt},</if>
      <if test="ldname != null">#{ldname},</if>
      <if test="wgxm != null">#{wgxm},</if>
      <if test="wgnr != null">#{wgnr},</if>
      <if test="wgje != null">#{wgje},</if>
      <if test="ssje != null">#{ssje},</if>
      <if test="ssly != null">#{ssly},</if>
      <if test="kddoctor != null">#{kddoctor},</if>
      <if test="fymxlsh != null">#{fymxlsh},</if>
      <if test="cfdate != null">#{cfdate},</if>
      <if test="wgsl != null">#{wgsl},</if>
      <if test="yyxmbh != null">#{yyxmbh},</if>
      <if test="yyxmmc != null">#{yyxmmc},</if>
      <if test="createDate != null">#{createDate},</if>
      <if test="remarks != null">#{remarks},</if>
    </trim>
  </insert>

  <update id="updateYbgkKkmx" parameterType="YbgkKkmx">
    update ybgk_kkxx
    <trim prefix="SET" suffixOverrides=",">
      <if test="jzhlsh != null">jzhlsh = #{jzhlsh},</if>
      <if test="ybqh != null">ybqh = #{ybqh},</if>
      <if test="jzje != null">jzje = #{jzje},</if>
      <if test="yyjg != null">yyjg = #{yyjg},</if>
      <if test="jglx != null">jglx = #{jglx},</if>
      <if test="jgdj != null">jgdj = #{jgdj},</if>
      <if test="brname != null">brname = #{brname},</if>
      <if test="sfz != null">sfz = #{sfz},</if>
      <if test="sex != null">sex = #{sex},</if>
      <if test="yllx != null">yllx = #{yllx},</if>
      <if test="rydate != null">rydate = #{rydate},</if>
      <if test="cydate != null">cydate = #{cydate},</if>
      <if test="jsdate != null">jsdate = #{jsdate},</if>
      <if test="cyzd != null">cyzd = #{cyzd},</if>
      <if test="deptname != null">deptname = #{deptname},</if>
      <if test="djjd != null">djjd = #{djjd},</if>
      <if test="clzt != null">clzt = #{clzt},</if>
      <if test="sfkk != null">sfkk = #{sfkk},</if>
      <if test="xfdate != null">xfdate = #{xfdate},</if>
      <if test="ldzt != null">ldzt = #{ldzt},</if>
      <if test="ldname != null">ldname = #{ldname},</if>
      <if test="wgxm != null">wgxm = #{wgxm},</if>
      <if test="wgnr != null">wgnr = #{wgnr},</if>
      <if test="wgje != null">wgje = #{wgje},</if>
      <if test="ssje != null">ssje = #{ssje},</if>
      <if test="ssly != null">ssly = #{ssly},</if>
      <if test="kddoctor != null">kddoctor = #{kddoctor},</if>
      <if test="fymxlsh != null">fymxlsh = #{fymxlsh},</if>
      <if test="cfdate != null">cfdate = #{cfdate},</if>
      <if test="wgsl != null">wgsl = #{wgsl},</if>
      <if test="yyxmbh != null">yyxmbh = #{yyxmbh},</if>
      <if test="yyxmmc != null">yyxmmc = #{yyxmmc},</if>
      <if test="createDate != null">create_date = #{createDate},</if>
      <if test="remarks != null">remarks = #{remarks},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteYbgkKkmxById" parameterType="Integer">
    delete
    from ybgk_kkxx
    where id = #{id}
  </delete>

  <delete id="deleteYbgkKkmxByIds" parameterType="String">
    delete from ybgk_kkxx where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
