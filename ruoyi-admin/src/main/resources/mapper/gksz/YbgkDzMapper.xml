<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkDzMapper">

  <resultMap type="YbgkDz" id="YbgkDzResult">
    <result property="id" column="id"/>
    <result property="xmbm" column="xmbm"/>
    <result property="fymid" column="fymid"/>
    <result property="jx" column="jx"/>
    <result property="cd" column="cd"/>
    <result property="gg" column="gg"/>
    <result property="bz" column="bz"/>
    <result property="name" column="name"/>
    <result property="dw" column="dw"/>
    <result property="price" column="price"/>
    <result property="type" column="type"/>
    <result property="flag" column="flag"/>
    <result property="ybjx" column="ybjx"/>
    <result property="ybcd" column="ybcd"/>
    <result property="ybgg" column="ybgg"/>
    <result property="ybbz" column="ybbz"/>
    <result property="ybname" column="ybname"/>
    <result property="ybdw" column="ybdw"/>
    <result property="ybprice" column="ybprice"/>
    <result property="fydj" column="fydj"/>
    <result property="opdate" column="opdate"/>
    <result property="nccd" column="nccd"/>
    <result property="createBy" column="create_by"/>
    <result property="createDate" column="create_date"/>
    <result property="updateBy" column="update_by"/>
    <result property="updateDate" column="update_date"/>
    <result property="remarks" column="remarks"/>
    <result property="delFlag" column="del_flag"/>
    <result property="dxlbcode" column="dxlbcode"/>
    <result property="dxlbname" column="dxlbname"/>
    <result property="xxlbcode" column="xxlbcode"/>
    <result property="xxlbname" column="xxlbname"/>
    <result property="yycode" column="yycode"/>
    <result property="ybcode" column="ybcode"/>
    <result property="lbcode" column="lbcode"/>
    <result property="lbname" column="lbname"/>
    <result property="nameFlag" column="name_flag"/>
    <result property="ggFlag" column="gg_flag"/>
    <result property="cdFlag" column="cd_flag"/>
    <result property="gjxmdm" column="gjxmdm"/>
    <result property="ybxz" column="ybxz"/>
    <result property="orgcode" column="orgcode"/>
  </resultMap>

  <sql id="selectYbgkDzVo">
    select distinct xmbm,
                    ybname,
                    ybjx,
                    ybgg,
                    ybdw,
                    ybprice,
                    nccd,
                    type,
                    ybxz,fydj
    from ybgk_dz
  </sql>


  <select id="getProList" parameterType="String" resultMap="YbgkDzResult">
    select distinct xmbm,ybname from ybgk_dz
    where
      ybname like concat('%', #{value}, '%') or
      nccd like concat('%', #{value}, '%') or
      xmbm like concat('%', #{value}, '%')
    limit 50
  </select>

  <select id="selectYbgkDzAll" parameterType="YbgkDz" resultMap="YbgkDzResult">
    select * from ybgk_dz
    <where>
      <if test="ybname != null  and ybname != ''">and ybname like concat('%', #{ybname}, '%')</if>
      <if test="nccd != null  and nccd != ''">and nccd like concat('%', #{nccd}, '%')</if>
      <if test="xmbm != null  and xmbm != ''">and xmbm = #{xmbm}</if>
      <if test="name != null  and name != ''">and name = #{name}</if>
      <if test="xmlb != null and xmlb != ''">
        and (
        (#{xmlb} = 'hc' and left(xmbm,1) = 'C') or
        (#{xmlb} = 'cy' and left(xmbm,1) = 'T') or
        (#{xmlb} = 'zl' and left(xmbm,1) in ('0','1','2','3','4','5','6','7','8','9')) or
        (#{xmlb} = 'yp' and left(xmbm,1) not in ('C', 'T', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'))
        )
      </if>
      <if test="btflag != null and btflag != ''">
        and (
        (#{btflag} = 'jg' and price != ybprice) or
        (#{btflag} = 'cd' and cd != ybcd) or
        (#{btflag} = 'gg' and gg != ybgg) or
        (#{btflag} = 'mc' and name != ybname)
        )
      </if>
    </where>
  </select>

  <select id="selectYbgkDzList" parameterType="YbgkDz" resultMap="YbgkDzResult">
    <include refid="selectYbgkDzVo"/>
    <where>
      <if test="ybname != null  and ybname != ''">and ybname like concat('%', #{ybname}, '%')</if>
      <if test="nccd != null  and nccd != ''">and nccd like concat('%', #{nccd}, '%')</if>
      <if test="xmbm != null  and xmbm != ''">and xmbm = #{xmbm}</if>
    </where>
    order by opdate desc
  </select>

  <select id="getYpInfoList" resultMap="YbgkDzResult">
    select id, xmbm, name, gg, cd
    from ybgk_dz
    where xmbm is not null
      and xmbm != ''
        and (ybsz_date is null or ybsz_date &lt;= DATE_SUB(CURDATE(), INTERVAL 10 DAY))
        and LEFT(xmbm,1) not in ('0','1','2','3','4','5','6','7','8','9')
  </select>

  <select id="selectYbgkDzById" parameterType="String" resultMap="YbgkDzResult">
    <include refid="selectYbgkDzVo"/>
    where id = #{id}
  </select>
  <select id="selectYbgkDzListForSync" resultType="com.ruoyi.system.domain.YbgkDz">
    select xmbm, fymid, name, id
    from ybgk_dz
  </select>

  <select id="selectXmmcByXmbm" parameterType="String" resultType="String">
    select xmmc from zlxm_total_name where xmbm = #{xmbm}
  </select>

  <insert id="syncDzxx" parameterType="java.util.List">
    insert into ybgk_dz
    (
    id,xmbm,fymid,jx,cd,gg,bz,name,dw,price,type,flag,ybjx,ybcd,ybgg,ybbz,ybname,ybdw,
    ybprice,fydj,opdate,nccd,create_by,create_date,update_by,update_date,
    remarks,del_flag,dxlbcode,dxlbname,xxlbcode,xxlbname,yycode,
    ybcode,lbcode,lbname,name_flag,gg_flag,cd_flag,gjxmdm,ybxz,orgcode,xzmc
    )
    values
    <foreach collection="list" separator="," item="item">
      (
      #{item.id},
      #{item.xmbm},
      #{item.fymid},
      #{item.jx},
      #{item.cd},
      #{item.gg},
      #{item.bz},
      #{item.name},
      #{item.dw},
      #{item.price},
      #{item.type},
      #{item.flag},
      #{item.ybjx},
      #{item.ybcd},
      #{item.ybgg},
      #{item.ybbz},
      #{item.ybname},
      #{item.ybdw},
      #{item.ybprice},
      #{item.fydj},
      #{item.opdate},
      #{item.nccd},
      #{item.createBy},
      #{item.createDate},
      #{item.updateBy},
      #{item.updateDate},
      #{item.remarks},
      #{item.delFlag},
      #{item.dxlbcode},
      #{item.dxlbname},
      #{item.xxlbcode},
      #{item.xxlbname},
      #{item.yycode},
      #{item.ybcode},
      #{item.lbcode},
      #{item.lbname},
      #{item.nameFlag},
      #{item.ggFlag},
      #{item.cdFlag},
      #{item.gjxmdm},
      #{item.ybxz},
      #{item.orgcode},
      #{item.xzmc}
      )
    </foreach>
  </insert>


  <insert id="insertYbgkDz" parameterType="YbgkDz">
    insert into ybgk_dz
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="xmbm != null and xmbm != ''">xmbm,</if>
      <if test="fymid != null and fymid != ''">fymid,</if>
      <if test="jx != null">jx,</if>
      <if test="cd != null">cd,</if>
      <if test="gg != null">gg,</if>
      <if test="bz != null">bz,</if>
      <if test="name != null">name,</if>
      <if test="dw != null">dw,</if>
      <if test="price != null">price,</if>
      <if test="type != null and type != ''">type,</if>
      <if test="flag != null">flag,</if>
      <if test="ybjx != null">ybjx,</if>
      <if test="ybcd != null">ybcd,</if>
      <if test="ybgg != null">ybgg,</if>
      <if test="ybbz != null">ybbz,</if>
      <if test="ybname != null">ybname,</if>
      <if test="ybdw != null">ybdw,</if>
      <if test="ybprice != null">ybprice,</if>
      <if test="fydj != null">fydj,</if>
      <if test="opdate != null">opdate,</if>
      <if test="nccd != null">nccd,</if>
      <if test="createBy != null">create_by,</if>
      <if test="createDate != null">create_date,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="updateDate != null">update_date,</if>
      <if test="remarks != null">remarks,</if>
      <if test="delFlag != null">del_flag,</if>
      <if test="dxlbcode != null">dxlbcode,</if>
      <if test="dxlbname != null">dxlbname,</if>
      <if test="xxlbcode != null">xxlbcode,</if>
      <if test="xxlbname != null">xxlbname,</if>
      <if test="yycode != null">yycode,</if>
      <if test="ybcode != null">ybcode,</if>
      <if test="lbcode != null">lbcode,</if>
      <if test="lbname != null">lbname,</if>
      <if test="nameFlag != null">name_flag,</if>
      <if test="ggFlag != null">gg_flag,</if>
      <if test="cdFlag != null">cd_flag,</if>
      <if test="gjxmdm != null">gjxmdm,</if>
      <if test="ybxz != null">ybxz,</if>
      <if test="orgcode != null">orgcode,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id},</if>
      <if test="xmbm != null and xmbm != ''">#{xmbm},</if>
      <if test="fymid != null and fymid != ''">#{fymid},</if>
      <if test="jx != null">#{jx},</if>
      <if test="cd != null">#{cd},</if>
      <if test="gg != null">#{gg},</if>
      <if test="bz != null">#{bz},</if>
      <if test="name != null">#{name},</if>
      <if test="dw != null">#{dw},</if>
      <if test="price != null">#{price},</if>
      <if test="type != null and type != ''">#{type},</if>
      <if test="flag != null">#{flag},</if>
      <if test="ybjx != null">#{ybjx},</if>
      <if test="ybcd != null">#{ybcd},</if>
      <if test="ybgg != null">#{ybgg},</if>
      <if test="ybbz != null">#{ybbz},</if>
      <if test="ybname != null">#{ybname},</if>
      <if test="ybdw != null">#{ybdw},</if>
      <if test="ybprice != null">#{ybprice},</if>
      <if test="fydj != null">#{fydj},</if>
      <if test="opdate != null">#{opdate},</if>
      <if test="nccd != null">#{nccd},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="createDate != null">#{createDate},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="updateDate != null">#{updateDate},</if>
      <if test="remarks != null">#{remarks},</if>
      <if test="delFlag != null">#{delFlag},</if>
      <if test="dxlbcode != null">#{dxlbcode},</if>
      <if test="dxlbname != null">#{dxlbname},</if>
      <if test="xxlbcode != null">#{xxlbcode},</if>
      <if test="xxlbname != null">#{xxlbname},</if>
      <if test="yycode != null">#{yycode},</if>
      <if test="ybcode != null">#{ybcode},</if>
      <if test="lbcode != null">#{lbcode},</if>
      <if test="lbname != null">#{lbname},</if>
      <if test="nameFlag != null">#{nameFlag},</if>
      <if test="ggFlag != null">#{ggFlag},</if>
      <if test="cdFlag != null">#{cdFlag},</if>
      <if test="gjxmdm != null">#{gjxmdm},</if>
      <if test="ybxz != null">#{ybxz},</if>
      <if test="orgcode != null">#{orgcode},</if>
    </trim>
  </insert>

  <update id="updateYbgkDz" parameterType="YbgkDz">
    update ybgk_dz
    <trim prefix="SET" suffixOverrides=",">
      <if test="xmbm != null and xmbm != ''">xmbm = #{xmbm},</if>
      <if test="fymid != null and fymid != ''">fymid = #{fymid},</if>
      <if test="jx != null">jx = #{jx},</if>
      <if test="cd != null">cd = #{cd},</if>
      <if test="gg != null">gg = #{gg},</if>
      <if test="bz != null">bz = #{bz},</if>
      <if test="name != null">name = #{name},</if>
      <if test="dw != null">dw = #{dw},</if>
      <if test="price != null">price = #{price},</if>
      <if test="type != null and type != ''">type = #{type},</if>
      <if test="flag != null">flag = #{flag},</if>
      <if test="ybjx != null">ybjx = #{ybjx},</if>
      <if test="ybcd != null">ybcd = #{ybcd},</if>
      <if test="ybgg != null">ybgg = #{ybgg},</if>
      <if test="ybbz != null">ybbz = #{ybbz},</if>
      <if test="ybname != null">ybname = #{ybname},</if>
      <if test="ybdw != null">ybdw = #{ybdw},</if>
      <if test="ybprice != null">ybprice = #{ybprice},</if>
      <if test="fydj != null">fydj = #{fydj},</if>
      <if test="opdate != null">opdate = #{opdate},</if>
      <if test="nccd != null">nccd = #{nccd},</if>
      <if test="createBy != null">create_by = #{createBy},</if>
      <if test="createDate != null">create_date = #{createDate},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="updateDate != null">update_date = #{updateDate},</if>
      <if test="remarks != null">remarks = #{remarks},</if>
      <if test="delFlag != null">del_flag = #{delFlag},</if>
      <if test="dxlbcode != null">dxlbcode = #{dxlbcode},</if>
      <if test="dxlbname != null">dxlbname = #{dxlbname},</if>
      <if test="xxlbcode != null">xxlbcode = #{xxlbcode},</if>
      <if test="xxlbname != null">xxlbname = #{xxlbname},</if>
      <if test="yycode != null">yycode = #{yycode},</if>
      <if test="ybcode != null">ybcode = #{ybcode},</if>
      <if test="lbcode != null">lbcode = #{lbcode},</if>
      <if test="lbname != null">lbname = #{lbname},</if>
      <if test="nameFlag != null">name_flag = #{nameFlag},</if>
      <if test="ggFlag != null">gg_flag = #{ggFlag},</if>
      <if test="cdFlag != null">cd_flag = #{cdFlag},</if>
      <if test="gjxmdm != null">gjxmdm = #{gjxmdm},</if>
      <if test="ybxz != null">ybxz = #{ybxz},</if>
      <if test="orgcode != null">orgcode = #{orgcode},</if>
    </trim>
    where id = #{id}
  </update>

  <update id="setYbProInfo" parameterType="YbgkDz">
    update ybgk_dz
    <trim prefix="SET" suffixOverrides=",">
      <if test="ybjx != null">ybjx = #{ybjx},</if>
      <if test="ybcd != null">ybcd = #{ybcd},</if>
      <if test="ybgg != null">ybgg = #{ybgg},</if>
      <if test="ybname != null">ybname = #{ybname},</if>
      <if test="ybdw != null">ybdw = #{ybdw},</if>
      <if test="ybprice != null">ybprice = #{ybprice},</if>
      <if test="fydj != null">fydj = #{fydj},</if>
      <if test="nameFlag != null">name_flag = #{nameFlag},</if>
      <if test="ggFlag != null">gg_flag = #{ggFlag},</if>
      <if test="cdFlag != null">cd_flag = #{cdFlag},</if>
      ybsz_date = NOW()
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteYbgkDzById" parameterType="String">
    delete
    from ybgk_dz
    where id = #{id}
  </delete>

  <delete id="deleteYbgkDzByIds" parameterType="String">
    delete from ybgk_dz where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <delete id="deleteYbgkDzByFymidAndXmbm" parameterType="java.util.List">
    delete from ybgk_dz where (fymid,xmbm) in
    <foreach collection="list" index="index" separator="," item="item" open="(" close=")">
      (#{item.fymid},#{item.xmbm})
    </foreach>
  </delete>
</mapper>
