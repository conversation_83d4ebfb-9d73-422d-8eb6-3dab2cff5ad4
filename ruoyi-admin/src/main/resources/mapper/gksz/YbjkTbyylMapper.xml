<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbjkTbyylMapper">
    
    <resultMap type="YbjkTbyyl" id="YbjkTbyylResult">
        <result property="id"    column="id"    />
        <result property="bzbh"    column="bzbh"    />
        <result property="bzmc"    column="bzmc"    />
        <result property="type"    column="type"    />
        <result property="xmlsh"    column="xmlsh"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="usecs"    column="usecs"    />
        <result property="usesl"    column="usesl"    />
        <result property="monthsl"    column="monthsl"    />
        <result property="yearsl"    column="yearsl"    />
        <result property="flag"    column="flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="remarks"    column="remarks"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectYbjkTbyylVo">
        select id, bzbh, bzmc, type, xmlsh, xmmc, usecs, usesl, monthsl, yearsl, flag, create_by, create_date, update_by, update_date, remarks, del_flag from ybjk_tbyyl
    </sql>

    <select id="selectYbjkTbyylList" parameterType="YbjkTbyyl" resultMap="YbjkTbyylResult">
        <include refid="selectYbjkTbyylVo"/>
        where bzbh = #{bzbh}
    </select>
    
    <select id="selectYbjkTbyylById" parameterType="Long" resultMap="YbjkTbyylResult">
        <include refid="selectYbjkTbyylVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertYbjkTbyyl" parameterType="YbjkTbyyl" useGeneratedKeys="true" keyProperty="id">
        insert into ybjk_tbyyl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bzbh != null">bzbh,</if>
            <if test="bzmc != null">bzmc,</if>
            <if test="type != null">type,</if>
            <if test="xmlsh != null">xmlsh,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="usecs != null">usecs,</if>
            <if test="usesl != null">usesl,</if>
            <if test="monthsl != null">monthsl,</if>
            <if test="yearsl != null">yearsl,</if>
            <if test="flag != null">flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="remarks != null">remarks,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bzbh != null">#{bzbh},</if>
            <if test="bzmc != null">#{bzmc},</if>
            <if test="type != null">#{type},</if>
            <if test="xmlsh != null">#{xmlsh},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="usecs != null">#{usecs},</if>
            <if test="usesl != null">#{usesl},</if>
            <if test="monthsl != null">#{monthsl},</if>
            <if test="yearsl != null">#{yearsl},</if>
            <if test="flag != null">#{flag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateYbjkTbyyl" parameterType="YbjkTbyyl">
        update ybjk_tbyyl
        <trim prefix="SET" suffixOverrides=",">
            <if test="bzbh != null">bzbh = #{bzbh},</if>
            <if test="bzmc != null">bzmc = #{bzmc},</if>
            <if test="type != null">type = #{type},</if>
            <if test="xmlsh != null">xmlsh = #{xmlsh},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="usecs != null">usecs = #{usecs},</if>
            <if test="usesl != null">usesl = #{usesl},</if>
            <if test="monthsl != null">monthsl = #{monthsl},</if>
            <if test="yearsl != null">yearsl = #{yearsl},</if>
            <if test="flag != null">flag = #{flag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteYbjkTbyylById" parameterType="Long">
        delete from ybjk_tbyyl where id = #{id}
    </delete>

    <delete id="deleteYbjkTbyylByIds" parameterType="String">
        delete from ybjk_tbyyl where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>