<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbjkXzxmTotalJmMapper">

    <resultMap type="YbjkXzxmTotalJm" id="YbjkXzxmTotalJmResult">
        <result property="xmbh"    column="xmbh"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="xyydj"    column="xyydj"    />
        <result property="xlx"    column="xlx"    />
        <result property="xzyszc"    column="xzyszc"    />
        <result property="dcylxz"    column="dcylxz"    />
        <result property="xzmz"    column="xzmz"    />
        <result property="ybbz"    column="ybbz"    />
        <result property="xzks"    column="xzks"    />
        <result property="rqzdyl"    column="rqzdyl"    />
        <result property="blgyxm"    column="blgyxm"    />
        <result property="zyzdyl"    column="zyzdyl"    />
        <result property="yzyfsy"    column="yzyfsy"    />
        <result property="syzdts"    column="syzdts"    />
        <result property="xnx"    column="xnx"    />
        <result property="xglyy"    column="xglyy"    />
        <result property="plxz"    column="plxz"    />
        <result property="xxmsyhjs"    column="xxmsyhjs"    />
        <result property="xzbz"    column="xzbz"    />
        <result property="yzetsy"    column="yzetsy"    />
        <result property="numdcyl"    column="numdcyl"    />
        <result property="xzxxlb"    column="xzxxlb"    />
        <result property="xzdxlb"    column="xzdxlb"    />
        <result property="yzlrsy"    column="yzlrsy"    />
        <result property="jjz"    column="jjz"    />
        <result property="xzlcbx"    column="xzlcbx"    />
        <result property="yzsyqt"    column="yzsyqt"    />
        <result property="xmtype"    column="xmtype"    />
        <result property="xzgs"    column="xzgs"    />
        <result property="xzsy"    column="xzsy"    />
        <result property="fydj"    column="fydj"    />
        <result property="updateDate"    column="update_date"    />
        <result property="shflag"    column="shflag"    />
        <result property="nccd"    column="nccd"    />
        <result property="sno"    column="sno"    />
        <result property="bz"    column="bz"    />
        <result property="syz"    column="syz"    />
        <result property="wjxzflag"    column="wjxzflag"    />
    </resultMap>

    <sql id="selectYbjkXzxmTotalJmVo">
        select xmbh, xmmc, xyydj, xlx, xzyszc, dcylxz, xzmz, ybbz, xzks, rqzdyl, blgyxm, zyzdyl, yzyfsy, syzdts, xnx, xglyy, plxz, xxmsyhjs, xzbz, yzetsy, numdcyl, xzxxlb, xzdxlb, yzlrsy, jjz, xzlcbx, yzsyqt, xmtype, xzgs, xzsy, fydj, update_date, shflag, nccd, sno, bz,syz, wjxzflag from ybjk_xzxm_total_jm
    </sql>

    <select id="selectYbjkXzxmTotalJmList" parameterType="YbjkXzxmTotalJm" resultMap="YbjkXzxmTotalJmResult">
        <include refid="selectYbjkXzxmTotalJmVo"/>
        <where>
            <if test="xmbh != null  and xmbh != ''"> and xmbh = #{xmbh}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc = #{xmmc}</if>
            <if test="xyydj != null  and xyydj != ''"> and xyydj = #{xyydj}</if>
            <if test="xlx != null  and xlx != ''"> and xlx = #{xlx}</if>
            <if test="xzyszc != null  and xzyszc != ''"> and xzyszc = #{xzyszc}</if>
            <if test="dcylxz != null  and dcylxz != ''"> and dcylxz = #{dcylxz}</if>
            <if test="xzmz != null  and xzmz != ''"> and xzmz = #{xzmz}</if>
            <if test="ybbz != null  and ybbz != ''"> and ybbz = #{ybbz}</if>
            <if test="xzks != null  and xzks != ''"> and xzks = #{xzks}</if>
            <if test="rqzdyl != null  and rqzdyl != ''"> and rqzdyl = #{rqzdyl}</if>
            <if test="blgyxm != null  and blgyxm != ''"> and blgyxm = #{blgyxm}</if>
            <if test="zyzdyl != null  and zyzdyl != ''"> and zyzdyl = #{zyzdyl}</if>
            <if test="yzyfsy != null  and yzyfsy != ''"> and yzyfsy = #{yzyfsy}</if>
            <if test="syzdts != null  and syzdts != ''"> and syzdts = #{syzdts}</if>
            <if test="xnx != null  and xnx != ''"> and xnx = #{xnx}</if>
            <if test="xglyy != null  and xglyy != ''"> and xglyy = #{xglyy}</if>
            <if test="plxz != null  and plxz != ''"> and plxz = #{plxz}</if>
            <if test="xxmsyhjs != null  and xxmsyhjs != ''"> and xxmsyhjs = #{xxmsyhjs}</if>
            <if test="xzbz != null  and xzbz != ''"> and xzbz = #{xzbz}</if>
            <if test="yzetsy != null  and yzetsy != ''"> and yzetsy = #{yzetsy}</if>
            <if test="numdcyl != null  and numdcyl != ''"> and numdcyl = #{numdcyl}</if>
            <if test="xzxxlb != null  and xzxxlb != ''"> and xzxxlb = #{xzxxlb}</if>
            <if test="xzdxlb != null  and xzdxlb != ''"> and xzdxlb = #{xzdxlb}</if>
            <if test="yzlrsy != null  and yzlrsy != ''"> and yzlrsy = #{yzlrsy}</if>
            <if test="jjz != null  and jjz != ''"> and jjz = #{jjz}</if>
            <if test="xzlcbx != null  and xzlcbx != ''"> and xzlcbx = #{xzlcbx}</if>
            <if test="yzsyqt != null  and yzsyqt != ''"> and yzsyqt = #{yzsyqt}</if>
            <if test="xmtype != null  and xmtype != ''"> and xmtype = #{xmtype}</if>
            <if test="xzgs != null  and xzgs != ''"> and xzgs = #{xzgs}</if>
            <if test="xzsy != null  and xzsy != ''"> and xzsy = #{xzsy}</if>
            <if test="fydj != null  and fydj != ''"> and fydj = #{fydj}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="shflag != null "> and shflag = #{shflag}</if>
            <if test="nccd != null  and nccd != ''"> and nccd = #{nccd}</if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
        </where>
    </select>

    <select id="selectYbjkXzxmTotalJmBySno" parameterType="String" resultMap="YbjkXzxmTotalJmResult">
        <include refid="selectYbjkXzxmTotalJmVo"/>
        where sno = #{sno}
    </select>
    <select id="encodeAllXzxm">
      call usp_get_encode_xm(#{pwd})
    </select>

    <insert id="insertYbjkXzxmTotalJm" parameterType="YbjkXzxmTotalJm" useGeneratedKeys="true" keyProperty="sno">
        insert into ybjk_xzxm_total_jm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="xmbh != null and xmbh != ''">xmbh,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="xyydj != null">xyydj,</if>
            <if test="xlx != null">xlx,</if>
            <if test="xzyszc != null">xzyszc,</if>
            <if test="dcylxz != null">dcylxz,</if>
            <if test="xzmz != null">xzmz,</if>
            <if test="ybbz != null">ybbz,</if>
            <if test="xzks != null">xzks,</if>
            <if test="rqzdyl != null">rqzdyl,</if>
            <if test="blgyxm != null">blgyxm,</if>
            <if test="zyzdyl != null">zyzdyl,</if>
            <if test="yzyfsy != null">yzyfsy,</if>
            <if test="syzdts != null">syzdts,</if>
            <if test="xnx != null">xnx,</if>
            <if test="xglyy != null">xglyy,</if>
            <if test="plxz != null">plxz,</if>
            <if test="xxmsyhjs != null">xxmsyhjs,</if>
            <if test="xzbz != null">xzbz,</if>
            <if test="yzetsy != null">yzetsy,</if>
            <if test="numdcyl != null">numdcyl,</if>
            <if test="xzxxlb != null">xzxxlb,</if>
            <if test="xzdxlb != null">xzdxlb,</if>
            <if test="yzlrsy != null">yzlrsy,</if>
            <if test="jjz != null">jjz,</if>
            <if test="xzlcbx != null">xzlcbx,</if>
            <if test="yzsyqt != null">yzsyqt,</if>
            <if test="xmtype != null">xmtype,</if>
            <if test="xzgs != null">xzgs,</if>
            <if test="xzsy != null">xzsy,</if>
            <if test="fydj != null">fydj,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="shflag != null">shflag,</if>
            <if test="nccd != null">nccd,</if>
            <if test="bz != null">bz,</if>
            <if test="syz != null">syz,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="xmbh != null and xmbh != ''">#{xmbh},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="xyydj != null">#{xyydj},</if>
            <if test="xlx != null">#{xlx},</if>
            <if test="xzyszc != null">#{xzyszc},</if>
            <if test="dcylxz != null">#{dcylxz},</if>
            <if test="xzmz != null">#{xzmz},</if>
            <if test="ybbz != null">#{ybbz},</if>
            <if test="xzks != null">#{xzks},</if>
            <if test="rqzdyl != null">#{rqzdyl},</if>
            <if test="blgyxm != null">#{blgyxm},</if>
            <if test="zyzdyl != null">#{zyzdyl},</if>
            <if test="yzyfsy != null">#{yzyfsy},</if>
            <if test="syzdts != null">#{syzdts},</if>
            <if test="xnx != null">#{xnx},</if>
            <if test="xglyy != null">#{xglyy},</if>
            <if test="plxz != null">#{plxz},</if>
            <if test="xxmsyhjs != null">#{xxmsyhjs},</if>
            <if test="xzbz != null">#{xzbz},</if>
            <if test="yzetsy != null">#{yzetsy},</if>
            <if test="numdcyl != null">#{numdcyl},</if>
            <if test="xzxxlb != null">#{xzxxlb},</if>
            <if test="xzdxlb != null">#{xzdxlb},</if>
            <if test="yzlrsy != null">#{yzlrsy},</if>
            <if test="jjz != null">#{jjz},</if>
            <if test="xzlcbx != null">#{xzlcbx},</if>
            <if test="yzsyqt != null">#{yzsyqt},</if>
            <if test="xmtype != null">#{xmtype},</if>
            <if test="xzgs != null">#{xzgs},</if>
            <if test="xzsy != null">#{xzsy},</if>
            <if test="fydj != null">#{fydj},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="shflag != null">#{shflag},</if>
            <if test="nccd != null">#{nccd},</if>
            <if test="bz != null">#{bz},</if>
            <if test="syz != null">#{syz},</if>
         </trim>
    </insert>
    <update id="decodeAndSaveJmData" parameterType="ybjkXzxmTotalJm">
      replace into ybjk_xzxm_total (xmbh, xmmc,
                                    xyydj, xlx, xzyszc, dcylxz, xzmz, ybbz,
                                    xzks, rqzdyl, blgyxm, zyzdyl, yzyfsy, syzdts,
                                    xnx, xglyy, plxz, xxmsyhjs, xzbz, yzetsy, numdcyl,
                                    xzxxlb, xzdxlb, yzlrsy, jjz, xzlcbx, yzsyqt, xmtype,
                                    xzgs, xzsy,
                                    fydj, update_date, shflag, nccd, sno, bz,syz, wjxzflag, xzage, gdjc)
          values(
                  #{jm.xmbh}, #{jm.xmmc},
                  aes_decrypt(unhex(#{jm.xyydj}), '${pwd}') , aes_decrypt(unhex(#{jm.xlx}), '${pwd}'), aes_decrypt(unhex(#{jm.xzyszc}), '${pwd}'), aes_decrypt(unhex(#{jm.dcylxz}), '${pwd}'), aes_decrypt(unhex(#{jm.xzmz}), '${pwd}'), aes_decrypt(unhex(#{jm.ybbz}), '${pwd}'),
                  aes_decrypt(unhex(#{jm.xzks}), '${pwd}'), aes_decrypt(unhex(#{jm.rqzdyl}), '${pwd}'), aes_decrypt(unhex(#{jm.blgyxm}), '${pwd}'), aes_decrypt(unhex(#{jm.zyzdyl}), '${pwd}'), aes_decrypt(unhex(#{jm.yzyfsy}), '${pwd}'), aes_decrypt(unhex(#{jm.syzdts}), '${pwd}'),
                  aes_decrypt(unhex(#{jm.xnx}), '${pwd}'), aes_decrypt(unhex(#{jm.xglyy}), '${pwd}'), aes_decrypt(unhex(#{jm.plxz}), '${pwd}'), aes_decrypt(unhex(#{jm.xxmsyhjs}), '${pwd}'), aes_decrypt(unhex(#{jm.xzbz}), '${pwd}'), aes_decrypt(unhex(#{jm.yzetsy}), '${pwd}'), aes_decrypt(unhex(#{jm.numdcyl}), '${pwd}'),
                  aes_decrypt(unhex(#{jm.xzxxlb}), '${pwd}'), aes_decrypt(unhex(#{jm.xzdxlb}), '${pwd}'), aes_decrypt(unhex(#{jm.yzlrsy}), '${pwd}'), aes_decrypt(unhex(#{jm.jjz}), '${pwd}'), aes_decrypt(unhex(#{jm.xzlcbx}), '${pwd}'), aes_decrypt(unhex(#{jm.yzsyqt}), '${pwd}'), aes_decrypt(unhex(#{jm.xmtype}), '${pwd}'),
                  xzgs, xzsy,
                  fydj, update_date, #{jm.shflag}, #{jm.nccd}, #{jm.sno}, #{jm.bz},#{jm.syz}, #{jm.wjxzflag}, #{jm.xzage}, #{jm.gdjc}
                )


    </update>

    <update id="updateYbjkXzxmTotalJm" parameterType="YbjkXzxmTotalJm">
        update ybjk_xzxm_total_jm
        <trim prefix="SET" suffixOverrides=",">
            <if test="xmbh != null and xmbh != ''">xmbh = #{xmbh},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="xyydj != null">xyydj = #{xyydj},</if>
            <if test="xlx != null">xlx = #{xlx},</if>
            <if test="xzyszc != null">xzyszc = #{xzyszc},</if>
            <if test="dcylxz != null">dcylxz = #{dcylxz},</if>
            <if test="xzmz != null">xzmz = #{xzmz},</if>
            <if test="ybbz != null">ybbz = #{ybbz},</if>
            <if test="xzks != null">xzks = #{xzks},</if>
            <if test="rqzdyl != null">rqzdyl = #{rqzdyl},</if>
            <if test="blgyxm != null">blgyxm = #{blgyxm},</if>
            <if test="zyzdyl != null">zyzdyl = #{zyzdyl},</if>
            <if test="yzyfsy != null">yzyfsy = #{yzyfsy},</if>
            <if test="syzdts != null">syzdts = #{syzdts},</if>
            <if test="xnx != null">xnx = #{xnx},</if>
            <if test="xglyy != null">xglyy = #{xglyy},</if>
            <if test="plxz != null">plxz = #{plxz},</if>
            <if test="xxmsyhjs != null">xxmsyhjs = #{xxmsyhjs},</if>
            <if test="xzbz != null">xzbz = #{xzbz},</if>
            <if test="yzetsy != null">yzetsy = #{yzetsy},</if>
            <if test="numdcyl != null">numdcyl = #{numdcyl},</if>
            <if test="xzxxlb != null">xzxxlb = #{xzxxlb},</if>
            <if test="xzdxlb != null">xzdxlb = #{xzdxlb},</if>
            <if test="yzlrsy != null">yzlrsy = #{yzlrsy},</if>
            <if test="jjz != null">jjz = #{jjz},</if>
            <if test="xzlcbx != null">xzlcbx = #{xzlcbx},</if>
            <if test="yzsyqt != null">yzsyqt = #{yzsyqt},</if>
            <if test="xmtype != null">xmtype = #{xmtype},</if>
            <if test="xzgs != null">xzgs = #{xzgs},</if>
            <if test="xzsy != null">xzsy = #{xzsy},</if>
            <if test="fydj != null">fydj = #{fydj},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="shflag != null">shflag = #{shflag},</if>
            <if test="nccd != null">nccd = #{nccd},</if>
            <if test="bz != null">bz = #{bz},</if>
             <if test="syz != null">syz = #{syz},</if>
        </trim>
        where sno = #{sno}
    </update>
    <select id="handleXzxmTotal">
        TRUNCATE table ybjk_xzxm_total
    </select>
    <select id="handleXzxm">
        call usp_update_xzxm()
    </select>
    <select id="countXzxmTotal" resultType="java.lang.Integer">
        select count(*) from ybjk_xzxm_total
    </select>

    <delete id="deleteYbjkXzxmTotalJmBySno" parameterType="String">
        delete from ybjk_xzxm_total_jm where sno = #{sno}
    </delete>

    <delete id="deleteYbjkXzxmTotalJmBySnos" parameterType="String">
        delete from ybjk_xzxm_total_jm where sno in
        <foreach item="sno" collection="array" open="(" separator="," close=")">
            #{sno}
        </foreach>
    </delete>
</mapper>
