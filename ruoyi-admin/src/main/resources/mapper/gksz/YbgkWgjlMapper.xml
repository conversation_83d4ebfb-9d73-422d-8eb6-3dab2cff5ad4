<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YbgkWgjlMapper">

  <resultMap type="YbgkWgjl" id="YbgkWgjlResult">
    <result property="id" column="id"/>
    <result property="jzh" column="jzh"/>
    <result property="jklog" column="jklog"/>
    <result property="xzlb" column="xzlb"/>
    <result property="brname" column="brname"/>
    <result property="brtype" column="brtype"/>
    <result property="kdksname" column="kdksname"/>
    <result property="jktype" column="jktype"/>
    <result property="doctorname" column="doctorname"/>
    <result property="fymName" column="fym_name"/>
    <result property="sl" column="sl"/>
    <result property="je" column="je"/>
    <result property="clFlag" column="cl_flag"/>
    <result property="clHfxx" column="cl_hfxx"/>
    <result property="createDate" column="create_date"/>
    <result property="zyh" column="zyh"/>
    <result property="ybbz" column="ybbz"/>
    <result property="tshOper" column="tsh_oper"/>
    <result property="ldecZsl" column="ldecZsl"/>
    <result property="ldtMaxdate" column="ldtMaxdate"/>
    <result property="zyzt" column="zyzt"/>
    <result property="bed" column="bed"/>
    <result property="fymId" column="fym_id"/>
    <result property="wjxzflag" column="wjxzflag"/>
    <result property="checkStatus" column="check_status"/>
    <result property="checkRemark" column="check_remark"/>
    <result property="wgtype" column="wgtype"/>
    <result property="wgyy" column="wgyy"/>
    <result property="jcjg" column="jcjg"/>
    <result property="score" column="score"/>
    <result property="sendDate" column="send_date"/>
     <result property="brid" column="brid"/>
     <result property="zyid" column="zyid"/>
     <result property="brbs" column="brbs"/>
     <result property="showcolor" column="showcolor"/>
     <result property="sfz" column="sfz"/>
     <result property="rydate" column="rydate"/>
     <result property="cydate" column="cydate"/>
     <result property="fydj" column="fydj"/>
      <result property="ybbx" column="ybbx"/>
      <result property="tc" column="tc"/>
      <result property="de" column="de"/>
      <result property="yljz" column="yljz"/>
      <result property="setl_id" column="setl_id"/>
      <result property="fymx" column="fymx"/>
      <result property="insutype" column="insutype"/>
  </resultMap>


  <resultMap type="YbgkWgjl" id="YbgkWgjlResult1">
    <result property="id" column="id"/>
    <result property="jkCode" column="jk_code"/>
    <result property="xzlb" column="xzlb"/>
    <result property="brtype" column="brtype"/>
    <result property="jzh" column="jzh"/>
    <result property="bed" column="bed"/>
    <result property="jktype" column="jktype"/>
    <result property="jklog" column="jklog"/>
    <result property="brname" column="brname"/>
    <result property="kdks" column="kdks"/>
    <result property="doctor" column="doctor"/>
    <result property="kdksname" column="kdksname"/>
    <result property="doctorname" column="doctorname"/>
    <result property="tshOper" column="tsh_oper"/>
    <result property="fyType" column="fy_type"/>
    <result property="fymId" column="fym_id"/>
    <result property="fymName" column="fym_name"/>
    <result property="sl" column="sl"/>
    <result property="je" column="je"/>
    <result property="bzmc" column="bzmc"/>
    <result property="pl" column="pl"/>
    <result property="jldw" column="jldw"/>
    <result property="zkyl" column="zkyl"/>
    <result property="kzdate" column="kzdate"/>
    <result property="clFlag" column="cl_flag"/>
    <result property="clHfxx" column="cl_hfxx"/>
    <result property="jylsh" column="jylsh"/>
    <result property="createBy" column="create_by"/>
    <result property="createDate" column="create_date"/>
    <result property="updateBy" column="update_by"/>
    <result property="updateDate" column="update_date"/>
    <result property="remarks" column="remarks"/>
    <result property="delFlag" column="del_flag"/>
    <result property="zyh" column="zyh"/>
    <result property="ybbz" column="ybbz"/>
    <result property="ldecZsl" column="ldecZsl"/>
    <result property="createDateStart" column="createDateStart"/>
    <result property="createDateEnd" column="createDateEnd"/>
    <result property="ldtMaxdate" column="ldtMaxdate"/>
    <result property="llNum" column="llNum"/>
    <result property="liHaveflag" column="liHaveflag"/>
    <result property="brid" column="brid"/>
    <result property="zyid" column="zyid"/>
    <result property="zyzt" column="zyzt"/>
    <result property="wjxzflag" column="wjxzflag"/>
    <result property="checkStatus" column="check_status"/>
     <result property="brid" column="brid"/>
     <result property="zyid" column="zyid"/>
     <result property="brbs" column="brbs"/>
     <result property="sfz" column="sfz"/>
     <result property="rydate" column="rydate"/>
     <result property="cydate" column="cydate"/>
     <result property="fydj" column="fydj"/>
      <result property="ybbx" column="ybbx"/>
      <result property="tc" column="tc"/>
      <result property="de" column="de"/>
      <result property="yljz" column="yljz"/>
      <result property="setl_id" column="setl_id"/>
      <result property="fymx" column="fymx"/>
      <result property="insutype" column="insutype"/>
  </resultMap>


  <resultMap type="YbgkWgjlGdjc" id="YbgkWgjlGdjcResult">
    <result property="jzh" column="jzh"/>
    <result property="jklog" column="jklog"/>
    <result property="brname" column="brname"/>
    <result property="kdksname" column="kdksname"/>
    <result property="doctorname" column="doctorname"/>
    <result property="fymId" column="fym_id"/>
    <result property="fymName" column="fym_name"/>
    <result property="sl" column="sl"/>
    <result property="je" column="je"/>
    <result property="ybbz" column="ybbz"/>
    <result property="cblb" column="cblb"/>
    <result property="zyh" column="zyh"/>
    <result property="fydj" column="fydj"/>
      <result property="sfz" column="sfz"/>
    <result property="rydate" column="rydate"/>
    <result property="cydate" column="cydate"/>
    <result property="jktype" column="jktype"/>
    <result property="price" column="price"/>
    <result property="zdxx" column="zdxx"/>
  </resultMap>

  <sql id="selectYbgkWgjlVo">
    select distinct fy_type,
                    id,
                    jzh,
                    xzlb,
                    brname,
                    brtype,
                    kdksname,
                    sl,
                    je,
                    doctorname,
                    jklog,
                    jktype,
                    fym_name,
                    fym_id,
                    cl_flag,
                    cl_hfxx,
                    create_date,
                    zyh,
                    ybbz,
                    tsh_oper,
                    zyzt,
                    bed,
                    wjxzflag,
                    jgid,brid,zyid,brbs,
                    showcolor,sfz,
	rydate,
	cydate,
	fydj,
	ybbx,
	tc,
	de,
	yljz,
	setl_id,fymx,insutype,zje
    from ybgk_wgjl
  </sql>

  <select id="selectYbgkWgjlGdjc" parameterType="YbgkWgjlGdjc" resultMap="YbgkWgjlGdjcResult">
    select
       case when a.brtype='1' then '门诊' else '住院' end as  brtype,a.jzh,a.jklog,a.brname,a.kdksname,a.doctorname,a.fym_id,a.fym_name,a.sl,a.je,a.ybbz,a.zyh,a.fydj,
      a.insutype as cblb,DATE_FORMAT(a.rydate, '%Y-%m-%d') as rydate,
      DATE_FORMAT(a.cydate, '%Y-%m-%d') as cydate,
      "过度检查" AS jktype,
      ROUND(a.je/a.sl) AS price,a.bzmc as zdxx,a.sfz,a.ybbx,a.tc,a.de,a.yljz,a.zje
       from ybgk_wgjl_gdjc a

    <where>
      <if test="zyh != null and zyh != ''">and a.zyh = #{zyh}</if>
      <if test="brname != null and brname != ''">and a.brname like concat('%', #{brname}, '%')</if>
      <if test="kdksname != null and kdksname != '' and kdksname != '所有'">and a.kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null and doctorname != ''">and a.doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="fymName != null and fymName != ''">and a.fym_name like concat('%', #{fymName}, '%')</if>
      <if test="startDate != null">and a.create_date >= #{startDate}</if>
      <if test="endDate != null">and a.create_date &lt;= #{endDate}</if>
    </where>
    UNION ALL
     select
       case when a.brtype='1' then '门诊' else '住院' end as  brtype,a.jzh,a.jklog,a.brname,a.kdksname,a.doctorname,a.fym_id,a.fym_name,a.sl,a.je,a.ybbz,a.zyh,a.fydj,
      a.insutype as cblb,DATE_FORMAT(a.rydate, '%Y-%m-%d') as rydate,
      DATE_FORMAT(a.cydate, '%Y-%m-%d') as cydate,
      "过度检查" AS jktype,
      ROUND(a.je/a.sl) AS price,a.bzmc as zdxx,a.sfz,a.ybbx,a.tc,a.de,a.yljz,a.zje
       from ybgk_wgjl a

    <where>
      <if test="zyh != null and zyh != ''">and a.zyh = #{zyh}</if>
      <if test="brname != null and brname != ''">and a.brname like concat('%', #{brname}, '%')</if>
      <if test="kdksname != null and kdksname != '' and kdksname != '所有'">and a.kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null and doctorname != ''">and a.doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="fymName != null and fymName != ''">and a.fym_name like concat('%', #{fymName}, '%')</if>
      <if test="startDate != null">and a.create_date >= #{startDate}</if>
      <if test="endDate != null">and a.create_date &lt;= #{endDate}</if>
      and jktype='gdjc'
    </where>

  </select>

  <update id="updateCheckStatus" parameterType="Long">
    update ybgk_wgjl_check_record set check_status = 1 where id = #{id}
  </update>

  <select id="selectCheckWgjl" parameterType="YbgkWgjl" resultMap="YbgkWgjlResult">
    select a.*,b.wgtype,b.wgyy,b.jcjg,b.score
    from ybgk_wgjl_check_record a left join ybgk_wgjl_check_result b on a.id = b.wgjl_id
    <where>
      <if test="jzh != null  and jzh != ''">and jzh = #{jzh}</if>
      <if test="zyh != null  and zyh != ''">and zyh = #{zyh}</if>
      <if test="brname != null  and brname != ''">and brname like concat('%', #{brname}, '%')</if>
      <if test="kdksname != null  and kdksname != ''">and kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null  and doctorname != ''">and doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="fymName != null  and fymName != ''">and fym_name like concat('%', #{fymName}, '%')</if>
      <if test="jktype != null  and jktype != ''">and jktype = #{jktype}</if>
      <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
      <if test="checkStatus != null">and check_status = #{checkStatus}</if>
      <if test="wjxzflag != null  and wjxzflag != ''">and wjxzflag = #{wjxzflag}</if>
      <if test="createDateStart != null">and create_date >= #{createDateStart}</if>
      <if test="createDateEnd != null">and create_date &lt;= #{createDateEnd}</if>
      <if test="checkStatus != null">and check_status = #{checkStatus}</if>
      <if test="sendDateStart != null ">and a.send_date >= #{sendDateStart}</if>
      <if test="sendDateEnd != null ">and a.send_date &lt; #{sendDateEnd}</if>
      <if test="jktypes != null and jktypes.length > 0">
        and jktype in
        <foreach item="item" index="index" collection="jktypes" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and (case when #{xzlb} = '#FFFFFF' then xzlb is null or xzlb = '' or xzlb = '#FFFFFF'
      when #{xzlb} is not null and #{xzlb} != '' then xzlb = #{xzlb}
      else 1 = 1 end)
      and check_doctor = #{checkDoctor}
      and check_dept in
      <foreach item="item" index="index" collection="checkDeptList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </where>
    order by id desc
  </select>


  <select id="selectAllYbglWgjl" parameterType="YbgkWgjl" resultMap="YbgkWgjlResult">
    select * from ybgk_wgjl
    <where>
      <if test="jkCode != null  and jkCode != ''">and jk_code = #{jkCode}</if>
      <if test="brtype != null  and brtype != ''">and brtype = #{brtype}</if>
      <if test="jzh != null  and jzh != ''">and jzh = #{jzh}</if>
      <if test="bed != null  and bed != ''">and bed = #{bed}</if>
      <if test="jktype != null  and jktype != ''">and jktype = #{jktype}</if>
      <if test="jklog != null  and jklog != ''">and jklog = #{jklog}</if>
      <if test="brname != null  and brname != ''">and brname = #{brname}</if>
      <if test="kdks != null  and kdks != ''">and kdks = #{kdks}</if>
      <if test="doctor != null  and doctor != ''">and doctor = #{doctor}</if>
      <if test="kdksname != null  and kdksname != ''">and kdksname = #{kdksname}</if>
      <if test="doctorname != null  and doctorname != ''">and doctorname = #{doctorname}</if>
      <if test="tshOper != null  and tshOper != ''">and tsh_oper = #{tshOper}</if>
      <if test="fyType != null  and fyType != ''">and fy_type = #{fyType}</if>
      <if test="fymId != null  and fymId != ''">and fym_id = #{fymId}</if>
      <if test="fymName != null  and fymName != ''">and fym_name = #{fymName}</if>
      <if test="sl != null ">and sl = #{sl}</if>
      <if test="je != null ">and je = #{je}</if>
      <if test="bzmc != null  and bzmc != ''">and bzmc = #{bzmc}</if>
      <if test="pl != null  and pl != ''">and pl = #{pl}</if>
      <if test="jldw != null  and jldw != ''">and jldw = #{jldw}</if>
      <if test="zkyl != null  and zkyl != ''">and zkyl = #{zkyl}</if>
      <if test="kzdate != null ">and kzdate = #{kzdate}</if>
      <if test="clFlag != null  and clFlag != ''">and cl_flag = #{clFlag}</if>
      <if test="clHfxx != null  and clHfxx != ''">and cl_hfxx = #{clHfxx}</if>
      <if test="jylsh != null  and jylsh != ''">and jylsh = #{jylsh}</if>
      <if test="createDate != null ">and create_date = #{createDate}</if>
      <if test="updateDate != null ">and update_date = #{updateDate}</if>
      <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
      <if test="zyh != null  and zyh != ''">and zyh = #{zyh}</if>
      <if test="ybbz != null  and ybbz != ''">and ybbz = #{ybbz}</if>
      <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
      <if test="wjxzflag != null  and wjxzflag != ''">and wjxzflag = #{wjxzflag}</if>
    </where>
  </select>

  <select id="selectBrJzhByDays" resultMap="YbgkWgjlResult" parameterType="Integer">
    SELECT DISTINCT jzh
    FROM ybgk_wgjl
    WHERE create_date &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
      AND jzh IS NOT NULL
      AND brtype = '2'
  </select>

  <select id="selectYbgkWgjlList1" parameterType="YbgkWgjl" resultMap="YbgkWgjlResult1">
    select bed, jklog, brname,
    kdksname, doctorname, tsh_oper, sl, je, bzmc,
    create_date, zyh, ybbz from ybgk_wgjl
    <where>
      <if test="jkCode != null  and jkCode != ''">and jk_code = #{jkCode}</if>
      <if test="brtype != null  and brtype != ''">and brtype = #{brtype}</if>
      <if test="jzh != null  and jzh != ''">and jzh = #{jzh}</if>
      <if test="bed != null  and bed != ''">and bed = #{bed}</if>
      <if test="jktype != null  and jktype != ''">and jktype = #{jktype}</if>
      <if test="jklog != null  and jklog != ''">and jklog = #{jklog}</if>
      <if test="brname != null  and brname != ''">and brname like concat('%', #{brname}, '%')</if>
      <if test="kdks != null  and kdks != ''">and kdks = #{kdks}</if>
      <if test="doctor != null  and doctor != ''">and doctor = #{doctor}</if>
      <if test="kdksname != null  and kdksname != ''">and kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null  and doctorname != ''">and doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="tshOper != null  and tshOper != ''">and tsh_oper = #{tshOper}</if>
      <if test="fyType != null  and fyType != ''">and fy_type = #{fyType}</if>
      <if test="fymId != null  and fymId != ''">and fym_id = #{fymId}</if>
      <if test="fymName != null  and fymName != ''">and fym_name like concat('%', #{fymName}, '%')</if>
      <if test="sl != null ">and sl = #{sl}</if>
      <if test="je != null ">and je = #{je}</if>
      <if test="bzmc != null  and bzmc != ''">and bzmc = #{bzmc}</if>
      <if test="pl != null  and pl != ''">and pl = #{pl}</if>
      <if test="jldw != null  and jldw != ''">and jldw = #{jldw}</if>
      <if test="zkyl != null  and zkyl != ''">and zkyl = #{zkyl}</if>
      <if test="kzdate != null ">and kzdate = #{kzdate}</if>
      <if test="clFlag != null  and clFlag != ''">and cl_flag = #{clFlag}</if>
      <if test="clHfxx != null  and clHfxx != ''">and cl_hfxx = #{clHfxx}</if>
      <if test="jylsh != null  and jylsh != ''">and jylsh = #{jylsh}</if>
      <if test="createDate != null ">and create_date = #{createDate}</if>
      <if test="updateDate != null ">and update_date = #{updateDate}</if>
      <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
      <if test="zyh != null  and zyh != ''">and zyh = #{zyh}</if>
      <if test="ybbz != null  and ybbz != ''">and ybbz = #{ybbz}</if>
      <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
      <if test="wjxzflag != null  and wjxzflag != ''">and wjxzflag = #{wjxzflag}</if>
      and create_date>DATE_SUB(CURRENT_DATE(), INTERVAL 100 DAY)
    </where>
    order by create_date desc
    LIMIT 20
  </select>


  <select id="selectYbgkWgjlListByHospital" parameterType="YbgkWgjl" resultMap="YbgkWgjlResult">
    <include refid="selectYbgkWgjlVo"/>
    <where>
      <if test="jzh != null  and jzh != ''">and jzh = #{jzh}</if>
      <if test="zyh != null  and zyh != ''">and zyh = #{zyh}</if>
      <if test="brname != null  and brname != ''">and brname like concat('%', #{brname}, '%')</if>
      <if test="jklog != null  and jklog != ''">and jklog = #{jklog}</if>
      <if test="kdksname != null  and kdksname != ''">and kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null  and doctorname != ''">and doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="fymName != null  and fymName != ''">and fym_name like concat('%', #{fymName}, '%')</if>
      <if test="jktype != null  and jktype != ''">and jktype = #{jktype}</if>
      <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
      <if test="wjxzflag != null  and wjxzflag != ''">and wjxzflag = #{wjxzflag}</if>
      <if test="createDateStart != null  and createDateStart != ''and createDateEnd != null  and createDateEnd != ''">
        and create_date between #{createDateStart} and #{createDateEnd}
      </if>
      <if test="jktypes != null and jktypes.length > 0">
        and jktype in
        <foreach item="item" index="index" collection="jktypes" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and brtype = 2
      and (case when #{xzlb} = '#FFFFFF' then xzlb is null or xzlb = '' or xzlb = '#FFFFFF'
      when #{xzlb} is not null and #{xzlb} != '' then xzlb = #{xzlb}
      else 1 = 1 end)
    </where>
    order by create_date desc
  </select>

  <select id="selectYbgkWgjlListByOutpatient" parameterType="YbgkWgjl" resultMap="YbgkWgjlResult">
    <include refid="selectYbgkWgjlVo"/>
    <where>
      <if test="jzh != null  and jzh != ''">and jzh = #{jzh}</if>
      <if test="zyh != null  and zyh != ''">and zyh = #{zyh}</if>
      <if test="brname != null  and brname != ''">and brname like concat('%', #{brname}, '%')</if>
      <if test="jklog != null  and jklog != ''">and jklog = #{jklog}</if>
      <if test="kdksname != null  and kdksname != ''">and kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null  and doctorname != ''">and doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="jktype != null  and jktype != ''">and jktype = #{jktype}</if>
      <if test="fymName != null  and fymName != ''">and fym_name like concat('%', #{fymName}, '%')</if>
      <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
      <if test="wjxzflag != null  and wjxzflag != ''">and wjxzflag = #{wjxzflag}</if>
      <if test="createDateStart != null  and createDateStart != '' and createDateEnd != null  and createDateEnd != ''">
        and create_date between#{createDateStart} and #{createDateEnd}
      </if>
      <if test="jktypes != null and jktypes.length > 0">
        and jktype in
        <foreach item="item" index="index" collection="jktypes" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and brtype = 1
      and (case when #{xzlb} = '#FFFFFF' then xzlb is null or xzlb = '' or xzlb = '#FFFFFF'
      when #{xzlb} is not null and #{xzlb} != '' then xzlb = #{xzlb}
      else 1 = 1 end)
    </where>
    order by create_date desc
  </select>


  <select id="selectYbgkWgjlListByDoctor" parameterType="YbgkWgjl" resultMap="YbgkWgjlResult1">
    select distinct fy_type,id, jzh, brname,brtype, kdksname,ROUND(ifNull(sl,0),2) as sl,ROUND(ifnull(je,0),2) as je,
    doctorname, jklog,jktype,fym_name, fym_id, cl_flag, cl_hfxx, create_date, zyh, ybbz,tsh_oper, zyzt, bed, xzlb, wjxzflag from ybgk_wgjl
    <where>
      brtype = '2'
      <if test="kdksname != null  and kdksname != ''">and kdksname = #{kdksname}</if>
      <if test="doctorname != null  and doctorname != ''"> and  doctorname = #{doctorname}</if>
      <if test="kdks != null  and kdks != ''">and kdks = #{kdks}</if>
      <if test="doctor != null  and doctor != ''"> and  doctor = #{doctor}</if>
      <if test="zyzt != null and zyzt != ''">and zyzt = #{zyzt}</if>
      <if test="jgid != null and jgid != ''">and jgid = #{jgid}</if>
      <if test="wjxzflag != null  and wjxzflag != ''">and wjxzflag = #{wjxzflag}</if>
      <if test="createDateStart != null  and createDateStart != ''and createDateEnd != null  and createDateEnd != ''">
        and create_date &gt; #{createDateStart} and create_date &lt; #{createDateEnd}
      </if>
    </where>
    order by create_date desc limit 300
  </select>


  <select id="selectUniqueWgjlByHospital" parameterType="YbgkWgjl" resultMap="YbgkWgjlResult">
    SELECT DISTINCT
    MAX(jzh) as jzh,MAX(brname) as brname,MAX(jklog) as jklog,MAX(jktype) as jktype,MAX(fym_name) as fym_name,MAX(zyzt)
    as zyzt
    FROM
    ybgk_wgjl
    <where>
      <if test="jzh != null  and jzh != ''">and jzh = #{jzh}</if>
      <if test="zyh != null  and zyh != ''">and zyh = #{zyh}</if>
      <if test="brname != null  and brname != ''">and brname like concat('%', #{brname}, '%')</if>
      <if test="jklog != null  and jklog != ''">and jklog = #{jklog}</if>
      <if test="kdksname != null  and kdksname != ''">and kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null  and doctorname != ''">and doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="fymName != null  and fymName != ''">and fym_name like concat('%', #{fymName}, '%')</if>
      <if test="jktype != null  and jktype != ''">and jktype = #{jktype}</if>
      <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
      <if test="wjxzflag != null  and wjxzflag != ''">and wjxzflag = #{wjxzflag}</if>
      <if test="createDateStart != null  and createDateStart != ''and createDateEnd != null  and createDateEnd != ''">
        and create_date between #{createDateStart} and #{createDateEnd}
      </if>
      and brtype = 2
    </where>
    group by jklog,jzh
    order by create_date desc
  </select>

  <select id="selectYbgkWgjlys" resultMap="YbgkWgjlResult" parameterType="YbgkWgjl">
    select doctorname from ybgk_wgjl
    <where>
      brtype = '2'
      <if test="kdksname != null  and kdksname != ''">and kdksname = #{kdksname}</if>
      <if test="brtype != null  and brtype != ''">and brtype = #{brtype}</if>
      and doctorname is not null
      AND create_date &gt; DATE_SUB(NOW(), INTERVAL 1 MONTH)
    </where>
    group by doctorname
  </select>

  <select id="insertblzk" statementType="CALLABLE" parameterType="String">
    call usp_bl_date_zk(#{jzh,mode=IN})
  </select>

  <select id="selectYbgkWgjlks" resultMap="YbgkWgjlResult" parameterType="YbgkWgjl">
    select kdksname from ybgk_wgjl
    <where>
      <if test="brtype != null  and brtype != ''">and brtype = #{brtype}</if>
      and kdksname is not null
      AND create_date &gt; DATE_SUB(NOW(), INTERVAL 1 MONTH)
    </where>
    group by kdksname
  </select>

  <select id="selectFyxxSl" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjl">
    SELECT ROUND(IFNULL(SUM(sl), 0), 2) as ldec_zsl
    FROM fyxx
    WHERE jzh = #{jzh}
      AND (fydate between #{createDateStart} AND #{createDateEnd})
      AND xmbm = #{bzmc};
  </select>

  <select id="selectSysDate" resultType="Date">
    select sysdate()
    from p_company
    where c_id = "Y"
  </select>

  <select id="selectZyslxz" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjl">
    select ifnull(fn_zyslxz(#{jzh}, #{bzmc}, 0), 0) as ldecZsl
    from p_company;
  </select>

  <select id="selectRqslxzZl" resultType="String" parameterType="YbgkWgjl">
    select ifnull(fn_rqslxz_zl(#{jzh}, #{fymId}, #{sl}, #{createDate}, ''), '') as jklog
    from p_company;
  </select>


  <select id="selectRqslxz" resultType="java.math.BigDecimal" parameterType="YbgkWgjl">
    select ifnull(fn_rqslxz(#{jzh}, #{fymId}, 0, #{createDate}, ''), 0) as ldec_zsl
    from p_company;
  </select>

  <select id="selectFyxxSl1" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjl">
    select count(*) as liHaveflag
    from fyxx
    where jzh = #{jzh}
      and xmmc like #{bzmc};
  </select>

  <select id="selectBlgyxm" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjl">
    select ifnull(fn_blgyxm(#{jzh}, #{bzmc}, "1", #{createDate}, #{fymId}), 0) as ldecZsl
    from p_company;
  </select>


  <select id="selectKcSl" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjl">
    SELECT count(*) as liHaveflag
    FROM kc22,
         kc21
    WHERE akc190 = #{jzh}
      and akc223 like #{bzmc};
  </select>

  <select id="selectZyzdsyts" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjl">
    SELECT COUNT( DISTINCT DATE_FORMAT(fydate, '%Y-%m-%d')) as llNum ,min(fydate) as  kzdate
     FROM fyxx WHERE jzh= #{jzh}   AND xmbm=#{fymId}
  </select>

  <select id="selectBlsl" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjl">
    select count(*) as liHaveflag
    from blxx
    where jzh = #{jzh}
      and blnr like #{blnr};
  </select>

  <select id="selectSlAndMaxDate" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjl">
    SELECT ifnull(sum(akc226), 0) as ldecZsl, ifnull(max(AKC221) as ldtMaxdate, '1900-01-01')
    FROM kc22
    WHERE kc22.AKC190 = #{jzh}
      AND AKC510 = #{bzmc};
  </select>



  <select id="selectYbgkWgjlList" parameterType="YbgkWgjl" resultMap="YbgkWgjlResult">
    <include refid="selectYbgkWgjlVo"/>
    <where>
      <if test="jzh != null  and jzh != ''">and jzh = #{jzh}</if>
      <if test="brname != null  and brname != ''">and brname like concat('%', #{brname}, '%')</if>
      <if test="kdksname != null  and kdksname != ''">and kdksname like concat('%', #{kdksname}, '%')</if>
      <if test="doctorname != null  and doctorname != ''">and doctorname like concat('%', #{doctorname}, '%')</if>
      <if test="fymName != null  and fymName != ''">and fym_name like concat('%', #{fymName}, '%')</if>
      <if test="createDateStart != null  and createDateStart != '' and createDateEnd != null  and createDateEnd != ''">
        and create_date between#{createDateStart} and #{createDateEnd}
      </if>
    </where>
    order by create_date desc
  </select>

  <select id="selectYbgkWgjlById" parameterType="String" resultMap="YbgkWgjlResult">
    <include refid="selectYbgkWgjlVo"/>
    where id = #{id}
  </select>


  <select id="selectWgjlByIds" resultMap="YbgkWgjlResult1" parameterType="YbgkWgjlCheckRecord">
    select * from ybgk_wgjl
    where id in
    <foreach item="item" index="index" collection="wgjlList" open="(" separator="," close=")">
      #{item.id}
    </foreach>
  </select>

  <insert id="insertWgjlCheckRecord" parameterType="java.util.List">
    INSERT INTO ybgk_wgjl_check_record (
    check_remark, check_dept, check_doctor, check_status, send_date,
    jk_code, xzlb, brtype, jzh, bed, jktype, jklog, brname, kdks, doctor,
    kdksname, doctorname, tsh_oper, fy_type, fym_id, fym_name, sl, je,
    bzmc, pl, jldw, zkyl, kzdate, cl_flag, cl_hfxx, jylsh, create_by,
    create_date, update_by, update_date, remarks, del_flag, zyh, ybbz,
    zyzt, jgid, wjxzflag
    ) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.checkRemark}, #{item.checkDept}, #{item.checkDoctor}, #{item.checkStatus}, #{item.sendDate},
      #{item.jkCode}, #{item.xzlb}, #{item.brtype}, #{item.jzh}, #{item.bed}, #{item.jktype}, #{item.jklog}, #{item.brname}, #{item.kdks}, #{item.doctor},
      #{item.kdksname}, #{item.doctorname}, #{item.tshOper}, #{item.fyType}, #{item.fymId}, #{item.fymName}, #{item.sl}, #{item.je},
      #{item.bzmc}, #{item.pl}, #{item.jldw}, #{item.zkyl}, #{item.kzdate}, #{item.clFlag}, #{item.clHfxx}, #{item.jylsh}, #{item.createBy},
      #{item.createDate}, #{item.updateBy}, #{item.updateDate}, #{item.remarks}, #{item.delFlag}, #{item.zyh}, #{item.ybbz},
      #{item.zyzt}, #{item.jgid}, #{item.wjxzflag}
      )
    </foreach>
  </insert>


  <insert id="insertYbgkWgjlGdjc" parameterType="YbgkWgjl">
    insert into ybgk_wgjl_gdjc
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="jkCode != null">jk_code,</if>
      <if test="brtype != null">brtype,</if>
      <if test="jzh != null">jzh,</if>
      <if test="bed != null">bed,</if>
      <if test="jktype != null">jktype,</if>
      <if test="jklog != null">jklog,</if>
      <if test="brname != null">brname,</if>
      <if test="kdks != null">kdks,</if>
      <if test="doctor != null">doctor,</if>
      <if test="kdksname != null">kdksname,</if>
      <if test="doctorname != null">doctorname,</if>
      <if test="tshOper != null">tsh_oper,</if>
      <if test="fyType != null">fy_type,</if>
      <if test="fymId != null">fym_id,</if>
      <if test="fymName != null">fym_name,</if>
      <if test="sl != null">sl,</if>
      <if test="je != null">je,</if>
      <if test="bzmc != null">bzmc,</if>
      <if test="pl != null">pl,</if>
      <if test="jldw != null">jldw,</if>
      <if test="zkyl != null">zkyl,</if>
      <if test="kzdate != null">kzdate,</if>
      <if test="clFlag != null">cl_flag,</if>
      <if test="clHfxx != null">cl_hfxx,</if>
      <if test="jylsh != null">jylsh,</if>
      <if test="createBy != null">create_by,</if>
      <if test="createDate != null">create_date,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="updateDate != null">update_date,</if>
      <if test="remarks != null">remarks,</if>
      <if test="delFlag != null">del_flag,</if>
      <if test="zyh != null">zyh,</if>
      <if test="ybbz != null">ybbz,</if>
      <if test="zyzt != null">zyzt,</if>
      <if test="fydate != null">fydate,</if>
      <if test="brid != null">brid,</if>
      <if test="zyid != null">zyid,</if>
      <if test="brbs != null">brbs,</if>
       <if test="sfz != null">sfz,</if>
      <if test="rydate != null">rydate,</if>
      <if test="cydate != null">cydate,</if>
      <if test="fydj != null">fydj,</if>
      <if test="ybbx != null">ybbx,</if>
      <if test="tc != null">tc,</if>
      <if test="de != null">de,</if>
      <if test="yljz != null">yljz,</if>
      <if test="setl_id != null">setl_id,</if>
      <if test="insutype != null">insutype,</if>
       <if test="zje != null">zje,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id},</if>
      <if test="jkCode != null">#{jkCode},</if>
      <if test="brtype != null">#{brtype},</if>
      <if test="jzh != null">#{jzh},</if>
      <if test="bed != null">#{bed},</if>
      <if test="jktype != null">#{jktype},</if>
      <if test="jklog != null">#{jklog},</if>
      <if test="brname != null">#{brname},</if>
      <if test="kdks != null">#{kdks},</if>
      <if test="doctor != null">#{doctor},</if>
      <if test="kdksname != null">#{kdksname},</if>
      <if test="doctorname != null">#{doctorname},</if>
      <if test="tshOper != null">#{tshOper},</if>
      <if test="fyType != null">#{fyType},</if>
      <if test="fymId != null">#{fymId},</if>
      <if test="fymName != null">#{fymName},</if>
      <if test="sl != null">#{sl},</if>
      <if test="je != null">#{je},</if>
      <if test="bzmc != null">#{bzmc},</if>
      <if test="pl != null">#{pl},</if>
      <if test="jldw != null">#{jldw},</if>
      <if test="zkyl != null">#{zkyl},</if>
      <if test="kzdate != null">#{kzdate},</if>
      <if test="clFlag != null">#{clFlag},</if>
      <if test="clHfxx != null">#{clHfxx},</if>
      <if test="jylsh != null">#{jylsh},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="createDate != null">#{createDate},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="updateDate != null">#{updateDate},</if>
      <if test="remarks != null">#{remarks},</if>
      <if test="delFlag != null">#{delFlag},</if>
      <if test="zyh != null">#{zyh},</if>
      <if test="ybbz != null">#{ybbz},</if>
      <if test="zyzt != null">#{zyzt},</if>
      <if test="fydate != null">#{fydate},</if>
      <if test="brid != null">#{brid},</if>
      <if test="zyid != null">#{zyid},</if>
      <if test="brbs != null">#{brbs},</if>
      <if test="sfz != null">#{sfz},</if>
      <if test="rydate != null">#{rydate},</if>
      <if test="cydate != null">#{cydate},</if>
      <if test="fydj != null">#{fydj},</if>
      <if test="ybbx != null">#{ybbx},</if>
      <if test="tc != null">#{tc},</if>
      <if test="de != null">#{de},</if>
      <if test="yljz != null">#{yljz},</if>
      <if test="setl_id != null">#{setl_id},</if>
      <if test="insutype != null">#{insutype},</if>
      <if test="zje != null">#{zje},</if>
    </trim>
  </insert>

  <insert id="insertYbgkWgjl" parameterType="YbgkWgjl">
    insert into ybgk_wgjl
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">id,</if>
      <if test="jkCode != null">jk_code,</if>
      <if test="brtype != null">brtype,</if>
      <if test="jzh != null">jzh,</if>
      <if test="bed != null">bed,</if>
      <if test="jktype != null">jktype,</if>
      <if test="jklog != null">jklog,</if>
      <if test="brname != null">brname,</if>
      <if test="kdks != null">kdks,</if>
      <if test="doctor != null">doctor,</if>
      <if test="kdksname != null">kdksname,</if>
      <if test="doctorname != null">doctorname,</if>
      <if test="tshOper != null">tsh_oper,</if>
      <if test="fyType != null">fy_type,</if>
      <if test="fymId != null">fym_id,</if>
      <if test="fymName != null">fym_name,</if>
      <if test="sl != null">sl,</if>
      <if test="je != null">je,</if>
      <if test="bzmc != null">bzmc,</if>
      <if test="pl != null">pl,</if>
      <if test="jldw != null">jldw,</if>
      <if test="zkyl != null">zkyl,</if>
      <if test="kzdate != null">kzdate,</if>
      <if test="clFlag != null">cl_flag,</if>
      <if test="clHfxx != null">cl_hfxx,</if>
      <if test="jylsh != null">jylsh,</if>
      <if test="createBy != null">create_by,</if>
      <if test="createDate != null">create_date,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="updateDate != null">update_date,</if>
      <if test="remarks != null">remarks,</if>
      <if test="delFlag != null">del_flag,</if>
      <if test="zyh != null">zyh,</if>
      <if test="ybbz != null">ybbz,</if>
      <if test="zyzt != null">zyzt,</if>
       <if test="brid != null">brid,</if>
      <if test="zyid != null">zyid,</if>
      <if test="brbs != null">brbs,</if>
      <if test="showcolor != null">showcolor,</if>
      <if test="sfz != null">sfz,</if>
      <if test="rydate != null">rydate,</if>
      <if test="cydate != null">cydate,</if>
      <if test="fydj != null">fydj,</if>
      <if test="ybbx != null">ybbx,</if>
      <if test="tc != null">tc,</if>
      <if test="de != null">de,</if>
      <if test="yljz != null">yljz,</if>
      <if test="setl_id != null">setl_id,</if>
      <if test="insutype != null">insutype,</if>
      <if test="fymx != null">fymx,</if>
       <if test="zje != null">zje,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id},</if>
      <if test="jkCode != null">#{jkCode},</if>
      <if test="brtype != null">#{brtype},</if>
      <if test="jzh != null">#{jzh},</if>
      <if test="bed != null">#{bed},</if>
      <if test="jktype != null">#{jktype},</if>
      <if test="jklog != null">#{jklog},</if>
      <if test="brname != null">#{brname},</if>
      <if test="kdks != null">#{kdks},</if>
      <if test="doctor != null">#{doctor},</if>
      <if test="kdksname != null">#{kdksname},</if>
      <if test="doctorname != null">#{doctorname},</if>
      <if test="tshOper != null">#{tshOper},</if>
      <if test="fyType != null">#{fyType},</if>
      <if test="fymId != null">#{fymId},</if>
      <if test="fymName != null">#{fymName},</if>
      <if test="sl != null">#{sl},</if>
      <if test="je != null">#{je},</if>
      <if test="bzmc != null">#{bzmc},</if>
      <if test="pl != null">#{pl},</if>
      <if test="jldw != null">#{jldw},</if>
      <if test="zkyl != null">#{zkyl},</if>
      <if test="kzdate != null">#{kzdate},</if>
      <if test="clFlag != null">#{clFlag},</if>
      <if test="clHfxx != null">#{clHfxx},</if>
      <if test="jylsh != null">#{jylsh},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="createDate != null">#{createDate},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="updateDate != null">#{updateDate},</if>
      <if test="remarks != null">#{remarks},</if>
      <if test="delFlag != null">#{delFlag},</if>
      <if test="zyh != null">#{zyh},</if>
      <if test="ybbz != null">#{ybbz},</if>
      <if test="zyzt != null">#{zyzt},</if>
       <if test="brid != null">#{brid},</if>
      <if test="zyid != null">#{zyid},</if>
      <if test="brbs != null">#{brbs},</if>
      <if test="showcolor != null">#{showcolor},</if>
      <if test="sfz != null">#{sfz},</if>
      <if test="rydate != null">#{rydate},</if>
      <if test="cydate != null">#{cydate},</if>
      <if test="fydj != null">#{fydj},</if>
      <if test="ybbx != null">#{ybbx},</if>
      <if test="tc != null">#{tc},</if>
      <if test="de != null">#{de},</if>
      <if test="yljz != null">#{yljz},</if>
      <if test="setl_id != null">#{setl_id},</if>
      <if test="insutype != null">#{insutype},</if>
      <if test="fymx != null">#{fymx},</if>
      <if test="zje != null">#{zje},</if>
    </trim>
  </insert>

  <update id="updateYbgkWgjl" parameterType="YbgkWgjl">
    update ybgk_wgjl
    <trim prefix="SET" suffixOverrides=",">
      <if test="jkCode != null">jk_code = #{jkCode},</if>
      <if test="brtype != null">brtype = #{brtype},</if>
      <if test="jzh != null">jzh = #{jzh},</if>
      <if test="bed != null">bed = #{bed},</if>
      <if test="jktype != null">jktype = #{jktype},</if>
      <if test="jklog != null">jklog = #{jklog},</if>
      <if test="brname != null">brname = #{brname},</if>
      <if test="kdks != null">kdks = #{kdks},</if>
      <if test="doctor != null">doctor = #{doctor},</if>
      <if test="kdksname != null">kdksname = #{kdksname},</if>
      <if test="doctorname != null">doctorname = #{doctorname},</if>
      <if test="tshOper != null">tsh_oper = #{tshOper},</if>
      <if test="fyType != null">fy_type = #{fyType},</if>
      <if test="fymId != null">fym_id = #{fymId},</if>
      <if test="fymName != null">fym_name = #{fymName},</if>
      <if test="sl != null">sl = #{sl},</if>
      <if test="je != null">je = #{je},</if>
      <if test="bzmc != null">bzmc = #{bzmc},</if>
      <if test="pl != null">pl = #{pl},</if>
      <if test="jldw != null">jldw = #{jldw},</if>
      <if test="zkyl != null">zkyl = #{zkyl},</if>
      <if test="kzdate != null">kzdate = #{kzdate},</if>
      <if test="clFlag != null">cl_flag = #{clFlag},</if>
      <if test="clHfxx != null">cl_hfxx = #{clHfxx},</if>
      <if test="jylsh != null">jylsh = #{jylsh},</if>
      <if test="createBy != null">create_by = #{createBy},</if>
      <if test="createDate != null">create_date = #{createDate},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="updateDate != null">update_date = #{updateDate},</if>
      <if test="remarks != null">remarks = #{remarks},</if>
      <if test="delFlag != null">del_flag = #{delFlag},</if>
      <if test="zyh != null">zyh = #{zyh},</if>
      <if test="ybbz != null">ybbz = #{ybbz},</if>
      <if test="zyzt != null">zyzt = #{zyzt},</if>
      <if test="zje != null">zje = #{zje},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteYbgkWgjlById" parameterType="String">
    delete
    from ybgk_wgjl
    where id = #{id}
  </delete>


  <delete id="deleteYbgkWgjlByJzh" parameterType="String">
    delete
    from ybgk_wgjl
    where jzh = #{jzh}
  </delete>


  <delete id="deleteYbgkWgjlGdjcByJzh" parameterType="String">
    delete
    from ybgk_wgjl_gdjc
    where jzh = #{jzh}
  </delete>

  <delete id="deleteYbgkWgjlByJzhandDate" parameterType="YbgkWgjl">
    delete
    from ybgk_wgjl
    where jzh = #{jzh}
    AND create_date >= #{createDateStart}
  </delete>


  <delete id="deleteYbgkWgjlByJzhAndJklog" parameterType="YbgkWgjl">
    delete
    from ybgk_wgjl
    where jzh = #{jzh}
      and jklog = #{jklog}
  </delete>

  <delete id="deleteYbgkWgjlByIds" parameterType="String">
    delete from ybgk_wgjl where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
  <select id="selectWgjlByBridList" parameterType="String" resultMap="YbgkWgjlResult">
    select  * from ybgk_wgjl where brid in
    <foreach item="bridList" collection="array" open="(" separator="," close=")">
      #{bridList}
    </foreach>
  </select>
    <delete id="deleteYbgkWgjlByJzhAndXm" parameterType="ybgkWgjl">
      delete from ybgk_wgjl
             where jzh = #{jzh} and
                   jktype = #{jktype} and
                   fym_id = #{fymId}
    </delete>

    <select id="selectYsph" resultType="ysphRes" parameterType="zkfxVo">
    select doctorname, count(doctorname) nums
    from ybgk_wgjl
    where create_date &gt;= #{vo.startDate}
      and create_date &lt;= #{vo.endDate}
    group by doctorname
    order by nums desc limit 10
  </select>

  <select id="selectKsph" resultType="ksphRes" parameterType="zkfxVo">
    select kdksname, count(kdksname) nums
    from ybgk_wgjl
    where create_date &gt;= #{startDate}
      and create_date &lt;= #{endDate}
    group by kdksname
  </select>

  <select id="selectLxzb" resultType="echartsResVo" parameterType="zkfxVo">
    select jktype as name, count(jktype) as value
    from ybgk_wgjl
    where create_date &gt;= #{startDate}
      and create_date &lt;= #{endDate}
    group by jktype
  </select>

  <select id="selectXmWordCloud" resultType="echartsResVo" parameterType="zkfxVo">
    select fym_name as name, count(fym_name) as value
    from ybgk_wgjl
    where create_date &gt;= #{startDate}
      and create_date &lt;= #{endDate}
    group by fym_name
    order by value desc limit 50
  </select>

  <select id="selectMrwg" resultType="echartsResVo" parameterType="zkfxVo">
    select date_format(create_date, '%Y-%m-%d') as name, count(date_format(create_date, '%Y-%m-%d')) as value
    from ybgk_wgjl
    where create_date &gt;= #{startDate}
      and create_date &lt;= #{endDate}
    group by name
    order by name
  </select>

  <select id="selectXzxmSl" resultType="echartsResVo" parameterType="zkfxVo">
    select '诊疗' as name, count(*) as value
    from ybgk_xzxm a join zlxm b
    on a.xmbm = b.gjxmdm
    union all
    select '药品' as name, count(*) as value
    from ybgk_xzxm a join ypml b
    on a.xmbm = b.gjypdm
  </select>
</mapper>
