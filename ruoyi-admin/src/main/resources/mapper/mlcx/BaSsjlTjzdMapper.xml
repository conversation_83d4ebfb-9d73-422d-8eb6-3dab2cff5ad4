<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaSsjlTjzdMapper">

    <resultMap type="BaSsjlTjzd" id="BaSsjlTjzdResult">
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="sscode"    column="sscode"    />
        <result property="ssname"    column="ssname"    />
        <result property="sslx"    column="sslx"    />
        <result property="ssjb"    column="ssjb"    />
        <result property="sssort"    column="sssort"    />
        <result property="id"    column="id"    />
        <result property="drgbh"    column="drgbh"    />
        <result property="bgfy"    column="bgfy"    />
    </resultMap>

    <sql id="selectBaSsjlTjzdVo">
        select jzh, brid, zyid, sscode, ssname, sslx, ssjb, sssort, id, drgbh, bgfy from ba_ssjl_tjzd
    </sql>


  <select id="selectBaSsjlTjzdListNotZnzd" parameterType="BaSsjlTjzd" resultMap="BaSsjlTjzdResult">
    select jzh, brid, zyid, sscode, ssname, sslx, ssjb, sssort, id, drgbh, bgfy from ba_ssjl_tjzd
    where jzh = #{jzh} and sslx != 'znzd'
    order by sssort asc
  </select>


    <select id="selectBaSsjlTjzdList" parameterType="BaSsjlTjzd" resultMap="BaSsjlTjzdResult">
        <include refid="selectBaSsjlTjzdVo"/>
        <where>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="sscode != null  and sscode != ''"> and sscode = #{sscode}</if>
            <if test="ssname != null  and ssname != ''"> and ssname like concat('%', #{ssname}, '%')</if>
            <if test="sslx != null  and sslx != ''"> and sslx = #{sslx}</if>
            <if test="ssjb != null  and ssjb != ''"> and ssjb = #{ssjb}</if>
            <if test="sssort != null "> and sssort = #{sssort}</if>
            <if test="drgbh != null  and drgbh != ''"> and drgbh = #{drgbh}</if>
            <if test="bgfy != null "> and bgfy = #{bgfy}</if>
        </where>
        order by sssort asc
    </select>

    <select id="selectBaSsjlTjzdById" parameterType="Long" resultMap="BaSsjlTjzdResult">
        <include refid="selectBaSsjlTjzdVo"/>
        where id = #{id}
    </select>

    <insert id="insertBaSsjlTjzd" parameterType="BaSsjlTjzd" useGeneratedKeys="true" keyProperty="id">
        insert into ba_ssjl_tjzd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">jzh,</if>
            <if test="brid != null and brid != ''">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="sscode != null">sscode,</if>
            <if test="ssname != null">ssname,</if>
            <if test="sslx != null">sslx,</if>
            <if test="ssjb != null">ssjb,</if>
            <if test="sssort != null">sssort,</if>
            <if test="drgbh != null">drgbh,</if>
            <if test="bgfy != null">bgfy,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">#{jzh},</if>
            <if test="brid != null and brid != ''">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="sscode != null">#{sscode},</if>
            <if test="ssname != null">#{ssname},</if>
            <if test="sslx != null">#{sslx},</if>
            <if test="ssjb != null">#{ssjb},</if>
            <if test="sssort != null">#{sssort},</if>
            <if test="drgbh != null">#{drgbh},</if>
            <if test="bgfy != null">#{bgfy},</if>
         </trim>
    </insert>

    <update id="updateBaSsjlTjzd" parameterType="BaSsjlTjzd">
        update ba_ssjl_tjzd
        <trim prefix="SET" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">jzh = #{jzh},</if>
            <if test="brid != null and brid != ''">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="sscode != null">sscode = #{sscode},</if>
            <if test="ssname != null">ssname = #{ssname},</if>
            <if test="sslx != null">sslx = #{sslx},</if>
            <if test="ssjb != null">ssjb = #{ssjb},</if>
            <if test="sssort != null">sssort = #{sssort},</if>
            <if test="drgbh != null">drgbh = #{drgbh},</if>
            <if test="bgfy != null">bgfy = #{bgfy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaSsjlTjzdById" parameterType="Long">
        delete from ba_ssjl_tjzd where id = #{id}
    </delete>


    <delete id="deleteBaSsjlTjzdByBridAndZyid" parameterType="BaSsjlTjzd">
        delete from ba_ssjl_tjzd where brid = #{brid} and zyid = #{zyid}
    </delete>



  <delete id="deleteBaSsjlTjzdByJzhAndNotZnzd" parameterType="String">
    delete from ba_ssjl_tjzd where jzh=#{jzh} and sslx != 'znzd';
  </delete>

    <delete id="deleteBaSsjlTjzdByIds" parameterType="String">
        delete from ba_ssjl_tjzd where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
