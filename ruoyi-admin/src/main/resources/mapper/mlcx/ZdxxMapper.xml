<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ZdxxMapper">

    <resultMap type="Zdxx" id="ZdxxResult">
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="zdcode"    column="zdcode"    />
        <result property="zdname"    column="zdname"    />
        <result property="zdtype"    column="zdtype"    />
        <result property="zdsort"    column="zdsort"    />
        <result property="drgbh"    column="drgbh"    />
        <result property="bgfy"    column="bgfy"    />
        <result property="id"    column="id"    />
        <result property="rybq"    column="rybq"    />
        <result property="jlly"    column="jlly"    />
    </resultMap>

    <sql id="selectZdxxVo">
        select jzh, brid, zyid, zdcode, zdname, zdtype, zdsort, id,drgbh,bgfy,jlly,rybq from zdxx
    </sql>

    <select id="selectZdxxList" parameterType="Zdxx" resultMap="ZdxxResult">
        <include refid="selectZdxxVo"/>
        <where>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="zdcode != null  and zdcode != ''"> and zdcode = #{zdcode}</if>
            <if test="zdname != null  and zdname != ''"> and zdname like concat('%', #{zdname}, '%')</if>
            <if test="zdsort != null  and zdsort != ''"> and zdsort = #{zdsort}</if>
            <if test="zdtype != null  and zdtype != ''"> and zdtype = #{zdtype}</if>
            <if test="drgbh != null  and drgbh != ''"> and drgbh = #{drgbh}</if>
            <if test="bgfy != null  and bgfy != ''"> and bgfy = #{bgfy}</if>
            <if test="jlly != null  and jlly != ''"> and jlly = #{jlly}</if>
            <if test="rybq != null  and rybq != ''"> and rybq = #{rybq}</if>
        </where>
        order by zdsort asc
    </select>

    <select id="selectJlly" parameterType="Zdxx" resultMap="ZdxxResult">
      select max(jlly) as jlly,max(jzh) as jzh
      from zdxx
      where brid = #{brid} and ifnull(zyid,'') = #{zyid}
            and jlly in ('3','4','医生','病案');
    </select>


<select id="selectjsxxzdxx" parameterType="Zdxx" resultMap="ZdxxResult">
      select max(dise_name) as zdname
      from jsxx_his_zy
      where brbs=#{jzh}
    </select>


    <select id="selectZdxxById" parameterType="Long" resultMap="ZdxxResult">
        <include refid="selectZdxxVo"/>
        where id = #{id}
    </select>

    <insert id="insertZdxx" parameterType="Zdxx" useGeneratedKeys="true" keyProperty="id">
        insert into zdxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">jzh,</if>
            <if test="brid != null and brid != ''">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="zdcode != null">zdcode,</if>
            <if test="zdname != null">zdname,</if>
            <if test="zdtype != null">zdtype,</if>
            <if test="zdsort != null">zdsort,</if>
            <if test="drgbh != null">drgbh,</if>
            <if test="bgfy != null">bgfy,</if>
            <if test="jlly != null">jlly,</if>
            <if test="rybq != null">rybq,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">#{jzh},</if>
            <if test="brid != null and brid != ''">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="zdcode != null">#{zdcode},</if>
            <if test="zdname != null">#{zdname},</if>
            <if test="zdtype != null">#{zdtype},</if>
            <if test="zdsort != null">#{zdsort},</if>
            <if test="drgbh != null">#{drgbh},</if>
            <if test="bgfy != null">#{bgfy},</if>
            <if test="jlly != null">#{jlly},</if>
            <if test="rybq != null">#{rybq},</if>
         </trim>
    </insert>
    
       <insert id="batchInsertZdxx" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zdxx (
            jzh, 
            brid, 
            zyid,
            zdtype,
            zdcode, 
            zdname, 
            jlly, 
            zdsort, 
            rybq
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.jzh},
                #{item.brid},
                 #{item.zyid},
                  #{item.zdtype},
                #{item.zdcode},
                #{item.zdname},
                #{item.jlly},
                #{item.zdsort},
                #{item.rybq}
            )
        </foreach>
    </insert>

    <update id="updateZdxx" parameterType="Zdxx">
        update zdxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">jzh = #{jzh},</if>
            <if test="brid != null and brid != ''">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="zdcode != null">zdcode = #{zdcode},</if>
            <if test="zdname != null">zdname = #{zdname},</if>
            <if test="zdtype != null">zdtype = #{zdtype},</if>
            <if test="zdsort != null">zdsort = #{zdsort},</if>
            <if test="drgbh != null">drgbh = #{drgbh},</if>
            <if test="bgfy != null">bgfy = #{bgfy},</if>
            <if test="jlly != null">jlly = #{jlly},</if>
            <if test="rybq != null">rybq = #{rybq},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZdxxById" parameterType="Long">
        delete from zdxx where id = #{id}
    </delete>


    <delete id="deleteZdxxByJzh" parameterType="String">
      delete from zdxx where jzh = #{jzh}
    </delete>
    
    <select id="updateBrzdxx" parameterType="Zdxx" >
        call usp_update_brzdxx('','',#{brid},#{zyid})
    </select>
 

    <delete id="deleteZdxxByBridAndZyid" parameterType="Zdxx">
        delete from zdxx where brid = #{brid}
        <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
    </delete>
    
    <delete id="deleteZdxxqd" parameterType="Zdxx">
        delete from zdxx where brid = #{brid}
        <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
         <if test="jlly != null  and jlly != ''"> and jlly = #{jlly}</if>
    </delete>

    <delete id="deleteZdxxByIds" parameterType="String">
        delete from zdxx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
