<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BlxxMapper">

    <resultMap type="Blxx" id="BlxxResult">
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="blname"    column="blname"    />
        <result property="blnr"    column="blnr"    />
        <result property="doctor"    column="doctor"    />
        <result property="createdate"    column="createdate"    />
        <result property="updatedate"    column="updatedate"    />
        <result property="id"    column="id"    />
    </resultMap>

    <sql id="selectBlxxVo">
        select jzh, brid, zyid, blname, blnr, doctor, createdate, updatedate, id from blxx
    </sql>

    <select id="selectBlxxList" parameterType="Blxx" resultMap="BlxxResult">
        <include refid="selectBlxxVo"/>
        <where>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="blname != null  and blname != ''"> and blname like concat('%', #{blname}, '%')</if>
            <if test="blnr != null  and blnr != ''"> and blnr = #{blnr}</if>
            <if test="doctor != null  and doctor != ''"> and doctor = #{doctor}</if>
            <if test="params.beginCreatedate != null and params.beginCreatedate != '' and params.endCreatedate != null and params.endCreatedate != ''"> and createdate between #{params.beginCreatedate} and #{params.endCreatedate}</if>
            <if test="updatedate != null "> and updatedate = #{updatedate}</if>
        </where>
    </select>

    <select id="selectMaxUpdateDate" resultType="Blxx" parameterType="Blxx">
      select ifnull(max(updatedate),'2000-01-01 00:01:01') as updatedate  from blxx
      <where>
        <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
        <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
        <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
        and (blname LIKE '%入院%' OR blname LIKE '%病程%' OR  blname LIKE '%出院%' OR  blname LIKE '%手术%' OR blname LIKE '%死亡%')
      </where>

    </select>

    <select id="selectBlxxById" parameterType="Long" resultMap="BlxxResult">
        <include refid="selectBlxxVo"/>
        where id = #{id}
    </select>


  <select id="selectRyBlidBegin" parameterType="Blxx" resultType="Blxx">
      SELECT max(id)  as id
      FROM blxx WHERE jzh = #{jzh}
      AND (blname = '入院病历' or blname = '入院记录') and (blnr like '%初步诊断%' or blnr like '%诊断记录%' );
    </select>

  <select id="selectRyBlidEnd" parameterType="Blxx" resultType="Blxx">
    SELECT max(id) as id
    FROM blxx WHERE jzh = #{jzh}
    AND (blname = '入院病历' or blname = '入院记录') and (blnr like '%医师签名%' );
  </select>

  <select id="selectRyBlryzd" parameterType="Blxx" resultType="Blxx">
    SELECT REPLACE(REPLACE(GROUP_CONCAT(blnr), '\n', '\r'),'  ',' ')
    as blnr
    FROM blxx WHERE jzh = #{jzh}
    AND (blname = '入院病历' or blname = '入院记录')
    AND id >= #{begin_id} AND id &lt; #{end_id};
  </select>

  <select id="selectCyBlidBegin" parameterType="Blxx" resultType="Blxx">
    SELECT max(id)
    as id
    FROM blxx WHERE jzh = #{jzh}
    AND (blname = '出院病历' or blname = '出院记录') and (blnr like '%出院诊断%' or blnr like '%诊断记录%');
  </select>

  <select id="selectCyBlidEnd" parameterType="Blxx" resultType="Blxx">
    SELECT max(id)
    as id
    FROM blxx WHERE jzh = #{jzh}
    AND (blname = '出院病历' or blname = '出院记录') and (blnr like '%出院医嘱%' );
  </select>

  <select id="selectCyBlcyzd" parameterType="Blxx" resultType="Blxx">
    SELECT REPLACE(REPLACE(GROUP_CONCAT(blnr), '\n', '\r'),'  ',' ')
    as blnr
    FROM blxx WHERE jzh = #{jzh}
    AND (blname = '出院病历' or blname = '出院记录')
    AND id >= #{begin_id} AND id &lt; #{end_id};
  </select>

    <insert id="insertBlxx" parameterType="Blxx" useGeneratedKeys="true" keyProperty="id">
        insert into blxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">jzh,</if>
            <if test="brid != null and brid != ''">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="blname != null">blname,</if>
            <if test="blnr != null">blnr,</if>
            <if test="doctor != null">doctor,</if>
            <if test="createdate != null">createdate,</if>
            <if test="updatedate != null">updatedate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">#{jzh},</if>
            <if test="brid != null and brid != ''">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="blname != null">#{blname},</if>
            <if test="blnr != null">#{blnr},</if>
            <if test="doctor != null">#{doctor},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="updatedate != null">#{updatedate},</if>
         </trim>
    </insert>

    <update id="updateBlxx" parameterType="Blxx">
        update blxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="jzh != null and jzh != ''">jzh = #{jzh},</if>
            <if test="brid != null and brid != ''">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="blname != null">blname = #{blname},</if>
            <if test="blnr != null">blnr = #{blnr},</if>
            <if test="doctor != null">doctor = #{doctor},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="updatedate != null">updatedate = #{updatedate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBlxxById" parameterType="Long">
        delete from blxx where id = #{id}
    </delete>

  <delete id="deleteBlxxByJzh" parameterType="Blxx">
    delete from blxx where jzh = #{jzh}
  </delete>

  <delete id="deleteBlxxByZyidAndBrid" parameterType="Blxx">
    delete from blxx
    <where>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
      and brid = #{brid}
    </where>
  </delete>

    <delete id="deleteBlxxByIds" parameterType="String">
        delete from blxx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
