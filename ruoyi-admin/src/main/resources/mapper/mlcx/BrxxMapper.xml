<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BrxxMapper">

    <resultMap type="Brxx" id="BrxxResult">
        <result property="brtype"    column="brtype"    />
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="zyh"    column="zyh"    />
        <result property="name"    column="name"    />
        <result property="bed"    column="bed"    />
        <result property="age"    column="age"    />
        <result property="sex"    column="sex"    />
        <result property="tel"    column="tel"    />
        <result property="ybh"    column="ybh"    />
        <result property="jgid"    column="jgid"    />
        <result property="rydate"    column="rydate"    />
        <result property="zyzt"    column="zyzt"    />
        <result property="deptid"    column="deptid"    />
        <result property="doctorid"    column="doctorid"    />
        <result property="bzcode"    column="bzcode"    />
        <result property="deptname"    column="deptname"    />
        <result property="doctorname"    column="doctorname"    />
        <result property="bzname"    column="bzname"    />
        <result property="cydate"    column="cydate"    />
        <result property="blryzd"    column="blryzd"    />
        <result property="blcyzd"    column="blcyzd"    />
        <result property="blssjl"    column="blssjl"    />
        <result property="blzs"    column="blzs"    />
        <result property="bljws"    column="bljws"    />
        <result property="blzljg"    column="blzljg"    />
        <result property="blxbs"    column="blxbs"    />
        <result property="shzt"    column="shzt"    />
        <result property="advice"    column="advice"    />
        <result property="path"    column="path"    />
        <result property="ybdjlsh"    column="ybdjlsh"   />
         <result property="qddate"    column="qddate"   />
          <result property="gddate"    column="gddate"   />
    </resultMap>

    <sql id="selectBrxxVo">
        select path,brtype, jzh, brid, zyid, zyh, name, bed, age, sex, tel, ybh, jgid, rydate, zyzt, deptid, doctorid, bzcode, deptname, doctorname, bzname, cydate, blryzd, blcyzd, blssjl, blzs, bljws, blzljg,blxbs,shzt,advice from brxx
    </sql>


  <select id="selectZyAndCyThreeDaysBrxx" parameterType="Brxx" resultMap="BrxxResult">
    SELECT * FROM brxx WHERE zyzt = '1' OR (zyzt = '0' AND cydate &gt;= DATE_SUB(NOW(), INTERVAL 7 DAY))
  </select>

  <select id="selectRyThreeDaysBrxx" parameterType="Brxx" resultMap="BrxxResult" >
    SELECT * FROM brxx WHERE rydate >= DATE_SUB(NOW(), INTERVAL 3 DAY)
  </select>

    <select id="selectRydateByJzh" parameterType="Brxx" resultType="Brxx">
      select ifnull(rydate,#{rydate}) as rydate from brxx where jzh=#{jzh};
    </select>

     <select id="select10dayzy" parameterType="Brxx" resultType="Brxx">
		SELECT a.zyh ,a.rydate ,b.enddate AS cydate,
		DATEDIFF(a.rydate,b.enddate) AS shzt,a.name AS NAME
		 FROM brxx a,jsxx_his_zy b
		WHERE a.ybh = b.psn_no
		AND a.brtype = '2'
		AND CONCAT(a.brid,'_',a.zyid)&lt;&gt;CONCAT(b.brid,'_',b.zyid)
		AND DATE_FORMAT( a.rydate,'%Y-%m-%d')&lt;&gt;DATE_FORMAT( b.begndate,'%Y-%m-%d')
		AND b.med_type = '21'
		AND DATEDIFF(a.rydate,b.enddate)&lt;10
		AND a.rydate &gt; b.begndate
		AND a.rydate &gt; DATE_SUB(CURDATE(), INTERVAL 2 DAY)
		AND a.jzh=#{jzh}
		AND a.zyzt='1'
    </select>


    <select id="selectBrxxList" parameterType="Brxx" resultMap="BrxxResult">
        <include refid="selectBrxxVo"/>
        <where>
            <if test="brtype != null  and brtype != ''"> and brtype = #{brtype}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="zyh != null  and zyh != ''"> and zyh = #{zyh}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="bed != null  and bed != ''"> and bed = #{bed}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="tel != null  and tel != ''"> and tel = #{tel}</if>
            <if test="ybh != null  and ybh != ''"> and ybh = #{ybh}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
            <if test="params.beginRydate != null and params.beginRydate != '' and params.endRydate != null and params.endRydate != ''"> and rydate between #{params.beginRydate} and #{params.endRydate}</if>
            <if test="zyzt != null  and zyzt != ''"> and zyzt = #{zyzt}</if>
            <if test="deptid != null  and deptid != ''"> and deptid = #{deptid}</if>
            <if test="doctorid != null  and doctorid != ''"> and doctorid = #{doctorid}</if>
            <if test="bzcode != null  and bzcode != ''"> and bzcode = #{bzcode}</if>
            <if test="deptname != null  and deptname != ''"> and deptname like concat('%', #{deptname}, '%')</if>
            <if test="doctorname != null  and doctorname != ''"> and doctorname like concat('%', #{doctorname}, '%')</if>
            <if test="bzname != null  and bzname != ''"> and bzname like concat('%', #{bzname}, '%')</if>
            <if test="cydate != null "> and cydate = #{cydate}</if>
            <if test="blryzd != null  and blryzd != ''"> and blryzd = #{blryzd}</if>
            <if test="blcyzd != null  and blcyzd != ''"> and blcyzd = #{blcyzd}</if>
            <if test="blssjl != null  and blssjl != ''"> and blssjl = #{blssjl}</if>
            <if test="blzs != null  and blzs != ''"> and blzs = #{blzs}</if>
            <if test="bljws != null  and bljws != ''"> and bljws = #{bljws}</if>
            <if test="blzljg != null  and blzljg != ''"> and blzljg = #{blzljg}</if>
            <if test="path != null  and path != ''"> and path = #{path}</if>
            <if test="ybdjlsh != null  and ybdjlsh != ''"> and ybdjlsh = #{ybdjlsh}</if>
        </where>
    </select>

     <select id="selectzyBrxxList" parameterType="Brxx" resultMap="BrxxResult">
        <include refid="selectBrxxVo"/>
       WHERE zyzt ='1' OR (zyzt='0' AND cydate>DATE_ADD(CURDATE(), INTERVAL -3 DAY))
    </select>

    <select id="selectMaxRydate" resultType="Brxx">
      select max(rydate) as rydate from brxx where brtype='1'
    </select>

    <select id="selectNumAndZyztByJzh" parameterType="String" resultType="Brxx">
      select count(*) as num,max(zyzt) as zyzt from brxx where jzh = #{jzh}
    </select>

    <select id="selectBrxxByBrtype" parameterType="String" resultMap="BrxxResult">
        <include refid="selectBrxxVo"/>
        where brtype = #{brtype}
    </select>


    <select id="selectBrxxByJzh" parameterType="String" resultMap="BrxxResult">
      select path,brtype, jzh, brid, zyid, zyh, name, bed, age, sex, tel, ybh, jgid, rydate, zyzt, deptid, doctorid, bzcode, deptname, doctorname, bzname, cydate, blryzd, blcyzd, blssjl, blzs, bljws, blzljg,blxbs,shzt,advice, jgid from brxx
        where jzh = #{jzh} and brtype = '2'
    </select>


    <select id="selectBrxxByBridAndZyid" parameterType="Brxx" resultType="Brxx">
      select path,brtype, jzh, brid, zyid, zyh, name, bed, age, sex, tel, ybh, jgid, rydate, zyzt, deptid, doctorid, bzcode, deptname, doctorname, bzname, cydate, blryzd, blcyzd, blssjl, blzs, bljws, blzljg,blxbs,shzt,advice, jgid from brxx
      <where>
        <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
        <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
        <if test="jzh != null and jzh != ''"> and jzh = #{jzh}</if>
      </where>
      limit 1;
    </select>

    <select id="selectBrxxByybdjlsh" parameterType="Brxx" resultType="Brxx">
      <include refid="selectBrxxVo"/>
      <where>
        <if test="ybdjlsh != null  and ybdjlsh != ''"> and ybdjlsh = #{ybdjlsh}</if>
      </where>
      and zyzt='1'
      limit 1;
    </select>

    <select id="selectBrxxByBrbs" parameterType="String" resultType="Brxx">
      <include refid="selectBrxxVo"/>
      where jzh = #{brbs}
      limit 1;
    </select>

    <select id="selectBrxxByzyh" parameterType="String" resultType="Brxx">
      <include refid="selectBrxxVo"/>
      where zyh = #{zyh} and zyzt='1'
      limit 1;
    </select>

    <select id="selectLastThrreOut" parameterType="String" resultMap="BrxxResult">
        <include refid="selectBrxxVo"/>
        where cydate &gt;= #{primaryDate}
    </select>
    <select id="selectBrxxByKkxx" parameterType="String" resultMap="BrxxResult">
        <include refid="selectBrxxVo"/>
        <where>
            and name = #{name}
            and rydate like concat(#{rydate}, '%')
        </where>
    </select>
    <select id="selectSyncFeeParamVoList" resultType="com.ruoyi.system.domain.vo.SyncFeeParamVO">
        select a.brid as brid, a.zyid as zyid, max(b.fydate) as fydate, a.rydate as rydate from brxx a left join fyxx b on a.jzh = b.jzh
        where a.zyzt = '1' or a.cydate &gt;= #{primaryDate}
        group by a.jzh
    </select>
    <select id="selectOutForDaysBrxx" resultMap="BrxxResult">
        select * from brxx where cydate &gt;= date_sub(DATE(now()), interval #{days} DAY)
        and cydate &lt;= date_sub(DATE(now()), interval #{days} - 1 day)
    </select>
    <select id="selectDeptList" resultType="com.ruoyi.system.domain.Brxx">
      SELECT DISTINCT deptname
      FROM brxx
      <where>
        and deptname is not null
        and rydate >= date_add(curdate(), interval -9 month)
        <if test="jgid != null and jgid != ''">
          and jgid = #{jgid}
        </if>
      </where>
    </select>
  <select id="selectDoctorByDept" resultType="com.ruoyi.system.domain.Brxx">
    select distinct doctorname
    from brxx
    <where>
      and deptname = #{deptname}
      and doctorname is not null
      and deptname NOT REGEXP '^[0-9]+$'
      <if test="jgid != null and jgid != ''">
        and jgid = #{jgid}
      </if>
    </where>
  </select>
  <select id="selectBrxxByToday" resultType="string">
    SELECT a.brid FROM brxx a LEFT JOIN jsxx_his_zy b ON a.brid = b.brid WHERE
    a.cydate   &gt;= CURDATE() - INTERVAL 1 DAY
    AND a.cydate  &lt; CURDATE();
  </select>


  <insert id="insertBrxx" parameterType="Brxx">
        insert into brxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brtype != null">brtype,</if>
            <if test="jzh != null">jzh,</if>
            <if test="brid != null and brid != ''">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="zyh != null and zyh != ''">zyh,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="bed != null">bed,</if>
            <if test="age != null">age,</if>
            <if test="sex != null">sex,</if>
            <if test="tel != null">tel,</if>
            <if test="ybh != null">ybh,</if>
            <if test="jgid != null">jgid,</if>
            <if test="rydate != null">rydate,</if>
            <if test="zyzt != null">zyzt,</if>
            <if test="deptid != null">deptid,</if>
            <if test="doctorid != null">doctorid,</if>
            <if test="bzcode != null">bzcode,</if>
            <if test="deptname != null">deptname,</if>
            <if test="doctorname != null">doctorname,</if>
            <if test="bzname != null">bzname,</if>
            <if test="cydate != null">cydate,</if>
            <if test="blryzd != null">blryzd,</if>
            <if test="blcyzd != null">blcyzd,</if>
            <if test="blssjl != null">blssjl,</if>
            <if test="blzs != null">blzs,</if>
            <if test="bljws != null">bljws,</if>
            <if test="blzljg != null">blzljg,</if>
            <if test="path != null">path,</if>
            <if test="cblb != null">cblb,</if>
            <if test="ybdjlsh != null">ybdjlsh,</if>
             <if test="qddate != null">qddate,</if>
              <if test="gddate != null">gddate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brtype != null">#{brtype},</if>
            <if test="jzh != null">#{jzh},</if>
            <if test="brid != null and brid != ''">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="zyh != null and zyh != ''">#{zyh},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="bed != null">#{bed},</if>
            <if test="age != null">#{age},</if>
            <if test="sex != null">#{sex},</if>
            <if test="tel != null">#{tel},</if>
            <if test="ybh != null">#{ybh},</if>
            <if test="jgid != null">#{jgid},</if>
            <if test="rydate != null">#{rydate},</if>
            <if test="zyzt != null">#{zyzt},</if>
            <if test="deptid != null">#{deptid},</if>
            <if test="doctorid != null">#{doctorid},</if>
            <if test="bzcode != null">#{bzcode},</if>
            <if test="deptname != null">#{deptname},</if>
            <if test="doctorname != null">#{doctorname},</if>
            <if test="bzname != null">#{bzname},</if>
            <if test="cydate != null">#{cydate},</if>
            <if test="blryzd != null">#{blryzd},</if>
            <if test="blcyzd != null">#{blcyzd},</if>
            <if test="blssjl != null">#{blssjl},</if>
            <if test="blzs != null">#{blzs},</if>
            <if test="bljws != null">#{bljws},</if>
            <if test="blzljg != null">#{blzljg},</if>
            <if test="path != null">#{path},</if>
             <if test="cblb != null">#{cblb},</if>
             <if test="ybdjlsh != null">#{ybdjlsh},</if>
               <if test="qddate != null">#{qddate},</if>
             <if test="gddate != null">#{gddate},</if>

         </trim>
    </insert>


    <update id="updateBrxxShzt" parameterType="Brxx">
        update brxx set shzt = #{shzt},advice = #{advice}
        <where>
          <if test="jzh != null and jzh != ''"> and jzh = #{jzh}</if>
          <if test="brid != null and brid != ''"> and brid = #{brid}</if>
          <if test="zyid != null and zyid != ''"> and zyid = #{zyid}</if>
        </where>
    </update>

    <update id="updateBrxx" parameterType="Brxx">
        update brxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="brid != null and brid != ''">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="zyh != null and zyh != ''">zyh = #{zyh},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="bed != null">bed = #{bed},</if>
            <if test="age != null">age = #{age},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="ybh != null">ybh = #{ybh},</if>
            <if test="jgid != null">jgid = #{jgid},</if>
            <if test="rydate != null">rydate = #{rydate},</if>
            <if test="zyzt != null">zyzt = #{zyzt},</if>
            <if test="deptid != null">deptid = #{deptid},</if>
            <if test="doctorid != null">doctorid = #{doctorid},</if>
            <if test="bzcode != null">bzcode = #{bzcode},</if>
            <if test="deptname != null">deptname = #{deptname},</if>
            <if test="doctorname != null">doctorname = #{doctorname},</if>
            <if test="bzname != null">bzname = #{bzname},</if>
            <if test="cydate != null">cydate = #{cydate},</if>
            <if test="blryzd != null">blryzd = #{blryzd},</if>
            <if test="blcyzd != null">blcyzd = #{blcyzd},</if>
            <if test="blssjl != null">blssjl = #{blssjl},</if>
            <if test="blzs != null">blzs = #{blzs},</if>
            <if test="blxbs != null">blxbs = #{blxbs},</if>
            <if test="bljws != null">bljws = #{bljws},</if>
            <if test="blzljg != null">blzljg = #{blzljg},</if>
            <if test="path != null">path = #{path},</if>
             <if test="cblb != null">cblb = #{cblb},</if>
            <if test="ybdjlsh != null">ybdjlsh = #{ybdjlsh},</if>
            <if test="qddate != null">qddate = #{qddate},</if>
            <if test="gddate != null">gddate = #{gddate},</if>
        </trim>
        <where>
          jzh = #{jzh}
          <if test="brtype != null  and brtype != ''"> and brtype = #{brtype}</if>
        </where>
    </update>

    <delete id="deleteBrxxByBrtype" parameterType="String">
        delete from brxx where brtype = #{brtype}
    </delete>

    <delete id="deleteBrxxByBrtypes" parameterType="String">
        delete from brxx where brtype in
        <foreach item="brtype" collection="array" open="(" separator="," close=")">
            #{brtype}
        </foreach>
    </delete>
</mapper>
