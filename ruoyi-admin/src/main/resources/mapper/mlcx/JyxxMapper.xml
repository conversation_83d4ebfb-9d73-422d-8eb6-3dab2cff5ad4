<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JyxxMapper">

    <resultMap type="Jyxx" id="JyxxResult">
        <result property="id"    column="id"    />
        <result property="brbs"    column="brbs"    />
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="xmbh"    column="xmbh"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="jyfw"    column="jyfw"    />
        <result property="jyjg"    column="jyjg"    />
        <result property="jydw"    column="jydw"    />
        <result property="jgbz"    column="jgbz"    />
        <result property="doctor"    column="doctor"    />
        <result property="createdate"    column="createdate"    />
        <result property="reportdate"    column="reportdate"    />
      <result property="ldtFromDate"    column="ldtFromDate"    />
      <result property="ldtToDate"    column="ldtToDate"    />
      <result property="jgid"    column="jgid"    />
    </resultMap>

    <sql id="selectJyxxVo">
        select id, brbs, jzh, brid, zyid, xmbh, xmmc, jyfw, jyjg, jydw, jgbz, doctor, createdate, reportdate, jgid from jyxx
    </sql>


    <select id="selectMaxReportDate" parameterType="String" resultType="java.time.LocalDateTime">
        select ifnull(max(reportdate),'2023-09-01 00:00:00') as ldtFromDate from jyxx
        <where>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
        </where>
    </select>


<!--  <select id="selectMaxReportDate" parameterType="Integer" resultType="Jyxx">-->
<!--    select ifnull(max(reportdate),'2023-09-01') as ldtFromDate,-->
<!--           DATE_ADD(ifnull(max(reportdate),'2023-09-01'), INTERVAL + #{days} DAY) as ldtToDate-->
<!--    from jyxx-->
<!--    <where>-->
<!--      <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>-->
<!--    </where>-->
<!--  </select>-->

    <select id="selectJyxxList" parameterType="Jyxx" resultMap="JyxxResult">
        <include refid="selectJyxxVo"/>
        <where>
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="xmbh != null  and xmbh != ''"> and xmbh = #{xmbh}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc like concat(#{xmmc}, '%')</if>
            <if test="jyfw != null  and jyfw != ''"> and jyfw = #{jyfw}</if>
            <if test="jyjg != null  and jyjg != ''"> and jyjg = #{jyjg}</if>
            <if test="jydw != null  and jydw != ''"> and jydw = #{jydw}</if>
            <if test="jgbz != null  and jgbz != ''"> and jgbz = #{jgbz}</if>
            <if test="doctor != null  and doctor != ''"> and doctor = #{doctor}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
            <if test="reportdate != null "> and reportdate = #{reportdate}</if>
        </where>
    </select>


    <select id="selectJyxxast" parameterType="Jyxx" resultMap="JyxxResult">
        <include refid="selectJyxxVo"/>
        <where>
		    jyjg REGEXP  '^[0-9]+(\.[0-9]+)?$'
		    AND CAST(jyjg AS UNSIGNED) > 120
		    AND (xmbh='AST' OR  xmbh='ALT')
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="xmbh != null  and xmbh != ''"> and xmbh = #{xmbh}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc like concat(#{xmmc}, '%')</if>
            <if test="jyfw != null  and jyfw != ''"> and jyfw = #{jyfw}</if>
            <if test="jyjg != null  and jyjg != ''"> and jyjg = #{jyjg}</if>
            <if test="jydw != null  and jydw != ''"> and jydw = #{jydw}</if>
            <if test="jgbz != null  and jgbz != ''"> and jgbz = #{jgbz}</if>
            <if test="doctor != null  and doctor != ''"> and doctor = #{doctor}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
            <if test="reportdate != null "> and reportdate = #{reportdate}</if>
        </where>
    </select>


    <select id="selectJyxxById" parameterType="Long" resultMap="JyxxResult">
        <include refid="selectJyxxVo"/>
        where id = #{id}
    </select>
  <select id="selectJyxxForChatList" resultType="com.ruoyi.system.domain.Jyxx">
    <include refid="selectJyxxVo"/>
    where jgbz is not null and jgbz != ''
    and jzh = #{jzh}
  </select>

  <insert id="insertJyxx" parameterType="Jyxx" useGeneratedKeys="true" keyProperty="id">
        insert into jyxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="jzh != null">jzh,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="xmbh != null">xmbh,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="jyfw != null">jyfw,</if>
            <if test="jyjg != null">jyjg,</if>
            <if test="jydw != null">jydw,</if>
            <if test="jgbz != null">jgbz,</if>
            <if test="doctor != null">doctor,</if>
            <if test="createdate != null">createdate,</if>
            <if test="reportdate != null">reportdate,</if>
            <if test="jgid != null">jgid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="jzh != null">#{jzh},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="xmbh != null">#{xmbh},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="jyfw != null">#{jyfw},</if>
            <if test="jyjg != null">#{jyjg},</if>
            <if test="jydw != null">#{jydw},</if>
            <if test="jgbz != null">#{jgbz},</if>
            <if test="doctor != null">#{doctor},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="reportdate != null">#{reportdate},</if>
            <if test="jgid != null">#{jgid},</if>
         </trim>
    on duplicate key update
    brbs = values(brbs),
    reportdate = values(reportdate),
    xmbh = values(xmbh),
    xmmc = values(xmmc);
    </insert>

    <update id="updateJyxx" parameterType="Jyxx">
        update jyxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="brbs != null">brbs = #{brbs},</if>
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="xmbh != null">xmbh = #{xmbh},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="jyfw != null">jyfw = #{jyfw},</if>
            <if test="jyjg != null">jyjg = #{jyjg},</if>
            <if test="jydw != null">jydw = #{jydw},</if>
            <if test="jgbz != null">jgbz = #{jgbz},</if>
            <if test="doctor != null">doctor = #{doctor},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="reportdate != null">reportdate = #{reportdate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJyxxById" parameterType="Long">
        delete from jyxx where id = #{id}
    </delete>

    <delete id="deleteJyxxByIds" parameterType="String">
        delete from jyxx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
