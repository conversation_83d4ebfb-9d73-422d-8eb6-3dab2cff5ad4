<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.PisxxMapper">
    
    <resultMap type="Pisxx" id="PisxxResult">
        <result property="id"    column="id"    />
        <result property="brbs"    column="brbs"    />
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="blh"    column="blh"    />
        <result property="jcmc"    column="jcmc"    />
        <result property="bljg"    column="bljg"    />
        <result property="createdate"    column="createdate"    />
        <result property="reportdate"    column="reportdate"    />
    </resultMap>

    <sql id="selectPisxxVo">
        select id, brbs, jzh, brid, zyid, blh, jcmc, bljg, createdate, reportdate from pisxx
    </sql>

    <select id="selectPisxxList" parameterType="Pisxx" resultMap="PisxxResult">
        <include refid="selectPisxxVo"/>
        <where>  
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="blh != null  and blh != ''"> and blh = #{blh}</if>
            <if test="jcmc != null  and jcmc != ''"> and jcmc = #{jcmc}</if>
            <if test="bljg != null  and bljg != ''"> and bljg = #{bljg}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
            <if test="reportdate != null "> and reportdate = #{reportdate}</if>
        </where>
    </select>
    
    <select id="selectPisxxById" parameterType="Long" resultMap="PisxxResult">
        <include refid="selectPisxxVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPisxx" parameterType="Pisxx" useGeneratedKeys="true" keyProperty="id">
        insert into pisxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="jzh != null">jzh,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="blh != null">blh,</if>
            <if test="jcmc != null">jcmc,</if>
            <if test="bljg != null">bljg,</if>
            <if test="createdate != null">createdate,</if>
            <if test="reportdate != null">reportdate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="jzh != null">#{jzh},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="blh != null">#{blh},</if>
            <if test="jcmc != null">#{jcmc},</if>
            <if test="bljg != null">#{bljg},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="reportdate != null">#{reportdate},</if>
         </trim>
    </insert>

    <update id="updatePisxx" parameterType="Pisxx">
        update pisxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="brbs != null">brbs = #{brbs},</if>
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="blh != null">blh = #{blh},</if>
            <if test="jcmc != null">jcmc = #{jcmc},</if>
            <if test="bljg != null">bljg = #{bljg},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="reportdate != null">reportdate = #{reportdate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePisxxById" parameterType="Long">
        delete from pisxx where id = #{id}
    </delete>

    <delete id="deletePisxxByIds" parameterType="String">
        delete from pisxx where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>