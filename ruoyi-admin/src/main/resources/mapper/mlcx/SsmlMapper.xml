<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SsmlMapper">

  <resultMap type="Ssml" id="SsmlResult">
    <result property="ssbm"    column="ssbm"    />
    <result property="ssmc"    column="ssmc"    />
    <result property="zjm"    column="zjm"    />
    <result property="ssbm1"    column="ssbm1"    />
    <result property="ssmc1"    column="ssmc1"    />
    <result property="ssbm2"    column="ssbm2"    />
    <result property="ssmc2"    column="ssmc2"    />
    <result property="ssbm3"    column="ssbm3"    />
    <result property="ssmc3"    column="ssmc3"    />
    <result property="ssbm4"    column="ssbm4"    />
    <result property="ssmc4"    column="ssmc4"    />
    <result property="type"    column="TYPE"    />
  </resultMap>

  <sql id="selectSsmlVo">
    select ssbm, ssmc, zjm, ssbm1, ssmc1, ssbm2, ssmc2, ssbm3, ssmc3, ssbm4, ssmc4, TYPE from ssml
  </sql>

  <select id="selectSsmlList" parameterType="Ssml" resultMap="SsmlResult">
    <include refid="selectSsmlVo"/>
    <where>
     <if test="ssbm != null  and ssbm != ''"> and ssbm = #{ssbm}</if>
      <if test="ssmc != null  and ssmc != ''"> and ssmc = #{ssmc}</if>
      <if test="zjm != null  and zjm != ''"> and zjm = #{zjm}</if>
      <if test="ssbm1 != null  and ssbm1 != ''"> and ssbm1 = #{ssbm1}</if>
      <if test="ssmc1 != null  and ssmc1 != ''"> and ssmc1 = #{ssmc1}</if>
      <if test="ssbm2 != null  and ssbm2 != ''"> and ssbm2 = #{ssbm2}</if>
      <if test="ssmc2 != null  and ssmc2 != ''"> and ssmc2 = #{ssmc2}</if>
      <if test="ssbm3 != null  and ssbm3 != ''"> and ssbm3 = #{ssbm3}</if>
      <if test="ssmc3 != null  and ssmc3 != ''"> and ssmc3 = #{ssmc3}</if>
      <if test="ssbm4 != null  and ssbm4 != ''"> and ssbm4 = #{ssbm4}</if>
      <if test="ssmc4 != null  and ssmc4 != ''"> and ssmc4 = #{ssmc4}</if>
      <if test="type != null  and type != ''"> and TYPE = #{type}</if>
    </where>
  </select>

  <select id="selectSsmlBySsbm" parameterType="String" resultMap="SsmlResult">
    <include refid="selectSsmlVo"/>
    where ssbm = #{ssbm}
  </select>

  <select id="selectSsType" parameterType="String" resultType="String">
    select IFNULL(type,"") as type from ssml
    where ssbm = #{ssbm}
  </select>

  <select id="selectSsxxBySsbm" parameterType="String" resultType="Ssml">
    SELECT MAX(ssmc1) as ssmc1,max(ssmc4) as ssmc4,max(ssmc) as ssmc,max(type) as type
    FROM  ssml  WHERE ssbm LIKE concat(#{ssbm},'%');
  </select>

  <insert id="insertSsml" parameterType="Ssml">
    insert into ssml
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ssbm != null">ssbm,</if>
      <if test="ssmc != null">ssmc,</if>
      <if test="zjm != null">zjm,</if>
      <if test="ssbm1 != null">ssbm1,</if>
      <if test="ssmc1 != null">ssmc1,</if>
      <if test="ssbm2 != null">ssbm2,</if>
      <if test="ssmc2 != null">ssmc2,</if>
      <if test="ssbm3 != null">ssbm3,</if>
      <if test="ssmc3 != null">ssmc3,</if>
      <if test="ssbm4 != null">ssbm4,</if>
      <if test="ssmc4 != null">ssmc4,</if>
      <if test="type != null">TYPE,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ssbm != null">#{ssbm},</if>
      <if test="ssmc != null">#{ssmc},</if>
      <if test="zjm != null">#{zjm},</if>
      <if test="ssbm1 != null">#{ssbm1},</if>
      <if test="ssmc1 != null">#{ssmc1},</if>
      <if test="ssbm2 != null">#{ssbm2},</if>
      <if test="ssmc2 != null">#{ssmc2},</if>
      <if test="ssbm3 != null">#{ssbm3},</if>
      <if test="ssmc3 != null">#{ssmc3},</if>
      <if test="ssbm4 != null">#{ssbm4},</if>
      <if test="ssmc4 != null">#{ssmc4},</if>
      <if test="type != null">#{type},</if>
    </trim>
  </insert>

  <update id="updateSsml" parameterType="Ssml">
    update ssml
    <trim prefix="SET" suffixOverrides=",">
      <if test="ssmc != null">ssmc = #{ssmc},</if>
      <if test="zjm != null">zjm = #{zjm},</if>
      <if test="ssbm1 != null">ssbm1 = #{ssbm1},</if>
      <if test="ssmc1 != null">ssmc1 = #{ssmc1},</if>
      <if test="ssbm2 != null">ssbm2 = #{ssbm2},</if>
      <if test="ssmc2 != null">ssmc2 = #{ssmc2},</if>
      <if test="ssbm3 != null">ssbm3 = #{ssbm3},</if>
      <if test="ssmc3 != null">ssmc3 = #{ssmc3},</if>
      <if test="ssbm4 != null">ssbm4 = #{ssbm4},</if>
      <if test="ssmc4 != null">ssmc4 = #{ssmc4},</if>
      <if test="type != null">TYPE = #{type},</if>
    </trim>
    where ssbm = #{ssbm}
  </update>

  <delete id="deleteSsmlBySsbm" parameterType="String">
    delete from ssml where ssbm = #{ssbm}
  </delete>

  <delete id="deleteSsmlBySsbms" parameterType="String">
    delete from ssml where ssbm in
    <foreach item="ssbm" collection="array" open="(" separator="," close=")">
      #{ssbm}
    </foreach>
  </delete>
</mapper>
