<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ZdpxMapper">

    <resultMap type="Zdpx" id="ZdpxResult">
        <result property="id"    column="id"    />
        <result property="jbbm"    column="jbbm"    />
        <result property="zdmc"    column="zdmc"    />
        <result property="zdcx"    column="zdcx"    />
        <result property="zdlb"    column="zdlb"    />
    </resultMap>

    <sql id="selectZdpxVo">
        select id, jbbm, zdmc, zdcx, zdlb from zdpx
    </sql>

    <select id="selectZpByZdlb" parameterType="String" resultMap="ZdpxResult">
      select * from zdpx where zdlb = #{zdlb}
    </select>

    <select id="selectTlxZd" parameterType="String" resultMap="ZdpxResult">
      select * from zdpx where zdlb = (select zdlb from zdpx where jbbm = #{jbbm} and zdmc = #{zdmc} limit 1) and
        jbbm != #{jbbm} and zdmc != #{zdmc}
    </select>

    <select id="selectZdpxList" parameterType="Zdpx" resultMap="ZdpxResult">
        <include refid="selectZdpxVo"/>
        <where>
            <if test="jbbm != null  and jbbm != ''"> and jbbm = #{jbbm}</if>
            <if test="zdmc != null  and zdmc != ''"> and zdmc = #{zdmc}</if>
            <if test="zdcx != null "> and zdcx = #{zdcx}</if>
            <if test="zdlb != null  and zdlb != ''"> and zdlb = #{zdlb}</if>
        </where>
    </select>

    <select id="selectZdpxById" parameterType="Long" resultMap="ZdpxResult">
        <include refid="selectZdpxVo"/>
        where id = #{id}
    </select>

    <insert id="insertZdpx" parameterType="Zdpx" useGeneratedKeys="true" keyProperty="id">
        insert into zdpx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jbbm != null">jbbm,</if>
            <if test="zdmc != null">zdmc,</if>
            <if test="zdcx != null">zdcx,</if>
            <if test="zdlb != null">zdlb,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jbbm != null">#{jbbm},</if>
            <if test="zdmc != null">#{zdmc},</if>
            <if test="zdcx != null">#{zdcx},</if>
            <if test="zdlb != null">#{zdlb},</if>
         </trim>
    </insert>

    <update id="updateZdpx" parameterType="Zdpx">
        update zdpx
        <trim prefix="SET" suffixOverrides=",">
            <if test="jbbm != null">jbbm = #{jbbm},</if>
            <if test="zdmc != null">zdmc = #{zdmc},</if>
            <if test="zdcx != null">zdcx = #{zdcx},</if>
            <if test="zdlb != null">zdlb = #{zdlb},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZdpxById" parameterType="Long">
        delete from zdpx where id = #{id}
    </delete>

    <delete id="deleteZdpxByIds" parameterType="String">
        delete from zdpx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
