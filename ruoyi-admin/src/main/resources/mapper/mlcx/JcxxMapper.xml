<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JcxxMapper">

    <resultMap type="Jcxx" id="JcxxResult">
        <result property="id"    column="id"    />
        <result property="brbs"    column="brbs"    />
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="jcsj"    column="jcsj"    />
        <result property="jcjl"    column="jcjl"    />
        <result property="doctor"    column="doctor"    />
        <result property="createdate"    column="createdate"    />
        <result property="reportdate"    column="reportdate"    />
        <result property="ldtFromDate"    column="ldtFromDate"    />
        <result property="ldtToDate"    column="ldtToDate"    />
        <result property="jgid"    column="jgid"    />
    </resultMap>

    <sql id="selectJcxxVo">
        select id, brbs, jzh, brid, zyid, xmmc, jcsj, jcjl, doctor, createdate, reportdate from jcxx
    </sql>

<!--    <select id="selectMaxReportDate" parameterType="Integer" resultType="Jcxx">-->
<!--      select ifnull(max(reportdate),'2023-09-01') as ldtFromDate,-->
<!--             DATE_ADD(ifnull(max(reportdate),'2023-09-01'), INTERVAL + #{days} DAY) as ldtToDate-->
<!--      from jcxx-->
<!--      <where>-->
<!--        <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>-->
<!--      </where>-->
<!--    </select>-->

    <select id="selectMaxReportDate" parameterType="String"  resultType="java.time.LocalDateTime">
        select ifnull(max(reportdate),'2023-09-01 00:00:00') as ldtFromDate from jcxx
        <where>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
        </where>
    </select>

    <select id="selectJcxxList" parameterType="Jcxx" resultMap="JcxxResult">
        <include refid="selectJcxxVo"/>
        <where>
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc = #{xmmc}</if>
            <if test="jcsj != null  and jcsj != ''"> and jcsj = #{jcsj}</if>
            <if test="jcjl != null  and jcjl != ''"> and jcjl = #{jcjl}</if>
            <if test="doctor != null  and doctor != ''"> and doctor = #{doctor}</if>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
            <if test="reportdate != null "> and reportdate = #{reportdate}</if>
        </where>
    </select>

    <select id="selectJcxxById" parameterType="Long" resultMap="JcxxResult">
        <include refid="selectJcxxVo"/>
        where id = #{id}
    </select>
    <select id="selectJcxxForChat" resultType="com.ruoyi.system.domain.Jcxx">
      <include refid="selectJcxxVo"/>
      where jzh = #{jzh}
    </select>

    <insert id="insertJcxx" parameterType="Jcxx" useGeneratedKeys="true" keyProperty="id">
        insert into jcxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="jzh != null">jzh,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="jcsj != null">jcsj,</if>
            <if test="jcjl != null">jcjl,</if>
            <if test="doctor != null">doctor,</if>
            <if test="createdate != null">createdate,</if>
            <if test="reportdate != null">reportdate,</if>
            <if test="jgid != null">jgid,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="jzh != null">#{jzh},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="jcsj != null">#{jcsj},</if>
            <if test="jcjl != null">#{jcjl},</if>
            <if test="doctor != null">#{doctor},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="reportdate != null">#{reportdate},</if>
            <if test="jgid != null">#{jgid},</if>
         </trim>
        on duplicate key update
          brbs = values(brbs),
          reportdate = values(reportdate),
          xmmc = values(xmmc);
    </insert>

    <update id="updateJcxx" parameterType="Jcxx">
        update jcxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="brbs != null">brbs = #{brbs},</if>
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="jcsj != null">jcsj = #{jcsj},</if>
            <if test="jcjl != null">jcjl = #{jcjl},</if>
            <if test="doctor != null">doctor = #{doctor},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="reportdate != null">reportdate = #{reportdate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJcxxById" parameterType="Long">
        delete from jcxx where id = #{id}
    </delete>

    <delete id="deleteJcxxByIds" parameterType="String">
        delete from jcxx where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
