<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ZlxmMapper">
    
    <resultMap type="Zlxm" id="ZlxmResult">
        <result property="lbdm1"    column="lbdm1"    />
        <result property="lbdm2"    column="lbdm2"    />
        <result property="lbdm3"    column="lbdm3"    />
        <result property="lbdm4"    column="lbdm4"    />
        <result property="xmlsh"    column="xmlsh"    />
        <result property="xmbm"    column="xmbm"    />
        <result property="xmmc"    column="xmmc"    />
        <result property="zjm"    column="zjm"    />
        <result property="tpj"    column="tpj"    />
        <result property="ylbzj"    column="ylbzj"    />
        <result property="gsbzj"    column="gsbzj"    />
        <result property="sybzj"    column="sybzj"    />
        <result property="dw"    column="dw"    />
        <result property="ylfydj"    column="ylfydj"    />
        <result property="gsfydj"    column="gsfydj"    />
        <result property="syfydj"    column="syfydj"    />
        <result property="ylzfbl"    column="ylzfbl"    />
        <result property="gszfbl"    column="gszfbl"    />
        <result property="syzfbl"    column="syzfbl"    />
        <result property="txbl"    column="txbl"    />
        <result property="xjfs"    column="xjfs"    />
        <result property="lsf"    column="lsf"    />
        <result property="bz"    column="bz"    />
        <result property="bgsj"    column="bgsj"    />
        <result property="tpxmbz"    column="tpxmbz"    />
        <result property="tqfydj"    column="tqfydj"    />
        <result property="tqzfbl"    column="tqzfbl"    />
        <result property="tqbzdj"    column="tqbzdj"    />
        <result property="xmnh"    column="xmnh"    />
        <result property="cwnr"    column="cwnr"    />
        <result property="cjzfbl"    column="cjzfbl"    />
        <result property="cjbzdj"    column="cjbzdj"    />
        <result property="cjfydj"    column="cjfydj"    />
        <result property="bzj"    column="bzj"    />
        <result property="zzbl"    column="zzbl"    />
        <result property="fydj"    column="fydj"    />
        <result property="gsfzqjbj"    column="gsfzqjbj"    />
        <result property="gskfxmbj"    column="gskfxmbj"    />
        <result property="gsfpgxxmbj"    column="gsfpgxxmbj"    />
        <result property="gsktfbj"    column="gsktfbj"    />
        <result property="tsytbj"    column="tsytbj"    />
        <result property="nhycxhc"    column="nhycxhc"    />
        <result property="jjsm"    column="jjsm"    />
        <result property="zgcfjl"    column="zgcfjl"    />
        <result property="zgcfyl15"    column="zgcfyl15"    />
        <result property="zgcfyl20"    column="zgcfyl20"    />
        <result property="zgcfcb"    column="zgcfcb"    />
        <result property="zgcfzf"    column="zgcfzf"    />
        <result property="jmcfjl"    column="jmcfjl"    />
        <result property="jmcfyl15"    column="jmcfyl15"    />
        <result property="jmcfyl20"    column="jmcfyl20"    />
        <result property="jmcfcb"    column="jmcfcb"    />
        <result property="jmcfzf"    column="jmcfzf"    />
        <result property="gscfjl"    column="gscfjl"    />
        <result property="gscfcb"    column="gscfcb"    />
        <result property="gscfzf"    column="gscfzf"    />
        <result property="sycfjl"    column="sycfjl"    />
        <result property="sycfyl15"    column="sycfyl15"    />
        <result property="sycfyl20"    column="sycfyl20"    />
        <result property="sycfcb"    column="sycfcb"    />
        <result property="sycfzf"    column="sycfzf"    />
        <result property="tqcfjl"    column="tqcfjl"    />
        <result property="tqcfyl15"    column="tqcfyl15"    />
        <result property="tqcfyl20"    column="tqcfyl20"    />
        <result property="tqcfcb"    column="tqcfcb"    />
        <result property="tqcfzf"    column="tqcfzf"    />
        <result property="zzsj"    column="zzsj"    />
        <result property="jbsj"    column="jbsj"    />
        <result property="zgdebxbz"    column="zgdebxbz"    />
        <result property="jmdebxbz"    column="jmdebxbz"    />
        <result property="ylggxmbj"    column="ylggxmbj"    />
        <result property="gjxmdm"    column="gjxmdm"    />
        <result property="cGjxmfl"    column="c_gjxmfl"    />
    </resultMap>

    <sql id="selectZlxmVo">
        select lbdm1, lbdm2, lbdm3, lbdm4, xmlsh, xmbm, xmmc, zjm, tpj, ylbzj, gsbzj, sybzj, dw, ylfydj, gsfydj, syfydj, ylzfbl, gszfbl, syzfbl, txbl, xjfs, lsf, bz, bgsj, tpxmbz, tqfydj, tqzfbl, tqbzdj, xmnh, cwnr, cjzfbl, cjbzdj, cjfydj, bzj, zzbl, fydj, gsfzqjbj, gskfxmbj, gsfpgxxmbj, gsktfbj, tsytbj, nhycxhc, jjsm, zgcfjl, zgcfyl15, zgcfyl20, zgcfcb, zgcfzf, jmcfjl, jmcfyl15, jmcfyl20, jmcfcb, jmcfzf, gscfjl, gscfcb, gscfzf, sycfjl, sycfyl15, sycfyl20, sycfcb, sycfzf, tqcfjl, tqcfyl15, tqcfyl20, tqcfcb, tqcfzf, zzsj, jbsj, zgdebxbz, jmdebxbz, ylggxmbj, gjxmdm, c_gjxmfl from zlxm
    </sql>

    <select id="selectZlxmList" parameterType="Zlxm" resultMap="ZlxmResult">
        <include refid="selectZlxmVo"/>
        <where>  
            <if test="lbdm1 != null  and lbdm1 != ''"> and lbdm1 = #{lbdm1}</if>
            <if test="lbdm2 != null  and lbdm2 != ''"> and lbdm2 = #{lbdm2}</if>
            <if test="lbdm3 != null  and lbdm3 != ''"> and lbdm3 = #{lbdm3}</if>
            <if test="lbdm4 != null  and lbdm4 != ''"> and lbdm4 = #{lbdm4}</if>
            <if test="xmbm != null  and xmbm != ''"> and xmbm = #{xmbm}</if>
            <if test="xmmc != null  and xmmc != ''"> and xmmc = #{xmmc}</if>
            <if test="zjm != null  and zjm != ''"> and zjm = #{zjm}</if>
            <if test="tpj != null "> and tpj = #{tpj}</if>
            <if test="ylbzj != null "> and ylbzj = #{ylbzj}</if>
            <if test="gsbzj != null "> and gsbzj = #{gsbzj}</if>
            <if test="sybzj != null "> and sybzj = #{sybzj}</if>
            <if test="dw != null  and dw != ''"> and dw = #{dw}</if>
            <if test="ylfydj != null  and ylfydj != ''"> and ylfydj = #{ylfydj}</if>
            <if test="gsfydj != null  and gsfydj != ''"> and gsfydj = #{gsfydj}</if>
            <if test="syfydj != null  and syfydj != ''"> and syfydj = #{syfydj}</if>
            <if test="ylzfbl != null "> and ylzfbl = #{ylzfbl}</if>
            <if test="gszfbl != null "> and gszfbl = #{gszfbl}</if>
            <if test="syzfbl != null "> and syzfbl = #{syzfbl}</if>
            <if test="txbl != null "> and txbl = #{txbl}</if>
            <if test="xjfs != null  and xjfs != ''"> and xjfs = #{xjfs}</if>
            <if test="lsf != null  and lsf != ''"> and lsf = #{lsf}</if>
            <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
            <if test="bgsj != null "> and bgsj = #{bgsj}</if>
            <if test="tpxmbz != null  and tpxmbz != ''"> and tpxmbz = #{tpxmbz}</if>
            <if test="tqfydj != null  and tqfydj != ''"> and tqfydj = #{tqfydj}</if>
            <if test="tqzfbl != null "> and tqzfbl = #{tqzfbl}</if>
            <if test="tqbzdj != null "> and tqbzdj = #{tqbzdj}</if>
            <if test="xmnh != null  and xmnh != ''"> and xmnh = #{xmnh}</if>
            <if test="cwnr != null  and cwnr != ''"> and cwnr = #{cwnr}</if>
            <if test="cjzfbl != null "> and cjzfbl = #{cjzfbl}</if>
            <if test="cjbzdj != null "> and cjbzdj = #{cjbzdj}</if>
            <if test="cjfydj != null  and cjfydj != ''"> and cjfydj = #{cjfydj}</if>
            <if test="bzj != null "> and bzj = #{bzj}</if>
            <if test="zzbl != null "> and zzbl = #{zzbl}</if>
            <if test="fydj != null  and fydj != ''"> and fydj = #{fydj}</if>
            <if test="gsfzqjbj != null  and gsfzqjbj != ''"> and gsfzqjbj = #{gsfzqjbj}</if>
            <if test="gskfxmbj != null  and gskfxmbj != ''"> and gskfxmbj = #{gskfxmbj}</if>
            <if test="gsfpgxxmbj != null  and gsfpgxxmbj != ''"> and gsfpgxxmbj = #{gsfpgxxmbj}</if>
            <if test="gsktfbj != null  and gsktfbj != ''"> and gsktfbj = #{gsktfbj}</if>
            <if test="tsytbj != null  and tsytbj != ''"> and tsytbj = #{tsytbj}</if>
            <if test="nhycxhc != null  and nhycxhc != ''"> and nhycxhc = #{nhycxhc}</if>
            <if test="jjsm != null  and jjsm != ''"> and jjsm = #{jjsm}</if>
            <if test="zgcfjl != null "> and zgcfjl = #{zgcfjl}</if>
            <if test="zgcfyl15 != null "> and zgcfyl15 = #{zgcfyl15}</if>
            <if test="zgcfyl20 != null "> and zgcfyl20 = #{zgcfyl20}</if>
            <if test="zgcfcb != null "> and zgcfcb = #{zgcfcb}</if>
            <if test="zgcfzf != null "> and zgcfzf = #{zgcfzf}</if>
            <if test="jmcfjl != null "> and jmcfjl = #{jmcfjl}</if>
            <if test="jmcfyl15 != null "> and jmcfyl15 = #{jmcfyl15}</if>
            <if test="jmcfyl20 != null "> and jmcfyl20 = #{jmcfyl20}</if>
            <if test="jmcfcb != null "> and jmcfcb = #{jmcfcb}</if>
            <if test="jmcfzf != null "> and jmcfzf = #{jmcfzf}</if>
            <if test="gscfjl != null "> and gscfjl = #{gscfjl}</if>
            <if test="gscfcb != null "> and gscfcb = #{gscfcb}</if>
            <if test="gscfzf != null "> and gscfzf = #{gscfzf}</if>
            <if test="sycfjl != null "> and sycfjl = #{sycfjl}</if>
            <if test="sycfyl15 != null "> and sycfyl15 = #{sycfyl15}</if>
            <if test="sycfyl20 != null "> and sycfyl20 = #{sycfyl20}</if>
            <if test="sycfcb != null "> and sycfcb = #{sycfcb}</if>
            <if test="sycfzf != null "> and sycfzf = #{sycfzf}</if>
            <if test="tqcfjl != null "> and tqcfjl = #{tqcfjl}</if>
            <if test="tqcfyl15 != null "> and tqcfyl15 = #{tqcfyl15}</if>
            <if test="tqcfyl20 != null "> and tqcfyl20 = #{tqcfyl20}</if>
            <if test="tqcfcb != null "> and tqcfcb = #{tqcfcb}</if>
            <if test="tqcfzf != null "> and tqcfzf = #{tqcfzf}</if>
            <if test="zzsj != null "> and zzsj = #{zzsj}</if>
            <if test="jbsj != null "> and jbsj = #{jbsj}</if>
            <if test="zgdebxbz != null "> and zgdebxbz = #{zgdebxbz}</if>
            <if test="jmdebxbz != null "> and jmdebxbz = #{jmdebxbz}</if>
            <if test="ylggxmbj != null  and ylggxmbj != ''"> and ylggxmbj = #{ylggxmbj}</if>
            <if test="gjxmdm != null  and gjxmdm != ''"> and gjxmdm = #{gjxmdm}</if>
            <if test="cGjxmfl != null  and cGjxmfl != ''"> and c_gjxmfl = #{cGjxmfl}</if>
        </where>
    </select>
    
    <select id="selectZlxmByXmlsh" parameterType="String" resultMap="ZlxmResult">
        <include refid="selectZlxmVo"/>
        where xmlsh = #{xmlsh}
    </select>
        
    <insert id="insertZlxm" parameterType="Zlxm">
        insert into zlxm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lbdm1 != null">lbdm1,</if>
            <if test="lbdm2 != null">lbdm2,</if>
            <if test="lbdm3 != null">lbdm3,</if>
            <if test="lbdm4 != null">lbdm4,</if>
            <if test="xmlsh != null">xmlsh,</if>
            <if test="xmbm != null">xmbm,</if>
            <if test="xmmc != null">xmmc,</if>
            <if test="zjm != null">zjm,</if>
            <if test="tpj != null">tpj,</if>
            <if test="ylbzj != null">ylbzj,</if>
            <if test="gsbzj != null">gsbzj,</if>
            <if test="sybzj != null">sybzj,</if>
            <if test="dw != null">dw,</if>
            <if test="ylfydj != null">ylfydj,</if>
            <if test="gsfydj != null">gsfydj,</if>
            <if test="syfydj != null">syfydj,</if>
            <if test="ylzfbl != null">ylzfbl,</if>
            <if test="gszfbl != null">gszfbl,</if>
            <if test="syzfbl != null">syzfbl,</if>
            <if test="txbl != null">txbl,</if>
            <if test="xjfs != null">xjfs,</if>
            <if test="lsf != null">lsf,</if>
            <if test="bz != null">bz,</if>
            <if test="bgsj != null">bgsj,</if>
            <if test="tpxmbz != null">tpxmbz,</if>
            <if test="tqfydj != null">tqfydj,</if>
            <if test="tqzfbl != null">tqzfbl,</if>
            <if test="tqbzdj != null">tqbzdj,</if>
            <if test="xmnh != null">xmnh,</if>
            <if test="cwnr != null">cwnr,</if>
            <if test="cjzfbl != null">cjzfbl,</if>
            <if test="cjbzdj != null">cjbzdj,</if>
            <if test="cjfydj != null">cjfydj,</if>
            <if test="bzj != null">bzj,</if>
            <if test="zzbl != null">zzbl,</if>
            <if test="fydj != null">fydj,</if>
            <if test="gsfzqjbj != null">gsfzqjbj,</if>
            <if test="gskfxmbj != null">gskfxmbj,</if>
            <if test="gsfpgxxmbj != null">gsfpgxxmbj,</if>
            <if test="gsktfbj != null">gsktfbj,</if>
            <if test="tsytbj != null">tsytbj,</if>
            <if test="nhycxhc != null">nhycxhc,</if>
            <if test="jjsm != null">jjsm,</if>
            <if test="zgcfjl != null">zgcfjl,</if>
            <if test="zgcfyl15 != null">zgcfyl15,</if>
            <if test="zgcfyl20 != null">zgcfyl20,</if>
            <if test="zgcfcb != null">zgcfcb,</if>
            <if test="zgcfzf != null">zgcfzf,</if>
            <if test="jmcfjl != null">jmcfjl,</if>
            <if test="jmcfyl15 != null">jmcfyl15,</if>
            <if test="jmcfyl20 != null">jmcfyl20,</if>
            <if test="jmcfcb != null">jmcfcb,</if>
            <if test="jmcfzf != null">jmcfzf,</if>
            <if test="gscfjl != null">gscfjl,</if>
            <if test="gscfcb != null">gscfcb,</if>
            <if test="gscfzf != null">gscfzf,</if>
            <if test="sycfjl != null">sycfjl,</if>
            <if test="sycfyl15 != null">sycfyl15,</if>
            <if test="sycfyl20 != null">sycfyl20,</if>
            <if test="sycfcb != null">sycfcb,</if>
            <if test="sycfzf != null">sycfzf,</if>
            <if test="tqcfjl != null">tqcfjl,</if>
            <if test="tqcfyl15 != null">tqcfyl15,</if>
            <if test="tqcfyl20 != null">tqcfyl20,</if>
            <if test="tqcfcb != null">tqcfcb,</if>
            <if test="tqcfzf != null">tqcfzf,</if>
            <if test="zzsj != null">zzsj,</if>
            <if test="jbsj != null">jbsj,</if>
            <if test="zgdebxbz != null">zgdebxbz,</if>
            <if test="jmdebxbz != null">jmdebxbz,</if>
            <if test="ylggxmbj != null">ylggxmbj,</if>
            <if test="gjxmdm != null">gjxmdm,</if>
            <if test="cGjxmfl != null">c_gjxmfl,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lbdm1 != null">#{lbdm1},</if>
            <if test="lbdm2 != null">#{lbdm2},</if>
            <if test="lbdm3 != null">#{lbdm3},</if>
            <if test="lbdm4 != null">#{lbdm4},</if>
            <if test="xmlsh != null">#{xmlsh},</if>
            <if test="xmbm != null">#{xmbm},</if>
            <if test="xmmc != null">#{xmmc},</if>
            <if test="zjm != null">#{zjm},</if>
            <if test="tpj != null">#{tpj},</if>
            <if test="ylbzj != null">#{ylbzj},</if>
            <if test="gsbzj != null">#{gsbzj},</if>
            <if test="sybzj != null">#{sybzj},</if>
            <if test="dw != null">#{dw},</if>
            <if test="ylfydj != null">#{ylfydj},</if>
            <if test="gsfydj != null">#{gsfydj},</if>
            <if test="syfydj != null">#{syfydj},</if>
            <if test="ylzfbl != null">#{ylzfbl},</if>
            <if test="gszfbl != null">#{gszfbl},</if>
            <if test="syzfbl != null">#{syzfbl},</if>
            <if test="txbl != null">#{txbl},</if>
            <if test="xjfs != null">#{xjfs},</if>
            <if test="lsf != null">#{lsf},</if>
            <if test="bz != null">#{bz},</if>
            <if test="bgsj != null">#{bgsj},</if>
            <if test="tpxmbz != null">#{tpxmbz},</if>
            <if test="tqfydj != null">#{tqfydj},</if>
            <if test="tqzfbl != null">#{tqzfbl},</if>
            <if test="tqbzdj != null">#{tqbzdj},</if>
            <if test="xmnh != null">#{xmnh},</if>
            <if test="cwnr != null">#{cwnr},</if>
            <if test="cjzfbl != null">#{cjzfbl},</if>
            <if test="cjbzdj != null">#{cjbzdj},</if>
            <if test="cjfydj != null">#{cjfydj},</if>
            <if test="bzj != null">#{bzj},</if>
            <if test="zzbl != null">#{zzbl},</if>
            <if test="fydj != null">#{fydj},</if>
            <if test="gsfzqjbj != null">#{gsfzqjbj},</if>
            <if test="gskfxmbj != null">#{gskfxmbj},</if>
            <if test="gsfpgxxmbj != null">#{gsfpgxxmbj},</if>
            <if test="gsktfbj != null">#{gsktfbj},</if>
            <if test="tsytbj != null">#{tsytbj},</if>
            <if test="nhycxhc != null">#{nhycxhc},</if>
            <if test="jjsm != null">#{jjsm},</if>
            <if test="zgcfjl != null">#{zgcfjl},</if>
            <if test="zgcfyl15 != null">#{zgcfyl15},</if>
            <if test="zgcfyl20 != null">#{zgcfyl20},</if>
            <if test="zgcfcb != null">#{zgcfcb},</if>
            <if test="zgcfzf != null">#{zgcfzf},</if>
            <if test="jmcfjl != null">#{jmcfjl},</if>
            <if test="jmcfyl15 != null">#{jmcfyl15},</if>
            <if test="jmcfyl20 != null">#{jmcfyl20},</if>
            <if test="jmcfcb != null">#{jmcfcb},</if>
            <if test="jmcfzf != null">#{jmcfzf},</if>
            <if test="gscfjl != null">#{gscfjl},</if>
            <if test="gscfcb != null">#{gscfcb},</if>
            <if test="gscfzf != null">#{gscfzf},</if>
            <if test="sycfjl != null">#{sycfjl},</if>
            <if test="sycfyl15 != null">#{sycfyl15},</if>
            <if test="sycfyl20 != null">#{sycfyl20},</if>
            <if test="sycfcb != null">#{sycfcb},</if>
            <if test="sycfzf != null">#{sycfzf},</if>
            <if test="tqcfjl != null">#{tqcfjl},</if>
            <if test="tqcfyl15 != null">#{tqcfyl15},</if>
            <if test="tqcfyl20 != null">#{tqcfyl20},</if>
            <if test="tqcfcb != null">#{tqcfcb},</if>
            <if test="tqcfzf != null">#{tqcfzf},</if>
            <if test="zzsj != null">#{zzsj},</if>
            <if test="jbsj != null">#{jbsj},</if>
            <if test="zgdebxbz != null">#{zgdebxbz},</if>
            <if test="jmdebxbz != null">#{jmdebxbz},</if>
            <if test="ylggxmbj != null">#{ylggxmbj},</if>
            <if test="gjxmdm != null">#{gjxmdm},</if>
            <if test="cGjxmfl != null">#{cGjxmfl},</if>
         </trim>
    </insert>

    <update id="updateZlxm" parameterType="Zlxm">
        update zlxm
        <trim prefix="SET" suffixOverrides=",">
            <if test="lbdm1 != null">lbdm1 = #{lbdm1},</if>
            <if test="lbdm2 != null">lbdm2 = #{lbdm2},</if>
            <if test="lbdm3 != null">lbdm3 = #{lbdm3},</if>
            <if test="lbdm4 != null">lbdm4 = #{lbdm4},</if>
            <if test="xmbm != null">xmbm = #{xmbm},</if>
            <if test="xmmc != null">xmmc = #{xmmc},</if>
            <if test="zjm != null">zjm = #{zjm},</if>
            <if test="tpj != null">tpj = #{tpj},</if>
            <if test="ylbzj != null">ylbzj = #{ylbzj},</if>
            <if test="gsbzj != null">gsbzj = #{gsbzj},</if>
            <if test="sybzj != null">sybzj = #{sybzj},</if>
            <if test="dw != null">dw = #{dw},</if>
            <if test="ylfydj != null">ylfydj = #{ylfydj},</if>
            <if test="gsfydj != null">gsfydj = #{gsfydj},</if>
            <if test="syfydj != null">syfydj = #{syfydj},</if>
            <if test="ylzfbl != null">ylzfbl = #{ylzfbl},</if>
            <if test="gszfbl != null">gszfbl = #{gszfbl},</if>
            <if test="syzfbl != null">syzfbl = #{syzfbl},</if>
            <if test="txbl != null">txbl = #{txbl},</if>
            <if test="xjfs != null">xjfs = #{xjfs},</if>
            <if test="lsf != null">lsf = #{lsf},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="bgsj != null">bgsj = #{bgsj},</if>
            <if test="tpxmbz != null">tpxmbz = #{tpxmbz},</if>
            <if test="tqfydj != null">tqfydj = #{tqfydj},</if>
            <if test="tqzfbl != null">tqzfbl = #{tqzfbl},</if>
            <if test="tqbzdj != null">tqbzdj = #{tqbzdj},</if>
            <if test="xmnh != null">xmnh = #{xmnh},</if>
            <if test="cwnr != null">cwnr = #{cwnr},</if>
            <if test="cjzfbl != null">cjzfbl = #{cjzfbl},</if>
            <if test="cjbzdj != null">cjbzdj = #{cjbzdj},</if>
            <if test="cjfydj != null">cjfydj = #{cjfydj},</if>
            <if test="bzj != null">bzj = #{bzj},</if>
            <if test="zzbl != null">zzbl = #{zzbl},</if>
            <if test="fydj != null">fydj = #{fydj},</if>
            <if test="gsfzqjbj != null">gsfzqjbj = #{gsfzqjbj},</if>
            <if test="gskfxmbj != null">gskfxmbj = #{gskfxmbj},</if>
            <if test="gsfpgxxmbj != null">gsfpgxxmbj = #{gsfpgxxmbj},</if>
            <if test="gsktfbj != null">gsktfbj = #{gsktfbj},</if>
            <if test="tsytbj != null">tsytbj = #{tsytbj},</if>
            <if test="nhycxhc != null">nhycxhc = #{nhycxhc},</if>
            <if test="jjsm != null">jjsm = #{jjsm},</if>
            <if test="zgcfjl != null">zgcfjl = #{zgcfjl},</if>
            <if test="zgcfyl15 != null">zgcfyl15 = #{zgcfyl15},</if>
            <if test="zgcfyl20 != null">zgcfyl20 = #{zgcfyl20},</if>
            <if test="zgcfcb != null">zgcfcb = #{zgcfcb},</if>
            <if test="zgcfzf != null">zgcfzf = #{zgcfzf},</if>
            <if test="jmcfjl != null">jmcfjl = #{jmcfjl},</if>
            <if test="jmcfyl15 != null">jmcfyl15 = #{jmcfyl15},</if>
            <if test="jmcfyl20 != null">jmcfyl20 = #{jmcfyl20},</if>
            <if test="jmcfcb != null">jmcfcb = #{jmcfcb},</if>
            <if test="jmcfzf != null">jmcfzf = #{jmcfzf},</if>
            <if test="gscfjl != null">gscfjl = #{gscfjl},</if>
            <if test="gscfcb != null">gscfcb = #{gscfcb},</if>
            <if test="gscfzf != null">gscfzf = #{gscfzf},</if>
            <if test="sycfjl != null">sycfjl = #{sycfjl},</if>
            <if test="sycfyl15 != null">sycfyl15 = #{sycfyl15},</if>
            <if test="sycfyl20 != null">sycfyl20 = #{sycfyl20},</if>
            <if test="sycfcb != null">sycfcb = #{sycfcb},</if>
            <if test="sycfzf != null">sycfzf = #{sycfzf},</if>
            <if test="tqcfjl != null">tqcfjl = #{tqcfjl},</if>
            <if test="tqcfyl15 != null">tqcfyl15 = #{tqcfyl15},</if>
            <if test="tqcfyl20 != null">tqcfyl20 = #{tqcfyl20},</if>
            <if test="tqcfcb != null">tqcfcb = #{tqcfcb},</if>
            <if test="tqcfzf != null">tqcfzf = #{tqcfzf},</if>
            <if test="zzsj != null">zzsj = #{zzsj},</if>
            <if test="jbsj != null">jbsj = #{jbsj},</if>
            <if test="zgdebxbz != null">zgdebxbz = #{zgdebxbz},</if>
            <if test="jmdebxbz != null">jmdebxbz = #{jmdebxbz},</if>
            <if test="ylggxmbj != null">ylggxmbj = #{ylggxmbj},</if>
            <if test="gjxmdm != null">gjxmdm = #{gjxmdm},</if>
            <if test="cGjxmfl != null">c_gjxmfl = #{cGjxmfl},</if>
        </trim>
        where xmlsh = #{xmlsh}
    </update>

    <delete id="deleteZlxmByXmlsh" parameterType="String">
        delete from zlxm where xmlsh = #{xmlsh}
    </delete>

    <delete id="deleteZlxmByXmlshs" parameterType="String">
        delete from zlxm where xmlsh in 
        <foreach item="xmlsh" collection="array" open="(" separator="," close=")">
            #{xmlsh}
        </foreach>
    </delete>
</mapper>