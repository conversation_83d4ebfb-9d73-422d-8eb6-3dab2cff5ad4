<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.YpmlMapper">

  <resultMap type="Ypml" id="YpmlResult">
    <result property="yplsh" column="yplsh"/>
    <result property="ypbm" column="ypbm"/>
    <result property="tym" column="tym"/>
    <result property="tymzjm" column="tymzjm"/>
    <result property="spm" column="spm"/>
    <result property="spmzjm" column="spmzjm"/>
    <result property="ywm" column="ywm"/>
    <result property="lbdm" column="lbdm"/>
    <result property="cfybz" column="cfybz"/>
    <result property="ylfydj" column="ylfydj"/>
    <result property="gsfydj" column="gsfydj"/>
    <result property="syfydj" column="syfydj"/>
    <result property="pfj" column="pfj"/>
    <result property="ylbzdj" column="ylbzdj"/>
    <result property="gsbzdj" column="gsbzdj"/>
    <result property="sybzdj" column="sybzdj"/>
    <result property="ylzfbl" column="ylzfbl"/>
    <result property="gszfbl" column="gszfbl"/>
    <result property="syzfbl" column="syzfbl"/>
    <result property="jx" column="jx"/>
    <result property="bzsl" column="bzsl"/>
    <result property="bzdw" column="bzdw"/>
    <result property="hl" column="hl"/>
    <result property="hldw" column="hldw"/>
    <result property="rl" column="rl"/>
    <result property="rldw" column="rldw"/>
    <result property="gmp" column="gmp"/>
    <result property="ycmc" column="ycmc"/>
    <result property="ypxjfs" column="ypxjfs"/>
    <result property="bgsj" column="bgsj"/>
    <result property="tqfydj" column="tqfydj"/>
    <result property="tqzfbl" column="tqzfbl"/>
    <result property="tqbzdj" column="tqbzdj"/>
    <result property="bz" column="bz"/>
    <result property="cjzfbl" column="cjzfbl"/>
    <result property="cjbzdj" column="cjbzdj"/>
    <result property="cjfydj" column="cjfydj"/>
    <result property="wsssybz" column="wsssybz"/>
    <result property="xetsybz" column="xetsybz"/>
    <result property="xmzsybz" column="xmzsybz"/>
    <result property="jcybz" column="jcybz"/>
    <result property="zzsjbz" column="zzsjbz"/>
    <result property="zzsjsbjg" column="zzsjsbjg"/>
    <result property="bzdj" column="bzdj"/>
    <result property="zfbl" column="zfbl"/>
    <result property="fydj" column="fydj"/>
    <result property="gsfzqjbj" column="gsfzqjbj"/>
    <result property="gskfxmbj" column="gskfxmbj"/>
    <result property="gsfpgxxmbl" column="gsfpgxxmbl"/>
    <result property="jbsj" column="jbsj"/>
    <result property="tqxmdj" column="tqxmdj"/>
    <result property="starttime" column="starttime"/>
    <result property="endtime" column="endtime"/>
    <result property="cCoId" column="c_co_id"/>
    <result property="cJx" column="c_jx"/>
    <result property="cPym" column="c_pym"/>
    <result property="changeMask" column="change_mask"/>
    <result property="fylb" column="fylb"/>
    <result property="bzsm" column="bzsm"/>
    <result property="ycbm" column="ycbm"/>
    <result property="flm" column="flm"/>
    <result property="flypxh" column="flypxh"/>
    <result property="xpmm" column="xpmm"/>
    <result property="jxm" column="jxm"/>
    <result property="ggm" column="ggm"/>
    <result property="zlm" column="zlm"/>
    <result property="bzm" column="bzm"/>
    <result property="tqxmbz" column="tqxmbz"/>
    <result property="sypc" column="sypc"/>
    <result property="ypyf" column="ypyf"/>
    <result property="ypgg" column="ypgg"/>
    <result property="yxbsm" column="yxbsm"/>
    <result property="cBxblXjyy" column="c_bxbl_xjyy"/>
    <result property="cBxblZxzwsy" column="c_bxbl_zxzwsy"/>
    <result property="cBxblXzwsy" column="c_bxbl_xzwsy"/>
    <result property="cGsfydj" column="c_gsfydj"/>
    <result property="cSyfydj" column="c_syfydj"/>
    <result property="cGszfbl" column="c_gszfbl"/>
    <result property="cSyzfbl" column="c_syzfbl"/>
    <result property="cHyfydj" column="c_hyfydj"/>
    <result property="cCwbsm" column="c_cwbsm"/>
    <result property="cRtbsm" column="c_rtbsm"/>
    <result property="cMzbsm" column="c_mzbsm"/>
    <result property="cJcbsm" column="c_jcbsm"/>
    <result property="cZzbsm" column="c_zzbsm"/>
    <result property="cZzsbjg" column="c_zzsbjg"/>
    <result property="wjbm" column="wjbm"/>
    <result property="cGjypdm" column="c_gjypdm"/>
    <result property="cZcjx" column="c_zcjx"/>
    <result property="cZcgg" column="c_zcgg"/>
    <result property="cSjgg" column="c_sjgg"/>
    <result property="cBzcz" column="c_bzcz"/>
    <result property="cZxbzsl" column="c_zxbzsl"/>
    <result property="cZxbzdw" column="c_zxbzdw"/>
    <result property="cZxzjdw" column="c_zxzjdw"/>
    <result property="cPzwh" column="c_pzwh"/>
    <result property="cYpbwm" column="c_ypbwm"/>
    <result property="cYcmc1" column="c_ycmc1"/>
    <result property="cGxfl" column="c_gxfl"/>
    <result property="cYckly" column="c_yckly"/>
    <result property="cYczly" column="c_yczly"/>
    <result property="cYybw" column="c_yybw"/>
    <result property="cPzff" column="c_pzff"/>
    <result property="cXwygj" column="c_xwygj"/>
    <result property="cGnyzz" column="c_gnyzz"/>
    <result property="cYfyyl" column="c_yfyyl"/>
    <result property="gjypdm" column="gjypdm"/>
    <result property="mcyl" column="mcyl"/>
  </resultMap>

  <sql id="selectYpmlVo">
    select yplsh,
           ypbm,
           tym,
           tymzjm,
           spm,
           spmzjm,
           ywm,
           lbdm,
           cfybz,
           ylfydj,
           gsfydj,
           syfydj,
           pfj,
           ylbzdj,
           gsbzdj,
           sybzdj,
           ylzfbl,
           gszfbl,
           syzfbl,
           jx,
           bzsl,
           bzdw,
           hl,
           hldw,
           rl,
           rldw,
           gmp,
           ycmc,
           ypxjfs,
           bgsj,
           tqfydj,
           tqzfbl,
           tqbzdj,
           bz,
           cjzfbl,
           cjbzdj,
           cjfydj,
           wsssybz,
           xetsybz,
           xmzsybz,
           jcybz,
           zzsjbz,
           zzsjsbjg,
           bzdj,
           zfbl,
           fydj,
           gsfzqjbj,
           gskfxmbj,
           gsfpgxxmbl,
           jbsj,
           tqxmdj,
           starttime,
           endtime,
           c_co_id,
           c_jx,
           c_pym,
           change_mask,
           fylb,
           bzsm,
           ycbm,
           flm,
           flypxh,
           xpmm,
           jxm,
           ggm,
           zlm,
           bzm,
           tqxmbz,
           sypc,
           ypyf,
           ypgg,
           yxbsm,
           c_bxbl_xjyy,
           c_bxbl_zxzwsy,
           c_bxbl_xzwsy,
           c_gsfydj,
           c_syfydj,
           c_gszfbl,
           c_syzfbl,
           c_hyfydj,
           c_cwbsm,
           c_rtbsm,
           c_mzbsm,
           c_jcbsm,
           c_zzbsm,
           c_zzsbjg,
           wjbm,
           c_gjypdm,
           c_zcjx,
           c_zcgg,
           c_sjgg,
           c_bzcz,
           c_zxbzsl,
           c_zxbzdw,
           c_zxzjdw,
           c_pzwh,
           c_ypbwm,
           c_ycmc1,
           c_gxfl,
           c_yckly,
           c_yczly,
           c_yybw,
           c_pzff,
           c_xwygj,
           c_gnyzz,
           c_yfyyl,
           gjypdm,
           mcyl
    from ypml
  </sql>


  <select id="selectYpmlList" parameterType="Ypml" resultMap="YpmlResult">
    <include refid="selectYpmlVo"/>
    <where>
      <if test="ypbm != null  and ypbm != ''">and ypbm = #{ypbm}</if>
      <if test="tym != null  and tym != ''">and tym = #{tym}</if>
      <if test="tymzjm != null  and tymzjm != ''">and tymzjm = #{tymzjm}</if>
      <if test="spm != null  and spm != ''">and spm = #{spm}</if>
      <if test="spmzjm != null  and spmzjm != ''">and spmzjm = #{spmzjm}</if>
      <if test="ywm != null  and ywm != ''">and ywm = #{ywm}</if>
      <if test="lbdm != null  and lbdm != ''">and lbdm = #{lbdm}</if>
      <if test="cfybz != null  and cfybz != ''">and cfybz = #{cfybz}</if>
      <if test="ylfydj != null  and ylfydj != ''">and ylfydj = #{ylfydj}</if>
      <if test="gsfydj != null  and gsfydj != ''">and gsfydj = #{gsfydj}</if>
      <if test="syfydj != null  and syfydj != ''">and syfydj = #{syfydj}</if>
      <if test="pfj != null ">and pfj = #{pfj}</if>
      <if test="ylbzdj != null ">and ylbzdj = #{ylbzdj}</if>
      <if test="gsbzdj != null ">and gsbzdj = #{gsbzdj}</if>
      <if test="sybzdj != null ">and sybzdj = #{sybzdj}</if>
      <if test="ylzfbl != null ">and ylzfbl = #{ylzfbl}</if>
      <if test="gszfbl != null ">and gszfbl = #{gszfbl}</if>
      <if test="syzfbl != null ">and syzfbl = #{syzfbl}</if>
      <if test="jx != null  and jx != ''">and jx = #{jx}</if>
      <if test="bzsl != null ">and bzsl = #{bzsl}</if>
      <if test="bzdw != null  and bzdw != ''">and bzdw = #{bzdw}</if>
      <if test="hl != null  and hl != ''">and hl = #{hl}</if>
      <if test="hldw != null  and hldw != ''">and hldw = #{hldw}</if>
      <if test="rl != null  and rl != ''">and rl = #{rl}</if>
      <if test="rldw != null  and rldw != ''">and rldw = #{rldw}</if>
      <if test="gmp != null  and gmp != ''">and gmp = #{gmp}</if>
      <if test="ycmc != null  and ycmc != ''">and ycmc = #{ycmc}</if>
      <if test="ypxjfs != null  and ypxjfs != ''">and ypxjfs = #{ypxjfs}</if>
      <if test="bgsj != null ">and bgsj = #{bgsj}</if>
      <if test="tqfydj != null  and tqfydj != ''">and tqfydj = #{tqfydj}</if>
      <if test="tqzfbl != null ">and tqzfbl = #{tqzfbl}</if>
      <if test="tqbzdj != null ">and tqbzdj = #{tqbzdj}</if>
      <if test="bz != null  and bz != ''">and bz = #{bz}</if>
      <if test="cjzfbl != null ">and cjzfbl = #{cjzfbl}</if>
      <if test="cjbzdj != null ">and cjbzdj = #{cjbzdj}</if>
      <if test="cjfydj != null  and cjfydj != ''">and cjfydj = #{cjfydj}</if>
      <if test="wsssybz != null  and wsssybz != ''">and wsssybz = #{wsssybz}</if>
      <if test="xetsybz != null  and xetsybz != ''">and xetsybz = #{xetsybz}</if>
      <if test="xmzsybz != null  and xmzsybz != ''">and xmzsybz = #{xmzsybz}</if>
      <if test="jcybz != null  and jcybz != ''">and jcybz = #{jcybz}</if>
      <if test="zzsjbz != null  and zzsjbz != ''">and zzsjbz = #{zzsjbz}</if>
      <if test="zzsjsbjg != null  and zzsjsbjg != ''">and zzsjsbjg = #{zzsjsbjg}</if>
      <if test="bzdj != null ">and bzdj = #{bzdj}</if>
      <if test="zfbl != null ">and zfbl = #{zfbl}</if>
      <if test="fydj != null  and fydj != ''">and fydj = #{fydj}</if>
      <if test="gsfzqjbj != null  and gsfzqjbj != ''">and gsfzqjbj = #{gsfzqjbj}</if>
      <if test="gskfxmbj != null  and gskfxmbj != ''">and gskfxmbj = #{gskfxmbj}</if>
      <if test="gsfpgxxmbl != null  and gsfpgxxmbl != ''">and gsfpgxxmbl = #{gsfpgxxmbl}</if>
      <if test="jbsj != null ">and jbsj = #{jbsj}</if>
      <if test="tqxmdj != null  and tqxmdj != ''">and tqxmdj = #{tqxmdj}</if>
      <if test="starttime != null  and starttime != ''">and starttime = #{starttime}</if>
      <if test="endtime != null  and endtime != ''">and endtime = #{endtime}</if>
      <if test="cCoId != null  and cCoId != ''">and c_co_id = #{cCoId}</if>
      <if test="cJx != null  and cJx != ''">and c_jx = #{cJx}</if>
      <if test="cPym != null  and cPym != ''">and c_pym = #{cPym}</if>
      <if test="changeMask != null  and changeMask != ''">and change_mask = #{changeMask}</if>
      <if test="fylb != null  and fylb != ''">and fylb = #{fylb}</if>
      <if test="bzsm != null  and bzsm != ''">and bzsm = #{bzsm}</if>
      <if test="ycbm != null  and ycbm != ''">and ycbm = #{ycbm}</if>
      <if test="flm != null  and flm != ''">and flm = #{flm}</if>
      <if test="flypxh != null  and flypxh != ''">and flypxh = #{flypxh}</if>
      <if test="xpmm != null  and xpmm != ''">and xpmm = #{xpmm}</if>
      <if test="jxm != null  and jxm != ''">and jxm = #{jxm}</if>
      <if test="ggm != null  and ggm != ''">and ggm = #{ggm}</if>
      <if test="zlm != null  and zlm != ''">and zlm = #{zlm}</if>
      <if test="bzm != null  and bzm != ''">and bzm = #{bzm}</if>
      <if test="tqxmbz != null  and tqxmbz != ''">and tqxmbz = #{tqxmbz}</if>
      <if test="sypc != null  and sypc != ''">and sypc = #{sypc}</if>
      <if test="ypyf != null  and ypyf != ''">and ypyf = #{ypyf}</if>
      <if test="ypgg != null  and ypgg != ''">and ypgg = #{ypgg}</if>
      <if test="yxbsm != null  and yxbsm != ''">and yxbsm = #{yxbsm}</if>
      <if test="cBxblXjyy != null  and cBxblXjyy != ''">and c_bxbl_xjyy = #{cBxblXjyy}</if>
      <if test="cBxblZxzwsy != null  and cBxblZxzwsy != ''">and c_bxbl_zxzwsy = #{cBxblZxzwsy}</if>
      <if test="cBxblXzwsy != null  and cBxblXzwsy != ''">and c_bxbl_xzwsy = #{cBxblXzwsy}</if>
      <if test="cGsfydj != null  and cGsfydj != ''">and c_gsfydj = #{cGsfydj}</if>
      <if test="cSyfydj != null  and cSyfydj != ''">and c_syfydj = #{cSyfydj}</if>
      <if test="cGszfbl != null ">and c_gszfbl = #{cGszfbl}</if>
      <if test="cSyzfbl != null ">and c_syzfbl = #{cSyzfbl}</if>
      <if test="cHyfydj != null  and cHyfydj != ''">and c_hyfydj = #{cHyfydj}</if>
      <if test="cCwbsm != null  and cCwbsm != ''">and c_cwbsm = #{cCwbsm}</if>
      <if test="cRtbsm != null  and cRtbsm != ''">and c_rtbsm = #{cRtbsm}</if>
      <if test="cMzbsm != null  and cMzbsm != ''">and c_mzbsm = #{cMzbsm}</if>
      <if test="cJcbsm != null  and cJcbsm != ''">and c_jcbsm = #{cJcbsm}</if>
      <if test="cZzbsm != null  and cZzbsm != ''">and c_zzbsm = #{cZzbsm}</if>
      <if test="cZzsbjg != null  and cZzsbjg != ''">and c_zzsbjg = #{cZzsbjg}</if>
      <if test="wjbm != null  and wjbm != ''">and wjbm = #{wjbm}</if>
      <if test="cGjypdm != null  and cGjypdm != ''">and c_gjypdm = #{cGjypdm}</if>
      <if test="cZcjx != null  and cZcjx != ''">and c_zcjx = #{cZcjx}</if>
      <if test="cZcgg != null  and cZcgg != ''">and c_zcgg = #{cZcgg}</if>
      <if test="cSjgg != null  and cSjgg != ''">and c_sjgg = #{cSjgg}</if>
      <if test="cBzcz != null  and cBzcz != ''">and c_bzcz = #{cBzcz}</if>
      <if test="cZxbzsl != null ">and c_zxbzsl = #{cZxbzsl}</if>
      <if test="cZxbzdw != null  and cZxbzdw != ''">and c_zxbzdw = #{cZxbzdw}</if>
      <if test="cZxzjdw != null  and cZxzjdw != ''">and c_zxzjdw = #{cZxzjdw}</if>
      <if test="cPzwh != null  and cPzwh != ''">and c_pzwh = #{cPzwh}</if>
      <if test="cYpbwm != null  and cYpbwm != ''">and c_ypbwm = #{cYpbwm}</if>
      <if test="cYcmc1 != null  and cYcmc1 != ''">and c_ycmc1 = #{cYcmc1}</if>
      <if test="cGxfl != null  and cGxfl != ''">and c_gxfl = #{cGxfl}</if>
      <if test="cYckly != null  and cYckly != ''">and c_yckly = #{cYckly}</if>
      <if test="cYczly != null  and cYczly != ''">and c_yczly = #{cYczly}</if>
      <if test="cYybw != null  and cYybw != ''">and c_yybw = #{cYybw}</if>
      <if test="cPzff != null  and cPzff != ''">and c_pzff = #{cPzff}</if>
      <if test="cXwygj != null  and cXwygj != ''">and c_xwygj = #{cXwygj}</if>
      <if test="cGnyzz != null  and cGnyzz != ''">and c_gnyzz = #{cGnyzz}</if>
      <if test="cYfyyl != null  and cYfyyl != ''">and c_yfyyl = #{cYfyyl}</if>
      <if test="gjypdm != null  and gjypdm != ''">and gjypdm = #{gjypdm}</if>
      <if test="mcyl != null ">and mcyl = #{mcyl}</if>
    </where>
  </select>

  <select id="selectYpmlByYplsh" parameterType="String" resultMap="YpmlResult">
    <include refid="selectYpmlVo"/>
    where yplsh = #{yplsh}
  </select>

  <insert id="insertYpml" parameterType="Ypml">
    insert into ypml
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="yplsh != null">yplsh,</if>
      <if test="ypbm != null">ypbm,</if>
      <if test="tym != null">tym,</if>
      <if test="tymzjm != null">tymzjm,</if>
      <if test="spm != null">spm,</if>
      <if test="spmzjm != null">spmzjm,</if>
      <if test="ywm != null">ywm,</if>
      <if test="lbdm != null">lbdm,</if>
      <if test="cfybz != null">cfybz,</if>
      <if test="ylfydj != null">ylfydj,</if>
      <if test="gsfydj != null">gsfydj,</if>
      <if test="syfydj != null">syfydj,</if>
      <if test="pfj != null">pfj,</if>
      <if test="ylbzdj != null">ylbzdj,</if>
      <if test="gsbzdj != null">gsbzdj,</if>
      <if test="sybzdj != null">sybzdj,</if>
      <if test="ylzfbl != null">ylzfbl,</if>
      <if test="gszfbl != null">gszfbl,</if>
      <if test="syzfbl != null">syzfbl,</if>
      <if test="jx != null">jx,</if>
      <if test="bzsl != null">bzsl,</if>
      <if test="bzdw != null">bzdw,</if>
      <if test="hl != null">hl,</if>
      <if test="hldw != null">hldw,</if>
      <if test="rl != null">rl,</if>
      <if test="rldw != null">rldw,</if>
      <if test="gmp != null">gmp,</if>
      <if test="ycmc != null">ycmc,</if>
      <if test="ypxjfs != null">ypxjfs,</if>
      <if test="bgsj != null">bgsj,</if>
      <if test="tqfydj != null">tqfydj,</if>
      <if test="tqzfbl != null">tqzfbl,</if>
      <if test="tqbzdj != null">tqbzdj,</if>
      <if test="bz != null">bz,</if>
      <if test="cjzfbl != null">cjzfbl,</if>
      <if test="cjbzdj != null">cjbzdj,</if>
      <if test="cjfydj != null">cjfydj,</if>
      <if test="wsssybz != null">wsssybz,</if>
      <if test="xetsybz != null">xetsybz,</if>
      <if test="xmzsybz != null">xmzsybz,</if>
      <if test="jcybz != null">jcybz,</if>
      <if test="zzsjbz != null">zzsjbz,</if>
      <if test="zzsjsbjg != null">zzsjsbjg,</if>
      <if test="bzdj != null">bzdj,</if>
      <if test="zfbl != null">zfbl,</if>
      <if test="fydj != null">fydj,</if>
      <if test="gsfzqjbj != null">gsfzqjbj,</if>
      <if test="gskfxmbj != null">gskfxmbj,</if>
      <if test="gsfpgxxmbl != null">gsfpgxxmbl,</if>
      <if test="jbsj != null">jbsj,</if>
      <if test="tqxmdj != null">tqxmdj,</if>
      <if test="starttime != null">starttime,</if>
      <if test="endtime != null">endtime,</if>
      <if test="cCoId != null">c_co_id,</if>
      <if test="cJx != null">c_jx,</if>
      <if test="cPym != null">c_pym,</if>
      <if test="changeMask != null">change_mask,</if>
      <if test="fylb != null">fylb,</if>
      <if test="bzsm != null">bzsm,</if>
      <if test="ycbm != null">ycbm,</if>
      <if test="flm != null">flm,</if>
      <if test="flypxh != null">flypxh,</if>
      <if test="xpmm != null">xpmm,</if>
      <if test="jxm != null">jxm,</if>
      <if test="ggm != null">ggm,</if>
      <if test="zlm != null">zlm,</if>
      <if test="bzm != null">bzm,</if>
      <if test="tqxmbz != null">tqxmbz,</if>
      <if test="sypc != null">sypc,</if>
      <if test="ypyf != null">ypyf,</if>
      <if test="ypgg != null">ypgg,</if>
      <if test="yxbsm != null">yxbsm,</if>
      <if test="cBxblXjyy != null">c_bxbl_xjyy,</if>
      <if test="cBxblZxzwsy != null">c_bxbl_zxzwsy,</if>
      <if test="cBxblXzwsy != null">c_bxbl_xzwsy,</if>
      <if test="cGsfydj != null">c_gsfydj,</if>
      <if test="cSyfydj != null">c_syfydj,</if>
      <if test="cGszfbl != null">c_gszfbl,</if>
      <if test="cSyzfbl != null">c_syzfbl,</if>
      <if test="cHyfydj != null">c_hyfydj,</if>
      <if test="cCwbsm != null">c_cwbsm,</if>
      <if test="cRtbsm != null">c_rtbsm,</if>
      <if test="cMzbsm != null">c_mzbsm,</if>
      <if test="cJcbsm != null">c_jcbsm,</if>
      <if test="cZzbsm != null">c_zzbsm,</if>
      <if test="cZzsbjg != null">c_zzsbjg,</if>
      <if test="wjbm != null">wjbm,</if>
      <if test="cGjypdm != null">c_gjypdm,</if>
      <if test="cZcjx != null">c_zcjx,</if>
      <if test="cZcgg != null">c_zcgg,</if>
      <if test="cSjgg != null">c_sjgg,</if>
      <if test="cBzcz != null">c_bzcz,</if>
      <if test="cZxbzsl != null">c_zxbzsl,</if>
      <if test="cZxbzdw != null">c_zxbzdw,</if>
      <if test="cZxzjdw != null">c_zxzjdw,</if>
      <if test="cPzwh != null">c_pzwh,</if>
      <if test="cYpbwm != null">c_ypbwm,</if>
      <if test="cYcmc1 != null">c_ycmc1,</if>
      <if test="cGxfl != null">c_gxfl,</if>
      <if test="cYckly != null">c_yckly,</if>
      <if test="cYczly != null">c_yczly,</if>
      <if test="cYybw != null">c_yybw,</if>
      <if test="cPzff != null">c_pzff,</if>
      <if test="cXwygj != null">c_xwygj,</if>
      <if test="cGnyzz != null">c_gnyzz,</if>
      <if test="cYfyyl != null">c_yfyyl,</if>
      <if test="gjypdm != null">gjypdm,</if>
      <if test="mcyl != null">mcyl,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="yplsh != null">#{yplsh},</if>
      <if test="ypbm != null">#{ypbm},</if>
      <if test="tym != null">#{tym},</if>
      <if test="tymzjm != null">#{tymzjm},</if>
      <if test="spm != null">#{spm},</if>
      <if test="spmzjm != null">#{spmzjm},</if>
      <if test="ywm != null">#{ywm},</if>
      <if test="lbdm != null">#{lbdm},</if>
      <if test="cfybz != null">#{cfybz},</if>
      <if test="ylfydj != null">#{ylfydj},</if>
      <if test="gsfydj != null">#{gsfydj},</if>
      <if test="syfydj != null">#{syfydj},</if>
      <if test="pfj != null">#{pfj},</if>
      <if test="ylbzdj != null">#{ylbzdj},</if>
      <if test="gsbzdj != null">#{gsbzdj},</if>
      <if test="sybzdj != null">#{sybzdj},</if>
      <if test="ylzfbl != null">#{ylzfbl},</if>
      <if test="gszfbl != null">#{gszfbl},</if>
      <if test="syzfbl != null">#{syzfbl},</if>
      <if test="jx != null">#{jx},</if>
      <if test="bzsl != null">#{bzsl},</if>
      <if test="bzdw != null">#{bzdw},</if>
      <if test="hl != null">#{hl},</if>
      <if test="hldw != null">#{hldw},</if>
      <if test="rl != null">#{rl},</if>
      <if test="rldw != null">#{rldw},</if>
      <if test="gmp != null">#{gmp},</if>
      <if test="ycmc != null">#{ycmc},</if>
      <if test="ypxjfs != null">#{ypxjfs},</if>
      <if test="bgsj != null">#{bgsj},</if>
      <if test="tqfydj != null">#{tqfydj},</if>
      <if test="tqzfbl != null">#{tqzfbl},</if>
      <if test="tqbzdj != null">#{tqbzdj},</if>
      <if test="bz != null">#{bz},</if>
      <if test="cjzfbl != null">#{cjzfbl},</if>
      <if test="cjbzdj != null">#{cjbzdj},</if>
      <if test="cjfydj != null">#{cjfydj},</if>
      <if test="wsssybz != null">#{wsssybz},</if>
      <if test="xetsybz != null">#{xetsybz},</if>
      <if test="xmzsybz != null">#{xmzsybz},</if>
      <if test="jcybz != null">#{jcybz},</if>
      <if test="zzsjbz != null">#{zzsjbz},</if>
      <if test="zzsjsbjg != null">#{zzsjsbjg},</if>
      <if test="bzdj != null">#{bzdj},</if>
      <if test="zfbl != null">#{zfbl},</if>
      <if test="fydj != null">#{fydj},</if>
      <if test="gsfzqjbj != null">#{gsfzqjbj},</if>
      <if test="gskfxmbj != null">#{gskfxmbj},</if>
      <if test="gsfpgxxmbl != null">#{gsfpgxxmbl},</if>
      <if test="jbsj != null">#{jbsj},</if>
      <if test="tqxmdj != null">#{tqxmdj},</if>
      <if test="starttime != null">#{starttime},</if>
      <if test="endtime != null">#{endtime},</if>
      <if test="cCoId != null">#{cCoId},</if>
      <if test="cJx != null">#{cJx},</if>
      <if test="cPym != null">#{cPym},</if>
      <if test="changeMask != null">#{changeMask},</if>
      <if test="fylb != null">#{fylb},</if>
      <if test="bzsm != null">#{bzsm},</if>
      <if test="ycbm != null">#{ycbm},</if>
      <if test="flm != null">#{flm},</if>
      <if test="flypxh != null">#{flypxh},</if>
      <if test="xpmm != null">#{xpmm},</if>
      <if test="jxm != null">#{jxm},</if>
      <if test="ggm != null">#{ggm},</if>
      <if test="zlm != null">#{zlm},</if>
      <if test="bzm != null">#{bzm},</if>
      <if test="tqxmbz != null">#{tqxmbz},</if>
      <if test="sypc != null">#{sypc},</if>
      <if test="ypyf != null">#{ypyf},</if>
      <if test="ypgg != null">#{ypgg},</if>
      <if test="yxbsm != null">#{yxbsm},</if>
      <if test="cBxblXjyy != null">#{cBxblXjyy},</if>
      <if test="cBxblZxzwsy != null">#{cBxblZxzwsy},</if>
      <if test="cBxblXzwsy != null">#{cBxblXzwsy},</if>
      <if test="cGsfydj != null">#{cGsfydj},</if>
      <if test="cSyfydj != null">#{cSyfydj},</if>
      <if test="cGszfbl != null">#{cGszfbl},</if>
      <if test="cSyzfbl != null">#{cSyzfbl},</if>
      <if test="cHyfydj != null">#{cHyfydj},</if>
      <if test="cCwbsm != null">#{cCwbsm},</if>
      <if test="cRtbsm != null">#{cRtbsm},</if>
      <if test="cMzbsm != null">#{cMzbsm},</if>
      <if test="cJcbsm != null">#{cJcbsm},</if>
      <if test="cZzbsm != null">#{cZzbsm},</if>
      <if test="cZzsbjg != null">#{cZzsbjg},</if>
      <if test="wjbm != null">#{wjbm},</if>
      <if test="cGjypdm != null">#{cGjypdm},</if>
      <if test="cZcjx != null">#{cZcjx},</if>
      <if test="cZcgg != null">#{cZcgg},</if>
      <if test="cSjgg != null">#{cSjgg},</if>
      <if test="cBzcz != null">#{cBzcz},</if>
      <if test="cZxbzsl != null">#{cZxbzsl},</if>
      <if test="cZxbzdw != null">#{cZxbzdw},</if>
      <if test="cZxzjdw != null">#{cZxzjdw},</if>
      <if test="cPzwh != null">#{cPzwh},</if>
      <if test="cYpbwm != null">#{cYpbwm},</if>
      <if test="cYcmc1 != null">#{cYcmc1},</if>
      <if test="cGxfl != null">#{cGxfl},</if>
      <if test="cYckly != null">#{cYckly},</if>
      <if test="cYczly != null">#{cYczly},</if>
      <if test="cYybw != null">#{cYybw},</if>
      <if test="cPzff != null">#{cPzff},</if>
      <if test="cXwygj != null">#{cXwygj},</if>
      <if test="cGnyzz != null">#{cGnyzz},</if>
      <if test="cYfyyl != null">#{cYfyyl},</if>
      <if test="gjypdm != null">#{gjypdm},</if>
      <if test="mcyl != null">#{mcyl},</if>
    </trim>
  </insert>

  <update id="updateYpml" parameterType="Ypml">
    update ypml
    <trim prefix="SET" suffixOverrides=",">
      <if test="ypbm != null">ypbm = #{ypbm},</if>
      <if test="tym != null">tym = #{tym},</if>
      <if test="tymzjm != null">tymzjm = #{tymzjm},</if>
      <if test="spm != null">spm = #{spm},</if>
      <if test="spmzjm != null">spmzjm = #{spmzjm},</if>
      <if test="ywm != null">ywm = #{ywm},</if>
      <if test="lbdm != null">lbdm = #{lbdm},</if>
      <if test="cfybz != null">cfybz = #{cfybz},</if>
      <if test="ylfydj != null">ylfydj = #{ylfydj},</if>
      <if test="gsfydj != null">gsfydj = #{gsfydj},</if>
      <if test="syfydj != null">syfydj = #{syfydj},</if>
      <if test="pfj != null">pfj = #{pfj},</if>
      <if test="ylbzdj != null">ylbzdj = #{ylbzdj},</if>
      <if test="gsbzdj != null">gsbzdj = #{gsbzdj},</if>
      <if test="sybzdj != null">sybzdj = #{sybzdj},</if>
      <if test="ylzfbl != null">ylzfbl = #{ylzfbl},</if>
      <if test="gszfbl != null">gszfbl = #{gszfbl},</if>
      <if test="syzfbl != null">syzfbl = #{syzfbl},</if>
      <if test="jx != null">jx = #{jx},</if>
      <if test="bzsl != null">bzsl = #{bzsl},</if>
      <if test="bzdw != null">bzdw = #{bzdw},</if>
      <if test="hl != null">hl = #{hl},</if>
      <if test="hldw != null">hldw = #{hldw},</if>
      <if test="rl != null">rl = #{rl},</if>
      <if test="rldw != null">rldw = #{rldw},</if>
      <if test="gmp != null">gmp = #{gmp},</if>
      <if test="ycmc != null">ycmc = #{ycmc},</if>
      <if test="ypxjfs != null">ypxjfs = #{ypxjfs},</if>
      <if test="bgsj != null">bgsj = #{bgsj},</if>
      <if test="tqfydj != null">tqfydj = #{tqfydj},</if>
      <if test="tqzfbl != null">tqzfbl = #{tqzfbl},</if>
      <if test="tqbzdj != null">tqbzdj = #{tqbzdj},</if>
      <if test="bz != null">bz = #{bz},</if>
      <if test="cjzfbl != null">cjzfbl = #{cjzfbl},</if>
      <if test="cjbzdj != null">cjbzdj = #{cjbzdj},</if>
      <if test="cjfydj != null">cjfydj = #{cjfydj},</if>
      <if test="wsssybz != null">wsssybz = #{wsssybz},</if>
      <if test="xetsybz != null">xetsybz = #{xetsybz},</if>
      <if test="xmzsybz != null">xmzsybz = #{xmzsybz},</if>
      <if test="jcybz != null">jcybz = #{jcybz},</if>
      <if test="zzsjbz != null">zzsjbz = #{zzsjbz},</if>
      <if test="zzsjsbjg != null">zzsjsbjg = #{zzsjsbjg},</if>
      <if test="bzdj != null">bzdj = #{bzdj},</if>
      <if test="zfbl != null">zfbl = #{zfbl},</if>
      <if test="fydj != null">fydj = #{fydj},</if>
      <if test="gsfzqjbj != null">gsfzqjbj = #{gsfzqjbj},</if>
      <if test="gskfxmbj != null">gskfxmbj = #{gskfxmbj},</if>
      <if test="gsfpgxxmbl != null">gsfpgxxmbl = #{gsfpgxxmbl},</if>
      <if test="jbsj != null">jbsj = #{jbsj},</if>
      <if test="tqxmdj != null">tqxmdj = #{tqxmdj},</if>
      <if test="starttime != null">starttime = #{starttime},</if>
      <if test="endtime != null">endtime = #{endtime},</if>
      <if test="cCoId != null">c_co_id = #{cCoId},</if>
      <if test="cJx != null">c_jx = #{cJx},</if>
      <if test="cPym != null">c_pym = #{cPym},</if>
      <if test="changeMask != null">change_mask = #{changeMask},</if>
      <if test="fylb != null">fylb = #{fylb},</if>
      <if test="bzsm != null">bzsm = #{bzsm},</if>
      <if test="ycbm != null">ycbm = #{ycbm},</if>
      <if test="flm != null">flm = #{flm},</if>
      <if test="flypxh != null">flypxh = #{flypxh},</if>
      <if test="xpmm != null">xpmm = #{xpmm},</if>
      <if test="jxm != null">jxm = #{jxm},</if>
      <if test="ggm != null">ggm = #{ggm},</if>
      <if test="zlm != null">zlm = #{zlm},</if>
      <if test="bzm != null">bzm = #{bzm},</if>
      <if test="tqxmbz != null">tqxmbz = #{tqxmbz},</if>
      <if test="sypc != null">sypc = #{sypc},</if>
      <if test="ypyf != null">ypyf = #{ypyf},</if>
      <if test="ypgg != null">ypgg = #{ypgg},</if>
      <if test="yxbsm != null">yxbsm = #{yxbsm},</if>
      <if test="cBxblXjyy != null">c_bxbl_xjyy = #{cBxblXjyy},</if>
      <if test="cBxblZxzwsy != null">c_bxbl_zxzwsy = #{cBxblZxzwsy},</if>
      <if test="cBxblXzwsy != null">c_bxbl_xzwsy = #{cBxblXzwsy},</if>
      <if test="cGsfydj != null">c_gsfydj = #{cGsfydj},</if>
      <if test="cSyfydj != null">c_syfydj = #{cSyfydj},</if>
      <if test="cGszfbl != null">c_gszfbl = #{cGszfbl},</if>
      <if test="cSyzfbl != null">c_syzfbl = #{cSyzfbl},</if>
      <if test="cHyfydj != null">c_hyfydj = #{cHyfydj},</if>
      <if test="cCwbsm != null">c_cwbsm = #{cCwbsm},</if>
      <if test="cRtbsm != null">c_rtbsm = #{cRtbsm},</if>
      <if test="cMzbsm != null">c_mzbsm = #{cMzbsm},</if>
      <if test="cJcbsm != null">c_jcbsm = #{cJcbsm},</if>
      <if test="cZzbsm != null">c_zzbsm = #{cZzbsm},</if>
      <if test="cZzsbjg != null">c_zzsbjg = #{cZzsbjg},</if>
      <if test="wjbm != null">wjbm = #{wjbm},</if>
      <if test="cGjypdm != null">c_gjypdm = #{cGjypdm},</if>
      <if test="cZcjx != null">c_zcjx = #{cZcjx},</if>
      <if test="cZcgg != null">c_zcgg = #{cZcgg},</if>
      <if test="cSjgg != null">c_sjgg = #{cSjgg},</if>
      <if test="cBzcz != null">c_bzcz = #{cBzcz},</if>
      <if test="cZxbzsl != null">c_zxbzsl = #{cZxbzsl},</if>
      <if test="cZxbzdw != null">c_zxbzdw = #{cZxbzdw},</if>
      <if test="cZxzjdw != null">c_zxzjdw = #{cZxzjdw},</if>
      <if test="cPzwh != null">c_pzwh = #{cPzwh},</if>
      <if test="cYpbwm != null">c_ypbwm = #{cYpbwm},</if>
      <if test="cYcmc1 != null">c_ycmc1 = #{cYcmc1},</if>
      <if test="cGxfl != null">c_gxfl = #{cGxfl},</if>
      <if test="cYckly != null">c_yckly = #{cYckly},</if>
      <if test="cYczly != null">c_yczly = #{cYczly},</if>
      <if test="cYybw != null">c_yybw = #{cYybw},</if>
      <if test="cPzff != null">c_pzff = #{cPzff},</if>
      <if test="cXwygj != null">c_xwygj = #{cXwygj},</if>
      <if test="cGnyzz != null">c_gnyzz = #{cGnyzz},</if>
      <if test="cYfyyl != null">c_yfyyl = #{cYfyyl},</if>
      <if test="gjypdm != null">gjypdm = #{gjypdm},</if>
      <if test="mcyl != null">mcyl = #{mcyl},</if>
    </trim>
    where yplsh = #{yplsh}
  </update>

  <delete id="deleteYpmlByYplsh" parameterType="String">
    delete
    from ypml
    where yplsh = #{yplsh}
  </delete>

  <delete id="deleteYpmlByYplshs" parameterType="String">
    delete from ypml where yplsh in
    <foreach item="yplsh" collection="array" open="(" separator="," close=")">
      #{yplsh}
    </foreach>
  </delete>


  <select id="selectka09" parameterType="Ypml" resultMap="YpmlResult">
    select lbbm as yplsh ,lbmc as tym,'类别' as bz from ka09
    <where>
      <if test="yplsh != null  and yplsh != ''">and lbbm = #{yplsh}</if>
      <if test="tym != null  and tym != ''">and lbmc like concat('%', #{tym}, '%')</if>
    </where>
  </select>


  <select id="findUsetymList" resultType="Ypml">
    SELECT distinct

    a.tym AS "tym",
    ifnull(a.tymzjm,a.spmzjm) as "yplsh",
    '药品' as bz

    FROM ypml a,ybjk_usexm b
    where a.yplsh=b.yblsh


    <if test="yplsh != null and yplsh != ''">
      AND a.yplsh = #{yplsh}
    </if>
    <if test="tym != null and tym != ''">
      AND a.tym LIKE
      concat('%',#{tym},'%')
    </if>

    <if test="tymzjm != null and tymzjm != ''">
      AND a.tymzjm LIKE concat('%',#{tymzjm},'%')
    </if>
    order by b.opdate desc

  </select>


  <select id="findUseListzlxm" resultType="Ypml">
    SELECT
    a.xmmc as tym,
    a.zjm as yplsh,
    '诊疗' as bz

    FROM zlxm a,ybjk_usexm b
    where a.xmlsh=b.yblsh
    <if test="tym != null and tym != ''">
      AND a.xmmc LIKE
      concat('%',#{tym},'%')

    </if>
    <if test="yplsh != null and yplsh != ''">

      AND a.zjm LIKE

      concat('%',#{yplsh},'%')
    </if>
    order by b.opdate desc

  </select>
</mapper>
