<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SmxxMapper">
    
    <resultMap type="Smxx" id="SmxxResult">
        <result property="id"    column="id"    />
        <result property="brbs"    column="brbs"    />
        <result property="jzh"    column="jzh"    />
        <result property="brid"    column="brid"    />
        <result property="zyid"    column="zyid"    />
        <result property="ssbm"    column="ssbm"    />
        <result property="ssmc"    column="ssmc"    />
        <result property="createdate"    column="createdate"    />
    </resultMap>

    <sql id="selectSmxxVo">
        select id, brbs, jzh, brid, zyid, ssbm, ssmc, createdate from smxx
    </sql>

    <select id="selectSmxxList" parameterType="Smxx" resultMap="SmxxResult">
        <include refid="selectSmxxVo"/>
        <where>  
            <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
            <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
            <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
            <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
            <if test="ssbm != null  and ssbm != ''"> and ssbm = #{ssbm}</if>
            <if test="ssmc != null  and ssmc != ''"> and ssmc = #{ssmc}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
        </where>
    </select>
    
    <select id="selectSmxxById" parameterType="Long" resultMap="SmxxResult">
        <include refid="selectSmxxVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSmxx" parameterType="Smxx" useGeneratedKeys="true" keyProperty="id">
        insert into smxx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brbs != null">brbs,</if>
            <if test="jzh != null">jzh,</if>
            <if test="brid != null">brid,</if>
            <if test="zyid != null">zyid,</if>
            <if test="ssbm != null">ssbm,</if>
            <if test="ssmc != null">ssmc,</if>
            <if test="createdate != null">createdate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brbs != null">#{brbs},</if>
            <if test="jzh != null">#{jzh},</if>
            <if test="brid != null">#{brid},</if>
            <if test="zyid != null">#{zyid},</if>
            <if test="ssbm != null">#{ssbm},</if>
            <if test="ssmc != null">#{ssmc},</if>
            <if test="createdate != null">#{createdate},</if>
         </trim>
    </insert>

    <update id="updateSmxx" parameterType="Smxx">
        update smxx
        <trim prefix="SET" suffixOverrides=",">
            <if test="brbs != null">brbs = #{brbs},</if>
            <if test="jzh != null">jzh = #{jzh},</if>
            <if test="brid != null">brid = #{brid},</if>
            <if test="zyid != null">zyid = #{zyid},</if>
            <if test="ssbm != null">ssbm = #{ssbm},</if>
            <if test="ssmc != null">ssmc = #{ssmc},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSmxxById" parameterType="Long">
        delete from smxx where id = #{id}
    </delete>

    <delete id="deleteSmxxByIds" parameterType="String">
        delete from smxx where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>