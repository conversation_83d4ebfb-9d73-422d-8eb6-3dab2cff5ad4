<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.VSysDictDataMapper">
    
    <resultMap type="VSysDictData" id="VSysDictDataResult">
        <result property="dictCode"    column="dict_code"    />
        <result property="dictSort"    column="dict_sort"    />
        <result property="dictLabel"    column="dict_label"    />
        <result property="dictValue"    column="dict_value"    />
        <result property="dictType"    column="dict_type"    />
        <result property="cssClass"    column="css_class"    />
        <result property="listClass"    column="list_class"    />
        <result property="isDefault"    column="is_default"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectVSysDictDataVo">
        select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from v_sys_dict_data
    </sql>

    <select id="selectVSysDictDataList" parameterType="VSysDictData" resultMap="VSysDictDataResult">
        <include refid="selectVSysDictDataVo"/>
        <where>
            <if test="dictType != null  and dictType != ''"> and dict_type = #{dictType}</if>
        </where>
    </select>
    
    <select id="selectVSysDictDataByDictCode" parameterType="Long" resultMap="VSysDictDataResult">
        <include refid="selectVSysDictDataVo"/>
        where dict_code = #{dictCode}
    </select>

</mapper>