<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BzmlMapper">
    
    <resultMap type="Bzml" id="BzmlResult">
        <result property="bzbm"    column="bzbm"    />
        <result property="bzmc"    column="bzmc"    />
        <result property="zjm"    column="zjm"    />
        <result property="bzfl"    column="bzfl"    />
        <result property="tjm"    column="tjm"    />
        <result property="jmbzfl"    column="jmbzfl"    />
        <result property="sybzfl"    column="sybzfl"    />
    </resultMap>

    <sql id="selectBzmlVo">
        select bzbm, bzmc, zjm, bzfl, tjm, jmbzfl, sybzfl from bzml
    </sql>

    <select id="selectBzmlList" parameterType="Bzml" resultMap="BzmlResult">
        <include refid="selectBzmlVo"/>
        <where>  
            <if test="bzmc != null  and bzmc != ''"> and bzmc = #{bzmc}</if>
            <if test="bzbm != null  and bzbm != ''"> and bzbm = #{bzbm}</if>
            <if test="zjm != null  and zjm != ''"> and zjm = #{zjm}</if>
            <if test="bzfl != null  and bzfl != ''"> and bzfl = #{bzfl}</if>
            <if test="tjm != null  and tjm != ''"> and tjm = #{tjm}</if>
            <if test="jmbzfl != null  and jmbzfl != ''"> and jmbzfl = #{jmbzfl}</if>
            <if test="sybzfl != null  and sybzfl != ''"> and sybzfl = #{sybzfl}</if>
        </where>
    </select>
    
    <select id="selectBzmlByBzbm" parameterType="String" resultMap="BzmlResult">
        <include refid="selectBzmlVo"/>
        where bzbm = #{bzbm}
    </select>
    
     <select id="selectSsbmListLike" parameterType="list" resultMap="BzmlResult">
       select icdbh as bzbm,icdname as bzmc  from yb_ssml
          <if test="keys != null">
            <where>
                <foreach collection="keys" item="item" index="index" separator=" or ">
                   icdname like concat('%', #{item}, '%')
                </foreach>
            </where>
        </if>
    </select>
    
      <select id="selectJbbmListLike" parameterType="list" resultMap="BzmlResult">
        select bzbm as bzbm,bzmc as bzmc from bzml
          <if test="keys != null">
            <where>
                <foreach collection="keys" item="item" index="index" separator=" or ">
                   bzmc like concat('%', #{item}, '%')
                </foreach>
            </where>
        </if>
    </select>
    
      <select id="selecticd10ListLike" parameterType="list" resultMap="BzmlResult">
        select bzbm as bzbm,bzmc as bzmc from icd10ybdy
          <if test="keys != null">
            <where>
                <foreach collection="keys" item="item" index="index" separator=" or ">
                   bzmc like concat('%', #{item}, '%')
                </foreach>
            </where>
        </if>
    </select>
    
        
    
    <select id="selectbzmc3ListLike" parameterType="list" resultMap="BzmlResult">
        select bzbm as bzbm,bzmc as bzmc,bzmc3,bzmc1,bzmc2 from bzml
          <if test="keys != null">
            <where>
                <foreach collection="keys" item="item" index="index" separator=" or ">
                   bzmc3 like concat('%', #{item}, '%') or  bzmc like concat('%', #{item}, '%')
                </foreach>
             
            </where>
           
        </if>
    </select>
    
    <select id="selectZlxmListLike" parameterType="list" resultMap="BzmlResult">
       SELECT xmlsh AS bzbm,xmmc AS bzmc FROM zlxm_total_name
          <if test="keys != null">
            <where>
                <foreach collection="keys" item="item" index="index" separator=" or ">
                   xmmc like concat('%', #{item}, '%')
                </foreach>
            </where>
        </if>
    </select>
        
    <insert id="insertBzml" parameterType="Bzml">
        insert into bzml
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bzbm != null">bzbm,</if>
            <if test="bzmc != null">bzmc,</if>
            <if test="zjm != null">zjm,</if>
            <if test="bzfl != null">bzfl,</if>
            <if test="tjm != null">tjm,</if>
            <if test="jmbzfl != null">jmbzfl,</if>
            <if test="sybzfl != null">sybzfl,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bzbm != null">#{bzbm},</if>
            <if test="bzmc != null">#{bzmc},</if>
            <if test="zjm != null">#{zjm},</if>
            <if test="bzfl != null">#{bzfl},</if>
            <if test="tjm != null">#{tjm},</if>
            <if test="jmbzfl != null">#{jmbzfl},</if>
            <if test="sybzfl != null">#{sybzfl},</if>
         </trim>
    </insert>

    <update id="updateBzml" parameterType="Bzml">
        update bzml
        <trim prefix="SET" suffixOverrides=",">
            <if test="bzmc != null">bzmc = #{bzmc},</if>
            <if test="zjm != null">zjm = #{zjm},</if>
            <if test="bzfl != null">bzfl = #{bzfl},</if>
            <if test="tjm != null">tjm = #{tjm},</if>
            <if test="jmbzfl != null">jmbzfl = #{jmbzfl},</if>
            <if test="sybzfl != null">sybzfl = #{sybzfl},</if>
        </trim>
        where bzbm = #{bzbm}
    </update>

    <delete id="deleteBzmlByBzbm" parameterType="String">
        delete from bzml where bzbm = #{bzbm}
    </delete>

    <delete id="deleteBzmlByBzbms" parameterType="String">
        delete from bzml where bzbm in 
        <foreach item="bzbm" collection="array" open="(" separator="," close=")">
            #{bzbm}
        </foreach>
    </delete>
</mapper>