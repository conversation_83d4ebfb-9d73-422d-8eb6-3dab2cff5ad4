<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgdictMapper">

  <resultMap type="Drgdict" id="DrgdictResult">
    <result property="adrgbh" column="adrgbh"/>
    <result property="adrgmc" column="adrgmc"/>
    <result property="mdcbh" column="mdcbh"/>
    <result property="mdcmc" column="mdcmc"/>
    <result property="drgbh" column="drgbh"/>
    <result property="drgmc" column="drgmc"/>
    <result property="zfqz" column="zfqz"/>
    <result property="zfbz" column="zfbz"/>
    <result property="jcfl" column="jcfl"/>
    <result property="djxs" column="djxs"/>
    <result property="pjdays" column="pjdays"/>
    <result property="fxdj" column="fxdj"/>
    <result property="fztype" column="fztype"/>
    <result property="years" column="years"/>
    <result property="yljg" column="yljg"/>
    <result property="pjdays3" column="pjdays3"/>
    <result property="zfbz_jm" column="zfbz_jm"/>
    <result property="sqlstr" column="sqlstr"/>
    <result property="zfbz2" column="zfbz2"/>
    <result property="zfbz3" column="zfbz3"/>
    <result property="jgje" column="jgje"/>
    <result property="jdje" column="jdje"/>
    <result property="jgje2" column="jgje2"/>
    <result property="jdje2" column="jdje2"/>
    <result property="zfbzEt" column="zfbz_et"/>
    <result property="zfbzJmEt" column="zfbz_jm_et"/>
  </resultMap>


  <sql id="selectDrgdictVo">
    select sqlstr,
           adrgbh,
           adrgmc,
           mdcbh,
           mdcmc,
           drgbh,
           drgmc,
           zfqz,
           zfbz,
           jcfl,
           djxs,
           pjdays,
           fxdj,
           fztype,
           years,
           yljg,
           pjdays3,
           zfbz_jm,
           jgje,
           jdje,
           jgje2,
           jdje2,
           rzsl,
           zfbz2,
           zfbz3,
           zfqz2,
           zfqz3,
           IFNULL(ypf_bg,0) AS ypf,
           IFNULL(jyf_bg,0) AS jyf,
           IFNULL(jcf_bg,0) AS jcf,
           IFNULL(ssf_bg,0) AS ssf,
           IFNULL(zlf_bg,0) AS zlf,
           IFNULL(qtf_bg,0) AS qtf,
           IFNULL(hcf_bg,0) AS hcf
    from drgdict
  </sql>

  <sql id="selectDrgdictVoXj">
    select sqlstr,
           adrgbh,
           adrgmc,
           mdcbh,
           mdcmc,
           drgbh,
           drgmc,
           zfqz,
           zfbz,
           jcfl,
           djxs,
           pjdays,
           fxdj,
           fztype,
           years,
           yljg,
           pjdays3,
           zfbz_jm,
           jgje,
           jdje,
           rzsl,
           zfbz2,
           zfbz3,
           IFNULL(ypf_bg,0) AS ypf,
           IFNULL(jyf_bg,0) AS jyf,
           IFNULL(jcf_bg,0) AS jcf,
           IFNULL(ssf_bg,0) AS ssf,
           IFNULL(zlf_bg,0) AS zlf,
           IFNULL(qtf_bg,0) AS qtf,
           IFNULL(hcf_bg,0) AS hcf,
           zfbz_et,
           zfbz_jm_et
    from drgdict
  </sql>

  <select id="selectDrgdictList" parameterType="Drgdict" resultMap="DrgdictResult">
    <include refid="selectDrgdictVo"/>
    <where>
      <if test="adrgbh != null  and adrgbh != ''">and adrgbh = #{adrgbh}</if>
      <if test="adrgmc != null  and adrgmc != ''">and adrgmc = #{adrgmc}</if>
      <if test="mdcbh != null  and mdcbh != ''">and mdcbh = #{mdcbh}</if>
      <if test="mdcmc != null  and mdcmc != ''">and mdcmc = #{mdcmc}</if>
      <if test="drgbh != null  and drgbh != ''">and drgbh = #{drgbh}</if>
      <if test="drgmc != null  and drgmc != ''">and drgmc = #{drgmc}</if>
      <if test="zfqz != null ">and zfqz = #{zfqz}</if>
      <if test="zfbz != null ">and zfbz = #{zfbz}</if>
      <if test="jcfl != null ">and jcfl = #{jcfl}</if>
      <if test="djxs != null ">and djxs = #{djxs}</if>
      <if test="pjdays != null ">and pjdays = #{pjdays}</if>
      <if test="fxdj != null  and fxdj != ''">and fxdj = #{fxdj}</if>
      <if test="fztype != null  and fztype != ''">and fztype = #{fztype}</if>
      <if test="years != null  and years != ''">and years = #{years}</if>
      <if test="yljg != null  and yljg != ''">and yljg = #{yljg}</if>
      <if test="pjdays3 != null ">and pjdays3 = #{pjdays3}</if>
      <if test="zfbz_jm != null ">and zfbz_jm = #{zfbz_jm}</if>
      <if test="sqlstr != null ">and sqlstr = #{sqlstr}</if>
      <if test="zfbz2 != null ">and zfbz2 = #{zfbz2}</if>
      <if test="zfbz3 != null ">and zfbz3 = #{zfbz3}</if>
      <if test="useflag != null and useflag != ''">and useflag = #{useflag}</if>
    </where>
  </select>
  
    <select id="selectcnDrgdictList" parameterType="Drgdict" resultMap="DrgdictResult">
       select drgbh,drgmc,zfqz,ifnull(fxdj,'') as fxdj ,useflag from drgdict_cn
    <where>
      <if test="drgbh != null  and drgbh != ''">and drgbh = #{drgbh}</if>
      <if test="drgmc != null  and drgmc != ''">and drgmc = #{drgmc}</if>
      <if test="zfqz != null ">and zfqz = #{zfqz}</if>
      <if test="fxdj != null  and fxdj != ''">and fxdj = #{fxdj}</if>
      <if test="useflag != null and useflag != ''">and useflag = #{useflag}</if>
    </where>
  </select>

  <select id="selectDrgdictListXj" parameterType="Drgdict" resultMap="DrgdictResult">
    <include refid="selectDrgdictVoXj"/>
    <where>
      <if test="adrgbh != null  and adrgbh != ''">and adrgbh = #{adrgbh}</if>
      <if test="adrgmc != null  and adrgmc != ''">and adrgmc = #{adrgmc}</if>
      <if test="mdcbh != null  and mdcbh != ''">and mdcbh = #{mdcbh}</if>
      <if test="mdcmc != null  and mdcmc != ''">and mdcmc = #{mdcmc}</if>
      <if test="drgbh != null  and drgbh != ''">and drgbh = #{drgbh}</if>
      <if test="drgmc != null  and drgmc != ''">and drgmc = #{drgmc}</if>
      <if test="zfqz != null ">and zfqz = #{zfqz}</if>
      <if test="zfbz != null ">and zfbz = #{zfbz}</if>
      <if test="jcfl != null ">and jcfl = #{jcfl}</if>
      <if test="djxs != null ">and djxs = #{djxs}</if>
      <if test="pjdays != null ">and pjdays = #{pjdays}</if>
      <if test="fxdj != null  and fxdj != ''">and fxdj = #{fxdj}</if>
      <if test="fztype != null  and fztype != ''">and fztype = #{fztype}</if>
      <if test="years != null  and years != ''">and years = #{years}</if>
      <if test="yljg != null  and yljg != ''">and yljg = #{yljg}</if>
      <if test="pjdays3 != null ">and pjdays3 = #{pjdays3}</if>
      <if test="zfbz_jm != null ">and zfbz_jm = #{zfbz_jm}</if>
      <if test="sqlstr != null ">and sqlstr = #{sqlstr}</if>
      <if test="zfbz2 != null ">and zfbz2 = #{zfbz2}</if>
      <if test="zfbz3 != null ">and zfbz3 = #{zfbz3}</if>
      <if test="useflag != null and useflag != ''">and useflag = #{useflag}</if>
    </where>
  </select>

  <select id="selectDrgdictByDrgbhAndJgid" parameterType="Drgdict" resultType="Drgdict">
    select sqlstr,
           adrgbh,
           adrgmc,
           mdcbh,
           mdcmc,
           drgbh,
           drgmc,
           zfqz,
           case (select yyjb from sys_tenant where tenant_id = #{jgid})
               when '3' then zfbz3
               when '2' then zfbz2
               else zfbz
           end as zfbz,
           jcfl,
           djxs,
           pjdays,
           fxdj,
           fztype,
           years,
           yljg,
           pjdays3,
           zfbz_jm,
           jgje,
           jdje,
           jgje2,
           jdje2,
           rzsl,
           IFNULL(ypf_bg,0) AS ypf,
           IFNULL(jyf_bg,0) AS jyf,
           IFNULL(jcf_bg,0) AS jcf,
           IFNULL(ssf_bg,0) AS ssf,
           IFNULL(zlf_bg,0) AS zlf,
           IFNULL(qtf_bg,0) AS qtf,
           IFNULL(hcf_bg,0) AS hcf
    from drgdict
    where drgbh = #{drgbh}
  </select>

  <select id="selectDrgdictBySjzyts" resultMap="DrgdictResult">
    <include refid="selectDrgdictVo"/>
    where adrgbh = #{adrgbh} and drgmc like concat('%',#{condition},'%')
  </select>


  <select id="selectDrgdictByAdrgbh" parameterType="String" resultMap="DrgdictResult">
    <include refid="selectDrgdictVo"/>
    where adrgbh = #{adrgbh}
  </select>


  <select id="selectDrgdictByDrgbh" parameterType="String" resultMap="DrgdictResult">
    <include refid="selectDrgdictVo"/>
    where drgbh = #{drgbh}
  </select>
  <select id="selectDrgdict11List" resultType="com.ruoyi.system.domain.Drgdict">
    select sqlstr,
           adrgbh,
           adrgmc,
           mdcbh,
           mdcmc,
           drgbh,
           drgmc,
           zfqz,
           zfbz,
           jcfl,
           djxs,
           pjdays,
           fxdj,
           fztype,
           years,
           yljg,
           pjdays3,
           zfbz_jm,
           jgje,
           jdje,
           jgje2,
           jdje2,
           rzsl,
           zfbz2,
           zfbz3,
           IFNULL(ypf_bg,0) AS ypf,
           IFNULL(jyf_bg,0) AS jyf,
           IFNULL(jcf_bg,0) AS jcf,
           IFNULL(ssf_bg,0) AS ssf,
           IFNULL(zlf_bg,0) AS zlf,
           IFNULL(qtf_bg,0) AS qtf,
           IFNULL(hcf_bg,0) AS hcf
    from drgdict_11
    <where>
      <if test="adrgbh != null  and adrgbh != ''">and adrgbh = #{adrgbh}</if>
      <if test="adrgmc != null  and adrgmc != ''">and adrgmc = #{adrgmc}</if>
      <if test="mdcbh != null  and mdcbh != ''">and mdcbh = #{mdcbh}</if>
      <if test="mdcmc != null  and mdcmc != ''">and mdcmc = #{mdcmc}</if>
      <if test="drgbh != null  and drgbh != ''">and drgbh = #{drgbh}</if>
      <if test="drgmc != null  and drgmc != ''">and drgmc = #{drgmc}</if>
      <if test="zfqz != null ">and zfqz = #{zfqz}</if>
      <if test="zfbz != null ">and zfbz = #{zfbz}</if>
      <if test="jcfl != null ">and jcfl = #{jcfl}</if>
      <if test="djxs != null ">and djxs = #{djxs}</if>
      <if test="pjdays != null ">and pjdays = #{pjdays}</if>
      <if test="fxdj != null  and fxdj != ''">and fxdj = #{fxdj}</if>
      <if test="fztype != null  and fztype != ''">and fztype = #{fztype}</if>
      <if test="years != null  and years != ''">and years = #{years}</if>
      <if test="yljg != null  and yljg != ''">and yljg = #{yljg}</if>
      <if test="pjdays3 != null ">and pjdays3 = #{pjdays3}</if>
      <if test="zfbz_jm != null ">and zfbz_jm = #{zfbz_jm}</if>
      <if test="sqlstr != null ">and sqlstr = #{sqlstr}</if>
      <if test="zfbz2 != null ">and zfbz2 = #{zfbz2}</if>
      <if test="zfbz3 != null ">and zfbz3 = #{zfbz3}</if>
      <if test="useflag != null and useflag != ''">and useflag = #{useflag}</if>
    </where>
  </select>

  <insert id="insertDrgdict" parameterType="Drgdict">
    insert into drgdict
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adrgbh != null">adrgbh,</if>
      <if test="adrgmc != null">adrgmc,</if>
      <if test="mdcbh != null">mdcbh,</if>
      <if test="mdcmc != null">mdcmc,</if>
      <if test="drgbh != null">drgbh,</if>
      <if test="drgmc != null">drgmc,</if>
      <if test="zfqz != null">zfqz,</if>
      <if test="zfbz != null">zfbz,</if>
      <if test="jcfl != null">jcfl,</if>
      <if test="djxs != null">djxs,</if>
      <if test="pjdays != null">pjdays,</if>
      <if test="fxdj != null">fxdj,</if>
      <if test="fztype != null">fztype,</if>
      <if test="years != null">years,</if>
      <if test="yljg != null">yljg,</if>
      <if test="pjdays3 != null">pjdays3,</if>
      <if test="zfbz_jm != null">zfbz_jm,</if>
      <if test="sqlstr != null">sqlstr,</if>
      <if test="zfbz2 != null">zfbz2,</if>
      <if test="zfbz3 != null">zfbz3,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adrgbh != null">#{adrgbh},</if>
      <if test="adrgmc != null">#{adrgmc},</if>
      <if test="mdcbh != null">#{mdcbh},</if>
      <if test="mdcmc != null">#{mdcmc},</if>
      <if test="drgbh != null">#{drgbh},</if>
      <if test="drgmc != null">#{drgmc},</if>
      <if test="zfqz != null">#{zfqz},</if>
      <if test="zfbz != null">#{zfbz},</if>
      <if test="jcfl != null">#{jcfl},</if>
      <if test="djxs != null">#{djxs},</if>
      <if test="pjdays != null">#{pjdays},</if>
      <if test="fxdj != null">#{fxdj},</if>
      <if test="fztype != null">#{fztype},</if>
      <if test="years != null">#{years},</if>
      <if test="yljg != null">#{yljg},</if>
      <if test="pjdays3 != null">#{pjdays3},</if>
      <if test="zfbz_jm != null">#{zfbz_jm},</if>
      <if test="sqlstr != null">#{sqlstr},</if>
      <if test="zfbz2 != null">#{zfbz2},</if>
      <if test="zfbz3 != null">#{zfbz3},</if>
    </trim>
  </insert>

  <update id="updateDrgdict" parameterType="Drgdict">
    update drgdict
    <trim prefix="SET" suffixOverrides=",">
      <if test="adrgmc != null">adrgmc = #{adrgmc},</if>
      <if test="mdcbh != null">mdcbh = #{mdcbh},</if>
      <if test="mdcmc != null">mdcmc = #{mdcmc},</if>
      <if test="drgbh != null">drgbh = #{drgbh},</if>
      <if test="drgmc != null">drgmc = #{drgmc},</if>
      <if test="zfqz != null">zfqz = #{zfqz},</if>
      <if test="zfbz != null">zfbz = #{zfbz},</if>
      <if test="jcfl != null">jcfl = #{jcfl},</if>
      <if test="djxs != null">djxs = #{djxs},</if>
      <if test="pjdays != null">pjdays = #{pjdays},</if>
      <if test="fxdj != null">fxdj = #{fxdj},</if>
      <if test="fztype != null">fztype = #{fztype},</if>
      <if test="years != null">years = #{years},</if>
      <if test="yljg != null">yljg = #{yljg},</if>
      <if test="pjdays3 != null">pjdays3 = #{pjdays3},</if>
      <if test="zfbz_jm != null">zfbz_jm = #{zfbz_jm},</if>
      <if test="sqlstr != null">sqlstr = #{sqlstr},</if>
      <if test="zfbz2 != null">zfbz2 = #{zfbz2},</if>
      <if test="zfbz3 != null">zfbz3 = #{zfbz3},</if>
    </trim>
    where adrgbh = #{adrgbh}
  </update>

  <delete id="deleteDrgdictByAdrgbh" parameterType="String">
    delete
    from drgdict
    where adrgbh = #{adrgbh}
  </delete>

  <delete id="deleteDrgdictByAdrgbhs" parameterType="String">
    delete from drgdict where adrgbh in
    <foreach item="adrgbh" collection="array" open="(" separator="," close=")">
      #{adrgbh}
    </foreach>
  </delete>
</mapper>

