<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaSyjlKsyyMapper">

  <resultMap type="BaSyjlKsyy" id="BaSyjlKsyyResult">
    <result property="id" column="id"/>
    <result property="brbs" column="brbs"/>
    <result property="type" column="type"/>
    <result property="details" column="details"/>
    <result property="createdate" column="createdate"/>
    <result property="xmmc" column="xmmc"/>
    <result property="brfy" column="brfy"/>
    <result property="pjfy" column="pjfy"/>
  </resultMap>

  <resultMap type="BaSyjlKsyy" id="BaSyjlKsyymxResult">
    <result property="cykb" column="cykb"/>
    <result property="zyys" column="zyys"/>
    <result property="drgbh" column="drgbh"/>
    <result property="drgmc" column="drgmc"/>
    <result property="zyh" column="bah"/>
    <result property="id" column="id"/>
    <result property="brbs" column="brbs"/>
    <result property="type" column="type"/>
    <result property="details" column="details"/>
    <result property="createdate" column="createdate"/>
    <result property="xmmc" column="xmmc"/>
    <result property="brfy" column="brfy"/>
    <result property="pjfy" column="pjfy"/>
  </resultMap>

  <sql id="selectBaSyjlKsyyVo">
    select id, brbs, type, details, createdate, xmmc, brfy, pjfy
    from ba_syjl_ksyy
  </sql>


  <select id="selectBaSyjlKsyymx" parameterType="BaSyjlKsyy" resultMap="BaSyjlKsyymxResult">
    select a.cykb,a.drgbh,a.drgmc,a.zyys,a.bah,b.* from ba_syjl a join ba_syjl_ksyy b on a.brbs = b.brbs
    <where>
      <if test="drgbh != null  and drgbh != ''">and drgbh = #{drgbh}</if>
      <if test="cykb != null  and cykb != ''">and (cykb = #{cykb} or #{cykb} = '所有')</if>
      <if test="zyys != null  and zyys != ''">and zyys = #{zyys}</if>
      <if test="adtFrom != null ">and his_jsdate >= #{adtFrom}</if>
      <if test="adtTo != null ">and his_jsdate &lt; #{adtTo}</if>
    </where>
  </select>


  <select id="getBaKsyy" parameterType="String" resultMap="BaSyjlKsyyResult">
    call usp_set_baksyy(#{brbs})
  </select>

  <select id="selectBaSyjlKsyyList" parameterType="BaSyjlKsyy" resultMap="BaSyjlKsyyResult">
    <include refid="selectBaSyjlKsyyVo"/>
    <where>
      <if test="brbs != null  and brbs != ''">and brbs = #{brbs}</if>
      <if test="type != null  and type != ''">and type = #{type}</if>
      <if test="details != null  and details != ''">and details = #{details}</if>
      <if test="createdate != null ">and createdate = #{createdate}</if>
    </where>
  </select>

  <select id="selectBaSyjlKsyyById" parameterType="Long" resultMap="BaSyjlKsyyResult">
    <include refid="selectBaSyjlKsyyVo"/>
    where id = #{id}
  </select>

  <insert id="insertBaSyjlKsyy" parameterType="BaSyjlKsyy" useGeneratedKeys="true" keyProperty="id">
    insert into ba_syjl_ksyy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brbs != null">brbs,</if>
      <if test="type != null">type,</if>
      <if test="details != null">details,</if>
      <if test="createdate != null">createdate,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brbs != null">#{brbs},</if>
      <if test="type != null">#{type},</if>
      <if test="details != null">#{details},</if>
      <if test="createdate != null">#{createdate},</if>
    </trim>
  </insert>

  <update id="updateBaSyjlKsyy" parameterType="BaSyjlKsyy">
    update ba_syjl_ksyy
    <trim prefix="SET" suffixOverrides=",">
      <if test="brbs != null">brbs = #{brbs},</if>
      <if test="type != null">type = #{type},</if>
      <if test="details != null">details = #{details},</if>
      <if test="createdate != null">createdate = #{createdate},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteBaSyjlKsyyById" parameterType="Long">
    delete
    from ba_syjl_ksyy
    where id = #{id}
  </delete>

  <delete id="deleteBaSyjlKsyyByIds" parameterType="String">
    delete from ba_syjl_ksyy where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
