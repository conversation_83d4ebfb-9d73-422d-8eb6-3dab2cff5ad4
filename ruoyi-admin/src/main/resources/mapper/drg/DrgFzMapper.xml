<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgFzMapper">

  <resultMap id="DrgBfzResult" type="DrgBfz">
    <result property="listid"    column="listid"    />
    <result property="bzbm"    column="bzbm"    />
    <result property="type"    column="type"    />
    <result property="bzmc"    column="bzmc"    />
  </resultMap>

  <resultMap id="AdrgbzssResult" type="Adrgbzss">
    <result property="adrg"    column="adrg"    />
    <result property="bzbm"    column="bzbm"    />
    <result property="ssbm"    column="ssbm"    />
  </resultMap>

  <resultMap id="DrgAdrgFzResult" type="DrgAdrgFz">
    <result property="id"    column="id"    />
    <result property="nccd"    column="nccd"    />
    <result property="mdcbh"    column="mdcbh"    />
    <result property="adrgbh"    column="adrgbh"    />
    <result property="icdbh"    column="icdbh"    />
    <result property="icdname"    column="icdname"    />
    <result property="type"    column="type"    />
    <result property="icdtype"    column="icdtype"    />
    <result property="andflag"    column="andflag"    />
    <result property="sssflag"    column="sssflag"    />
    <result property="ssbw"    column="ssbw"    />
  </resultMap>

  <select id="selectDrgBfzList" resultMap="DrgBfzResult" parameterType="DrgBfz">
    SELECT * FROM drg_bfz
    <where>
      <if test="version != null  and version != ''">and version = #{version}</if>
    </where>
  </select>

  <select id="selectDrgBfzpcList" resultMap="DrgBfzResult" parameterType="DrgBfz">
    SELECT * FROM drg_bfzpc
    <where>
      <if test="version != null  and version != ''">and version = #{version}</if>
    </where>
  </select>

  <select id="selectAdrgbzssList" resultMap="AdrgbzssResult">
    SELECT * FROM adrgbzss
  </select>

  <select id="selectDrgAdrgFzAll" resultMap="DrgAdrgFzResult">
    SELECT drg_adrg_fz.mdcbh,
           drg_adrg_fz.adrgbh,
           drg_adrg_fz.icdbh,
           drg_adrg_fz.icdname,
           drg_adrg_fz.type,
           drg_adrg_fz.icdtype,
           drg_adrg_fz.andflag,
           drg_adrg_fz.sssflag,
           drg_adrg_fz.ssbw,
           (case when drg_adrg_fz.id = 0 then '222' else drg_adrg_fz.id end) as id,
           drg_adrg_fz.nccd
    FROM drg_adrg_fz
  </select>

  <select id="selectDrgAdrgFzAll20" resultMap="DrgAdrgFzResult">
    SELECT mdcbh,
           adrgbh,
           icdbh,
           icdname,
           type,
           icdtype,
           andflag,
           sssflag,
           ssbw,
           (case when id = 0 then '222' else id end) as id,
           nccd
    FROM drg_adrg_fz20
  </select>

  <select id="selectDrgAdrgFzList" parameterType="String" resultMap="DrgAdrgFzResult">
    SELECT drg_adrg_fz.mdcbh,
    drg_adrg_fz.adrgbh,
    drg_adrg_fz.icdbh,
    drg_adrg_fz.icdname,
    drg_adrg_fz.type,
    drg_adrg_fz.icdtype,
    drg_adrg_fz.andflag,
    drg_adrg_fz.sssflag,
    drg_adrg_fz.id ,
    drg_adrg_fz.ssbw
    FROM drg_adrg_fz
    WHERE (mdcbh='MDCY' or adrgbh='RB2' or adrgbh='RB1') AND icdbh in
    <foreach collection="array" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select parameterType="DrgAdrgFz" resultType="Integer" id="selectNumByIcdbhAndAdrgbh">
    select count(*) from drg_adrg_fz where icdbh=#{icdbh} and icdtype=#{icdtype} and adrgbh=#{adrgbh};
  </select>

  <select parameterType="DrgAdrgFz" resultType="Integer" id="selectNumByIcdbh">
    select count(*) from drg_adrg_fz where icdbh=#{icdbh} ;
  </select>

  <select id="selectDrgAdrgFzNum" parameterType="String" resultType="Integer">
    SELECT count(distinct ssbw) as ssbwsl
    FROM drg_adrg_fz
    WHERE adrgbh='MDCZ' AND icdbh in
    <foreach collection="array" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <!--  <select id="selectDrgAdrgFzCheckqy" parameterType="DrgAdrgFz" resultMap="DrgAdrgFzResult">-->
  <!--    SELECT drg_adrg_fz.mdcbh,-->
  <!--           drg_adrg_fz.adrgbh,-->
  <!--           drg_adrg_fz.icdbh,-->
  <!--           drg_adrg_fz.icdname,-->
  <!--           drg_adrg_fz.type,-->
  <!--           drg_adrg_fz.icdtype,-->
  <!--           drg_adrg_fz.andflag,-->
  <!--           drg_adrg_fz.sssflag,-->
  <!--           drg_adrg_fz.id-->
  <!--    FROM drg_adrg_fz-->
  <!--    WHERE icdbh in-->
  <!--    <foreach collection="zdxx" open="(" close=")" separator="," item="item">-->
  <!--      #{item}-->
  <!--    </foreach>-->
  <!--    or icdbh in-->
  <!--    <foreach collection="ssxx" open="(" close=")" separator="," item="item">-->
  <!--      #{item}-->
  <!--    </foreach>-->
  <!--  </select>-->

  <select id="selectDrgAdrgFzBsss"  parameterType="DrgAdrgFz" resultMap="DrgAdrgFzResult">
    SELECT drg_adrg_fz.mdcbh,
    drg_adrg_fz.adrgbh,
    drg_adrg_fz.icdbh,
    drg_adrg_fz.icdname,
    drg_adrg_fz.type,
    drg_adrg_fz.icdtype,
    drg_adrg_fz.andflag,
    drg_adrg_fz.id
    FROM drg_adrg_fz
    where (andflag='同时包含和' or andflag='同时包含')
    and adrgbh=#{adrgbh}
    and icdbh in
    <foreach collection="icdbhs" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectDrgAdrgFzAdrgpx"  parameterType="DrgAdrgFz" resultMap="DrgAdrgFzResult">
    SELECT drg_adrg_fz.mdcbh,
    drg_adrg_fz.adrgbh,
    drg_adrg_fz.icdbh,
    drg_adrg_fz.icdname,
    drg_adrg_fz.type,
    drg_adrg_fz.icdtype,
    drg_adrg_fz.andflag,
    drg_adrg_fz.sssflag,
    (case when drg_adrg_fz.id = 0 then '222' else drg_adrg_fz.id end) as id,
    drg_adrg_fz.ssbw
    FROM drg_adrg_fz
    <where>
      <if test="bzbh != null  and bzbh != ''"> and icdbh = #{bzbh}</if>
      <if test="ssbh != null  and ssbh != ''"> or icdbh = #{ssbh}</if>
    </where>
  </select>


  <select id="selectDrgAdrgFzByAdrg"  parameterType="String" resultMap="DrgAdrgFzResult">
    SELECT drg_adrg_fz.mdcbh,
           drg_adrg_fz.adrgbh,
           drg_adrg_fz.icdbh,
           drg_adrg_fz.icdname,
           drg_adrg_fz.type,
           drg_adrg_fz.icdtype,
           drg_adrg_fz.andflag,
           drg_adrg_fz.sssflag,
           drg_adrg_fz.id ,
           drg_adrg_fz.ssbw
    FROM drg_adrg_fz
    WHERE adrgbh = #{adrgbh}
  </select>

  <select id="selectDrgbhByIcdbh"  parameterType="DrgAdrgFz" resultMap="DrgAdrgFzResult">
    select min(adrgbh) as adrgbh from drg_adrg_fz where mdcbh='MDCZ' and icdbh = #{icdbh};
  </select>

  <select id="selectCostLowOrHigh" parameterType="DrgFzResult" resultType="String">
    select fn_get_jgjdbx(#{brbs},#{drgbh},#{zfy})
  </select>

  <select id="selectBrcblb"  parameterType="String" resultType="Integer">
    select fn_get_brcblb(#{brbs}) from p_company;
  </select>

  <select id="selectSsType"  parameterType="String" resultType="String">
    select c_note from b_icd_ssbm where code= #{ssbm};
  </select>

  <select id="selectSsxxBySsbm" parameterType="String" resultType="Ssml">
    SELECT MAX(TYPE),max(ssmc),max(ssbm) FROM ssml   WHERE ssbm LIKE concat(#{ssbm},'%');
  </select>

  <insert id="insertBdFzxx" parameterType="DrgFzResult">
    insert into a_bd_fz(bah, brbs, drgbh, drgmc, zfqz, zfbz, zfy, pjdays, zydays, fztype,cysj)
    values(#{bah},#{brbs},#{drgbh},#{drgmc},#{zfqz},#{zfbz},#{zfy},#{pjdays},#{zydays},#{fztype},#{cysj})
  </insert>

  <insert id="insertKyFzxx" parameterType="DrgFzResult">
    insert into a_ky_fz(bah, brbs, drgbh, drgmc, zfqz, zfbz, zfy, pjdays, zydays, fztype)
    values(#{bah},null,#{drgbh},#{drgmc},null,null,null,null,null,null)
  </insert>

  <insert id="insertPbFzxx" parameterType="DrgFzResult">
    insert into a_pb_fz(bah, brbs, drgbh, drgmc, zfqz, zfbz, zfy, pjdays, zydays, fztype)
    values(#{bah},null,#{drgbh},#{drgmc},null,null,null,null,null,null)
  </insert>

  <select id="updateFz">
    call usp_update_drgzf_from_zx()
  </select>

  <select id="updateBx">
    call usp_update_bx_from_zx()
  </select>
  

   <!-- 存储过程调用 -->
    <select id="selectZzlByBrbs" parameterType="string" resultType="java.math.BigDecimal">
        {call usp_get_zzl(#{in_brbs, jdbcType=VARCHAR})}
    </select>

</mapper>

