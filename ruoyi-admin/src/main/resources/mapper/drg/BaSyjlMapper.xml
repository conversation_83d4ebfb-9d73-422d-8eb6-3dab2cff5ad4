<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaSyjlMapper">

  <resultMap type="BaSyjl" id="BaSyjlResult">
    <result property="id"    column="id"    />
    <result property="jzh"    column="jzh"    />
    <result property="brbs"    column="brbs"    />
    <result property="brid"    column="brid"    />
    <result property="zyid"    column="zyid"    />
    <result property="username"    column="username"    />
    <result property="ylfkfs"    column="ylfkfs"    />
    <result property="jkkh"    column="jkkh"    />
    <result property="zycs"    column="zycs"    />
    <result property="bah"    column="bah"    />
    <result property="xm"    column="xm"    />
    <result property="xb"    column="xb"    />
    <result property="csrq"    column="csrq"    />
    <result property="nl"    column="nl"    />
    <result property="gj"    column="gj"    />
    <result property="bzyzsnl"    column="bzyzsnl"    />
    <result property="xsecstz"    column="xsecstz"    />
    <result property="xserytz"    column="xserytz"    />
    <result property="csd"    column="csd"    />
    <result property="gg"    column="gg"    />
    <result property="mz"    column="mz"    />
    <result property="sfzh"    column="sfzh"    />
    <result property="zy"    column="zy"    />
    <result property="hy"    column="hy"    />
    <result property="xzz"    column="xzz"    />
    <result property="dh"    column="dh"    />
    <result property="yb1"    column="yb1"    />
    <result property="hkdz"    column="hkdz"    />
    <result property="yb2"    column="yb2"    />
    <result property="gzdwjdz"    column="gzdwjdz"    />
    <result property="dwdh"    column="dwdh"    />
    <result property="yb3"    column="yb3"    />
    <result property="lxrxm"    column="lxrxm"    />
    <result property="gx"    column="gx"    />
    <result property="dz"    column="dz"    />
    <result property="dh2"    column="dh2"    />
    <result property="rytj"    column="rytj"    />
    <result property="rysj"    column="rysj"    />
    <result property="rysjs"    column="rysjs"    />
    <result property="rykb"    column="rykb"    />
    <result property="rybf"    column="rybf"    />
    <result property="zkkb"    column="zkkb"    />
    <result property="cysj"    column="cysj"    />
    <result property="cysjs"    column="cysjs"    />
    <result property="cykb"    column="cykb"    />
    <result property="cybf"    column="cybf"    />
    <result property="sjzyts"    column="sjzyts"    />
    <result property="mzzd"    column="mzzd"    />
    <result property="jbbm"    column="jbbm"    />
    <result property="zyzd"    column="zyzd"    />
    <result property="jbdm"    column="jbdm"    />
    <result property="rybq"    column="rybq"    />
    <result property="qtzd1"    column="qtzd1"    />
    <result property="jbdm1"    column="jbdm1"    />
    <result property="rybq1"    column="rybq1"    />
    <result property="qtzd2"    column="qtzd2"    />
    <result property="jbdm2"    column="jbdm2"    />
    <result property="rybq2"    column="rybq2"    />
    <result property="qtzd3"    column="qtzd3"    />
    <result property="jbdm3"    column="jbdm3"    />
    <result property="rybq3"    column="rybq3"    />
    <result property="qtzd4"    column="qtzd4"    />
    <result property="jbdm4"    column="jbdm4"    />
    <result property="rybq4"    column="rybq4"    />
    <result property="qtzd5"    column="qtzd5"    />
    <result property="jbdm5"    column="jbdm5"    />
    <result property="rybq5"    column="rybq5"    />
    <result property="qtzd6"    column="qtzd6"    />
    <result property="jbdm6"    column="jbdm6"    />
    <result property="rybq6"    column="rybq6"    />
    <result property="qtzd7"    column="qtzd7"    />
    <result property="jbdm7"    column="jbdm7"    />
    <result property="rybq7"    column="rybq7"    />
    <result property="qtzd8"    column="qtzd8"    />
    <result property="jbdm8"    column="jbdm8"    />
    <result property="rybq8"    column="rybq8"    />
    <result property="qtzd9"    column="qtzd9"    />
    <result property="jbdm9"    column="jbdm9"    />
    <result property="rybq9"    column="rybq9"    />
    <result property="qtzd10"    column="qtzd10"    />
    <result property="jbdm10"    column="jbdm10"    />
    <result property="rybq10"    column="rybq10"    />
    <result property="qtzd11"    column="qtzd11"    />
    <result property="jbdm11"    column="jbdm11"    />
    <result property="rybq11"    column="rybq11"    />
    <result property="qtzd12"    column="qtzd12"    />
    <result property="jbdm12"    column="jbdm12"    />
    <result property="rybq12"    column="rybq12"    />
    <result property="qtzd13"    column="qtzd13"    />
    <result property="jbdm13"    column="jbdm13"    />
    <result property="rybq13"    column="rybq13"    />
    <result property="qtzd14"    column="qtzd14"    />
    <result property="jbdm14"    column="jbdm14"    />
    <result property="rybq14"    column="rybq14"    />
    <result property="qtzd15"    column="qtzd15"    />
    <result property="jbdm15"    column="jbdm15"    />
    <result property="rybq15"    column="rybq15"    />
    <result property="wbyy"    column="wbyy"    />
    <result property="h23"    column="h23"    />
    <result property="blzd"    column="blzd"    />
    <result property="jbmm"    column="jbmm"    />
    <result property="blh"    column="blh"    />
    <result property="ywgm"    column="ywgm"    />
    <result property="gmyw"    column="gmyw"    />
    <result property="swhzsj"    column="swhzsj"    />
    <result property="xx"    column="xx"    />
    <result property="rh"    column="rh"    />
    <result property="kzr"    column="kzr"    />
    <result property="zrys"    column="zrys"    />
    <result property="zzys"    column="zzys"    />
    <result property="zyys"    column="zyys"    />
    <result property="zrhs"    column="zrhs"    />
    <result property="jxys"    column="jxys"    />
    <result property="sxys"    column="sxys"    />
    <result property="bmy"    column="bmy"    />
    <result property="bazl"    column="bazl"    />
    <result property="zkys"    column="zkys"    />
    <result property="zkhs"    column="zkhs"    />
    <result property="zkrq"    column="zkrq"    />
    <result property="ssjczbm1"    column="ssjczbm1"    />
    <result property="ssjczrq1"    column="ssjczrq1"    />
    <result property="ssjb1"    column="ssjb1"    />
    <result property="ssjczmc1"    column="ssjczmc1"    />
    <result property="sz1"    column="sz1"    />
    <result property="yz1"    column="yz1"    />
    <result property="ez1"    column="ez1"    />
    <result property="qkdj1"    column="qkdj1"    />
    <result property="qkyhlb1"    column="qkyhlb1"    />
    <result property="mzfs1"    column="mzfs1"    />
    <result property="mzys1"    column="mzys1"    />
    <result property="ssjczbm2"    column="ssjczbm2"    />
    <result property="ssjczrq2"    column="ssjczrq2"    />
    <result property="ssjb2"    column="ssjb2"    />
    <result property="ssjczmc2"    column="ssjczmc2"    />
    <result property="sz2"    column="sz2"    />
    <result property="yz2"    column="yz2"    />
    <result property="ez2"    column="ez2"    />
    <result property="qkdj2"    column="qkdj2"    />
    <result property="qkyhlb2"    column="qkyhlb2"    />
    <result property="mzfs2"    column="mzfs2"    />
    <result property="mzys2"    column="mzys2"    />
    <result property="ssjczbm3"    column="ssjczbm3"    />
    <result property="ssjczrq3"    column="ssjczrq3"    />
    <result property="ssjb3"    column="ssjb3"    />
    <result property="ssjczmc3"    column="ssjczmc3"    />
    <result property="sz3"    column="sz3"    />
    <result property="yz3"    column="yz3"    />
    <result property="ez3"    column="ez3"    />
    <result property="qkdj3"    column="qkdj3"    />
    <result property="qkyhlb3"    column="qkyhlb3"    />
    <result property="mzfs3"    column="mzfs3"    />
    <result property="mzys3"    column="mzys3"    />
    <result property="ssjczbm4"    column="ssjczbm4"    />
    <result property="ssjczrq4"    column="ssjczrq4"    />
    <result property="ssjb4"    column="ssjb4"    />
    <result property="ssjczmc4"    column="ssjczmc4"    />
    <result property="sz4"    column="sz4"    />
    <result property="yz4"    column="yz4"    />
    <result property="ez4"    column="ez4"    />
    <result property="qkdj4"    column="qkdj4"    />
    <result property="qkyhlb4"    column="qkyhlb4"    />
    <result property="mzfs4"    column="mzfs4"    />
    <result property="mzys4"    column="mzys4"    />
    <result property="ssjczbm5"    column="ssjczbm5"    />
    <result property="ssjczrq5"    column="ssjczrq5"    />
    <result property="ssjb5"    column="ssjb5"    />
    <result property="ssjczmc5"    column="ssjczmc5"    />
    <result property="sz5"    column="sz5"    />
    <result property="yz5"    column="yz5"    />
    <result property="ez5"    column="ez5"    />
    <result property="qkdj5"    column="qkdj5"    />
    <result property="qkyhlb5"    column="qkyhlb5"    />
    <result property="mzfs5"    column="mzfs5"    />
    <result property="mzys5"    column="mzys5"    />
    <result property="ssjczbm6"    column="ssjczbm6"    />
    <result property="ssjczrq6"    column="ssjczrq6"    />
    <result property="ssjb6"    column="ssjb6"    />
    <result property="ssjczmc6"    column="ssjczmc6"    />
    <result property="sz6"    column="sz6"    />
    <result property="yz6"    column="yz6"    />
    <result property="ez6"    column="ez6"    />
    <result property="qkdj6"    column="qkdj6"    />
    <result property="qkyhlb6"    column="qkyhlb6"    />
    <result property="mzfs6"    column="mzfs6"    />
    <result property="mzys6"    column="mzys6"    />
    <result property="ssjczbm7"    column="ssjczbm7"    />
    <result property="ssjczrq7"    column="ssjczrq7"    />
    <result property="ssjb7"    column="ssjb7"    />
    <result property="ssjczmc7"    column="ssjczmc7"    />
    <result property="sz7"    column="sz7"    />
    <result property="yz7"    column="yz7"    />
    <result property="ez7"    column="ez7"    />
    <result property="qkdj7"    column="qkdj7"    />
    <result property="qkyhlb7"    column="qkyhlb7"    />
    <result property="mzfs7"    column="mzfs7"    />
    <result property="mzys7"    column="mzys7"    />
    <result property="lyfs"    column="lyfs"    />
    <result property="yzzyYljg"    column="yzzy_yljg"    />
    <result property="wsyYljg"    column="wsy_yljg"    />
    <result property="sfzzyjh"    column="sfzzyjh"    />
    <result property="md"    column="md"    />
    <result property="ryqT"    column="ryq_t"    />
    <result property="ryqXs"    column="ryq_xs"    />
    <result property="ryqF"    column="ryq_f"    />
    <result property="ryhT"    column="ryh_t"    />
    <result property="ryhXs"    column="ryh_xs"    />
    <result property="ryhF"    column="ryh_f"    />
    <result property="zfy"    column="zfy"    />
    <result property="zfje"    column="zfje"    />
    <result property="ylfuf"    column="ylfuf"    />
    <result property="zlczf"    column="zlczf"    />
    <result property="hlf"    column="hlf"    />
    <result property="qtfy"    column="qtfy"    />
    <result property="blzdf"    column="blzdf"    />
    <result property="syszdf"    column="syszdf"    />
    <result property="yxxzdf"    column="yxxzdf"    />
    <result property="lczdxmf"    column="lczdxmf"    />
    <result property="fsszlxmf"    column="fsszlxmf"    />
    <result property="wlzlf"    column="wlzlf"    />
    <result property="sszlf"    column="sszlf"    />
    <result property="maf"    column="maf"    />
    <result property="ssf"    column="ssf"    />
    <result property="kff"    column="kff"    />
    <result property="zyzlf"    column="zyzlf"    />
    <result property="xyf"    column="xyf"    />
    <result property="kjywf"    column="kjywf"    />
    <result property="zcyf"    column="zcyf"    />
    <result property="zcyf1"    column="zcyf1"    />
    <result property="xf"    column="xf"    />
    <result property="bdblzpf"    column="bdblzpf"    />
    <result property="qdblzpf"    column="qdblzpf"    />
    <result property="nxyzlzpf"    column="nxyzlzpf"    />
    <result property="xbyzlzpf"    column="xbyzlzpf"    />
    <result property="hcyyclf"    column="hcyyclf"    />
    <result property="yyclf"    column="yyclf"    />
    <result property="ycxyyclf"    column="ycxyyclf"    />
    <result property="qtf"    column="qtf"    />
    <result property="psh"    column="psh"    />
    <result property="basytype"    column="basytype"    />
    <result property="orgcode"    column="orgcode"    />
    <result property="bycode"    column="bycode"    />
    <result property="opname"    column="opname"    />
    <result property="opdate"    column="opdate"    />
    <result property="xgcs"    column="xgcs"    />
    <result property="cxflag"    column="cxflag"    />
    <result property="jsdate"    column="jsdate"    />
    <result property="cqflag"    column="cqflag"    />
    <result property="jyflag"    column="jyflag"    />
    <result property="datasrc"    column="datasrc"    />
    <result property="jxstatus"    column="jxstatus"    />
    <result property="sfsslcljgl"    column="sfsslcljgl"    />
    <result property="sfwclclj"    column="sfwclclj"    />
    <result property="drgmc"    column="drgmc"    />
    <result property="sfby"    column="sfby"    />
    <result property="byyy"    column="byyy"    />
    <result property="ljbzmc"    column="ljbzmc"    />
    <result property="rydate"    column="rydate"    />
    <result property="cydate"    column="cydate"    />
    <result property="drgbh"    column="drgbh"    />
    <result property="rzflag"    column="rzflag"    />
    <result property="wrzyy"    column="wrzyy"    />
    <result property="tczf"    column="tczf"    />
    <result property="drgzf"    column="drgzf"    />
    <result property="jystatus"    column="jystatus"    />
    <result property="jlly"    column="jlly"    />
    <result property="qjcs"    column="qjcs"    />
    <result property="qjcgcs"    column="qjcgcs"    />
    <result property="qzrq"    column="qzrq"    />
    <result property="zyzt"    column="zyzt"    />
    <result property="zdf"    column="zdf"    />
    <result property="hisJsdate"    column="his_jsdate"    />
    <result property="qtzd"    column="qtzd"    />
    <result property="ssjl"    column="ssjl"    />
    <result property="wgs"    column="wgs"    />
    <result property="pjdays"    column="pjdays"    />
  </resultMap>


  <resultMap id="DisOrOperDRGCostInfoResult" type="DisOrOperDRGCostInfo">
    <result property="disCode"    column="jbbm"    />
    <result property="disName"    column="zdmc"    />
    <result property="operCode"    column="ssbm"    />
    <result property="operName"    column="ssmc"    />
    <result property="totalCost"    column="zfy"    />
    <result property="caseNum"    column="bas"    />
    <result property="drgWeight"    column="zfqz"    />
    <result property="drgCode"    column="drgbh"    />
    <result property="drgName"    column="drgmc"    />
    <result property="costExpendIndex"    column="fyxhzs"    />
  </resultMap>

  <resultMap id="ProjUseInfoResult" type="ProjUseInfo">
    <result property="projectName"    column="xmmc"    />
    <result property="projectCode"    column="xmbm"    />
    <result property="projectType"    column="fykmname"    />
    <result property="useCaseNum"    column="usrbrs"    />
    <result property="projectProportion"    column="xmzb"    />
    <result property="dayAvgUseNum"    column="rpjyl"    />
    <result property="avgCost"    column="pjfy"    />
    <result property="totalUseDays"    column="syzts"    />
    <result property="firstUseDays"    column="scsyts"    />
    <result property="costProportion"    column="fyzb"    />
  </resultMap>

  <resultMap id="OperUseInfoResult" type="ProjUseInfo">
    <result property="projectCode"    column="ssbm"    />
    <result property="projectName"    column="ssmc"    />
    <result property="useCaseNum"    column="usrbrs"    />
    <result property="projectProportion"    column="sszb"    />
    <result property="avgCost"    column="pjfy"    />
    <result property="firstUseDays"    column="scsyts"    />
    <result property="costProportion"    column="fyzb"    />
    <result property="avgUseNum"    column="pjsycs"    />
    <result property="projectType"    column="type"    />
  </resultMap>

  <resultMap id="drgBzbmResult" type="BaSyjl">
    <result property="cykb"    column="cykb"    />
    <result property="drgbh"    column="drgbh"    />
    <result property="jbdm"    column="jbdm"    />
    <result property="sl"    column="sl"    />
  </resultMap>

  <resultMap id="DeptDrgZyQueryVoResult" type="DeptDrgZyQueryVo">
    <result property="cykb"    column="cykb"    />
    <result property="zyrs"    column="zyrs"    />
    <result property="wrzrs"    column="wrzrs"    />
    <result property="cbgrs"    column="cbgrs"    />
    <result property="gblrs"    column="gblrs"    />
    <result property="dblrs"    column="dblrs"    />
    <result property="zcblrs"    column="zcblrs"    />
    <result property="cfzyrs"    column="cfzyrs"    />
    <result property="fyyjrs"    column="fyyjrs"    />
    <result property="tsgbrs"    column="tsgbrs"    />
    <result property="wgs"    column="wgs"    />
  </resultMap>

  <resultMap id="DeptZyBrVoResult" type="DeptZyBrVo">
    <result property="jzh"    column="jzh"    />
    <result property="brbs"    column="brbs"    />
    <result property="brid"    column="brid"    />
    <result property="zyid"    column="zyid"    />
    <result property="bah"    column="bah"    />
    <result property="xm"    column="xm"    />
    <result property="cykb"    column="cykb"    />
    <result property="zyys"    column="zyys"    />
    <result property="zfy"    column="zfy"    />
    <result property="drgbh"    column="drgbh"    />
    <result property="drgmc"    column="drgmc"    />
    <result property="zfbz"    column="zfbz"    />
    <result property="ssjczmc1"    column="ssjczmc1"    />
    <result property="ssjczbm1"    column="ssjczbm1"    />
    <result property="zyzd"    column="zyzd"    />
    <result property="jbdm"    column="jbdm"    />
    <result property="sjzyts"    column="sjzyts"    />
    <result property="pjdays"    column="pjdays"    />
    <result property="wgs"    column="wgs"    />
  </resultMap>

  <sql id="selectBaSyjlVo">
    select id, jzh, brbs, brid, zyid, username, ylfkfs, jkkh, zycs, bah, xm, xb, csrq, nl, gj, bzyzsnl, xsecstz, xserytz, csd, gg, mz, sfzh, zy, hy, xzz, dh, yb1, hkdz, yb2, gzdwjdz, dwdh, yb3, lxrxm, gx, dz, dh2, rytj, rysj, rysjs, rykb, rybf, zkkb, cysj, cysjs, cykb, cybf, sjzyts, mzzd, jbbm, zyzd, jbdm, rybq, qtzd1, jbdm1, rybq1, qtzd2, jbdm2, rybq2, qtzd3, jbdm3, rybq3, qtzd4, jbdm4, rybq4, qtzd5, jbdm5, rybq5, qtzd6, jbdm6, rybq6, qtzd7, jbdm7, rybq7, qtzd8, jbdm8, rybq8, qtzd9, jbdm9, rybq9, qtzd10, jbdm10, rybq10, qtzd11, jbdm11, rybq11, qtzd12, jbdm12, rybq12, qtzd13, jbdm13, rybq13, qtzd14, jbdm14, rybq14, qtzd15, jbdm15, rybq15, wbyy, h23, blzd, jbmm, blh, ywgm, gmyw, swhzsj, xx, rh, kzr, zrys, zzys, zyys, zrhs, jxys, sxys, bmy, bazl, zkys, zkhs, zkrq, ssjczbm1, ssjczrq1, ssjb1, ssjczmc1, sz1, yz1, ez1, qkdj1, qkyhlb1, mzfs1, mzys1, ssjczbm2, ssjczrq2, ssjb2, ssjczmc2, sz2, yz2, ez2, qkdj2, qkyhlb2, mzfs2, mzys2, ssjczbm3, ssjczrq3, ssjb3, ssjczmc3, sz3, yz3, ez3, qkdj3, qkyhlb3, mzfs3, mzys3, ssjczbm4, ssjczrq4, ssjb4, ssjczmc4, sz4, yz4, ez4, qkdj4, qkyhlb4, mzfs4, mzys4, ssjczbm5, ssjczrq5, ssjb5, ssjczmc5, sz5, yz5, ez5, qkdj5, qkyhlb5, mzfs5, mzys5, ssjczbm6, ssjczrq6, ssjb6, ssjczmc6, sz6, yz6, ez6, qkdj6, qkyhlb6, mzfs6, mzys6, ssjczbm7, ssjczrq7, ssjb7, ssjczmc7, sz7, yz7, ez7, qkdj7, qkyhlb7, mzfs7, mzys7, lyfs, yzzy_yljg, wsy_yljg, sfzzyjh, md, ryq_t, ryq_xs, ryq_f, ryh_t, ryh_xs, ryh_f, zfy, zfje, ylfuf, zlczf, hlf, qtfy, blzdf, syszdf, yxxzdf, lczdxmf, fsszlxmf, wlzlf, sszlf, maf, ssf, kff, zyzlf, xyf, kjywf, zcyf, zcyf1, xf, bdblzpf, qdblzpf, nxyzlzpf, xbyzlzpf, hcyyclf, yyclf, ycxyyclf, qtf, psh, basytype, orgcode, bycode, opname, opdate, xgcs, cxflag, jsdate, cqflag, jyflag, datasrc, jxstatus, sfsslcljgl, sfwclclj, drgmc, sfby, byyy, ljbzmc, rydate, cydate, drgbh, rzflag, wrzyy, tczf, drgzf, jystatus, jlly, qjcs, qjcgcs, qzrq, zyzt, zdf, his_jsdate, zfbz, yzzy_yljg from ba_syjl
  </sql>

  <select id="selectFzErrBasy" resultType="String">
    select brbs from ba_syjl where jbdm is not null and jbdm != '' and (drgbh is null or drgbh = '')
  </select>

  <select id="selectZdListByDrg" resultMap="BaSyjlResult" parameterType="String">
    select distinct CONCAT(jbdm,zyzd) as zyzd from ba_syjl where drgbh = #{drgbh} and IFNULL(CONCAT(jbdm,zyzd),'') != ''
  </select>

  <select id="selectSsListByDrg" resultMap="BaSyjlResult" parameterType="String">
    select distinct CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) as ssjczmc1 from ba_syjl where drgbh = #{drgbh} and IFNULL(CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')),'') != ''
  </select>


  <select id="selectFzTestBa" resultType="Drgfz" parameterType="String">
    SELECT brbs,bah,xb,nl,bzyzsnl,xserytz,lyfs,zfy,sjzyts,jbdm as zzdbm,
    REPLACE(CONCAT(jbdm1,',',jbdm2,',',jbdm3,',',jbdm4,',',jbdm5,',',jbdm6,',',jbdm7,',',jbdm8,',',jbdm9,',',jbdm10,',',jbdm11,',',jbdm12,',',jbdm13,',',jbdm14,',',jbdm15),',,','') AS zdxx,
    REPLACE(CONCAT(ssjczbm1,',',ssjczbm2,',',ssjczbm3,',',ssjczbm4,',',ssjczbm5,',',ssjczbm6,',',ssjczbm7),',,','') AS ssxx
    FROM blfzmxyb where brbs = #{brbs} or #{brbs} is null
  </select>

  <select id="selectDeptDrgZyQueryVo" parameterType="DeptDrgZyQueryVo" resultMap="DeptDrgZyQueryVoResult">

    SELECT a.*,b.wgs FROM (

    SELECT
    a.cykb,
    COUNT(DISTINCT a.brbs)  AS zyrs,
    COUNT( CASE WHEN (a.drgbh is null or a.drgbh = '' or a.drgbh = '000' or a.drgbh like '%QY') THEN a.brbs ELSE NULL END) AS wrzrs,
    COUNT( CASE WHEN a.zfbz is not null and IFNULL(a.zfy,0) > IFNULL(a.zfbz,0) THEN a.brbs ELSE NULL END) AS cbgrs,
    COUNT( CASE WHEN b.cfzyflag = '1' THEN a.brbs ELSE NULL END)  AS cfzyrs,
    COUNT( CASE WHEN a.zfbz is not null AND IFNULL(a.zfy,0) > IFNULL(a.zfbz,0) * 0.8 THEN a.brbs ELSE NULL END) AS fyyjrs,
    COUNT( CASE WHEN a.blflag = '1' AND ROUND(TIMESTAMPDIFF(SECOND, b.rydate, NOW()) / 86400, 2) > IFNULL(c.pjdays,0) * 0.5 THEN a.brbs ELSE NULL END)  AS tsgbrs,
    COUNT( CASE WHEN a.blflag = '2' THEN a.brbs ELSE NULL END)  AS gblrs
    FROM  brxx b
    JOIN ba_syjl a ON a.brid = b.brid AND a.zyid = b.zyid
    LEFT JOIN drgdict c ON a.drgbh = c.drgbh
    WHERE
    a.cykb IS NOT NULL AND a.cykb != ''
    <if test="cykbList != null">
      and a.cykb in
      (<foreach collection="cykbList" separator="," item="item">
      #{item}
    </foreach>)
    </if>
    <if test="zyys != null and zyys != ''"> and a.zyys = #{zyys}</if>
    <if test="jgid != null and jgid != ''"> and a.username = #{jgid}</if>
    AND b.zyzt = '1'
    GROUP BY a.cykb
    ORDER BY zyrs DESC

    ) a left join
    (

    select c.cykb,count(*) as wgs from brxx a
    join ba_syjl c on a.brid = c.brid and a.zyid = c.zyid
    join ybgk_wgjl b on a.jzh = b.jzh
    where a.zyzt = '1'
    <if test="cykbList != null">
      and c.cykb in
      (<foreach collection="cykbList" separator="," item="item">
        #{item}
      </foreach>)
    </if>
    <if test="zyys != null and zyys != ''"> and c.zyys = #{zyys}</if>
    GROUP BY  c.cykb


    ) b on a.cykb = b.cykb

  </select>

  <select id="selectDeptZyBrList" parameterType="DeptZyBrVo" resultMap="DeptZyBrVoResult">
    SELECT a.*,b.wgs FROM (

    SELECT
    a.brbs,a.brid,a.zyid,
    a.jzh,
    a.bah,
    a.xm,
    a.cykb,
    a.zyys,
    a.zfy,
    a.drgbh,
    c.drgmc,
    a.zfbz,
    a.ssjczmc1,
    a.zyzd,
    a.jbdm,
    a.ssjczbm1,
    ROUND(TIMESTAMPDIFF(SECOND,b.rydate, NOW()) / 86400, 2) as sjzyts,
    c.pjdays
    FROM  brxx b
    JOIN ba_syjl a ON a.brid = b.brid AND a.zyid = b.zyid
    LEFT JOIN drgdict c ON a.drgbh = c.drgbh
    WHERE
    a.cykb = #{cykb} and b.zyzt = '1'
    <if test="zyys != null and zyys != ''">and a.zyys = #{zyys}</if>
    <if test="type == 'zyrs'">
    </if>
    <if test="type == 'wrzrs'">
      and (a.drgbh is null or a.drgbh = '' or a.drgbh = '000' or a.drgbh like '%QY')
    </if>
    <if test="type == 'cbgrs'">
      and a.zfbz is not null and IFNULL(a.zfy,0) > IFNULL(a.zfbz,0)
    </if>
    <if test="type == 'cfzyrs'">
      and b.cfzyflag = '1'
    </if>
    <if test="type == 'fyyjrs'">
      and a.zfbz is not null AND IFNULL(a.zfy,0) > IFNULL(a.zfbz,0) * 0.8
    </if>
    <if test="type == 'tsgbrs'">
      and a.blflag = '1' AND ROUND(TIMESTAMPDIFF(SECOND, b.rydate, NOW()) / 86400, 2) > IFNULL(c.pjdays,0) * 0.5
    </if>
    <if test="type == 'wgs'">
    </if>
    <if test="type == 'gblrs'">
      and a.blflag = '2'
    </if>

    ) a left join
    (

    select a.brbs,count(*) as wgs from brxx b
    join ba_syjl a on a.brid = b.brid and a.zyid = b.zyid
    join ybgk_wgjl d on d.jzh = b.jzh
    where
    a.cykb = #{cykb} and b.zyzt = '1'
    <if test="zyys != null and zyys != ''">and a.zyys = #{zyys}</if>


    group by a.brbs

    ) b on a.brbs = b.brbs

  </select>


  <select id="selectDrgBzbmCount" resultMap="drgBzbmResult">
    select cykb,drgbh,drgmc,jbdm,zyzd,count(*) as sl from ba_syjl where drgbh is not null and jbdm is not null group by cykb,drgbh,jbdm order by sl desc
  </select>


  <select id="selectBaSyjlScore" parameterType="BaSyjl" resultType="Double" >
    SELECT
      IFNULL(100-SUM(bse.score),100) AS score
    FROM
      ba_syjl bs join ba_syjl_error bse on bs.brbs = bse.brbs
    WHERE
      bs.brbs = #{brbs} AND bse.jlly = #{jlly}
  </select>

  <select id="selectBajyList" parameterType="BaSyjl" resultType="BaSyjl">
    SELECT brbs,jzh,zyid,brid,zyzt,bah,xm,xb,nl,sjzyts,cykb,zyys,zycs,rydate,cydate,zyzd,ssjczmc1,jbdm,ssjczbm1,jlly,
    fn_get_bascore(brbs,jlly) AS score
    FROM ba_syjl bs
    <where>
      <if test="dateType == 'cydate'">
        <if test="adtFrom != null">
          and cydate >= #{adtFrom}
        </if>
        <if test="adtTo != null">
          and cydate &lt; #{adtTo}
        </if>
      </if>
      <if test="dateType == 'jsdate'">
        <if test="adtFrom != null">
          and his_jsdate >= #{adtFrom}
        </if>
        <if test="adtTo != null">
          and his_jsdate &lt; #{adtTo}
        </if>
      </if>
      <if test="bah != null  and bah != ''"> and bah = #{bah}</if>
      <if test="cykb != null  and cykb != ''"> and cykb = #{cykb}</if>
      <if test="jlly != null  and jlly != ''"> and jlly = #{jlly}</if>
    </where>
  </select>


  <select id="selectBaSyjlScoreStats" parameterType="BaSyjl" resultMap="BaSyjlResult" >
    SELECT
      bs.brbs,
      bs.bah,
      MAX( jzh ) AS jzh,
      MAX( xm ) AS xm,
      MAX( sjzyts ) AS sjzyts,
      MAX( cykb ) AS cykb,
      MAX( zyys ) AS zyys,
      MAX( zycs ) AS zycs,
      MAX( rydate ) AS rydate,
      MAX( cydate ) AS cydate,
      MAX( his_jsdate ) AS jsdate,
      MAX( opdate ) AS opdate,
      MAX( drgbh ) AS drgbh,
      MAX( zyzt ) AS zyzt,
      bse.jlly  AS jlly,
      fn_get_bascore(bs.brbs,bse.jlly) AS score
    FROM
      ba_syjl bs
        LEFT JOIN ba_syjl_error bse ON bs.brbs = bse.brbs
    <where>
      <if test="dateType == 'cydate'">
        <if test="adtFrom != null">
          and cydate >= #{adtFrom}
        </if>
        <if test="adtTo != null">
          and cydate &lt; #{adtTo}
        </if>
      </if>
      <if test="dateType == 'jsdate'">
        <if test="adtFrom != null">
          and his_jsdate >= #{adtFrom}
        </if>
        <if test="adtTo != null">
          and his_jsdate &lt; #{adtTo}
        </if>
      </if>
      <if test="brbs != null  and brbs != ''"> AND bs.brbs = #{brbs}</if>
      <if test="bah != null  and bah != ''"> AND bs.bah = #{bah}</if>
      <if test="xm != null  and xm != ''"> AND bs.xm = #{xm}</if>
      <if test="cykb != null  and cykb != ''"> AND bs.cykb = #{cykb}</if>
      <if test="zyys != null  and zyys != ''"> AND bs.zyys = #{zyys}</if>
      <if test="jlly != null  and jlly != ''"> AND bse.jlly = #{jlly}</if>
      and bs.zyzt = '0'
    </where>
    GROUP BY
      bs.brbs
    <if test="(minScore != null and minScore != '') or (maxScore != '' and maxScore != null)">
      HAVING
      <if test="minScore != null and minScore != ''">score >= #{minScore}</if>
      <if test="minScore != null and minScore != '' and maxScore != '' and maxScore != null">and</if>
      <if test="maxScore != null and maxScore != ''">score &lt;= #{maxScore}</if>
    </if>
    ORDER BY
      cydate desc
  </select>

  <select parameterType="BaSyjl" resultMap="BaSyjlResult" id="selectFzData">
    SELECT bah,xb,nl,bzyzsnl,xserytz,lyfs,zfy,sjzyts,brid,brbs,jzh,xsecstz,
    IFNULL((SELECT max(jbbm) FROM ba_brzdxx WHERE ba_brzdxx.brbs = a.brbs AND zdcx=1),'') jbdm,
    IFNULL((SELECT max(ssbm) FROM ba_ssjl WHERE ba_ssjl.brbs = a.brbs and sscx=1),'') ssjczbm1,
    IFNULL((SELECT GROUP_CONCAT(jbbm ORDER BY zdcx) FROM ba_brzdxx WHERE ba_brzdxx.brbs = a.brbs AND zdcx != 1 ),'') AS  qtzd,
    IFNULL((SELECT GROUP_CONCAT(ssbm ORDER BY sscx) FROM ba_ssjl WHERE ba_ssjl.brbs = a.brbs ),'') AS  ssjl
    FROM  ba_syjl a
    <where>
      <if test="brbs != null  and brbs != ''"> and a.brbs = #{brbs}</if>
      <if test="jlly != null  and jlly != ''"> and a.jlly = #{jlly}</if>
    </where>
  </select>

  <select parameterType="String" resultMap="BaSyjlResult" id="selectBrbsByBah">
    select max(brbs) as brbs,count(*) as count from ba_syjl where bah=#{bah};
  </select>

  <select parameterType="String" resultMap="BaSyjlResult" id="selectBrbsByBahAndRysj">
    select max(brbs) from ba_syjl where bah=#{bah} and rysj=(select max(rysj) from ba_syjl where bah=#{bah});
  </select>


  <select parameterType="DrgFz" resultType="String" id="selectYbicdbh">
    select fn_get_icdybbm(#{jbbm},#{type},',')
  </select>


  <select id="selectDrgFzIp" resultType="String">
    select c_value from ybjk_option where  c_code = 'drgfz_ip'
  </select>
  <select id="selectFzPageIp" resultType="String">
    select c_value from ybjk_option where  c_code = 'fz_page_ip'
  </select>
  <select id="selectDk" resultType="String">
    select c_value from ybjk_option where  c_code = 'dkh'
  </select>


  <select id="findColumnNameIsNull" parameterType="Basyjl" resultType="Object">
    select #{columnName} from ba_syjl where brbs = #{brbs}
  </select>


  <select id="selectBaSyjlByBridAndZyid" parameterType="BaSyjl" resultType="BaSyjl">
    <include refid="selectBaSyjlVo"/>
    <where>
      <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
    </where>
    limit 1;
  </select>

  <select id="selectBaSyjlByBrbs" parameterType="String" resultType="BaSyjl">
    <include refid="selectBaSyjlVo"/>
    where brbs = #{brbs}
    limit 1;
  </select>

  <!--  统筹信息  -->
  <select id="selectTcxxByBrbs" parameterType="String" resultType="BaSyjl">
    select count(*) as count,
    ifnull(zfy,0) as zfy,
    ifnull(tczf,0) as tczf,
    ifnull(drgzf,0) as drgzf,
    ifnull(his_jsdate,'2000-01-01 00:00:00') as his_jsdate,
    ifnull(opdate,'2000-01-01 00:00:00') as opdate,
    zyzt,drgbh,jbdm
    from ba_syjl where brbs=#{brbs} ;
  </select>

  <select id="selectBaSyjlListblfzxm" parameterType="BaSyjl" resultMap="BaSyjlResult">
   SELECT scdate as hisjsdate, username, jzh, xm, brbs, brid, bah, zycs, xb, nl, bzyzsnl, xsecstz, xserytz, csrq, rytj, rysj, rykb, cysj, cykb, sjzyts, lyfs, zfy, drgbh, drgmc, drgqz, IFNULL(jbdm,'') AS jbdm, zyzd, IFNULL(jbdm1,'') AS jbdm1, qtzd1, sfzl1, IFNULL(jbdm2,'') AS jbdm2, qtzd2, sfzl2, IFNULL(jbdm3,'') AS jbdm3, qtzd3, sfzl3, IFNULL(jbdm4,'') AS jbdm4, qtzd4, sfzl4, IFNULL(jbdm5,'') AS jbdm5, qtzd5, sfzl5, IFNULL(jbdm6,'') AS jbdm6, qtzd6, sfzl6, IFNULL(jbdm7,'') AS jbdm7, qtzd7, sfzl7, IFNULL(jbdm8,'') AS jbdm8, qtzd8, sfzl8, IFNULL(jbdm9,'') AS jbdm9, qtzd9, sfzl9, IFNULL(jbdm10,'') AS jbdm10, qtzd10, sfzl10, IFNULL(jbdm11,'') AS jbdm11, qtzd11, sfzl11, IFNULL(jbdm12,'') AS jbdm12, qtzd12, sfzl12, jbdm13, qtzd13, sfzl13, jbdm14, qtzd14, sfzl14, jbdm15, qtzd15, sfzl15, jsdate, IFNULL(ssjczbm1,'') AS ssjczbm1, ssjczmc1, IFNULL(ssjczbm2,'') AS ssjczbm2, ssjczmc2, IFNULL(ssjczbm3,'') AS ssjczbm3, ssjczmc3, IFNULL(ssjczbm4,'') AS ssjczbm4, ssjczmc4, IFNULL(ssjczbm5,'') AS ssjczbm5, ssjczmc5, ssjczbm6, ssjczmc6, ssjczbm7, ssjczmc7, ssjczbm8, ssjczmc8, drgrzlb, jstype, jsfs, zyzfy, tczf, drgzfbz, drgybf, cyksbm, cyksmc
FROM blfzmx
<where>
      <if test="jzh != null  and jzh != ''"> and jzh = #{jzh}</if>
      <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
      <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
       <if test="bah != null  and bah != ''"> and bah = #{bah}</if>
</where>

  </select>

  <select id="selectBaSyjlList" parameterType="BaSyjl" resultMap="BaSyjlResult">
      <include refid="selectBaSyjlVo"/>
      <where>
          <if test="jzh != null  and jzh != ''">and jzh = #{jzh}</if>
          <if test="brbs != null  and brbs != ''">and brbs = #{brbs}</if>
          <if test="brid != null  and brid != ''">and brid = #{brid}</if>
          <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
          <if test="username != null  and username != ''">and username like concat('%', #{username}, '%')</if>
          <if test="ylfkfs != null  and ylfkfs != ''">and ylfkfs = #{ylfkfs}</if>
          <if test="jkkh != null  and jkkh != ''">and jkkh = #{jkkh}</if>
          <if test="zycs != null  and zycs != ''">and zycs = #{zycs}</if>
          <if test="bah != null  and bah != ''">and bah = #{bah}</if>
          <if test="xm != null  and xm != ''">and xm like concat('%', #{xm}, '%')</if>
          <if test="xb != null  and xb != ''">and xb = #{xb}</if>
          <if test="csrq != null  and csrq != ''">and csrq = #{csrq}</if>
          <if test="nl != null ">and nl = #{nl}</if>
          <if test="gj != null  and gj != ''">and gj = #{gj}</if>
          <if test="bzyzsnl != null ">and bzyzsnl = #{bzyzsnl}</if>
          <if test="xsecstz != null ">and xsecstz = #{xsecstz}</if>
          <if test="xserytz != null ">and xserytz = #{xserytz}</if>
          <if test="csd != null  and csd != ''">and csd = #{csd}</if>
          <if test="gg != null  and gg != ''">and gg = #{gg}</if>
          <if test="mz != null  and mz != ''">and mz = #{mz}</if>
          <if test="sfzh != null  and sfzh != ''">and sfzh = #{sfzh}</if>
          <if test="zy != null  and zy != ''">and zy = #{zy}</if>
          <if test="hy != null  and hy != ''">and hy = #{hy}</if>
          <if test="xzz != null  and xzz != ''">and xzz = #{xzz}</if>
          <if test="dh != null  and dh != ''">and dh = #{dh}</if>
          <if test="yb1 != null  and yb1 != ''">and yb1 = #{yb1}</if>
          <if test="hkdz != null  and hkdz != ''">and hkdz = #{hkdz}</if>
          <if test="yb2 != null  and yb2 != ''">and yb2 = #{yb2}</if>
          <if test="gzdwjdz != null  and gzdwjdz != ''">and gzdwjdz = #{gzdwjdz}</if>
          <if test="dwdh != null  and dwdh != ''">and dwdh = #{dwdh}</if>
          <if test="yb3 != null  and yb3 != ''">and yb3 = #{yb3}</if>
          <if test="lxrxm != null  and lxrxm != ''">and lxrxm = #{lxrxm}</if>
          <if test="gx != null  and gx != ''">and gx = #{gx}</if>
          <if test="dz != null  and dz != ''">and dz = #{dz}</if>
          <if test="dh2 != null  and dh2 != ''">and dh2 = #{dh2}</if>
          <if test="rytj != null  and rytj != ''">and rytj = #{rytj}</if>
          <if test="rysj != null  and rysj != ''">and rysj = #{rysj}</if>
          <if test="rysjs != null ">and rysjs = #{rysjs}</if>
          <if test="rykb != null  and rykb != ''">and rykb = #{rykb}</if>
          <if test="rybf != null  and rybf != ''">and rybf = #{rybf}</if>
          <if test="zkkb != null  and zkkb != ''">and zkkb = #{zkkb}</if>
          <if test="cysj != null  and cysj != ''">and cysj = #{cysj}</if>
          <if test="cysjs != null ">and cysjs = #{cysjs}</if>
          <if test="cykb != null  and cykb != ''">and cykb = #{cykb}</if>
          <if test="cybf != null  and cybf != ''">and cybf = #{cybf}</if>
          <if test="sjzyts != null ">and sjzyts = #{sjzyts}</if>
          <if test="mzzd != null  and mzzd != ''">and mzzd = #{mzzd}</if>
          <if test="jbbm != null  and jbbm != ''">and jbbm = #{jbbm}</if>
          <if test="zyzd != null  and zyzd != ''">and zyzd = #{zyzd}</if>
          <if test="jbdm != null  and jbdm != ''">and jbdm = #{jbdm}</if>
          <if test="rybq != null  and rybq != ''">and rybq = #{rybq}</if>
          <if test="qtzd1 != null  and qtzd1 != ''">and qtzd1 = #{qtzd1}</if>
          <if test="jbdm1 != null  and jbdm1 != ''">and jbdm1 = #{jbdm1}</if>
          <if test="rybq1 != null  and rybq1 != ''">and rybq1 = #{rybq1}</if>
          <if test="qtzd2 != null  and qtzd2 != ''">and qtzd2 = #{qtzd2}</if>
          <if test="jbdm2 != null  and jbdm2 != ''">and jbdm2 = #{jbdm2}</if>
          <if test="rybq2 != null  and rybq2 != ''">and rybq2 = #{rybq2}</if>
          <if test="qtzd3 != null  and qtzd3 != ''">and qtzd3 = #{qtzd3}</if>
          <if test="jbdm3 != null  and jbdm3 != ''">and jbdm3 = #{jbdm3}</if>
          <if test="rybq3 != null  and rybq3 != ''">and rybq3 = #{rybq3}</if>
          <if test="qtzd4 != null  and qtzd4 != ''">and qtzd4 = #{qtzd4}</if>
          <if test="jbdm4 != null  and jbdm4 != ''">and jbdm4 = #{jbdm4}</if>
          <if test="rybq4 != null  and rybq4 != ''">and rybq4 = #{rybq4}</if>
          <if test="qtzd5 != null  and qtzd5 != ''">and qtzd5 = #{qtzd5}</if>
          <if test="jbdm5 != null  and jbdm5 != ''">and jbdm5 = #{jbdm5}</if>
          <if test="rybq5 != null  and rybq5 != ''">and rybq5 = #{rybq5}</if>
          <if test="qtzd6 != null  and qtzd6 != ''">and qtzd6 = #{qtzd6}</if>
          <if test="jbdm6 != null  and jbdm6 != ''">and jbdm6 = #{jbdm6}</if>
          <if test="rybq6 != null  and rybq6 != ''">and rybq6 = #{rybq6}</if>
          <if test="qtzd7 != null  and qtzd7 != ''">and qtzd7 = #{qtzd7}</if>
          <if test="jbdm7 != null  and jbdm7 != ''">and jbdm7 = #{jbdm7}</if>
          <if test="rybq7 != null  and rybq7 != ''">and rybq7 = #{rybq7}</if>
          <if test="qtzd8 != null  and qtzd8 != ''">and qtzd8 = #{qtzd8}</if>
          <if test="jbdm8 != null  and jbdm8 != ''">and jbdm8 = #{jbdm8}</if>
          <if test="rybq8 != null  and rybq8 != ''">and rybq8 = #{rybq8}</if>
          <if test="qtzd9 != null  and qtzd9 != ''">and qtzd9 = #{qtzd9}</if>
          <if test="jbdm9 != null  and jbdm9 != ''">and jbdm9 = #{jbdm9}</if>
          <if test="rybq9 != null  and rybq9 != ''">and rybq9 = #{rybq9}</if>
          <if test="qtzd10 != null  and qtzd10 != ''">and qtzd10 = #{qtzd10}</if>
          <if test="jbdm10 != null  and jbdm10 != ''">and jbdm10 = #{jbdm10}</if>
          <if test="rybq10 != null  and rybq10 != ''">and rybq10 = #{rybq10}</if>
          <if test="qtzd11 != null  and qtzd11 != ''">and qtzd11 = #{qtzd11}</if>
          <if test="jbdm11 != null  and jbdm11 != ''">and jbdm11 = #{jbdm11}</if>
          <if test="rybq11 != null  and rybq11 != ''">and rybq11 = #{rybq11}</if>
          <if test="qtzd12 != null  and qtzd12 != ''">and qtzd12 = #{qtzd12}</if>
          <if test="jbdm12 != null  and jbdm12 != ''">and jbdm12 = #{jbdm12}</if>
          <if test="rybq12 != null  and rybq12 != ''">and rybq12 = #{rybq12}</if>
          <if test="qtzd13 != null  and qtzd13 != ''">and qtzd13 = #{qtzd13}</if>
          <if test="jbdm13 != null  and jbdm13 != ''">and jbdm13 = #{jbdm13}</if>
          <if test="rybq13 != null  and rybq13 != ''">and rybq13 = #{rybq13}</if>
          <if test="qtzd14 != null  and qtzd14 != ''">and qtzd14 = #{qtzd14}</if>
          <if test="jbdm14 != null  and jbdm14 != ''">and jbdm14 = #{jbdm14}</if>
          <if test="rybq14 != null  and rybq14 != ''">and rybq14 = #{rybq14}</if>
          <if test="qtzd15 != null  and qtzd15 != ''">and qtzd15 = #{qtzd15}</if>
          <if test="jbdm15 != null  and jbdm15 != ''">and jbdm15 = #{jbdm15}</if>
          <if test="rybq15 != null  and rybq15 != ''">and rybq15 = #{rybq15}</if>
          <if test="wbyy != null  and wbyy != ''">and wbyy = #{wbyy}</if>
          <if test="h23 != null  and h23 != ''">and h23 = #{h23}</if>
          <if test="blzd != null  and blzd != ''">and blzd = #{blzd}</if>
          <if test="jbmm != null  and jbmm != ''">and jbmm = #{jbmm}</if>
          <if test="blh != null  and blh != ''">and blh = #{blh}</if>
          <if test="ywgm != null  and ywgm != ''">and ywgm = #{ywgm}</if>
          <if test="gmyw != null  and gmyw != ''">and gmyw = #{gmyw}</if>
          <if test="swhzsj != null  and swhzsj != ''">and swhzsj = #{swhzsj}</if>
          <if test="xx != null  and xx != ''">and xx = #{xx}</if>
          <if test="rh != null  and rh != ''">and rh = #{rh}</if>
          <if test="kzr != null  and kzr != ''">and kzr = #{kzr}</if>
          <if test="zrys != null  and zrys != ''">and zrys = #{zrys}</if>
          <if test="zzys != null  and zzys != ''">and zzys = #{zzys}</if>
          <if test="zyys != null  and zyys != ''">and zyys = #{zyys}</if>
          <if test="zrhs != null  and zrhs != ''">and zrhs = #{zrhs}</if>
          <if test="jxys != null  and jxys != ''">and jxys = #{jxys}</if>
          <if test="sxys != null  and sxys != ''">and sxys = #{sxys}</if>
          <if test="bmy != null  and bmy != ''">and bmy = #{bmy}</if>
          <if test="bazl != null  and bazl != ''">and bazl = #{bazl}</if>
          <if test="zkys != null  and zkys != ''">and zkys = #{zkys}</if>
          <if test="zkhs != null  and zkhs != ''">and zkhs = #{zkhs}</if>
          <if test="zkrq != null  and zkrq != ''">and zkrq = #{zkrq}</if>
          <if test="ssjczbm1 != null  and ssjczbm1 != ''">and ssjczbm1 = #{ssjczbm1}</if>
          <if test="ssjczrq1 != null  and ssjczrq1 != ''">and ssjczrq1 = #{ssjczrq1}</if>
          <if test="ssjb1 != null  and ssjb1 != ''">and ssjb1 = #{ssjb1}</if>
          <if test="ssjczmc1 != null  and ssjczmc1 != ''">and ssjczmc1 = #{ssjczmc1}</if>
          <if test="sz1 != null  and sz1 != ''">and sz1 = #{sz1}</if>
          <if test="yz1 != null  and yz1 != ''">and yz1 = #{yz1}</if>
          <if test="ez1 != null  and ez1 != ''">and ez1 = #{ez1}</if>
          <if test="qkdj1 != null  and qkdj1 != ''">and qkdj1 = #{qkdj1}</if>
          <if test="qkyhlb1 != null  and qkyhlb1 != ''">and qkyhlb1 = #{qkyhlb1}</if>
          <if test="mzfs1 != null  and mzfs1 != ''">and mzfs1 = #{mzfs1}</if>
          <if test="mzys1 != null  and mzys1 != ''">and mzys1 = #{mzys1}</if>
          <if test="ssjczbm2 != null  and ssjczbm2 != ''">and ssjczbm2 = #{ssjczbm2}</if>
          <if test="ssjczrq2 != null  and ssjczrq2 != ''">and ssjczrq2 = #{ssjczrq2}</if>
          <if test="ssjb2 != null  and ssjb2 != ''">and ssjb2 = #{ssjb2}</if>
          <if test="ssjczmc2 != null  and ssjczmc2 != ''">and ssjczmc2 = #{ssjczmc2}</if>
          <if test="sz2 != null  and sz2 != ''">and sz2 = #{sz2}</if>
          <if test="yz2 != null  and yz2 != ''">and yz2 = #{yz2}</if>
          <if test="ez2 != null  and ez2 != ''">and ez2 = #{ez2}</if>
          <if test="qkdj2 != null  and qkdj2 != ''">and qkdj2 = #{qkdj2}</if>
          <if test="qkyhlb2 != null  and qkyhlb2 != ''">and qkyhlb2 = #{qkyhlb2}</if>
          <if test="mzfs2 != null  and mzfs2 != ''">and mzfs2 = #{mzfs2}</if>
          <if test="mzys2 != null  and mzys2 != ''">and mzys2 = #{mzys2}</if>
          <if test="ssjczbm3 != null  and ssjczbm3 != ''">and ssjczbm3 = #{ssjczbm3}</if>
          <if test="ssjczrq3 != null  and ssjczrq3 != ''">and ssjczrq3 = #{ssjczrq3}</if>
          <if test="ssjb3 != null  and ssjb3 != ''">and ssjb3 = #{ssjb3}</if>
          <if test="ssjczmc3 != null  and ssjczmc3 != ''">and ssjczmc3 = #{ssjczmc3}</if>
          <if test="sz3 != null  and sz3 != ''">and sz3 = #{sz3}</if>
          <if test="yz3 != null  and yz3 != ''">and yz3 = #{yz3}</if>
          <if test="ez3 != null  and ez3 != ''">and ez3 = #{ez3}</if>
          <if test="qkdj3 != null  and qkdj3 != ''">and qkdj3 = #{qkdj3}</if>
          <if test="qkyhlb3 != null  and qkyhlb3 != ''">and qkyhlb3 = #{qkyhlb3}</if>
          <if test="mzfs3 != null  and mzfs3 != ''">and mzfs3 = #{mzfs3}</if>
          <if test="mzys3 != null  and mzys3 != ''">and mzys3 = #{mzys3}</if>
          <if test="ssjczbm4 != null  and ssjczbm4 != ''">and ssjczbm4 = #{ssjczbm4}</if>
          <if test="ssjczrq4 != null  and ssjczrq4 != ''">and ssjczrq4 = #{ssjczrq4}</if>
          <if test="ssjb4 != null  and ssjb4 != ''">and ssjb4 = #{ssjb4}</if>
          <if test="ssjczmc4 != null  and ssjczmc4 != ''">and ssjczmc4 = #{ssjczmc4}</if>
          <if test="sz4 != null  and sz4 != ''">and sz4 = #{sz4}</if>
          <if test="yz4 != null  and yz4 != ''">and yz4 = #{yz4}</if>
          <if test="ez4 != null  and ez4 != ''">and ez4 = #{ez4}</if>
          <if test="qkdj4 != null  and qkdj4 != ''">and qkdj4 = #{qkdj4}</if>
          <if test="qkyhlb4 != null  and qkyhlb4 != ''">and qkyhlb4 = #{qkyhlb4}</if>
          <if test="mzfs4 != null  and mzfs4 != ''">and mzfs4 = #{mzfs4}</if>
          <if test="mzys4 != null  and mzys4 != ''">and mzys4 = #{mzys4}</if>
          <if test="ssjczbm5 != null  and ssjczbm5 != ''">and ssjczbm5 = #{ssjczbm5}</if>
          <if test="ssjczrq5 != null  and ssjczrq5 != ''">and ssjczrq5 = #{ssjczrq5}</if>
          <if test="ssjb5 != null  and ssjb5 != ''">and ssjb5 = #{ssjb5}</if>
          <if test="ssjczmc5 != null  and ssjczmc5 != ''">and ssjczmc5 = #{ssjczmc5}</if>
          <if test="sz5 != null  and sz5 != ''">and sz5 = #{sz5}</if>
          <if test="yz5 != null  and yz5 != ''">and yz5 = #{yz5}</if>
          <if test="ez5 != null  and ez5 != ''">and ez5 = #{ez5}</if>
          <if test="qkdj5 != null  and qkdj5 != ''">and qkdj5 = #{qkdj5}</if>
          <if test="qkyhlb5 != null  and qkyhlb5 != ''">and qkyhlb5 = #{qkyhlb5}</if>
          <if test="mzfs5 != null  and mzfs5 != ''">and mzfs5 = #{mzfs5}</if>
          <if test="mzys5 != null  and mzys5 != ''">and mzys5 = #{mzys5}</if>
          <if test="ssjczbm6 != null  and ssjczbm6 != ''">and ssjczbm6 = #{ssjczbm6}</if>
          <if test="ssjczrq6 != null  and ssjczrq6 != ''">and ssjczrq6 = #{ssjczrq6}</if>
          <if test="ssjb6 != null  and ssjb6 != ''">and ssjb6 = #{ssjb6}</if>
          <if test="ssjczmc6 != null  and ssjczmc6 != ''">and ssjczmc6 = #{ssjczmc6}</if>
          <if test="sz6 != null  and sz6 != ''">and sz6 = #{sz6}</if>
          <if test="yz6 != null  and yz6 != ''">and yz6 = #{yz6}</if>
          <if test="ez6 != null  and ez6 != ''">and ez6 = #{ez6}</if>
          <if test="qkdj6 != null  and qkdj6 != ''">and qkdj6 = #{qkdj6}</if>
          <if test="qkyhlb6 != null  and qkyhlb6 != ''">and qkyhlb6 = #{qkyhlb6}</if>
          <if test="mzfs6 != null  and mzfs6 != ''">and mzfs6 = #{mzfs6}</if>
          <if test="mzys6 != null  and mzys6 != ''">and mzys6 = #{mzys6}</if>
          <if test="ssjczbm7 != null  and ssjczbm7 != ''">and ssjczbm7 = #{ssjczbm7}</if>
          <if test="ssjczrq7 != null  and ssjczrq7 != ''">and ssjczrq7 = #{ssjczrq7}</if>
          <if test="ssjb7 != null  and ssjb7 != ''">and ssjb7 = #{ssjb7}</if>
          <if test="ssjczmc7 != null  and ssjczmc7 != ''">and ssjczmc7 = #{ssjczmc7}</if>
          <if test="sz7 != null  and sz7 != ''">and sz7 = #{sz7}</if>
          <if test="yz7 != null  and yz7 != ''">and yz7 = #{yz7}</if>
          <if test="ez7 != null  and ez7 != ''">and ez7 = #{ez7}</if>
          <if test="qkdj7 != null  and qkdj7 != ''">and qkdj7 = #{qkdj7}</if>
          <if test="qkyhlb7 != null  and qkyhlb7 != ''">and qkyhlb7 = #{qkyhlb7}</if>
          <if test="mzfs7 != null  and mzfs7 != ''">and mzfs7 = #{mzfs7}</if>
          <if test="mzys7 != null  and mzys7 != ''">and mzys7 = #{mzys7}</if>
          <if test="lyfs != null  and lyfs != ''">and lyfs = #{lyfs}</if>
          <if test="yzzyYljg != null  and yzzyYljg != ''">and yzzy_yljg = #{yzzyYljg}</if>
          <if test="wsyYljg != null  and wsyYljg != ''">and wsy_yljg = #{wsyYljg}</if>
          <if test="sfzzyjh != null  and sfzzyjh != ''">and sfzzyjh = #{sfzzyjh}</if>
          <if test="md != null  and md != ''">and md = #{md}</if>
          <if test="ryqT != null ">and ryq_t = #{ryqT}</if>
          <if test="ryqXs != null ">and ryq_xs = #{ryqXs}</if>
          <if test="ryqF != null ">and ryq_f = #{ryqF}</if>
          <if test="ryhT != null ">and ryh_t = #{ryhT}</if>
          <if test="ryhXs != null ">and ryh_xs = #{ryhXs}</if>
          <if test="ryhF != null ">and ryh_f = #{ryhF}</if>
          <if test="zfy != null ">and zfy = #{zfy}</if>
          <if test="zfje != null ">and zfje = #{zfje}</if>
          <if test="ylfuf != null ">and ylfuf = #{ylfuf}</if>
          <if test="zlczf != null ">and zlczf = #{zlczf}</if>
          <if test="hlf != null ">and hlf = #{hlf}</if>
          <if test="qtfy != null ">and qtfy = #{qtfy}</if>
          <if test="blzdf != null ">and blzdf = #{blzdf}</if>
          <if test="syszdf != null ">and syszdf = #{syszdf}</if>
          <if test="yxxzdf != null ">and yxxzdf = #{yxxzdf}</if>
          <if test="lczdxmf != null ">and lczdxmf = #{lczdxmf}</if>
          <if test="fsszlxmf != null ">and fsszlxmf = #{fsszlxmf}</if>
          <if test="wlzlf != null ">and wlzlf = #{wlzlf}</if>
          <if test="sszlf != null ">and sszlf = #{sszlf}</if>
          <if test="maf != null ">and maf = #{maf}</if>
          <if test="ssf != null ">and ssf = #{ssf}</if>
          <if test="kff != null ">and kff = #{kff}</if>
          <if test="zyzlf != null ">and zyzlf = #{zyzlf}</if>
          <if test="xyf != null ">and xyf = #{xyf}</if>
          <if test="kjywf != null ">and kjywf = #{kjywf}</if>
          <if test="zcyf != null ">and zcyf = #{zcyf}</if>
          <if test="zcyf1 != null ">and zcyf1 = #{zcyf1}</if>
          <if test="xf != null ">and xf = #{xf}</if>
          <if test="bdblzpf != null ">and bdblzpf = #{bdblzpf}</if>
          <if test="qdblzpf != null ">and qdblzpf = #{qdblzpf}</if>
          <if test="nxyzlzpf != null ">and nxyzlzpf = #{nxyzlzpf}</if>
          <if test="xbyzlzpf != null ">and xbyzlzpf = #{xbyzlzpf}</if>
          <if test="hcyyclf != null ">and hcyyclf = #{hcyyclf}</if>
          <if test="yyclf != null ">and yyclf = #{yyclf}</if>
          <if test="ycxyyclf != null ">and ycxyyclf = #{ycxyyclf}</if>
          <if test="qtf != null ">and qtf = #{qtf}</if>
          <if test="psh != null  and psh != ''">and psh = #{psh}</if>
          <if test="basytype != null  and basytype != ''">and basytype = #{basytype}</if>
          <if test="orgcode != null  and orgcode != ''">and orgcode = #{orgcode}</if>
          <if test="bycode != null  and bycode != ''">and bycode = #{bycode}</if>
          <if test="opname != null  and opname != ''">and opname like concat('%', #{opname}, '%')</if>
          <if test="opdate != null ">and opdate = #{opdate}</if>
          <if test="xgcs != null ">and xgcs = #{xgcs}</if>
          <if test="cxflag != null  and cxflag != ''">and cxflag = #{cxflag}</if>
          <if test="jsdate != null ">and jsdate = #{jsdate}</if>
          <if test="cqflag != null  and cqflag != ''">and cqflag = #{cqflag}</if>
          <if test="jyflag != null  and jyflag != ''">and jyflag = #{jyflag}</if>
          <if test="datasrc != null  and datasrc != ''">and datasrc = #{datasrc}</if>
          <if test="jxstatus != null  and jxstatus != ''">and jxstatus = #{jxstatus}</if>
          <if test="sfsslcljgl != null  and sfsslcljgl != ''">and sfsslcljgl = #{sfsslcljgl}</if>
          <if test="sfwclclj != null  and sfwclclj != ''">and sfwclclj = #{sfwclclj}</if>
          <if test="drgmc != null  and drgmc != ''">and drgmc = #{drgmc}</if>
          <if test="sfby != null  and sfby != ''">and sfby = #{sfby}</if>
          <if test="byyy != null  and byyy != ''">and byyy = #{byyy}</if>
          <if test="ljbzmc != null  and ljbzmc != ''">and ljbzmc = #{ljbzmc}</if>
          <if test="rydate != null ">and rydate = #{rydate}</if>
          <if test="cydateStart != null and cydateStart != ''">
              and cydate &gt;= #{cydateStart}
          </if>
          <if test="cydateEnd != null and cydateEnd != ''">
              and cydate &lt;= #{cydateEnd}
          </if>
          <if test="drgbh != null  and drgbh != ''">and drgbh = #{drgbh}</if>
          <if test="rzflag != null ">and rzflag = #{rzflag}</if>
          <if test="wrzyy != null  and wrzyy != ''">and wrzyy = #{wrzyy}</if>
          <if test="tczf != null ">and tczf = #{tczf}</if>
          <if test="drgzf != null ">and drgzf = #{drgzf}</if>
          <if test="jystatus != null ">and jystatus = #{jystatus}</if>
          <if test="jlly != null  and jlly != ''">and jlly = #{jlly}</if>
          <if test="qjcs != null  and qjcs != ''">and qjcs = #{qjcs}</if>
          <if test="qjcgcs != null  and qjcgcs != ''">and qjcgcs = #{qjcgcs}</if>
          <if test="qzrq != null ">and qzrq = #{qzrq}</if>
          <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
          <if test="zdf != null  and zdf != ''">and zdf = #{zdf}</if>
          <if test="hisJsdate != null ">and his_jsdate = #{hisJsdate}</if>
          <if test="cykbList != null">
              and cykb in
              (<foreach collection="cykbList" separator="," item="item">
              #{item}
          </foreach>)
          </if>
      </where>
      order by rydate desc
  </select>

  <select id="selectBaSyjlList2" parameterType="BaSyjl" resultMap="BaSyjlResult">
    SELECT bah,brbs,brid,xm,xb,nl,cykb,sjzyts,(SELECT max(jbbm) from ba_brzdxx_sy where brbs = a.brbs and brid = a.brid and zdcx = 1) as jbdm,
    (SELECT ssbm from ba_ssjl_sy where brbs = a.brbs and brid = a.brid and sscx = 1) as ssjczbm1,rydate,zyzt from ba_syjl a
    <where>
      <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
      <if test="bah != null  and bah != ''"> and bah = #{bah}</if>
    </where>
  </select>

  <select id="selectCykbList"  resultMap="BaSyjlResult">
    SELECT DISTINCT cykb FROM ba_syjl  WHERE rydate> DATE_ADD(CURDATE(), INTERVAL -3 MONTH) AND  cykb IS NOT NULL
  </select>

  <select id="selectDeptList"  resultMap="BaSyjlResult">
    SELECT DISTINCT cykb FROM ba_syjl where cykb is not null and rydate >= date_add(curdate(), interval -6 month)
  </select>


  <select id="selectDeptListByBas"  resultMap="BaSyjlResult">
    SELECT DISTINCT cykb FROM ba_syjl where cykb is not null and jlly = '4'
  </select>

  <select id="selectDoctorList"  resultMap="BaSyjlResult">
    SELECT DISTINCT zyys FROM ba_syjl where zyys is not null
  </select>

  <select id="selectYsAndKsByBrbs"  resultType="BaSyjl" parameterType="String">
    select count(*) as count,max(zyzt) as zyzt,max(zyys) as zyys,max(cykb) as cykb from ba_syjl where brbs = #{brbs};
  </select>

  <select id="selectDoctorByDept"  resultMap="BaSyjlResult" parameterType="BaSyjl">
    select distinct zyys from ba_syjl
    <where>
      <if test="cykb != null and cykb != '' ">and cykb = #{cykb}</if>
      and zyys is not null
      and cykb NOT REGEXP '^[0-9]+$'
    </where>
  </select>

  <select id="selectJsId" parameterType="BaSyjl" resultType="String">
    select max(setl_id) as setl_id from jsxx_his_zy
    <where>
      <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
    </where>
  </select>



  <select id="selectBaSyjlById" parameterType="Long" resultMap="BaSyjlResult">
    <include refid="selectBaSyjlVo"/>
    where id = #{id}
  </select>

    <select id="getMonthInfo" resultType="com.ruoyi.system.domain.vo.SettleInfoDetail">
      SELECT bs.brbs as brbs, bs.brid as brid, bs.zyid as zyid, bs.zfy as zfy, bs.zfbz as zfbz, bs.his_jsdate as hisJsdate,
             bs.cydate as cydate, bs.zyzd as zyzd, bs.jbdm as jbdm, bs.ssjczbm1 as ssjczbm,
             bs.ssjczmc1 as ssjczmc, bs.drgbh as drgbh,
             bs.xm
      FROM ba_syjl bs
      <where>
        <if test="brbs != null">and bs.brbs = #{brbs}</if>
        <if test="cykb != null">and bs.cykb = #{cykb}</if>
        <if test="zyys != null">and bs.zyys = #{zyys}</if>
        and bs.cydate like concat(date_format(now(), '%Y-%m'), '%')
      </where>
      order by bs.cydate desc
    </select>

  <insert id="insertBaSyjlByBrxx" parameterType="BaSyjl">
    insert into ba_syjl(brbs,jzh,brid,zyid,xm,bah,zyzt,cykb,rykb,rysj,cysj,zyys)
    select #{brbs},#{brbs},brid,zyid,name,zyh,zyzt,deptname,deptname,rydate,cydate,doctorname
    from brxx where brid=#{brid} and ifnull(zyid,'')=#{zyid} and zyh != ''
  </insert>

  <insert id="insertBaSyjl" parameterType="BaSyjl" useGeneratedKeys="true" keyProperty="id">
    insert into ba_syjl
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jzh != null and jzh != ''">jzh,</if>
      <if test="brbs != null and brbs != ''">brbs,</if>
      <if test="brid != null">brid,</if>
      <if test="zyid != null">zyid,</if>
      <if test="username != null">username,</if>
      <if test="ylfkfs != null">ylfkfs,</if>
      <if test="jkkh != null">jkkh,</if>
      <if test="zycs != null">zycs,</if>
      <if test="bah != null">bah,</if>
      <if test="xm != null">xm,</if>
      <if test="xb != null">xb,</if>
      <if test="csrq != null">csrq,</if>
      <if test="nl != null">nl,</if>
      <if test="gj != null">gj,</if>
      <if test="bzyzsnl != null">bzyzsnl,</if>
      <if test="xsecstz != null">xsecstz,</if>
      <if test="xserytz != null">xserytz,</if>
      <if test="csd != null">csd,</if>
      <if test="gg != null">gg,</if>
      <if test="mz != null">mz,</if>
      <if test="sfzh != null">sfzh,</if>
      <if test="zy != null">zy,</if>
      <if test="hy != null">hy,</if>
      <if test="xzz != null">xzz,</if>
      <if test="dh != null">dh,</if>
      <if test="yb1 != null">yb1,</if>
      <if test="hkdz != null">hkdz,</if>
      <if test="yb2 != null">yb2,</if>
      <if test="gzdwjdz != null">gzdwjdz,</if>
      <if test="dwdh != null">dwdh,</if>
      <if test="yb3 != null">yb3,</if>
      <if test="lxrxm != null">lxrxm,</if>
      <if test="gx != null">gx,</if>
      <if test="dz != null">dz,</if>
      <if test="dh2 != null">dh2,</if>
      <if test="rytj != null">rytj,</if>
      <if test="rysj != null">rysj,</if>
      <if test="rysjs != null">rysjs,</if>
      <if test="rykb != null">rykb,</if>
      <if test="rybf != null">rybf,</if>
      <if test="zkkb != null">zkkb,</if>
      <if test="cysj != null">cysj,</if>
      <if test="cysjs != null">cysjs,</if>
      <if test="cykb != null">cykb,</if>
      <if test="cybf != null">cybf,</if>
      <if test="sjzyts != null">sjzyts,</if>
      <if test="mzzd != null">mzzd,</if>
      <if test="jbbm != null">jbbm,</if>
      <if test="zyzd != null">zyzd,</if>
      <if test="jbdm != null">jbdm,</if>
      <if test="rybq != null">rybq,</if>
      <if test="qtzd1 != null">qtzd1,</if>
      <if test="jbdm1 != null">jbdm1,</if>
      <if test="rybq1 != null">rybq1,</if>
      <if test="qtzd2 != null">qtzd2,</if>
      <if test="jbdm2 != null">jbdm2,</if>
      <if test="rybq2 != null">rybq2,</if>
      <if test="qtzd3 != null">qtzd3,</if>
      <if test="jbdm3 != null">jbdm3,</if>
      <if test="rybq3 != null">rybq3,</if>
      <if test="qtzd4 != null">qtzd4,</if>
      <if test="jbdm4 != null">jbdm4,</if>
      <if test="rybq4 != null">rybq4,</if>
      <if test="qtzd5 != null">qtzd5,</if>
      <if test="jbdm5 != null">jbdm5,</if>
      <if test="rybq5 != null">rybq5,</if>
      <if test="qtzd6 != null">qtzd6,</if>
      <if test="jbdm6 != null">jbdm6,</if>
      <if test="rybq6 != null">rybq6,</if>
      <if test="qtzd7 != null">qtzd7,</if>
      <if test="jbdm7 != null">jbdm7,</if>
      <if test="rybq7 != null">rybq7,</if>
      <if test="qtzd8 != null">qtzd8,</if>
      <if test="jbdm8 != null">jbdm8,</if>
      <if test="rybq8 != null">rybq8,</if>
      <if test="qtzd9 != null">qtzd9,</if>
      <if test="jbdm9 != null">jbdm9,</if>
      <if test="rybq9 != null">rybq9,</if>
      <if test="qtzd10 != null">qtzd10,</if>
      <if test="jbdm10 != null">jbdm10,</if>
      <if test="rybq10 != null">rybq10,</if>
      <if test="qtzd11 != null">qtzd11,</if>
      <if test="jbdm11 != null">jbdm11,</if>
      <if test="rybq11 != null">rybq11,</if>
      <if test="qtzd12 != null">qtzd12,</if>
      <if test="jbdm12 != null">jbdm12,</if>
      <if test="rybq12 != null">rybq12,</if>
      <if test="qtzd13 != null">qtzd13,</if>
      <if test="jbdm13 != null">jbdm13,</if>
      <if test="rybq13 != null">rybq13,</if>
      <if test="qtzd14 != null">qtzd14,</if>
      <if test="jbdm14 != null">jbdm14,</if>
      <if test="rybq14 != null">rybq14,</if>
      <if test="qtzd15 != null">qtzd15,</if>
      <if test="jbdm15 != null">jbdm15,</if>
      <if test="rybq15 != null">rybq15,</if>
      <if test="wbyy != null">wbyy,</if>
      <if test="h23 != null">h23,</if>
      <if test="blzd != null">blzd,</if>
      <if test="jbmm != null">jbmm,</if>
      <if test="blh != null">blh,</if>
      <if test="ywgm != null">ywgm,</if>
      <if test="gmyw != null">gmyw,</if>
      <if test="swhzsj != null">swhzsj,</if>
      <if test="xx != null">xx,</if>
      <if test="rh != null">rh,</if>
      <if test="kzr != null">kzr,</if>
      <if test="zrys != null">zrys,</if>
      <if test="zzys != null">zzys,</if>
      <if test="zyys != null">zyys,</if>
      <if test="zrhs != null">zrhs,</if>
      <if test="jxys != null">jxys,</if>
      <if test="sxys != null">sxys,</if>
      <if test="bmy != null">bmy,</if>
      <if test="bazl != null">bazl,</if>
      <if test="zkys != null">zkys,</if>
      <if test="zkhs != null">zkhs,</if>
      <if test="zkrq != null">zkrq,</if>
      <if test="ssjczbm1 != null">ssjczbm1,</if>
      <if test="ssjczrq1 != null">ssjczrq1,</if>
      <if test="ssjb1 != null">ssjb1,</if>
      <if test="ssjczmc1 != null">ssjczmc1,</if>
      <if test="sz1 != null">sz1,</if>
      <if test="yz1 != null">yz1,</if>
      <if test="ez1 != null">ez1,</if>
      <if test="qkdj1 != null">qkdj1,</if>
      <if test="qkyhlb1 != null">qkyhlb1,</if>
      <if test="mzfs1 != null">mzfs1,</if>
      <if test="mzys1 != null">mzys1,</if>
      <if test="ssjczbm2 != null">ssjczbm2,</if>
      <if test="ssjczrq2 != null">ssjczrq2,</if>
      <if test="ssjb2 != null">ssjb2,</if>
      <if test="ssjczmc2 != null">ssjczmc2,</if>
      <if test="sz2 != null">sz2,</if>
      <if test="yz2 != null">yz2,</if>
      <if test="ez2 != null">ez2,</if>
      <if test="qkdj2 != null">qkdj2,</if>
      <if test="qkyhlb2 != null">qkyhlb2,</if>
      <if test="mzfs2 != null">mzfs2,</if>
      <if test="mzys2 != null">mzys2,</if>
      <if test="ssjczbm3 != null">ssjczbm3,</if>
      <if test="ssjczrq3 != null">ssjczrq3,</if>
      <if test="ssjb3 != null">ssjb3,</if>
      <if test="ssjczmc3 != null">ssjczmc3,</if>
      <if test="sz3 != null">sz3,</if>
      <if test="yz3 != null">yz3,</if>
      <if test="ez3 != null">ez3,</if>
      <if test="qkdj3 != null">qkdj3,</if>
      <if test="qkyhlb3 != null">qkyhlb3,</if>
      <if test="mzfs3 != null">mzfs3,</if>
      <if test="mzys3 != null">mzys3,</if>
      <if test="ssjczbm4 != null">ssjczbm4,</if>
      <if test="ssjczrq4 != null">ssjczrq4,</if>
      <if test="ssjb4 != null">ssjb4,</if>
      <if test="ssjczmc4 != null">ssjczmc4,</if>
      <if test="sz4 != null">sz4,</if>
      <if test="yz4 != null">yz4,</if>
      <if test="ez4 != null">ez4,</if>
      <if test="qkdj4 != null">qkdj4,</if>
      <if test="qkyhlb4 != null">qkyhlb4,</if>
      <if test="mzfs4 != null">mzfs4,</if>
      <if test="mzys4 != null">mzys4,</if>
      <if test="ssjczbm5 != null">ssjczbm5,</if>
      <if test="ssjczrq5 != null">ssjczrq5,</if>
      <if test="ssjb5 != null">ssjb5,</if>
      <if test="ssjczmc5 != null">ssjczmc5,</if>
      <if test="sz5 != null">sz5,</if>
      <if test="yz5 != null">yz5,</if>
      <if test="ez5 != null">ez5,</if>
      <if test="qkdj5 != null">qkdj5,</if>
      <if test="qkyhlb5 != null">qkyhlb5,</if>
      <if test="mzfs5 != null">mzfs5,</if>
      <if test="mzys5 != null">mzys5,</if>
      <if test="ssjczbm6 != null">ssjczbm6,</if>
      <if test="ssjczrq6 != null">ssjczrq6,</if>
      <if test="ssjb6 != null">ssjb6,</if>
      <if test="ssjczmc6 != null">ssjczmc6,</if>
      <if test="sz6 != null">sz6,</if>
      <if test="yz6 != null">yz6,</if>
      <if test="ez6 != null">ez6,</if>
      <if test="qkdj6 != null">qkdj6,</if>
      <if test="qkyhlb6 != null">qkyhlb6,</if>
      <if test="mzfs6 != null">mzfs6,</if>
      <if test="mzys6 != null">mzys6,</if>
      <if test="ssjczbm7 != null">ssjczbm7,</if>
      <if test="ssjczrq7 != null">ssjczrq7,</if>
      <if test="ssjb7 != null">ssjb7,</if>
      <if test="ssjczmc7 != null">ssjczmc7,</if>
      <if test="sz7 != null">sz7,</if>
      <if test="yz7 != null">yz7,</if>
      <if test="ez7 != null">ez7,</if>
      <if test="qkdj7 != null">qkdj7,</if>
      <if test="qkyhlb7 != null">qkyhlb7,</if>
      <if test="mzfs7 != null">mzfs7,</if>
      <if test="mzys7 != null">mzys7,</if>
      <if test="lyfs != null">lyfs,</if>
      <if test="yzzyYljg != null">yzzy_yljg,</if>
      <if test="wsyYljg != null">wsy_yljg,</if>
      <if test="sfzzyjh != null">sfzzyjh,</if>
      <if test="md != null">md,</if>
      <if test="ryqT != null">ryq_t,</if>
      <if test="ryqXs != null">ryq_xs,</if>
      <if test="ryqF != null">ryq_f,</if>
      <if test="ryhT != null">ryh_t,</if>
      <if test="ryhXs != null">ryh_xs,</if>
      <if test="ryhF != null">ryh_f,</if>
      <if test="zfy != null">zfy,</if>
      <if test="zfje != null">zfje,</if>
      <if test="ylfuf != null">ylfuf,</if>
      <if test="zlczf != null">zlczf,</if>
      <if test="hlf != null">hlf,</if>
      <if test="qtfy != null">qtfy,</if>
      <if test="blzdf != null">blzdf,</if>
      <if test="syszdf != null">syszdf,</if>
      <if test="yxxzdf != null">yxxzdf,</if>
      <if test="lczdxmf != null">lczdxmf,</if>
      <if test="fsszlxmf != null">fsszlxmf,</if>
      <if test="wlzlf != null">wlzlf,</if>
      <if test="sszlf != null">sszlf,</if>
      <if test="maf != null">maf,</if>
      <if test="ssf != null">ssf,</if>
      <if test="kff != null">kff,</if>
      <if test="zyzlf != null">zyzlf,</if>
      <if test="xyf != null">xyf,</if>
      <if test="kjywf != null">kjywf,</if>
      <if test="zcyf != null">zcyf,</if>
      <if test="zcyf1 != null">zcyf1,</if>
      <if test="xf != null">xf,</if>
      <if test="bdblzpf != null">bdblzpf,</if>
      <if test="qdblzpf != null">qdblzpf,</if>
      <if test="nxyzlzpf != null">nxyzlzpf,</if>
      <if test="xbyzlzpf != null">xbyzlzpf,</if>
      <if test="hcyyclf != null">hcyyclf,</if>
      <if test="yyclf != null">yyclf,</if>
      <if test="ycxyyclf != null">ycxyyclf,</if>
      <if test="qtf != null">qtf,</if>
      <if test="psh != null">psh,</if>
      <if test="basytype != null">basytype,</if>
      <if test="orgcode != null">orgcode,</if>
      <if test="bycode != null">bycode,</if>
      <if test="opname != null">opname,</if>
      <if test="opdate != null">opdate,</if>
      <if test="xgcs != null">xgcs,</if>
      <if test="cxflag != null">cxflag,</if>
      <if test="jsdate != null">jsdate,</if>
      <if test="cqflag != null">cqflag,</if>
      <if test="jyflag != null">jyflag,</if>
      <if test="datasrc != null">datasrc,</if>
      <if test="jxstatus != null">jxstatus,</if>
      <if test="sfsslcljgl != null">sfsslcljgl,</if>
      <if test="sfwclclj != null">sfwclclj,</if>
      <if test="drgmc != null">drgmc,</if>
      <if test="sfby != null">sfby,</if>
      <if test="byyy != null">byyy,</if>
      <if test="ljbzmc != null">ljbzmc,</if>
      <if test="rydate != null">rydate,</if>
      <if test="cydate != null">cydate,</if>
      <if test="drgbh != null">drgbh,</if>
      <if test="rzflag != null">rzflag,</if>
      <if test="wrzyy != null">wrzyy,</if>
      <if test="tczf != null">tczf,</if>
      <if test="drgzf != null">drgzf,</if>
      <if test="jystatus != null">jystatus,</if>
      <if test="jlly != null">jlly,</if>
      <if test="qjcs != null">qjcs,</if>
      <if test="qjcgcs != null">qjcgcs,</if>
      <if test="qzrq != null">qzrq,</if>
      <if test="zyzt != null">zyzt,</if>
      <if test="zdf != null">zdf,</if>
      <if test="hisJsdate != null">his_jsdate,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jzh != null and jzh != ''">#{jzh},</if>
      <if test="brbs != null and brbs != ''">#{brbs},</if>
      <if test="brid != null">#{brid},</if>
      <if test="zyid != null">#{zyid},</if>
      <if test="username != null">#{username},</if>
      <if test="ylfkfs != null">#{ylfkfs},</if>
      <if test="jkkh != null">#{jkkh},</if>
      <if test="zycs != null">#{zycs},</if>
      <if test="bah != null">#{bah},</if>
      <if test="xm != null">#{xm},</if>
      <if test="xb != null">#{xb},</if>
      <if test="csrq != null">#{csrq},</if>
      <if test="nl != null">#{nl},</if>
      <if test="gj != null">#{gj},</if>
      <if test="bzyzsnl != null">#{bzyzsnl},</if>
      <if test="xsecstz != null">#{xsecstz},</if>
      <if test="xserytz != null">#{xserytz},</if>
      <if test="csd != null">#{csd},</if>
      <if test="gg != null">#{gg},</if>
      <if test="mz != null">#{mz},</if>
      <if test="sfzh != null">#{sfzh},</if>
      <if test="zy != null">#{zy},</if>
      <if test="hy != null">#{hy},</if>
      <if test="xzz != null">#{xzz},</if>
      <if test="dh != null">#{dh},</if>
      <if test="yb1 != null">#{yb1},</if>
      <if test="hkdz != null">#{hkdz},</if>
      <if test="yb2 != null">#{yb2},</if>
      <if test="gzdwjdz != null">#{gzdwjdz},</if>
      <if test="dwdh != null">#{dwdh},</if>
      <if test="yb3 != null">#{yb3},</if>
      <if test="lxrxm != null">#{lxrxm},</if>
      <if test="gx != null">#{gx},</if>
      <if test="dz != null">#{dz},</if>
      <if test="dh2 != null">#{dh2},</if>
      <if test="rytj != null">#{rytj},</if>
      <if test="rysj != null">#{rysj},</if>
      <if test="rysjs != null">#{rysjs},</if>
      <if test="rykb != null">#{rykb},</if>
      <if test="rybf != null">#{rybf},</if>
      <if test="zkkb != null">#{zkkb},</if>
      <if test="cysj != null">#{cysj},</if>
      <if test="cysjs != null">#{cysjs},</if>
      <if test="cykb != null">#{cykb},</if>
      <if test="cybf != null">#{cybf},</if>
      <if test="sjzyts != null">#{sjzyts},</if>
      <if test="mzzd != null">#{mzzd},</if>
      <if test="jbbm != null">#{jbbm},</if>
      <if test="zyzd != null">#{zyzd},</if>
      <if test="jbdm != null">#{jbdm},</if>
      <if test="rybq != null">#{rybq},</if>
      <if test="qtzd1 != null">#{qtzd1},</if>
      <if test="jbdm1 != null">#{jbdm1},</if>
      <if test="rybq1 != null">#{rybq1},</if>
      <if test="qtzd2 != null">#{qtzd2},</if>
      <if test="jbdm2 != null">#{jbdm2},</if>
      <if test="rybq2 != null">#{rybq2},</if>
      <if test="qtzd3 != null">#{qtzd3},</if>
      <if test="jbdm3 != null">#{jbdm3},</if>
      <if test="rybq3 != null">#{rybq3},</if>
      <if test="qtzd4 != null">#{qtzd4},</if>
      <if test="jbdm4 != null">#{jbdm4},</if>
      <if test="rybq4 != null">#{rybq4},</if>
      <if test="qtzd5 != null">#{qtzd5},</if>
      <if test="jbdm5 != null">#{jbdm5},</if>
      <if test="rybq5 != null">#{rybq5},</if>
      <if test="qtzd6 != null">#{qtzd6},</if>
      <if test="jbdm6 != null">#{jbdm6},</if>
      <if test="rybq6 != null">#{rybq6},</if>
      <if test="qtzd7 != null">#{qtzd7},</if>
      <if test="jbdm7 != null">#{jbdm7},</if>
      <if test="rybq7 != null">#{rybq7},</if>
      <if test="qtzd8 != null">#{qtzd8},</if>
      <if test="jbdm8 != null">#{jbdm8},</if>
      <if test="rybq8 != null">#{rybq8},</if>
      <if test="qtzd9 != null">#{qtzd9},</if>
      <if test="jbdm9 != null">#{jbdm9},</if>
      <if test="rybq9 != null">#{rybq9},</if>
      <if test="qtzd10 != null">#{qtzd10},</if>
      <if test="jbdm10 != null">#{jbdm10},</if>
      <if test="rybq10 != null">#{rybq10},</if>
      <if test="qtzd11 != null">#{qtzd11},</if>
      <if test="jbdm11 != null">#{jbdm11},</if>
      <if test="rybq11 != null">#{rybq11},</if>
      <if test="qtzd12 != null">#{qtzd12},</if>
      <if test="jbdm12 != null">#{jbdm12},</if>
      <if test="rybq12 != null">#{rybq12},</if>
      <if test="qtzd13 != null">#{qtzd13},</if>
      <if test="jbdm13 != null">#{jbdm13},</if>
      <if test="rybq13 != null">#{rybq13},</if>
      <if test="qtzd14 != null">#{qtzd14},</if>
      <if test="jbdm14 != null">#{jbdm14},</if>
      <if test="rybq14 != null">#{rybq14},</if>
      <if test="qtzd15 != null">#{qtzd15},</if>
      <if test="jbdm15 != null">#{jbdm15},</if>
      <if test="rybq15 != null">#{rybq15},</if>
      <if test="wbyy != null">#{wbyy},</if>
      <if test="h23 != null">#{h23},</if>
      <if test="blzd != null">#{blzd},</if>
      <if test="jbmm != null">#{jbmm},</if>
      <if test="blh != null">#{blh},</if>
      <if test="ywgm != null">#{ywgm},</if>
      <if test="gmyw != null">#{gmyw},</if>
      <if test="swhzsj != null">#{swhzsj},</if>
      <if test="xx != null">#{xx},</if>
      <if test="rh != null">#{rh},</if>
      <if test="kzr != null">#{kzr},</if>
      <if test="zrys != null">#{zrys},</if>
      <if test="zzys != null">#{zzys},</if>
      <if test="zyys != null">#{zyys},</if>
      <if test="zrhs != null">#{zrhs},</if>
      <if test="jxys != null">#{jxys},</if>
      <if test="sxys != null">#{sxys},</if>
      <if test="bmy != null">#{bmy},</if>
      <if test="bazl != null">#{bazl},</if>
      <if test="zkys != null">#{zkys},</if>
      <if test="zkhs != null">#{zkhs},</if>
      <if test="zkrq != null">#{zkrq},</if>
      <if test="ssjczbm1 != null">#{ssjczbm1},</if>
      <if test="ssjczrq1 != null">#{ssjczrq1},</if>
      <if test="ssjb1 != null">#{ssjb1},</if>
      <if test="ssjczmc1 != null">#{ssjczmc1},</if>
      <if test="sz1 != null">#{sz1},</if>
      <if test="yz1 != null">#{yz1},</if>
      <if test="ez1 != null">#{ez1},</if>
      <if test="qkdj1 != null">#{qkdj1},</if>
      <if test="qkyhlb1 != null">#{qkyhlb1},</if>
      <if test="mzfs1 != null">#{mzfs1},</if>
      <if test="mzys1 != null">#{mzys1},</if>
      <if test="ssjczbm2 != null">#{ssjczbm2},</if>
      <if test="ssjczrq2 != null">#{ssjczrq2},</if>
      <if test="ssjb2 != null">#{ssjb2},</if>
      <if test="ssjczmc2 != null">#{ssjczmc2},</if>
      <if test="sz2 != null">#{sz2},</if>
      <if test="yz2 != null">#{yz2},</if>
      <if test="ez2 != null">#{ez2},</if>
      <if test="qkdj2 != null">#{qkdj2},</if>
      <if test="qkyhlb2 != null">#{qkyhlb2},</if>
      <if test="mzfs2 != null">#{mzfs2},</if>
      <if test="mzys2 != null">#{mzys2},</if>
      <if test="ssjczbm3 != null">#{ssjczbm3},</if>
      <if test="ssjczrq3 != null">#{ssjczrq3},</if>
      <if test="ssjb3 != null">#{ssjb3},</if>
      <if test="ssjczmc3 != null">#{ssjczmc3},</if>
      <if test="sz3 != null">#{sz3},</if>
      <if test="yz3 != null">#{yz3},</if>
      <if test="ez3 != null">#{ez3},</if>
      <if test="qkdj3 != null">#{qkdj3},</if>
      <if test="qkyhlb3 != null">#{qkyhlb3},</if>
      <if test="mzfs3 != null">#{mzfs3},</if>
      <if test="mzys3 != null">#{mzys3},</if>
      <if test="ssjczbm4 != null">#{ssjczbm4},</if>
      <if test="ssjczrq4 != null">#{ssjczrq4},</if>
      <if test="ssjb4 != null">#{ssjb4},</if>
      <if test="ssjczmc4 != null">#{ssjczmc4},</if>
      <if test="sz4 != null">#{sz4},</if>
      <if test="yz4 != null">#{yz4},</if>
      <if test="ez4 != null">#{ez4},</if>
      <if test="qkdj4 != null">#{qkdj4},</if>
      <if test="qkyhlb4 != null">#{qkyhlb4},</if>
      <if test="mzfs4 != null">#{mzfs4},</if>
      <if test="mzys4 != null">#{mzys4},</if>
      <if test="ssjczbm5 != null">#{ssjczbm5},</if>
      <if test="ssjczrq5 != null">#{ssjczrq5},</if>
      <if test="ssjb5 != null">#{ssjb5},</if>
      <if test="ssjczmc5 != null">#{ssjczmc5},</if>
      <if test="sz5 != null">#{sz5},</if>
      <if test="yz5 != null">#{yz5},</if>
      <if test="ez5 != null">#{ez5},</if>
      <if test="qkdj5 != null">#{qkdj5},</if>
      <if test="qkyhlb5 != null">#{qkyhlb5},</if>
      <if test="mzfs5 != null">#{mzfs5},</if>
      <if test="mzys5 != null">#{mzys5},</if>
      <if test="ssjczbm6 != null">#{ssjczbm6},</if>
      <if test="ssjczrq6 != null">#{ssjczrq6},</if>
      <if test="ssjb6 != null">#{ssjb6},</if>
      <if test="ssjczmc6 != null">#{ssjczmc6},</if>
      <if test="sz6 != null">#{sz6},</if>
      <if test="yz6 != null">#{yz6},</if>
      <if test="ez6 != null">#{ez6},</if>
      <if test="qkdj6 != null">#{qkdj6},</if>
      <if test="qkyhlb6 != null">#{qkyhlb6},</if>
      <if test="mzfs6 != null">#{mzfs6},</if>
      <if test="mzys6 != null">#{mzys6},</if>
      <if test="ssjczbm7 != null">#{ssjczbm7},</if>
      <if test="ssjczrq7 != null">#{ssjczrq7},</if>
      <if test="ssjb7 != null">#{ssjb7},</if>
      <if test="ssjczmc7 != null">#{ssjczmc7},</if>
      <if test="sz7 != null">#{sz7},</if>
      <if test="yz7 != null">#{yz7},</if>
      <if test="ez7 != null">#{ez7},</if>
      <if test="qkdj7 != null">#{qkdj7},</if>
      <if test="qkyhlb7 != null">#{qkyhlb7},</if>
      <if test="mzfs7 != null">#{mzfs7},</if>
      <if test="mzys7 != null">#{mzys7},</if>
      <if test="lyfs != null">#{lyfs},</if>
      <if test="yzzyYljg != null">#{yzzyYljg},</if>
      <if test="wsyYljg != null">#{wsyYljg},</if>
      <if test="sfzzyjh != null">#{sfzzyjh},</if>
      <if test="md != null">#{md},</if>
      <if test="ryqT != null">#{ryqT},</if>
      <if test="ryqXs != null">#{ryqXs},</if>
      <if test="ryqF != null">#{ryqF},</if>
      <if test="ryhT != null">#{ryhT},</if>
      <if test="ryhXs != null">#{ryhXs},</if>
      <if test="ryhF != null">#{ryhF},</if>
      <if test="zfy != null">#{zfy},</if>
      <if test="zfje != null">#{zfje},</if>
      <if test="ylfuf != null">#{ylfuf},</if>
      <if test="zlczf != null">#{zlczf},</if>
      <if test="hlf != null">#{hlf},</if>
      <if test="qtfy != null">#{qtfy},</if>
      <if test="blzdf != null">#{blzdf},</if>
      <if test="syszdf != null">#{syszdf},</if>
      <if test="yxxzdf != null">#{yxxzdf},</if>
      <if test="lczdxmf != null">#{lczdxmf},</if>
      <if test="fsszlxmf != null">#{fsszlxmf},</if>
      <if test="wlzlf != null">#{wlzlf},</if>
      <if test="sszlf != null">#{sszlf},</if>
      <if test="maf != null">#{maf},</if>
      <if test="ssf != null">#{ssf},</if>
      <if test="kff != null">#{kff},</if>
      <if test="zyzlf != null">#{zyzlf},</if>
      <if test="xyf != null">#{xyf},</if>
      <if test="kjywf != null">#{kjywf},</if>
      <if test="zcyf != null">#{zcyf},</if>
      <if test="zcyf1 != null">#{zcyf1},</if>
      <if test="xf != null">#{xf},</if>
      <if test="bdblzpf != null">#{bdblzpf},</if>
      <if test="qdblzpf != null">#{qdblzpf},</if>
      <if test="nxyzlzpf != null">#{nxyzlzpf},</if>
      <if test="xbyzlzpf != null">#{xbyzlzpf},</if>
      <if test="hcyyclf != null">#{hcyyclf},</if>
      <if test="yyclf != null">#{yyclf},</if>
      <if test="ycxyyclf != null">#{ycxyyclf},</if>
      <if test="qtf != null">#{qtf},</if>
      <if test="psh != null">#{psh},</if>
      <if test="basytype != null">#{basytype},</if>
      <if test="orgcode != null">#{orgcode},</if>
      <if test="bycode != null">#{bycode},</if>
      <if test="opname != null">#{opname},</if>
      <if test="opdate != null">#{opdate},</if>
      <if test="xgcs != null">#{xgcs},</if>
      <if test="cxflag != null">#{cxflag},</if>
      <if test="jsdate != null">#{jsdate},</if>
      <if test="cqflag != null">#{cqflag},</if>
      <if test="jyflag != null">#{jyflag},</if>
      <if test="datasrc != null">#{datasrc},</if>
      <if test="jxstatus != null">#{jxstatus},</if>
      <if test="sfsslcljgl != null">#{sfsslcljgl},</if>
      <if test="sfwclclj != null">#{sfwclclj},</if>
      <if test="drgmc != null">#{drgmc},</if>
      <if test="sfby != null">#{sfby},</if>
      <if test="byyy != null">#{byyy},</if>
      <if test="ljbzmc != null">#{ljbzmc},</if>
      <if test="rydate != null">#{rydate},</if>
      <if test="cydate != null">#{cydate},</if>
      <if test="drgbh != null">#{drgbh},</if>
      <if test="rzflag != null">#{rzflag},</if>
      <if test="wrzyy != null">#{wrzyy},</if>
      <if test="tczf != null">#{tczf},</if>
      <if test="drgzf != null">#{drgzf},</if>
      <if test="jystatus != null">#{jystatus},</if>
      <if test="jlly != null">#{jlly},</if>
      <if test="qjcs != null">#{qjcs},</if>
      <if test="qjcgcs != null">#{qjcgcs},</if>
      <if test="qzrq != null">#{qzrq},</if>
      <if test="zyzt != null">#{zyzt},</if>
      <if test="zdf != null">#{zdf},</if>
      <if test="hisJsdate != null">#{hisJsdate},</if>
    </trim>
  </insert>

  <update id="updateBaSyjlByBrxx" parameterType="BaSyjl">
    UPDATE ba_syjl a
      JOIN brxx b ON a.brid = b.brid AND a.zyid=b.zyid
      SET a.zyzt = b.zyzt,a.cydate=CASE WHEN b.rydate IS NULL THEN b.rydate ELSE b.cydate END,a.cysj=DATE_FORMAT(CASE WHEN b.rydate IS NULL THEN b.rydate ELSE b.cydate END, '%Y-%m-%d'),a.zyys=b.doctorname,a.cykb=b.deptname,
    a.xb= (case when b.sex='1' then '男' when b.sex='2' then '女' else b.sex end),
    a.nl=(case when b.age like '%岁%' THEN SUBSTRING(b.age, 1, LOCATE('岁', b.age) - 1) else (case when b.age like '%\%' or b.age like '%时%'  or b.age like '%月%' or b.age like '%分钟%' or b.age like '%天%' then null else b.age end) end)
    WHERE a.brid=#{brid} AND IFNULL(a.zyid,'')=#{zyid} AND zyh != ''
  </update>


  <update id="updateBaSyjl" parameterType="BaSyjl">
    update ba_syjl
    <trim prefix="SET" suffixOverrides=",">
      <if test="jzh != null and jzh != ''">jzh = #{jzh},</if>
      <if test="brbs != null and brbs != ''">brbs = #{brbs},</if>
      <if test="brid != null">brid = #{brid},</if>
      <if test="zyid != null">zyid = #{zyid},</if>
      <if test="username != null">username = #{username},</if>
      <if test="ylfkfs != null">ylfkfs = #{ylfkfs},</if>
      <if test="jkkh != null">jkkh = #{jkkh},</if>
      <if test="zycs != null">zycs = #{zycs},</if>
      <if test="bah != null">bah = #{bah},</if>
      <if test="xm != null">xm = #{xm},</if>
      <if test="xb != null">xb = #{xb},</if>
      <if test="csrq != null">csrq = #{csrq},</if>
      <if test="nl != null">nl = #{nl},</if>
      <if test="gj != null">gj = #{gj},</if>
      <if test="bzyzsnl != null">bzyzsnl = #{bzyzsnl},</if>
      <if test="xsecstz != null">xsecstz = #{xsecstz},</if>
      <if test="xserytz != null">xserytz = #{xserytz},</if>
      <if test="csd != null">csd = #{csd},</if>
      <if test="gg != null">gg = #{gg},</if>
      <if test="mz != null">mz = #{mz},</if>
      <if test="sfzh != null">sfzh = #{sfzh},</if>
      <if test="zy != null">zy = #{zy},</if>
      <if test="hy != null">hy = #{hy},</if>
      <if test="xzz != null">xzz = #{xzz},</if>
      <if test="dh != null">dh = #{dh},</if>
      <if test="yb1 != null">yb1 = #{yb1},</if>
      <if test="hkdz != null">hkdz = #{hkdz},</if>
      <if test="yb2 != null">yb2 = #{yb2},</if>
      <if test="gzdwjdz != null">gzdwjdz = #{gzdwjdz},</if>
      <if test="dwdh != null">dwdh = #{dwdh},</if>
      <if test="yb3 != null">yb3 = #{yb3},</if>
      <if test="lxrxm != null">lxrxm = #{lxrxm},</if>
      <if test="gx != null">gx = #{gx},</if>
      <if test="dz != null">dz = #{dz},</if>
      <if test="dh2 != null">dh2 = #{dh2},</if>
      <if test="rytj != null">rytj = #{rytj},</if>
      <if test="rysj != null">rysj = #{rysj},</if>
      <if test="rysjs != null">rysjs = #{rysjs},</if>
      <if test="rykb != null">rykb = #{rykb},</if>
      <if test="rybf != null">rybf = #{rybf},</if>
      <if test="zkkb != null">zkkb = #{zkkb},</if>
      <if test="cysj != null">cysj = #{cysj},</if>
      <if test="cysjs != null">cysjs = #{cysjs},</if>
      <if test="cykb != null">cykb = #{cykb},</if>
      <if test="cybf != null">cybf = #{cybf},</if>
      <if test="sjzyts != null">sjzyts = #{sjzyts},</if>
      <if test="mzzd != null">mzzd = #{mzzd},</if>
      <if test="jbbm != null">jbbm = #{jbbm},</if>
      <if test="zyzd != null">zyzd = #{zyzd},</if>
      <if test="jbdm != null">jbdm = #{jbdm},</if>
      <if test="rybq != null">rybq = #{rybq},</if>
      <if test="qtzd1 != null">qtzd1 = #{qtzd1},</if>
      <if test="jbdm1 != null">jbdm1 = #{jbdm1},</if>
      <if test="rybq1 != null">rybq1 = #{rybq1},</if>
      <if test="qtzd2 != null">qtzd2 = #{qtzd2},</if>
      <if test="jbdm2 != null">jbdm2 = #{jbdm2},</if>
      <if test="rybq2 != null">rybq2 = #{rybq2},</if>
      <if test="qtzd3 != null">qtzd3 = #{qtzd3},</if>
      <if test="jbdm3 != null">jbdm3 = #{jbdm3},</if>
      <if test="rybq3 != null">rybq3 = #{rybq3},</if>
      <if test="qtzd4 != null">qtzd4 = #{qtzd4},</if>
      <if test="jbdm4 != null">jbdm4 = #{jbdm4},</if>
      <if test="rybq4 != null">rybq4 = #{rybq4},</if>
      <if test="qtzd5 != null">qtzd5 = #{qtzd5},</if>
      <if test="jbdm5 != null">jbdm5 = #{jbdm5},</if>
      <if test="rybq5 != null">rybq5 = #{rybq5},</if>
      <if test="qtzd6 != null">qtzd6 = #{qtzd6},</if>
      <if test="jbdm6 != null">jbdm6 = #{jbdm6},</if>
      <if test="rybq6 != null">rybq6 = #{rybq6},</if>
      <if test="qtzd7 != null">qtzd7 = #{qtzd7},</if>
      <if test="jbdm7 != null">jbdm7 = #{jbdm7},</if>
      <if test="rybq7 != null">rybq7 = #{rybq7},</if>
      <if test="qtzd8 != null">qtzd8 = #{qtzd8},</if>
      <if test="jbdm8 != null">jbdm8 = #{jbdm8},</if>
      <if test="rybq8 != null">rybq8 = #{rybq8},</if>
      <if test="qtzd9 != null">qtzd9 = #{qtzd9},</if>
      <if test="jbdm9 != null">jbdm9 = #{jbdm9},</if>
      <if test="rybq9 != null">rybq9 = #{rybq9},</if>
      <if test="qtzd10 != null">qtzd10 = #{qtzd10},</if>
      <if test="jbdm10 != null">jbdm10 = #{jbdm10},</if>
      <if test="rybq10 != null">rybq10 = #{rybq10},</if>
      <if test="qtzd11 != null">qtzd11 = #{qtzd11},</if>
      <if test="jbdm11 != null">jbdm11 = #{jbdm11},</if>
      <if test="rybq11 != null">rybq11 = #{rybq11},</if>
      <if test="qtzd12 != null">qtzd12 = #{qtzd12},</if>
      <if test="jbdm12 != null">jbdm12 = #{jbdm12},</if>
      <if test="rybq12 != null">rybq12 = #{rybq12},</if>
      <if test="qtzd13 != null">qtzd13 = #{qtzd13},</if>
      <if test="jbdm13 != null">jbdm13 = #{jbdm13},</if>
      <if test="rybq13 != null">rybq13 = #{rybq13},</if>
      <if test="qtzd14 != null">qtzd14 = #{qtzd14},</if>
      <if test="jbdm14 != null">jbdm14 = #{jbdm14},</if>
      <if test="rybq14 != null">rybq14 = #{rybq14},</if>
      <if test="qtzd15 != null">qtzd15 = #{qtzd15},</if>
      <if test="jbdm15 != null">jbdm15 = #{jbdm15},</if>
      <if test="rybq15 != null">rybq15 = #{rybq15},</if>
      <if test="wbyy != null">wbyy = #{wbyy},</if>
      <if test="h23 != null">h23 = #{h23},</if>
      <if test="blzd != null">blzd = #{blzd},</if>
      <if test="jbmm != null">jbmm = #{jbmm},</if>
      <if test="blh != null">blh = #{blh},</if>
      <if test="ywgm != null">ywgm = #{ywgm},</if>
      <if test="gmyw != null">gmyw = #{gmyw},</if>
      <if test="swhzsj != null">swhzsj = #{swhzsj},</if>
      <if test="xx != null">xx = #{xx},</if>
      <if test="rh != null">rh = #{rh},</if>
      <if test="kzr != null">kzr = #{kzr},</if>
      <if test="zrys != null">zrys = #{zrys},</if>
      <if test="zzys != null">zzys = #{zzys},</if>
      <if test="zyys != null">zyys = #{zyys},</if>
      <if test="zrhs != null">zrhs = #{zrhs},</if>
      <if test="jxys != null">jxys = #{jxys},</if>
      <if test="sxys != null">sxys = #{sxys},</if>
      <if test="bmy != null">bmy = #{bmy},</if>
      <if test="bazl != null">bazl = #{bazl},</if>
      <if test="zkys != null">zkys = #{zkys},</if>
      <if test="zkhs != null">zkhs = #{zkhs},</if>
      <if test="zkrq != null">zkrq = #{zkrq},</if>
      <if test="ssjczbm1 != null">ssjczbm1 = #{ssjczbm1},</if>
      <if test="ssjczrq1 != null">ssjczrq1 = #{ssjczrq1},</if>
      <if test="ssjb1 != null">ssjb1 = #{ssjb1},</if>
      <if test="ssjczmc1 != null">ssjczmc1 = #{ssjczmc1},</if>
      <if test="sz1 != null">sz1 = #{sz1},</if>
      <if test="yz1 != null">yz1 = #{yz1},</if>
      <if test="ez1 != null">ez1 = #{ez1},</if>
      <if test="qkdj1 != null">qkdj1 = #{qkdj1},</if>
      <if test="qkyhlb1 != null">qkyhlb1 = #{qkyhlb1},</if>
      <if test="mzfs1 != null">mzfs1 = #{mzfs1},</if>
      <if test="mzys1 != null">mzys1 = #{mzys1},</if>
      <if test="ssjczbm2 != null">ssjczbm2 = #{ssjczbm2},</if>
      <if test="ssjczrq2 != null">ssjczrq2 = #{ssjczrq2},</if>
      <if test="ssjb2 != null">ssjb2 = #{ssjb2},</if>
      <if test="ssjczmc2 != null">ssjczmc2 = #{ssjczmc2},</if>
      <if test="sz2 != null">sz2 = #{sz2},</if>
      <if test="yz2 != null">yz2 = #{yz2},</if>
      <if test="ez2 != null">ez2 = #{ez2},</if>
      <if test="qkdj2 != null">qkdj2 = #{qkdj2},</if>
      <if test="qkyhlb2 != null">qkyhlb2 = #{qkyhlb2},</if>
      <if test="mzfs2 != null">mzfs2 = #{mzfs2},</if>
      <if test="mzys2 != null">mzys2 = #{mzys2},</if>
      <if test="ssjczbm3 != null">ssjczbm3 = #{ssjczbm3},</if>
      <if test="ssjczrq3 != null">ssjczrq3 = #{ssjczrq3},</if>
      <if test="ssjb3 != null">ssjb3 = #{ssjb3},</if>
      <if test="ssjczmc3 != null">ssjczmc3 = #{ssjczmc3},</if>
      <if test="sz3 != null">sz3 = #{sz3},</if>
      <if test="yz3 != null">yz3 = #{yz3},</if>
      <if test="ez3 != null">ez3 = #{ez3},</if>
      <if test="qkdj3 != null">qkdj3 = #{qkdj3},</if>
      <if test="qkyhlb3 != null">qkyhlb3 = #{qkyhlb3},</if>
      <if test="mzfs3 != null">mzfs3 = #{mzfs3},</if>
      <if test="mzys3 != null">mzys3 = #{mzys3},</if>
      <if test="ssjczbm4 != null">ssjczbm4 = #{ssjczbm4},</if>
      <if test="ssjczrq4 != null">ssjczrq4 = #{ssjczrq4},</if>
      <if test="ssjb4 != null">ssjb4 = #{ssjb4},</if>
      <if test="ssjczmc4 != null">ssjczmc4 = #{ssjczmc4},</if>
      <if test="sz4 != null">sz4 = #{sz4},</if>
      <if test="yz4 != null">yz4 = #{yz4},</if>
      <if test="ez4 != null">ez4 = #{ez4},</if>
      <if test="qkdj4 != null">qkdj4 = #{qkdj4},</if>
      <if test="qkyhlb4 != null">qkyhlb4 = #{qkyhlb4},</if>
      <if test="mzfs4 != null">mzfs4 = #{mzfs4},</if>
      <if test="mzys4 != null">mzys4 = #{mzys4},</if>
      <if test="ssjczbm5 != null">ssjczbm5 = #{ssjczbm5},</if>
      <if test="ssjczrq5 != null">ssjczrq5 = #{ssjczrq5},</if>
      <if test="ssjb5 != null">ssjb5 = #{ssjb5},</if>
      <if test="ssjczmc5 != null">ssjczmc5 = #{ssjczmc5},</if>
      <if test="sz5 != null">sz5 = #{sz5},</if>
      <if test="yz5 != null">yz5 = #{yz5},</if>
      <if test="ez5 != null">ez5 = #{ez5},</if>
      <if test="qkdj5 != null">qkdj5 = #{qkdj5},</if>
      <if test="qkyhlb5 != null">qkyhlb5 = #{qkyhlb5},</if>
      <if test="mzfs5 != null">mzfs5 = #{mzfs5},</if>
      <if test="mzys5 != null">mzys5 = #{mzys5},</if>
      <if test="ssjczbm6 != null">ssjczbm6 = #{ssjczbm6},</if>
      <if test="ssjczrq6 != null">ssjczrq6 = #{ssjczrq6},</if>
      <if test="ssjb6 != null">ssjb6 = #{ssjb6},</if>
      <if test="ssjczmc6 != null">ssjczmc6 = #{ssjczmc6},</if>
      <if test="sz6 != null">sz6 = #{sz6},</if>
      <if test="yz6 != null">yz6 = #{yz6},</if>
      <if test="ez6 != null">ez6 = #{ez6},</if>
      <if test="qkdj6 != null">qkdj6 = #{qkdj6},</if>
      <if test="qkyhlb6 != null">qkyhlb6 = #{qkyhlb6},</if>
      <if test="mzfs6 != null">mzfs6 = #{mzfs6},</if>
      <if test="mzys6 != null">mzys6 = #{mzys6},</if>
      <if test="ssjczbm7 != null">ssjczbm7 = #{ssjczbm7},</if>
      <if test="ssjczrq7 != null">ssjczrq7 = #{ssjczrq7},</if>
      <if test="ssjb7 != null">ssjb7 = #{ssjb7},</if>
      <if test="ssjczmc7 != null">ssjczmc7 = #{ssjczmc7},</if>
      <if test="sz7 != null">sz7 = #{sz7},</if>
      <if test="yz7 != null">yz7 = #{yz7},</if>
      <if test="ez7 != null">ez7 = #{ez7},</if>
      <if test="qkdj7 != null">qkdj7 = #{qkdj7},</if>
      <if test="qkyhlb7 != null">qkyhlb7 = #{qkyhlb7},</if>
      <if test="mzfs7 != null">mzfs7 = #{mzfs7},</if>
      <if test="mzys7 != null">mzys7 = #{mzys7},</if>
      <if test="lyfs != null">lyfs = #{lyfs},</if>
      <if test="yzzyYljg != null">yzzy_yljg = #{yzzyYljg},</if>
      <if test="wsyYljg != null">wsy_yljg = #{wsyYljg},</if>
      <if test="sfzzyjh != null">sfzzyjh = #{sfzzyjh},</if>
      <if test="md != null">md = #{md},</if>
      <if test="ryqT != null">ryq_t = #{ryqT},</if>
      <if test="ryqXs != null">ryq_xs = #{ryqXs},</if>
      <if test="ryqF != null">ryq_f = #{ryqF},</if>
      <if test="ryhT != null">ryh_t = #{ryhT},</if>
      <if test="ryhXs != null">ryh_xs = #{ryhXs},</if>
      <if test="ryhF != null">ryh_f = #{ryhF},</if>
      <if test="zfy != null">zfy = #{zfy},</if>
      <if test="zfje != null">zfje = #{zfje},</if>
      <if test="ylfuf != null">ylfuf = #{ylfuf},</if>
      <if test="zlczf != null">zlczf = #{zlczf},</if>
      <if test="hlf != null">hlf = #{hlf},</if>
      <if test="qtfy != null">qtfy = #{qtfy},</if>
      <if test="blzdf != null">blzdf = #{blzdf},</if>
      <if test="syszdf != null">syszdf = #{syszdf},</if>
      <if test="yxxzdf != null">yxxzdf = #{yxxzdf},</if>
      <if test="lczdxmf != null">lczdxmf = #{lczdxmf},</if>
      <if test="fsszlxmf != null">fsszlxmf = #{fsszlxmf},</if>
      <if test="wlzlf != null">wlzlf = #{wlzlf},</if>
      <if test="sszlf != null">sszlf = #{sszlf},</if>
      <if test="maf != null">maf = #{maf},</if>
      <if test="ssf != null">ssf = #{ssf},</if>
      <if test="kff != null">kff = #{kff},</if>
      <if test="zyzlf != null">zyzlf = #{zyzlf},</if>
      <if test="xyf != null">xyf = #{xyf},</if>
      <if test="kjywf != null">kjywf = #{kjywf},</if>
      <if test="zcyf != null">zcyf = #{zcyf},</if>
      <if test="zcyf1 != null">zcyf1 = #{zcyf1},</if>
      <if test="xf != null">xf = #{xf},</if>
      <if test="bdblzpf != null">bdblzpf = #{bdblzpf},</if>
      <if test="qdblzpf != null">qdblzpf = #{qdblzpf},</if>
      <if test="nxyzlzpf != null">nxyzlzpf = #{nxyzlzpf},</if>
      <if test="xbyzlzpf != null">xbyzlzpf = #{xbyzlzpf},</if>
      <if test="hcyyclf != null">hcyyclf = #{hcyyclf},</if>
      <if test="yyclf != null">yyclf = #{yyclf},</if>
      <if test="ycxyyclf != null">ycxyyclf = #{ycxyyclf},</if>
      <if test="qtf != null">qtf = #{qtf},</if>
      <if test="psh != null">psh = #{psh},</if>
      <if test="basytype != null">basytype = #{basytype},</if>
      <if test="orgcode != null">orgcode = #{orgcode},</if>
      <if test="bycode != null">bycode = #{bycode},</if>
      <if test="opname != null">opname = #{opname},</if>
      <if test="opdate != null">opdate = #{opdate},</if>
      <if test="xgcs != null">xgcs = #{xgcs},</if>
      <if test="cxflag != null">cxflag = #{cxflag},</if>
      <if test="jsdate != null">jsdate = #{jsdate},</if>
      <if test="cqflag != null">cqflag = #{cqflag},</if>
      <if test="jyflag != null">jyflag = #{jyflag},</if>
      <if test="datasrc != null">datasrc = #{datasrc},</if>
      <if test="jxstatus != null">jxstatus = #{jxstatus},</if>
      <if test="sfsslcljgl != null">sfsslcljgl = #{sfsslcljgl},</if>
      <if test="sfwclclj != null">sfwclclj = #{sfwclclj},</if>
      <choose>
        <when test="drgmc != null">drgmc = #{drgmc},</when>
        <when test="drgmc == null and drgbh == '000'">drgmc = null,</when>
      </choose>
      <if test="sfby != null">sfby = #{sfby},</if>
      <if test="byyy != null">byyy = #{byyy},</if>
      <if test="ljbzmc != null">ljbzmc = #{ljbzmc},</if>
      <if test="rydate != null">rydate = #{rydate},</if>
      <if test="cydate != null">cydate = #{cydate},</if>
      <if test="drgbh != null">drgbh = #{drgbh},</if>
      <if test="rzflag != null">rzflag = #{rzflag},</if>
      <if test="wrzyy != null">wrzyy = #{wrzyy},</if>
      <if test="tczf != null">tczf = #{tczf},</if>
      <if test="drgzf != null">drgzf = #{drgzf},</if>
      <if test="jystatus != null">jystatus = #{jystatus},</if>
      <if test="jlly != null">jlly = #{jlly},</if>
      <if test="qjcs != null">qjcs = #{qjcs},</if>
      <if test="qjcgcs != null">qjcgcs = #{qjcgcs},</if>
      <if test="qzrq != null">qzrq = #{qzrq},</if>
      <if test="zyzt != null">zyzt = #{zyzt},</if>
       <if test="zfbz != null">zfbz = #{zfbz},</if>
      <if test="zdf != null">zdf = #{zdf},</if>
      <if test="hisJsdate != null">his_jsdate = #{hisJsdate},</if>
    </trim>
    where id = #{id} or brbs=#{brbs}
  </update>


  <update id="updateDrgzf" parameterType="BaSyjl" >
    CALL usp_update_drgzf(#{brbs},#{username})
  </update>
  
  <update id="updatebasyextcndrg" parameterType="BaSyjl" >
    CALL usp_update_ba_syjl_ext(#{brbs},#{cndrgbh},#{zyzt})
  </update>
  
  <update id="updateBaSyjlFz">
    update ba_syjl
    set drgmc = (select drgmc from drgdict where drgbh = #{drgbh}),
        zfbz = (select zfbz from drgdict where drgbh = #{drgbh}),
        cmi = (select zfqz from drgdict where drgbh = #{drgbh}),
        drgbh = #{drgbh}
    where brbs = #{brbs}
  </update>

  <delete id="deleteBaSyjlById" parameterType="Long">
    delete from ba_syjl where id = #{id}
  </delete>

  <delete id="deleteBaSyjlByBrbs" parameterType="String">
    delete from ba_syjl where brbs = #{brbs}
  </delete>

  <delete id="deleteBaSyjlByIds" parameterType="String">
    delete from ba_syjl where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>


  <select id="selectDrgCostInfoByIcd" parameterType="BaSyjl" resultMap="DisOrOperDRGCostInfoResult">
    SELECT
      dd.drgbh AS drgbh,
      dd.drgmc AS drgmc,
      MAX(ba.jbdm) AS jbbm,
      MAX(ba.zyzd) AS zdmc,
      COUNT(*) AS bas,
      ROUND( IFNULL( SUM( ba.zfy ), 0 ) , 4 ) AS zfy,
      ROUND( IFNULL( MAX( dd.zfqz ), 0 ) , 4 ) AS zfqz,
      ROUND( IFNULL( AVG( ba.zfy ), 0 ) / ba.zfbz , 4 ) AS fyxhzs ,
      MAX(ba.ssjczbm1) AS ssbm,
      MAX(ba.ssjczmc1) AS ssmc
    FROM
      ba_syjl ba JOIN drgdict dd on ba.drgbh = dd.drgbh
    WHERE
      ba.jbdm = #{jbdm}
      <if test="ssjczbm1 != null and ssjczbm1 != ''"> AND ba.ssjczbm1 = #{ssjczbm1}</if>
    GROUP BY
      dd.drgbh,
      dd.drgmc;
  </select>


  <select id="selectProjUseInfoByIcd" parameterType="BaSyjl" resultMap="ProjUseInfoResult">
     call usp_drg_xmfyxxbyicd(#{jbdm},#{ssjczbm1},#{drgbh},#{type})
  </select>

  <select id="selectOperUesInfoByIcd" parameterType="BaSyjl" resultMap="OperUseInfoResult">
    call usp_drg_ssfyxxbyicd(#{jbdm},#{drgbh},#{type})
  </select>

  <select id="getDeptHbList" resultType="map">
    select case when cykb in ('产科', '妇科') then '妇产科'
                when cykb = '儿科' OR cykb LIKE '%新生儿%' then '新生儿与儿科'
                when cykb IN ('重症医学科', '神经ICU') then '神经ICU与重症医学科'
                when cykb in ('眼科', '眼科日间') then '眼科日间与眼科'
                when cykb in ('消化、肿瘤科', '消化科') then '消化内科'
                when cykb in ('中医科', '康复医学科') then '中医科与康复医学科'
                else cykb end as text,
           case when cykb in ('产科', '妇科') then '妇产科'
                when cykb = '儿科' OR cykb LIKE '%新生儿%' then '新生儿与儿科'
                when cykb IN ('重症医学科', '神经ICU') then '神经ICU与重症医学科'
                when cykb in ('眼科', '眼科日间') then '眼科日间与眼科'
                when cykb in ('消化、肿瘤科', '消化科') then '消化内科'
                when cykb in ('中医科', '康复医学科') then '中医科与康复医学科'
                else cykb end as value
    from ba_syjl
    where cydate > date_sub(now(), interval 3 month)
    and cykb is not null
    group by text
  </select>
  <select id="selectBaSyjlFzshYfz" resultType="com.ruoyi.system.domain.BaSyjl">
    select a.brid as brid, a.zyid as zyid, a.bah, a.xm, a.brbs, a.jzh
    from ba_syjl a left join drg_fzsh b on a.brid = b.brid and a.zyid = b.zyid
    <where>
      <if test="auditStatus != null and auditStatus == 9">and (b.brid is null or b.status = '2')</if>
      <if test="auditStatus != null and auditStatus != 9">and b.status = #{auditStatus}</if>
      <if test="bah != null and bah != ''">and bah = #{bah}</if>
      <if test="jzh != null  and jzh != ''">and a.jzh = #{jzh}</if>
      <if test="brbs != null  and brbs != ''">and a.brbs = #{brbs}</if>
      <if test="brid != null  and brid != ''">and a.brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and a.zyid = #{zyid}</if>
      <if test="username != null  and username != ''">and username like concat('%', #{username}, '%')</if>
      <if test="cydateStart != null and cydateStart != ''">
        and cydate &gt;= #{cydateStart}
      </if>
      <if test="xm != null  and xm != ''">and a.xm like concat('%', #{xm}, '%')</if>
      <if test="cydateEnd != null and cydateEnd != ''">
        and cydate &lt;= #{cydateEnd}
      </if>
      <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
      <if test="cykb != null  and cykb != ''">and cykb = #{cykb}</if>
      <if test="cykbList != null">
        and cykb in
        (<foreach collection="cykbList" separator="," item="item">
            #{item}
        </foreach>)
      </if>
    </where>
    order by a.rydate desc, a.cydate desc
  </select>

  <select id="selectDeptListByJgid" resultType="com.ruoyi.system.domain.BaSyjl">
    SELECT DISTINCT cykb FROM ba_syjl where username = #{jgid} and cykb is not null and rydate >= date_add(curdate(), interval -6 month)
  </select>
    <select id="selectYfzPatList" resultMap="BaSyjlResult">
      select a.id, a.jzh, brbs, a.brid, a.zyid, username, bah, a.xm, xb, cydate, rydate, his_jsdate,
             status as auditStatus,scflag,scerrlog
      from ba_syjl a left join drg_fzsh b on a.brid = b.brid and a.zyid = b.zyid
      <where>
        <if test="auditStatus != null and auditStatus == 9">and (b.brid is null or b.status = '2')</if>
        <if test="auditStatus != null and auditStatus != 9">and b.status = #{auditStatus}</if>
        <if test="bah != null  and bah != ''">and bah = #{bah}</if>
        <if test="xm != null  and xm != ''">and a.xm like concat('%', #{xm}, '%')</if>
        <if test="jzh != null  and jzh != ''">and a.jzh = #{jzh}</if>
        <if test="brbs != null  and brbs != ''">and brbs = #{brbs}</if>
        <if test="brid != null  and brid != ''">and a.brid = #{brid}</if>
        <if test="zyid != null  and zyid != ''">and a.zyid = #{zyid}</if>
        <if test="zyys != null  and zyys != ''">and zyys = #{zyys}</if>
        <if test="cykb != null  and cykb != ''">and cykb = #{cykb}</if>
        <if test="username != null  and username != ''">and username like concat('%', #{username}, '%')</if>
        <if test="cydateStart != null and cydateStart != ''">
          and cydate &gt;= #{cydateStart}
        </if>
        <if test="cydateEnd != null and cydateEnd != ''">
          and cydate &lt;= #{cydateEnd}
        </if>
        <if test="zyzt != null  and zyzt != ''">and zyzt = #{zyzt}</if>
        <if test="cykbList != null">
          and cykb in
          (<foreach collection="cykbList" separator="," item="item">
          #{item}
        </foreach>)
        </if>
      </where>
      order by rydate desc
    </select>
  <select id="selectRepeatBasy" resultMap="BaSyjlResult">
    call repeat_br()
  </select>
  <select id="selectDeptListByBasAndTime" resultType="com.ruoyi.system.domain.BaSyjl">
    select distinct cykb from ba_syjl
    <where>
      and cykb is not null
      <if test="cydateStart != null and cydateStart != ''">and rydate &gt;= #{cydateStart}</if>
      <if test="cydateEnd != null and cydateEnd != ''">and rydate &lt;= #{cydateEnd}</if>
    </where>
  </select>

</mapper>
