<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaBrzdxxMapper">

  <resultMap type="BaBrzdxx" id="BaBrzdxxResult">
    <result property="id"    column="id"    />
    <result property="brbs"    column="brbs"    />
    <result property="brid"    column="brid"    />
    <result property="zyid"    column="zyid"    />
    <result property="zdlx"    column="zdlx"    />
    <result property="zdcx"    column="zdcx"    />
    <result property="jbbm"    column="jbbm"    />
    <result property="zdmc"    column="zdmc"    />
    <result property="rybq"    column="rybq"    />
    <result property="cyqk"    column="cyqk"    />
    <result property="fm"    column="fm"    />
    <result property="bz"    column="bz"    />
    <result property="type"    column="type"    />
    <result property="je"    column="je"    />
    <result property="flag"    column="flag"    />
    <result property="zdstr"    column="zdstr"    />
  </resultMap>

  <resultMap type="BaBrzdxx" id="Result">
    <result property="jbbm"    column="icdbh"    />
    <result property="zdmc"    column="icdname"    />
  </resultMap>

  <resultMap type="BaBrzdxx" id="icd10">
    <result property="jbbm"    column="bzbm"    />
    <result property="zdmc"    column="bzmc"    />
  </resultMap>

  <resultMap type="BaBrzdxx" id="icd09">
    <result property="jbbm"    column="bm"    />
    <result property="zdmc"    column="mc"    />
  </resultMap>

  <resultMap type="BaBrzdxx" id="ybzdxx">
    <result property="jbbm"    column="ybbzbm"    />
    <result property="zdmc"    column="ybbzmc"    />
  </resultMap>

  <resultMap type="BrzdFyxx" id="brzdFyxx">
    <result property="jbbm"    column="jbbm"    />
    <result property="je"    column="je"    />
    <result property="flag"    column="flag"    />
    <result property="zdmc"    column="zdmc"    />
    <result property="sno"    column="sno"    />
    <result property="bzbm"    column="bzbm"    />
    <result property="bzmc"    column="bzmc"    />
    <result property="bzmc2"    column="bzmc2"    />
    <result property="bzmc3"    column="bzmc3"    />
    <result property="xmmc"    column="xmmc"    />
    <result property="xmbm"    column="xmbm"    />
  </resultMap>

  <resultMap type="BrzdFyxx" id="brwkzd">
    <result property="jbbm"    column="jbbm"    />
    <result property="zdmc"    column="zdmc"    />
    <result property="ssbm"    column="ssbm"    />
    <result property="ssmc"    column="ssmc"    />
    <result property="je"    column="je"    />
    <result property="xmmc"    column="xmmc"    />
    <result property="mrflag"    column="mrflag"    />
    <result property="drgflag"    column="drgflag"    />
    <result property="flag"    column="flag"    />
    <result property="sstype"    column="sstype"    />
  </resultMap>

  <resultMap type="IcdStopUse" id="IcdStopUseResult">
    <result property="bm"    column="bm"    />
    <result property="mc"    column="mc"    />
    <result property="type"    column="type"    />
  </resultMap>

  <sql id="selectBaBrzdxxVo">
    select id, brbs, brid, zyid, zdlx, zdcx, jbbm, zdmc, rybq, cyqk, fm, bz, jlr from ba_brzdxx
  </sql>

  <select id="selectStopUseIcd10" resultMap="IcdStopUseResult">
    select * from icd_stop_use where type = '1'
  </select>

  <select id="selectIcd10CodeHm" resultType="Integer">
    select sum(hm) as hm from icd10ybdy where bzbm = #{jbbm}
    <if test="zdmc != null  and zdmc != ''">and bzmc = #{zdmc}</if>
  </select>

  <select id="selectClinicalDiagNameByCode" parameterType="String" resultType="String">
    select bzmc from icd10ybdy where bzbm = #{jbbm} limit 1
  </select>


  <select id="selectIcd10NamesByIcd10Codes" resultType="String" parameterType="String">
    select DISTINCT ybbzmc as zdmc from icd10ybdy where ybbzmc is not null and ybbzbm in
    <foreach item="jbbm" collection="array" open="(" separator="," close=")">
      #{jbbm}
    </foreach>
  </select>

  <select id="selectBaBrzdxxList" parameterType="BaBrzdxx" resultMap="BaBrzdxxResult">
    <include refid="selectBaBrzdxxVo"/>
    <where>
      <if test="id != null  and id != ''"> and id = #{id}</if>
      <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
      <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
      <if test="zdlx != null  and zdlx != ''"> and zdlx = #{zdlx}</if>
      <if test="zdcx != null "> and zdcx = #{zdcx}</if>
      <if test="jbbm != null  and jbbm != ''"> and jbbm = #{jbbm}</if>
      <if test="zdmc != null  and zdmc != ''"> and zdmc = #{zdmc}</if>
      <if test="rybq != null  and rybq != ''"> and rybq = #{rybq}</if>
      <if test="cyqk != null  and cyqk != ''"> and cyqk = #{cyqk}</if>
      <if test="fm != null  and fm != ''"> and fm = #{fm}</if>
      <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
    </where>
    order by zdcx asc
  </select>


  <select id="selectBaBrzdxxSyList" parameterType="BaBrzdxx" resultMap="BaBrzdxxResult">
    select id, brbs, brid, zyid, zdlx, zdcx, jbbm, zdmc, rybq, cyqk, fm, bz from ba_brzdxx_sy
    <where>
      <if test="id != null  and id != ''"> and id = #{id}</if>
      <if test="brbs != null  and brbs != ''"> and brbs = #{brbs}</if>
      <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
      <if test="zdlx != null  and zdlx != ''"> and zdlx = #{zdlx}</if>
      <if test="zdcx != null "> and zdcx = #{zdcx}</if>
      <if test="jbbm != null  and jbbm != ''"> and jbbm = #{jbbm}</if>
      <if test="zdmc != null  and zdmc != ''"> and zdmc = #{zdmc}</if>
      <if test="rybq != null  and rybq != ''"> and rybq = #{rybq}</if>
      <if test="cyqk != null  and cyqk != ''"> and cyqk = #{cyqk}</if>
      <if test="fm != null  and fm != ''"> and fm = #{fm}</if>
      <if test="bz != null  and bz != ''"> and bz = #{bz}</if>
    </where>
    order by zdcx asc
  </select>

  <select id="selectMaxYbSsID" resultType="Integer">
    select MAX(id) from ba_ssjl;
  </select>
  <select id="selectMaxYbZdID" resultType="Integer">
    select MAX(id) from ba_brzdxx;
  </select>
  <select id="selectMaxSySsID" resultType="Integer">
    select MAX(id) from ba_ssjl_sy;
  </select>
  <select id="selectMaxSyZdID" resultType="Integer">
    select MAX(id) from ba_brzdxx_sy;
  </select>

  <select id="seelctCzd" parameterType="BaSyjl" resultType="String">
    select fn_get_zlbrzd(#{brid},#{zyid});
  </select>

  <select id="selectBrzdfy" parameterType="Brxx" resultMap="brzdFyxx">
    CALL usp_brzdfy( null,#{brid},#{zyid},'4')
  </select>

  <select id="selectZdFyxx" parameterType="BaSyjl" resultMap="BaBrzdxxResult">
    CALL usp_get_brzdfy(#{brid},#{zyid})
  </select>

  <!--    取影响drg分组的诊断的数量 -->
  <select id="selectZdByYxDrgFz" resultType="Integer" parameterType="BaBrzdxx">
    select count(*)  from drg_adrg_fz where icdbh = #{jbbm};
  </select>


  <select id="selectIcdByFyxx" parameterType="Brxx" resultMap="brzdFyxx">
    SELECT  icdbh as jbbm,sum(je) AS je,space(20) as flag,space(120) as zdmc FROM drg_icddyxm a ,
                                                                                  (SELECT xmbm,SUM(je) AS je FROM fyxx WHERE brid=#{brid} and zyid=#{zyid} GROUP BY xmbm) b
    WHERE a.xmbm = b.xmbm  AND a.icdbh IN (select jbbm from ba_brzdxx where brid=#{brid} and zyid=#{zyid})
    GROUP BY icdbh
    ORDER BY je DESC
  </select>

  <select id="selectZyzd" parameterType="BaSyjl" resultMap="BaBrzdxxResult">
    select * from ba_brzdxx
    <where>
      <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
    </where>
    and zdcx = 1
  </select>

  <!--    外科主诊断-->
  <select id="selectWkZyzd" parameterType="BaSyjl" resultMap="brwkzd">
    SELECT DISTINCT d.bzbm as jbbm,d.bzmc as zdmc,d.ssbm as ssbm,d.ssmc as ssmc,je,xmmc,mrflag,drgflag,space(120) as flag,sstype FROM (
       SELECT  ssbm,ssmc,a.xmmc,MAX(jzh) AS jzh,MAX(je) AS je,b.mrflag,b.drgflag,CASE WHEN b.sstype='手术' THEN 0 ELSE 1 END AS sstype FROM (
       SELECT  xmbm,MAX(jzh) AS jzh,MAX(xmmc) AS xmmc,SUM(je) AS je FROM fyxx WHERE  brid = #{brid} AND zyid = #{zyid} GROUP BY xmbm ORDER BY je) a,drg_gdssdyxm b WHERE
       a.xmbm = b.xmbm  AND drgflag=1 AND je>20
       GROUP BY ssbm,ssmc ,b.xmmc ) a,drg_icddyss d WHERE a.ssbm=d.ssbm
    AND bzbm IN (select jbbm from ba_brzdxx where brid=#{brid} and zyid=#{zyid})
    ORDER BY sstype,je DESC
  </select>


  <select id="selectLcZdByJbbm" resultMap="BaBrzdxxResult">
    select bzmc as zdmc from icd10ybdy where bzbm = #{jbbm}
  </select>

  <select id="selectZdxx" parameterType="BaBrzdxx" resultMap="Result">
    select icdbh,icdname from v_yb_zdml
    <where>
      <if test="zdmc != null and zdmc !=''">
        and nccd like concat('%',#{zdmc},'%')
        or icdbh like concat('%',#{zdmc},'%')
        or icdname like concat('%',#{zdmc},'%')
      </if>
    </where>
    limit 50
  </select>

  <!-- 预分组页面下拉框中供选择的诊断信息 -->
  <select id="selectZdxxByYb" parameterType="String" resultMap="BaBrzdxxResult">
      select distinct
      ybbzbm as jbbm,
      ybbzmc as zdmc,
      concat(ybbzmc,'[权重:',ifnull(cmi,''),'][',ybbzbm,']',ifnull(ccflag,'')) as zdstr
      from
      icd10ybdy
      <where>
          <if test="condition != null and condition !=''">
              and ybbzbm like concat('%',#{condition},'%')
              or ybbzmc like concat('%',#{condition},'%')
              or ybbzmc_nccd like concat('%',#{condition},'%')
          </if>
      </where>
      limit 50
  </select>

  <select id="selectZdxxByLc" parameterType="String" resultMap="BaBrzdxxResult">
    SELECT distinct
    bzbm AS jbbm,
    bzmc AS zdmc,
    concat(bzmc,'[权重:',ifnull( cmi, '' ),'][',bzbm,']',ifnull( ccflag, '' )) AS zdstr
    FROM
    icd10ybdy
    <where>
      <if test="condition != null and condition !=''">
        and bzbm like concat('%',#{condition},'%')
        or bzmc like concat('%',#{condition},'%')
        or bzmc_nccd like concat('%',#{condition},'%')
      </if>
    </where>
    limit 50
  </select>
  <!--        -->

  <select id="selectSsxx" parameterType="BaBrzdxx" resultMap="Result">
    select icdbh,icdname from v_yb_ssml
    <where>
      <if test="zdmc != null and zdmc !=''">
        and nccd like concat('%',#{zdmc},'%')
        or icdbh like concat('%',#{zdmc},'%')
        or icdname like concat('%',#{zdmc},'%')
      </if>
    </where>
    limit 50
  </select>



  <select id="selectIcd10List"  resultMap="icd10">
    SELECT distinct bzbm,bzmc from icd10ybdy
  </select>

  <select id="selectIcd10" parameterType="BaBrzdxx" resultMap="icd10">
    SELECT distinct bzbm,bzmc from  icd10ybdy
    where
      ybbzbm = #{jbbm}
      and
      ybbzmc = #{zdmc}
  </select>


  <select id="selectIcd10Yb" parameterType="BaBrzdxx" resultMap="icd10">
    SELECT distinct ybbzbm as bzbm,ybbzmc as bzmc from  icd10ybdy
    where
      bzbm = #{jbbm}
  </select>

  <select id="selectYbzdByJbbm" parameterType="BaBrzdxx" resultMap="ybzdxx">
    SELECT distinct ybbzmc from icd10ybdy where ybbzbm = #{jbbm}
  </select>

   <select id="selectYbzdByJbbmcc" parameterType="BaBrzdxx" resultMap="ybzdxx">
   SELECT max(concat(ybbzmc,ifnull(cmi,''),ifnull(ccflag,''))) as ybbzmc  FROM icd10ybdy WHERE ybbzbm = #{jbbm} ;
  </select>

   <select id="selectzdByJbbmcc" parameterType="BaBrzdxx" resultMap="ybzdxx">
   SELECT max(concat(bzmc,ifnull(cmi,''),ifnull(ccflag,''))) as ybbzmc  FROM icd10ybdy WHERE  bzbm=#{jbbm};
  </select>

  <select id="selectYbzdByybzdbb" parameterType="BaBrzdxx" resultMap="ybzdxx">
  	SELECT max(concat(ybmc,ifnull(cmi,''))) as ybbzmc FROM icd9ybdy WHERE ybbm =#{jbbm} ;
  </select>

   <select id="selectzdByybzdbb" parameterType="BaBrzdxx" resultMap="ybzdxx">
  	SELECT max(concat(mc,ifnull(cmi,''))) as ybbzmc FROM icd9ybdy WHERE   bm =#{jbbm};
  </select>

  <select id="selectzdbmtoyb" parameterType="BaBrzdxx" resultMap="ybzdxx">
  	SELECT max(ifnull(ybbzbm,'')) as ybbzbm FROM icd10ybdy WHERE bzbm =#{jbbm};
  </select>



  <select id="selectYbzdByLczd" parameterType="BaBrzdxx" resultMap="ybzdxx">
    SELECT distinct ybbzbm,ybbzmc from icd10ybdy
    where
      bzbm = #{jbbm}
      and
      bzmc = #{zdmc}
  </select>


  <select id="selectIcd09" parameterType="BaBrzdxx" resultMap="icd09">
    SELECT distinct bm,mc from  icd9ybdy
    where
      ybbm = #{jbbm}
      and
      ybmc = #{zdmc}
  </select>


  <select id="selectIcd09Yb" parameterType="BaBrzdxx" resultMap="icd09">
    SELECT distinct ybbm as bm, ybmc as mc from  icd9ybdy
    where
      bm = #{jbbm}
      and
      mc = #{zdmc}
  </select>

  <select id="selectYbZdxx" parameterType="BaBrzdxx" resultMap="BaBrzdxxResult">
    select DISTINCT id,brbs,brid,zyid,zdcx,jbbm,zdmc,fn_getbztype_yb(a.jbbm) as type,rybq,jlr from ba_brzdxx a 
    <where>
      <if test="brid != null and brid !=''">
        and brid = #{brid}
      </if>
      <if test="zyid != null and zyid !=''">
        and zyid = #{zyid}
      </if>
      <if test="brbs != null and brbs !=''">
        and brbs = #{brbs}
      </if>
    </where>
    order by zdcx
  </select>

  <select id="selectBzType" parameterType="BaBrzdxx" resultMap="BaBrzdxxResult">
    select b.type as type from (SELECT ybbzbm from icd10ybdy where bzbm = #{jbbm}) a join drg_bfz b on a.ybbzbm = b.bzbm;
  </select>


  <select id="selectSyZdxx" parameterType="BaBrzdxx" resultMap="BaBrzdxxResult">
    select DISTINCT id,brbs,brid,zyid,zdcx,jbbm,zdmc,rybq,fn_getbztype(jbbm) as type,jlr from ba_brzdxx_sy a
    <where>
      <if test="brbs != null and brbs !=''">
        and brbs = #{brbs}
      </if>
      <if test="brid != null and brid !=''">
        and brid = #{brid}
      </if>
      <if test="zyid != null and zyid !=''">
        and zyid = #{zyid}
      </if>
    </where>
    order by zdcx
  </select>

  <select id="selectBaBrzdxxById" parameterType="Long" resultMap="BaBrzdxxResult">
    <include refid="selectBaBrzdxxVo"/>
    where id = #{id}
  </select>

  <insert id="insertBaBrzdxxBySyzd" parameterType="BaBrzdxx">
    INSERT INTO ba_brzdxx(brbs,brid,zyid,zdlx,jbbm,zdmc,zdcx)
    SELECT  brbs,brid,zyid,zdlx,IFNULL(b.ybbzbm,a.jbbm),IFNULL(b.ybbzmc,a.zdmc),zdcx
    FROM ba_brzdxx_sy a
           LEFT JOIN icd10ybdy b ON a.jbbm = b.bzbm
    WHERE a.brid = #{brid}
      and a.zyid = #{zyid}
    ORDER BY  a.zdcx;
  </insert>

  <insert id="insertBaBrzdxx" parameterType="BaBrzdxx" useGeneratedKeys="true" keyProperty="id">
    insert into ba_brzdxx
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brbs != null">brbs,</if>
      <if test="brid != null">brid,</if>
      <if test="zyid != null">zyid,</if>
      <if test="zdlx != null">zdlx,</if>
      <if test="zdcx != null">zdcx,</if>
      <if test="jbbm != null">jbbm,</if>
      <if test="zdmc != null">zdmc,</if>
      <if test="rybq != null">rybq,</if>
      <if test="cyqk != null">cyqk,</if>
      <if test="fm != null">fm,</if>
      <if test="bz != null">bz,</if>
      <if test="jlr != null">jlr,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brbs != null">#{brbs},</if>
      <if test="brid != null">#{brid},</if>
      <if test="zyid != null">#{zyid},</if>
      <if test="zdlx != null">#{zdlx},</if>
      <if test="zdcx != null">#{zdcx},</if>
      <if test="jbbm != null">#{jbbm},</if>
      <if test="zdmc != null">#{zdmc},</if>
      <if test="rybq != null">#{rybq},</if>
      <if test="cyqk != null">#{cyqk},</if>
      <if test="fm != null">#{fm},</if>
      <if test="bz != null">#{bz},</if>
      <if test="jlr != null">#{jlr},</if>
    </trim>
  </insert>

  <update id="updateBaBrzdxx" parameterType="BaBrzdxx">
    update ba_brzdxx
    <trim prefix="SET" suffixOverrides=",">
      <if test="brbs != null">brbs = #{brbs},</if>
      <if test="brid != null">brid = #{brid},</if>
      <if test="zyid != null">zyid = #{zyid},</if>
      <if test="zdlx != null">zdlx = #{zdlx},</if>
      <if test="zdcx != null">zdcx = #{zdcx},</if>
      <if test="jbbm != null">jbbm = #{jbbm},</if>
      <if test="zdmc != null">zdmc = #{zdmc},</if>
      <if test="rybq != null">rybq = #{rybq},</if>
      <if test="cyqk != null">cyqk = #{cyqk},</if>
      <if test="fm != null">fm = #{fm},</if>
      <if test="bz != null">bz = #{bz},</if>
    </trim>
    where id = #{id}
  </update>
  <update id="updateJlr">
    update ba_brzdxx a join icd10ybdy b on a.jbbm = b.ybbzbm
    set jlr = #{jlr}
    where brid = #{brid}
    and zyid = #{zyid}
    and b.bzbm = #{jbbm}
    and zdcx = #{zdcx}
  </update>

  <delete id="deleteBaBrzdxxById" parameterType="BaBrzdxx">
    delete from ba_brzdxx
    <where>
      <if test="brid != null  and brid != ''"> and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''"> and zyid = #{zyid}</if>
    </where>
  </delete>




  <delete id="deleteBaBrzdxxByIds" parameterType="String">
    delete from ba_brzdxx where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
