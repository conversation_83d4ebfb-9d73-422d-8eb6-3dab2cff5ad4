<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaBrzdxxSyMapper">

  <resultMap type="BaBrzdxxSy" id="BaBrzdxxSyResult">
    <result property="id" column="id"/>
    <result property="brbs" column="brbs"/>
    <result property="brid" column="brid"/>
    <result property="zyid" column="zyid"/>
    <result property="zdlx" column="zdlx"/>
    <result property="zdcx" column="zdcx"/>
    <result property="jbbm" column="jbbm"/>
    <result property="zdmc" column="zdmc"/>
    <result property="rybq" column="rybq"/>
    <result property="cyqk" column="cyqk"/>
    <result property="fm" column="fm"/>
    <result property="bz" column="bz"/>
    <result property="type" column="type"/>
  </resultMap>

  <sql id="selectBaBrzdxxSyVo">
    select id,
           brbs,
           brid,
           zyid,
           zdlx,
           zdcx,
           jbbm,
           zdmc,
           rybq,
           cyqk,
           fm,
           bz
    from ba_brzdxx_sy
  </sql>

  <select id="selectBaBrzdxxSyList" parameterType="BaBrzdxxSy" resultMap="BaBrzdxxSyResult">
    <include refid="selectBaBrzdxxSyVo"/>
    <where>
      <if test="brbs != null  and brbs != ''">and brbs = #{brbs}</if>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
      <if test="zdlx != null  and zdlx != ''">and zdlx = #{zdlx}</if>
      <if test="zdcx != null ">and zdcx = #{zdcx}</if>
      <if test="jbbm != null  and jbbm != ''">and jbbm = #{jbbm}</if>
      <if test="zdmc != null  and zdmc != ''">and zdmc = #{zdmc}</if>
      <if test="rybq != null  and rybq != ''">and rybq = #{rybq}</if>
      <if test="cyqk != null  and cyqk != ''">and cyqk = #{cyqk}</if>
      <if test="fm != null  and fm != ''">and fm = #{fm}</if>
      <if test="bz != null  and bz != ''">and bz = #{bz}</if>
    </where>
    order by zdcx asc
  </select>

  <select id="selectMainDiagByBridAndZyid" resultType="BaBrzdxxSy" parameterType="BaBrzdxxSy">
    select *
    from ba_brzdxx_sy
    where brid = #{brid}
      and zyid = #{zyid}
      and zdcx = 1
  </select>

  <select id="selectSyZdxx" parameterType="BaBrzdxxSy" resultMap="BaBrzdxxSyResult">
    select DISTINCT id,brbs,brid,zyid,zdcx,jbbm,zdmc,fn_getbztype(jbbm) as type,rybq from ba_brzdxx_sy a left join
    drg_bfz b on a.jbbm = b.bzbm
    <where>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
    </where>
    order by zdcx
  </select>

  <select id="selectBaBrzdxxSyById" parameterType="Long" resultMap="BaBrzdxxSyResult">
    <include refid="selectBaBrzdxxSyVo"/>
    where id = #{id}
  </select>

  <select id="insertBaBrzdxxSyBySyZd" parameterType="BaBrzdxxSy">
    INSERT INTO ba_brzdxx_sy(brbs, brid, zyid, zdlx, jbbm, zdmc, zdcx)
    SELECT concat(brid, '_', zyid), brid, zyid, '', zdcode, zdname, zdsort
    FROM zdxx a
    WHERE a.brid = #{brid}
      and a.zyid = #{zyid}
    ORDER BY a.zdsort
  </select>

  <insert id="insertBaBrzdxxSy" parameterType="BaBrzdxx" useGeneratedKeys="true" keyProperty="id">
    insert into ba_brzdxx_sy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brbs != null">brbs,</if>
      <if test="brid != null">brid,</if>
      <if test="zyid != null">zyid,</if>
      <if test="zdlx != null">zdlx,</if>
      <if test="zdcx != null">zdcx,</if>
      <if test="jbbm != null">jbbm,</if>
      <if test="zdmc != null">zdmc,</if>
      <if test="rybq != null">rybq,</if>
      <if test="cyqk != null">cyqk,</if>
      <if test="fm != null">fm,</if>
      <if test="bz != null">bz,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brbs != null">#{brbs},</if>
      <if test="brid != null">#{brid},</if>
      <if test="zyid != null">#{zyid},</if>
      <if test="zdlx != null">#{zdlx},</if>
      <if test="zdcx != null">#{zdcx},</if>
      <if test="jbbm != null">#{jbbm},</if>
      <if test="zdmc != null">#{zdmc},</if>
      <if test="rybq != null">#{rybq},</if>
      <if test="cyqk != null">#{cyqk},</if>
      <if test="fm != null">#{fm},</if>
      <if test="bz != null">#{bz},</if>
    </trim>
  </insert>

  <update id="updateBaBrzdxxSy" parameterType="BaBrzdxxSy">
    update ba_brzdxx_sy
    <trim prefix="SET" suffixOverrides=",">
      <if test="brbs != null">brbs = #{brbs},</if>
      <if test="brid != null">brid = #{brid},</if>
      <if test="zyid != null">zyid = #{zyid},</if>
      <if test="zdlx != null">zdlx = #{zdlx},</if>
      <if test="zdcx != null">zdcx = #{zdcx},</if>
      <if test="jbbm != null">jbbm = #{jbbm},</if>
      <if test="zdmc != null">zdmc = #{zdmc},</if>
      <if test="rybq != null">rybq = #{rybq},</if>
      <if test="cyqk != null">cyqk = #{cyqk},</if>
      <if test="fm != null">fm = #{fm},</if>
      <if test="bz != null">bz = #{bz},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteBaBrzdxxSyById" parameterType="BaBrzdxx">
    delete from ba_brzdxx_sy
    <where>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
    </where>
  </delete>

  <delete id="deleteBaBrzdxxSyByIds" parameterType="String">
    delete from ba_brzdxx_sy where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
