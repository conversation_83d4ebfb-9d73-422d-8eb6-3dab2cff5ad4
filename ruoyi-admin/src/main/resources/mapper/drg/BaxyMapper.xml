<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BBasyCheckBzMapper">

  <resultMap type="BBasyCheckBz" id="BBasyCheckBzResult">
    <result property="id" column="id"/>
    <result property="columnName" column="column_name"/>
    <result property="comments" column="comments"/>
    <result property="ismust" column="ismust"/>
    <result property="ismustdesc" column="ismustdesc"/>
    <result property="note" column="note"/>
    <result property="errtype" column="errtype"/>
    <result property="tip" column="tip"/>
    <result property="score" column="score"/>
    <result property="hbbzbm" column="hbbzbm"/>
    <result property="islink" column="islink"/>
    <result property="linkData" column="link_data"/>
    <result property="rybq" column="rybq"/>
    <result property="rybqobject" column="rybqobject"/>
    <result property="rybqpdbz" column="rybqpdbz"/>
  </resultMap>

  <resultMap type="DeductScoreItemVo" id="DeductScoreItems">
    <result property="id" column="id"/>
    <result property="name" column="comments"/>
    <result property="errType" column="errtype"/>
    <result property="errMessage" column="tip"/>
    <result property="score" column="score"/>
  </resultMap>

  <sql id="selectBBasyCheckBzVo">
    select id,
           column_name,
           comments,
           ismust,
           ismustdesc,
           note,
           errtype,
           tip,
           score,
           hbbzbm,
           islink,
           link_data,
           rybq,
           rybqobject,
           rybqpdbz,
           dept
    from b_basy_check_bz
  </sql>

  <select id="selectBBasyCheckBzList" parameterType="BBasyCheckBz" resultMap="BBasyCheckBzResult">
    <include refid="selectBBasyCheckBzVo"/>
    <where>
      <if test="columnName != null  and columnName != ''">and column_name like concat('%', #{columnName}, '%')</if>
      <if test="comments != null  and comments != ''">and comments like concat('%', #{comments}, '%')</if>
      <if test="ismust != null  and ismust != ''">and ismust = #{ismust}</if>
      <if test="ismustdesc != null  and ismustdesc != ''">and ismustdesc = #{ismustdesc}</if>
      <if test="note != null  and note != ''">and note = #{note}</if>
      <if test="errtype != null  and errtype != ''">and errtype = #{errtype}</if>
      <if test="tip != null  and tip != ''">and tip like concat('%', #{tip}, '%')</if>
      <if test="score != null  and score != ''">and score = #{score}</if>
      <if test="hbbzbm != null  and hbbzbm != ''">and hbbzbm = #{hbbzbm}</if>
      <if test="islink != null  and islink != ''">and islink = #{islink}</if>
    </where>
  </select>

  <select id="selectBBasyCheckBzById" parameterType="Long" resultMap="BBasyCheckBzResult">
    <include refid="selectBBasyCheckBzVo"/>
    where id = #{id}
  </select>

  <select id="selectDeductScoreItem" resultMap="DeductScoreItems">
    select * from b_basy_check_bz where score > 0
  </select>

  <select id="selectBBasyCheckBzErrortype" resultMap="BBasyCheckBzResult">
    select distinct errtype
    from b_basy_check_bz where errtype is not null
  </select>

  <insert id="insertBBasyCheckBz" parameterType="BBasyCheckBz" useGeneratedKeys="true" keyProperty="id">
    insert into b_basy_check_bz
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="columnName != null">column_name,</if>
      <if test="comments != null and comments != ''">comments,</if>
      <if test="ismust != null">ismust,</if>
      <if test="ismustdesc != null">ismustdesc,</if>
      <if test="note != null">note,</if>
      <if test="errtype != null">errtype,</if>
      <if test="tip != null">tip,</if>
      <if test="hbbzbm != null">hbbzbm,</if>
      <if test="islink != null">islink,</if>
      <if test="score != null">score,</if>
      <if test="linkData != null">link_data,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="columnName != null">#{columnName},</if>
      <if test="comments != null and comments != ''">#{comments},</if>
      <if test="ismust != null">#{ismust},</if>
      <if test="ismustdesc != null">#{ismustdesc},</if>
      <if test="note != null">#{note},</if>
      <if test="errtype != null">#{errtype},</if>
      <if test="tip != null">#{tip},</if>
      <if test="hbbzbm != null">#{hbbzbm},</if>
      <if test="islink != null">#{islink},</if>
      <if test="score != null">#{score},</if>
      <if test="linkData != null">#{linkData},</if>
    </trim>
  </insert>

  <update id="updateBBasyCheckBz" parameterType="BBasyCheckBz">
    update b_basy_check_bz
    <trim prefix="SET" suffixOverrides=",">
      <if test="columnName != null">column_name = #{columnName},</if>
      <if test="comments != null and comments != ''">comments = #{comments},</if>
      <if test="comments == null">comments = null,</if>
      <if test="ismust != null">ismust = #{ismust},</if>
      <if test="ismustdesc != null">ismustdesc = #{ismustdesc},</if>
      <if test="note != null">note = #{note},</if>
      <if test="errtype != null">errtype = #{errtype},</if>
      <if test="tip != null">tip = #{tip},</if>
      <if test="tip == null">tip = null,</if>
      <if test="hbbzbm != null">hbbzbm = #{hbbzbm},</if>
      <if test="score == null">score = null,</if>
      <if test="score != null">score = #{score},</if>
      <if test="islink != null">islink = #{islink},</if>
      <if test="linkData != null">link_data = #{linkData},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteBBasyCheckBzById" parameterType="Long">
    delete
    from b_basy_check_bz
    where id = #{id}
  </delete>

  <delete id="deleteBBasyCheckBzByIds" parameterType="String">
    delete from b_basy_check_bz where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
