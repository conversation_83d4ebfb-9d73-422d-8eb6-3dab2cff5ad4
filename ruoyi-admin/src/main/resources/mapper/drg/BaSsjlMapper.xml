<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaSsjlMapper">

  <resultMap type="BaSsjl" id="BaSsjlResult">
    <result property="id" column="id"/>
    <result property="brbs" column="brbs"/>
    <result property="brid" column="brid"/>
    <result property="zyid" column="zyid"/>
    <result property="sscx" column="sscx"/>
    <result property="ssbm" column="ssbm"/>
    <result property="ssmc" column="ssmc"/>
    <result property="ssrq" column="ssrq"/>
    <result property="ssjb" column="ssjb"/>
    <result property="sskssj" column="sskssj"/>
    <result property="ssjssj" column="ssjssj"/>
    <result property="sz" column="sz"/>
    <result property="dyzs" column="dyzs"/>
    <result property="dezs" column="dezs"/>
    <result property="qkyhdj" column="qkyhdj"/>
    <result property="mzfs" column="mzfs"/>
    <result property="mzys" column="mzys"/>
    <result property="ssqk" column="ssqk"/>
    <result property="jlly" column="jlly"/>
    <result property="ssstr" column="ssstr"/>
  </resultMap>

  <resultMap type="BaSsjl" id="Tjss">
    <result property="ssbm" column="ssbm"/>
    <result property="ssmc" column="ssmc"/>
    <result property="bzmc" column="bzmc"/>
    <result property="bzbm" column="bzbm"/>
    <result property="xmmc" column="xmmc"/>
    <result property="je" column="je"/>
    <result property="mrflag" column="mrflag"/>
  </resultMap>

  <resultMap type="BrssFyxx" id="brssFyxx">
    <result property="ssbm" column="ssbm"/>
    <result property="ssmc" column="ssmc"/>
    <result property="xmmc" column="xmmc"/>
    <result property="jzh" column="jzh"/>
    <result property="je" column="je"/>
    <result property="mrflag" column="mrflag"/>
  </resultMap>

  <resultMap type="BrssFyxx" id="brwkss">
    <result property="ssbm" column="ssbm"/>
    <result property="ssmc" column="ssmc"/>
    <result property="xmmc" column="xmmc"/>
    <result property="je" column="je"/>
    <result property="flag" column="flag"/>
    <result property="sstype" column="sstype"/>
  </resultMap>

  <resultMap type="BrssFyxx" id="brnkss">
    <result property="ssbm" column="ssbm"/>
    <result property="ssmc" column="ssmc"/>
    <result property="xmmc" column="xmmc"/>
    <result property="je" column="je"/>
    <result property="mrflag" column="mrflag"/>
    <result property="flag" column="flag"/>
    <result property="sstype" column="sstype"/>
    <result property="mdcbh" column="mdcbh"/>
    <result property="icdtype" column="icdtype"/>
    <result property="zfqz" column="zfqz"/>
    <result property="adrgbh" column="adrgbh"/>
  </resultMap>

  <resultMap type="IcdStopUse" id="IcdStopUseResult">
    <result property="bm"    column="bm"    />
    <result property="mc"    column="mc"    />
    <result property="type"    column="type"    />
  </resultMap>

  <select id="selectIcd9CodeHm" resultType="Integer">
    select sum(hm) as hm from icd9ybdy where bm = #{ssbm}
    <if test="ssmc != null  and ssmc != ''">and mc = #{ssmc}</if>
  </select>

  <select id="selectStopUseIcd9" resultMap="IcdStopUseResult">
    select * from icd_stop_use where type = '2'
  </select>

  <select id="selectClinicalOperNameByCode" parameterType="String" resultType="String">
    select mc from icd9ybdy where bm = #{ssbm} limit 1;
  </select>

  <update id="updateAnstInfo" parameterType="baSsjl">
    update ba_ssjl a join icd9ybdy b
    on a.ssbm = b.ybbm
    set a.mzfs = #{mzfs},
        a.mzys = #{mzys},
        a.mzkssj = #{mzkssj},
        a.mzjssj = #{mzjssj},
        a.sskssj = #{sskssj},
        a.ssjssj = #{ssjssj},
        a.sz = #{sz},
        a.ssjb = #{ssjb},
        a.ssrq = #{ssrq},
        a.zdysdm = #{zdysdm},
        a.mzysdm = #{mzysdm}
    where b.bm = #{ssbm}
    and sscx = #{sscx}
    and brid = #{brid}
    and zyid = #{zyid}
  </update>


  <select id="selectIcd09NamesByIcd09Codes" resultType="String" parameterType="String">
    select DISTINCT ybmc as ssmc from icd9ybdy where ybmc is not null and ybbm in
    <foreach item="ssbm" collection="array" open="(" separator="," close=")">
      #{ssbm}
    </foreach>
  </select>

  <select id="selectFydyssxx" parameterType="Fyxx" resultMap="BaSsjlResult">
    select ssmc, ssbm
    from drg_gdssdyxm
    where xmbm = #{xmbm}
  </select>

  <select id="selectSsByFyxx" parameterType="Brxx" resultMap="brssFyxx">
    SELECT *
    FROM (SELECT ssbm, ssmc, a.xmmc, MAX(jzh) AS jzh, max(je) AS je, b.mrflag
          FROM (SELECT xmbm, MAX(jzh) AS jzh, MAX(xmmc) AS xmmc, SUM(je) AS je
                FROM fyxx
                WHERE brid = #{brid}
                  AND zyid = #{zyid}
                GROUP BY xmbm
                ORDER BY je) a,
               drg_gdssdyxm b
          WHERE a.xmbm = b.xmbm
            AND drgflag = 1
            AND je > 20
          GROUP BY ssbm, ssmc, b.xmmc) a
    ORDER BY je desc
  </select>

  <sql id="selectBaSsjlVo">
    select jlly,
           id,
           brbs,
           brid,
           zyid,
           sscx,
           ssbm,
           ssmc,
           ssrq,
           ssjb,
           sskssj,
           ssjssj,
           sz,
           dyzs,
           dezs,
           qkyhdj,
           mzfs,
           mzys,
           ssqk,
           mzkssj,
           mzjssj,
           zdysdm,
           mzysdm
    from ba_ssjl
  </sql>

  <select id="selectBaSsjlList" parameterType="BaSsjl" resultMap="BaSsjlResult">
    <include refid="selectBaSsjlVo"/>
    <where>
      <if test="brbs != null  and brbs != ''">and brbs = #{brbs}</if>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
      <if test="sscx != null ">and sscx = #{sscx}</if>
      <if test="ssbm != null  and ssbm != ''">and ssbm = #{ssbm}</if>
      <if test="ssmc != null  and ssmc != ''">and ssmc = #{ssmc}</if>
      <if test="ssrq != null ">and ssrq = #{ssrq}</if>
      <if test="ssjb != null  and ssjb != ''">and ssjb = #{ssjb}</if>
      <if test="sskssj != null ">and sskssj = #{sskssj}</if>
      <if test="ssjssj != null ">and ssjssj = #{ssjssj}</if>
      <if test="sz != null  and sz != ''">and sz = #{sz}</if>
      <if test="dyzs != null  and dyzs != ''">and dyzs = #{dyzs}</if>
      <if test="dezs != null  and dezs != ''">and dezs = #{dezs}</if>
      <if test="qkyhdj != null  and qkyhdj != ''">and qkyhdj = #{qkyhdj}</if>
      <if test="mzfs != null  and mzfs != ''">and mzfs = #{mzfs}</if>
      <if test="mzys != null  and mzys != ''">and mzys = #{mzys}</if>
      <if test="ssqk != null  and ssqk != ''">and ssqk = #{ssqk}</if>
      <if test="jlly != null  and jlly != ''">and jlly = #{jlly}</if>
    </where>
    order by sscx asc
  </select>

  <select id="selectBaSsjlSyList" parameterType="BaSsjl" resultMap="BaSsjlResult">
    select jlly,id, brbs, brid, zyid, sscx, ssbm, ssmc, ssrq, ssjb, sskssj, ssjssj, sz, dyzs, dezs, qkyhdj, mzfs, mzys,
    ssqk from ba_ssjl_sy
    <where>
      <if test="brbs != null  and brbs != ''">and brbs = #{brbs}</if>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
      <if test="sscx != null ">and sscx = #{sscx}</if>
      <if test="ssbm != null  and ssbm != ''">and ssbm = #{ssbm}</if>
      <if test="ssmc != null  and ssmc != ''">and ssmc = #{ssmc}</if>
      <if test="ssrq != null ">and ssrq = #{ssrq}</if>
      <if test="ssjb != null  and ssjb != ''">and ssjb = #{ssjb}</if>
      <if test="sskssj != null ">and sskssj = #{sskssj}</if>
      <if test="ssjssj != null ">and ssjssj = #{ssjssj}</if>
      <if test="sz != null  and sz != ''">and sz = #{sz}</if>
      <if test="dyzs != null  and dyzs != ''">and dyzs = #{dyzs}</if>
      <if test="dezs != null  and dezs != ''">and dezs = #{dezs}</if>
      <if test="qkyhdj != null  and qkyhdj != ''">and qkyhdj = #{qkyhdj}</if>
      <if test="mzfs != null  and mzfs != ''">and mzfs = #{mzfs}</if>
      <if test="mzys != null  and mzys != ''">and mzys = #{mzys}</if>
      <if test="ssqk != null  and ssqk != ''">and ssqk = #{ssqk}</if>
      <if test="jlly != null  and jlly != ''">and jlly = #{jlly}</if>
    </where>
    order by sscx asc
  </select>

  <select id="selectTjss" statementType="CALLABLE" parameterType="BaSyjl" resultMap="Tjss">
    call usp_drg_tjss(#{brid}, #{zyid}, #{brbs})
  </select>

  <select id="selectYbSsxx" parameterType="BaSsjl" resultMap="BaSsjlResult">
    select DISTINCT id,brbs,brid,zyid,sscx,ssbm,ssmc,'0' as type,sskssj,ssjssj,mzkssj,mzjssj,sz,mzfs,mzys,ssrq,zdysdm,mzysdm,ssjb from ba_ssjl a
    <where>
      <if test="brid != null and brid !=''">
        and brid = #{brid}
      </if>
      <if test="zyid != null and zyid !=''">
        and zyid = #{zyid}
      </if>
      <if test="brbs != null and brbs !=''">
        and brbs = #{brbs}
      </if>
    </where>

    order by sscx
  </select>

  <select id="selectSySsxx" parameterType="BaSsjl" resultMap="BaSsjlResult">
    select DISTINCT id,brbs,brid,zyid,sscx,ssbm,ssmc,type,sskssj,ssjssj,mzkssj,mzjssj,sz,mzfs,mzys,ssrq,zdysdm,mzysdm,ssjb from ba_ssjl_sy a left join drg_bfz b on a.ssbm = b.bzbm
    <where>
      <if test="brbs != null and brbs !=''">
        and brbs = #{brbs}
      </if>
      <if test="brid != null and brid !=''">
        and brid = #{brid}
      </if>
      <if test="zyid != null and zyid !=''">
        and zyid = #{zyid}
      </if>
    </where>
    order by sscx
  </select>

  <!-- 预分组页面下拉框中供选择的手术信息 -->
  <select id="selectSsxxByYb" parameterType="String" resultMap="BaSsjlResult">
    SELECT distinct
    ybbm AS ssbm,
    ybmc AS ssmc,
    concat( ybmc, '[权重:', ifnull( cmi, '' ), '][', ybbm, ']' ) AS ssstr
    FROM
    icd9ybdy
    <where>
      <if test="condition != null and condition !=''">
        and ybbm like concat('%',#{condition},'%')
        or ybmc like concat('%',#{condition},'%')
        or nccd like concat('%',#{condition},'%')
      </if>
    </where>
    limit 50
  </select>


  <select id="selectSsxxByLc" parameterType="String" resultMap="BaSsjlResult">
    SELECT distinct
    bm AS ssbm,
    mc AS ssmc,
    concat( mc, '[权重:', ifnull( cmi, '' ), '][', bm, ']' ) AS ssstr
    FROM
    icd9ybdy
    <where>
      <if test="condition != null and condition !=''">
        and bm like concat('%',#{condition},'%')
        or mc like concat('%',#{condition},'%')
        or nccd like concat('%',#{condition},'%')
      </if>
    </where>
    limit 50
  </select>
  <!--        -->

  <select id="selectBaSsjlById" parameterType="Long" resultMap="BaSsjlResult">
    <include refid="selectBaSsjlVo"/>
    where id = #{id}
  </select>

  <select id="selectZyss" parameterType="BaSyjl" resultMap="BaSsjlResult">
    select * from ba_ssjl
    <where>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
    </where>
    and sscx = 1
  </select>

  <insert id="insertBaSsjlByTjzd" parameterType="BaSsjl">
    INSERT INTO ba_ssjl(brbs, brid, zyid, sscx, ssbm, ssmc, ssjb, jlly)
    SELECT concat(brid, '_', zyid),
           brid,
           zyid,
           sssort,
           IFNULL(b.ybbm, a.sscode),
           IFNULL(b.ybmc, a.ssname),
           a.ssjb,
           sslx
    FROM ba_ssjl_tjzd a
           LEFT JOIN icd9ybdy b ON a.sscode = b.bm
    WHERE a.brid = #{brid}
      and a.zyid = #{zyid}
    ORDER BY a.sssort;
  </insert>


  <!--    外科主手术-->
  <select id="selectWkZyss" parameterType="BaSyjl" resultMap="brwkss">
    SELECT DISTINCT a.ssbm, a.ssmc, a.xmmc, je, SPACE (20) AS flag, sstype
    FROM (
      SELECT ssbm, ssmc, a.xmmc, MAX (jzh) AS jzh, MAX (je) AS je, brid, zyid, CASE WHEN b.sstype='手术' THEN 0 ELSE 1 END AS sstype FROM (
      SELECT xmbm, MAX (jzh) AS jzh, MAX (xmmc) AS xmmc, SUM (je) AS je, MAX (brid) AS brid, MAX (zyid) AS zyid FROM fyxx WHERE brid = #{brid} AND zyid = #{zyid} GROUP BY xmbm ORDER BY je) a, drg_gdssdyxm b WHERE
      a.xmbm = b.xmbm AND drgflag=1 AND je>20
      GROUP BY ssbm, ssmc, b.xmmc ) a
    WHERE a.ssbm IN (select ssbm from ba_ssjl where brid=#{brid}
      and zyid=#{zyid} )
    ORDER BY sstype, je DESC
  </select>

  <insert id="insertBaSsjl" parameterType="BaSsjl" useGeneratedKeys="true" keyProperty="id">
    insert into ba_ssjl
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brbs != null">brbs,</if>
      <if test="brid != null">brid,</if>
      <if test="zyid != null">zyid,</if>
      <if test="sscx != null">sscx,</if>
      <if test="ssbm != null">ssbm,</if>
      <if test="ssmc != null">ssmc,</if>
      <if test="ssrq != null">ssrq,</if>
      <if test="ssjb != null">ssjb,</if>
      <if test="sskssj != null">sskssj,</if>
      <if test="ssjssj != null">ssjssj,</if>
      <if test="sz != null">sz,</if>
      <if test="dyzs != null">dyzs,</if>
      <if test="dezs != null">dezs,</if>
      <if test="qkyhdj != null">qkyhdj,</if>
      <if test="mzfs != null">mzfs,</if>
      <if test="mzys != null">mzys,</if>
      <if test="ssqk != null">ssqk,</if>
      <if test="jlly != null">jlly,</if>
      <if test="mzkssj != null">mzkssj,</if>
      <if test="mzjssj != null">mzjssj,</if>
      <if test="zdysdm != null">zdysdm,</if>
      <if test="mzysdm != null">mzysdm,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brbs != null">#{brbs},</if>
      <if test="brid != null">#{brid},</if>
      <if test="zyid != null">#{zyid},</if>
      <if test="sscx != null">#{sscx},</if>
      <if test="ssbm != null">#{ssbm},</if>
      <if test="ssmc != null">#{ssmc},</if>
      <if test="ssrq != null">#{ssrq},</if>
      <if test="ssjb != null">#{ssjb},</if>
      <if test="sskssj != null">#{sskssj},</if>
      <if test="ssjssj != null">#{ssjssj},</if>
      <if test="sz != null">#{sz},</if>
      <if test="dyzs != null">#{dyzs},</if>
      <if test="dezs != null">#{dezs},</if>
      <if test="qkyhdj != null">#{qkyhdj},</if>
      <if test="mzfs != null">#{mzfs},</if>
      <if test="mzys != null">#{mzys},</if>
      <if test="ssqk != null">#{ssqk},</if>
      <if test="jlly != null">#{jlly},</if>
      <if test="mzkssj != null">#{mzkssj},</if>
      <if test="mzjssj != null">#{mzjssj},</if>
      <if test="zdysdm != null">#{zdysdm},</if>
      <if test="mzysdm != null">#{mzysdm},</if>
    </trim>
  </insert>

  <insert id="insertBaSsjlxxBySyss" parameterType="BaSsjl">
    INSERT INTO ba_ssjl(brbs, brid, zyid, jlly, ssbm, ssmc, sscx, sz, dyzs, dezs, ssjb, qkyhdj, mzys, mzfs)
    SELECT brbs,
           brid,
           zyid,
           jlly,
           IFNULL(b.ybbm, a.ssbm),
           IFNULL(b.ybmc, a.ssmc),
           sscx,
           sz,
           dyzs,
           dezs,
           b.ssjb,
           qkyhdj,
           mzys,
           mzfs
    FROM ba_ssjl_sy a
           LEFT JOIN icd9ybdy b ON a.ssbm = b.bm
    WHERE a.brid = #{brid}
      and a.zyid = #{zyid}
    ORDER BY a.sscx;

  </insert>


  <update id="updateBaSsjl" parameterType="BaSsjl">
    update ba_ssjl
    <trim prefix="SET" suffixOverrides=",">
      <if test="brbs != null">brbs = #{brbs},</if>
      <if test="brid != null">brid = #{brid},</if>
      <if test="zyid != null">zyid = #{zyid},</if>
      <if test="sscx != null">sscx = #{sscx},</if>
      <if test="ssbm != null">ssbm = #{ssbm},</if>
      <if test="ssmc != null">ssmc = #{ssmc},</if>
      <if test="ssrq != null">ssrq = #{ssrq},</if>
      <if test="ssjb != null">ssjb = #{ssjb},</if>
      <if test="sskssj != null">sskssj = #{sskssj},</if>
      <if test="ssjssj != null">ssjssj = #{ssjssj},</if>
      <if test="sz != null">sz = #{sz},</if>
      <if test="dyzs != null">dyzs = #{dyzs},</if>
      <if test="dezs != null">dezs = #{dezs},</if>
      <if test="qkyhdj != null">qkyhdj = #{qkyhdj},</if>
      <if test="mzfs != null">mzfs = #{mzfs},</if>
      <if test="mzys != null">mzys = #{mzys},</if>
      <if test="ssqk != null">ssqk = #{ssqk},</if>
      <if test="jlly != null">jlly = #{jlly},</if>
    </trim>
    where id = #{id}
  </update>
  <update id="updateLcAnstInfo">
    update ba_ssjl_sy a
      set a.mzfs = #{mzfs},
        a.mzys = #{mzys},
        a.mzkssj = #{mzkssj},
        a.mzjssj = #{mzjssj},
        a.sskssj = #{sskssj},
        a.ssjssj = #{ssjssj},
        a.sz = #{sz},
        a.ssjb = #{ssjb},
        a.ssrq = #{ssrq},
        a.zdysdm = #{zdysdm},
        a.mzysdm = #{mzysdm}
    where ssbm = #{ssbm}
      and sscx = #{sscx}
      and brid = #{brid}
      and zyid = #{zyid}
  </update>

  <delete id="deleteBaSsjlById" parameterType="BaSsjl">
    delete from ba_ssjl
    <where>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
    </where>
  </delete>

  <delete id="deleteBaSsjlByIds" parameterType="String">
    delete from ba_ssjl where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
