<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BaSsjlSyMapper">

  <resultMap type="BaSsjlSy" id="BaSsjlSyResult">
    <result property="id" column="id"/>
    <result property="brbs" column="brbs"/>
    <result property="brid" column="brid"/>
    <result property="zyid" column="zyid"/>
    <result property="sscx" column="sscx"/>
    <result property="ssbm" column="ssbm"/>
    <result property="ssmc" column="ssmc"/>
    <result property="ssrq" column="ssrq"/>
    <result property="ssjb" column="ssjb"/>
    <result property="sskssj" column="sskssj"/>
    <result property="ssjssj" column="ssjssj"/>
    <result property="sz" column="sz"/>
    <result property="dyzs" column="dyzs"/>
    <result property="dezs" column="dezs"/>
    <result property="qkyhdj" column="qkyhdj"/>
    <result property="mzfs" column="mzfs"/>
    <result property="mzfj" column="mzfj"/>
    <result property="mzys" column="mzys"/>
    <result property="ssqk" column="ssqk"/>
    <result property="ssbw" column="ssbw"/>
    <result property="type" column="type"/>
    <result property="jlly" column="jlly"/>
  </resultMap>

  <sql id="selectBaSsjlSyVo">
    select jlly,
           id,
           brbs,
           brid,
           zyid,
           sscx,
           ssbm,
           ssmc,
           ssrq,
           ssjb,
           sskssj,
           ssjssj,
           sz,
           dyzs,
           dezs,
           qkyhdj,
           mzfs,
           mzfj,
           mzys,
           ssqk,
           ssbw
    from ba_ssjl_sy
  </sql>

  <select id="selectBaSsjlSyList" parameterType="BaSsjlSy" resultMap="BaSsjlSyResult">
    <include refid="selectBaSsjlSyVo"/>
    <where>
      <if test="brbs != null  and brbs != ''">and brbs = #{brbs}</if>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
      <if test="sscx != null ">and sscx = #{sscx}</if>
      <if test="ssbm != null  and ssbm != ''">and ssbm = #{ssbm}</if>
      <if test="ssmc != null  and ssmc != ''">and ssmc = #{ssmc}</if>
      <if test="ssrq != null ">and ssrq = #{ssrq}</if>
      <if test="ssjb != null  and ssjb != ''">and ssjb = #{ssjb}</if>
      <if test="sskssj != null ">and sskssj = #{sskssj}</if>
      <if test="ssjssj != null ">and ssjssj = #{ssjssj}</if>
      <if test="sz != null  and sz != ''">and sz = #{sz}</if>
      <if test="dyzs != null  and dyzs != ''">and dyzs = #{dyzs}</if>
      <if test="dezs != null  and dezs != ''">and dezs = #{dezs}</if>
      <if test="qkyhdj != null  and qkyhdj != ''">and qkyhdj = #{qkyhdj}</if>
      <if test="mzfs != null  and mzfs != ''">and mzfs = #{mzfs}</if>
      <if test="mzfj != null  and mzfj != ''">and mzfj = #{mzfj}</if>
      <if test="mzys != null  and mzys != ''">and mzys = #{mzys}</if>
      <if test="ssqk != null  and ssqk != ''">and ssqk = #{ssqk}</if>
      <if test="ssbw != null  and ssbw != ''">and ssbw = #{ssbw}</if>
      <if test="jlly != null  and jlly != ''">and jlly = #{jlly}</if>
    </where>
    order by sscx asc
  </select>

  <select id="selectMainOperByBridAndZyid" resultType="BaSsjlSy" parameterType="BaSsjlSy">
    select *
    from ba_ssjl_sy
    where brid = #{brid}
      and zyid = #{zyid}
      and sscx = 1
  </select>

  <select id="selectSySsxx" parameterType="BaSsjlSy" resultMap="BaSsjlSyResult">
    select DISTINCT id,brbs,brid,zyid,sscx,ssbm,ssmc,'0' as type from ba_ssjl_sy a
    <where>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>

    </where>
    order by sscx
  </select>
  <select id="selectBaSsjlSyByBrbs"  resultMap="BaSsjlSyResult">
    SELECT IFNULL(ssmc ,"") AS ssmc ,IFNULL(ssbm , "") AS ssbm,
           ssrq AS ssrq,IFNULL(ssjb,"") AS ssjb,IFNULL(sz ,"") AS sz,
           IFNULL(dyzs,"") AS dyzs,IFNULL(dezs,"") AS dezs,
           IFNULL(qkyhdj,"") AS qkyhdj,IFNULL(mzfs,"") AS mzfs,
           IFNULL(mzys,"") AS mzys
    FROM ba_ssjl_sy a
      where
    a.brbs = #{brbs}
    order by sscx
  </select>

  <select id="selectBaSsjlSyById" parameterType="Long" resultMap="BaSsjlSyResult">
    <include refid="selectBaSsjlSyVo"/>
    where id = #{id}
  </select>


  <insert id="insertBaSsjlSyByTjzd" parameterType="BaSsjlSy">
    INSERT INTO ba_ssjl_sy(brbs, brid, zyid, sscx, ssbm, ssmc, ssjb, jlly)
    SELECT concat(brid, '_', zyid),
           brid,
           zyid,
           sssort,
           sscode,
           ssname,
           ssjb,
           sslx
    FROM ba_ssjl_tjzd a
    WHERE a.brid = #{brid}
      and a.zyid = #{zyid}
    ORDER BY a.sssort
  </insert>

  <insert id="insertBaSsjlSy" parameterType="BaSsjl" useGeneratedKeys="true" keyProperty="id">
    insert into ba_ssjl_sy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brbs != null">brbs,</if>
      <if test="brid != null">brid,</if>
      <if test="zyid != null">zyid,</if>
      <if test="sscx != null">sscx,</if>
      <if test="ssbm != null">ssbm,</if>
      <if test="ssmc != null">ssmc,</if>
      <if test="ssrq != null">ssrq,</if>
      <if test="ssjb != null">ssjb,</if>
      <if test="sskssj != null">sskssj,</if>
      <if test="ssjssj != null">ssjssj,</if>
      <if test="sz != null">sz,</if>
      <if test="dyzs != null">dyzs,</if>
      <if test="dezs != null">dezs,</if>
      <if test="qkyhdj != null">qkyhdj,</if>
      <if test="mzfs != null">mzfs,</if>
      <if test="mzys != null">mzys,</if>
      <if test="ssqk != null">ssqk,</if>
      <if test="ssbw != null">ssbw,</if>
      <if test="jlly != null">jlly,</if>
      <if test="zdysdm != null">zdysdm,</if>
      <if test="mzysdm != null">mzysdm,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brbs != null">#{brbs},</if>
      <if test="brid != null">#{brid},</if>
      <if test="zyid != null">#{zyid},</if>
      <if test="sscx != null">#{sscx},</if>
      <if test="ssbm != null">#{ssbm},</if>
      <if test="ssmc != null">#{ssmc},</if>
      <if test="ssrq != null">#{ssrq},</if>
      <if test="ssjb != null">#{ssjb},</if>
      <if test="sskssj != null">#{sskssj},</if>
      <if test="ssjssj != null">#{ssjssj},</if>
      <if test="sz != null">#{sz},</if>
      <if test="dyzs != null">#{dyzs},</if>
      <if test="dezs != null">#{dezs},</if>
      <if test="qkyhdj != null">#{qkyhdj},</if>
      <if test="mzfs != null">#{mzfs},</if>
      <if test="mzys != null">#{mzys},</if>
      <if test="ssqk != null">#{ssqk},</if>
      <if test="ssbw != null">#{ssbw},</if>
      <if test="jlly != null">#{jlly},</if>
      <if test="zdysdm != null">#{zdysdm},</if>
      <if test="mzysdm != null">#{mzysdm},</if>
    </trim>
  </insert>

  <update id="updateBaSsjlSy" parameterType="BaSsjlSy">
    update ba_ssjl_sy
    <trim prefix="SET" suffixOverrides=",">
      <if test="brbs != null">brbs = #{brbs},</if>
      <if test="brid != null">brid = #{brid},</if>
      <if test="zyid != null">zyid = #{zyid},</if>
      <if test="sscx != null">sscx = #{sscx},</if>
      <if test="ssbm != null">ssbm = #{ssbm},</if>
      <if test="ssmc != null">ssmc = #{ssmc},</if>
      <if test="ssrq != null">ssrq = #{ssrq},</if>
      <if test="ssjb != null">ssjb = #{ssjb},</if>
      <if test="sskssj != null">sskssj = #{sskssj},</if>
      <if test="ssjssj != null">ssjssj = #{ssjssj},</if>
      <if test="sz != null">sz = #{sz},</if>
      <if test="dyzs != null">dyzs = #{dyzs},</if>
      <if test="dezs != null">dezs = #{dezs},</if>
      <if test="qkyhdj != null">qkyhdj = #{qkyhdj},</if>
      <if test="mzfs != null">mzfs = #{mzfs},</if>
      <if test="mzfj != null">mzfj = #{mzfj},</if>
      <if test="mzys != null">mzys = #{mzys},</if>
      <if test="ssqk != null">ssqk = #{ssqk},</if>
      <if test="ssbw != null">ssbw = #{ssbw},</if>
      <if test="jlly != null">jlly = #{jlly},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteBaSsjlSyById" parameterType="BaSsjl">
    delete from ba_ssjl_sy
    <where>
      <if test="brid != null  and brid != ''">and brid = #{brid}</if>
      <if test="zyid != null  and zyid != ''">and zyid = #{zyid}</if>
    </where>
  </delete>

  <delete id="deleteBaSsjlSyByIds" parameterType="String">
    delete from ba_ssjl_sy where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>
