<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DrgZxbxmxMapper">

    <resultMap type="DrgZxbxmx" id="DrgZxbxmxResult">
        <result property="jgid"    column="jgid"    />
        <result property="yymc"    column="yymc"    />
        <result property="setlId"    column="setl_id"    />
        <result property="psnNo"    column="psn_no"    />
        <result property="xm"    column="xm"    />
        <result property="jcfl"    column="jcfl"    />
        <result property="zfqz"    column="zfqz"    />
        <result property="drgbh"    column="drgbh"    />
        <result property="drgmc"    column="drgmc"    />
        <result property="jsdate"    column="jsdate"    />
        <result property="zfy"    column="zfy"    />
        <result property="yljgxs"    column="yljgxs"    />
        <result property="sfz"    column="sfz"    />
        <result property="age"    column="age"    />
        <result property="xb"    column="xb"    />
        <result property="medType"    column="med_type"    />
        <result property="rydate"    column="rydate"    />
        <result property="cydate"    column="cydate"    />
        <result property="deptname"    column="deptname"    />
        <result property="qbx"    column="qbx"    />
        <result property="cxzf"    column="cxzf"    />
        <result property="xxzf"    column="xxzf"    />
        <result property="gjzf"    column="gjzf"    />
        <result property="zhzf"    column="zhzf"    />
        <result property="jbdm"    column="jbdm"    />
        <result property="zdmc"    column="zdmc"    />
        <result property="ssjczbm1"    column="ssjczbm1"    />
        <result property="ssjczmc1"    column="ssjczmc1"    />
        <result property="tfflag"    column="tfflag"    />
        <result property="sjzyts"    column="sjzyts"    />
        <result property="fztype"    column="fztype"    />
        <result property="jsjglx"    column="jsjglx"    />
        <result property="zfbz"    column="zfbz"    />
        <result property="bah"    column="bah"    />
        <result property="jfl"    column="jfl"    />
        <result property="yk"    column="yk"    />
        <result property="zgtc"    column="zgtc"    />
        <result property="zgtcdrg"    column="zgtcdrg"    />
        <result property="jmtc"    column="jmtc"    />
        <result property="jmtcdrg"    column="jmtcdrg"    />
        <result property="gwybz"    column="gwybz"    />
        <result property="gwybzdrg"    column="gwybzdrg"    />
        <result property="dezf"    column="dezf"    />
        <result property="dezfdrg"    column="dezfdrg"    />
        <result property="dbzf"    column="dbzf"    />
        <result property="dbzfdrg"    column="dbzfdrg"    />
        <result property="scbz"    column="scbz"    />
        <result property="scbzdrg"    column="scbzdrg"    />
        <result property="bjj"    column="bjj"    />
        <result property="bjjdrg"    column="bjjdrg"    />
        <result property="bjyf"    column="bjyf"    />
        <result property="bjyfdrg"    column="bjyfdrg"    />
        <result property="qybc"    column="qybc"    />
        <result property="qybcdrg"    column="qybcdrg"    />
        <result property="jmywsh"    column="jmywsh"    />
        <result property="jmywshdrg"    column="jmywshdrg"    />
        <result property="yljz"    column="yljz"    />
        <result property="yljzdrg"    column="yljzdrg"    />
        <result property="thb"    column="thb"    />
        <result property="thbdrg"    column="thbdrg"    />
        <result property="zfdd"    column="zfdd"    />
        <result property="zfdddrg"    column="zfdddrg"    />
        <result property="zgdbbx"    column="zgdbbx"    />
        <result property="zgdbbxdrg"    column="zgdbbxdrg"    />
        <result property="qt"    column="qt"    />
        <result property="qtdrg"    column="qtdrg"    />
        <result property="yljzzjz"    column="yljzzjz"    />
        <result property="yljzzjzdrg"    column="yljzzjzdrg"    />
    </resultMap>

    <sql id="selectDrgZxbxmxVo">
        select jgid, setl_id, yymc, psn_no, xm, jcfl, zfqz, drgbh, drgmc, jsdate, zfy, yljgxs, sfz, age, xb, med_type, rydate, cydate, deptname, qbx, cxzf, xxzf, gjzf, zhzf, jbdm, zdmc, ssjczbm1, ssjczmc1, tfflag, sjzyts, fztype, jsjglx, zfbz, bah, jfl, yk, zgtc, zgtcdrg, jmtc, jmtcdrg, gwybz, gwybzdrg, dezf, dezfdrg, dbzf, dbzfdrg, scbz, scbzdrg, bjj, bjjdrg, bjyf, bjyfdrg, qybc, qybcdrg, jmywsh, jmywshdrg, yljz, yljzdrg, thb, thbdrg, zfdd, zfdddrg, zgdbbx, zgdbbxdrg, qt, qtdrg, yljzzjz, yljzzjzdrg from drg_zxbxmx
    </sql>

    <select id="selectDrgZxbxmxList" parameterType="DrgZxbxmx" resultMap="DrgZxbxmxResult">
        <include refid="selectDrgZxbxmxVo"/>
        <where>
            <if test="jgid != null  and jgid != ''"> and jgid = #{jgid}</if>
            <if test="setlId != null  and setlId != ''"> and setl_id = #{setlId}</if>
            <if test="bah != null  and bah != ''"> and bah = #{bah}</if>
            <if test="drgbh != null  and drgbh != ''"> and drgbh = #{drgbh}</if>
        </where>
    </select>

    <insert id="insertDrgZxbxmx" parameterType="DrgZxbxmx">
        insert into drg_zxbxmx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jgid != null">jgid,</if>
            <if test="yymc != null">yymc,</if>
            <if test="setlId != null">setl_id,</if>
            <if test="psnNo != null">psn_no,</if>
            <if test="xm != null">xm,</if>
            <if test="jcfl != null">jcfl,</if>
            <if test="zfqz != null">zfqz,</if>
            <if test="drgbh != null">drgbh,</if>
            <if test="drgmc != null">drgmc,</if>
            <if test="jsdate != null">jsdate,</if>
            <if test="zfy != null">zfy,</if>
            <if test="yljgxs != null">yljgxs,</if>
            <if test="sfz != null">sfz,</if>
            <if test="age != null">age,</if>
            <if test="xb != null">xb,</if>
            <if test="medType != null">med_type,</if>
            <if test="rydate != null">rydate,</if>
            <if test="cydate != null">cydate,</if>
            <if test="deptname != null">deptname,</if>
            <if test="qbx != null">qbx,</if>
            <if test="cxzf != null">cxzf,</if>
            <if test="xxzf != null">xxzf,</if>
            <if test="gjzf != null">gjzf,</if>
            <if test="zhzf != null">zhzf,</if>
            <if test="jbdm != null">jbdm,</if>
            <if test="zdmc != null">zdmc,</if>
            <if test="ssjczbm1 != null">ssjczbm1,</if>
            <if test="ssjczmc1 != null">ssjczmc1,</if>
            <if test="tfflag != null">tfflag,</if>
            <if test="sjzyts != null">sjzyts,</if>
            <if test="fztype != null">fztype,</if>
            <if test="jsjglx != null">jsjglx,</if>
            <if test="zfbz != null">zfbz,</if>
            <if test="bah != null">bah,</if>
            <if test="jfl != null">jfl,</if>
            <if test="yk != null">yk,</if>
            <if test="zgtc != null">zgtc,</if>
            <if test="zgtcdrg != null">zgtcdrg,</if>
            <if test="jmtc != null">jmtc,</if>
            <if test="jmtcdrg != null">jmtcdrg,</if>
            <if test="gwybz != null">gwybz,</if>
            <if test="gwybzdrg != null">gwybzdrg,</if>
            <if test="dezf != null">dezf,</if>
            <if test="dezfdrg != null">dezfdrg,</if>
            <if test="dbzf != null">dbzf,</if>
            <if test="dbzfdrg != null">dbzfdrg,</if>
            <if test="scbz != null">scbz,</if>
            <if test="scbzdrg != null">scbzdrg,</if>
            <if test="bjj != null">bjj,</if>
            <if test="bjjdrg != null">bjjdrg,</if>
            <if test="bjyf != null">bjyf,</if>
            <if test="bjyfdrg != null">bjyfdrg,</if>
            <if test="qybc != null">qybc,</if>
            <if test="qybcdrg != null">qybcdrg,</if>
            <if test="jmywsh != null">jmywsh,</if>
            <if test="jmywshdrg != null">jmywshdrg,</if>
            <if test="yljz != null">yljz,</if>
            <if test="yljzdrg != null">yljzdrg,</if>
            <if test="thb != null">thb,</if>
            <if test="thbdrg != null">thbdrg,</if>
            <if test="zfdd != null">zfdd,</if>
            <if test="zfdddrg != null">zfdddrg,</if>
            <if test="zgdbbx != null">zgdbbx,</if>
            <if test="zgdbbxdrg != null">zgdbbxdrg,</if>
            <if test="qt != null">qt,</if>
            <if test="qtdrg != null">qtdrg,</if>
            <if test="yljzzjz != null">yljzzjz,</if>
            <if test="yljzzjzdrg != null">yljzzjzdrg,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jgid != null">#{jgid},</if>
            <if test="yymc != null">#{yymc},</if>
            <if test="setlId != null">#{setlId},</if>
            <if test="psnNo != null">#{psnNo},</if>
            <if test="xm != null">#{xm},</if>
            <if test="jcfl != null">#{jcfl},</if>
            <if test="zfqz != null">#{zfqz},</if>
            <if test="drgbh != null">#{drgbh},</if>
            <if test="drgmc != null">#{drgmc},</if>
            <if test="jsdate != null">#{jsdate},</if>
            <if test="zfy != null">#{zfy},</if>
            <if test="yljgxs != null">#{yljgxs},</if>
            <if test="sfz != null">#{sfz},</if>
            <if test="age != null">#{age},</if>
            <if test="xb != null">#{xb},</if>
            <if test="medType != null">#{medType},</if>
            <if test="rydate != null">#{rydate},</if>
            <if test="cydate != null">#{cydate},</if>
            <if test="deptname != null">#{deptname},</if>
            <if test="qbx != null">#{qbx},</if>
            <if test="cxzf != null">#{cxzf},</if>
            <if test="xxzf != null">#{xxzf},</if>
            <if test="gjzf != null">#{gjzf},</if>
            <if test="zhzf != null">#{zhzf},</if>
            <if test="jbdm != null">#{jbdm},</if>
            <if test="zdmc != null">#{zdmc},</if>
            <if test="ssjczbm1 != null">#{ssjczbm1},</if>
            <if test="ssjczmc1 != null">#{ssjczmc1},</if>
            <if test="tfflag != null">#{tfflag},</if>
            <if test="sjzyts != null">#{sjzyts},</if>
            <if test="fztype != null">#{fztype},</if>
            <if test="jsjglx != null">#{jsjglx},</if>
            <if test="zfbz != null">#{zfbz},</if>
            <if test="bah != null">#{bah},</if>
            <if test="jfl != null">#{jfl},</if>
            <if test="yk != null">#{yk},</if>
            <if test="zgtc != null">#{zgtc},</if>
            <if test="zgtcdrg != null">#{zgtcdrg},</if>
            <if test="jmtc != null">#{jmtc},</if>
            <if test="jmtcdrg != null">#{jmtcdrg},</if>
            <if test="gwybz != null">#{gwybz},</if>
            <if test="gwybzdrg != null">#{gwybzdrg},</if>
            <if test="dezf != null">#{dezf},</if>
            <if test="dezfdrg != null">#{dezfdrg},</if>
            <if test="dbzf != null">#{dbzf},</if>
            <if test="dbzfdrg != null">#{dbzfdrg},</if>
            <if test="scbz != null">#{scbz},</if>
            <if test="scbzdrg != null">#{scbzdrg},</if>
            <if test="bjj != null">#{bjj},</if>
            <if test="bjjdrg != null">#{bjjdrg},</if>
            <if test="bjyf != null">#{bjyf},</if>
            <if test="bjyfdrg != null">#{bjyfdrg},</if>
            <if test="qybc != null">#{qybc},</if>
            <if test="qybcdrg != null">#{qybcdrg},</if>
            <if test="jmywsh != null">#{jmywsh},</if>
            <if test="jmywshdrg != null">#{jmywshdrg},</if>
            <if test="yljz != null">#{yljz},</if>
            <if test="yljzdrg != null">#{yljzdrg},</if>
            <if test="thb != null">#{thb},</if>
            <if test="thbdrg != null">#{thbdrg},</if>
            <if test="zfdd != null">#{zfdd},</if>
            <if test="zfdddrg != null">#{zfdddrg},</if>
            <if test="zgdbbx != null">#{zgdbbx},</if>
            <if test="zgdbbxdrg != null">#{zgdbbxdrg},</if>
            <if test="qt != null">#{qt},</if>
            <if test="qtdrg != null">#{qtdrg},</if>
            <if test="yljzzjz != null">#{yljzzjz},</if>
            <if test="yljzzjzdrg != null">#{yljzzjzdrg},</if>
         </trim>
    </insert>

    <update id="updateDrgZxbxmx" parameterType="DrgZxbxmx">
        update drg_zxbxmx
        <trim prefix="SET" suffixOverrides=",">
            <if test="yymc != null">yymc = #{yymc},</if>
            <if test="setlId != null">setl_id = #{setlId},</if>
            <if test="psnNo != null">psn_no = #{psnNo},</if>
            <if test="xm != null">xm = #{xm},</if>
            <if test="jcfl != null">jcfl = #{jcfl},</if>
            <if test="zfqz != null">zfqz = #{zfqz},</if>
            <if test="drgbh != null">drgbh = #{drgbh},</if>
            <if test="drgmc != null">drgmc = #{drgmc},</if>
            <if test="jsdate != null">jsdate = #{jsdate},</if>
            <if test="zfy != null">zfy = #{zfy},</if>
            <if test="yljgxs != null">yljgxs = #{yljgxs},</if>
            <if test="sfz != null">sfz = #{sfz},</if>
            <if test="age != null">age = #{age},</if>
            <if test="xb != null">xb = #{xb},</if>
            <if test="medType != null">med_type = #{medType},</if>
            <if test="rydate != null">rydate = #{rydate},</if>
            <if test="cydate != null">cydate = #{cydate},</if>
            <if test="deptname != null">deptname = #{deptname},</if>
            <if test="qbx != null">qbx = #{qbx},</if>
            <if test="cxzf != null">cxzf = #{cxzf},</if>
            <if test="xxzf != null">xxzf = #{xxzf},</if>
            <if test="gjzf != null">gjzf = #{gjzf},</if>
            <if test="zhzf != null">zhzf = #{zhzf},</if>
            <if test="jbdm != null">jbdm = #{jbdm},</if>
            <if test="zdmc != null">zdmc = #{zdmc},</if>
            <if test="ssjczbm1 != null">ssjczbm1 = #{ssjczbm1},</if>
            <if test="ssjczmc1 != null">ssjczmc1 = #{ssjczmc1},</if>
            <if test="tfflag != null">tfflag = #{tfflag},</if>
            <if test="sjzyts != null">sjzyts = #{sjzyts},</if>
            <if test="fztype != null">fztype = #{fztype},</if>
            <if test="jsjglx != null">jsjglx = #{jsjglx},</if>
            <if test="zfbz != null">zfbz = #{zfbz},</if>
            <if test="bah != null">bah = #{bah},</if>
            <if test="jfl != null">jfl = #{jfl},</if>
            <if test="yk != null">yk = #{yk},</if>
            <if test="zgtc != null">zgtc = #{zgtc},</if>
            <if test="zgtcdrg != null">zgtcdrg = #{zgtcdrg},</if>
            <if test="jmtc != null">jmtc = #{jmtc},</if>
            <if test="jmtcdrg != null">jmtcdrg = #{jmtcdrg},</if>
            <if test="gwybz != null">gwybz = #{gwybz},</if>
            <if test="gwybzdrg != null">gwybzdrg = #{gwybzdrg},</if>
            <if test="dezf != null">dezf = #{dezf},</if>
            <if test="dezfdrg != null">dezfdrg = #{dezfdrg},</if>
            <if test="dbzf != null">dbzf = #{dbzf},</if>
            <if test="dbzfdrg != null">dbzfdrg = #{dbzfdrg},</if>
            <if test="scbz != null">scbz = #{scbz},</if>
            <if test="scbzdrg != null">scbzdrg = #{scbzdrg},</if>
            <if test="bjj != null">bjj = #{bjj},</if>
            <if test="bjjdrg != null">bjjdrg = #{bjjdrg},</if>
            <if test="bjyf != null">bjyf = #{bjyf},</if>
            <if test="bjyfdrg != null">bjyfdrg = #{bjyfdrg},</if>
            <if test="qybc != null">qybc = #{qybc},</if>
            <if test="qybcdrg != null">qybcdrg = #{qybcdrg},</if>
            <if test="jmywsh != null">jmywsh = #{jmywsh},</if>
            <if test="jmywshdrg != null">jmywshdrg = #{jmywshdrg},</if>
            <if test="yljz != null">yljz = #{yljz},</if>
            <if test="yljzdrg != null">yljzdrg = #{yljzdrg},</if>
            <if test="thb != null">thb = #{thb},</if>
            <if test="thbdrg != null">thbdrg = #{thbdrg},</if>
            <if test="zfdd != null">zfdd = #{zfdd},</if>
            <if test="zfdddrg != null">zfdddrg = #{zfdddrg},</if>
            <if test="zgdbbx != null">zgdbbx = #{zgdbbx},</if>
            <if test="zgdbbxdrg != null">zgdbbxdrg = #{zgdbbxdrg},</if>
            <if test="qt != null">qt = #{qt},</if>
            <if test="qtdrg != null">qtdrg = #{qtdrg},</if>
            <if test="yljzzjz != null">yljzzjz = #{yljzzjz},</if>
            <if test="yljzzjzdrg != null">yljzzjzdrg = #{yljzzjzdrg},</if>
        </trim>
        where jgid = #{jgid} and setl_id = #{setlId}
    </update>

</mapper>
