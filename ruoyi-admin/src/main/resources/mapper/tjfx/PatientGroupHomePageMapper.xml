<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.PatientGroupHomePageMapper">

  <resultMap type="PatientGroupHomePage" id="PatientGroupHomePageResult">
    <result property="drgbh"    column="drgbh"    />
    <result property="drgmc"    column="drgmc"    />
    <result property="zfy"    column="zfy"    />
    <result property="cybls"    column="cybls"    />
    <result property="cjfy"    column="cjfy"    />
    <result property="zqz"    column="zqz"    />
    <result property="rcrqb"    column="rcrqb"    />
    <result property="fyxhzs"    column="fyxhzs"    />
    <result property="sjxhzs"    column="sjxhzs"    />
    <result property="swl"    column="swl"    />
    <result property="zryl"    column="zryl"    />
    <result property="czbls"    column="czbls"    />
    <result property="zyk"    column="zyk"    />
    <result property="zhylfwf"    column="zhylfwf"    />
    <result property="kff"    column="kff"    />
    <result property="zdf"    column="zdf"    />
    <result property="zlf"    column="zlf"    />
    <result property="ywf"    column="ywf"    />
    <result property="xyyxyzpf"    column="xyyxyzpf"    />
    <result property="hcf"    column="hcf"    />
    <result property="zyf"    column="zyf"    />
    <result property="zyf2"    column="zyf2"    />
    <result property="xyf"    column="xyf"    />
    <result property="qtf"    column="qtf"    />
    <result property="gblbl"    column="gblbl"    />
    <result property="dblbl"    column="dblbl"    />
    <result property="zcblbl"    column="zcblbl"    />
    <result property="xm"    column="xm"    />
    <result property="cmi"    column="cmi"    />
    <result property="bah"    column="bah"    />
    <result property="zyys"    column="zyys"    />
    <result property="bls"    column="bls"    />
    <result property="pjzyr"    column="pjzyr"    />
    <result property="yzb"    column="yzb"    />
    <result property="czb"    column="czb"    />
    <result property="zlqt"    column="zlqt"    />
    <result property="cykb"    column="cykb"    />
    <result property="flag"    column="flag"    />
    <result property="zfbz"    column="zfbz"    />
    <result property="bas"    column="bas"    />
    <result property="blflag"    column="blflag"    />
    <result property="jcf"    column="jcf"    />
    <result property="jyf"    column="jyf"    />
    <result property="ypfbg"    column="ypf_bg"    />
    <result property="jyfbg"    column="jyf_bg"    />
    <result property="jcfbg"    column="jcf_bg"    />
    <result property="zlfbg"    column="zlf_bg"    />
    <result property="hcfbg"    column="hcf_bg"    />
    <result property="qtfbg"    column="qtf_bg"    />
    <result property="qtfy"    column="qtfy"    />
  </resultMap>

  <resultMap type="BaSyjlKsyy" id="BaSyjlKsyyResult">
    <result property="cykb" column="cykb"/>
    <result property="zyys" column="zyys"/>
    <result property="drgbh" column="drgbh"/>
    <result property="drgmc" column="drgmc"/>
    <result property="xmmc" column="xmmc"/>
    <result property="brfy" column="brfy"/>
    <result property="pjfy" column="pjfy"/>
    <result property="details" column="details"/>
    <result property="zyh" column="bah"/>
  </resultMap>

  <resultMap id="GroupDoctorDataVoResult" type="GroupDoctorDataVo">
    <result property="drgbh"    column="drgbh"    />
    <result property="drgmc"    column="drgmc"    />
    <result property="zyys"    column="zyys"    />
    <result property="bls"    column="bls"    />
    <result property="zfy"    column="zfy"    />
    <result property="zyk"    column="zyk"    />
    <result property="cjfy"    column="cjfy"    />
    <result property="pjzyr"    column="pjzyr"    />
    <result property="yzb"    column="yzb"    />
    <result property="czb"    column="czb"    />
    <result property="ywf"    column="ywf"    />
    <result property="ypfbg"    column="ypf_bg"    />
    <result property="zlf"    column="zlf"    />
    <result property="zlfbg"    column="zlf_bg"    />
    <result property="hcf"    column="hcf"    />
    <result property="hcfbg"    column="hcf_bg"    />
    <result property="jcf"    column="jcf"    />
    <result property="jcfbg"    column="jcf_bg"    />
    <result property="jyf"    column="jyf"    />
    <result property="jyfbg"    column="jyf_bg"    />
    <result property="qtf"    column="qtf"    />
    <result property="qtfbg"    column="qtf_bg"    />
  </resultMap>

  <resultMap id="GroupDeptDataVoResult" type="GroupDeptDataVo">
    <result property="drgbh"    column="drgbh"    />
    <result property="drgmc"    column="drgmc"    />
    <result property="cykb"    column="cykb"    />
    <result property="bls"    column="bls"    />
    <result property="zfy"    column="zfy"    />
    <result property="zyk"    column="zyk"    />
    <result property="cjfy"    column="cjfy"    />
    <result property="pjzyr"    column="pjzyr"    />
    <result property="yzb"    column="yzb"    />
    <result property="czb"    column="czb"    />
    <result property="ywf"    column="ywf"    />
    <result property="ypfbg"    column="ypf_bg"    />
    <result property="zlf"    column="zlf"    />
    <result property="zlfbg"    column="zlf_bg"    />
    <result property="hcf"    column="hcf"    />
    <result property="hcfbg"    column="hcf_bg"    />
    <result property="jcf"    column="jcf"    />
    <result property="jcfbg"    column="jcf_bg"    />
    <result property="jyf"    column="jyf"    />
    <result property="jyfbg"    column="jyf_bg"    />
    <result property="qtf"    column="qtf"    />
    <result property="qtfbg"    column="qtf_bg"    />
  </resultMap>


  <!-- 根据科室分组-->
  <select id="selectBzblByDept" parameterType="PatientGroupHomePage" resultMap="GroupDeptDataVoResult">
    select
    a.drgbh,
    b.drgmc,
    cykb,
    COUNT(*) as bls,
    IFNULL(REPLACE(ROUND(SUM(zfy),4),',',''),0) as zfy,
    IFNULL(REPLACE(ROUND(SUM(drgzf - tczf),4),',',''),0) AS zyk,
    IFNULL(REPLACE(ROUND(SUM(zfy)/COUNT(*),4),',',''),0) as cjfy,
    IFNULL(ROUND(SUM(sjzyts)/COUNT(*),4),0) as pjzyr,
    IFNULL(ROUND(SUM(IFNULL(xyf,0)+IFNULL(kjywf,0)+IFNULL(zcyf1,0)+IFNULL(zcyf,0))*100/SUM(zfy),4),0) as yzb,
    IFNULL(ROUND(SUM(IFNULL(hcyyclf,0)+IFNULL(yyclf,0)+IFNULL(ycxyyclf,0))*100/SUM(zfy),4),0) as czb,
    IFNULL(ROUND(SUM(IFNULL(qtf,0)),4),0) as zlqt,


    IFNULL(ROUND(AVG(IFNULL(yxxzdf, 0) + IFNULL(lczdxmf, 0)),2),0) AS jcf,
    IFNULL(ROUND(AVG(IFNULL(syszdf, 0) + IFNULL(blzdf, 0)),2),0) AS jyf,
    IFNULL(ROUND(AVG(IFNULL(qtf,0)),2),0) AS qtf,
    IFNULL(ROUND(AVG(IFNULL(hcyyclf,0) + IFNULL(yyclf,0) + IFNULL(ycxyyclf,0)),2),0) AS hcf,
    IFNULL(ROUND(AVG(IFNULL(fsszlxmf,0) + IFNULL(maf,0) + IFNULL(zyzlf,0) + IFNULL(zlczf,0) + IFNULL(ssf,0)),2),0) AS zlf,
    IFNULL(ROUND(AVG(IFNULL(xyf,0) + IFNULL(zcyf,0) + IFNULL(zcyf1,0)),2),0) AS ywf,

    IFNULL(MAX(b.ypf_bg),0) AS ypf_bg,
    IFNULL(MAX(b.jyf_bg),0) AS jyf_bg,
    IFNULL(MAX(b.jcf_bg),0) AS jcf_bg,
    IFNULL(MAX(b.zlf_bg),0) AS zlf_bg,
    IFNULL(MAX(b.hcf_bg),0) AS hcf_bg,
    IFNULL(MAX(b.qtf_bg),0) AS qtf_bg
    FROM
    ba_syjl a LEFT JOIN drgdict b ON a.drgbh = b.drgbh
    WHERE a.drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    GROUP BY cykb,a.drgbh,b.drgmc
  </select>



  <select id="selectBzblByDoctor" parameterType="PatientGroupHomePage" resultMap="GroupDoctorDataVoResult">
    select
    a.drgbh,
    b.drgmc,
    zyys,
    COUNT(*) as bls,
    IFNULL(REPLACE(ROUND(SUM(zfy),4),',',''),0) as zfy,
    IFNULL(REPLACE(ROUND(SUM(drgzf - tczf),4),',',''),0) AS zyk,
    IFNULL(REPLACE(ROUND(SUM(zfy)/COUNT(*),4),',',''),0) as cjfy,
    IFNULL(ROUND(SUM(sjzyts)/COUNT(*),4),0) as pjzyr,
    IFNULL(ROUND(SUM(IFNULL(xyf,0)+IFNULL(kjywf,0)+IFNULL(zcyf1,0)+IFNULL(zcyf,0))*100/SUM(zfy),4),0) as yzb,
    IFNULL(ROUND(SUM(IFNULL(hcyyclf,0)+IFNULL(yyclf,0)+IFNULL(ycxyyclf,0))*100/SUM(zfy),4),0) as czb,
    IFNULL(ROUND(SUM(IFNULL(qtf,0)),4),0) as zlqt,

    IFNULL(ROUND(AVG(IFNULL(yxxzdf, 0) + IFNULL(lczdxmf, 0)),2),0) AS jcf,
    IFNULL(ROUND(AVG(IFNULL(syszdf, 0) + IFNULL(blzdf, 0)),2),0) AS jyf,
    IFNULL(ROUND(AVG(IFNULL(qtf,0)),2),0) AS qtf,
    IFNULL(ROUND(AVG(IFNULL(hcyyclf,0) + IFNULL(yyclf,0) + IFNULL(ycxyyclf,0)),2),0) AS hcf,
    IFNULL(ROUND(AVG(IFNULL(fsszlxmf,0) + IFNULL(maf,0) + IFNULL(zyzlf,0) + IFNULL(zlczf,0) + IFNULL(ssf,0)),2),0) AS zlf,
    IFNULL(ROUND(AVG(IFNULL(xyf,0) + IFNULL(zcyf,0) + IFNULL(zcyf1,0)),2),0) AS ywf,

    IFNULL(MAX(b.ypf_bg),0) AS ypf_bg,
    IFNULL(MAX(b.jyf_bg),0) AS jyf_bg,
    IFNULL(MAX(b.jcf_bg),0) AS jcf_bg,
    IFNULL(MAX(b.zlf_bg),0) AS zlf_bg,
    IFNULL(MAX(b.hcf_bg),0) AS hcf_bg,
    IFNULL(MAX(b.qtf_bg),0) AS qtf_bg
    FROM
    ba_syjl a LEFT JOIN drgdict b ON a.drgbh = b.drgbh
    WHERE a.drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    GROUP BY zyys,a.drgbh,b.drgmc
  </select>


  <!-- 费用结构 -->
  <select id="selectFyjg" parameterType="PatientGroupHomePage" resultMap="PatientGroupHomePageResult">
    select
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(ylfuf, 0) + IFNULL(zlczf, 0) + IFNULL(hlf, 0) + IFNULL(qtfy, 0)),0)/ 10000, 4),',','') AS zhylfwf,
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(kff, 0)),0)/ 10000, 4 ),',','') AS kff,
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(blzdf, 0) + IFNULL(syszdf, 0) + IFNULL(yxxzdf, 0) + IFNULL(lczdxmf, 0)),0)/ 10000, 4),',','') AS zdf,
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(fsszlxmf, 0) + IFNULL(wlzlf, 0) + IFNULL(sszlf, 0) + IFNULL(maf, 0) + IFNULL(ssf, 0)),0)/ 10000, 4),',','') AS zlf,
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(xyf, 0) + IFNULL(kjywf, 0) + IFNULL(zcyf1, 0) +IFNULL( zcyf, 0)),0)/ 10000, 4 ),',','') AS ywf,
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(xf, 0) + IFNULL(bdblzpf, 0) + IFNULL(qdblzpf, 0) + IFNULL(nxyzlzpf, 0) + IFNULL(xbyzlzpf, 0)),0)/ 10000, 4),',','') AS xyyxyzpf,
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(hcyyclf, 0) + IFNULL(yyclf, 0) + IFNULL(ycxyyclf, 0)),0)/ 10000, 4),',','') AS hcf,
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(zyzlf, 0)),0)/ 10000, 4),',','') AS zyf,
    REPLACE(FORMAT(IFNULL(SUM(IFNULL(qtf, 0)),0)/ 10000, 4),',','') AS qtf
    FROM
      ba_syjl a
    where a.drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <!-- 病组整体情况 -->
  <select id="selectBzztfx" parameterType="PatientGroupHomePage" resultMap="PatientGroupHomePageResult">
    SELECT
    a.drgbh,
    b.drgmc,
    COUNT(*) AS bas,
    IFNULL(REPLACE ( FORMAT( SUM(zfy)/ 10000, 4 ), ',', '' ),0) AS zfy,
    IFNULL(SUM( CASE WHEN a.zyzt = 0 THEN 1 ELSE 0 END ),0) AS cybls,
    IFNULL(REPLACE ( FORMAT( SUM(zfy)/COUNT(*) / 10000, 4 ), ',', '' ),0) AS cjfy,
    IFNULL(REPLACE ( ROUND( SUM( a.cmi ), 4 ), ',', '' ),0) AS zqz,
    IFNULL(ROUND( COUNT(*)/( SELECT COUNT(*) FROM ba_syjl ), 4 ),0) AS rcrqb,
    IFNULL(ROUND( AVG(zfy)/ a.zfbz, 4 ),0) AS fyxhzs,
    IFNULL(ROUND(IFNULL(AVG(IFNULL(a.sjzyts,0))/b.pjdays,0),4),0) AS sjxhzs,
    IFNULL(ROUND((SUM(case when lyfs='死亡' then 1 else 0 end)*100)/COUNT(*),4),0) AS swl,
    0 AS zryl,
    IFNULL(REPLACE(FORMAT(SUM(IFNULL(drgzf,0) -IFNULL(tczf,0))/10000,4),',',''),0) as zyk
    FROM ba_syjl a LEFT JOIN drgdict b ON a.drgbh = b.drgbh WHERE a.drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    GROUP BY a.drgbh, b.drgmc
  </select>

  <!-- 倍率情况分析 -->
  <select id="selectBzBllxfx" parameterType="PatientGroupHomePage" resultMap="PatientGroupHomePageResult">
    select
    xm,
    ROUND(zfy/zfbz,4) AS fyxhzs,
    cmi,
    IFNULL(zfy,0) as zfy,
    zfbz,
    blflag
    FROM ba_syjl a
    WHERE drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <!-- 超支结余病例 -->
  <select id="selectBzCzjyBl" parameterType="PatientGroupHomePage" resultMap="PatientGroupHomePageResult">
    select
    xm,
    ROUND(zfy/zfbz,4) AS fyxhzs,
    cmi,
    (CASE WHEN zfy > zfbz THEN 1 ELSE 0 END) as flag
    FROM ba_syjl a WHERE a.drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <!-- 病组超支结余数 -->
  <select id="selectBzCzjy" parameterType="PatientGroupHomePage" resultMap="PatientGroupHomePageResult">
    SELECT
    SUM(CASE WHEN IFNULL(zfy,0) > IFNULL(zfbz,0) THEN 1 ELSE 0 END) AS czbls,
    SUM(CASE WHEN IFNULL(zfy,0) &lt; IFNULL(zfbz,0) THEN 1 ELSE 0 END) AS jybls
    FROM ba_syjl a WHERE drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>


  <!-- 病组超支病例费用结构  -->
  <select id="selectBzczblfyjg" parameterType="PatientGroupHomePage" resultMap="PatientGroupHomePageResult">
    SELECT
    IFNULL(AVG(IFNULL(yxxzdf, 0) + IFNULL(lczdxmf, 0)),0) AS jcf,
    IFNULL(AVG(IFNULL(syszdf, 0) + IFNULL(blzdf, 0)),0) AS jyf,
    IFNULL(AVG(IFNULL(qtf,0)),0) AS qtf,
    IFNULL(AVG(IFNULL(hcyyclf,0) + IFNULL(yyclf,0) + IFNULL(ycxyyclf,0)),0) AS hcf,
    IFNULL(AVG(IFNULL(fsszlxmf,0) + IFNULL(maf,0) + IFNULL(zyzlf,0) + IFNULL(zlczf,0) + IFNULL(ssf,0)),0) AS zlf,
    IFNULL(AVG(IFNULL(xyf,0) + IFNULL(zcyf,0) + IFNULL(zcyf1,0)),0) AS ywf,
    IFNULL(AVG(IFNULL(zfy,0)),0) AS zfy
    FROM ba_syjl a WHERE IFNULL(zfy,0) > IFNULL(zfbz,0) AND drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <!-- 病组结余病例费用结构  -->
  <select id="selectBzjyblfyjg" parameterType="PatientGroupHomePage" resultMap="PatientGroupHomePageResult">
    SELECT
    IFNULL(AVG(IFNULL(yxxzdf, 0) + IFNULL(lczdxmf, 0)),0) AS jcf,
    IFNULL(AVG(IFNULL(syszdf, 0) + IFNULL(blzdf, 0)),0) AS jyf,
    IFNULL(AVG(IFNULL(qtf,0)),0) AS qtf,
    IFNULL(AVG(IFNULL(hcyyclf,0) + IFNULL(yyclf,0) + IFNULL(ycxyyclf,0)),0) AS hcf,
    IFNULL(AVG(IFNULL(fsszlxmf,0) + IFNULL(maf,0) + IFNULL(zyzlf,0) + IFNULL(zlczf,0) + IFNULL(ssf,0)),0) AS zlf,
    IFNULL(AVG(IFNULL(xyf,0) + IFNULL(zcyf,0) + IFNULL(zcyf1,0)),0) AS ywf,
    IFNULL(AVG(IFNULL(zfy,0)),0) AS zfy
    FROM ba_syjl a WHERE IFNULL(zfy,0) &lt; IFNULL(zfbz,0) AND drgbh = #{drgbh}
    AND (zfy IS NOT NULL AND zfy != 0)
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
        select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>


  <!-- 病组亏损明细  -->
  <select id="selectBzKsmx" parameterType="PatientGroupHomePage" resultMap="BaSyjlKsyyResult">
    SELECT a.cykb,a.zyys,a.drgbh,a.drgmc,b.xmmc,b.brfy,b.pjfy,b.details,a.bah,b.type
    FROM ba_syjl a
    JOIN ba_syjl_ksyy b ON a.brbs = b.brbs
    WHERE a.drgbh = #{drgbh}
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
      select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>



  <!-- 病组亏损项目统计  -->
  <select id="selectBzKsxm" parameterType="PatientGroupHomePage" resultMap="BaSyjlKsyyResult">
    SELECT b.xmmc,SUM(IFNULL(brfy,0) - IFNULL(pjfy,0)) AS ccje
    FROM ba_syjl a
    JOIN ba_syjl_ksyy b ON a.brbs = b.brbs
    WHERE a.drgbh = #{drgbh} AND b.xmmc IS NOT NULL
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
      select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    GROUP BY b.xmmc
    ORDER BY ccje DESC
    LIMIT 10
  </select>


  <!-- 病组亏损明细  -->
  <select id="selectBzKsmxDept" parameterType="PatientGroupHomePage" resultMap="BaSyjlKsyyResult">
    SELECT a.cykb,MAX(a.drgbh) AS drgbh,MAX(a.drgmc) AS drgmc,b.xmmc,ROUND(AVG(b.brfy),4) AS brfy,ROUND(AVG(b.pjfy),4) AS pjfy,b.type AS type
    FROM ba_syjl a
    JOIN ba_syjl_ksyy b ON a.brbs = b.brbs
    WHERE a.drgbh = #{drgbh}
    <if test="deptName != null and deptName != '' and deptName != '所有'">
      AND (cykb = #{deptName}  OR cykb = (
      select source_dept_name from dept_consolidation where target_dept_name = #{deptName} limit 1
      ))
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND
      POSITION(ylfkfs IN #{cblb}) > 0) OR
      (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
    </if>
    <if test="dateType == 'cydate'">
      and zyzt = '0'
      <if test="adtFrom != null">
        and cydate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and cydate &lt; #{adtTo}
      </if>
    </if>
    <if test="dateType == 'jsdate'">
      <if test="adtFrom != null">
        and his_jsdate >= #{adtFrom}
      </if>
      <if test="adtTo != null">
        and his_jsdate &lt; #{adtTo}
      </if>
    </if>
    <if test="isCheckRy == 'check'">
      <if test="adtFromRy != null">
        and rydate >= #{adtFromRy}
      </if>
      <if test="adtToRy != null">
        and rydate &lt; #{adtToRy}
      </if>
    </if>
    <if test="qsfs != null and qsfs != ''">
      and psh like concat(#{qsfs}, '%')
    </if>
    <if test="zyys != null and zyys != ''">
      AND zyys = #{zyys}
    </if>
    <if test="ykflag != null and ykflag != ''">
      <if test="ykflag == 'ying'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) > 0
      </if>
      <if test="ykflag == 'kui'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) &lt; 0
      </if>
      <if test="ykflag == 'ping'">
        AND IFNULL(a.drgzf,0) - IFNULL(a.tczf,0) = 0
      </if>
    </if>
    <if test="zd != null and zd != ''">
      AND CONCAT(jbdm,zyzd) = #{zd}
    </if>
    <if test="ss != null and ss != ''">
      AND CONCAT(IFNULL(ssjczbm1,''),IFNULL(ssjczmc1,'')) = #{ss}
    </if>
    <if test="deptList != null and deptList.size() > 0">
      and cykb in
      <foreach collection="deptList" item="item" close=")" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    GROUP BY a.cykb,b.xmmc,b.type
  </select>


</mapper>
