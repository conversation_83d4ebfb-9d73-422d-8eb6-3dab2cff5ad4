<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DoctorHomePageMapper">

  <resultMap type="DoctorHomePage" id="DoctorHomePageResult">
    <result property="zyys" column="zyys"/>
    <result property="zfy" column="zfy"/>
    <result property="cybls" column="cybls"/>
    <result property="cjfy" column="cjfy"/>
    <result property="zqz" column="zqz"/>
    <result property="rcrqb" column="rcrqb"/>
    <result property="fyxhzs" column="fyxhzs"/>
    <result property="sjxhzs" column="sjxhzs"/>
    <result property="swl" column="swl"/>
    <result property="drgzs" column="drgzs"/>
    <result property="bas" column="bas"/>
    <result property="rzl" column="rzl"/>
    <result property="cmi" column="cmi"/>
    <result property="pjzyr" column="pjzyr"/>
    <result property="yzb" column="yzb"/>
    <result property="czb" column="czb"/>
    <result property="zfl" column="zfl"/>
    <result property="zyk" column="zyk"/>
    <result property="xm" column="xm"/>
    <result property="drgmc" column="drgmc"/>
    <result property="flag" column="flag"/>
    <result property="zhylfwf" column="zhylfwf"/>
    <result property="kff" column="kff"/>
    <result property="zdf" column="zdf"/>
    <result property="zlf" column="zlf"/>
    <result property="ywf" column="ywf"/>
    <result property="xyyxyzpf" column="xyyxyzpf"/>
    <result property="hcf" column="hcf"/>
    <result property="zyf" column="zyf"/>
    <result property="qtf" column="qtf"/>
    <result property="zfbz" column="zfbz"/>
  </resultMap>


  <!-- 查询整体数据 -->
  <select id="selectYsztsj" parameterType="DoctorHomePage" resultMap="DoctorHomePageResult">
    SELECT
    a.zyys,
    a.cykb,
    REPLACE(FORMAT(SUM(a.zfy)/10000,4),',','') AS zfy,
    SUM(CASE WHEN a.zyzt = 0 THEN 1 ELSE 0 END) AS cybls,
    REPLACE(FORMAT((SUM(a.zfy)/COUNT(*))/10000,4),',','') AS cjfy,
    REPLACE(ROUND(SUM(a.cmi),4),',','') AS zqz,
    ROUND(COUNT(*)/(SELECT COUNT(*) FROM ba_syjl),4) AS rcrqb,
    ROUND(AVG(a.zfy)/a.zfbz,4) AS fyxhzs,
    ROUND(IFNULL(AVG(a.sjzyts)/b.pjdays,0),4) AS sjxhzs,
    ROUND((SUM(CASE WHEN lyfs='死亡' THEN 1 ELSE 0 END)) * 100/COUNT(*),4) AS swl,
    COUNT(DISTINCT CASE WHEN a.drgbh='000' OR a.drgbh LIKE '%QY' THEN null ELSE a.drgbh END) AS drgzs,
    SUM(rzflag) AS bas,
    ROUND(SUM(rzflag)*100/COUNT(*),4) AS rzl,
    ROUND(SUM(a.cmi)/COUNT(*),4) AS cmi,
    ROUND(SUM(a.sjzyts)/COUNT(*),4) AS pjzyr,
    ROUND(SUM(IFNULL(xyf,0)+IFNULL(kjywf,0)+IFNULL(zcyf1,0)+IFNULL(zcyf,0)) * 100/SUM(a.zfy),4) AS yzb,
    ROUND(SUM(IFNULL(hcyyclf,0)+IFNULL(yyclf,0)+IFNULL(ycxyyclf,0)) * 100/SUM(a.zfy),4) AS czb,
    FORMAT(SUM(IFNULL(drgzf,0) -IFNULL(tczf,0))/10000,4) AS zyk
    FROM ba_syjl a JOIN drgdict b ON a.drgbh = b.drgbh WHERE a.cykb = #{cykb}
    <if test="zyys != null and zyys != ''">
      and a.zyys = #{zyys}
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN IFNULL(#{cblb},'') = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND POSITION(ylfkfs IN #{cblb}) > 0)
      OR (CASE WHEN IFNULL(#{cblb},'') ='' THEN '%' ELSE #{cblb} END = '%' ))
    </if>
    <if test="dateType != null">
      AND ((#{dateType} = 'cydate' AND cydate >= #{adtFrom} AND cydate &lt; #{adtTo}) OR (#{dateType} = 'jsdate' AND
      his_jsdate >= #{adtFrom} AND his_jsdate &lt; #{adtTo}))
    </if>
    GROUP BY
    <if test="zyys != null and zyys != ''">
      a.zyys,
    </if>
    a.cykb
  </select>

  <!-- 查询超支结余病历情况 -->
  <select id="selectYsCzjyBl" parameterType="DoctorHomePage" resultMap="DoctorHomePageResult">
    select
    xm,
    ROUND(a.zfy/a.zfbz,4) AS fyxhzs,
    a.cmi AS cmi,
    (CASE WHEN a.zfy > a.zfbz THEN 1 ELSE 0 END) AS flag
    FROM ba_syjl a JOIN drgdict b ON a.drgbh = b.drgbh WHERE a.cykb = #{cykb}
    <if test="zyys != null and zyys != ''">
      and a.zyys = #{zyys}
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN IFNULL(#{cblb},'') = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND POSITION(ylfkfs IN #{cblb}) > 0)
      OR (CASE WHEN IFNULL(#{cblb},'') ='' THEN '%' ELSE #{cblb} END = '%' ))
    </if>
    <if test="dateType != null">
      AND ((#{dateType} = 'cydate' AND cydate >= #{adtFrom} AND cydate &lt; #{adtTo}) OR (#{dateType} = 'jsdate' AND
      his_jsdate >= #{adtFrom} AND his_jsdate &lt; #{adtTo}))
    </if>
  </select>


  <!-- 查询超支结余病组情况 -->
  <select id="selectYsCzjyBz" parameterType="DoctorHomePage" resultMap="DoctorHomePageResult">
    select
    b.drgmc,
    a.drgbh,
    ROUND(AVG(a.zfy)/a.zfbz,4) AS fyxhzs,
    ROUND(SUM(a.cmi)/COUNT(*),4) AS cmi,
    (CASE WHEN SUM(a.zfy) > SUM(a.zfbz) THEN 1 ELSE 0 END) as flag
    FROM ba_syjl a JOIN drgdict b ON a.drgbh = b.drgbh WHERE a.cykb = #{cykb}
    <if test="zyys != null and zyys != ''">
      and a.zyys = #{zyys}
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN IFNULL(#{cblb},'') = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND POSITION(ylfkfs IN #{cblb}) > 0)
      OR (CASE WHEN IFNULL(#{cblb},'') ='' THEN '%' ELSE #{cblb} END = '%' ))
    </if>
    <if test="dateType != null">
      AND ((#{dateType} = 'cydate' AND cydate >= #{adtFrom} AND cydate &lt; #{adtTo}) OR (#{dateType} = 'jsdate' AND
      his_jsdate >= #{adtFrom} AND his_jsdate &lt; #{adtTo}))
    </if>
    GROUP BY a.drgbh,b.drgmc
  </select>


  <!-- 费用倍率情况查询 -->
  <select id="selectYsBllxfx" parameterType="DoctorHomePage" resultMap="DoctorHomePageResult">
    select
    xm,
    ROUND(a.zfy/a.zfbz,4) AS fyxhzs,
    a.cmi AS cmi,
    a.zfbz,
    REPLACE(IFNULL(a.zfy,0),',','') as zfy,
    (CASE WHEN a.blflag = 2 THEN 1
          WHEN a.blflag = 1 THEN 2
          WHEN (a.blflag = 0 OR a.blflag IS NULL) THEN 0
          ELSE NULL
    END) AS flag
    FROM ba_syjl a JOIN drgdict b ON a.drgbh = b.drgbh
    WHERE a.cykb = #{cykb}
    <if test="zyys != null and zyys != ''">
      and a.zyys = #{zyys}
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN IFNULL(#{cblb},'') = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND POSITION(ylfkfs IN #{cblb}) > 0)
      OR (CASE WHEN IFNULL(#{cblb},'') ='' THEN '%' ELSE #{cblb} END = '%' ))
    </if>
    <if test="dateType != null">
      AND ((#{dateType} = 'cydate' AND cydate >= #{adtFrom} AND cydate &lt; #{adtTo}) OR (#{dateType} = 'jsdate' AND
      his_jsdate >= #{adtFrom} AND his_jsdate &lt; #{adtTo}))
    </if>
  </select>


  <!-- 费用结构 -->
  <select id="selectYsFyjg" parameterType="DoctorHomePage" resultMap="DoctorHomePageResult">
    SELECT
    REPLACE(FORMAT( SUM( IFNULL(ylfuf, 0) + IFNULL(zlczf, 0) + IFNULL(hlf, 0) + IFNULL(qtfy, 0))/ 10000, 4),',','') AS zhylfwf,
    REPLACE(FORMAT( SUM( IFNULL(kff, 0))/ 10000, 4 ),',','') AS kff,
    REPLACE(FORMAT( SUM( IFNULL(blzdf, 0) + IFNULL(syszdf, 0) + IFNULL(yxxzdf, 0) + IFNULL(lczdxmf, 0))/ 10000, 4),',','') AS zdf,
    REPLACE(FORMAT( SUM( IFNULL(fsszlxmf, 0) + IFNULL(wlzlf, 0) + IFNULL(sszlf, 0) + IFNULL(maf, 0) + IFNULL(ssf, 0))/ 10000, 4),',','') AS zlf,
    REPLACE(FORMAT( SUM( IFNULL(xyf, 0) + IFNULL(kjywf, 0) + IFNULL(zcyf1, 0) +IFNULL( zcyf, 0))/ 10000, 4 ),',','') AS ywf,
    REPLACE(FORMAT( SUM( IFNULL(xf, 0) + IFNULL(bdblzpf, 0) + IFNULL(qdblzpf, 0) + IFNULL(nxyzlzpf, 0) + IFNULL(xbyzlzpf, 0))/ 10000, 4),',','') AS xyyxyzpf,
    REPLACE(FORMAT( SUM( IFNULL(hcyyclf, 0) + IFNULL(yyclf, 0) + IFNULL(ycxyyclf, 0))/ 10000, 4),',','') AS hcf,
    REPLACE(FORMAT( SUM( IFNULL(zyzlf, 0))/ 10000, 4),',','') AS zyf,
    REPLACE(FORMAT( SUM( IFNULL(qtf, 0))/ 10000, 4),',','') AS qtf
    FROM
      ba_syjl a
    WHERE a.cykb = #{cykb}
    <if test="zyys != null and zyys != ''">
      and a.zyys = #{zyys}
    </if>
    <if test="cblb != null">
      AND ((CASE WHEN IFNULL(#{cblb},'') = '' THEN '%' ELSE #{cblb} END &lt;> '%' AND POSITION(ylfkfs IN #{cblb}) > 0)
      OR (CASE WHEN IFNULL(#{cblb},'') ='' THEN '%' ELSE #{cblb} END = '%' ))
    </if>
    <if test="dateType != null">
      AND ((#{dateType} = 'cydate' AND cydate >= #{adtFrom} AND cydate &lt; #{adtTo}) OR (#{dateType} = 'jsdate' AND
      his_jsdate >= #{adtFrom} AND his_jsdate &lt; #{adtTo}))
    </if>
  </select>
</mapper>
