<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TjfxMapper">

  <resultMap type="BlqzYkxx" id="BlqzYkxxResult">
    <result property="drgbh" column="drgbh"/>
    <result property="drgmc" column="drgmc"/>
    <result property="zyts" column="zyts"/>
    <result property="zfqz" column="zfqz"/>
    <result property="zyk" column="zyk"/>
    <result property="rs" column="rs"/>
    <result property="bz" column="bz"/>
    <result property="date" column="date"/>
    <result property="cykb" column="cykb"/>
    <result property="cblb" column="cblb"/>
    <result property="datetype" column="datetype"/>
    <result property="ljfy" column="ljfy"/>
  </resultMap>

  <resultMap type="BaZbfx" id="BaZbfxResult">
    <result property="title" column="title"/>
    <result property="num" column="num"/>
    <result property="name" column="name"/>
    <result property="value" column="value"/>
  </resultMap>


  <resultMap type="DeptYxzbQueryVo" id="DeptYxzbQueryVoResult">
    <result property="cykb" column="cykb"/>
    <result property="zfqz" column="zfqz"/>
    <result property="pjdays" column="pjdays"/>
    <result property="fyxhzs" column="fyxhzs"/>
    <result property="sjxhzs" column="sjxhzs"/>
    <result property="zyk" column="zyk"/>
    <result property="ljzfqz" column="ljzfqz"/>
  </resultMap>



  <select id="selectBaZbfx" resultMap="BaZbfxResult" parameterType="BaZbfx">
    call usp_drg_bazbfx(#{adtFrom},#{adtTo},#{cykb},#{cblb},#{datetype},#{jlly},'admin');
  </select>

  <sql id="selectBlqzYkxxVo">

  </sql>


  <select id="selectBlqzYkxx" parameterType="BlqzYkxx" resultMap="BlqzYkxxResult">
    select
    a.drgbh as drgbh,
    b.drgmc as drgmc,
    IFNULL(ROUND(SUM(a.sjzyts)/count(*)),0) as zyts,
    ROUND(a.cmi,2) as zfqz,
    ROUND(SUM(drgzf -tczf),2) AS zyk,
    COUNT(*) as rs,
    round(sum(zfy)/count(*), 2) as ljfy
    from ba_syjl a join drgdict b
    on a.drgbh = b.drgbh
    <where>
      <if test="cykb != null  and cykb != ''">and cykb = #{cykb}</if>
      <if test="params.beginDate != null and params.beginDate != '' and
                   params.endDate != null and params.endDate != ''   and
                   datetype != null and datetype != ''">
        and ((#{datetype}='cydate' AND cydate>=#{params.beginDate} AND cydate &lt; #{params.endDate}) OR
        (#{datetype}='jsdate' AND his_jsdate>=#{params.beginDate} AND his_jsdate &lt; #{params.endDate}))
      </if>
      <if test="cblb != null  and cblb != ''">
        and (( case when #{cblb}='' then '%' else #{cblb} end &lt;&gt; '%' AND POSITION( ylfkfs IN #{cblb})>0) OR
        ( case when #{cblb}='' then '%' else #{cblb} end= '%' ))
      </if>
    </where>
    GROUP BY a.drgbh,b.drgmc,a.cmi
  </select>

  <select id="selectKsqzYkxx" resultMap="BlqzYkxxResult" parameterType="blqzYkxx">
    select
    IFNULL(ROUND(SUM(a.sjzyts)/count(*)),0) as zyts,
    ROUND(a.cmi,2) as zfqz,
    ROUND(SUM(drgzf -tczf),2) AS zyk,
    COUNT(*) as rs,
    round(sum(zfy)/count(*), 2) as ljfy,
    cykb as cykb
    from ba_syjl a join drgdict b
    on a.drgbh = b.drgbh
    <where>
      <if test="params.beginDate != null and params.beginDate != '' and
                   params.endDate != null and params.endDate != ''   and
                   datetype != null and datetype != ''">
        and ((#{datetype}='cydate' AND cydate>=#{params.beginDate} AND cydate &lt; #{params.endDate}) OR
        (#{datetype}='jsdate' AND his_jsdate>=#{params.beginDate} AND his_jsdate &lt; #{params.endDate}))
      </if>
      <if test="cblb != null  and cblb != ''">
        and (( case when #{cblb}='' then '%' else #{cblb} end &lt;&gt; '%' AND POSITION( ylfkfs IN #{cblb})>0) OR
        ( case when #{cblb}='' then '%' else #{cblb} end= '%' ))
      </if>
    </where>
    GROUP BY a.cykb
  </select>

  <select id="selectAvgCMI" resultType="Double">
    select ROUND(AVG(a.cmi), 2)
    from ba_syjl a
           join drgdict b on a.drgbh = b.drgbh
  </select>
  <select id="selectZbfx" parameterType="zbfxQueryVo" resultType="zbfx">
    select
    drgbh,
    drgmc,
    cykb,
    sum(rzflag) AS drgzs,
    ROUND(SUM(cmi), 2) AS zqz,
    round(max(cmi), 2) as zfqz,
    COUNT(mdcbh) AS mdczs,
    ROUND(AVG(cmi), 2) AS pjqz,
    COUNT(DISTINCT jbdm) AS jbdmsl,
    COUNT(DISTINCT ssjczbm1) AS sssl,
    COUNT(brbs) AS brs,
    ROUND(AVG(ifnull(zfy, 0) / zfbz), 2) AS fyxhzs,
    ROUND(AVG(ifnull(sjzyts, 0) / pjdays), 2) AS sjxhzs,
    ROUND(SUM(swrs) * 100 / COUNT(*), 2) AS swl,
    SUM(rzflag) AS rzls,
    SUM(rzflag) / COUNT(*) AS rzl,
    SUM(zfy) AS zfy,
    SUM(ykje) AS yk,
    ROUND(AVG(zfy), 2) AS cjfy,
    ROUND(AVG(sjzyts), 2) AS brpjdays,
    IFNULL(ROUND(SUM(ypf) / SUM(zfy), 2), 0) AS yzb,
    IFNULL(ROUND(SUM(hcf) / SUM(zfy), 2), 0) AS czb,
    round(sum(zfy / zfbz), 2) as zfbzb,
    jcfl,
    max(zfbz) as zfbz,
    ROUND(SUM(cmi)/COUNT(*),2) AS cmi,
    SUM(CASE WHEN sjzyts+0&lt;2 THEN 1 ELSE 0 END) AS shortterm,
    SUM(CASE WHEN sjzyts+0&gt;60 THEN 1 ELSE 0 END) AS longterm,
    sum(case when zfy &lt; ifnull(zfbz * 0.4, 0) then 1 else 0 end) as fyjd,
    sum(case when cmi &lt;= 1 and zfy &gt;= zfbz * 3 then 1
    when cmi &lt;= 2 and cmi &gt; 1 and zfy &gt;= zfbz * 2.5 then 1
    when cmi &gt; 2 and cmi &lt;= 3 and zfy &gt;= zfbz * 2 then 1
    when cmi &gt; 3 and zfy &gt;= zfbz * 1.5 then 1
    else 0 end ) as fyjg
    from
    (SELECT cykb,
    zyys,
    drgdict.drgbh as drgbh,
    rzflag,
    cmi,
    mdcbh,
    jbdm,
    ssjczbm1,
    brbs,
    zfy,
    sjzyts,
    ba_syjl.zfbz as zfbz,
    pjdays,
    IF(dfxflag = '1' and lyfs = '死亡', 1, 0) dfxsw,
    IF(dfxflag = '1', 1, 0) AS dfxrs,
    IF(lyfs = '死亡', 1, 0) AS swrs,
    IFNULL(xyf, 0) + IFNULL(zcyf1, 0) + IFNULL(zcyf, 0) AS ypf,
    IFNULL(hcyyclf, 0) + IFNULL(yyclf, 0) + IFNULL(ycxyyclf, 0) AS hcf,
    drgzf - tczf AS ykje,
    ylfkfs AS cblb,
    jcfl,
    drgdict.drgmc as drgmc
    FROM ba_syjl
    LEFT JOIN drgdict ON ba_syjl.drgbh = drgdict.drgbh
    <where>
      and case when #{vo.datetype} = 'jsdate' then his_jsdate else cydate end &gt;= #{vo.startDate}
      and case when #{vo.datetype} = 'jsdate' then his_jsdate else cydate end &lt;= #{vo.endDate}
      AND zyzt = '0'
      <if test="vo.deptname != null and vo.deptname != '' and vo.deptname != '全院'">and cykb = #{vo.deptname}</if>
      <if test="vo.doctor != null and vo.doctor != '' and vo.doctor != '所有'">and zyys = #{vo.doctor}</if>
      <if test="vo.cblb != null and vo.cblb != '' and vo.cblb != '所有'">and ylfkfs = #{vo.cblb}</if>
      AND (ba_syjl.zfy IS NOT NULL AND ba_syjl.zfy &lt;&gt; 0)
    </where>
    ) as a
    <if test="(vo.is_hz == null or vo.is_hz != 1) and vo.dataType == 1">
      group by drgbh
    </if>
    <if test="(vo.is_hz == null or vo.is_hz != 1) and vo.dataType == 2">
      group by cykb
    </if>
    order by yk desc
  </select>

  <select id="selectFztjData" resultType="map" parameterType="fztjQueryVo">
    SELECT *
    FROM (SELECT #{cblb}                                                                     as cblb,
                 #{dateType}                                                                 as dateType,
                 #{startDate}                                                                AS startDate,
                 #{endDate}                                                                  AS endDate,
                 b.drgmc                                                                     AS drgmc,
                 a.drgbh                                                                     AS drgbh,
                 a.cmi                                                                      AS zfqz,
                 COUNT(DISTINCT a.cykb)                                                      AS cykb,
                 ROUND(SUM(a.cmi), 2)                                                       AS zqz,
                 ROUND(AVG(sjzyts), 2)                                                       AS pjzyr,
                 ROUND(AVG(a.zfy), 2)                                                        AS pjfy,
                 ROUND(AVG(a.zfy) / a.zfbz, 2)                                               AS fyxhzs,
                 ROUND(SUM(a.zfy), 2)                                                        AS zfy,
                 ROUND(SUM(drgzf - tczf), 2)                                                 AS zyk,
                 ROUND(SUM(drgzf - tczf), 2) / COUNT(*)                                      AS ljyk,
                 (SUM(CASE WHEN lyfs = '死亡' THEN 1 ELSE 0 END) * 100) / COUNT(*)           AS swl,
                 SUM(CASE WHEN zfy &lt; a.zfbz / 5 OR zfy &gt; a.zfbz * 2 THEN 1 ELSE 0 END) AS drgfyjdjjgz,
                 SUM(CASE WHEN sjzyts + 0 &lt;= 2 OR sjzyts &gt; 60 THEN 1 ELSE 0 END)       AS zytsxy2hdy60t,
                 COUNT(*)                                                                    AS sl,
                 MAX(a.zfbz)                                                                 AS zfbz
          FROM ba_syjl a
                 LEFT JOIN drgdict b ON a.drgbh = b.drgbh
          WHERE zyzt = '0'
            AND (a.zfy IS NOT NULL AND a.zfy &lt;&gt; 0)
            AND (a.drgbh NOT LIKE '%QY%' AND a.drgbh NOT LIKE '%00%' AND a.drgbh IS NOT NULL)
            AND ((CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END &lt;&gt; '%' AND
                  POSITION(ylfkfs IN #{cblb}) &gt; 0) OR
                 (CASE WHEN #{cblb} = '' THEN '%' ELSE #{cblb} END = '%'))
            AND ((#{dateType} = 'cydate' AND cydate &gt;= #{startDate} AND cydate &lt;= #{endDate}) OR
                 (#{dateType} = 'jsdate' AND his_jsdate &gt;= #{startDate} AND his_jsdate &lt;= #{endDate})
            OR (#{dateType} = 'rydate' AND rydate &gt;= #{startDate} AND his_jsdate &lt;= #{endDate}))
            and (cykb = #{ksname} or 1 = if(#{ksname} = '所有', 1, 0))
          GROUP BY b.drgmc, a.drgbh) a
    ORDER BY zyk
  </select>

  <select id="selectBlykfx" resultType="blykfxVo">
      SELECT * FROM (

      SELECT '0' AS TYPE,bah,xm,xb,ylfkfs,nl,cykb,zyys,cydate,rydate,a.his_jsdate as
      hisJsdate,a.drgmc,a.drgbh,a.zfbz,zfy,
      CASE WHEN (a.drgbh LIKE '%QY%' OR a.drgbh LIKE '%00%' OR a.drgbh IS NULL ) THEN 1 ELSE ROUND(zfy/CASE WHEN
      (a.zfbz=0 OR a.zfbz IS NULL ) THEN zfy ELSE a.zfbz END,4) END AS bl ,
      CASE WHEN blflag =3 THEN '03主诊不存在' WHEN blflag = 4 THEN '04歧义组' ELSE
      CASE WHEN blflag =2 THEN '01费用极高'
      WHEN blflag =1 THEN '02费用极低'
      ELSE '05正常倍率' END END AS blzt,
      sjzyts,ROUND(drgzf -tczf,2) AS ykje, CONCAT(jbdm,'|',zyzd) AS jbdm,CONCAT(ssjczbm1,'|',ssjczmc1) AS ssjczbm1,
      xyf+zcyf+zcyf AS ypf,b.ypf_bg,syszdf AS jyf,b.jyf_bg,yxxzdf jcf,b.jcf_bg,ssf AS ssf,b.ssf_bg,zlczf AS
      zlf,b.zlf_bg,hcyyclf+yyclf+ycxyyclf AS hcf,b.hcf_bg,a.tczf,a.drgzf,a.brbs,pjdays,
      CONCAT(
      (CASE WHEN a.qtzd1 IS NOT NULL AND a.qtzd1
      &lt;&gt;
      '' THEN CONCAT(a.jbdm1,'[',a.qtzd1,']') ELSE '' END),
      (CASE WHEN a.qtzd2 IS NOT NULL AND a.qtzd2
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm2,'[',a.qtzd2,']') ELSE '' END),
      (CASE WHEN a.qtzd3 IS NOT NULL AND a.qtzd3
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm3,'[',a.qtzd3,']') ELSE '' END),
      (CASE WHEN a.qtzd4 IS NOT NULL AND a.qtzd4
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm4,'[',a.qtzd4,']') ELSE '' END),
      (CASE WHEN a.qtzd5 IS NOT NULL AND a.qtzd5
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm5,'[',a.qtzd5,']') ELSE '' END),
      (CASE WHEN a.qtzd6 IS NOT NULL AND a.qtzd6
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm6,'[',a.qtzd6,']') ELSE '' END),
      (CASE WHEN a.qtzd7 IS NOT NULL AND a.qtzd7
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm7,'[',a.qtzd7,']') ELSE '' END),
      (CASE WHEN a.qtzd8 IS NOT NULL AND a.qtzd8
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm8,'[',a.qtzd8,']') ELSE '' END),
      (CASE WHEN a.qtzd9 IS NOT NULL AND a.qtzd9
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm9,'[',a.qtzd9,']') ELSE '' END),
      (CASE WHEN a.qtzd10 IS NOT NULL AND a.qtzd10
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm10,'[',a.qtzd10,']') ELSE '' END),
      (CASE WHEN a.qtzd11 IS NOT NULL AND a.qtzd11
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm11,'[',a.qtzd11,']') ELSE '' END),
      (CASE WHEN a.qtzd12 IS NOT NULL AND a.qtzd12
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm12,'[',a.qtzd12,']') ELSE '' END),
      (CASE WHEN a.qtzd13 IS NOT NULL AND a.qtzd13
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm13,'[',a.qtzd13,']') ELSE '' END),
      (CASE WHEN a.qtzd14 IS NOT NULL AND a.qtzd14
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm14,'[',a.qtzd14,']') ELSE '' END),
      (CASE WHEN a.qtzd15 IS NOT NULL AND a.qtzd15
      &lt;&gt;
      '' THEN CONCAT('|',a.jbdm15,'[',a.qtzd15,']') ELSE '' END)
      ) as otherDiags,
      CONCAT(
      (CASE WHEN a.ssjczmc2 IS NOT NULL AND a.ssjczmc2
      &lt;&gt;
      '' THEN CONCAT(a.ssjczbm2,'[',a.ssjczmc2,']') ELSE '' END),
      (CASE WHEN a.ssjczmc3 IS NOT NULL AND a.ssjczmc3
      &lt;&gt;
      '' THEN CONCAT('|',a.ssjczbm3,'[',a.ssjczmc3,']') ELSE '' END),
      (CASE WHEN a.ssjczmc4 IS NOT NULL AND a.ssjczmc4
      &lt;&gt;
      '' THEN CONCAT('|',a.ssjczbm4,'[',a.ssjczmc4,']') ELSE '' END),
      (CASE WHEN a.ssjczmc5 IS NOT NULL AND a.ssjczmc5
      &lt;&gt;
      '' THEN CONCAT('|',a.ssjczbm5,'[',a.ssjczmc5,']') ELSE '' END),
      (CASE WHEN a.ssjczmc6 IS NOT NULL AND a.ssjczmc6
      &lt;&gt;
      '' THEN CONCAT('|',a.ssjczbm6,'[',a.ssjczmc6,']') ELSE '' END),
      (CASE WHEN a.ssjczmc7 IS NOT NULL AND a.ssjczmc7
      &lt;&gt;
      '' THEN CONCAT('|',a.ssjczbm7,'[',a.ssjczmc7,']') ELSE '' END)
      ) as otherOpers,
      jhz.psn_pay + jhz.acct_pay as zfje,
      b.fztype as fztype
      FROM ba_syjl a
      LEFT JOIN drgdict b ON a.drgbh = b.drgbh left join jsxx_his_zy jhz on a.brid = jhz.brid and a.zyid = jhz.zyid
      where
      ((#{datetype}='cydate' AND cydate&gt;=#{startDate} AND cydate&lt;#{endDate}) OR (#{datetype}='jsdate' AND
      a.his_jsdate&gt;=#{startDate} AND a.his_jsdate&lt;#{endDate}))
      <if test="cblb != null and cblb.size() > 0">
          and ylfkfs in
          <foreach collection="cblb" item="lb" open="(" close=")" separator=",">
              #{lb}
          </foreach>
      </if>
      AND zyzt='0' AND (zfy&lt;&gt;0 AND zfy IS NOT NULL)

      ) a

      where ( cykb = #{ksname} or 1 = if( '所有' = #{ksname}, 1, 0 ) )
      <if test="type != null">
          and blzt LIKE CASE WHEN #{type} ='' THEN '%' ELSE #{type} END
      </if>
      <if test="bah != null">
          and ( bah= #{bah} or 1 = if( '' = #{bah}, 1, 0 ) or 1 = if( '所有' = #{bah}, 1, 0 ))
      </if>


      ORDER BY blzt ,TYPE,bl DESC

  </select>

  <select id="selectKsdrgfx" resultType="ksdrgfxVo" parameterType="ksdrgfxQueryVo">
    SELECT
    cykb kszmc,
    case when #{depttype} ='doctor' then zyys else '' end as ys,
    case when #{showzd} ='1' then concat(jbdm,zyzd ) else '' end as zd,
    case when #{showzd} ='1' then concat(ifnull(ssjczbm1,''),ifnull(ssjczmc1,'') ) else '' end as sscz,
    bs.drgbh AS drgbm,
    dd.drgmc AS drgmc,
    COUNT(*) AS rzbas,
    ROUND( SUM( bs.cmi )/ COUNT(*), 4 ) AS cmi,
    ROUND( SUM( bs.cmi ), 2 ) AS zqz,
    ROUND( AVG( bs.sjzyts/dd.pjdays), 4 ) AS sjxhzs,
    ROUND( AVG( bs.zfy / bs.zfbz), 4 ) AS fyxhzs,
    ROUND( AVG( bs.sjzyts ), 2 ) AS pjzyts,
    ROUND( SUM( bs.zfy ), 4 ) AS zfy,
    ROUND( SUM( drgzf - tczf )/ COUNT(*), 4 ) AS ljyk,
    ROUND( bs.zfbz, 4 ) AS zfbz,
    COUNT( CASE WHEN lyfs = '死亡' OR lyfs = '5' THEN 1 END ) AS swrs,
    COUNT( CASE WHEN (lyfs = '死亡' OR lyfs = '5') AND dfxflag=1 THEN 1 END ) AS dfxswrs,
    ROUND( SUM( IFNULL(bs.xyf,2) + IFNULL(bs.zcyf,2) + IFNULL(bs.zcyf1,2) ), 4 ) AS ypfy,

    ROUND( SUM( IFNULL(bs.ylfuf,2) + IFNULL(bs.zlczf,2) + IFNULL(bs.hlf,2) + IFNULL(bs.qtfy,2) ), 4 ) AS zhfwfy,
    ROUND( SUM( IFNULL(bs.blzdf,2) + IFNULL(bs.syszdf,2) + IFNULL(bs.yxxzdf,2) + IFNULL(bs.lczdxmf,0) ), 4 ) AS
    zdlfy,
    ROUND( SUM( IFNULL(bs.fsszlxmf,2) + IFNULL(bs.wlzlf,2) + IFNULL(bs.sszlf,2)) , 4 ) AS zllfy,
    ROUND( SUM( IFNULL(bs.kff,0) ), 4 ) AS kfl,
    ROUND( SUM( IFNULL(bs.zyzlf,0) ), 4 ) AS zyl,
    ROUND( SUM( IFNULL(bs.xf,0) + IFNULL(bs.bdblzpf,0) + IFNULL(bs.qdblzpf,0) + IFNULL(bs.nxyzlzpf,0) +
    IFNULL(bs.xbyzlzpf,0) ), 4 ) AS xf,
    ROUND( SUM( IFNULL(bs.hcyyclf,0) + IFNULL(bs.yyclf,0) + IFNULL(bs.ycxyyclf,0) ), 4 ) AS cllfy,
    ROUND( SUM( IFNULL(bs.qtf,0) ), 4 ) AS qtfy,
    ROUND( SUM(IFNULL(bs.xyf,0) + IFNULL(bs.kjywf,0) + IFNULL(bs.zcyf,0) + IFNULL(bs.zcyf1,0) + IFNULL(bs.ylfuf,0) +
    IFNULL(bs.zlczf,0) + IFNULL(bs.hlf,0) + IFNULL(bs.qtfy,0) + IFNULL(bs.blzdf,0) + IFNULL(bs.syszdf,0) +
    IFNULL(bs.yxxzdf,0) + IFNULL(bs.lczdxmf,0) + IFNULL(bs.fsszlxmf,0) + IFNULL(bs.wlzlf,0)
    + IFNULL(bs.sszlf,0) + IFNULL(bs.maf,0) + IFNULL(bs.ssf,0) + IFNULL(bs.kff,0) + IFNULL(bs.zyzlf,0) +
    IFNULL(bs.xf,0) + IFNULL(bs.bdblzpf,0) + IFNULL(bs.qdblzpf,0) + IFNULL(bs.nxyzlzpf,0) + IFNULL(bs.xbyzlzpf,0) +
    IFNULL(bs.hcyyclf,0) + IFNULL(bs.yyclf,0) + IFNULL(bs.ycxyyclf,0) + IFNULL(bs.qtf,0) ) , 4) AS fyhj,
    ROUND( SUM( IFNULL(bs.yxxzdf,0) ),4 ) AS `jcfyzdlfybf`,
    ROUND( SUM( IFNULL(bs.syszdf,0) ),4 ) AS `jyfyzdlfybf`,
    ROUND( SUM( IFNULL(bs.xyf,0) + IFNULL(bs.kjywf,0) + IFNULL(bs.zcyf,0) + IFNULL(bs.zcyf1,0) ) / SUM( bs.zfy ), 4
    ) AS yzb,
    ROUND( SUM( IFNULL(bs.hcyyclf,0) + IFNULL(bs.yyclf,0) + IFNULL(bs.ycxyyclf,0) ) / SUM( bs.zfy ), 4 ) AS czb,
    '' AS yxsr,
    ROUND( SUM( drgzf - tczf ), 2 ) AS ycyk,
    ROUND(
    SUM( CASE WHEN bs.blflag = 0 OR bs.blflag IS NULL THEN 1 ELSE 0 END ),
    4
    ) AS zcblrs,
    ROUND( SUM( CASE WHEN bs.blflag = 2 THEN 1 ELSE 0 END ), 4 ) AS gblrs,
    '' AS wrzrs ,
    ROUND( SUM( CASE WHEN bs.blflag = 1 THEN 1 ELSE 0 END ), 4 ) AS dblrs,
    SUM(CASE WHEN sjzyts+0&lt;=2 THEN 1 ELSE 0 END) AS zytsxy2t,
    SUM(CASE WHEN sjzyts+0&gt;60 THEN 1 ELSE 0 END) AS zytsdy60t,
    ROUND(AVG(zfy),2) AS pjfy
    FROM
    ba_syjl bs
    LEFT JOIN drgdict dd ON bs.drgbh = dd.drgbh
    WHERE
    zyzt = '0' AND (bs.zfy IS NOT NULL AND bs.zfy&lt;&gt;0)
    <if test="dept != null and dept != '' and dept != '所有'">
      AND (bs.cykb = #{dept} OR #{dept} = '' OR #{dept} = '所有' OR #{dept} = '%')
    </if>
    <if test="datetype != null and datetype != ''">
      AND ((#{datetype}='cydate' AND cydate&gt;=#{startDate} AND cydate&lt;#{endDate}) OR (#{datetype}='jsdate'
      AND his_jsdate&gt;=#{startDate} AND his_jsdate&lt;#{endDate}))
    </if>
    <if test="cblb != null and cblb.size() > 0">
      and ylfkfs in
      <foreach collection="cblb" item="lb" open="(" close=")" separator=",">
        #{lb}
      </foreach>
    </if>
    AND bs.drgbh IS NOT NULL
    GROUP BY
    bs.cykb,case when #{depttype} ='doctor' then zyys else '' end,dd.drgmc,bs.drgbh,
    case when #{showzd} ='1' then concat(jbdm,zyzd ) else '' end,
    case when #{showzd} ='1' then concat(ifnull(ssjczbm1,''),ifnull(ssjczmc1,'') ) else '' end
  </select>

  <select id="selectYsdrgtj" resultType="ysdrgtjVo">
    SELECT #{startDate} AS adt_from,
    #{endDate} AS adt_to,
    #{datetype} as datetype,
    cykb,
    ys,
    SUM(drgzs) AS drgzs,
    SUM(zbas) AS zbas,
    SUM(rzbas) AS rzbas,
    ROUND(SUM(rzbas) / SUM(zbas) * 100, 2) AS rzl,
    SUM(zqz) AS zqz,
    SUM(cmi) AS cmi,
    SUM(zfy) AS zfy,
    SUM(pjzyr) AS pjzyr,
    SUM(zyk) AS zyk,
    ROUND(SUM(ljyk), 2) AS ljyk,
    max(fyxhzs) as fyxhzs
    FROM (SELECT cykb AS cykb,
    zyys AS ys,
    COUNT(DISTINCT CASE WHEN a.drgbh = '000' OR a.drgbh LIKE '%QY' THEN NULL ELSE a.drgbh END) AS drgzs,
    COUNT(*) AS zbas,
    SUM(rzflag) AS rzbas,
    ROUND(SUM(a.cmi), 2) AS zqz,
    ROUND(SUM(a.cmi) / COUNT(*), 2) AS cmi,
    ROUND(SUM(zfy), 2) AS zfy,
    ROUND(SUM(sjzyts) / COUNT(*), 2) AS pjzyr,
    ROUND(SUM(drgzf - tczf), 2) AS zyk,
    ROUND(SUM(drgzf - tczf), 2) / COUNT(*) AS ljyk,
    round(sum(zfy / a.zfbz), 2) as fyxhzs
    FROM ba_syjl a
    left JOIN drgdict b ON a.drgbh = b.drgbh
    WHERE zyzt = '0'
    AND (a.zfy IS NOT NULL AND a.zfy &lt;&gt; 0)
    <if test="cblb != null and cblb.size() > 0">
      and ylfkfs in
      <foreach collection="cblb" item="lb" open="(" close=")" separator=",">
        #{lb}
      </foreach>
    </if>
    <if test="datetype != null and datetype != ''">
      AND ((#{datetype} = 'cydate' AND cydate &gt;= #{startDate} AND cydate &lt; #{endDate}) OR
      (#{datetype} = 'jsdate' AND his_jsdate &gt;= #{startDate} AND his_jsdate &lt; #{endDate}))
    </if>
    <if test="ksname != null and ksname != '' and ksname != '所有'">
      and (cykb = #{ksname} or 1 = if(#{ksname} = '所有', 1, 0))
    </if>
    GROUP BY cykb, zyys) a
    GROUP BY cykb, ys
    ORDER BY cykb, ROUND(SUM(rzbas) / SUM(zbas) * 100, 2) DESC
  </select>

  <select id="ykjlByMonth" resultType="ykjlVo">
    select bah            as bah,
           zfje           as zfje,
           ylfuf          as ybylfwf,
           zlczf          as ybylczf,
           hlf            as hlfzyf,
           qtfy           as qtfy,
           blzdf          as blzdf,
           syszdf         as syszdf,
           yxxzdf         as yxxzdf,
           lczdxmf        as lczdxmf,
           fsszlxmf       as fsszlxmf,
           wlzlf          as lcwlzlf,
           sszlf          as sszlf,
           maf            as mzf,
           ssf            as ssf,
           kff            as kff,
           zyzlf          as zyzlf,
           xyf            as xyf,
           kjywf          as kjywf,
           zcyf           as zcyf,
           zcyf1          as zcyf1,
           xf             as xf,
           bdblzpf        as bdblzpf,
           qdblzpf        as qdblzpf,
           nxyzlzpf       as nxyzlzpf,
           xbyzlzpf       as xbyzlzpf,
           hcyyclf        as jcyycxyyclf,
           yyclf          as zcyycxyyclf,
           ycxyyclf       as ssyycxyyclf,
           qtf            as qtf,
           tczf           as tczf,
           drgzf          as drgzf,
           ylfkfs         as ylfkfs,
           zfy            as zfy
    from ba_syjl
    where bah = #{bah}
  </select>

  <select id="bafxYs" resultType="bafxYsVo">
    select bah,
    brbs,
    xm as xm,
    rysj as rysj,
    cykb as cykb,
    zyzd as zyzd,
    cysj as cysj,
    ssjczmc1 as ssjczmc1,
    zyys as zyys,
    IFNULL(zfy, 0) as zfy,
    jsdate as jsdate,
    a.drgbh as drgbh,
    sjzyts as sjzyts,
    a.zfbz,
    round(zfy / a.zfbz, 4) as bgwcbl,
    b.drgmc as drgmc,
    ROUND((IFNULL(xyf, 0) + IFNULL(zcyf, 0) + IFNULL(zcyf1, 0) + IFNULL(kjywf, 0)) / IFNULL(zfy, 0), 2) as ybbl,
    ROUND((IFNULL(hcyyclf, 0) + IFNULL(yyclf, 0) + IFNULL(ycxyyclf, 0)) / IFNULL(zfy, 0), 2) as hcbl,
    ROUND(IFNULL(drgzf, 0) - IFNULL(tczf, 0), 2) as ykje,
    round(sjzyts / pjdays, 2) as sjxhzs
    from ba_syjl a
    left join drgdict b on a.drgbh = b.drgbh
    where (zfy IS NOT NULL AND zfy &lt;&gt; 0)
    <if test="datetype != null and datetype != ''">
      case when ${datetype} = 'cydate' then cydate else his_jsdate end &gt;= ${startDate}
      and case when ${datetype} = 'cydate' then cydate else his_jsdate end &lt; ${endDate}
    </if>
    <if test="zyys != null and zyys != ''">
      zyys = #{zyys}
    </if>
    and zyzt = '0'
    <if test="cykb != null and cykb != ''">
      cykb = #{ks}
    </if>
  </select>

  <select id="ksdrgtj" resultType="ksdrgtjVo">
    SELECT #{startDate} AS adt_from,
    #{endDate} AS adt_to,
    #{datetype} AS datetype,
    cykb,
    SUM(drgzs) AS drgzs,
    SUM(zbas) AS zbas,
    SUM(zgbas) AS zgbas,
    SUM(jmbas) AS jmbas,
    SUM(rzbas) AS rzbas,
    ROUND(SUM(rzbas) / SUM(zbas) * 100, 2) AS rzl,
    SUM(zqz) AS zqz,
    SUM(cmi) AS cmi,
    SUM(zfy) AS zfy,
    SUM(pjzyr) AS pjzyr,
    SUM(zyk) AS zyk,
    ROUND(SUM(ljyk), 2) AS ljyk,
    fyxhzs,
    sjxhzs
    FROM (SELECT cykb AS cykb,
    COUNT(DISTINCT CASE
    WHEN a.drgbh = '000' OR a.drgbh LIKE '%QY' OR a.drgbh IS NULL THEN NULL
    ELSE a.drgbh END) AS drgzs,
    COUNT(*) AS zbas,
    SUM(CASE WHEN ylfkfs LIKE '%职工%' THEN 1 ELSE 0 END) AS zgbas,
    SUM(CASE WHEN ylfkfs LIKE '%居民%' THEN 1 ELSE 0 END) AS jmbas,
    SUM(rzflag) AS rzbas,
    ROUND(SUM(a.cmi), 2) AS zqz,
    ROUND(SUM(a.cmi) / COUNT(*), 2) AS cmi,
    ROUND(SUM(a.zfy), 2) AS zfy,
    ROUND(SUM(a.sjzyts) / COUNT(*), 2) AS pjzyr,
    ROUND(SUM(drgzf - tczf), 2) AS zyk,
    ROUND(SUM(drgzf - tczf), 2) / COUNT(*) AS ljyk,
    round(sum(a.zfy) / sum(a.zfbz), 2) as fyxhzs,
    round(sum(a.sjzyts) / sum(b.pjdays), 2) as sjxhzs
    FROM ba_syjl a
    LEFT JOIN drgdict b ON a.drgbh = b.drgbh
    WHERE zyzt = '0'
    AND (a.zfy IS NOT NULL AND a.zfy &lt;&gt; 0)
    <if test="cblb != null and cblb.size() > 0">
      and ylfkfs in
      <foreach collection="cblb" item="lb" close=")" open="(" separator=",">
        #{lb}
      </foreach>
    </if>
    <if test="datetype != null and datetype != ''">
      AND ((#{datetype} = 'cydate' AND cydate &gt;= #{startDate} AND cydate &lt; #{endDate}) OR
      (#{datetype} = 'jsdate' AND his_jsdate &gt;= #{startDate} AND his_jsdate &lt; #{endDate}))
    </if>
    <if test="ksname != null and ksname != ''">
      and (cykb = #{ksname} or #{ksname} = '所有' or #{ksname} = '' or #{ksname} = '%')
    </if>
    AND a.drgbh IS NOT NULL
    GROUP BY cykb) a
    GROUP BY cykb
    ORDER BY ROUND(SUM(rzbas) / SUM(zbas) * 100, 2)
  </select>


  <select id="selectDeptYxzb" resultMap="DeptYxzbQueryVoResult" parameterType="DeptYxzbQueryVo">
    SELECT
    cykb,
    ROUND(IFNULL(SUM(a.cmi),0),2) AS zfqz,
    ROUND(IFNULL(AVG(a.sjzyts),0),2) AS pjdays,
    ROUND(IFNULL(AVG(a.sjzyts/b.pjdays),0),2) AS sjxhzs,
    ROUND(IFNULL(AVG(a.zfy/a.zfbz),0),2) AS fyxhzs,
    ROUND(SUM(IFNULL(a.drgzf,0) -IFNULL(a.tczf,0)) / 10000,2) AS zyk,
    ROUND(AVG(a.cmi),2) AS ljzfqz
    FROM ba_syjl a
      JOIN drgdict b ON a.drgbh = b.drgbh
      LEFT JOIN jsxx_his_zy c ON a.brid = c.brid AND a.zyid = c.zyid
    WHERE IFNULL(a.cykb,'') != ''
    AND zyzt = '0'
    AND (a.zfy IS NOT NULL AND a.zfy != 0)
    AND (a.drgbh NOT LIKE '%QY%' AND a.drgbh NOT LIKE '%00%' AND a.drgbh IS NOT NULL) AND a.rzflag = '1'
    <if test="cblb != null and cblb.size() > 0">
      and ylfkfs in
      <foreach collection="cblb" item="lb" close=")" open="(" separator=",">
        #{lb}
      </foreach>
    </if>
    <if test="datetype == 'cydate'">
      <if test="startDate != null">
        and cydate >= #{startDate}
      </if>
      <if test="endDate != null">
        and cydate &lt; #{endDate}
      </if>
    </if>
    <if test="datetype == 'jsdate'">
      <if test="startDate != null">
        and c.his_jsdate >= #{startDate}
      </if>
      <if test="endDate != null">
        and c.his_jsdate &lt; #{endDate}
      </if>
    </if>
    GROUP BY cykb;
  </select>

    <select id="getQyyxzb" resultType="com.ruoyi.system.domain.wordReport.Qyyxzb">
      SELECT COUNT(DISTINCT CASE WHEN rzflag = 1 THEN a.drgbh ELSE NULL END) AS drgzs
      ,
      ROUND(SUM(zfqz), 2)                                             AS zqz,
      COUNT(DISTINCT mdcbh)                                           AS mdczs,
      ROUND(AVG(zfqz), 2)                                             AS pjqz,
      COUNT(DISTINCT jbdm)                                            AS jbdmsl,
      COUNT(DISTINCT ssjczbm1)                                        AS sssl,
      COUNT(DISTINCT adrgbh)                                          AS adrgsl,
      COUNT(brbs)                                                     AS brs,
      count(brid) as rs,
      ROUND(AVG(zfy / zfbz), 2)                                       AS fyxhzs,
      ROUND(AVG(sjzyts / pjdays), 2)                                  AS sjxhzs,
      ROUND(SUM(dfxsw) * 100 / SUM(dfxrs), 2)                         AS dfxswl,
      SUM(rzflag)                                                     AS rzls,
      SUM(rzflag) / COUNT(*)                                          AS rzl,
      SUM(zfy)                                                        AS zfy,
      SUM(ykje)                                                       AS ykje,
      ROUND(AVG(zfy), 2)                                              AS cjfy,
      ROUND(AVG(sjzyts), 2)                                           AS brpjdays,

      IFNULL(ROUND(SUM(ypf) / SUM(zfy), 2), 0)                        AS yzb,
      IFNULL(ROUND(SUM(hcf) / SUM(zfy), 2), 0)                        AS czb,
      cblb,
      datetype,
      ROUND(IFNULL(SUM(zfje) / SUM(zfy), 0), 4) AS zfl,
      ROUND(IFNULL(SUM(zfy) / SUM(zfqz), 0), 2) AS mqzfy,
      ROUND(COUNT(DISTINCT mdtrt_id)/COUNT(DISTINCT psn_no),4) AS rcrtb

      FROM (SELECT a.deptname                     cykb,
      a.doctorname                AS zyys,
      a.drgbh                     AS drgbh,
      a.rzflag,
      a.zfqz,
      b.mdcbh,
      a.dise_name                 AS jbdm,
      a.ssxx                      AS ssjczbm1,
      b.adrgbh,
      CONCAT(a.brid, '_', a.zyid) AS brbs,
      a.brid as brid,
      a.medfee_sumamt             AS zfy,
      a.sjzyts,
      a.zfbz,
      b.pjdays,
      CASE
      WHEN a.dfxflag = 1
      AND a.lyfs = '死亡' OR a.lyfs = '4' THEN 1
      ELSE 0 END                 dfxsw,
      CASE
      WHEN a.dfxflag = 1
      THEN 1
      ELSE 0 END              AS dfxrs,
      a.ypf                       AS ypf,
      a.hcf                       AS hcf,
      a.drgzf - a.tczf            AS ykje,
      '所有'                      AS cblb,
      'jsdate'                    AS datetype,
      mdtrt_id,
      psn_no,
      a.fulamt_ownpay_amt AS zfje
      FROM jsxx_his_zy a
      left JOIN drgdict b ON a.drgbh = b.drgbh

      WHERE (#{jgid} = 'admin' or a.fixmedins_code = #{jgid})
      AND ((a.his_jsdate &gt;= #{startDate} AND a.his_jsdate &lt;= #{endDate}))
      AND IFNULL(a.clr_way, '') LIKE CONCAT('40', '%')) a
    </select>
  <select id="getKsyxqk" resultType="com.ruoyi.system.domain.wordReport.Ksyxqk">
    call usp_drg_drgkstj(#{startDate}, #{endDate}, 'jsdate', '%', '%'  , '40', '否', '2024-01-01 00:00:00', '2025-01-01 00:00:00',#{jgid})
  </select>
  <select id="getYsyxqk" resultType="com.ruoyi.system.domain.wordReport.Ysyxqk">
    call usp_get_drgystj(#{startDate}, #{endDate}, 'jsdate', '%', '所有'  , '40', '否', '2024-01-01 00:00:00', '2025-01-01 00:00:00',#{jgid})
  </select>
  <select id="getKsblqk" resultType="com.ruoyi.system.domain.wordReport.Ksblqk">
    SELECT *
    FROM (SELECT '0'                                                                                                  AS TYPE,
    a.bah,
    a.psn_name                                                                                           AS xm,
    a.gend                                                                                               AS xb,
    CASE
    WHEN insutype = '310' THEN '职工'
    WHEN insutype = '390' THEN '居民'
    WHEN insutype = '3101' THEN '异地职工'
    WHEN insutype = '3901'
    THEN '异地居民' END                                                                          AS ylfkfs,
    a.age                                                                                                AS nl,
    a.deptname                                                                                           AS cykb,
    a.doctorname                                                                                         AS zyys,
    a.enddate                                                                                            AS cydate,
    begndate                                                                                             AS rydate,
    a.his_jsdate,
    a.drgmc,
    a.drgbh,
    a.zfbz,
    a.medfee_sumamt                                                                                      AS zfy,
    CASE
    WHEN (a.drgbh LIKE '%QY%' OR a.drgbh LIKE '%00%' OR a.drgbh IS NULL) THEN 1
    ELSE ROUND(a.medfee_sumamt /
    CASE WHEN (a.zfbz = 0 OR a.zfbz IS NULL) THEN a.medfee_sumamt ELSE a.zfbz END, 4) END AS bl,
    CASE
    WHEN a.blflag = 3 THEN '03主诊不存在'
    WHEN a.blflag = 4 THEN '04歧义组'
    ELSE
    CASE
    WHEN a.blflag = 2 THEN '01费用极高'
    WHEN a.blflag = 1 THEN '02费用极低'
    ELSE '05正常倍率' END END                                                                AS blzt,
    a.sjzyts,
    ROUND(a.drgzf - a.tczf, 2)                                                                           AS ykje,
    CASE
    WHEN ROUND(a.drgzf - a.tczf, 2) &gt; 0 THEN '是'
    WHEN ROUND(a.drgzf - a.tczf, 2) &lt; 0 THEN '否'
    WHEN ROUND(a.drgzf - a.tczf, 2) = 0
    THEN '平' END                                                                                AS ykflag,
    a.setl_id,
    clr_way,
    IFNULL(a.maf_pay, 0) + IFNULL(a.oth_pay, 0) + IFNULL(a.hifes_pay, 0) +
    IFNULL(cvlserv_pay, 0)                                                                               AS qtzf,
    IFNULL(a.maf_pay, 0)                                                                                 AS 医疗救助基金支出,
    IFNULL(a.oth_pay, 0)                                                                                 AS 其他支出,
    IFNULL(a.hifes_pay, 0)                                                                               AS 企业支出,
    IFNULL(cvlserv_pay, 0)                                                                               AS 公务员,
    a.dise_name                                                                                          AS jbdm,
    a.ssxx                                                                                               AS ssjczbm1,
    a.ypf                                                                                                AS ypf,
    b.ypf_bg,
    a.jyf                                                                                                AS jyf,
    b.jyf_bg,
    a.jcf                                                                                                   jcf,
    b.jcf_bg,
    0                                                                                                    AS ssf,
    b.ssf_bg,
    a.zlf                                                                                                AS zlf,
    b.zlf_bg,
    a.hcf                                                                                                AS hcf,
    b.hcf_bg,
    a.tczf,
    a.drgzf,
    IFNULL(a.cash_payamt, 0) + IFNULL(a.acct_pay, 0)                                                     AS grzf,
    b.fztype,
    CONCAT(a.brid, '_', zyid)                                                                            AS brbs,
    b.pjdays,
    0                                                                                                    AS drgfzsl,
    CASE WHEN a.dfxflag = '1' THEN '是' ELSE '否' END                                                    AS dfxflag,
    a.zfqz                                                                                               AS cmi,
    a.lyfs,
    a.certno                                                                                             AS sfzh,
    ''                                                                                                   AS otherDiags,
    ''                                                                                                   AS otherOpers
    FROM jsxx_his_zy a
    LEFT JOIN drgdict b ON a.drgbh = b.drgbh
    WHERE  (fixmedins_code = #{jgid} OR 'admin' = #{jgid})
    and clr_way = '40'
    AND (a.his_jsdate &gt;= #{startDate} AND a.his_jsdate &lt;= #{endDate})) a
    where ykje is not null and ykje &lt; 0
    ORDER BY ykje, blzt, TYPE, bl DESC
    limit 10
  </select>
  <select id="getBzqk" resultType="com.ruoyi.system.domain.wordReport.Bzqk">
    select a.drgbh,
           b.drgmc,
           count(setl_id)                      as bls,
           sum(medfee_sumamt) / count(setl_id) as ljfy,
           avg(sjzyts)                         as pjzyts,
           sum(medfee_sumamt)                  as zfy,
           sum(drgzf - tczf)                   as ykje,
           max(a.zfbz)                         as zfbz,
           max(b.pjdays)                       as pjdays
    from jsxx_his_zy a
           join drgdict b on a.drgbh = b.drgbh
    where clr_way = '40'
    and his_jsdate &gt;= #{startDate} and his_jsdate &lt;= #{endDate}
    and (#{jgid} = 'admin' or fixmedins_code = #{jgid})
    group by a.drgbh
    order by ykje
  </select>
  <select id="diagDiffDetails" parameterType="baZbfx" resultType="com.ruoyi.system.domain.vo.diagDiffDetailsRes">
    SELECT a.xm, a.bah, a.brid, a.zyid, a.brbs, a.cykb, a.zyys,
    yszd.zdname AS drDiagName, yszd.zdcode AS drDiagCode,
    bazd.zdname AS mrDiagName, bazd.zdcode AS mrDiagCode
    FROM ba_syjl a
    LEFT JOIN zdxx yszd ON a.brbs = yszd.jzh AND
    yszd.zdsort = 1 AND
    yszd.jlly = '3' AND
    (yszd.zdtype = 3 or yszd.zdtype = '出院诊断')
    LEFT JOIN zdxx bazd ON a.brbs = bazd.jzh AND
    bazd.zdsort = 1 AND
    bazd.jlly = '4' AND
    (bazd.zdtype = 3 or bazd.zdtype = '出院诊断')
    <where>
      <choose>
        <when test="datetype == 'cydate'">
          and a.cydate &gt;= #{adtFrom} and a.cydate &lt;= #{adtTo}
        </when>
        <when test="datetype == 'jsdate'">
          and a.his_jsdate &gt;= #{adtFrom} and a.his_jsdate &lt;= #{adtTo}
        </when>
      </choose>
      <if test="cykb != null and cykb != ''">and a.cykb = #{cykb}</if>
      <if test="cblb != '' and cblb != null">and a.ylfkfs = #{cblb}</if>
    </where>
  </select>


</mapper>
