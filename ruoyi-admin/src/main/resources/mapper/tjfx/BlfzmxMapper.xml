<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BlfzmxMapper">

    <resultMap type="Blfzmx" id="BlfzmxResult">
        <result property="scdate"    column="scdate"    />
        <result property="yljg"    column="yljg"    />
        <result property="jslsh"    column="jslsh"    />
        <result property="xm"    column="xm"    />
        <result property="bah"    column="bah"    />
        <result property="zycs"    column="zycs"    />
        <result property="xb"    column="xb"    />
        <result property="nl"    column="nl"    />
        <result property="bzyzsnl"    column="bzyzsnl"    />
        <result property="xsecstz"    column="xsecstz"    />
        <result property="xserytz"    column="xserytz"    />
        <result property="csrq"    column="csrq"    />
        <result property="rytj"    column="rytj"    />
        <result property="rysj"    column="rysj"    />
        <result property="rykb"    column="rykb"    />
        <result property="cysj"    column="cysj"    />
        <result property="cykb"    column="cykb"    />
        <result property="sjzyts"    column="sjzyts"    />
        <result property="lyfs"    column="lyfs"    />
        <result property="zfy"    column="zfy"    />
        <result property="drgbh"    column="drgbh"    />
        <result property="drgmc"    column="drgmc"    />
        <result property="drgqz"    column="drgqz"    />
        <result property="jbdm"    column="jbdm"    />
        <result property="zyzd"    column="zyzd"    />
        <result property="jbdm1"    column="jbdm1"    />
        <result property="qtzd1"    column="qtzd1"    />
        <result property="sfzl1"    column="sfzl1"    />
        <result property="jbdm2"    column="jbdm2"    />
        <result property="qtzd2"    column="qtzd2"    />
        <result property="sfzl2"    column="sfzl2"    />
        <result property="jbdm3"    column="jbdm3"    />
        <result property="qtzd3"    column="qtzd3"    />
        <result property="sfzl3"    column="sfzl3"    />
        <result property="jbdm4"    column="jbdm4"    />
        <result property="qtzd4"    column="qtzd4"    />
        <result property="sfzl4"    column="sfzl4"    />
        <result property="jbdm5"    column="jbdm5"    />
        <result property="qtzd5"    column="qtzd5"    />
        <result property="sfzl5"    column="sfzl5"    />
        <result property="jbdm6"    column="jbdm6"    />
        <result property="qtzd6"    column="qtzd6"    />
        <result property="sfzl6"    column="sfzl6"    />
        <result property="jbdm7"    column="jbdm7"    />
        <result property="qtzd7"    column="qtzd7"    />
        <result property="sfzl7"    column="sfzl7"    />
        <result property="jbdm8"    column="jbdm8"    />
        <result property="qtzd8"    column="qtzd8"    />
        <result property="sfzl8"    column="sfzl8"    />
        <result property="jbdm9"    column="jbdm9"    />
        <result property="qtzd9"    column="qtzd9"    />
        <result property="sfzl9"    column="sfzl9"    />
        <result property="jbdm10"    column="jbdm10"    />
        <result property="qtzd10"    column="qtzd10"    />
        <result property="sfzl10"    column="sfzl10"    />
        <result property="jbdm11"    column="jbdm11"    />
        <result property="qtzd11"    column="qtzd11"    />
        <result property="sfzl11"    column="sfzl11"    />
        <result property="jbdm12"    column="jbdm12"    />
        <result property="qtzd12"    column="qtzd12"    />
        <result property="sfzl12"    column="sfzl12"    />
        <result property="jbdm13"    column="jbdm13"    />
        <result property="qtzd13"    column="qtzd13"    />
        <result property="sfzl13"    column="sfzl13"    />
        <result property="jbdm14"    column="jbdm14"    />
        <result property="qtzd14"    column="qtzd14"    />
        <result property="sfzl14"    column="sfzl14"    />
        <result property="jbdm15"    column="jbdm15"    />
        <result property="qtzd15"    column="qtzd15"    />
        <result property="sfzl15"    column="sfzl15"    />
        <result property="jsdate"    column="jsdate"    />
        <result property="ssjczbm1"    column="ssjczbm1"    />
        <result property="ssjczmc1"    column="ssjczmc1"    />
        <result property="ssjczbm2"    column="ssjczbm2"    />
        <result property="ssjczmc2"    column="ssjczmc2"    />
        <result property="ssjczbm3"    column="ssjczbm3"    />
        <result property="ssjczmc3"    column="ssjczmc3"    />
        <result property="ssjczbm4"    column="ssjczbm4"    />
        <result property="ssjczmc4"    column="ssjczmc4"    />
        <result property="ssjczbm5"    column="ssjczbm5"    />
        <result property="ssjczmc5"    column="ssjczmc5"    />
        <result property="ssjczbm6"    column="ssjczbm6"    />
        <result property="ssjczmc6"    column="ssjczmc6"    />
        <result property="ssjczbm7"    column="ssjczbm7"    />
        <result property="ssjczmc7"    column="ssjczmc7"    />
        <result property="ssjczbm8"    column="ssjczbm8"    />
        <result property="ssjczmc8"    column="ssjczmc8"    />
        <result property="drgrzlb"    column="drgrzlb"    />
        <result property="jstype"    column="jstype"    />
        <result property="jsfs"    column="jsfs"    />
        <result property="zyzfy"    column="zyzfy"    />
        <result property="tczf"    column="tczf"    />
        <result property="drgzfbz"    column="drgzfbz"    />
        <result property="drgybf"    column="drgybf"    />
        <result property="cyksbm"    column="cyksbm"    />
        <result property="cyksmc"    column="cyksmc"    />
        <result property="csdrgbh"    column="csdrgbh"    />
        <result property="csdrgmc"    column="csdrgmc"    />
      <result property="kydrgmc"    column="kydrgmc"    />
      <result property="kydrgbh"    column="kydrgbh"    />
        <result property="sfqf"    column="sfqf"    />
    </resultMap>

    <sql id="selectBlfzmxVo">
        select kydrgbh,kydrgmc,csdrgbh,csdrgmc,scdate, yljg, jslsh, xm, bah, zycs, xb, nl, bzyzsnl, xsecstz, xserytz, csrq, rytj, rysj, rykb, cysj, cykb, sjzyts, lyfs, zfy, drgbh, drgmc, drgqz, jbdm, zyzd, jbdm1, qtzd1, sfzl1, jbdm2, qtzd2, sfzl2, jbdm3, qtzd3, sfzl3, jbdm4, qtzd4, sfzl4, jbdm5, qtzd5, sfzl5, jbdm6, qtzd6, sfzl6, jbdm7, qtzd7, sfzl7, jbdm8, qtzd8, sfzl8, jbdm9, qtzd9, sfzl9, jbdm10, qtzd10, sfzl10, jbdm11, qtzd11, sfzl11, jbdm12, qtzd12, sfzl12, jbdm13, qtzd13, sfzl13, jbdm14, qtzd14, sfzl14, jbdm15, qtzd15, sfzl15, jsdate, ssjczbm1, ssjczmc1, ssjczbm2, ssjczmc2, ssjczbm3, ssjczmc3, ssjczbm4, ssjczmc4, ssjczbm5, ssjczmc5, ssjczbm6, ssjczmc6, ssjczbm7, ssjczmc7, ssjczbm8, ssjczmc8, drgrzlb, jstype, jsfs, zyzfy, tczf, drgzfbz, drgybf, cyksbm, cyksmc from blfzmx
    </sql>

    <select id="selectBlfzmxList" parameterType="Blfzmx" resultMap="BlfzmxResult">
        <include refid="selectBlfzmxVo"/>
        <where>
            
            <if test="params.beginScdate != null and params.beginScdate != '' and params.endScdate != null and params.endScdate != ''"> and scdate between #{params.beginScdate} and #{params.endScdate}</if>
            <if test="yljg != null  and yljg != ''"> and yljg = #{yljg}</if>
            <if test="jslsh != null  and jslsh != ''"> and jslsh = #{jslsh}</if>
            <if test="xm != null  and xm != ''"> and xm = #{xm}</if>
            <if test="bah != null  and bah != ''"> and bah = #{bah}</if>
            <if test="zycs != null  and zycs != ''"> and zycs = #{zycs}</if>
            <if test="xb != null  and xb != ''"> and xb = #{xb}</if>
            <if test="nl != null "> and nl = #{nl}</if>
            <if test="bzyzsnl != null "> and bzyzsnl = #{bzyzsnl}</if>
            <if test="xsecstz != null "> and xsecstz = #{xsecstz}</if>
            <if test="xserytz != null "> and xserytz = #{xserytz}</if>
            <if test="csrq != null  and csrq != ''"> and csrq = #{csrq}</if>
            <if test="rytj != null  and rytj != ''"> and rytj = #{rytj}</if>
            <if test="rysj != null  and rysj != ''"> and rysj = #{rysj}</if>
            <if test="rykb != null  and rykb != ''"> and rykb = #{rykb}</if>
            <if test="cysj != null  and cysj != ''"> and cysj = #{cysj}</if>
            <if test="cykb != null  and cykb != ''"> and cykb = #{cykb}</if>
            <if test="sjzyts != null "> and sjzyts = #{sjzyts}</if>
            <if test="lyfs != null  and lyfs != ''"> and lyfs = #{lyfs}</if>
            <if test="zfy != null "> and zfy = #{zfy}</if>
            <if test="drgbh != null  and drgbh != ''"> and drgbh = #{drgbh}</if>
            <if test="drgmc != null  and drgmc != ''"> and drgmc = #{drgmc}</if>
            <if test="drgqz != null  and drgqz != ''"> and drgqz = #{drgqz}</if>
            <if test="jbdm != null  and jbdm != ''"> and jbdm = #{jbdm}</if>
            <if test="zyzd != null  and zyzd != ''"> and zyzd = #{zyzd}</if>
            <if test="jbdm1 != null  and jbdm1 != ''"> and jbdm1 = #{jbdm1}</if>
            <if test="qtzd1 != null  and qtzd1 != ''"> and qtzd1 = #{qtzd1}</if>
            <if test="sfzl1 != null  and sfzl1 != ''"> and sfzl1 = #{sfzl1}</if>
            <if test="jbdm2 != null  and jbdm2 != ''"> and jbdm2 = #{jbdm2}</if>
            <if test="qtzd2 != null  and qtzd2 != ''"> and qtzd2 = #{qtzd2}</if>
            <if test="sfzl2 != null  and sfzl2 != ''"> and sfzl2 = #{sfzl2}</if>
            <if test="jbdm3 != null  and jbdm3 != ''"> and jbdm3 = #{jbdm3}</if>
            <if test="qtzd3 != null  and qtzd3 != ''"> and qtzd3 = #{qtzd3}</if>
            <if test="sfzl3 != null  and sfzl3 != ''"> and sfzl3 = #{sfzl3}</if>
            <if test="jbdm4 != null  and jbdm4 != ''"> and jbdm4 = #{jbdm4}</if>
            <if test="qtzd4 != null  and qtzd4 != ''"> and qtzd4 = #{qtzd4}</if>
            <if test="sfzl4 != null  and sfzl4 != ''"> and sfzl4 = #{sfzl4}</if>
            <if test="jbdm5 != null  and jbdm5 != ''"> and jbdm5 = #{jbdm5}</if>
            <if test="qtzd5 != null  and qtzd5 != ''"> and qtzd5 = #{qtzd5}</if>
            <if test="sfzl5 != null  and sfzl5 != ''"> and sfzl5 = #{sfzl5}</if>
            <if test="jbdm6 != null  and jbdm6 != ''"> and jbdm6 = #{jbdm6}</if>
            <if test="qtzd6 != null  and qtzd6 != ''"> and qtzd6 = #{qtzd6}</if>
            <if test="sfzl6 != null  and sfzl6 != ''"> and sfzl6 = #{sfzl6}</if>
            <if test="jbdm7 != null  and jbdm7 != ''"> and jbdm7 = #{jbdm7}</if>
            <if test="qtzd7 != null  and qtzd7 != ''"> and qtzd7 = #{qtzd7}</if>
            <if test="sfzl7 != null  and sfzl7 != ''"> and sfzl7 = #{sfzl7}</if>
            <if test="jbdm8 != null  and jbdm8 != ''"> and jbdm8 = #{jbdm8}</if>
            <if test="qtzd8 != null  and qtzd8 != ''"> and qtzd8 = #{qtzd8}</if>
            <if test="sfzl8 != null  and sfzl8 != ''"> and sfzl8 = #{sfzl8}</if>
            <if test="jbdm9 != null  and jbdm9 != ''"> and jbdm9 = #{jbdm9}</if>
            <if test="qtzd9 != null  and qtzd9 != ''"> and qtzd9 = #{qtzd9}</if>
            <if test="sfzl9 != null  and sfzl9 != ''"> and sfzl9 = #{sfzl9}</if>
            <if test="jbdm10 != null  and jbdm10 != ''"> and jbdm10 = #{jbdm10}</if>
            <if test="qtzd10 != null  and qtzd10 != ''"> and qtzd10 = #{qtzd10}</if>
            <if test="sfzl10 != null  and sfzl10 != ''"> and sfzl10 = #{sfzl10}</if>
            <if test="jbdm11 != null  and jbdm11 != ''"> and jbdm11 = #{jbdm11}</if>
            <if test="qtzd11 != null  and qtzd11 != ''"> and qtzd11 = #{qtzd11}</if>
            <if test="sfzl11 != null  and sfzl11 != ''"> and sfzl11 = #{sfzl11}</if>
            <if test="jbdm12 != null  and jbdm12 != ''"> and jbdm12 = #{jbdm12}</if>
            <if test="qtzd12 != null  and qtzd12 != ''"> and qtzd12 = #{qtzd12}</if>
            <if test="sfzl12 != null  and sfzl12 != ''"> and sfzl12 = #{sfzl12}</if>
            <if test="jbdm13 != null  and jbdm13 != ''"> and jbdm13 = #{jbdm13}</if>
            <if test="qtzd13 != null  and qtzd13 != ''"> and qtzd13 = #{qtzd13}</if>
            <if test="sfzl13 != null  and sfzl13 != ''"> and sfzl13 = #{sfzl13}</if>
            <if test="jbdm14 != null  and jbdm14 != ''"> and jbdm14 = #{jbdm14}</if>
            <if test="qtzd14 != null  and qtzd14 != ''"> and qtzd14 = #{qtzd14}</if>
            <if test="sfzl14 != null  and sfzl14 != ''"> and sfzl14 = #{sfzl14}</if>
            <if test="jbdm15 != null  and jbdm15 != ''"> and jbdm15 = #{jbdm15}</if>
            <if test="qtzd15 != null  and qtzd15 != ''"> and qtzd15 = #{qtzd15}</if>
            <if test="sfzl15 != null  and sfzl15 != ''"> and sfzl15 = #{sfzl15}</if>
            <if test="jsdate != null "> and jsdate = #{jsdate}</if>
            <if test="ssjczbm1 != null  and ssjczbm1 != ''"> and ssjczbm1 = #{ssjczbm1}</if>
            <if test="ssjczmc1 != null  and ssjczmc1 != ''"> and ssjczmc1 = #{ssjczmc1}</if>
            <if test="ssjczbm2 != null  and ssjczbm2 != ''"> and ssjczbm2 = #{ssjczbm2}</if>
            <if test="ssjczmc2 != null  and ssjczmc2 != ''"> and ssjczmc2 = #{ssjczmc2}</if>
            <if test="ssjczbm3 != null  and ssjczbm3 != ''"> and ssjczbm3 = #{ssjczbm3}</if>
            <if test="ssjczmc3 != null  and ssjczmc3 != ''"> and ssjczmc3 = #{ssjczmc3}</if>
            <if test="ssjczbm4 != null  and ssjczbm4 != ''"> and ssjczbm4 = #{ssjczbm4}</if>
            <if test="ssjczmc4 != null  and ssjczmc4 != ''"> and ssjczmc4 = #{ssjczmc4}</if>
            <if test="ssjczbm5 != null  and ssjczbm5 != ''"> and ssjczbm5 = #{ssjczbm5}</if>
            <if test="ssjczmc5 != null  and ssjczmc5 != ''"> and ssjczmc5 = #{ssjczmc5}</if>
            <if test="ssjczbm6 != null  and ssjczbm6 != ''"> and ssjczbm6 = #{ssjczbm6}</if>
            <if test="ssjczmc6 != null  and ssjczmc6 != ''"> and ssjczmc6 = #{ssjczmc6}</if>
            <if test="ssjczbm7 != null  and ssjczbm7 != ''"> and ssjczbm7 = #{ssjczbm7}</if>
            <if test="ssjczmc7 != null  and ssjczmc7 != ''"> and ssjczmc7 = #{ssjczmc7}</if>
            <if test="ssjczbm8 != null  and ssjczbm8 != ''"> and ssjczbm8 = #{ssjczbm8}</if>
            <if test="ssjczmc8 != null  and ssjczmc8 != ''"> and ssjczmc8 = #{ssjczmc8}</if>
            <if test="drgrzlb != null  and drgrzlb != ''"> and drgrzlb = #{drgrzlb}</if>
            <if test="jstype != null  and jstype != ''"> and jstype = #{jstype}</if>
            <if test="jsfs != null  and jsfs != ''"> and jsfs = #{jsfs}</if>
            <if test="zyzfy != null  and zyzfy != ''"> and zyzfy = #{zyzfy}</if>
            <if test="tczf != null  and tczf != ''"> and tczf = #{tczf}</if>
            <if test="drgzfbz != null  and drgzfbz != ''"> and drgzfbz = #{drgzfbz}</if>
            <if test="drgybf != null  and drgybf != ''"> and drgybf = #{drgybf}</if>
            <if test="cyksbm != null  and cyksbm != ''"> and cyksbm = #{cyksbm}</if>
            <if test="cyksmc != null  and cyksmc != ''"> and cyksmc = #{cyksmc}</if>
            <if test="csdrgbh != null  and csdrgbh != ''"> and csdrgbh = #{csdrgbh}</if>
            <if test="csdrgmc != null  and csdrgmc != ''"> and csdrgmc = #{csdrgmc}</if>
          <if test="kydrgbh != null  and kydrgbh != ''"> and kydrgbh = #{kydrgbh}</if>
          <if test="kydrgmc != null  and kydrgmc != ''"> and kydrgmc = #{kydrgmc}</if>
            <if test="sfqf != null  and sfqf != ''"> and case when #{sfqf} = '1' then csdrgbh != drgbh when #{sfqf} = '0' then kydrgbh != drgbh else 1=1 end</if>
        </where>
    </select>

    <select id="selectBlfzmxByScdate" parameterType="Date" resultMap="BlfzmxResult">
        <include refid="selectBlfzmxVo"/>
        where scdate = #{scdate}
    </select>

    <insert id="insertBlfzmx" parameterType="Blfzmx">
        insert into blfzmx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scdate != null">scdate,</if>
            <if test="yljg != null">yljg,</if>
            <if test="jslsh != null">jslsh,</if>
            <if test="xm != null">xm,</if>
            <if test="bah != null">bah,</if>
            <if test="zycs != null">zycs,</if>
            <if test="xb != null">xb,</if>
            <if test="nl != null">nl,</if>
            <if test="bzyzsnl != null">bzyzsnl,</if>
            <if test="xsecstz != null">xsecstz,</if>
            <if test="xserytz != null">xserytz,</if>
            <if test="csrq != null">csrq,</if>
            <if test="rytj != null">rytj,</if>
            <if test="rysj != null">rysj,</if>
            <if test="rykb != null">rykb,</if>
            <if test="cysj != null">cysj,</if>
            <if test="cykb != null">cykb,</if>
            <if test="sjzyts != null">sjzyts,</if>
            <if test="lyfs != null">lyfs,</if>
            <if test="zfy != null">zfy,</if>
            <if test="drgbh != null">drgbh,</if>
            <if test="drgmc != null">drgmc,</if>
            <if test="drgqz != null">drgqz,</if>
            <if test="jbdm != null">jbdm,</if>
            <if test="zyzd != null">zyzd,</if>
            <if test="jbdm1 != null">jbdm1,</if>
            <if test="qtzd1 != null">qtzd1,</if>
            <if test="sfzl1 != null">sfzl1,</if>
            <if test="jbdm2 != null">jbdm2,</if>
            <if test="qtzd2 != null">qtzd2,</if>
            <if test="sfzl2 != null">sfzl2,</if>
            <if test="jbdm3 != null">jbdm3,</if>
            <if test="qtzd3 != null">qtzd3,</if>
            <if test="sfzl3 != null">sfzl3,</if>
            <if test="jbdm4 != null">jbdm4,</if>
            <if test="qtzd4 != null">qtzd4,</if>
            <if test="sfzl4 != null">sfzl4,</if>
            <if test="jbdm5 != null">jbdm5,</if>
            <if test="qtzd5 != null">qtzd5,</if>
            <if test="sfzl5 != null">sfzl5,</if>
            <if test="jbdm6 != null">jbdm6,</if>
            <if test="qtzd6 != null">qtzd6,</if>
            <if test="sfzl6 != null">sfzl6,</if>
            <if test="jbdm7 != null">jbdm7,</if>
            <if test="qtzd7 != null">qtzd7,</if>
            <if test="sfzl7 != null">sfzl7,</if>
            <if test="jbdm8 != null">jbdm8,</if>
            <if test="qtzd8 != null">qtzd8,</if>
            <if test="sfzl8 != null">sfzl8,</if>
            <if test="jbdm9 != null">jbdm9,</if>
            <if test="qtzd9 != null">qtzd9,</if>
            <if test="sfzl9 != null">sfzl9,</if>
            <if test="jbdm10 != null">jbdm10,</if>
            <if test="qtzd10 != null">qtzd10,</if>
            <if test="sfzl10 != null">sfzl10,</if>
            <if test="jbdm11 != null">jbdm11,</if>
            <if test="qtzd11 != null">qtzd11,</if>
            <if test="sfzl11 != null">sfzl11,</if>
            <if test="jbdm12 != null">jbdm12,</if>
            <if test="qtzd12 != null">qtzd12,</if>
            <if test="sfzl12 != null">sfzl12,</if>
            <if test="jbdm13 != null">jbdm13,</if>
            <if test="qtzd13 != null">qtzd13,</if>
            <if test="sfzl13 != null">sfzl13,</if>
            <if test="jbdm14 != null">jbdm14,</if>
            <if test="qtzd14 != null">qtzd14,</if>
            <if test="sfzl14 != null">sfzl14,</if>
            <if test="jbdm15 != null">jbdm15,</if>
            <if test="qtzd15 != null">qtzd15,</if>
            <if test="sfzl15 != null">sfzl15,</if>
            <if test="jsdate != null">jsdate,</if>
            <if test="ssjczbm1 != null">ssjczbm1,</if>
            <if test="ssjczmc1 != null">ssjczmc1,</if>
            <if test="ssjczbm2 != null">ssjczbm2,</if>
            <if test="ssjczmc2 != null">ssjczmc2,</if>
            <if test="ssjczbm3 != null">ssjczbm3,</if>
            <if test="ssjczmc3 != null">ssjczmc3,</if>
            <if test="ssjczbm4 != null">ssjczbm4,</if>
            <if test="ssjczmc4 != null">ssjczmc4,</if>
            <if test="ssjczbm5 != null">ssjczbm5,</if>
            <if test="ssjczmc5 != null">ssjczmc5,</if>
            <if test="ssjczbm6 != null">ssjczbm6,</if>
            <if test="ssjczmc6 != null">ssjczmc6,</if>
            <if test="ssjczbm7 != null">ssjczbm7,</if>
            <if test="ssjczmc7 != null">ssjczmc7,</if>
            <if test="ssjczbm8 != null">ssjczbm8,</if>
            <if test="ssjczmc8 != null">ssjczmc8,</if>
            <if test="drgrzlb != null">drgrzlb,</if>
            <if test="jstype != null">jstype,</if>
            <if test="jsfs != null">jsfs,</if>
            <if test="zyzfy != null">zyzfy,</if>
            <if test="tczf != null">tczf,</if>
            <if test="drgzfbz != null">drgzfbz,</if>
            <if test="drgybf != null">drgybf,</if>
            <if test="cyksbm != null">cyksbm,</if>
            <if test="cyksmc != null">cyksmc,</if>
            <if test="csdrgbh != null">csdrgbh,</if>
            <if test="csdrgmc != null">csdrgmc,</if>
          <if test="kydrgbh != null">kydrgbh,</if>
          <if test="kydrgmc != null">kydrgmc,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scdate != null">#{scdate},</if>
            <if test="yljg != null">#{yljg},</if>
            <if test="jslsh != null">#{jslsh},</if>
            <if test="xm != null">#{xm},</if>
            <if test="bah != null">#{bah},</if>
            <if test="zycs != null">#{zycs},</if>
            <if test="xb != null">#{xb},</if>
            <if test="nl != null">#{nl},</if>
            <if test="bzyzsnl != null">#{bzyzsnl},</if>
            <if test="xsecstz != null">#{xsecstz},</if>
            <if test="xserytz != null">#{xserytz},</if>
            <if test="csrq != null">#{csrq},</if>
            <if test="rytj != null">#{rytj},</if>
            <if test="rysj != null">#{rysj},</if>
            <if test="rykb != null">#{rykb},</if>
            <if test="cysj != null">#{cysj},</if>
            <if test="cykb != null">#{cykb},</if>
            <if test="sjzyts != null">#{sjzyts},</if>
            <if test="lyfs != null">#{lyfs},</if>
            <if test="zfy != null">#{zfy},</if>
            <if test="drgbh != null">#{drgbh},</if>
            <if test="drgmc != null">#{drgmc},</if>
            <if test="drgqz != null">#{drgqz},</if>
            <if test="jbdm != null">#{jbdm},</if>
            <if test="zyzd != null">#{zyzd},</if>
            <if test="jbdm1 != null">#{jbdm1},</if>
            <if test="qtzd1 != null">#{qtzd1},</if>
            <if test="sfzl1 != null">#{sfzl1},</if>
            <if test="jbdm2 != null">#{jbdm2},</if>
            <if test="qtzd2 != null">#{qtzd2},</if>
            <if test="sfzl2 != null">#{sfzl2},</if>
            <if test="jbdm3 != null">#{jbdm3},</if>
            <if test="qtzd3 != null">#{qtzd3},</if>
            <if test="sfzl3 != null">#{sfzl3},</if>
            <if test="jbdm4 != null">#{jbdm4},</if>
            <if test="qtzd4 != null">#{qtzd4},</if>
            <if test="sfzl4 != null">#{sfzl4},</if>
            <if test="jbdm5 != null">#{jbdm5},</if>
            <if test="qtzd5 != null">#{qtzd5},</if>
            <if test="sfzl5 != null">#{sfzl5},</if>
            <if test="jbdm6 != null">#{jbdm6},</if>
            <if test="qtzd6 != null">#{qtzd6},</if>
            <if test="sfzl6 != null">#{sfzl6},</if>
            <if test="jbdm7 != null">#{jbdm7},</if>
            <if test="qtzd7 != null">#{qtzd7},</if>
            <if test="sfzl7 != null">#{sfzl7},</if>
            <if test="jbdm8 != null">#{jbdm8},</if>
            <if test="qtzd8 != null">#{qtzd8},</if>
            <if test="sfzl8 != null">#{sfzl8},</if>
            <if test="jbdm9 != null">#{jbdm9},</if>
            <if test="qtzd9 != null">#{qtzd9},</if>
            <if test="sfzl9 != null">#{sfzl9},</if>
            <if test="jbdm10 != null">#{jbdm10},</if>
            <if test="qtzd10 != null">#{qtzd10},</if>
            <if test="sfzl10 != null">#{sfzl10},</if>
            <if test="jbdm11 != null">#{jbdm11},</if>
            <if test="qtzd11 != null">#{qtzd11},</if>
            <if test="sfzl11 != null">#{sfzl11},</if>
            <if test="jbdm12 != null">#{jbdm12},</if>
            <if test="qtzd12 != null">#{qtzd12},</if>
            <if test="sfzl12 != null">#{sfzl12},</if>
            <if test="jbdm13 != null">#{jbdm13},</if>
            <if test="qtzd13 != null">#{qtzd13},</if>
            <if test="sfzl13 != null">#{sfzl13},</if>
            <if test="jbdm14 != null">#{jbdm14},</if>
            <if test="qtzd14 != null">#{qtzd14},</if>
            <if test="sfzl14 != null">#{sfzl14},</if>
            <if test="jbdm15 != null">#{jbdm15},</if>
            <if test="qtzd15 != null">#{qtzd15},</if>
            <if test="sfzl15 != null">#{sfzl15},</if>
            <if test="jsdate != null">#{jsdate},</if>
            <if test="ssjczbm1 != null">#{ssjczbm1},</if>
            <if test="ssjczmc1 != null">#{ssjczmc1},</if>
            <if test="ssjczbm2 != null">#{ssjczbm2},</if>
            <if test="ssjczmc2 != null">#{ssjczmc2},</if>
            <if test="ssjczbm3 != null">#{ssjczbm3},</if>
            <if test="ssjczmc3 != null">#{ssjczmc3},</if>
            <if test="ssjczbm4 != null">#{ssjczbm4},</if>
            <if test="ssjczmc4 != null">#{ssjczmc4},</if>
            <if test="ssjczbm5 != null">#{ssjczbm5},</if>
            <if test="ssjczmc5 != null">#{ssjczmc5},</if>
            <if test="ssjczbm6 != null">#{ssjczbm6},</if>
            <if test="ssjczmc6 != null">#{ssjczmc6},</if>
            <if test="ssjczbm7 != null">#{ssjczbm7},</if>
            <if test="ssjczmc7 != null">#{ssjczmc7},</if>
            <if test="ssjczbm8 != null">#{ssjczbm8},</if>
            <if test="ssjczmc8 != null">#{ssjczmc8},</if>
            <if test="drgrzlb != null">#{drgrzlb},</if>
            <if test="jstype != null">#{jstype},</if>
            <if test="jsfs != null">#{jsfs},</if>
            <if test="zyzfy != null">#{zyzfy},</if>
            <if test="tczf != null">#{tczf},</if>
            <if test="drgzfbz != null">#{drgzfbz},</if>
            <if test="drgybf != null">#{drgybf},</if>
            <if test="cyksbm != null">#{cyksbm},</if>
            <if test="cyksmc != null">#{cyksmc},</if>
            <if test="csdrgbh != null">#{csdrgbh},</if>
            <if test="csdrgmc != null">#{csdrgmc},</if>
          <if test="kydrgbh != null">#{kydrgbh},</if>
          <if test="kydrgmc != null">#{kydrgmc},</if>
         </trim>
    </insert>

    <update id="updateBlfzmx" parameterType="Blfzmx">
        update blfzmx
        <trim prefix="SET" suffixOverrides=",">
            <if test="yljg != null">yljg = #{yljg},</if>
            <if test="jslsh != null">jslsh = #{jslsh},</if>
            <if test="xm != null">xm = #{xm},</if>
            <if test="bah != null">bah = #{bah},</if>
            <if test="zycs != null">zycs = #{zycs},</if>
            <if test="xb != null">xb = #{xb},</if>
            <if test="nl != null">nl = #{nl},</if>
            <if test="bzyzsnl != null">bzyzsnl = #{bzyzsnl},</if>
            <if test="xsecstz != null">xsecstz = #{xsecstz},</if>
            <if test="xserytz != null">xserytz = #{xserytz},</if>
            <if test="csrq != null">csrq = #{csrq},</if>
            <if test="rytj != null">rytj = #{rytj},</if>
            <if test="rysj != null">rysj = #{rysj},</if>
            <if test="rykb != null">rykb = #{rykb},</if>
            <if test="cysj != null">cysj = #{cysj},</if>
            <if test="cykb != null">cykb = #{cykb},</if>
            <if test="sjzyts != null">sjzyts = #{sjzyts},</if>
            <if test="lyfs != null">lyfs = #{lyfs},</if>
            <if test="zfy != null">zfy = #{zfy},</if>
            <if test="drgbh != null">drgbh = #{drgbh},</if>
            <if test="drgmc != null">drgmc = #{drgmc},</if>
            <if test="drgqz != null">drgqz = #{drgqz},</if>
            <if test="jbdm != null">jbdm = #{jbdm},</if>
            <if test="zyzd != null">zyzd = #{zyzd},</if>
            <if test="jbdm1 != null">jbdm1 = #{jbdm1},</if>
            <if test="qtzd1 != null">qtzd1 = #{qtzd1},</if>
            <if test="sfzl1 != null">sfzl1 = #{sfzl1},</if>
            <if test="jbdm2 != null">jbdm2 = #{jbdm2},</if>
            <if test="qtzd2 != null">qtzd2 = #{qtzd2},</if>
            <if test="sfzl2 != null">sfzl2 = #{sfzl2},</if>
            <if test="jbdm3 != null">jbdm3 = #{jbdm3},</if>
            <if test="qtzd3 != null">qtzd3 = #{qtzd3},</if>
            <if test="sfzl3 != null">sfzl3 = #{sfzl3},</if>
            <if test="jbdm4 != null">jbdm4 = #{jbdm4},</if>
            <if test="qtzd4 != null">qtzd4 = #{qtzd4},</if>
            <if test="sfzl4 != null">sfzl4 = #{sfzl4},</if>
            <if test="jbdm5 != null">jbdm5 = #{jbdm5},</if>
            <if test="qtzd5 != null">qtzd5 = #{qtzd5},</if>
            <if test="sfzl5 != null">sfzl5 = #{sfzl5},</if>
            <if test="jbdm6 != null">jbdm6 = #{jbdm6},</if>
            <if test="qtzd6 != null">qtzd6 = #{qtzd6},</if>
            <if test="sfzl6 != null">sfzl6 = #{sfzl6},</if>
            <if test="jbdm7 != null">jbdm7 = #{jbdm7},</if>
            <if test="qtzd7 != null">qtzd7 = #{qtzd7},</if>
            <if test="sfzl7 != null">sfzl7 = #{sfzl7},</if>
            <if test="jbdm8 != null">jbdm8 = #{jbdm8},</if>
            <if test="qtzd8 != null">qtzd8 = #{qtzd8},</if>
            <if test="sfzl8 != null">sfzl8 = #{sfzl8},</if>
            <if test="jbdm9 != null">jbdm9 = #{jbdm9},</if>
            <if test="qtzd9 != null">qtzd9 = #{qtzd9},</if>
            <if test="sfzl9 != null">sfzl9 = #{sfzl9},</if>
            <if test="jbdm10 != null">jbdm10 = #{jbdm10},</if>
            <if test="qtzd10 != null">qtzd10 = #{qtzd10},</if>
            <if test="sfzl10 != null">sfzl10 = #{sfzl10},</if>
            <if test="jbdm11 != null">jbdm11 = #{jbdm11},</if>
            <if test="qtzd11 != null">qtzd11 = #{qtzd11},</if>
            <if test="sfzl11 != null">sfzl11 = #{sfzl11},</if>
            <if test="jbdm12 != null">jbdm12 = #{jbdm12},</if>
            <if test="qtzd12 != null">qtzd12 = #{qtzd12},</if>
            <if test="sfzl12 != null">sfzl12 = #{sfzl12},</if>
            <if test="jbdm13 != null">jbdm13 = #{jbdm13},</if>
            <if test="qtzd13 != null">qtzd13 = #{qtzd13},</if>
            <if test="sfzl13 != null">sfzl13 = #{sfzl13},</if>
            <if test="jbdm14 != null">jbdm14 = #{jbdm14},</if>
            <if test="qtzd14 != null">qtzd14 = #{qtzd14},</if>
            <if test="sfzl14 != null">sfzl14 = #{sfzl14},</if>
            <if test="jbdm15 != null">jbdm15 = #{jbdm15},</if>
            <if test="qtzd15 != null">qtzd15 = #{qtzd15},</if>
            <if test="sfzl15 != null">sfzl15 = #{sfzl15},</if>
            <if test="jsdate != null">jsdate = #{jsdate},</if>
            <if test="ssjczbm1 != null">ssjczbm1 = #{ssjczbm1},</if>
            <if test="ssjczmc1 != null">ssjczmc1 = #{ssjczmc1},</if>
            <if test="ssjczbm2 != null">ssjczbm2 = #{ssjczbm2},</if>
            <if test="ssjczmc2 != null">ssjczmc2 = #{ssjczmc2},</if>
            <if test="ssjczbm3 != null">ssjczbm3 = #{ssjczbm3},</if>
            <if test="ssjczmc3 != null">ssjczmc3 = #{ssjczmc3},</if>
            <if test="ssjczbm4 != null">ssjczbm4 = #{ssjczbm4},</if>
            <if test="ssjczmc4 != null">ssjczmc4 = #{ssjczmc4},</if>
            <if test="ssjczbm5 != null">ssjczbm5 = #{ssjczbm5},</if>
            <if test="ssjczmc5 != null">ssjczmc5 = #{ssjczmc5},</if>
            <if test="ssjczbm6 != null">ssjczbm6 = #{ssjczbm6},</if>
            <if test="ssjczmc6 != null">ssjczmc6 = #{ssjczmc6},</if>
            <if test="ssjczbm7 != null">ssjczbm7 = #{ssjczbm7},</if>
            <if test="ssjczmc7 != null">ssjczmc7 = #{ssjczmc7},</if>
            <if test="ssjczbm8 != null">ssjczbm8 = #{ssjczbm8},</if>
            <if test="ssjczmc8 != null">ssjczmc8 = #{ssjczmc8},</if>
            <if test="drgrzlb != null">drgrzlb = #{drgrzlb},</if>
            <if test="jstype != null">jstype = #{jstype},</if>
            <if test="jsfs != null">jsfs = #{jsfs},</if>
            <if test="zyzfy != null">zyzfy = #{zyzfy},</if>
            <if test="tczf != null">tczf = #{tczf},</if>
            <if test="drgzfbz != null">drgzfbz = #{drgzfbz},</if>
            <if test="drgybf != null">drgybf = #{drgybf},</if>
            <if test="cyksbm != null">cyksbm = #{cyksbm},</if>
            <if test="cyksmc != null">cyksmc = #{cyksmc},</if>
            <if test="csdrgmc != null">csdrgmc = #{csdrgmc},</if>
            <if test="csdrgbh != null">csdrgbh = #{csdrgbh},</if>
          <if test="kydrgmc != null">kydrgmc = #{kydrgmc},</if>
          <if test="kydrgbh != null">kydrgbh = #{kydrgbh},</if>
        </trim>
        where bah = #{bah}
    </update>

    <delete id="deleteBlfzmxByScdate" parameterType="Date">
        delete from blfzmx where scdate = #{scdate}
    </delete>

    <delete id="deleteBlfzmxByScdates" parameterType="String">
        delete from blfzmx where scdate in
        <foreach item="scdate" collection="array" open="(" separator="," close=")">
            #{scdate}
        </foreach>
    </delete>
</mapper>
