<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HLcljItemMMapper">
    
    <resultMap type="HLcljItemM" id="HLcljItemMResult">
        <result property="cId"    column="c_id"    />
        <result property="cSjId"    column="c_sj_id"    />
        <result property="cName"    column="c_name"    />
        <result property="cSno"    column="c_sno"    />
        <result property="cType"    column="c_type"    />
        <result property="cKzType"    column="c_kz_type"    />
        <result property="cPdType"    column="c_pd_type"    />
        <result property="cOpId"    column="c_op_id"    />
        <result property="cOpDate"    column="c_op_date"    />
        <result property="cIschild"    column="c_ischild"    />
        <result property="cTsType"    column="c_ts_type"    />
    </resultMap>

    <sql id="selectHLcljItemMVo">
        select c_id, c_sj_id, c_name, c_sno, c_type, c_kz_type, c_pd_type, c_op_id, c_op_date, c_ischild, c_ts_type from h_lclj_item_m
    </sql>

    <select id="selectHLcljItemMList" parameterType="HLcljItemM" resultMap="HLcljItemMResult">
        <include refid="selectHLcljItemMVo"/>
        <where>  
            <if test="cSjId != null  and cSjId != ''"> and c_sj_id = #{cSjId}</if>
            <if test="cName != null  and cName != ''"> and c_name like concat('%', #{cName}, '%')</if>
            <if test="cType != null  and cType != ''"> and c_type = #{cType}</if>
            <if test="cKzType != null  and cKzType != ''"> and c_kz_type = #{cKzType}</if>
            <if test="cPdType != null  and cPdType != ''"> and c_pd_type = #{cPdType}</if>
            <if test="cOpId != null  and cOpId != ''"> and c_op_id = #{cOpId}</if>
            <if test="cOpDate != null "> and c_op_date = #{cOpDate}</if>
            <if test="cIschild != null  and cIschild != ''"> and c_ischild = #{cIschild}</if>
            <if test="cTsType != null  and cTsType != ''"> and c_ts_type = #{cTsType}</if>
        </where>
        order by c_sno asc
    </select>
    
    <select id="selectHLcljItemMByCId" parameterType="String" resultMap="HLcljItemMResult">
        <include refid="selectHLcljItemMVo"/>
        where c_id = #{cId}
    </select>
        
    <insert id="insertHLcljItemM" parameterType="HLcljItemM">
        insert into h_lclj_item_m
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cId != null">c_id,</if>
            <if test="cSjId != null and cSjId != ''">c_sj_id,</if>
            <if test="cName != null">c_name,</if>
            <if test="cSno != null">c_sno,</if>
            <if test="cType != null and cType != ''">c_type,</if>
            <if test="cKzType != null and cKzType != ''">c_kz_type,</if>
            <if test="cPdType != null and cPdType != ''">c_pd_type,</if>
            <if test="cOpId != null and cOpId != ''">c_op_id,</if>
            <if test="cOpDate != null">c_op_date,</if>
            <if test="cIschild != null">c_ischild,</if>
            <if test="cTsType != null">c_ts_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cId != null">#{cId},</if>
            <if test="cSjId != null and cSjId != ''">#{cSjId},</if>
            <if test="cName != null">#{cName},</if>
            <if test="cSno != null">#{cSno},</if>
            <if test="cType != null and cType != ''">#{cType},</if>
            <if test="cKzType != null and cKzType != ''">#{cKzType},</if>
            <if test="cPdType != null and cPdType != ''">#{cPdType},</if>
            <if test="cOpId != null and cOpId != ''">#{cOpId},</if>
            <if test="cOpDate != null">#{cOpDate},</if>
            <if test="cIschild != null">#{cIschild},</if>
            <if test="cTsType != null">#{cTsType},</if>
         </trim>
    </insert>

    <update id="updateHLcljItemM" parameterType="HLcljItemM">
        update h_lclj_item_m
        <trim prefix="SET" suffixOverrides=",">
            <if test="cSjId != null and cSjId != ''">c_sj_id = #{cSjId},</if>
            <if test="cName != null">c_name = #{cName},</if>
            <if test="cSno != null">c_sno = #{cSno},</if>
            <if test="cType != null and cType != ''">c_type = #{cType},</if>
            <if test="cKzType != null and cKzType != ''">c_kz_type = #{cKzType},</if>
            <if test="cPdType != null and cPdType != ''">c_pd_type = #{cPdType},</if>
            <if test="cOpId != null and cOpId != ''">c_op_id = #{cOpId},</if>
            <if test="cOpDate != null">c_op_date = #{cOpDate},</if>
            <if test="cIschild != null">c_ischild = #{cIschild},</if>
            <if test="cTsType != null">c_ts_type = #{cTsType},</if>
        </trim>
        where c_id = #{cId}
    </update>

    <delete id="deleteHLcljItemMByCId" parameterType="String">
        delete from h_lclj_item_m where c_id = #{cId}
    </delete>

    <delete id="deleteHLcljItemMByCIds" parameterType="String">
        delete from h_lclj_item_m where c_id in 
        <foreach item="cId" collection="array" open="(" separator="," close=")">
            #{cId}
        </foreach>
    </delete>
</mapper>