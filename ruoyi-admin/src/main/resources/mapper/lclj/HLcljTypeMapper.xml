<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HLcljTypeMapper">
    
    <resultMap type="HLcljType" id="HLcljTypeResult">
        <result property="cId"    column="c_id"    />
        <result property="cCode"    column="c_code"    />
        <result property="cNccd"    column="c_nccd"    />
        <result property="cWbcode"    column="c_wbcode"    />
        <result property="cName"    column="c_name"    />
        <result property="cIcdCode"    column="c_icd_code"    />
        <result property="cDeptId"    column="c_dept_id"    />
        <result property="cMinDays"    column="c_min_days"    />
        <result property="cMaxDays"    column="c_max_days"    />
        <result property="cNote"    column="c_note"    />
        <result property="cStatus"    column="c_status"    />
        <result property="cOpId"    column="c_op_id"    />
        <result property="cOpDate"    column="c_op_date"    />
        <result property="cMaxCost"    column="c_max_cost"    />
        <result property="cMinCost"    column="c_min_cost"    />
        <result property="cZkblId"    column="c_zkbl_id"    />
        <result property="cExpression"    column="c_expression"    />
        <result property="cYxYqdays"    column="c_yx_yqdays"    />
        <result property="cDbzName"    column="c_dbz_name"    />
        <result property="cDbzCost"    column="c_dbz_cost"    />
        <result property="cIcdCodeZy"    column="c_icd_code_zy"    />
        <result property="cDbzFlag"    column="c_dbz_flag"    />
        <result property="cBfzFlag"    column="c_bfz_flag"    />
    </resultMap>

    <sql id="selectHLcljTypeVo">
        select c_id, c_code, c_nccd, c_wbcode, c_name, c_icd_code, c_dept_id, c_min_days, c_max_days, c_note, c_status, c_op_id, c_op_date, c_max_cost, c_min_cost, c_zkbl_id, c_expression, c_yx_yqdays, c_dbz_name, c_dbz_cost, c_icd_code_zy, c_dbz_flag, c_bfz_flag from h_lclj_type
    </sql>

    <select id="selectHLcljTypeList" parameterType="HLcljType" resultMap="HLcljTypeResult">
        <include refid="selectHLcljTypeVo"/>
        <where>  
            <if test="cCode != null  and cCode != ''"> and c_code = #{cCode}</if>
            <if test="cNccd != null  and cNccd != ''"> and c_nccd like concat('%', #{cNccd}, '%')</if>
            <if test="cWbcode != null  and cWbcode != ''"> and c_wbcode = #{cWbcode}</if>
            <if test="cName != null  and cName != ''"> and c_name like concat('%', #{cName}, '%')</if>
            <if test="cIcdCode != null  and cIcdCode != ''"> and c_icd_code = #{cIcdCode}</if>
            <if test="cDeptId != null  and cDeptId != ''"> and c_dept_id = #{cDeptId}</if>
            <if test="cMinDays != null "> and c_min_days = #{cMinDays}</if>
            <if test="cMaxDays != null "> and c_max_days = #{cMaxDays}</if>
            <if test="cStatus != null "> and c_status = #{cStatus}</if>
            <if test="cMaxCost != null "> and c_max_cost = #{cMaxCost}</if>
            <if test="cMinCost != null "> and c_min_cost = #{cMinCost}</if>
            <if test="cZkblId != null  and cZkblId != ''"> and c_zkbl_id = #{cZkblId}</if>
            <if test="cYxYqdays != null "> and c_yx_yqdays = #{cYxYqdays}</if>
            <if test="cDbzName != null  and cDbzName != ''"> and c_dbz_name like concat('%', #{cDbzName}, '%')</if>
            <if test="cDbzCost != null "> and c_dbz_cost = #{cDbzCost}</if>
            <if test="cIcdCodeZy != null  and cIcdCodeZy != ''"> and c_icd_code_zy = #{cIcdCodeZy}</if>
            <if test="cDbzFlag != null  and cDbzFlag != ''"> and c_dbz_flag = #{cDbzFlag}</if>
        </where>
    </select>
    
    <select id="selectHLcljTypeByCId" parameterType="String" resultMap="HLcljTypeResult">
        <include refid="selectHLcljTypeVo"/>
        where c_id = #{cId}
    </select>
        
    <insert id="insertHLcljType" parameterType="HLcljType">
        insert into h_lclj_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cId != null">c_id,</if>
            <if test="cCode != null and cCode != ''">c_code,</if>
            <if test="cNccd != null and cNccd != ''">c_nccd,</if>
            <if test="cWbcode != null and cWbcode != ''">c_wbcode,</if>
            <if test="cName != null and cName != ''">c_name,</if>
            <if test="cIcdCode != null">c_icd_code,</if>
            <if test="cDeptId != null">c_dept_id,</if>
            <if test="cMinDays != null">c_min_days,</if>
            <if test="cMaxDays != null">c_max_days,</if>
            <if test="cNote != null">c_note,</if>
            <if test="cStatus != null">c_status,</if>
            <if test="cOpId != null and cOpId != ''">c_op_id,</if>
            <if test="cOpDate != null">c_op_date,</if>
            <if test="cMaxCost != null">c_max_cost,</if>
            <if test="cMinCost != null">c_min_cost,</if>
            <if test="cZkblId != null">c_zkbl_id,</if>
            <if test="cExpression != null">c_expression,</if>
            <if test="cYxYqdays != null">c_yx_yqdays,</if>
            <if test="cDbzName != null">c_dbz_name,</if>
            <if test="cDbzCost != null">c_dbz_cost,</if>
            <if test="cIcdCodeZy != null">c_icd_code_zy,</if>
            <if test="cDbzFlag != null">c_dbz_flag,</if>
            <if test="cBfzFlag != null">c_bfz_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cId != null">#{cId},</if>
            <if test="cCode != null and cCode != ''">#{cCode},</if>
            <if test="cNccd != null and cNccd != ''">#{cNccd},</if>
            <if test="cWbcode != null and cWbcode != ''">#{cWbcode},</if>
            <if test="cName != null and cName != ''">#{cName},</if>
            <if test="cIcdCode != null">#{cIcdCode},</if>
            <if test="cDeptId != null">#{cDeptId},</if>
            <if test="cMinDays != null">#{cMinDays},</if>
            <if test="cMaxDays != null">#{cMaxDays},</if>
            <if test="cNote != null">#{cNote},</if>
            <if test="cStatus != null">#{cStatus},</if>
            <if test="cOpId != null and cOpId != ''">#{cOpId},</if>
            <if test="cOpDate != null">#{cOpDate},</if>
            <if test="cMaxCost != null">#{cMaxCost},</if>
            <if test="cMinCost != null">#{cMinCost},</if>
            <if test="cZkblId != null">#{cZkblId},</if>
            <if test="cExpression != null">#{cExpression},</if>
            <if test="cYxYqdays != null">#{cYxYqdays},</if>
            <if test="cDbzName != null">#{cDbzName},</if>
            <if test="cDbzCost != null">#{cDbzCost},</if>
            <if test="cIcdCodeZy != null">#{cIcdCodeZy},</if>
            <if test="cDbzFlag != null">#{cDbzFlag},</if>
            <if test="cBfzFlag != null">#{cBfzFlag},</if>
         </trim>
    </insert>

    <update id="updateHLcljType" parameterType="HLcljType">
        update h_lclj_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="cCode != null and cCode != ''">c_code = #{cCode},</if>
            <if test="cNccd != null and cNccd != ''">c_nccd = #{cNccd},</if>
            <if test="cWbcode != null and cWbcode != ''">c_wbcode = #{cWbcode},</if>
            <if test="cName != null and cName != ''">c_name = #{cName},</if>
            <if test="cIcdCode != null">c_icd_code = #{cIcdCode},</if>
            <if test="cDeptId != null">c_dept_id = #{cDeptId},</if>
            <if test="cMinDays != null">c_min_days = #{cMinDays},</if>
            <if test="cMaxDays != null">c_max_days = #{cMaxDays},</if>
            <if test="cNote != null">c_note = #{cNote},</if>
            <if test="cStatus != null">c_status = #{cStatus},</if>
            <if test="cOpId != null and cOpId != ''">c_op_id = #{cOpId},</if>
            <if test="cOpDate != null">c_op_date = #{cOpDate},</if>
            <if test="cMaxCost != null">c_max_cost = #{cMaxCost},</if>
            <if test="cMinCost != null">c_min_cost = #{cMinCost},</if>
            <if test="cZkblId != null">c_zkbl_id = #{cZkblId},</if>
            <if test="cExpression != null">c_expression = #{cExpression},</if>
            <if test="cYxYqdays != null">c_yx_yqdays = #{cYxYqdays},</if>
            <if test="cDbzName != null">c_dbz_name = #{cDbzName},</if>
            <if test="cDbzCost != null">c_dbz_cost = #{cDbzCost},</if>
            <if test="cIcdCodeZy != null">c_icd_code_zy = #{cIcdCodeZy},</if>
            <if test="cDbzFlag != null">c_dbz_flag = #{cDbzFlag},</if>
            <if test="cBfzFlag != null">c_bfz_flag = #{cBfzFlag},</if>
        </trim>
        where c_id = #{cId}
    </update>

    <delete id="deleteHLcljTypeByCId" parameterType="String">
        delete from h_lclj_type where c_id = #{cId}
    </delete>

    <delete id="deleteHLcljTypeByCIds" parameterType="String">
        delete from h_lclj_type where c_id in 
        <foreach item="cId" collection="array" open="(" separator="," close=")">
            #{cId}
        </foreach>
    </delete>
</mapper>