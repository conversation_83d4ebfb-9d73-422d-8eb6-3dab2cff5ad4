<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HLcljDateMapper">
    
    <resultMap type="HLcljDate" id="HLcljDateResult">
        <result property="cId"    column="c_id"    />
        <result property="cLcljId"    column="c_lclj_id"    />
        <result property="cDayType"    column="c_day_type"    />
        <result property="cSjUnit"    column="c_sj_unit"    />
        <result property="cDateFrom"    column="c_date_from"    />
        <result property="cDateTo"    column="c_date_to"    />
        <result property="cMaxCost"    column="c_max_cost"    />
        <result property="cMinCost"    column="c_min_cost"    />
    </resultMap>

    <sql id="selectHLcljDateVo">
        select c_id, c_lclj_id, c_day_type, c_sj_unit, c_date_from, c_date_to, c_max_cost, c_min_cost from h_lclj_date
    </sql>

    <select id="selectHLcljDateList" parameterType="HLcljDate" resultMap="HLcljDateResult">
        <include refid="selectHLcljDateVo"/>
        <where>  
            <if test="cLcljId != null  and cLcljId != ''"> and c_lclj_id = #{cLcljId}</if>
            <if test="cDayType != null  and cDayType != ''"> and c_day_type = #{cDayType}</if>
            <if test="cSjUnit != null  and cSjUnit != ''"> and c_sj_unit = #{cSjUnit}</if>
            <if test="cDateFrom != null "> and c_date_from = #{cDateFrom}</if>
            <if test="cDateTo != null "> and c_date_to = #{cDateTo}</if>
            <if test="cMaxCost != null "> and c_max_cost = #{cMaxCost}</if>
            <if test="cMinCost != null "> and c_min_cost = #{cMinCost}</if>
        </where>
        order by c_date_from asc
    </select>
    
    <select id="selectHLcljDateByCId" parameterType="String" resultMap="HLcljDateResult">
        <include refid="selectHLcljDateVo"/>
        where c_id = #{cId}
    </select>
        
    <insert id="insertHLcljDate" parameterType="HLcljDate">
        insert into h_lclj_date
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cId != null">c_id,</if>
            <if test="cLcljId != null and cLcljId != ''">c_lclj_id,</if>
            <if test="cDayType != null and cDayType != ''">c_day_type,</if>
            <if test="cSjUnit != null and cSjUnit != ''">c_sj_unit,</if>
            <if test="cDateFrom != null">c_date_from,</if>
            <if test="cDateTo != null">c_date_to,</if>
            <if test="cMaxCost != null">c_max_cost,</if>
            <if test="cMinCost != null">c_min_cost,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cId != null">#{cId},</if>
            <if test="cLcljId != null and cLcljId != ''">#{cLcljId},</if>
            <if test="cDayType != null and cDayType != ''">#{cDayType},</if>
            <if test="cSjUnit != null and cSjUnit != ''">#{cSjUnit},</if>
            <if test="cDateFrom != null">#{cDateFrom},</if>
            <if test="cDateTo != null">#{cDateTo},</if>
            <if test="cMaxCost != null">#{cMaxCost},</if>
            <if test="cMinCost != null">#{cMinCost},</if>
         </trim>
    </insert>

    <update id="updateHLcljDate" parameterType="HLcljDate">
        update h_lclj_date
        <trim prefix="SET" suffixOverrides=",">
            <if test="cDayType != null and cDayType != ''">c_day_type = #{cDayType},</if>
            <if test="cSjUnit != null and cSjUnit != ''">c_sj_unit = #{cSjUnit},</if>
            <if test="cDateFrom != null and cDateFrom != ''">c_date_from = #{cDateFrom},</if>
            <if test="cDateTo != null and cDateTo != ''">c_date_to = #{cDateTo},</if>
        </trim>
        where c_id = #{cId}
    </update>

    <delete id="deleteHLcljDateByCId" parameterType="String">
        delete from h_lclj_date where c_id = #{cId}
    </delete>

    <delete id="deleteHLcljDateByCIds" parameterType="String">
        delete from h_lclj_date where c_id in 
        <foreach item="cId" collection="array" open="(" separator="," close=")">
            #{cId}
        </foreach>
    </delete>
</mapper>