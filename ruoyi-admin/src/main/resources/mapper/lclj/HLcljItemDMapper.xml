<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HLcljItemDMapper">
    
    <resultMap type="HLcljItemD" id="HLcljItemDResult">
        <result property="cBillId"    column="c_bill_id"    />
        <result property="cDsno"    column="c_dsno"    />
        <result property="cName"    column="c_name"    />
        <result property="cSno"    column="c_sno"    />
        <result property="cKzType"    column="c_kz_type"    />
        <result property="cPdType"    column="c_pd_type"    />
        <result property="cPdItem"    column="c_pd_item"    />
        <result property="cItemtype"    column="c_itemtype"    />
        <result property="cYpType"    column="c_yp_type"    />
        <result property="cItemid"    column="c_itemid"    />
        <result property="cItemgroupno"    column="c_itemgroupno"    />
        <result property="cUsestyle"    column="c_usestyle"    />
        <result property="cPinci"    column="c_pinci"    />
        <result property="cDoctornote"    column="c_doctornote"    />
        <result property="cDose"    column="c_dose"    />
        <result property="cDoseUnit"    column="c_dose_unit"    />
        <result property="cAmount"    column="c_amount"    />
        <result property="cFs"    column="c_fs"    />
        <result property="cType"    column="c_type"    />
        <result property="cStoreId"    column="c_store_id"    />
        <result property="cName2017"    column="c_name_2017"    />
        <result property="cItemid2017"    column="c_itemid_2017"    />
        <result property="cMainFyname"    column="c_main_fyname"    />
    </resultMap>

    <sql id="selectHLcljItemDVo">
        select c_bill_id, c_dsno, c_name, c_sno, c_kz_type, c_pd_type, c_pd_item, c_itemtype, c_yp_type, c_itemid, c_itemgroupno, c_usestyle, c_pinci, c_doctornote, c_dose, c_dose_unit, c_amount, c_fs, c_type, c_store_id, c_name_2017, c_itemid_2017, c_main_fyname from h_lclj_item_d
    </sql>

    <select id="selectHLcljItemDList" parameterType="HLcljItemD" resultMap="HLcljItemDResult">
        <include refid="selectHLcljItemDVo"/>
        <where>  
            <if test="cName != null  and cName != ''"> and c_name like concat('%', #{cName}, '%')</if>
            <if test="cBillId != null  and cBillId != ''"> and c_bill_id = #{cBillId}</if>
            <if test="cSno != null "> and c_sno = #{cSno}</if>
            <if test="cKzType != null  and cKzType != ''"> and c_kz_type = #{cKzType}</if>
            <if test="cPdType != null  and cPdType != ''"> and c_pd_type = #{cPdType}</if>
            <if test="cPdItem != null  and cPdItem != ''"> and c_pd_item = #{cPdItem}</if>
            <if test="cItemtype != null  and cItemtype != ''"> and c_itemtype = #{cItemtype}</if>
            <if test="cYpType != null  and cYpType != ''"> and c_yp_type = #{cYpType}</if>
            <if test="cItemid != null  and cItemid != ''"> and c_itemid = #{cItemid}</if>
            <if test="cItemgroupno != null  and cItemgroupno != ''"> and c_itemgroupno = #{cItemgroupno}</if>
            <if test="cUsestyle != null  and cUsestyle != ''"> and c_usestyle = #{cUsestyle}</if>
            <if test="cPinci != null  and cPinci != ''"> and c_pinci = #{cPinci}</if>
            <if test="cDoctornote != null  and cDoctornote != ''"> and c_doctornote = #{cDoctornote}</if>
            <if test="cDose != null  and cDose != ''"> and c_dose = #{cDose}</if>
            <if test="cDoseUnit != null  and cDoseUnit != ''"> and c_dose_unit = #{cDoseUnit}</if>
            <if test="cAmount != null "> and c_amount = #{cAmount}</if>
            <if test="cFs != null "> and c_fs = #{cFs}</if>
            <if test="cType != null  and cType != ''"> and c_type = #{cType}</if>
            <if test="cStoreId != null  and cStoreId != ''"> and c_store_id = #{cStoreId}</if>
            <if test="cName2017 != null  and cName2017 != ''"> and c_name_2017 = #{cName2017}</if>
            <if test="cItemid2017 != null  and cItemid2017 != ''"> and c_itemid_2017 = #{cItemid2017}</if>
            <if test="cMainFyname != null  and cMainFyname != ''"> and c_main_fyname like concat('%', #{cMainFyname}, '%')</if>
        </where>
        order by c_sno
    </select>
    
    <select id="selectHLcljItemDByCBillId" parameterType="String" resultMap="HLcljItemDResult">
        <include refid="selectHLcljItemDVo"/>
        where c_bill_id = #{cBillId}
    </select>
        
    <insert id="insertHLcljItemD" parameterType="HLcljItemD">
        insert into h_lclj_item_d
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cBillId != null">c_bill_id,</if>
            <if test="cDsno != null">c_dsno,</if>
            <if test="cName != null and cName != ''">c_name,</if>
            <if test="cSno != null">c_sno,</if>
            <if test="cKzType != null and cKzType != ''">c_kz_type,</if>
            <if test="cPdType != null and cPdType != ''">c_pd_type,</if>
            <if test="cPdItem != null">c_pd_item,</if>
            <if test="cItemtype != null">c_itemtype,</if>
            <if test="cYpType != null">c_yp_type,</if>
            <if test="cItemid != null">c_itemid,</if>
            <if test="cItemgroupno != null">c_itemgroupno,</if>
            <if test="cUsestyle != null">c_usestyle,</if>
            <if test="cPinci != null">c_pinci,</if>
            <if test="cDoctornote != null">c_doctornote,</if>
            <if test="cDose != null">c_dose,</if>
            <if test="cDoseUnit != null">c_dose_unit,</if>
            <if test="cAmount != null">c_amount,</if>
            <if test="cFs != null">c_fs,</if>
            <if test="cType != null">c_type,</if>
            <if test="cStoreId != null">c_store_id,</if>
            <if test="cName2017 != null">c_name_2017,</if>
            <if test="cItemid2017 != null">c_itemid_2017,</if>
            <if test="cMainFyname != null">c_main_fyname,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cBillId != null">#{cBillId},</if>
            <if test="cDsno != null">#{cDsno},</if>
            <if test="cName != null and cName != ''">#{cName},</if>
            <if test="cSno != null">#{cSno},</if>
            <if test="cKzType != null and cKzType != ''">#{cKzType},</if>
            <if test="cPdType != null and cPdType != ''">#{cPdType},</if>
            <if test="cPdItem != null">#{cPdItem},</if>
            <if test="cItemtype != null">#{cItemtype},</if>
            <if test="cYpType != null">#{cYpType},</if>
            <if test="cItemid != null">#{cItemid},</if>
            <if test="cItemgroupno != null">#{cItemgroupno},</if>
            <if test="cUsestyle != null">#{cUsestyle},</if>
            <if test="cPinci != null">#{cPinci},</if>
            <if test="cDoctornote != null">#{cDoctornote},</if>
            <if test="cDose != null">#{cDose},</if>
            <if test="cDoseUnit != null">#{cDoseUnit},</if>
            <if test="cAmount != null">#{cAmount},</if>
            <if test="cFs != null">#{cFs},</if>
            <if test="cType != null">#{cType},</if>
            <if test="cStoreId != null">#{cStoreId},</if>
            <if test="cName2017 != null">#{cName2017},</if>
            <if test="cItemid2017 != null">#{cItemid2017},</if>
            <if test="cMainFyname != null">#{cMainFyname},</if>
         </trim>
    </insert>

    <update id="updateHLcljItemD" parameterType="HLcljItemD">
        update h_lclj_item_d
        <trim prefix="SET" suffixOverrides=",">
            <if test="cName != null and cName != ''">c_name = #{cName},</if>
            <if test="cSno != null">c_sno = #{cSno},</if>
            <if test="cKzType != null and cKzType != ''">c_kz_type = #{cKzType},</if>
            <if test="cPdType != null and cPdType != ''">c_pd_type = #{cPdType},</if>
            <if test="cPdItem != null">c_pd_item = #{cPdItem},</if>
            <if test="cItemtype != null">c_itemtype = #{cItemtype},</if>
            <if test="cYpType != null">c_yp_type = #{cYpType},</if>
            <if test="cItemid != null">c_itemid = #{cItemid},</if>
            <if test="cItemgroupno != null">c_itemgroupno = #{cItemgroupno},</if>
            <if test="cUsestyle != null">c_usestyle = #{cUsestyle},</if>
            <if test="cPinci != null">c_pinci = #{cPinci},</if>
            <if test="cDoctornote != null">c_doctornote = #{cDoctornote},</if>
            <if test="cDose != null">c_dose = #{cDose},</if>
            <if test="cDoseUnit != null">c_dose_unit = #{cDoseUnit},</if>
            <if test="cAmount != null">c_amount = #{cAmount},</if>
            <if test="cFs != null">c_fs = #{cFs},</if>
            <if test="cType != null">c_type = #{cType},</if>
            <if test="cStoreId != null">c_store_id = #{cStoreId},</if>
            <if test="cName2017 != null">c_name_2017 = #{cName2017},</if>
            <if test="cItemid2017 != null">c_itemid_2017 = #{cItemid2017},</if>
            <if test="cMainFyname != null">c_main_fyname = #{cMainFyname},</if>
        </trim>
        where c_bill_id = #{cBillId} and c_dsno = #{cDsno}
    </update>

    <delete id="deleteHLcljItemDByCBillId" parameterType="String">
        delete from h_lclj_item_d where c_bill_id = #{cBillId}
    </delete>

    <delete id="deleteHLcljItemDByCBillIds" parameterType="String">
        delete from h_lclj_item_d where c_bill_id in 
        <foreach item="cBillId" collection="array" open="(" separator="," close=")">
            #{cBillId}
        </foreach>
    </delete>
</mapper>