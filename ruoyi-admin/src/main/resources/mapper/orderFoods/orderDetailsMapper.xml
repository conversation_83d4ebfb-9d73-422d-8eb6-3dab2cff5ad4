<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.OrderDetailMapper">

  <!-- 订单明细结果映射 -->
  <resultMap id="OrderDetailResultMap" type="com.ruoyi.orderFood.entity.OrderDetail">
    <id column="id" property="id"/>
    <result column="order_id" property="orderId"/>
    <result column="food_id" property="foodId"/>
    <result column="spec_id" property="specId"/>
    <result column="quantity" property="quantity"/>
    <result column="unit_price" property="unitPrice"/>
    <result column="remark" property="remark"/>
  </resultMap>

  <!-- 1. 批量插入订单明细（创建订单时用，一次插入多个商品） -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO order_details (
    order_id, food_id, spec_id, quantity, unit_price, remark
    ) VALUES
    <foreach collection="list" item="detail" separator=",">
      (
      #{detail.orderId},
      #{detail.foodId},
      #{detail.specId},
      #{detail.quantity},
      #{detail.unitPrice},
      #{detail.remark}
      )
    </foreach>
  </insert>

  <!-- 2. 根据订单ID查询明细（订单详情页用，已在orders.mapper.xml中关联查询，可选） -->
  <select id="selectByOrderId" parameterType="Long" resultMap="OrderDetailResultMap">
    SELECT
      id, order_id AS orderId, food_id AS foodId,
      spec_id AS specId, quantity, unit_price AS unitPrice, remark
    FROM
      order_details
    WHERE
      order_id = #{orderId}
  </select>
</mapper>
