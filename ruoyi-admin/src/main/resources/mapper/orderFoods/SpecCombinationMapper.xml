<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.SpecCombinationMapper">

  <!-- 规格组合映射 -->
  <resultMap id="CombinationMap" type="com.ruoyi.orderFood.vo.SpecCombinationVO">
    <id column="sc_id" property="dbId"/>
    <result column="original_price" property="originalPriceTemp"/>
    <result column="sell_price" property="sellPriceTemp"/>
    <result column="stock" property="stock"/>
    <result column="value_ids" property="valueIdsStr"/>
  </resultMap>

  <!-- 规格值映射 -->
  <resultMap id="SpecValueMap" type="com.ruoyi.orderFood.vo.SpecValueVO">
    <id column="sv_id" property="valueId"/>
    <result column="param_id" property="paramId"/>
    <result column="value_name" property="valueName"/>
  </resultMap>

  <!-- 查询规格组合 -->
  <select id="selectCombinationsByGoodsId" parameterType="Long" resultMap="CombinationMap">
    SELECT
      sc.id AS sc_id,
      sc.original_price,
      sc.sell_price,
      sc.stock,
      GROUP_CONCAT(scr.value_id ORDER BY scr.id) AS value_ids
    FROM spec_combinations sc
           LEFT JOIN spec_combination_relations scr ON sc.id = scr.combination_id
    WHERE sc.goods_id = #{goodsId}
    GROUP BY sc.id
  </select>

  <!-- 查询规格值 -->
  <select id="selectSpecValuesByGoodsId" parameterType="Long" resultMap="SpecValueMap">
    SELECT
      sv.id AS sv_id,
      sv.param_id,
      sv.value_name
    FROM spec_values sv
           LEFT JOIN spec_params sp ON sv.param_id = sp.id
    WHERE sp.goods_id = #{goodsId}
  </select>

</mapper>

