<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.OrderMapper">
  <!-- 订单VO结果映射 -->
  <resultMap id="OrderVOResultMap" type="com.ruoyi.orderFood.vo.OrderVO">
    <id column="id" property="id"/>
    <result column="order_no" property="orderNo"/>
    <result column="total_amount" property="totalAmount"/>
    <result column="status" property="status"/>
    <result column="statusText" property="statusText"/>
    <result column="delivery_address" property="deliveryAddress"/>
    <result column="contact_info" property="contactInfo"/>
    <result column="remark" property="remark"/>
    <result column="create_time" property="createTime"/>
    <result column="pay_time" property="payTime"/>
  </resultMap>


  <insert id="insert" parameterType="com.ruoyi.orderFood.entity.Order" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO `orders` (user_id, order_no, total_amount, status,
                         delivery_address, contact_info, remark, create_time)
    VALUES (#{userId}, #{orderNo}, #{totalAmount}, #{status},
            #{deliveryAddress}, #{contactInfo}, #{remark}, #{createTime})
  </insert>

  <select id="selectByUserId" parameterType="java.lang.Long" resultMap="OrderVOResultMap">
    SELECT
      id, order_no as orderNo, total_amount as totalAmount, status,
      CASE status
        WHEN 1 THEN '待支付'
        WHEN 2 THEN '已支付'
        WHEN 3 THEN '已完成'
        WHEN 4 THEN '已取消'
        ELSE '未知状态'
        END as statusText,
      delivery_address as deliveryAddress, contact_info as contactInfo,
      remark, create_time as createTime, pay_time as payTime
    FROM `orders`
    WHERE user_id = #{userId}
    ORDER BY create_time DESC
  </select>

  <select id="selectById" parameterType="java.lang.Long" resultMap="OrderVOResultMap">
    SELECT
      id, order_no as orderNo, total_amount as totalAmount, status,
      CASE status
        WHEN 1 THEN '待支付'
        WHEN 2 THEN '已支付'
        WHEN 3 THEN '已完成'
        WHEN 4 THEN '已取消'
        ELSE '未知状态'
        END as statusText,
      delivery_address as deliveryAddress, contact_info as contactInfo,
      remark, create_time as createTime, pay_time as payTime
    FROM `orders`
    WHERE id = #{id}
  </select>

  <update id="updatePayStatus">
    UPDATE `orders`
    SET status = #{status}, pay_time = #{payTime}
    WHERE id = #{id}
  </update>

</mapper>
