<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.SpecValueMapper">

  <!-- 规格值ResultMap -->
  <resultMap id="SpecValueResultMap" type="com.ruoyi.orderFood.dto.SpecValueDTO">
    <id column="id" property="id"/>
    <result column="param_id" property="paramId"/>
    <result column="value_name" property="valueName"/>
    <result column="sort" property="sort"/>
  </resultMap>

  <resultMap id="SpecValueVoResultMap" type="com.ruoyi.orderFood.vo.SpecValueVO">
    <result column="param_id" property="paramId"/>
    <result column="value_name" property="valueName"/>
    <result column="value_id" property="valueId"/>
  </resultMap>



  <!-- 根据参数ID列表查询规格值 -->
  <select id="selectByParamIds" resultMap="SpecValueResultMap">
    SELECT id, param_id, value_name, sort
    FROM spec_values
    WHERE param_id IN
    <foreach collection="array" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    ORDER BY sort ASC
  </select>

  <select id="selectValueVOByParamIds" resultMap="SpecValueVoResultMap">
    select id as value_id, param_id, value_name from
    spec_values where param_id in
    <foreach item="id" collection="paramIds" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <!-- 根据参数ID查询规格值 -->
  <select id="selectByParamId" resultMap="SpecValueResultMap">
    SELECT id, param_id, value_name, sort
    FROM spec_values
    WHERE param_id = #{paramId}
    ORDER BY sort ASC
  </select>

</mapper>
