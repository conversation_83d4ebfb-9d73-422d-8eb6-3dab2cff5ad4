<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.FoodSpecMapper">

  <!-- 食品规格结果映射 -->
  <resultMap id="FoodSpecResultMap" type="com.ruoyi.orderFood.entity.FoodSpec">
    <id column="id" property="id"/>
    <result column="food_id" property="foodId"/>
    <result column="spec_name" property="specName"/>
    <result column="spec_price" property="specPrice"/>
    <result column="stock" property="stock"/>
    <result column="image_url" property="imageUrl"/>
  </resultMap>

  <select id="selectByFoodId" parameterType="java.lang.Long" resultMap="FoodSpecResultMap">
    SELECT id, food_id, spec_name, spec_price, stock,image_url
    FROM food_specs
    WHERE food_id = #{foodId}
  </select>

  <select id="selectById" parameterType="java.lang.Long" resultMap="FoodSpecResultMap">
    SELECT id, food_id, spec_name, spec_price, stock
    FROM food_specs
    WHERE id = #{id}
  </select>

</mapper>
