<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.FoodCategoryMapper">
  <!-- 食品分类结果映射 -->
  <resultMap id="FoodCategoryResultMap" type="com.ruoyi.orderFood.entity.FoodCategory">
    <id column="id" property="id"/>
    <result column="category_name" property="categoryName"/>
    <result column="sort_order" property="sortOrder"/>
    <result column="is_enabled" property="isEnabled"/>
  </resultMap>

  <select id="selectAllEnabled" resultMap="FoodCategoryResultMap">
    SELECT id, category_name, sort_order, is_enabled
    FROM food_categories
    WHERE is_enabled = 1
    ORDER BY sort_order ASC
  </select>

</mapper>
