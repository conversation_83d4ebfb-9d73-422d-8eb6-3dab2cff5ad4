<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.FoodItemMapper">
  <!-- 基础商品信息结果映射 -->
  <resultMap id="FoodItemResultsMap" type="com.ruoyi.orderFood.entity.FoodItem">
    <id column="id" property="id"/>
    <result column="category_id" property="categoryId"/>
    <result column="food_name" property="foodName"/>
    <result column="short_desc" property="shortDesc"/>
    <result column="full_desc" property="fullDesc"/>
    <result column="price" property="price"/>
    <result column="nutrition_info" property="nutritionInfo"/>
    <result column="ingredients" property="ingredients"/>
    <result column="suggestions" property="suggestions"/>
    <result column="image_url" property="imageUrl"/>
    <result column="tags" property="tags"/>
    <result column="is_enabled" property="isEnabled"/>
    <result column="create_time" property="createTime"/>
    <result column="specs" property="specs"/>
  </resultMap>

  <!-- 食品项结果映射 -->
  <resultMap id="FoodItemResultMap" type="com.ruoyi.orderFood.entity.FoodItem">
    <id column="id" property="id"/>
    <result column="category_id" property="categoryId"/>
    <result column="food_name" property="foodName"/>
    <result column="short_desc" property="shortDesc"/>
    <result column="full_desc" property="fullDesc"/>
    <result column="price" property="price"/>
    <result column="nutrition_info" property="nutritionInfo"/>
    <result column="ingredients" property="ingredients"/>
    <result column="suggestions" property="suggestions"/>
    <result column="image_url" property="imageUrl"/>
    <result column="tags" property="tags"/>
    <result column="is_enabled" property="isEnabled"/>
    <result column="create_time" property="createTime"/>
  </resultMap>

  <!-- 1. 商品基础信息的resultMap -->
  <resultMap id="FoodBaseResultMap" type="com.ruoyi.orderFood.dto.FoodItemDTO">
    <id column="id" property="id"/>
    <result column="food_name" property="foodName"/>
    <result column="price" property="price"/>
    <result column="image_url" property="imageUrl"/>
    <result column="category_name" property="categoryName"/>
  </resultMap>

  <!-- 2. 规格参数的resultMap -->
  <resultMap id="SpecParamResultMap" type="com.ruoyi.orderFood.vo.SpecParamVO">
    <id column="param_id" property="paramId"/>
    <result column="param_name" property="paramName"/>
    <result column="sort" property="sort"/>
  </resultMap>

  <!-- 3. 规格值的resultMap -->
  <resultMap id="SpecValueResultMap" type="com.ruoyi.orderFood.vo.SpecValueVO">
    <id column="value_id" property="valueId"/>
    <result column="param_id" property="paramId"/>
    <result column="value_name" property="valueName"/>
    <result column="sort" property="sort"/>
  </resultMap>

  <!-- 4. 规格组合的resultMap -->
  <resultMap id="SpecCombinationResultMap" type="com.ruoyi.orderFood.vo.SpecCombinationVO">
    <id column="combination_id" property="combinationId"/>
    <result column="combination_code" property="combinationCode"/>
    <result column="original_price" property="originalPrice"/>
    <result column="sell_price" property="sellPrice"/>
    <result column="stock" property="stock"/>
    <result column="value_ids_str" property="valueIdsStr"/> <!-- 临时存储逗号分隔的字符串 -->
  </resultMap>




  <!-- 根据分类ID查询商品 -->
  <select id="selectByCategoriesId" parameterType="java.lang.Long" resultMap="FoodItemResultsMap">
    SELECT
      id,
      category_id,
      food_name,
      short_desc,
      full_desc,
      price,
      nutrition_info,
      ingredients,
      suggestions,
      image_url,
      tags,
      is_enabled,
      create_time
    FROM
      food_items
    WHERE
      category_id = #{categoryId} AND is_enabled = 1
  </select>

  <select id="selectAll" resultMap="FoodItemResultMap">
    SELECT id, category_id, food_name, short_desc, full_desc, price,
           nutrition_info, ingredients, suggestions, image_url, tags,
           is_enabled, create_time
    FROM food_items
    WHERE is_enabled = 1
  </select>

  <!-- 5. 查询商品基础信息（引用resultMap） -->
  <select id="selectFoodBaseInfo" parameterType="Long" resultMap="FoodBaseResultMap">
    SELECT
      fi.id,
      fi.food_name,
      fi.price,
      fi.image_url,
      fc.category_name
    FROM food_items fi
           LEFT JOIN food_categories fc ON fi.category_id = fc.id
    WHERE fi.id = #{id} AND fi.is_enabled = 1
  </select>

  <!-- 6. 查询规格参数（引用resultMap） -->
  <select id="selectSpecParams" parameterType="Long" resultMap="SpecParamResultMap">
    SELECT
      id AS param_id,
      param_name,
      sort
    FROM spec_params
    WHERE goods_id = #{goodsId}
    ORDER BY sort ASC
  </select>

  <!-- 7. 查询规格值（引用resultMap） -->
  <select id="selectSpecValues" parameterType="Long" resultMap="SpecValueResultMap">
    SELECT
      sv.id AS value_id,
      sv.param_id,
      sv.value_name,
      sv.sort
    FROM spec_values sv
           LEFT JOIN spec_params sp ON sv.param_id = sp.id
    WHERE sp.goods_id = #{goodsId}
    ORDER BY sv.sort ASC
  </select>

  <!-- 8. 查询规格组合（引用resultMap） -->
  <select id="selectSpecCombinations" parameterType="Long" resultMap="SpecCombinationResultMap">
    SELECT
      sc.id AS combination_id,
      sc.combination_code,
      sc.original_price,
      sc.sell_price,
      sc.stock,
      GROUP_CONCAT(scr.value_id ORDER BY scr.id) AS value_ids_str
    FROM spec_combinations sc
           LEFT JOIN spec_combination_relations scr ON sc.id = scr.combination_id
    WHERE sc.goods_id = #{goodsId}
    GROUP BY sc.id
  </select>





</mapper>
