<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.UserMapper">
  <!-- 命名空间需与 Mapper 接口全路径一致 -->
  <!-- 用户结果映射 -->
  <resultMap id="UserResultMap" type="com.ruoyi.orderFood.entity.User">
    <id column="id" property="id"/>
    <result column="username" property="username"/>
    <result column="real_name" property="realName"/>
    <result column="phone" property="phone"/>
    <result column="role" property="role"/>
    <result column="is_enabled" property="isEnabled"/>
  </resultMap>

  <!-- 1. 根据用户名查询用户（登录/验证用） -->
  <select id="selectByUsername" parameterType="String" resultMap="UserResultMap">
    SELECT
      id, username, real_name AS realName, phone, role, is_enabled AS isEnabled
    FROM
      users
    WHERE
      username = #{username} AND is_enabled = 1
  </select>

  <!-- 2. 根据用户ID查询用户（获取个人信息用） -->
  <select id="selectById" parameterType="Long" resultMap="UserResultMap">
    SELECT
      id, username, real_name AS realName, phone, role, is_enabled AS isEnabled
    FROM
      users
    WHERE
      id = #{id}
  </select>

  <!-- 3. 新增用户（管理员添加用户用，可选） -->
  <insert id="insert" parameterType="com.ruoyi.orderFood.entity.User" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO users (
      username, real_name, phone, role, is_enabled
    ) VALUES (
               #{username}, #{realName}, #{phone}, #{role}, #{isEnabled}
             )
  </insert>

  <!-- 4. 更新用户信息（修改手机号/姓名用，可选） -->
  <update id="update" parameterType="com.ruoyi.orderFood.entity.User">
    UPDATE users
    SET
      real_name = #{realName},
      phone = #{phone},
      is_enabled = #{isEnabled}
    WHERE
      id = #{id}
  </update>
</mapper>
