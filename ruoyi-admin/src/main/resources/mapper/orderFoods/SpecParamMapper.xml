<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.SpecParamMapper">

  <!-- 规格参数ResultMap -->
  <resultMap id="SpecParamResultMap" type="com.ruoyi.orderFood.dto.SpecParamDTO">
    <id column="id" property="id"/>
    <result column="goods_id" property="goodsId"/>
    <result column="param_name" property="paramName"/>
    <result column="sort" property="sort"/>
  </resultMap>

  <resultMap id="SpecParamVOMap" type="com.ruoyi.orderFood.vo.SpecParamVO">
    <id column="param_id" property="paramId"/>
    <result column="param_name" property="paramName"/>
  </resultMap>

  <!-- 通用查询列 -->
  <sql id="Base_Column_List">
    id as param_id, goods_id, param_name, sort
  </sql>

  <!-- 根据商品ID查询规格参数 -->
  <select id="selectByGoodsId" resultMap="SpecParamResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM spec_params
    WHERE goods_id = #{goodsId}
    ORDER BY sort ASC
  </select>
  <select id="selectParamVOByGoodsId" resultMap="SpecParamVOMap">
    select <include refid="Base_Column_List"/>
      from spec_params
    where goods_id = #{goodsId}
    order by sort asc
  </select>

  <!-- 根据ID查询规格参数 -->
  <select id="selectById" resultMap="SpecParamResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM spec_params
    WHERE id = #{id}
  </select>

</mapper>

