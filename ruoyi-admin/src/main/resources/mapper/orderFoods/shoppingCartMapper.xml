<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.orderFood.mapper.ShoppingCartMapper">
  <!-- 购物车实体结果映射 -->
  <resultMap id="ShoppingCartResultMap" type="com.ruoyi.orderFood.entity.ShoppingCart">
    <id column="id" property="id"/>
    <result column="user_id" property="userId"/>
    <result column="food_id" property="foodId"/>
    <result column="spec_id" property="specId"/>
    <result column="quantity" property="quantity"/>
    <result column="remark" property="remark"/>
    <result column="create_time" property="createTime"/>
  </resultMap>

  <select id="selectByUserId" parameterType="java.lang.Long" resultMap="ShoppingCartResultMap">
    SELECT
      sc.id,
      sc.food_id as foodId,
      fi.food_name as name,
      fi.image_url as image,
      fi.price as originalPrice,
      sc.spec_id as specId,
      fs.spec_name as specName,
      fs.spec_price as specPrice,
      sc.quantity,
      sc.remark,
      (fs.spec_price * sc.quantity) as totalPrice,
      sc.create_time as createTime
    FROM shopping_cart sc
           LEFT JOIN food_item fi ON sc.food_id = fi.id
           LEFT JOIN food_spec fs ON sc.spec_id = fs.id
    WHERE sc.user_id = #{userId}
    ORDER BY sc.create_time DESC
  </select>

  <insert id="insert" parameterType="com.ruoyi.orderFood.entity.ShoppingCart">
    INSERT INTO shopping_cart (user_id, food_id, spec_id, quantity, remark, create_time)
    VALUES (#{userId}, #{foodId}, #{specId}, #{quantity}, #{remark}, NOW())
  </insert>

  <update id="updateQuantityAndRemark">
    UPDATE shopping_cart
    SET quantity = #{quantity}, remark = #{remark}
    WHERE id = #{id}
  </update>

  <select id="selectByUserIdFoodIdAndSpecId" resultMap="ShoppingCartResultMap">
    SELECT *
    FROM shopping_cart
    WHERE user_id = #{userId}
      AND goods_id = #{goodsId}
      AND combination_id = #{combinationId}
  </select>

  <delete id="deleteById" parameterType="java.lang.Long">
    DELETE FROM shopping_cart WHERE id = #{id}
  </delete>

  <delete id="deleteByIds" parameterType="java.util.List">
    DELETE FROM shopping_cart WHERE id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

</mapper>
