<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>ruoyi</artifactId>
    <groupId>com.ruoyi</groupId>
    <version>3.8.5</version>
    <relativePath></relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <packaging>jar</packaging>
  <artifactId>ybgk</artifactId>

  <description>
    web服务入口
  </description>

  <dependencies>


    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.15.4</version> <!-- 确保使用最新版本 -->
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-freemarker</artifactId>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>org.apache.poi</groupId>-->
<!--      <artifactId>poi-ooxml</artifactId>-->
<!--      <version>5.2.3</version>-->
<!--    </dependency>-->

    <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
    <dependency>
      <groupId>org.dom4j</groupId>
      <artifactId>dom4j</artifactId>
      <version>2.1.4</version>
    </dependency>


    <dependency>
      <groupId>com.microsoft.sqlserver</groupId>
      <artifactId>mssql-jdbc</artifactId>
      <version>10.2.0.jre8</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-thymeleaf</artifactId>
    </dependency>

    <!-- spring-boot-devtools -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <optional>true</optional> <!-- 表示依赖不会传递 -->
    </dependency>

    <!-- swagger3-->
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-boot-starter</artifactId>
    </dependency>

    <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-models</artifactId>
      <version>1.6.2</version>
    </dependency>

    <!-- Mysql驱动包 -->
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>


    <!-- 核心模块-->
    <dependency>
      <groupId>com.ruoyi</groupId>
      <artifactId>ruoyi-framework</artifactId>
    </dependency>

    <!-- 定时任务-->
    <dependency>
      <groupId>com.ruoyi</groupId>
      <artifactId>ruoyi-quartz</artifactId>
    </dependency>

    <!-- 代码生成-->
    <dependency>
      <groupId>com.ruoyi</groupId>
      <artifactId>ruoyi-generator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jeecgframework.jimureport</groupId>
      <artifactId>jimureport-spring-boot-starter</artifactId>
      <version>1.5.8</version>
    </dependency>

    <dependency>
      <groupId>info.debatty</groupId>
      <artifactId>java-string-similarity</artifactId>
      <version>2.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.huaban</groupId>
      <artifactId>jieba-analysis</artifactId>
      <version>1.0.2</version>
    </dependency>

    <dependency>
      <groupId>org.ansj</groupId>
      <artifactId>ansj_seg</artifactId>
      <version>5.1.6</version>
    </dependency>


    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
      <version>2.3.0</version>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-impl</artifactId>
      <version>2.3.0</version>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-core</artifactId>
      <version>2.3.0</version>
    </dependency>
    <dependency>
      <groupId>javax.activation</groupId>
      <artifactId>activation</artifactId>
      <version>1.1.1</version>
    </dependency>

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.8.16</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.6.0</version>
    </dependency>

      <dependency>
            <groupId>com.pdxt</groupId>
            <artifactId>demo</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/src/main/resources/static/jconn4.jar</systemPath>
        </dependency>


    <dependency>
      <groupId>com.chsdrg</groupId>
      <artifactId>chsdrg</artifactId>
      <version>1.0.0</version>
      <scope>system</scope>
      <systemPath>${pom.basedir}/src/main/resources/static/grouper_chs_drg_11.jar</systemPath>
    </dependency>
      <dependency>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
          <scope>provided</scope>
      </dependency>


  </dependencies>

  <build>
   <resources>
            <resource>
                <!-- 指定resources插件处理哪个目录下的资源文件 -->
                <directory>${pom.basedir}/src/main/resources/static</directory>
                <!-- 需要将资源文件放到该目录下才能访问 -->
                <targetPath>META-INF/static</targetPath>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
            <resource>
                <directory>${pom.basedir}/src/main/resources</directory>
            </resource>
        </resources>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.1.1.RELEASE</version>
        <configuration>
          <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
          <includeSystemScope>true</includeSystemScope>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>net.roseboy</groupId>
        <artifactId>classfinal-maven-plugin</artifactId>
        <version>1.2.1</version>
        <configuration>
          <password>#</password>
          <packages>com.ruoyi,com.ruoyi</packages>
          <cfgfiles>application.yml,application-druid.yml</cfgfiles>
          <excludes>org.spring</excludes>
          <libjars>a.jar,b.jar</libjars>
        </configuration>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>classFinal</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <version>3.1.0</version>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <warName>${project.artifactId}</warName>
        </configuration>
      </plugin>
    </plugins>
    <finalName>${project.artifactId}</finalName>
  </build>

</project>
